package com.ls.ner.billing.charge.service;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;

import java.util.Map;


/**
 * 充电计费结果
 * <AUTHOR>
 */
public interface IChargeBillingRltService {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 	√刷卡充电开始时调用，生成充电订单计费版本（BOSS模块之间接口）
	 * @param inMap (
	 * 	pileNo 充电桩编号
	 * 	appNo 订单编号
	 * 	dataTime 数据时间
	 *  dataType 数据类型
	 *  timeSharFlag 分时标志(未用，预留)
	 *  bgnTime 开始时间
	 *  mrNum 示数
	 * )
	 * @throws Exception 错误信息
	 */
//	public void createChargeOrderVersion(Map<String,Object> inMap)throws ChargeBillingRltException;
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 	订单结算计费 ---*******.4	刷卡结算new时，调用此方法，传入刷卡结算数据
	 * @param inMap (
	 * 	APP_NO 订单编号
	 * 	DATA_TYPE 数据类型
	 *  。。。。
	 * )
	 * @return Map(
	 * 		tAmt;//充电金额。如果是分时，则为分时金额总和
	 * 		itemTAmt;//服务项目总金额
	 * 		amt;//总金额
	 * )
	 * @throws Exception 错误信息
	 */
//	public Map<String,Object> createOrderStatements(Map<String,Object> inMap);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 	√生成订单时调用，创建充电计费版本
	 * @param inMap (
	 * 	stationNo 充电站编号
	 * 	appNo 订单编号
	 * )
	 * @throws Exception 错误信息
	 */
	public void createChargeOrderVersionForOrder(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 	√生成订单后，启动充电时，修改充电计费版本(加入充电开始时间、充电开始电量等)
	 * @param inMap (
	 * 	appNo 订单编号
	 * 	dataTime 数据时间
	 *  dataType 数据类型     1BOSS计费，推送数据为示数    2BOSS计费，推送数据为电量      3桩计费，推送数据为量、价、费
	 *  timeSharFlag 分时标志(未用，预留)
	 *  bgnTime 开始时间
	 *  mrNum 示数
	 * )
	 * @throws Exception 错误信息
	 */
	public void updateChargeOrderVersionForOrder(Map<String, Object> inMap);

	//============================================接口部分===========================================
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-02
	 * @description 	*******	获取充电计费版本（价格信息）
	 * @param inMap (
	 * 	stationNo 充电站编号
	 * )
	 * @throws Exception 错误信息
	 */
	public Map<String,Object> obtainChargeOrderVersion(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-02
	 * @description 	获取充电实时计费信息或者充电结算计费信息
	 * @param inMap (
	 * 	appNo 订单编号
	 * 	chargeState 充电状态   02 充电中、03 已完成
	 * )
	 * @throws Exception 错误信息
	 * @return Map(
	 * 		tAmt;//充电金额。如果是分时，则为分时金额总和
	 * 		itemTAmt;//服务项目总金额
	 * 		amt;//总金额
	 * 		attachlist:[{
	 * 			itemNo;//收费项目编号
	 * 			itemName;//收费项目名称
	 * 			itemAmt;//服务项目金额
	 * 		}]
	 * )
	 */
	public Map<String,Object> obtainChargeOrderSettlement(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
	 */
	public int queryChargeBillingConfsNum(ChargeBillingConfQueryCondition bo);

	public ChargeBillingConfBo obtainChargeBillingConf(String stationId, String pileId, String endEftDate, String custId, String groupId, String pCustId);
}
