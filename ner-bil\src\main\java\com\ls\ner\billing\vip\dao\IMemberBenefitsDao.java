package com.ls.ner.billing.vip.dao;

import com.ls.ner.billing.api.vip.bo.VipInvoiceUpdateBo;
import com.ls.ner.billing.api.vip.bo.VipPayRecordRPCBo;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.vo.ExclusiveOfferVo;
import com.ls.ner.billing.vip.bo.*;
import com.ls.ner.billing.vip.condition.VipCondition;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.billing.vip.bo.RegularVipRankBo;
import com.ls.ner.billing.vip.bo.VipBo;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.condition.VipRecordCondition;
import com.ls.ner.billing.vip.condition.VipRecordPageCondition;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description IMemberBenefitsDao
 */
public interface IMemberBenefitsDao {
    List<MemberBenefitsBo> list();

    MemberBenefitsBo configDesc(MemberBenefitsBo bo);

    List<MemberBenefitsBo> getVipBenefits(MemberBenefitsBo bo);

    int count();

    MemberBenefitsBo getMemberBenefits(MemberBenefitsBo bo);

    MemberBenefitsBo getUserMemberBenefits(@Param("chargePq") BigDecimal chargePq);

    MemberBenefitsBo getUserMemberBenefitsByAmt(@Param("chargeAmtPq") BigDecimal chargeAmt);

    void updateEVipBenefits(MemberBenefitsBo bo);

    List<RegularVipRankBo> selectRegularVipConfigList();

    RegularVipRankBo getRegularVipConfigById(@Param("rankId") String rankId);

    void editRegularVipConfig(RegularVipRankBo regularVipRankBo);

    VipBo getVipConfigById(@Param("vipId") String vipId);

    List<VipBo> getVipConfigList();

    VipBo getMonthVipConfig();

    void insertVipConfig(VipBo bo);

    void updateVipConfig(VipBo bo);

    void updateVipConfigStatus(VipBo bo);

    List<VipPayRecordBo> getRecordList(VipRecordCondition condition);

    VipPayRecordBo qryEffectTime(VipRecordCondition condition);

    List<VipPayRecordBo> qryVipTime(VipRecordCondition condition);

    List<VipPayRecordBo> getRecordListByMobile(Map<String, Object> inMap);

    Integer getRecordListByMobileCount(VipRecordPageCondition condition);

    int getRecordListCount(VipRecordCondition condition);

    RegularVipRankBo getRegularVipLevel(Map<String, Object> inMap);

    VipLevelBo getUserVipLevel(String custId);

    void insertVipLevel(VipLevelBo bo);

    void updateVipLevel(VipLevelBo bo);

    void addRecord(VipPayRecordRPCBo record);

    void updateRecord(VipPayRecordRPCBo record);

    VipPayRecordBo getVipPayRecordById(@Param("recordId") String recordId);

    VipPayRecordBo getCustVipType(@Param("custId") String custId);

    List<MemberBenefitsBo> getBenefitsUser(MemberBenefitsBo bo);

    VipLevelBo getLevelFlag(String custId);

    void updateLevelFlag(VipLevelBo bo);

    /**
     * @description 查询优惠券
     * <AUTHOR>
     * @create 2018-11-13 17:15:38
     */
    List<CouponBo> queryCpnById(List<ExclusiveOfferVo> levelList);

    void updateInvoiceFlag(VipInvoiceUpdateBo bo);
}
