/**
 *
 * @(#) ItemPeriodsSet.java
 * @Package com.ls.ner.billing.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.bo;

import java.io.Serializable;

/**
 * 类描述：峰谷时段
 * 
 * @author: lipf
 * @version $Id: ItemPeriodsSet.java,v 1.1 2016/02/26 02:34:00 34544 Exp $
 * 
 *          History: 2016年2月17日 下午4:33:56 lipf Created.
 * 
 */
public class ItemPeriodsSet implements Serializable {

	private static final long serialVersionUID = -923343030759351136L;
	private String systemId;// 峰谷时段设置标识
	private String mainNo;// 主收费项目编号
	private String sn;// 序号
	private String beginTime;// 起始时间
	private String endTime;// 结束时间
	private String dataOperTime;// 数据操作时间
	private String dataOperType;// 数据操作类型

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getMainNo() {
		return mainNo;
	}

	public void setMainNo(String mainNo) {
		this.mainNo = mainNo;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getDataOperTime() {
		return dataOperTime;
	}

	public void setDataOperTime(String dataOperTime) {
		this.dataOperTime = dataOperTime;
	}

	public String getDataOperType() {
		return dataOperType;
	}

	public void setDataOperType(String dataOperType) {
		this.dataOperType = dataOperType;
	}

}
