package com.ls.ner.billing.vip.service;


import com.ls.ner.billing.api.vip.bo.VipRPCBo;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
public interface IVipLevelService {

    String updateVipLevel(VipRPCBo vipRPCBo);

    String getLevelFlag(VipRPCBo vipRPCBo);

    Map updateLevelFlag(VipRPCBo vipRPCBo);

    void openGift(VipRPCBo vipRPCBo);

    void exclusiveOffer(VipRPCBo vipRPCBo);

    void upgradeGift(VipRPCBo vipRPCBo);
}
