<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑优惠券" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script src="<%=request.getContextPath()%>/bil/comm/js/DateUtils.js" type="text/javascript"></script>

<ls:body>
		<ls:form name="couponForm" id="couponForm" enctype="multipart/form-data">
			<ls:text name="cpnId" property="cpnId" visible="false"></ls:text>
			<ls:text name="handleType" property="handleType" visible="false"></ls:text>
			<ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
			<ls:text name="prodId" property="prodId"  visible="false"></ls:text>
			<ls:text name="filePath" property="filePath"  visible="false"></ls:text>
			<ls:text name="attachId" property="attachId"  visible="false"></ls:text>
			<ls:text name="effectDate" property="effectDate"  visible="false"></ls:text>
			<ls:text name="dctCondCity" property="dctCondCity"  visible="false"></ls:text>
			<ls:text name="dctCondStation" property="dctCondStation"  visible="false"></ls:text>
			<ls:layout-use>
				<ls:layout-put into="container">
					<table align="center" class="tab_search">
						<colgroup>
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
						</colgroup>
						<tr>
							<td>
								<ls:label text="优惠券名称" ref="cpnName"/>
							</td>
							<td colspan="2">
								<ls:text name="cpnName" property="cpnName" required="true" hint="建议最多输入8个字符"></ls:text>
							</td>

							<td>
								<ls:label text="优惠券类型" ref="cpnType"/>
							</td>
							<td>
								<ls:select name="cpnType" required="true" property="cpnType" noClear="true" onchanged="changeCpnType">
									<ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue" />
								</ls:select>
							</td>

							<td rowspan="4">
								<div id="couponImage" style="width:200px;height:120px;background:gray">
									<img src="" id="headImg"  width="200" height="120"></img>
								</div>
								<div >
									<span><input id="fileData" accept="image/*" class="text" name="fileData" type="file" onchange="previewImage('couponImage',this)"/></span>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<ls:label text="优惠内容" ref="busiType"/>
							</td>
							<td>
<%--								<ls:select name="busiType" required="true" property="busiType" noClear="true" onchanged="busiTypeChange">--%>
								<ls:select name="busiType" required="true" property="busiType" noClear="true">
<%--									<ls:options property="busiTypeList" scope="request" text="codeName" value="codeValue" />--%>
									<ls:option text="充电" value="02"></ls:option>
								</ls:select>
							</td>
							<td>
								<ls:select name="dctType" required="true" property="dctType" noClear="true">
									<ls:options property="dctTypeList" scope="request" text="codeName" value="codeValue" />
								</ls:select>
							</td>
							<td>
								<ls:label text="减免面额" ref="cpnAmt" name="lab_cpnAmt" id="lab_cpnAmt"/>
							</td>
							<td>
								<ls:text name="cpnAmt" id="cpnAmt"  property="cpnAmt" required="true" ></ls:text>
							</td>
						</tr>

						<tr>
							<td>
								<ls:label text="发行数量" ref="cpnNum"/>
							</td>
							<td colspan="2">
								<table>
									<tr>
										<td><ls:text name="cpnNum"  property="cpnNum" required="true"></ls:text></td>
										<td>张</td>
									</tr>
								</table>
							</td>
							<td>
								<ls:label text="每人限领" ref="limGetNum" />
							</td>
							<td>
								<table>
									<tr>
										<td><ls:text name="limGetNum"  property="limGetNum" required="true"></ls:text></td>
										<td>张</td>
									</tr>
								</table>
							</td>
						</tr>

						<tr >
							<td>
								<ls:label text="优惠券描述" />
							</td>
							<td colspan="4">
								<ls:text name="cpnMarks" property="cpnMarks" type="textarea" maxlength="256"></ls:text>
							</td>
						</tr>

						<tr>
							<td>
								<ls:label text="有效时间" ref="cpnTimeType"/>
							</td>
							<td colspan="4">
								<table>
									<tr>
										<td>
											<ls:checklist type="radio" name="cpnTimeType" property="cpnTimeType" onchanged="setDateType" required="true">
												<ls:checkitem value="1" text="日期范围"></ls:checkitem>
												<ls:checkitem value="2" text="领用或发放后"></ls:checkitem>
											</ls:checklist>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr >
							<td>&nbsp;</td>
							<td colspan="4">
								<table>
									<tr id="dateType01">
										<td>
											<ls:checklist type="checkbox" name="isEffect" property="isEffect" onchanged="checkIsEffect">
												<ls:checkitem value="1" text="立即生效"></ls:checkitem>
											</ls:checklist>
										</td>
										<td >
											<ls:date name="eftDate" property="eftDate" format="yyyy-MM-dd" required="true"></ls:date>
										</td>
										<td >
											<ls:label text="至" ref="invDate"/>
										</td>
										<td >
											<ls:date name="invDate"  property="invDate" format="yyyy-MM-dd" required="true"></ls:date>
										</td>
									</tr>
									<tr style="display:none" id="dateType02">
										<td style="width: 60%">
											<ls:text name="timeDuration"  property="timeDuration" required="true"></ls:text>
										</td>
										<td style="width: 20%">
											<ls:select name="timeUnit" required="true" property="timeUnit" noClear="true">
												<ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue" />
											</ls:select>
										</td>
										<td style="width: 20%">内有效</td>
									</tr>
								</table>
							</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>

						<tr>
							<td>
								<ls:label text="计算结果精度" ref="calcPrecisionDigits"/>
							</td>
							<td>
								<ls:select name="calcPrecisionDigits" required="true" property="calcPrecisionDigits" noClear="true">
									<ls:options property="calcPrecisionDigitsList" scope="request" text="codeName" value="codeValue" />
								</ls:select>
							</td>
							<td>
								<ls:select name="calcPrecision" required="true" property="calcPrecision" noClear="true">
									<ls:options property="calcPrecisionList" scope="request" text="codeName" value="codeValue" />
								</ls:select>
							</td>
							<td scope="2"></td>
						</tr>
						<tr>
							<td>
								<ls:label text="用途" ref="cpnPurpose"/>
							</td>
							<td>
								<ls:select name="cpnPurpose" property="cpnPurpose" value="2" onchanged="setCpnPurposeFun">
									<ls:option value="1" text="首次添加爱车"></ls:option>
									<ls:option value="2" text="其他"></ls:option>
								</ls:select>
							</td>
							<td colspan="3">
								<div id="cpnPurposeText">此类型优惠券只能同时生效一种，若有多种，将以最新配置的为准。</div>
							</td>
<%--							<td scope="2"></td>--%>

						</tr>
						<tr>
							<td><ls:label text="赠送积分：" ref="integralNum"/></td>
							<td colspan="3">
								<div style="display: flex; align-items: center; flex-wrap: nowrap;">
									<ls:text name="integralNum" property="integralNum" type="number" min="0" style="width: 33%; margin-right: 2px;"/>
									<span style="white-space: nowrap;">积分</span>
								</div>
								<div style="font-size: 12px; color: #888; margin-top: 2px;">（填0或留空为不赠送）</div>
							</td>
						</tr>
					</table>
					<ls:title text="优惠条件" expand="false"></ls:title>
					<table class="tab_search hydj">
						<tr>
							<td width="9%">
								<ls:label text="会员叠加" ref="superpositionFlag"/>
							</td>
							<td width="20%">
								<ls:checklist type="radio" name="superpositionFlag" property="superpositionFlag" >
									<ls:checkitem value="1" text="允许"></ls:checkitem>
									<ls:checkitem value="2" text="不允许"></ls:checkitem>
								</ls:checklist>
							</td>
							<td width="1%"></td>
							<td width="7%"></td>
							<td width="35%"></td>
							<td width="17%"></td>
						</tr>
					</table>
					<table class="tab_search">
						<tr>
							<td width="10%">
								<ls:label text="条件类型" ref="dctCondFlag"/>
							</td>
							<td width="20%">
								<ls:checklist type="radio" name="dctCondFlag" property="dctCondFlag" onchanged="setDisplayDctCond">
									<ls:checkitem value="1" text="无条件"></ls:checkitem>
									<ls:checkitem value="2" text="条件"></ls:checkitem>
								</ls:checklist>
							</td>
							<td width="10%"></td>
							<td width="20%"></td>
							<td width="10%"></td>
							<td width="20%"></td>
							<td width="10%"></td>
						</tr>
						<tr id="dctCondVal2">
<%--							<ls:text name="dctCondId" property="dctCondId"  visible="false"></ls:text>--%>
<%--							<td width="10%" id="dctCondTypeHide">--%>
<%--								<ls:label text="优惠条件类别" ref="dctCondType"/>--%>
<%--							</td>--%>
<%--							<td width="20%" id="dctCondTypeValueHide">--%>
<%--								<ls:select name="dctCondType" required="true" property="dctCondType"  onchanged="setDctCondType">--%>
<%--									<ls:options property="dctCondTypeList" scope="request" text="codeName" value="codeValue" />--%>
<%--								</ls:select>--%>
<%--							</td>--%>
<%--							<td width="10%" id="lab_dctProdId">--%>
<%--								<ls:label text="优惠条件内容" ref="dctProdId"/>--%>
<%--							</td>--%>
<%--							<td width="20%" id="txt_dctProdId">--%>
<%--								<ls:select name="dctProdId" required="true" property="dctProdId" >--%>
<%--									<ls:options property="dctProdIdList" scope="request" text="codeName" value="codeValue" />--%>
<%--								</ls:select>--%>
<%--							</td>--%>
							<td width="10%" id="dctCondHide" style="display:none;">
								<ls:label text="最低消费金额"  ref="dctCondValue" />
							</td>
							<td width="20%" id="dctCondValueHide" style="display:none;">
								<ls:text  name="dctCondValue" property="dctCondValue" required="true" hint="请输入最低消费金额"></ls:text>
							</td>
							<td width="10%" id="maximumDiscountAmtHide" style="display:none;">
								<ls:label text="最高抵扣金额" ref="maximumDiscountAmt"/>
							</td>
							<td width="20%" id="maximumDiscountAmtValueHide" style="display:none;">
								<ls:text  name="maximumDiscountAmt" property="maximumDiscountAmt" required="true" hint="请输入最高抵扣金额"></ls:text>
							</td>
							<td colspan="3" id="dctCondRemark" style="display:none;"> 最低消费金额、最高抵扣金额根据优惠内容所选择的整单金额/服务费进行变动 </td>
<%--							<td></td>--%>
						</tr>
                    </table>
                    <table class="tab_search">
                        <tr>
                            <td width="9%">
                                <ls:label text="时间段限制" ref="timeCondFlag"/>
                            </td>
                            <td width="20%">
                                <ls:checklist type="radio" name="timeCondFlag" property="timeCondFlag"  onchanged="setDisplayTimeCond">
                                    <ls:checkitem value="1" text="全部"></ls:checkitem>
                                    <ls:checkitem value="2" text="其他时段"></ls:checkitem>
                                </ls:checklist>
                            </td>
							<td width="1%"></td>
							<td width="7%"></td>
							<td width="35%"></td>
							<td width="17%"></td>
                        </tr>
					</table>
					<table class="tab_search yhsjd">
                        <tr>
                            <td width="10%">
                                <ls:label text="优惠时间段"/>
                            </td>
							<td width="7%">
								<ls:text name="timeCondBeg" property="timeCondBeg" type="hidden"/>
								<input type="time" id="begTime" value="00:00" name="begTime" placeholder=""
									   onChange="timeBeginChange(this.value)"/>
                            </td>
                            <td width="1%" style="text-align: center;">
                                至
                            </td>
							<td width="7%">
								<ls:text name="timeCondEnd" property="timeCondEnd" type="hidden"/>
								<input type="time" id="endTime" name="endTime" value="23:59" placeholder=""
									   onChange="timeEndChange(this.value)"/>
                            </td>
							<td width="35%"></td>
							<td width="30%"></td>
                        </tr>
                    </table>
                    <table class="tab_search">
						<tr>
							<td width="9%">
								<ls:label text="使用区域"/>
							</td>
							<td width="20%">
								<ls:checklist type="radio" name="dctCondAreaFlag" property="dctCondAreaFlag" onchanged="checkUseCondition">
									<ls:checkitem value="1" text="无限制"></ls:checkitem>
									<ls:checkitem value="2" text="有限制"></ls:checkitem>
								</ls:checklist>
							</td>
							<td width="10%"></td>
							<td width="20%"></td>
							<td width="10%"></td>
							<td width="20%"></td>
						</tr>

						<tr id="dctCondVal3">
							<td width="10%"></td>
							<td colspan="2" width="40%">
								<ls:form name="cityForm">
									<table>
										<tr><td colspan="2">
											<ls:grid url="" name="cpnCityGrid" height="150px" showCheckBox="true" primaryKey="areaCode"
													 singleSelect="false" caption="城市列表">
												<ls:gridToolbar name="cityGridBar">
													<ls:gridToolbarItem name="selectCityBtn" text="添加" onclick="citySelect" imageKey="add"/>
													<ls:gridToolbarItem name="deleteCityBtn" text="删除" onclick="cityDelete" imageKey="delete"/>
												</ls:gridToolbar>
												<ls:column name="areaCode" caption="" hidden="true" />
												<ls:column name="areaName" caption="城市"/>
											</ls:grid>
										</td></tr>
									</table>
								</ls:form>
							</td>
							<td colspan="3" width="60%">
								<ls:form name="stationForm">
									<table>
										<tr><td colspan="3">
											<ls:grid url="" name="cpnStationGrid" height="150px" showCheckBox="true" primaryKey="stationId"
													 singleSelect="false" caption="站点列表">
												<ls:gridToolbar name="cityGridBar">
													<ls:gridToolbarItem name="selectStationBtn" text="添加" onclick="stationSelect" imageKey="add"/>
													<ls:gridToolbarItem name="deleteStationBtn" text="删除" onclick="stationDelete" imageKey="delete"/>
												</ls:gridToolbar>
												<ls:column name="stationId" caption="" hidden="true" />
												<ls:column caption="管理单位" name="orgCodeName"></ls:column>
												<ls:column caption="站点名称" name="stationName" />
												<ls:column caption="站点编号" name="stationNo"></ls:column>
												<ls:column caption="城市" name="cityName"></ls:column>
											</ls:grid>
										</td></tr>
									</table>
								</ls:form>
							</td>
							<td width="10%"></td>
						</tr>
					</table>
				</ls:layout-put>
				<ls:layout-put into="footer">
					<div class="pull-right">
						<ls:button text="保存" onclick="save"></ls:button>
						<ls:button text="发布" onclick="public" ></ls:button>
						<ls:button text="取消" onclick="cancel"></ls:button>
					</div>
				</ls:layout-put>
			</ls:layout-use>
		</ls:form>
		<ls:script>
		var headImg = document.getElementById('headImg');
		var pubPath = '${pubPath }';
		var dctTypeItems = new Array();
		var dctTypeItemsTemp = new Array();
		window.onload = function(){
			setDisplayDctCond();
			checkUseCondition();
			setDateType();
			setCpnPurposeFun();
<%--			setDctCondType();--%>
			dctTypeItems = dctType.items;
			for(var i=0;i< dctTypeItems.length;i++){
				dctTypeItemsTemp.add(dctTypeItems[i])
			}
			var _attachId = attachId.getValue();
			if(LS.isEmpty(cpnId.getValue()) || LS.isEmpty(_attachId)){
				headImg.src = pubPath + '/pub/image/js/add.png';
			}else{
				headImg.src = pubPath + '/attach/'+_attachId;
			}
			checkIsEffect();
			if(!LS.isEmpty(cpnId.getValue())){
				cityQuery();
				stationInitQuery();
			}
<%--			busiTypeChange();--%>
			var _dctType = dctType.getValue();
			dctType.setValue(_dctType);
		}
		function busiTypeChange(){

			var _busiType = busiType.getValue();
			dctTypeItems.length;
			dctTypeItemsTemp.length;
			var item = {};
			item.text = "服务费";
			item.value = "0110";
			if("02" == _busiType){
				dctType.appendItem(item);
			}else {
				dctType.clearItems();
				dctType.appendItems(dctTypeItemsTemp);
			}
		}

			function setCpnPurposeFun(){
				if(cpnPurpose.getValue() == "1"){
					$("#cpnPurposeText").show();
				}else{
					$("#cpnPurposeText").hide();
				}
			}

		function setDateType(){
			if(cpnTimeType.getValue() == "1"){
				$("#dateType01").show();
				$("#dateType02").hide();
			}else{
				$("#dateType01").hide();
				$("#dateType02").show();
			}
		}
		function isNumber(){
			var _cpnAmt = cpnAmt.getValue();
			var _cpnNum = cpnNum.getValue();
			var _limGetNum = limGetNum.getValue();

			if(_limGetNum!=null&&_limGetNum!=""&&isNaN(_limGetNum)){
				LS.message("info","每人限领非数字！");
				return false;
			}
			if(parseFloat(_limGetNum) <= 0){
				LS.message("info","每人限领应大于0");
				return false;
			}
			if("01" == cpnType.getValue()){
				if(_cpnAmt!=null&&_cpnAmt!=""&&isNaN(_cpnAmt)){
					LS.message("info","面额非数字");
					return false;
				}
				if(parseFloat(_cpnAmt) <= 0){
					LS.message("info","面额应大于0");
					return false;
				}
			}else{
				if(_cpnAmt!=null&&_cpnAmt!=""&&isNaN(_cpnAmt)){
					LS.message("info","折扣非数字");
					return false;
				}

				if(Number.isInteger(parseFloat(_cpnAmt)) && _cpnAmt > 0){

				}else{
					LS.message("info","减免面额需要为整数");
					return false;
				}

				if(parseFloat(_cpnAmt) < 0 || parseFloat(_cpnAmt)>=10){
					LS.message("info","折扣范围不合理，应在0-10之间！");
					return false;
				}
<%--				if(_cpnAmt.indexOf('.') >= 0){--%>
<%--					var reg = _cpnAmt.substring(_cpnAmt.indexOf('.') + 1);--%>
<%--					if (reg.length > 2) {--%>
<%--						LS.message("最多不可超过两位小数");--%>
<%--						return;--%>
<%--					}--%>
<%--				}--%>
			}
			if(_cpnNum!=null&&_cpnNum!=""&&isNaN(_cpnNum)){
				LS.message("info","发行数量非数字");
				return false;
			}

			var _dctCondValue = dctCondValue.getValue();
			var _cpnType = cpnType.getValue();
			if(_cpnType=='01' && dctCondFlag.value == '2' && _dctCondValue!=null && _dctCondValue!="" && isNaN(_dctCondValue)){
				LS.message("info","最低消费金额使用字段非数字！");
				return false;
			}
			if(_cpnType=='01' && dctCondFlag.value == '2' && parseFloat(_dctCondValue)<=0){
				LS.message("info","最低消费金额使用字段不能小于0！");
				return false;
			}
			var _maximumDiscountAmt=maximumDiscountAmt.getValue();
			if(_cpnType=='02' && dctCondFlag.value == '2' && _maximumDiscountAmt!=null && _maximumDiscountAmt!="" && isNaN(_maximumDiscountAmt)){
				LS.message("info","最高抵扣金额非数字！");
				return false;
			}
			if(_cpnType=='02'&& dctCondFlag.value == '2' && _maximumDiscountAmt<=0){
				LS.message("info","最高抵扣金额应大于0！");
				return false;
			}
			if(!LS.isEmpty(_cpnNum)&&!LS.isEmpty(_limGetNum)&&parseFloat(_limGetNum)>parseFloat(_cpnNum)){
				LS.message("info","每人限领数量不能大于发行数量！");
				return false;
			}
			return true;
		}
		//使用条件类型改变事件
		function setDisplayDctCond(){
			var _dctCondFlag = dctCondFlag.getValue();
			var _cpnType = cpnType.getValue();
			if("2"==_dctCondFlag){
				$("#dctCondVal2").show();
				$("#dctCondTypeValueHide").hide();
				$("#dctCondTypeHide").hide();
			    var hiddenCell1 = document.getElementById('maximumDiscountAmtHide');
			    var hiddenCell2 = document.getElementById('maximumDiscountAmtValueHide');
			    var hiddenCell3 = document.getElementById('dctCondHide');
			    var hiddenCell4 = document.getElementById('dctCondValueHide');
				var hiddenCell5 = document.getElementById('dctCondRemark');

			    if('02' == _cpnType){
			      hiddenCell1.style.display = 'table-cell';
			      hiddenCell2.style.display = 'table-cell';
			      hiddenCell3.style.display = 'table-cell';
			      hiddenCell4.style.display = 'table-cell';
			      hiddenCell5.style.display = 'table-cell';
			    }else{
			      hiddenCell1.style.display = 'none';
			      hiddenCell2.style.display = 'none';
			      hiddenCell3.style.display = 'table-cell';
			      hiddenCell4.style.display = 'table-cell';
			      hiddenCell5.style.display = 'table-cell';
			    }
			}else if("1"==_dctCondFlag){
				$("#dctCondVal2").hide();
				$("#dctCondTypeValueHide").hide();
				$("#dctCondTypeHide").hide();
			    $("#dctCondRemark").hide();
			}else{
				$("#dctCondVal2").hide();
				$("#dctCondTypeValueHide").hide();
				$("#dctCondTypeHide").hide();
				$("#dctCondRemark").hide();
			}
		}

		//区域限制类型改变事件
		function checkUseCondition(){
			var _useAreaFlag = dctCondAreaFlag.getValue();
			if("2"==_useAreaFlag){
				$("#dctCondVal3").show();
				$("#span_td").show();
			}else if("1"==_useAreaFlag){
				$("#dctCondVal3").hide();
				$("#span_td").hide();
			}else{
				$("#dctCondVal3").hide();
				$("#span_td").hide();
			}
		}

		//城市选择按钮
		function citySelect() {
            //获取已选的城市
            var removeCitys='';
            var citys=cpnCityGrid.getItems();
            if(citys && citys.length>0){
                for(var i=0;i< citys.length;i++){
                  removeCitys+=citys[i].areaCode+',';
                }
            }
            var params={};
            params.removeCitys=removeCitys;
			LS.dialog("~/billing/coupon/couponCitySelect","城市选择",600,450,true, params);
		}

		//添加城市（子页面调用）
		window.selectCpnCity = selectCpnCity;
		function selectCpnCity(items){
			var tmp = new Array();
			var gridItems = cpnCityGrid.getItems();
			if(gridItems.length > 0){
				var isRepeated;
				for(var j = 0;j < items.length;j++){
					isRepeated = false;
					for(var i = 0;i < gridItems.length;i++){
						if(items[j].areaCode == gridItems[i].areaCode){
							isRepeated = true;
							break;
						}
					}
					if (!isRepeated) {
						tmp.push(items[j]);
					}
				}
				cpnCityGrid.appendItem(tmp)
			}else{
				cpnCityGrid.appendItem(items)
			}
			<%--cpnCityGrid.selectAll(true);--%>
			<%--stationQuery();--%>
		}
		//城市删除按钮
		function cityDelete() {
			var items = cpnCityGrid.getCheckedItems();
			if(items && items.length>0) {
                 //删除站点列表中属于删除城市的站点
                for(var i=0;i< items.length;i++){
                    var stationItems=cpnStationGrid.getItems();
                    if(stationItems && stationItems.length>0){
                        for(var j=0;j< stationItems.length;j++){
                            if(stationItems[j].city==items[i].areaCode){
                                  cpnStationGrid.removeItem(stationItems[j]);
                            }
                        }
                    }
                }
				for(var i=0; i< items.length; i++) {
					cpnCityGrid.removeItem(items[i]);
				}
			}else {
				LS.message('info', '请选择一条记录');
			}
			var newItems=cpnCityGrid.getItems();
			if( newItems.length>0){
				<%--stationQuery();--%>
			}else{
				cpnStationGrid.removeAllItems();
				$('#cb__id_cpnCityGrid').removeClass('checkbox_false_part');
			}
		}

		//城市表格点击事件
		<%--cpnCityGrid.onitemclick=function(item, event, rowid){--%>
			<%--stationQuery();--%>
		<%--}--%>
		//城市表格查询方法
		function cityQuery(){
			if(!LS.isEmpty(dctCondCity.getValue())){
				var data = {
					citys : dctCondCity.getValue()
				};
				cpnCityGrid.query("~/billing/coupon/queryCityInfo", data,function(e){
					if(e.successful){
						<%--cpnCityGrid.selectAll(true);--%>
					}
				});
			}
		}
		//站点表格查询方法
		function stationQuery() {
			var data = stationForm.getFormData();
			var items = cpnCityGrid.getItems();
			var cityStr = '';
			for(var i = 0;i < items.length; i++){
				cityStr = cityStr + items[i].areaCode + ',';
			}
			data.citys = cityStr;
			data.busiType = busiType.getValue();
			cpnStationGrid.query("~/billing/coupon/queryStationInfo", data,function(e){
				if(e.successful){
					<%--cpnStationGrid.selectAll(true);--%>
				}
			});
		}
		//修改----初始化站点表格查询方法
		function stationInitQuery() {
			if(!LS.isEmpty(dctCondStation.getValue())){
				var data = {};
				data.stations = dctCondStation.getValue();
				data.busiType = busiType.getValue();
				cpnStationGrid.query("~/billing/coupon/queryStationInfo", data,function(e){
					if(e.successful){
						<%--cpnStationGrid.selectAll(true);--%>
					}
				});
			}
		}

		//站点选择按钮
		function stationSelect() {
			var items=cpnCityGrid.getItems();
			var citys=[];
			var removeStations='';
			//站点列表
			var stationItems=cpnStationGrid.getItems();
			<%--params.citys=items;--%>
			if(items && items.length>0){
				for(var i=0;i< items.length;i++){
					citys.push(items[i].areaCode);
				}

                if(stationItems && stationItems.length>0){
                    for(var i=0;i< stationItems.length;i++){
                        removeStations+=stationItems[i].stationId+',';
                    }
                }
                var params={};
                params.citys=citys;
                params.busiType=busiType.getValue();
                params.stations=removeStations;
                LS.dialog("~/billing/coupon/couponStationSelect","站点选择",950,480,true, params);

			}else{
				LS.message('info','请先选择限制使用城市');
			}
		}
		//站点删除
		function stationDelete() {
			var items = cpnStationGrid.getCheckedItems();
			if(items && items.length>0) {
				for(var i=0; i< items.length; i++) {
				cpnStationGrid.removeItem(items[i]);
			}
			$('#cb__id_cpnStationGrid').removeClass('checkbox_false_part');
			}else {
				LS.message('info', '请选择一条记录');
			}
		}
			//添加站点
		window.selectCpnStation = selectCpnStation;
		function selectCpnStation(items){
			var tmp = new Array();
			var gridItems = cpnStationGrid.getItems();
			if(gridItems.length > 0){
				var isRepeated;
				for(var j = 0;j < items.length;j++){
					isRepeated = false;
					for(var i = 0;i < gridItems.length;i++){
						if(items[j].stationId == gridItems[i].stationId){
							isRepeated = true;
							break;
						}
					}
					if (!isRepeated) {
						tmp.push(items[j]);
					}
				}
					cpnStationGrid.appendItem(tmp)
			}else{
				cpnStationGrid.appendItem(items)
			}
			<%--cpnStationGrid.selectAll(true);--%>
		}

		//保存
		function save(status){

			if(timeCondBeg.getValue() == ""){
				LS.message("info","优惠开始时段不能为空");
				return;
			}
			if(timeCondEnd.getValue() == ""){
				LS.message("info","优惠结束时段不能为空");
				return;
			}
			if(parseInt(timeCondEnd.getValue().replace(":","")) < parseInt(timeCondBeg.getValue().replace(":",""))){
				LS.message("info","优惠结束时段不能小于开始时段");
				return;
			}
			if(!couponForm.valid()){
				return;
			}
			if(!isNumber()){
				return;
			}
			var _eftDate = eftDate.getValue();
			var _invDate = invDate.getValue();
			let _now = DateUtils.getTime('yyyy-MM-dd');
			if(!LS.isEmpty(_eftDate) &&_eftDate < _now){
				LS.message("info","生效开始时间不能小于当前时间");
				return;
			}
			if(!LS.isEmpty(_invDate) &&_invDate < _now){
				LS.message("info","生效结束时间不能小于当前时间");
				return;
			}
			if(!LS.isEmpty(_eftDate)&&!LS.isEmpty(_invDate)&&_eftDate>=_invDate){
				LS.message("info","生效开始时间不能大于失效结束时间");
				return;
			}
			validateFile();
			// 优惠券状态为：草稿
			cpnStatus.setValue("0");
			var _msg = "保存成功";
			if('1' == status){
				cpnStatus.setValue("1");
				_msg = "发布成功";
			}
			<%--var cItems = cpnCityGrid.getCheckedItems();--%>
			var cItems = cpnCityGrid.getItems();
			if(cItems.length > 0){
				var cItemsStr = '';
				for(var i = 0 ; i < cItems.length; i++){
					if(i != 0){
						cItemsStr = cItemsStr + ",";
					}
					cItemsStr = cItemsStr + cItems[i].areaCode;
				}
				dctCondCity.setValue(cItemsStr);
			}else{
				dctCondCity.setValue(null);
			}
			<%--var sItems = cpnStationGrid.getCheckedItems();--%>
			var sItems = cpnStationGrid.getItems();
			if(sItems.length > 0){
				var sItemsStr = '';
				for(var i = 0 ; i < sItems.length; i++){
					if(i != 0){
						sItemsStr = sItemsStr + ",";
					}
					sItemsStr = sItemsStr + sItems[i].stationId;
				}
				dctCondStation.setValue(sItemsStr);
			}else{
				dctCondStation.setValue(null);
			}
			couponForm.submit("~/billing/coupon/saveCoupon",function(e){
				if(e.items[0].successful==true){
	   		 		LS.message("info",_msg);
	   		 		LS.parent().query();
	   		 		LS.window.close();
	   		 	}else{
	   		 		LS.message("error",e.items[0].resultValue);
	   		 	}
		    });
		}
		//发布
		function public(){
			if(timeCondBeg.getValue() == ""){
				LS.message("info","优惠开始时段不能为空");
				return;
			}
			if(timeCondEnd.getValue() == ""){
				LS.message("info","优惠结束时段不能为空");
				return;
			}
			if(parseInt(timeCondEnd.getValue().replace(":","")) < parseInt(timeCondBeg.getValue().replace(":",""))){
				LS.message("info","优惠结束时段不能小于开始时段");
				return;
			}

			if(timeCondBeg.getValue() == ""){
				LS.message("info","优惠开始时段不能为空");
				return;
			}
			if(timeCondEnd.getValue() == ""){
				LS.message("info","优惠结束时段不能为空");
				return;
			}
			if(parseInt(timeCondEnd.getValue().replace(":","")) < parseInt(timeCondBeg.getValue().replace(":",""))){
				LS.message("info","优惠结束时段不能小于开始时段");
				return;
			}
			if(!couponForm.valid()){
				return;
			}
			if(!isNumber()){
				return;
			}
			var _eftDate = eftDate.getValue();
			var _invDate = invDate.getValue();
			let _now = DateUtils.getTime('yyyy-MM-dd');
			if(!LS.isEmpty(_eftDate) &&_eftDate < _now){
				LS.message("info","生效开始时间不能小于当前时间");
				return;
			}
			if(!LS.isEmpty(_invDate) &&_invDate < _now){
				LS.message("info","生效结束时间不能小于当前时间");
				return;
			}
			if(!LS.isEmpty(_eftDate)&&!LS.isEmpty(_invDate)&&_eftDate>=_invDate){
				LS.message("info","生效开始时间不能大于失效结束时间");
				return;
			}
			validateFile();
			LS.confirm("发布后不能修改，是否继续发布？",function(data){
				if(data){
					save(1);
				}
			});

		}

		//取消
		function cancel(){
			LS.window.close();
		}
		function getOrgCodes(){
			var initValue=null;
	      	if(!LS.isEmpty(orgCodes.getValue())){
		        initValue=orgCodes.getValue().split(",");
		      }
		    js_util.selectOrgTree(true, null, true, initValue, false, setOrg);
		}

	    function setOrg(node){
	      if(node==null){
	        return;
	      }
	      var _orgCodes="";
	      var _orgCodeName="";
	      if(node.length==undefined){
	        _orgCodes=node.id;
	        _orgCodeName=node.text;
	      }else{
	        for(var i=0;i< node.length;i++){
	          if(node.length==i+1){
	            _orgCodes+=node[i].id;
	            _orgCodeName+=node[i].text;
	          }else{
	            _orgCodes+=node[i].id+',';
	            _orgCodeName+=node[i].text+',';
	          }
	        }
	      }
	      orgCodeName.setValue(_orgCodeName);
		  orgCodes.setValue(_orgCodes);
	    }

		function getOrgCode(){

		}

	// 图片的验证
	   function validateFile(){
	   	  if(typeof(fileData) != "undefined"){
		        if(fileData.value==null || fileData.value==''){
		          return;
		        }
		        if(!/\.(gif|jpg|jpeg|png|bmp)$/.test(document.getElementById("fileData").value.toLowerCase())){
		         	LS.message("warn","图片类型必须是gif,jpeg,jpg,png中的一种");
		        	return;
		      	}
		      	if(isIE()){
		      		 fileData.value=fileData.value;
		      	}else{
		      		fileData.files = fileData.files;
		      	}

	      }
	   }

	   //预览本地图片代码
window.previewImage = function(previewImageId,file) {
	var MAXWIDTH = 200;
	var MAXHEIGHT = 120;
	var imgIdTmp = 'imghead_'+previewImageId;
	var div = document.getElementById(previewImageId);
	//if (file.value!=null && file.value!='') {
	if (file.files || file.files[0]) {
		div.innerHTML = "<img id="+imgIdTmp+">";
		var img = document.getElementById(imgIdTmp);
		img.onload = function() {
			var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT,
					img.offsetWidth, img.offsetHeight);
			img.width = rect.width;
			img.height = rect.height;
			img.style.marginLeft = rect.left + 'px';
			img.style.marginTop = rect.top + 'px';
		}
		var reader = new FileReader();
		reader.onload = function(evt) {
			img.src = evt.target.result;
		}
		reader.readAsDataURL(file.files[0]);
	} else {
		var sFilter = 'filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale,src="';
		file.select();
		var src ;//document.selection.createRange().text;
		div.innerHTML = "<img id="+imgIdTmp+">";
		var img = document.getElementById(imgIdTmp);
		//img.filters.item('DXImageTransform.Microsoft.AlphaImageLoader').src = src;
		var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT, img.offsetWidth,
				img.offsetHeight);
		status = ('rect:' + rect.top + ',' + rect.left + ',' + rect.width
				+ ',' + rect.height);
		div.innerHTML = "<div id='divhead' style='width: "+rect.width+" px; height: "+rect.height+" px; margin-top: "+rect.top+" px; margin-left: "+rect.left+" px;"+sFilter+src+"\"'></div>";
	}
}
function clacImgZoomParam(maxWidth, maxHeight, width, height) {
	var param = {
		top : 0,
		left : 0,
		width : width,
		height : height
	};
	if (width > maxWidth || height > maxHeight) {
		rateWidth = width / maxWidth;
		rateHeight = height / maxHeight;

		if (rateWidth > rateHeight) {
			param.width = maxWidth;
			param.height = Math.round(height / rateWidth);
		} else {
			param.width = Math.round(width / rateHeight);
			param.height = maxHeight;
		}
	}
	param.left = Math.round((maxWidth - param.width) / 2);
	param.top = Math.round((maxHeight - param.height) / 2);
	return param;
}

function isIE() { //ie?
     if (navigator.appName.indexOf("Explorer") > -1)
       return true;
     else
       return false;
   }

<%--function setDctCondType(){--%>
<%--	var _dctCondType = dctCondType.value;--%>
<%--	if(_dctCondType === '00'){--%>
<%--		$("#lab_dctProdId").hide();--%>
<%--		$("#txt_dctProdId").hide();--%>
<%--	}else{--%>
<%--		$("#lab_dctProdId").show();--%>
<%--		$("#txt_dctProdId").show();--%>
<%--	}--%>
<%--}--%>

function checkIsEffect(){
	var _isEffect = isEffect.getValue();
	if('1' === _isEffect){
		eftDate.setEnabled(false);
		eftDate.setValue();
	}else{
		eftDate.setEnabled(true);
		eftDate.setValue(effectDate.getValue());
	}
}

function changeCpnType(){
	var _cpnType = cpnType.getValue();
	var hiddenCell1 = document.getElementById('maximumDiscountAmtHide');
	var hiddenCell2 = document.getElementById('maximumDiscountAmtValueHide');
	var hiddenCell3 = document.getElementById('dctCondHide');
	var hiddenCell4 = document.getElementById('dctCondValueHide');
			var hiddenCell5 = document.getElementById('dctCondRemark');

	if('02' == _cpnType){
			lab_cpnAmt.setText("折扣值");
			hiddenCell1.style.display = 'table-cell';
			hiddenCell2.style.display = 'table-cell';
			hiddenCell3.style.display = 'table-cell';
			hiddenCell4.style.display = 'table-cell';
			hiddenCell5.style.display = 'table-cell';
	}else{
			lab_cpnAmt.setText("减免面额");
			hiddenCell1.style.display = 'none';
			hiddenCell2.style.display = 'none';
			hiddenCell3.style.display = 'table-cell';
			hiddenCell4.style.display = 'table-cell';
			hiddenCell5.style.display = 'table-cell';

	}
	couponForm.addValid(cpnAmt,"required");
}

            window.timeBeginChange = timeBeginChange;
            window.timeEndChange = timeEndChange;
            function timeBeginChange(Value) {
				timeCondBeg.setValue(Value);
            }
            function timeEndChange(Value) {
				timeCondEnd.setValue(Value);
            }
            setDisplayTimeCond();

            //使用条件类型改变事件
            function setDisplayTimeCond(){
            var _timeCondFlag = timeCondFlag.getValue();
                if("1"==_timeCondFlag){
					timeCondBeg.setValue('00:00');
					timeCondEnd.setValue('23:59');
                    $('#begTime').val(timeCondBeg.getValue());
                    $('#endTime').val(timeCondEnd.getValue());
					$("#begTime").attr("disabled","disabled");
					$("#endTime").attr("disabled","disabled");
					$('.yhsjd').hide();
                }else{
                    $('#begTime').val(timeCondBeg.getValue());
                    $('#endTime').val(timeCondEnd.getValue());
					$("#begTime").removeAttr("disabled");
					$("#endTime").removeAttr("disabled");
					$('.yhsjd').show();
                }
            }

		function integralNumChanged() {
			var checked = $("[name='integralNumFlag']").prop("checked");
			if (checked) {
				$("#integralNumSpan").hide();
				$("[name='integralNum']").val('');
			} else {
				$("#integralNumSpan").show();
			}
		}
		$(function(){
			// 页面初始化时根据后端数据设置checkbox和输入框状态
			integralNumChanged();
		});
	</ls:script>
</ls:body>
</html>