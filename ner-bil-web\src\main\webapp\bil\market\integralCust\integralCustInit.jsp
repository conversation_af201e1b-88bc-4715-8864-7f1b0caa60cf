<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="积分流水查询" />
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
<ls:body>
	<ls:form name="custLogForm" id="custLogForm" >
		<ls:title text="查询条件"></ls:title>
      		  <table align="center" class="tab_search">
      		  	<tr>
      		  		<td><ls:label text="手机号:"></ls:label></td>
      		  		<td><ls:text name="phone" type="number"></ls:text></td>
      		  		
      		  		<td><ls:label text="时间:"></ls:label></td>
      		  		<td ><ls:date id="beginTime" name="beginTime" format="yyyy-MM-dd HH:mm:ss"
			                displayTime="true" /></td>
                    <td ><ls:label text="--" textAlign="center"/></td>
                    <td ><ls:date id="endTime" name="endTime" format="yyyy-MM-dd HH:mm:ss"
			                displayTime="true" /> </td>
	                <td><ls:label text="变更类型:"></ls:label></td>
	                <td>
	                    <ls:select name="chargeType">
	                    	<ls:option value="all" text="全部" ></ls:option>
	                        <ls:options property="chargeTypeList" scope="request" text="codeName" value="codeValue"/>
	                    </ls:select>
	                </td>
      		  	</tr>
      		  	<tr>
	      		  <td colspan="8">
	                    <div class="pull-right">
	                        <ls:button text="查询" onclick="query"/>
	                        <ls:button text="清空" onclick="clearAll"/>
	                    </div>
	                </td>
	          	</tr>
	          	<tr>
	                <td colspan="8">
	                	<ls:grid url="" name="custLogGrid" caption="积分流水列表" showRowNumber="false"
	                             height="350px" showCheckBox="false" >
	                    <ls:column caption="id" name="id" hidden="true"/>
	                    <ls:column caption="流水号" name="integralNo" align="center" width="20%"/>
	                    <ls:column caption="事件" name="eventName" align="center"  width="15%"/>
	                    <ls:column caption="手机号" name="phone" align="center" width="15%"/>
	                    <ls:column caption="变更值" name="chargeNum" align="center"  width="15%"/>
	                    <ls:column caption="变更类型" name="chargeType" align="center" hidden="true"/>
	                    <ls:column caption="变更类型" name="chargeTypeName" align="center" width="15%"/>
	                    <ls:column caption="时间" name="chargeTime"  align="center" width="20%"/>
	                    <ls:pager pageSize="15,20"></ls:pager>
	                </ls:grid></td>
	            </tr>
	        </table>
	</ls:form>
	<ls:script>
		query();
		function query(){
			var datas={};
            datas = custLogForm.getFormData();
            custLogGrid.query("~/billing/integralCustLog/getIntegralCustLogList",datas);
		}
		
		function clearAll(){
            custLogForm.clear();
        };
        
        window.onload = function() {
            initGridHeight('custLogForm','custLogGrid');
        }
		
	</ls:script>
</ls:body>
</html>