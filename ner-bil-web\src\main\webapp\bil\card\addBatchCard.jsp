<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="新增">
</ls:head>
<ls:body>
    <ls:form id="addBatchCardForm" name="addBatchCardForm">
        <ls:text name="cardId" type="hidden" property="cardId"></ls:text>
        <table class="tab_search">
            <tr>
                <td><ls:label text="充值卡名称" ref="cardName"/></td>
                <td><ls:text name="cardName" property="cardName" readOnly="true"></ls:text></td>
            </tr>
            <tr>
                <td><ls:label text="制卡数量" ref="cardCount"/></td>
                <td><ls:text name="cardCount" property="cardCount" required="true"></ls:text></td>
            </tr>
        </table>
    </ls:form>
    <table>
        <tr>
            <td>
                <div class="pull-right">
                    <ls:button text="保存" onclick="save" name="save"/>
                    <ls:button text="取消" onclick="close" />
                </div>
            </td>
        </tr>
    </table>

    <ls:script>

        function save(){
            addBatchCardForm.submit("~/bil/rechargeCard/addBatchCard",function(e){
                if(e.cardName != null && e.cardName != ''){
                    LS.message("info","保存成功");
                    LS.parent().doSearch();
                    LS.window.close();
                }else{
                    LS.message("info","保存失败");
                }
            });
        }

        function close(){
             LS.window.close();
        }


    </ls:script>
</ls:body>
</html>