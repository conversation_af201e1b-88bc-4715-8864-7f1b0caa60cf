<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费配置" />
<ls:body>
		<ls:form id="addTariffForm" name="addTariffForm">
				<ls:title text="资费标准基本信息 "></ls:title>
				<table class="tab_search">
				    <ls:text name="peBe" value="01" visible="false"></ls:text>
						<tr>
								<td>
										<ls:label text="资费名称" ref="planName" />
								</td>
								<td>
										<ls:text name="planName" required="true">
										</ls:text>
								</td>
								<td>
										<ls:label text="资费标准" />
								</td>
								<td>
										<ls:select name="subBe" required="true">
												<ls:options property="subBeList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
						</tr>
				</table>
				<ls:title text="车辆租赁费规则"></ls:title>
				<table class="tab_search">
						<tr>
								<td width="200px">
										<ls:label text="计费方式" ref="chargeMode" />
								</td>
								<td colspan="3">
										<ls:checklist type="radio" name="chargeMode" required="true" columns="20" property="chargeMode">
												<ls:checkitems property="chargeModeList" scope="request" text="codeName" value="codeValue"></ls:checkitems>
										</ls:checklist>
								</td>
						</tr>
						<tr>
								<td width="200px">
										<ls:label text="计费模式" />
								</td>
								<td colspan="3">
										<ls:checklist type="radio" name="chargeType" required="true" columns="20">
												<ls:checkitems scope="request" property="chargeTypeList" text="codeName" value="codeValue"></ls:checkitems>
										</ls:checklist>
								</td>
						</tr>
						<tr>
								<td width="200px">
										<ls:label text="最小计费单元" />
								</td>
								<td width="150px">
										<ls:text name="chargeNum"></ls:text>
								</td>
								<td width="100px">
										<ls:select name="chargeUnit">
												<ls:option value="01" text="分"></ls:option>
												<ls:option value="02" text="时"></ls:option>
												<ls:option value="03" text="天"></ls:option>
												<ls:option value="04" text="米"></ls:option>
												<ls:option value="05" text="公里"></ls:option>
										</ls:select>
								</td>
								<td>
										<div class="pull-left">
												<ls:label text="说明：不足1分钟，按1分钟收费"></ls:label>
										</div>
								</td>
						</tr>
				</table>
		</ls:form>
		<ls:title text="其他服务项目"></ls:title>
		<table align="center" class="tab_search">
				<tr>
						<td>
								<ls:grid height="200px"  width="100%" url="" name="attachItemGrid" cellEdit="true" treeGrid="true" expandColumn="value" showCheckBox="true" expand="true" primaryKey="id" treeGridModel="adjacency">
										<ls:gridToolbar name="code_grid_bar">
												<ls:gridToolbarItem name="addTariff" text="新增" imageKey="add" onclick="addTariff" />
												<ls:gridToolbarItem name="deleteTariff" text="删除" imageKey="delete" onclick="deleteTariff" />
										</ls:gridToolbar>
										<ls:column caption="收费项目编码" name="chargeItemCode" hidden="true"/>
										<ls:column caption="收费项目名称" name="itemName" editableEdit="false" />
										<ls:column caption="是否必须" name="buyIdentity" hidden="true" />
										<ls:column caption="是否必须" name="needFlagName" editableEdit="false" />
										<ls:column caption="是否必须" name="needFlag" hidden="true" />
										<ls:column caption="收费单位" name="chargeType">
												<ls:selectEditor name="chargeType" value="">
														<ls:optionEditor value="" text="">
														</ls:optionEditor>
														<ls:optionEditor value="01" text="次">
														</ls:optionEditor>
														<ls:optionEditor value="02" text="租赁时间">
														</ls:optionEditor>
														<ls:optionEditor value="03" text="行驶里程">
														</ls:optionEditor>
														<ls:optionEditor value="04" text="可结转">
														</ls:optionEditor>
												</ls:selectEditor>
										</ls:column>
										<ls:column caption="收费项目说明" name="remark">
												<ls:textEditor value=""></ls:textEditor>
										</ls:column>
										<ls:pager pageSize="20" />
								</ls:grid>
						</td>
				</tr>
				<tr>
						<td>
								<ls:button text="保存" onclick="savePlan"></ls:button>
						</td>
				</tr>
		</table>
		<ls:script>
    
    function addTariff(){
       LS.dialog("~/billing/chargeItem/init?flag=1","费用项信息",600,300,true);
    }
    
    function editTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          window.location.href=rootUrl+"billing/tariff/editTariff?id="+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function deleteTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      LS.confirm('确认删除吗?', function(data) {
          if (data) {
              LS.ajax("~/billing/tariff/deleteTariff?id="+item[0], {}, function(e) {
                if (e.resultValue=="Y") {                  
                  LS.message("info", "删除成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    
    function savePlan(){
       var dataParams = {
        items:attachItemGrid.getItems(),
        formData:addTariffForm.getFormData()
       };
      LS.ajax("~/billing/tariff/saveOrUpdate",dataParams,function(e){
        if(e.items[0]=="saveSucc"){
          LS.message("info","保存成功");
        }else if(e.items[0]=="updateSucc"){
          LS.message("info","更新成功");  
        }else{
          LS.message("info","保存失败:"+e.items[0].msg);
        }
      });
    }
    
    function startTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var items=tariffGridGrid.getCheckedItems();
      for(var i=0;i< items.length;i++){
        if(items[i].tariffGridStatus!="03"){
          LS.message("info","只能对已发布的内容进行撤销");
          return;
        }
      }
      LS.confirm('确认启用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = item;
              LS.ajax("~/billing/tariff/startTariff", params, function(e) {
                if (e.items[0]=="Y") {                  
                  LS.message("info", "启用成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    function stopTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var items=tariffGridGrid.getCheckedItems();
      for(var i=0;i< items.length;i++){
        if(items[i].tariffGridStatus!="03"){
          LS.message("info","只能对已发布的内容进行撤销");
          return;
        }
      }
      LS.confirm('确认停用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = item;
              LS.ajax("~/billing/tariff/stopTariff", params, function(e) {
                if (e.items[0]=="Y") {                  
                  LS.message("info", "停用成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    
    window.setItem = setItem;
    function setItem(item) {
       attachItemGrid.appendItem(item);
    }
    </ls:script>
</ls:body>
</html>