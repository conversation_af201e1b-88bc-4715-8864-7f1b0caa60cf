package com.ls.ner.billing.charge.condition;

import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;
/**
 * 充电资费管理---服务项目维护-查询条件
 * <AUTHOR>
 */
public class ChargeSerItemQueryCondition extends QueryCondition {
	private String itemNo;//收费项目编号
	private String itemName;//收费项目名称
	private String buyType;//是否必购，01选购、02必购
	private String pBe;//业务大类：01租车 02 充电
	private String itemType;//项目类型：01服务02押金
	private String price;//收费项目单价
	private String itemStatus;//状态，1启用、0停用
	private String unUseItemNos;//不使用的项目编号
	private String freeNum;//免费段
	private String systemId;//主键

	private String itemChargeMode;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getFreeNum() {
		return freeNum;
	}

	public void setFreeNum(String freeNum) {
		this.freeNum = freeNum;
	}

	public String getItemNo() {
		return itemNo;
	}
	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}
	public String[] getItemNos() {
		if(StringUtils.nullOrBlank(itemNo))
			return null;
		return itemNo.split(",");
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getBuyType() {
		return buyType;
	}
	public void setBuyType(String buyType) {
		this.buyType = buyType;
	}
	public String getpBe() {
		return pBe;
	}
	public void setpBe(String pBe) {
		this.pBe = pBe;
	}
	public String getItemType() {
		return itemType;
	}
	public void setItemType(String itemType) {
		this.itemType = itemType;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getItemStatus() {
		return itemStatus;
	}
	public void setItemStatus(String itemStatus) {
		this.itemStatus = itemStatus;
	}
	public String getUnUseItemNos() {
		return unUseItemNos;
	}
	public void setUnUseItemNos(String unUseItemNos) {
		this.unUseItemNos = unUseItemNos;
	}
	public String[] getUnUseItemNoss() {
		if(StringUtils.nullOrBlank(unUseItemNos))
			return null;
		return unUseItemNos.split(",");
	}

	public String getItemChargeMode() {
		return itemChargeMode;
	}

	public void setItemChargeMode(String itemChargeMode) {
		this.itemChargeMode = itemChargeMode;
	}
}
