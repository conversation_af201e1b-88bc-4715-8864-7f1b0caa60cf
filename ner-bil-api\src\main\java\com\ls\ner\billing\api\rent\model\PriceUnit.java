package com.ls.ner.billing.api.rent.model;

import java.io.Serializable;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 计价单位
 * 
 * 总价还是
 * 每单位计费，计费单位是多少
 * <AUTHOR>
 *
 */
public class PriceUnit implements Serializable{
	
	private static final long serialVersionUID = 7629382265832956593L;

	public static final String CODE_TYPE = "billingPriceUnit";
	
	public static final String MINUTE = "01";
	public static final String HOUR = "02";
	public static final String DAY = "03";
	public static final String YEAR = "07";
	
	public static final String M = "04";
	public static final String KM = "05";
	/**
	 * 次数
	 */
	public static final String TIMES = "00";
//	/**
//	 * 做为单价使用还是直接作为1 有无单位 01 单价 02 总价
//	 */
//	private String priceMethod;
	
	private int value = 1;
	/**
	 * 单位 时间 长度等 米
	 */
	private String unit = TIMES;
	
//	@JsonIgnore
	private String unitName;

	private String desc;
	
	public PriceUnit(int value,String unit){
		this.value = value;
		this.unit = unit;
	}

	public PriceUnit(){
		
	}
	
//	public String getPriceMethod() {
//		return priceMethod;
//	}
//	public void setPriceMethod(String priceMethod) {
//		this.priceMethod = priceMethod;
//	}
	public int getValue() {
		return value;
	}
	public void setValue(int value) {
		this.value = value;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	
	
	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	
	
	public String getDesc() {
		String valueDesc = getValue()==1?"":String.valueOf(getValue());
		
		return valueDesc+unitName;
	}


	public void setDesc(String desc) {
		this.desc = desc;
	}

	@Override
	public int hashCode() {
		
		return Objects.hash(value,unit);
	}
	@Override
	public boolean equals(Object obj) {
		if(!( obj instanceof PriceUnit)){
			return false;
		}
		PriceUnit that = (PriceUnit) obj;
		return this.value== that.value && this.unit.equals(that.unit);
	}
	/**
	 * 是否为代表一次的单位
	 * @return
	 */
	public boolean equalToOnce(){
		return this.value== 1 && this.unit.equals(TIMES);
	}
	
	public static final PriceUnit ONCE = new PriceUnit();

}
