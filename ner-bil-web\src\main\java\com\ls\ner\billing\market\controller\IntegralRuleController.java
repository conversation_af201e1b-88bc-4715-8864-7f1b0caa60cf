package com.ls.ner.billing.market.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.exception.BaseBusinessException;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.bo.IntegeralRuleSignExtend;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.service.IIntegeralRuleService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.util.BeanUtil;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:积分规则管理
 * 创建人:nijiajing
 * 创建时间: 2024-04-10 12:08
 */
@Controller
@RequestMapping("/billing/integralRule")
public class IntegralRuleController extends QueryController<IntegeralRuleBo> {

    private static final Logger log = LoggerFactory.getLogger(IntegralRuleController.class);

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(value = "integeralRuleService", serviceTypes = ServiceType.LOCAL)
    private IIntegeralRuleService integeralRuleService;

	@ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
	private ICouponService couponService;
    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    private RedisCluster redisCluster = RedisCluster.getInstance();

    private static final String INTEGRAL_CLEAR_TIME_KEY = "integral:clear:time";

    /**
     * 初始化积分规则管理界面
     * @param m
     * @return
     */
    @RequestMapping("/init")
    public String init(Model m, HttpServletRequest request) {
        String singleSelect = ESAPI.encoder().encodeForJavaScript(request.getParameter("singleSelect"));

    	List<CodeBO> integralEventTypeList = codeService.getStandardCodes("integralEventType", null);
        m.addAttribute("eventTypeList", integralEventTypeList);

        List<CodeBO> integralRuleStatusList = codeService.getStandardCodes("integralRuleStatus", null);
        m.addAttribute("ruleStatusList", integralRuleStatusList);
        List<IntegeralRuleBo> dataList = new ArrayList<IntegeralRuleBo>();
        IntegeralRuleBo integeralRuleBo = new IntegeralRuleBo();
        integeralRuleBo.setEventType("all");
        integeralRuleBo.setRuleStatus("all");
    	Map<String,String> eventTypeMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(integralEventTypeList)){
			for (CodeBO codeBO : integralEventTypeList) {
				eventTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		Map<String,String> ruleStatueMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(integralRuleStatusList)){
			for (CodeBO codeBO : integralRuleStatusList) {
				ruleStatueMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
        dataList =  integeralRuleService.queryRuleList(integeralRuleBo);
    	for (IntegeralRuleBo bo : dataList) {
    		bo.setEventTypeName(eventTypeMap.get(bo.getEventType()));
    		bo.setRuleStatusName(ruleStatueMap.get(bo.getRuleStatus()));
		}
        m.addAttribute("ruleGrid", dataList);
        m.addAttribute("ruleForm", integeralRuleBo);
        m.addAttribute("singleSelect",singleSelect);

    	return "/bil/market/integralRule/integralRuleInit";
    }

    /**
     * 获取积分规则列表
     * @param params
     * @return
     */
    @RequestMapping("/getRuleList")
    public @ItemResponseBody
    QueryResultObject getRuleList(@QueryRequestParam("params")
                                         RequestCondition params) {
    	int count = 0;
    	List<IntegeralRuleBo> dataList = new ArrayList<IntegeralRuleBo>();
    	IntegeralRuleBo integeralRuleBo = this.rCondition2QCondition(params);
    	Map<String,String> eventTypeMap = new HashMap<>();
		List<CodeBO> eventTypeList = codeService.getStandardCodes("integralEventType", null);
		if (CollectionUtils.isNotEmpty(eventTypeList)){
			for (CodeBO codeBO : eventTypeList) {
				eventTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		Map<String,String> ruleStatueMap = new HashMap<>();
		List<CodeBO> ruleStatueList = codeService.getStandardCodes("integralRuleStatus", null);
		if (CollectionUtils.isNotEmpty(ruleStatueList)){
			for (CodeBO codeBO : ruleStatueList) {
				ruleStatueMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
    	count = integeralRuleService.queryRuleListCount(integeralRuleBo);
    	if (count>0) {
        	dataList =  integeralRuleService.queryRuleList(integeralRuleBo);
        	for (IntegeralRuleBo bo : dataList) {
        		bo.setEventTypeName(eventTypeMap.get(bo.getEventType()));
        		bo.setRuleStatusName(ruleStatueMap.get(bo.getRuleStatus()));
			}
		}
    	log.info("====获取积分规则列表===dataList:"+dataList);

    	return RestUtils.wrappQueryResult(dataList, count);
    }

	/**
     * 查询积分规则详情
     * @param m
     * @param request
     * @return
     */
    @RequestMapping("/getRuleInfo")
    public String getRuleInfo(IntegeralRuleBo integeralRuleBo, Model m, HttpServletRequest request) {

    	String ruleId = request.getParameter("ruleId");
    	integeralRuleBo = integeralRuleService.getRuleInfo(ruleId);
		log.info("查询规则integeralRuleService.getRuleInfo:{}", JSONUtil.toJsonStr(integeralRuleBo));
    	List<CodeBO> integralEventTypeList = codeService.getStandardCodes("integralEventType", null);
        m.addAttribute("eventTypeList", integralEventTypeList);

        List<CodeBO> integralRuleStatusList = codeService.getStandardCodes("integralRuleStatus", null);
        m.addAttribute("ruleStatusList", integralRuleStatusList);

        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("ruleForm", integeralRuleBo);

        return "/bil/market/integralRule/integralRuleInfo";
    }

    /**
     * 创建积分规则初始化
     * @param integeralRuleBo
     * @param m
     * @param request
     * @return
     */
    @RequestMapping("/addRule")
    public String addRule(IntegeralRuleBo bo, Model m, HttpServletRequest request) {

    	bo = new IntegeralRuleBo();
    	List<CodeBO> integralEventTypeList = codeService.getStandardCodes("integralEventType", null);
        m.addAttribute("eventTypeList", integralEventTypeList);

        List<CodeBO> integralRuleStatusList = codeService.getStandardCodes("integralRuleStatus", null);
        m.addAttribute("ruleStatusList", integralRuleStatusList);

        bo.setIsDiscriminateUser("0");
        bo.setRuleStatus("01");
        bo.setEventType("01");
        bo.setMaxTimes(null);
        bo.setMoney(null);

        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("ruleForm", bo);

        return "/bil/market/integralRule/integralRuleAdd";
    }

    /**
     * 修改积分规则初始化
     * @param integeralRuleBo
     * @param m
     * @param request
     * @return
     */
    @RequestMapping(value = "/editRule")
    public String  editRule(IntegeralRuleBo integeralRuleBo, Model m, HttpServletRequest request) {

    	String ruleId = request.getParameter("ruleId");
    	integeralRuleBo = integeralRuleService.getRuleInfo(ruleId);
    	log.info("查询规则integeralRuleService.getRuleInfo:{}", JSONUtil.toJsonStr(integeralRuleBo));
    	List<CodeBO> integralEventTypeList = codeService.getStandardCodes("integralEventType", null);
        m.addAttribute("eventTypeList", integralEventTypeList);

        List<CodeBO> integralRuleStatusList = codeService.getStandardCodes("integralRuleStatus", null);
        m.addAttribute("ruleStatusList", integralRuleStatusList);

        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("ruleForm", integeralRuleBo);

        return "/bil/market/integralRule/integralRuleEdit";
    }

    /**
     * 保存积分规则
     * @param integeralRuleBo
     * @return
     */
    @RequestMapping(value = "/saveRule", method = RequestMethod.POST)
    public @ItemResponseBody WrappedResult  saveRule(@RequestBody Map<String, Object> param) {
		WrappedResult result = new WrappedResult();
		log.info("=====保存积分规则====入参:"+JsonUtil.obj2Json(param));
		IntegeralRuleBo bo = new IntegeralRuleBo();
		BeanUtil.populate(bo, param);
		bo.setExtendJson(StringUtil.nullForString(param.get("extendJson")));

		try {
			String eventType = bo.getEventType();
			if (StringUtils.isEmpty(eventType)) {
				result.setResultValue("事件类型不为空");
			}

			String userTypes = bo.getUserTypeListStr();
			if (StringUtils.isNotBlank(userTypes)) {
				List<Map<String, Object>> userTypesList = new ArrayList<Map<String,Object>>();
				Map<String,Object> userTypeMap = JsonUtil.json2Obj(userTypes, Map.class);

				if (userTypeMap != null && userTypeMap.size()>0) {
					userTypesList.add(userTypeMap);
					bo.setUserTypeList(userTypesList);
				}
			}
			if ("05".equals(bo.getEventType())){
				checkSignParma(bo);
			}

			//完善信息是支持送积分&优惠券的 如果后管配置积分&优惠券 只能同时存在一种情况 这里做校验优惠卷是否配置了用途为首次添加爱车的优惠卷
			//触发事件为绑定车辆才校验
			if("02".equals(eventType)){
				MarketCondition condition = new MarketCondition();
				condition.setCpnPurpose("1");
				condition.setCpnStatus("1");
				CouponBo couponBo = couponService.getCouponCpnPurposeDetail(condition);
				if(couponBo!=null){
					result.setResultValue("优惠卷管理已经配置过用途为首次添加爱车的优惠卷,无法创建触发时间为绑定车辆的积分规则");
					return result;
				}
			}


			if (StringUtil.isBlank(bo.getRuleId()) ) {
				integeralRuleService.addRule(bo);
			} else {
				integeralRuleService.editRule(bo);
			}
			result.setSuccessful(true);
		} catch (RuntimeException e) {
			result.setResultValue(e.getMessage());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setResultValue("保存积分规则失败");
			return result;
		}
        return result;
    }

	private  void checkSignParma(IntegeralRuleBo bo) {
		if (!"1".equals(bo.getIsDiscriminateUser())){
			if (StringUtils.isBlank(bo.getExtendJson())){
				throw new BaseBusinessException("签到参数异常");
			}
			IntegeralRuleSignExtend signExtend = JSONUtil.parseObj(bo.getExtendJson()).toBean(IntegeralRuleSignExtend.class);
			if (!ObjectUtil.isAllNotEmpty(signExtend.getMaxMoney(),signExtend.getMinMoney())){
				throw new BaseBusinessException("签到参数异常");
			}
			if (signExtend.getMaxMoney()<signExtend.getMinMoney()){
				throw new BaseBusinessException("签到参数异常");
			}
		}else {
			if (ObjectUtil.isEmpty(bo.getUserTypeList())){
				throw new BaseBusinessException("签到参数异常");
			}
			boolean anyMatch = bo.getUserTypeList().stream().anyMatch(x -> {
				if (StringUtils.isBlank(StringUtil.nullForString(x.get("extendJson")))) {
					return true;
				}
				IntegeralRuleSignExtend signExtend = JSONUtil.parseObj(x.get("extendJson")).toBean(IntegeralRuleSignExtend.class);
				if (!ObjectUtil.isAllNotEmpty(signExtend.getMaxMoney(), signExtend.getMinMoney())) {
					return true;
				}
				if (signExtend.getMaxMoney() < signExtend.getMinMoney()) {
					return true;
				}
				return false;
			});
			if (anyMatch){
				throw new BaseBusinessException("签到参数异常");
			}
		}
	}

	/**
     * 更新积分规则状态
     * @param integeralRuleBo
     * @return
     */
    @RequestMapping(value = "/updateStatus")
    public @ResponseBody WrappedResult  updateStatus(@RequestBody Map<String, Object> dataParams, Model m) {
    	String mark = "success";
    	try {
    		IntegeralRuleBo bo = new IntegeralRuleBo();
        	BeanUtil.populate(bo, dataParams);
//			IntegeralRuleBo queryBo = integeralRuleService.getRuleInfo(bo.getRuleId());
//			if("02".equals(bo.getRuleStatus()) && queryBo!=null && "02".equals(queryBo.getEventType()) ){
//				MarketCondition condition = new MarketCondition();
//				condition.setCpnPurpose("1");
//				condition.setCpnStatus("1");
//				CouponBo couponBo = couponService.getCouponCpnPurposeDetail(condition);
//				if(couponBo!=null){
//					return WrappedResult.failedWrappedResult("优惠卷管理已经配置过用途为首次添加爱车的优惠卷,无法启用触发时间为绑定车辆的积分规则");
//				}
//			}

        	integeralRuleService.updateRuleStatus(bo);
		} catch (Exception e) {
			log.error("积分规则启动失败:{}",e);
			return WrappedResult.failedWrappedResult("积分规则启动失败！");
		}
    	 return WrappedResult.successWrapedResult(true);
    }

    /**
     * 跳转到积分清零时间设置页面
     * @param m
     * @return
     */
    @RequestMapping("/setClearTime")
    public String setClearTime(Model m) {
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        Map<String, Object> clearTimeForm = new HashMap<>();
        clearTimeForm.put("oldClearTime", redisCluster.get(INTEGRAL_CLEAR_TIME_KEY));
        m.addAttribute("clearTimeForm", clearTimeForm);
        return "/bil/market/integralRule/integralClearTime";
    }

    /**
     * 保存积分清零时间到Redis
     */
	@RequestMapping(value = "/saveClearTime", method = RequestMethod.POST)
	public @ResponseBody
	WrappedResult saveClearTime(@RequestBody Map<String, Object> params) {
		WrappedResult result = new WrappedResult();
		try {
			String clearTime = (String) params.get("clearTime");
			if (StringUtils.isBlank(clearTime)) {
				result.setSuccessful(false);
				result.setResultValue("清零时间不能为空");
				return result;
			}

			// 保存到Redis，设置过期时间为30天（2592000秒）
			redisCluster.setex(INTEGRAL_CLEAR_TIME_KEY, clearTime, 2592000);

			log.info("积分清零时间设置成功：{}", clearTime);
			result.setSuccessful(true);
			result.setResultValue("积分清零时间设置成功");
		} catch (Exception e) {
			log.error("保存积分清零时间失败", e);
			result.setSuccessful(false);
			result.setResultValue("保存积分清零时间失败：" + e.getMessage());
		}
		return result;
	}

	@Override
	protected IntegeralRuleBo initCondition() {
		// TODO Auto-generated method stub
		return new IntegeralRuleBo();
	}

}
