package com.ls.ner.billing.gift.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.gift.bo.*;
import com.ls.ner.billing.gift.condition.CarGiftCondition;
import com.ls.ner.billing.gift.constant.CarGiftConstants;
import com.ls.ner.billing.gift.dao.ICarGiftDao;
import com.ls.ner.billing.gift.service.ICarGiftService;
import com.ls.ner.billing.gift.util.BusinessException;
import com.ls.ner.def.api.account.service.IDefrayGiftRpcService;
import com.ls.ner.util.AssertUtil;
import com.ls.ner.util.CacheTools;
import com.ls.ner.util.ExcelUtil.ImportExcelUtils;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.ls.ner.util.sign.ThreeDES;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.CollectionUtils;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.map.HashedMap;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.opensaml.xmlsec.signature.P;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description CarGiftServiceImpl
 * @create 2020-04-29 18:05
 */
@Service(target = { ServiceType.LOCAL }, value = "carGiftService")
public class CarGiftServiceImpl implements ICarGiftService {

    private static final com.ls.ner.base.log.Logger LOGGER = LoggerFactory.getLogger(CarGiftServiceImpl.class);

    private static final String ThreeDesKey = "+7+hkq4l97VMgGHTufKDEHzfH8FzQ0aw";

    @Autowired
    private ICarGiftDao carGiftDao;

    private RedisCluster cacheService = RedisCluster.getInstance();

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="defrayGiftRpcService")
    private IDefrayGiftRpcService defrayGiftRpcService;

    @Override
    public List<CarGiftBo> listCarGift(CarGiftCondition carGiftCondition) {
        return carGiftDao.listCarGift(carGiftCondition);
    }

    @Override
    public int countCarGift(CarGiftCondition carGiftCondition) {
        return carGiftDao.countCarGift(carGiftCondition);
    }

    /**
     * @param carGiftBo
     * @description 更新购车信息
     * <AUTHOR>
     * @create 2020-05-04 13:51:54
     */
    @Override
    public int updateCarGift(CarGiftBo carGiftBo) {
        return carGiftDao.updateCarGift(carGiftBo);
    }

    /**
     * @param request
     * @description 导入购车信息
     * <AUTHOR>
     * @create 2020-05-04 13:52:10
     */
    @Override
    public Map saveImportData(MultipartHttpServletRequest request) throws Exception{
        Map resultMap = new HashMap();
        StringBuilder stringBuilder = new StringBuilder();
        Iterator<String> iterator = request.getFileNames();
        while (iterator.hasNext()) {
            MultipartFile file = request.getFile(iterator.next());
            if (file != null) {
                HSSFWorkbook hssfWorkbook = new HSSFWorkbook(file.getInputStream());
                HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(0);
                List<CarGiftBo> carGiftBoList =new ArrayList<>();
                for (int rowNum = 2; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                    CarGiftBo carGiftBo =new CarGiftBo();
                    HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                    /** 车牌号 **/
                    if (StringUtil.isNotEmpty(hssfRow.getCell(0))) {
                        String licenseNo = ImportExcelUtils.getValue(hssfRow.getCell(0));
                        carGiftBo.setLicenseNo(licenseNo);
                    }else {
                        throw new RuntimeException("第"+(rowNum+1)+"行车牌号为空");
                    }
                    /** 车架号 **/
                    if (StringUtil.isNotEmpty(hssfRow.getCell(1))) {
                        String vin = ImportExcelUtils.getValue(hssfRow.getCell(1));
                        carGiftBo.setVin(vin);
                    }else {
                        throw new RuntimeException("第"+(rowNum+1)+"行车架号为空");
                    }
                    /** 身份证号 **/
                    if (StringUtil.isNotEmpty(hssfRow.getCell(2))) {
                        String certNo = ImportExcelUtils.getValue(hssfRow.getCell(2));
                        carGiftBo.setCertNo(certNo);
                    }else {
                        throw new RuntimeException("第"+(rowNum+1)+"行身份证号为空");
                    }
                    /** 购买人支付宝账号姓名 **/
                    if (StringUtil.isNotEmpty(hssfRow.getCell(3))) {
                        String buyerName = ImportExcelUtils.getValue(hssfRow.getCell(3));
                        carGiftBo.setBuyerName(buyerName);
                    }else {
                        throw new RuntimeException("第"+(rowNum+1)+"行姓名为空");
                    }

                    List<CarGiftBo> lists = carGiftDao.listCarGiftByCondition(carGiftBo);
                    if(CollectionUtils.isNotEmpty(lists)){
                        stringBuilder.append("第").append(rowNum+1).append("行数据已存在;");
                        continue;
                    }
                    carGiftBo.setGiftStatus(CarGiftConstants.carGiftGetStatus.NOT_APPLIED);
                    carGiftBoList.add(carGiftBo);
                }
                if(CollectionUtils.isNotEmpty(carGiftBoList)){
                    carGiftDao.insertImportData(carGiftBoList);
                }
            }
        }
        resultMap.put("msg",StringUtil.isBlank(stringBuilder.toString()) ? "保存成功" : stringBuilder.toString());
        return resultMap;
    }

    /**
     * @param carGiftBo
     * @description 审核通过直充
     * <AUTHOR>
     * @create 2020-05-04 13:52:10
     */
    @Override
    public Map<String,Object> doCharge(CarGiftBo carGiftBo) throws Exception {
        Map<String,Object> resultMap = MapUtils.createSucResult();
        // 调用rpc接口充值馈赠金
        Map<String, Object> chargeMap = new HashMap<>();
        chargeMap.put("mobile",carGiftBo.getMobile());
        chargeMap.put("payAmount",carGiftBo.getGiftAmt());
        try {
            defrayGiftRpcService.doCharge(chargeMap);
        }catch (RuntimeException e){
            resultMap.put("ret",PublicConstants.returnCode.RET_400);
            resultMap.put("msg",e.getMessage());
            return resultMap;
        }
        // 更新购车送券记录
        CarGiftBo bo = new CarGiftBo();
        bo.setGiftId(carGiftBo.getGiftId());
        bo.setGiftStatus(CarGiftConstants.carGiftGetStatus.WRITTEN_OFF);
        carGiftDao.updateCarGift(bo);
        return resultMap;
    }

    @Override
    public Map getUserInfo(Map inMap) throws Exception {

        AssertUtil.notEmptyForString(inMap.get("code"),"code不能为空");
        String code = String.valueOf(inMap.get("code"));
        String userCode = code;

        Map resultMap = new HashMap<>();
        String mobile;
        String certNo;

        String ndSignUrl = sysParamService.getSysParamsValues("ndSignUrl");
        String ndClientId = sysParamService.getSysParamsValues("ndClientId");
        String ndSecretKey = sysParamService.getSysParamsValues("ndSecretKey");
        String ndRedirectUri1 = sysParamService.getSysParamsValues("ndRedirectUri1");


        String getAccessTokenUrl = ndSignUrl+"/auth/token";
        String getUserInfoUrl = ndSignUrl+"/open/info/getUserInfo";

        String refreshToken = null;
        String token = null;
        String tokenStr = StringUtil.nullForString(cacheService.get("ndCertInfo_"+code));

        String getType = null;
        if (tokenStr != null) {
            code = tokenStr.split(":")[1];
        }

        UserAuth auth = new UserAuth(ndClientId, ndSecretKey, getAccessTokenUrl, getUserInfoUrl, ndRedirectUri1);
        //
        Map resultInfo = new HashedMap();
        if (tokenStr == null){
            LOGGER.debug("第一次进入，通过tcode获取用户信息");
            resultInfo = getCertUserInfoByToken(auth,getAuthInfo(code,"authorizationCode",null));
            if ("0".equals(resultInfo.get("result"))){
                throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.CODE_ERROR, "code已失效");
            }
        }else{
            //新获取用户信息
            token = tokenStr.split(":")[0];
            refreshToken = tokenStr.split(":")[1];

            LOGGER.debug("重新获取用户信息token=====:"+token+"refreshToekn===:"+refreshToken);

            resultInfo = getCertUserInfoByToken(auth,token);

//            resultInfo.put("result","0"); //先写死测试

            if ("0".equals(resultInfo.get("result"))){
                //用户信息获取失败，用refreshToken重新获取token请求用户信息
                resultInfo = getCertUserInfoByToken(auth,getAuthInfo(code,"refreshToken",refreshToken));
                if ("0".equals(resultInfo.get("result"))){
                    throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.CODE_ERROR, "code已失效");
                }
            }
        }

        certNo = StringUtil.nullForString(resultInfo.get("certNo"));
        mobile = StringUtil.nullForString(resultInfo.get("mobile"));

        Map userInfoMap = new HashedMap();
        userInfoMap.put("certNo",certNo);
        userInfoMap.put("mobile",mobile);
        userInfoMap.put("code",userCode);

        String userStrResult = ThreeDES.threeDesEncrypt(JsonUtil.obj2Json(userInfoMap),ThreeDesKey);
        resultMap.put("userStr",userStrResult);

        CarGiftCondition carGiftCondition = new CarGiftCondition();
        carGiftCondition.setCertNo(certNo);
        List<CarGiftBo> carGiftBos = carGiftDao.listCarGift(carGiftCondition);
        if (!carGiftBos.isEmpty()){
            CarGiftBo carGiftBo = carGiftBos.get(0);
            if (CarGiftConstants.carGiftGetStatus.PASS.equals(carGiftBo.getGiftStatus())){
                resultMap.put("ret",200);
                resultMap.put("applyStatus","1");
                resultMap.put("applyMsg","您已申请，请登录“电动宁德”app注册账号，等待1-5个工作日，请注意查收！");
                return resultMap;
            }
            if (CarGiftConstants.carGiftGetStatus.ISSUED.equals(carGiftBo.getGiftStatus())){
                resultMap.put("ret",200);
                resultMap.put("applyStatus","1");
                resultMap.put("applyMsg","您已申请，已将抵扣券发放至您的支付宝卡包中，请打开电动宁德APP进行兑换！");
                return resultMap;
            }
            if (CarGiftConstants.carGiftGetStatus.ISSUANCE_FAILED.equals(carGiftBo.getGiftStatus())){
                resultMap.put("ret",200);
                resultMap.put("applyStatus","1");
                resultMap.put("applyMsg","您提交的手机号不正确，请重新申请！");
                return resultMap;
            }
        }

        resultMap.put("ret",200);
        resultMap.put("applyStatus","0");
        return resultMap;
    }

    /***
     * @param code
     * @description
     * <AUTHOR>
     * @create 2020-05-05 00:40:56
     */
    private String getAuthInfo(String code,String getType,String refreshToken) throws Exception {

        LOGGER.debug("获取token入参===code:"+code+"======getType:"+getType+"======refreshToekn:"+refreshToken);

        String ndSignUrl = sysParamService.getSysParamsValues("ndSignUrl");
        String ndClientId = sysParamService.getSysParamsValues("ndClientId");
        String ndSecretKey = sysParamService.getSysParamsValues("ndSecretKey");
        String ndRedirectUri1 = sysParamService.getSysParamsValues("ndRedirectUri1");
        String getAccessTokenUrl = ndSignUrl+"/auth/token";
        String getUserInfoUrl = ndSignUrl+"/open/info/getUserInfo";


        String accessTokenStr = "{}";

        GetAccessTokenRequest input = new GetAccessTokenRequest();
        input.setCode(code);
        input.setClientId(ndClientId);
        input.setRedirectUri(ndRedirectUri1);
        input.setGrantType(getType);


        if ("refreshToken".equals(getType)){
            //通过refreshToken获取token
            if (refreshToken != null){
                input.setCode(refreshToken);
            }
        }

        AccessTokenResult tokenInfo = UserOAuthServiceImpl.getAccessToken(input, ndSecretKey, getAccessTokenUrl);
        accessTokenStr = JSONObject.toJSONString(tokenInfo);
        LOGGER.debug("获取token出参:{}",IJsonUtil.obj2Json(accessTokenStr));
        AccessTokenResult accessToken = JSONObject.parseObject(accessTokenStr, AccessTokenResult.class);

        //存缓存操作
        cacheService.setex("ndCertInfo_"+code,accessToken.getAccessToken()+":"+accessToken.getRefreshToken(),7200);
        LOGGER.debug("====存缓存操作:"+code+"=====accessToken:"+accessToken.getAccessToken()+"====refreshToken:"+accessToken.getRefreshToken());


        return accessToken.getAccessToken();
    }


    /**
     * @param auth
     * @param accessToken
     * @description
     * <AUTHOR>
     * @create 2020-05-05 01:40:32
     */
    private Map getCertUserInfoByToken(UserAuth auth,String accessToken){
        Map info = new HashedMap();

        LOGGER.debug("获取用户信息accessToken:"+accessToken);

        String userInfo = auth.getUserInfo(accessToken);
        CertUserInfoResult certUserInfoResult = JSONObject.parseObject(userInfo,CertUserInfoResult.class);

        LOGGER.debug("=======certUserInfoResult:{}",IJsonUtil.obj2Json(certUserInfoResult));

        String certNo = certUserInfoResult.getCertNo();
        String mobile = certUserInfoResult.getMobile();

        if (StringUtil.isBlank(certNo) || StringUtil.isBlank(mobile)){
            info.put("result","0");
        }else{
            info.put("result","1");
            info.put("mobile",mobile);
            info.put("certNo",certNo);
        }
        return info;

    }


    @Override
    public Map submitApply(Map inMap) throws Exception {

        Map checkMap = userInfo(inMap);
        if ("400".equals(StringUtil.nullForString(checkMap.get("ret")))){
            return checkMap;
        }

        Map resultMap = new HashedMap();

        String certNo = StringUtil.nullForString(checkMap.get("certNo"));

        //身份证号，车牌号，车架号进行唯一确定
        CarGiftCondition carGiftCondition = new CarGiftCondition();
        carGiftCondition.setCertNo(certNo);
        carGiftCondition.setLicenseNo(StringUtil.nullForString(inMap.get("licenseNo")));
        carGiftCondition.setVin(StringUtil.nullForString(inMap.get("vin")));
        List<CarGiftBo> carGiftBos = carGiftDao.listCarGift(carGiftCondition);
        if (carGiftBos.isEmpty() || carGiftBos.size() != 1){
            resultMap.put("ret",400);
            resultMap.put("msg","您输入的信息不可领取");
            return resultMap;
        }
        CarGiftBo carGiftBo = carGiftBos.get(0);

        LOGGER.debug("====carGiftBo:{}",IJsonUtil.obj2Json(carGiftBo));

        if (CarGiftConstants.carGiftGetStatus.PASS.equals(carGiftBo.getGiftStatus())){
            resultMap.put("ret",400);
            resultMap.put("msg","您已申领，请注意查收");
            return resultMap;
        }
        if (CarGiftConstants.carGiftGetStatus.ISSUED.equals(carGiftBo.getGiftStatus())){
            resultMap.put("ret",400);
            resultMap.put("msg","支付宝抵扣券已发放，请打开电动宁德APP进行兑换");
            return resultMap;
        }
        if (CarGiftConstants.carGiftGetStatus.WRITTEN_OFF.equals(carGiftBo.getGiftStatus())){
            resultMap.put("ret",400);
            resultMap.put("msg","您已领取过，不可再次领取");
            return resultMap;
        }

        if (CarGiftConstants.carGiftGetStatus.ISSUANCE_FAILED.equals(carGiftBo.getGiftStatus())
                || CarGiftConstants.carGiftGetStatus.NOT_APPLIED.equals(carGiftBo.getGiftStatus())){

            inMap.put("giftId",carGiftBo.getGiftId());
            inMap.putAll(checkMap);
            carGiftDao.updateCarGift(initCarGiftBo(inMap));
            resultMap.put("ret",200);
            resultMap.put("msg","申请成功");
            return resultMap;
        }else{
            resultMap.put("ret",400);
            resultMap.put("msg","申请信息错误");
            return resultMap;
        }



    }

    @Override
    public Map queryRecentGiftInfo(Map inMap) {
        Map giftInfoMap = carGiftDao.queryRecentGiftInfo(inMap);
        Map resultMap = new HashedMap();
        resultMap.put("ret",200);
        resultMap.put("msg","成功");
        if (giftInfoMap != null && StringUtil.isNotBlank(giftInfoMap.get("giftAmt"))){
            resultMap.put("hasGift",true);
            resultMap.put("giftAmt",giftInfoMap.get("giftAmt"));
        }else{
            resultMap.put("hasGift",false);
        }

        return resultMap;
    }

    private CarGiftBo initCarGiftBo(Map inMap){
        CarGiftBo bo = new CarGiftBo();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = dateFormat.format(new Date());
        bo.setMobile(StringUtil.nullForString(inMap.get("mobile")));
        bo.setAlipayNo(StringUtil.nullForString(inMap.get("alipayNo")));
        bo.setBuyAddr(StringUtil.nullForString(inMap.get("buyAddr")));
        bo.setApplyTime(now);
        bo.setAuditTime(now);
        bo.setGiftAmt("4000");
        bo.setGiftStatus(CarGiftConstants.carGiftGetStatus.PASS);
        bo.setCertNo(StringUtil.nullForString(inMap.get("certNo")));
        bo.setVin(StringUtil.nullForString(inMap.get("vin")));
        bo.setLicenseNo(StringUtil.nullForString(inMap.get("licenseNo")));
        bo.setGiftId(StringUtil.nullForString(inMap.get("giftId")));
        return bo;

    }

    private Map userInfo(Map inMap) throws Exception {

        LOGGER.debug("提交查询用户参数：inMap{}",IJsonUtil.obj2Json(inMap));

        Map resultMap = new HashedMap();

        AssertUtil.notEmptyForString(inMap.get("code"),"code不能为空");
        String code = StringUtil.nullForString(inMap.get("code"));
        //防止被代码篡改
        String userCode = code;

        String refreshToken = null;
        String tokenStr = StringUtil.nullForString(cacheService.get("ndCertInfo_"+code));
        if (tokenStr == null) {
            LOGGER.debug("验证信息失效请重新提交");
            resultMap.put("ret",400);
            resultMap.put("msg","验证信息失效请重新提交");
            return resultMap;
        }


        String ndSignUrl = sysParamService.getSysParamsValues("ndSignUrl");
        String ndClientId = sysParamService.getSysParamsValues("ndClientId");
        String ndSecretKey = sysParamService.getSysParamsValues("ndSecretKey");
        String ndRedirectUri1 = sysParamService.getSysParamsValues("ndRedirectUri1");

        String getAccessTokenUrl = ndSignUrl+"/auth/token";
        String getUserInfoUrl = ndSignUrl+"/open/info/getUserInfo";


        String accessToken = "{}";

        UserAuth auth = new UserAuth(ndClientId, ndSecretKey, getAccessTokenUrl, getUserInfoUrl, ndRedirectUri1);

        GetAccessTokenRequest input = new GetAccessTokenRequest();
        input.setClientId(ndClientId);
        input.setGrantType("refreshToken");
        input.setRedirectUri(ndRedirectUri1);
        input.setCode(refreshToken);

        String token = tokenStr.split(":")[0];
        refreshToken = tokenStr.split(":")[1];
        Map resultInfo = getCertUserInfoByToken(auth,token);
        if ("0".equals(resultInfo.get("result"))){
            //用户信息获取失败，用refreshToken重新获取token请求用户信息
            resultInfo = getCertUserInfoByToken(auth,getAuthInfo(code,"refreshToken",refreshToken));
            if ("0".equals(resultInfo.get("result"))){
                throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.CODE_ERROR, "code已失效");
            }
        }

        String certNo = StringUtil.nullForString(resultInfo.get("certNo"));
        String mobile = StringUtil.nullForString(resultInfo.get("mobile"));

        resultMap.put("certNo",certNo);
        resultMap.put("mobile",mobile);
        resultMap.put("code",userCode);


        return resultMap;
    }


}
