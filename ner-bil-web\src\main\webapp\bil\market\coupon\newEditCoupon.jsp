<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑优惠券" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>

<ls:body>
		<ls:form name="couponForm" id="couponForm" enctype="multipart/form-data">
			<ls:text name="cpnId" property="cpnId" visible="false"></ls:text>
			<ls:text name="handleType" property="handleType" visible="false"></ls:text>
			<ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
			<ls:text name="prodId" property="prodId"  visible="false"></ls:text>
			<ls:text name="filePath" property="filePath"  visible="false"></ls:text>
			<ls:text name="attachId" property="attachId"  visible="false"></ls:text>
			<ls:text name="effectDate" property="effectDate"  visible="false"></ls:text>
			<ls:text name="cityCodes" property="cityCodes" type="hidden"/>
			<ls:text name="stationIds" property="stationIds" type="hidden"/>
			<ls:text name="isDeleteImg" property="isDeleteImg" type="hidden"/>
			<ls:layout-use>
				<ls:layout-put into="container">
					<table align="center" class="tab_search">
						<colgroup>
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
						</colgroup>
						<tr>
							<td>
								<ls:label text="优惠券名称" ref="cpnName"/>
							</td>
							<td colspan="2">
								<ls:text name="cpnName" property="cpnName" required="true"></ls:text>
							</td>

							<td>
								<ls:label text="减免面额" ref="cpnAmt" name="lab_cpnAmt" id="lab_cpnAmt"/>
							</td>
							<td>
								<ls:text name="cpnAmt" id="cpnAmt"  property="cpnAmt" required="true" ></ls:text>
							</td>

							<td rowspan="4">
								<div id="couponImage" style="width:200px;height:120px;background:gray">
									<img src="" id="headImg"  width="200" height="120"></img>
								</div>
								<div>
									<table>
										<tr>
											<td><input id="fileData" accept="image/*" class="text" name="fileData" style="color:transparent;width: 100px;" type="file" onchange="previewImage('couponImage',this)"/></td>
											<td colspan="2"><span><input  type="button" value="立即删除" onclick="deleteImg()" class="text"></span></td>
											<td></td>
										</tr>
									</table>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<ls:label text="发行数量" ref="cpnNum"/>
							</td>
							<td colspan="2">
								<table>
									<tr>
										<td><ls:text name="cpnNum"  property="cpnNum" required="true"></ls:text></td>
										<td>张</td>
									</tr>
								</table>
							</td>

							<td>
								<ls:label text="每人限领" ref="limGetNum" />
							</td>
							<td>
								<table>
									<tr>
										<td><ls:text name="limGetNum"  property="limGetNum" required="true"></ls:text></td>
										<td>张</td>
									</tr>
								</table>
							</td>

						</tr>

						<tr>
							<td>
								<ls:label text="有效时间" ref="cpnTimeType"/>
							</td>
							<td colspan="4">
								<table>
									<tr>
										<td>
											<ls:checklist type="radio" name="cpnTimeType" property="cpnTimeType" onchanged="setDateType" required="true">
												<ls:checkitem value="1" text="日期范围"></ls:checkitem>
												<%--<ls:checkitem value="2" text="领用或发放后"></ls:checkitem>--%>
											</ls:checklist>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr >
							<td>&nbsp;</td>
							<td colspan="4">
								<table>
									<tr id="dateType01">
										<td>
											<ls:checklist type="checkbox" name="isEffect" property="isEffect" onchanged="checkIsEffect">
												<ls:checkitem value="1" text="立即生效"></ls:checkitem>
											</ls:checklist>
										</td>
										<td >
											<ls:date name="eftDate" property="eftDate" format="yyyy-MM-dd" required="true"></ls:date>
										</td>
										<td >
											<ls:label text="至" ref="invDate"/>
										</td>
										<td >
											<ls:date name="invDate"  property="invDate" format="yyyy-MM-dd" required="true"></ls:date>
										</td>
									</tr>
									<tr style="display:none" id="dateType02">
										<td style="width: 60%">
											<ls:text name="timeDuration"  property="timeDuration" required="true"></ls:text>
										</td>
										<td style="width: 20%">
											<ls:select name="timeUnit" required="true" property="timeUnit" noClear="true">
												<ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue" />
											</ls:select>
										</td>
										<td style="width: 20%">内有效</td>
									</tr>
								</table>
							</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>

						<tr >
							<td>
								<ls:label text="优惠券描述" />
							</td>
							<td colspan="4">
								<ls:text name="cpnMarks" property="cpnMarks" type="textarea"></ls:text>
							</td>
						</tr>
						<tr>
    <td><ls:label text="赠送积分：" ref="integralNum"/></td>
    <td colspan="3">
        <div style="display: flex; align-items: center; flex-wrap: nowrap;">
            <ls:text name="integralNum" property="integralNum" type="number" min="0" style="width: 33%; margin-right: 2px;"/>
            <span style="white-space: nowrap;">积分</span>
        </div>
        <div style="font-size: 12px; color: #888; margin-top: 2px;">（填0或留空为不赠送）</div>
    </td>
</tr>
                    </table>
                    <ls:title text="优惠条件" expand="false"></ls:title>
                    <table class="tab_search">
						<colgroup>
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
						</colgroup>
						<tr>
							<td width="10%">
								<ls:label text="条件类型" ref="dctCondFlag"/>
							</td>
							<td width="20%">
								<ls:checklist type="radio" name="dctCondFlag" property="dctCondFlag" onchanged="setDisplayDctCond">
									<ls:checkitem value="1" text="无条件"></ls:checkitem>
									<ls:checkitem value="2" text="条件"></ls:checkitem>
								</ls:checklist>
							</td>
							<td width="10%"></td>
							<td width="20%"></td>
							<td width="10%"></td>
							<td width="20%"></td>
						</tr>
						<tr id="dctCondVal2">
							<td width="10%">
								<ls:label text="满减金额" ref="dctCondValue"/>
							</td>
							<td width="20%">
								<ls:text  name="fullReductionAmt" property="dctCondValue" required="true" hint="请输入满减金额的数值"></ls:text>
							</td>
							<td width="20%">
								<ls:label text="最大折扣金额" ref="maximumDiscountAmt"/>
							</td>
							<td width="20%">
								<ls:text  name="maximumDiscountAmt" property="maximumDiscountAmt" required="true" hint="请输入最大折扣金额"></ls:text>
							</td>
							<td width="10%"></td>
							<td width="20%"></td>
						</tr>
                    </table>
                    <table class="tab_search">
						<colgroup>
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
						</colgroup>
                        <tr>
                            <td width="10%">
                                <ls:label text="时间段限制" ref="timeCondFlag"/>
                            </td>
                            <td width="20%">
                                <ls:checklist type="radio" name="timeCondFlag" property="timeCondFlag"  onchanged="setDisplayTimeCond">
                                    <ls:checkitem value="1" text="全部"></ls:checkitem>
                                    <ls:checkitem value="2" text="其他时段"></ls:checkitem>
                                </ls:checklist>
                            </td>
							<td width="1%"></td>
							<td width="7%"></td>
							<td width="35%"></td>
							<td width="17%"></td>
                        </tr>
					</table>
					<table class="tab_search yhsjd">
                        <tr>
							<td width="10%"></td>
                            <td width="10%"><ls:label text="优惠时间段"/></td>
							<td width="7%">
								<ls:text name="timeCondBeg" property="timeCondBeg" type="hidden"/>
								<input type="time" id="begTime" value="00:00" name="begTime" placeholder=""
									   onChange="timeBeginChange(this.value)"/>
                            </td>
                            <td width="1%" style="text-align: center;">
                                至
                            </td>
							<td width="7%">
								<ls:text name="timeCondEnd" property="timeCondEnd" type="hidden"/>
								<input type="time" id="endTime" name="endTime" value="23:59" placeholder=""
									   onChange="timeEndChange(this.value)"/>
                            </td>
							<td width="35%"></td>
							<td width="30%"></td>
                        </tr>
                    </table>
                    <table class="tab_search">
						<colgroup>
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
							<col width="20%" />
							<col width="15%" />
							<col width="15%" />
						</colgroup>
						<tr>
							<td width="10%"><ls:label text="适用运营商" ref="buildType"/></td>
							<td width="20%">
								<ls:checklist name="buildType" type="radio" property="buildType" required="true" onchanged="getBuildType" value="0">
									<ls:checkitem text="全部" value="0"/>
									<ls:checkitem text="部分" value="1"/>
								</ls:checklist>
							</td>
							<td width="1%"></td>
							<td width="35%"></td>
							<td width="17%"></td>
						</tr>
						<tr id="buildListTr" style="display: none" >
							<td width="10%"><ls:label text=""/></td>
							<td width="40%">
								<ls:select name="buildId" property="buildId" required="true" onchanged="getBuild">
									<ls:options property="buildList" scope="request" text="operName" value="operNo"/>
								</ls:select>
							</td>
							<td width="1%"></td>
							<td width="35%"></td>
							<td width="17%"></td>
						</tr>
						<tr>
							<td><ls:label text="适用城市" ref="cityType"/></td>
							<td >
								<ls:checklist name="cityType"  type="radio" property="cityType" required="true" value="0" onchanged="getCity">
									<ls:checkitem text="全部" value="0"/>
									<ls:checkitem text="部分" value="1"/>
								</ls:checklist>
							</td>
						</tr>
						<tr id="cityListTr" style="display: none">
							<td><ls:label text=""/></td>
							<td  width="40%">
							<span id="cityListSpan">
							</span>
							</td>
						</tr>
						<tr>
							<td><ls:label text="适用站点" ref="stationType"/></td>
							<td>
								<ls:checklist name="stationType"  property="stationType" type="radio" required="true" value="0"
											  onchanged="doSearchStation">
									<ls:checkitem text="全部" value="0"/>
									<ls:checkitem text="部分" value="1"/>
								</ls:checklist>
							</td>
						</tr>
						<tr id="stationListTr" style="display: none">
							<td><ls:label text=""/></td>
							<td width="40%">
								<ls:grid height="140px" width="250px" url="" name="station_grid" showCheckBox="true"
										 primaryKey="stationId"
										 allowSorting="true">
									<ls:column caption="站点Id" name="stationId" hidden="true"></ls:column>
									<ls:column caption="站点类型" name="stationTypeName"></ls:column>
									<ls:column caption="站点名称" name="stationName" width="200%"></ls:column>
								</ls:grid>
							</td>
						</tr>
					</table>
				</ls:layout-put>
				<ls:layout-put into="footer">
					<div class="pull-right">
						<ls:button text="保存" onclick="save"></ls:button>
						<ls:button text="发布" onclick="public" ></ls:button>
						<ls:button text="取消" onclick="cancel"></ls:button>
					</div>
				</ls:layout-put>
			</ls:layout-use>
		</ls:form>
		<ls:script>
		var headImg = document.getElementById('headImg');
		var pubPath = '${pubPath }';
		var dctTypeItems = new Array();
		var dctTypeItemsTemp = new Array();
		window.onload = function(){
			window.mAjaxFuncFlag=new Object();

			//活动
			if (!LS.isEmpty(buildId.getValue()) && buildId.getValue() != '1') { //运营商为部分
				buildType.setValue("1");
				$('#buildListTr').show();
			}else{
				buildId.setValue("")
			}
			if(!LS.isEmpty(cityCodes.getValue()) && cityCodes.getValue() != '1'){  //城市为部分
				cityType.setValue("1");
				getCity();
			}else{
				cityCodes.setValue("");
			}
			if(!LS.isEmpty(stationIds.getValue()) && stationIds.getValue() != '1'){  //站点为部分
				stationType.setValue("1");
				doSearchStation();

			}else{
				stationIds.setValue("");
			}


			setDisplayDctCond();
			setDateType();
			setDctCondType();
			var _attachId = attachId.getValue();

			var imageFlag = '0';
			if(LS.isEmpty(cpnId.getValue()) || LS.isEmpty(_attachId)){
				headImg.src = pubPath + '/pub/image/js/add.png';
			}else{
				imageFlag = 1;
				headImg.src = pubPath + '/attach/'+_attachId;
			}

			var image = new Image();
			image.src = headImg.src;
			image.onload = function(){

				if(imageFlag == 0){
					return;
				}
				var rect = clacImgZoomParam(200, 120, image.width,
				image.height);
				headImg.width = rect.width;
				headImg.height = rect.height;
				headImg.style.marginLeft = rect.left + 'px';
				headImg.style.marginTop = rect.top + 'px'
			}


			checkIsEffect();
			//重置


		}


		window.cityClick = cityClick;
		function cityClick(){
			station_grid.removeAllItems();
			var cityTypeValue = cityType.getValue();
			var stationIdValue = stationType.getValue();
			var obj = document.getElementsByName("cityIdBox");
			var cityCodesValue = "";
			var buildIdValue = buildId.getValue();

			if (cityTypeValue == 0) {
				for (k in obj) {
					cityCodesValue += obj[k].value + ",";
				}
				if (stationIdValue == 1) {
					if (cityCodesValue != "" && cityCodesValue != null) {
						$("#stationListTr").show();
						station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue});
					} else {
						LS.message("info", "请选择城市！");
						return;
					}
				} else {
					$("#stationListTr").hide();
				}
			} else {
				if (stationIdValue == 1) {
					for (k in obj) {
						if (obj[k].checked)
							cityCodesValue += obj[k].value + ",";
						}
						if (cityCodesValue != "" && cityCodesValue != null) {
							$("#stationListTr").show();
							station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue});
						} else {
							LS.message("info", "请选择城市！");
							return;
						}
				} else {
					$("#stationListTr").hide();
				}
			}
		}

		function setDateType(){
			if(cpnTimeType.getValue() == "1"){
				$("#dateType01").show();
				$("#dateType02").hide();
			}else{
				$("#dateType01").hide();
				$("#dateType02").show();
			}
		}
		function isNumber(){
			var _cpnAmt = cpnAmt.getValue();
			var _cpnNum = cpnNum.getValue();
			var _limGetNum = limGetNum.getValue();

			if(_limGetNum!=null&&_limGetNum!=""&&isNaN(_limGetNum)){
				LS.message("info","每人限领非数字！");
				return false;
			}
			if(parseFloat(_limGetNum) <= 0){
				LS.message("info","每人限领应大于0");
				return false;
			}
			if(_cpnAmt!=null&&_cpnAmt!=""&&isNaN(_cpnAmt)){
				LS.message("info","减免面额非数字");
				return false;
			}
			if(parseFloat(_cpnAmt) <= 0){
				LS.message("info","减免面额应大于0");
				return false;
			}
			if(_cpnNum!=null&&_cpnNum!=""&&isNaN(_cpnNum)){
				LS.message("info","发行数量非数字");
				return false;
			}

			var _dctCondValue = dctCondValue.getValue();
			if(dctCondFlag.value == '2' && _dctCondValue==null){
				LS.message("info","满减金额不能为空！");
				return false;
			}
			if(dctCondFlag.value == '2' && _dctCondValue!=null && _dctCondValue!="" && isNaN(_dctCondValue)){
				LS.message("info","满减金额非数字！");
				return false;
			}
			if(dctCondFlag.value == '2' && _dctCondValue<=0){
				LS.message("info","满减金额应大于0！");
				return false;
			}
			if(dctCondFlag.value == '2' && _dctCondValue < _cpnAmt){
				LS.message("info","满减金额不能小于减免金额！");
				return false;
			}
			var _maximumDiscountAmt=maximumDiscountAmt.getValue();
			if(dctCondFlag.value == '2' && _maximumDiscountAmt!=null && _maximumDiscountAmt!="" && isNaN(_maximumDiscountAmt)){
			LS.message("info","最大折扣金额非数字！");
			return false;
			}
			if(dctCondFlag.value == '2' && _maximumDiscountAmt<=0){
			LS.message("info","最大折扣金额应大于0！");
			return false;
			}
			if(!LS.isEmpty(_cpnNum)&&!LS.isEmpty(_limGetNum)&&parseFloat(_limGetNum)>parseFloat(_cpnNum)){
				LS.message("info","每人限领数量不能大于发行数量！");
				return false;
			}
			return true;
		}
		//使用条件类型改变事件
		function setDisplayDctCond(){
			var _dctCondFlag = dctCondFlag.getValue();
			if("2"==_dctCondFlag){
				$("#dctCondVal2").show();
			}else if("1"==_dctCondFlag){
				$("#dctCondVal2").hide();
			}else{
				$("#dctCondVal2").hide();
			}
		}

		//保存
		function save(status){
			if(!couponForm.valid()){
			return;
			}
			var r = /^\+?[0-9][0-9]*$/;
			//有效时间判断
			if(cpnTimeType.getValue() == "2"){
				if(timeDuration.getValue() == '0'){
					LS.message("info","有效时间不能为0");
					return;
				}

				if(!r.test(timeDuration.getValue())){
					LS.message("info","有效时间需为整数");
					return;
				}
			}

			var _dctCondType = dctCondType.getValue();


			if(timeCondBeg.getValue() == ""){
				LS.message("info","优惠开始时段不能为空");
				return;
			}
			if(timeCondEnd.getValue() == ""){
				LS.message("info","优惠结束时段不能为空");
				return;
			}
			if(timeCondBeg.getValue() == timeCondEnd.getValue()){
				LS.message("info","优惠开始时间不能等于结束时间");
				return;
			}
			if(parseInt(timeCondEnd.getValue().replace(":","")) < parseInt(timeCondBeg.getValue().replace(":",""))){
				LS.message("info","优惠结束时段不能小于开始时段");
				return;
			}


			if(!r.test(cpnAmt.getValue())){
				LS.message("info","减免面额需要为整数");
				return;
			}

			if(!r.test(cpnNum.getValue())){
				LS.message("info","发行数量需要为整数");
				return;
			}

			if(!r.test(limGetNum.getValue())){
				LS.message("info","每人限领需要为整数");
				return;
			}

			if(!isNumber()){
				return;
			}
			var _eftDate = eftDate.getValue();
			var _invDate = invDate.getValue();
			if(!LS.isEmpty(_eftDate)&&!LS.isEmpty(_invDate)&&_eftDate>_invDate){
				LS.message("info","生效开始时间不能大于失效结束时间");
				return;
			}


			if(cpnTimeType.getValue() == '1'){  //有效时间判断
				var current = (new Date()).Format("yyyy-MM-dd");  //当前时间
				if(current > _invDate){
					LS.message("error","失效时间小于当前时间");
					return;
				}
			}




			validateFile();
			// 优惠券状态为：草稿
			cpnStatus.setValue("0");
			var _msg = "保存成功";
			if('1' == status){
				cpnStatus.setValue("1");
				_msg = "发布成功";
			}

			var buildTypeValue = buildType.getValue();
			var buildIdValue = buildId.getValue();
			var cityTypeValue = cityType.getValue();
			var stationTypeValue = stationType.getValue();
			var stationIdsValue = "";
			var cityCodesValue = "";
			var stationIdsValue = "";


			<%--if(buildType.getValue() == '0' && stationType.getValue() == '1'){--%>
				<%--LS.message("info","适用全部运营商无需再选择站点");--%>
				<%--return;--%>
			<%--}--%>
			<%--if(cityType.getValue() == '0' && stationType.getValue() == '1'){--%>
				<%--LS.message("info", "适用全部城市无需再选择站点！")--%>
				<%--return;--%>
			<%--}--%>


			if (buildTypeValue == 0) {
				if (cityTypeValue != 0 || stationTypeValue != 0) {
					LS.message("info", "运营商选择全部时，城市和站点只能选择全部。");
					return;
				}
			} else {
				if (cityTypeValue == 1) {
					var obj = document.getElementsByName("cityIdBox");

					for (k in obj) {
						if (obj[k].checked)
							cityCodesValue += obj[k].value + ",";
					}
					if (cityCodesValue == "" || cityCodesValue == null) {
						LS.message("info", "城市选择部分时，要勾选城市");
						return;
					}
				}
				if (stationTypeValue == 1) {
					var item = station_grid.getCheckedItems();
					if (item == null || item.length == 0) {
						LS.message("info", "站点为部分时，请选择至少一条站点!");
						return;
					}
					var d = item.length;
					for (var i = 0; i < item.length; i++) {
						stationIdsValue += item[i].stationId + ",";
					}
				}
			}

			cityCodes.setValue(cityCodesValue.substring(0,cityCodesValue.lastIndexOf(',')));
			stationIds.setValue(stationIdsValue.substring(0,stationIdsValue.lastIndexOf(',')));

			couponForm.submit("~/billing/coupon/newSaveCoupon",function(e){
   		 		if(e){
	   		 		LS.message("info",_msg);
	   		 		LS.parent().query();
	   		 		LS.window.close();
	   		 	}else{
	   		 		LS.message("error","系统异常");
	   		 	}
		    });
		}

		//发布
		function public(){
			if(timeCondBeg.getValue() == ""){
				LS.message("info","优惠开始时段不能为空");
				return;
			}
			if(timeCondEnd.getValue() == ""){
				LS.message("info","优惠结束时段不能为空");
				return;
			}
			if(parseInt(timeCondEnd.getValue().replace(":","")) < parseInt(timeCondBeg.getValue().replace(":",""))){
				LS.message("info","优惠结束时段不能小于开始时段");
				return;
			}
			LS.confirm("发布后不能修改，是否继续发布？",function(data){
				if(data){
					save(1);
				}
			});

		}

		//取消
		function cancel(){
			LS.window.close();
		}
		function getOrgCodes(){
			var initValue=null;
	      	if(!LS.isEmpty(orgCodes.getValue())){
		        initValue=orgCodes.getValue().split(",");
		      }
		    js_util.selectOrgTree(true, null, true, initValue, false, setOrg);
		}

	    function setOrg(node){
	      if(node==null){
	        return;
	      }
	      var _orgCodes="";
	      var _orgCodeName="";
	      if(node.length==undefined){
	        _orgCodes=node.id;
	        _orgCodeName=node.text;
	      }else{
	        for(var i=0;i< node.length;i++){
	          if(node.length==i+1){
	            _orgCodes+=node[i].id;
	            _orgCodeName+=node[i].text;
	          }else{
	            _orgCodes+=node[i].id+',';
	            _orgCodeName+=node[i].text+',';
	          }
	        }
	      }
	      orgCodeName.setValue(_orgCodeName);
		  orgCodes.setValue(_orgCodes);
	    }

		function getOrgCode(){

		}

	// 图片的验证
	   function validateFile(){
	   	  if(typeof(fileData) != "undefined"){
		        if(fileData.value==null || fileData.value==''){
		          return;
		        }
		        if(!/\.(gif|jpg|jpeg|png|bmp)$/.test(document.getElementById("fileData").value.toLowerCase())){
		         	LS.message("warn","图片类型必须是gif,jpeg,jpg,png中的一种");
		        	return;
		      	}
		      	if(isIE()){
		      		 fileData.value=fileData.value;
		      	}else{
		      		fileData.files = fileData.files;
		      	}

	      }
	   }

	   //预览本地图片代码
window.previewImage = function(previewImageId,file) {
	isDeleteImg.setValue("0");
	var MAXWIDTH = 200;
	var MAXHEIGHT = 120;
	var imgIdTmp = 'imghead_'+previewImageId;
	var div = document.getElementById(previewImageId);
	//if (file.value!=null && file.value!='') {
	if (file.files || file.files[0]) {
		div.innerHTML = "<img id="+imgIdTmp+">";
		var img = document.getElementById(imgIdTmp);
		img.onload = function() {
			var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT,
					img.offsetWidth, img.offsetHeight);
			img.width = rect.width;
			img.height = rect.height;
			img.style.marginLeft = rect.left + 'px';
			img.style.marginTop = rect.top + 'px';
		}
		var reader = new FileReader();
		reader.onload = function(evt) {
			img.src = evt.target.result;
		}
		reader.readAsDataURL(file.files[0]);
	} else {
		var sFilter = 'filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale,src="';
		file.select();
		var src ;//document.selection.createRange().text;
		div.innerHTML = "<img id="+imgIdTmp+">";
		var img = document.getElementById(imgIdTmp);
		//img.filters.item('DXImageTransform.Microsoft.AlphaImageLoader').src = src;
		var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT, img.offsetWidth,
				img.offsetHeight);
		status = ('rect:' + rect.top + ',' + rect.left + ',' + rect.width
				+ ',' + rect.height);
		div.innerHTML = "<div id='divhead' style='width: "+rect.width+" px; height: "+rect.height+" px; margin-top: "+rect.top+" px; margin-left: "+rect.left+" px;"+sFilter+src+"\"'></div>";
	}
}
function clacImgZoomParam(maxWidth, maxHeight, width, height) {
	var param = {
		top : 0,
		left : 0,
		width : width,
		height : height
	};
	if (width > maxWidth || height > maxHeight) {
		rateWidth = width / maxWidth;
		rateHeight = height / maxHeight;

		if (rateWidth > rateHeight) {
			param.width = maxWidth;
			param.height = Math.round(height / rateWidth);
		} else {
			param.width = Math.round(width / rateHeight);
			param.height = maxHeight;
		}
	}
	param.left = Math.round((maxWidth - param.width) / 2);
	param.top = Math.round((maxHeight - param.height) / 2);
	return param;
}


			function isIE() { //ie?
     			if (navigator.appName.indexOf("Explorer") > -1)
       				return true;
     			else
       				return false;
			}

		function setDctCondType(){
			var _dctCondType = dctCondType.value;

		}

		function checkIsEffect(){
			var _isEffect = isEffect.getValue();
			if('1' === _isEffect){
				eftDate.setEnabled(false);
				eftDate.setValue();
			}else{
				eftDate.setEnabled(true);
				eftDate.setValue(effectDate.getValue());
			}
		}
			function changeCpnType(){
				var _cpnType = cpnType.getValue();
				if('02' == _cpnType){
					lab_cpnAmt.setText("折扣值");
				}else{
					lab_cpnAmt.setText("减免面额");
				}
				couponForm.addValid(cpnAmt,"required");
			}

            window.timeBeginChange = timeBeginChange;
            window.timeEndChange = timeEndChange;
            function timeBeginChange(Value) {
				timeCondBeg.setValue(Value);
            }
            function timeEndChange(Value) {
				timeCondEnd.setValue(Value);
            }
            setDisplayTimeCond();

            //使用条件类型改变事件
            function setDisplayTimeCond(){
            var _timeCondFlag = timeCondFlag.getValue();
                if("1"==_timeCondFlag){
					timeCondBeg.setValue('00:00');
					timeCondEnd.setValue('23:59');
                    $('#begTime').val(timeCondBeg.getValue());
                    $('#endTime').val(timeCondEnd.getValue());
					$("#begTime").attr("disabled","disabled");
					$("#endTime").attr("disabled","disabled");
					$('.yhsjd').hide();
                }else{
                    $('#begTime').val(timeCondBeg.getValue());
                    $('#endTime').val(timeCondEnd.getValue());
					$("#begTime").removeAttr("disabled");
					$("#endTime").removeAttr("disabled");
					$('.yhsjd').show();
                }
            }


			function getBuildType() {
				if (buildType.getValue() == 0) {
					$("#buildListTr").hide();
					$("#stationListTr").hide();
					$("#cityListTr").hide();
					$("#cityListSpan").empty();
					station_grid.removeAllItems();
					cityType.setValue("");
					buildType.setValue("");
					stationType.setValue("");
					cityType.setValue('0');
					buildType.setValue("0");
					stationType.setValue('0');

				}
				if (buildType.getValue() == 1) {
					$("#buildListTr").show();
				}
			}

			function getBuild() {
				cityCodes.setValue("");
				stationIds.setValue("");
				var i = cityType.getValue();
				cityType.setValue("");
				var j = cityType.getValue();
				stationType.setValue("");
				cityType.setValue("0");
				var z = cityType.getValue();
				stationType.setValue("0");
				$("#cityListTr").hide();
				$("#cityListSpan").empty();
				station_grid.removeAllItems();
				$("#stationListTr").hide();
			}

			function getCity() {

				if(cityType.getValue() == "0,1"){
					cityType.setValue(1);
				}
				station_grid.removeAllItems();
				stationType.setValue("0");

				var buildTypeValue = buildType.getValue();
				if(buildTypeValue == '0' && cityType.getValue() == '1'){
					LS.message("info", "适用全部运营商无需再选择城市！");
					return;
				}

				$("#stationListTr").hide();
				if (cityType.getValue() == 1) {
					var buildIdValue = buildId.getValue();
					if (buildIdValue == '' || buildIdValue == null ) {
						LS.message("info", "请选择运营商！");
						return;
					}

					var cityCodesValue = cityCodes.getValue();
					var cityArray = new Array(); //定义一数组
					if (!LS.isEmpty(cityCodesValue)) {
						cityArray = cityCodesValue.split(",");  //编辑页面获得值
					}



						//根据运营商获取城市
					LS.ajax("~/markertAct/getCityByBuild?buildNo=" + buildIdValue, null, function (e) {
						if (e.items.length == 0) {
							LS.message("info", "该运营商下没有站点和城市！");
							return;
						} else {
							$("#cityListSpan").empty();
							for (var i = 0; i < e.items.length; i++) {
								var checkNum = 0;
								for (var j = 0; j < cityArray.length; j++) {
									if (cityArray[j] == e.items[i].AREA_CODE) {  //遍历编辑页面传参
										checkNum++;
									}
								}
								if (checkNum > 0) {   //获得值
									$("#cityListSpan").append(
										'<input id="checkbox_' + i + '" type="checkbox" onclick="cityClick();" checked="checked" name="cityIdBox" value="' + e.items[i].AREA_CODE + '">' +
										'<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
								}else{
									$("#cityListSpan").append(
										'<input id="checkbox_' + i + '" type="checkbox" onclick="cityClick();" name="cityIdBox" value="' + e.items[i].AREA_CODE + '">' +
										'<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
								}
							}
							$("#cityListTr").show();
						}

					});

				} else {
					$("#cityListTr").hide();
				}
			}

			function doSearchStation() {

				if(stationType.getValue() == "0,1"){
					stationType.setValue(1);
				}

				station_grid.removeAllItems();
				var buildTypeValue = buildType.getValue();
				var stationIdValue = stationType.getValue();
				var cityValue = cityType.getValue();
				var station123 = stationIds.getValue();
				var buildIdValue = buildId.getValue();

				if(buildTypeValue == '0' && stationIdValue == '1'){
					LS.message("info","适用全部运营商无需再选择站点");
					return;
				}
				if(cityValue == '0' && stationIdValue == '1'){
					LS.message("info", "适用全部城市无需再选择站点！")
					return;
				}

				var obj = document.getElementsByName("cityIdBox");  //城市内容框所有站点

				var cityCodesValue = cityCodes.getValue();;
				if (cityValue == 0) {
					for (k in obj) {
						cityCodesValue += obj[k].value + ",";
					}
					if (stationIdValue == 1) {
						if (cityCodesValue != "" && cityCodesValue != null) {
							$("#stationListTr").show();
							station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue}, function () {
								if (!LS.isEmpty(stationIds.getValue())) {
									var stationIdsValue = stationIds.getValue();
									var stationArray = new Array();
									stationArray = stationIdsValue.split(",");
									var allItem = station_grid.getItems();
									for (var i = 0; i < allItem.length; i++) {
										for (var j = 0; j < stationArray.length; j++) {  //匹配
											if (allItem[i].stationId == stationArray[j]) {
												allItem[i].checked = true;
												station_grid.selectItem(allItem[i].stationId);
											}
										}
									}
								}
							});
						} else {
							LS.message("info", "请选择城市！");
							return;
						}
					} else {
						$("#stationListTr").hide();
					}

				} else {
					if (stationIdValue == 1) {
						for (k in obj) {
							if (obj[k].checked)  //匹配所有已经点击的城市按钮
								cityCodesValue += obj[k].value + ",";
							}
						cityCodesValue = judge(cityCodesValue);
						if (cityCodesValue != "" && cityCodesValue != null) {
							$("#stationListTr").show();
							station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue}, function () {
								if (!LS.isEmpty(stationIds.getValue())) {
									var stationIdsValue = stationIds.getValue();  //已选站点
									var stationArray = new Array();
									stationArray = stationIdsValue.split(",");
									var allItem = station_grid.getItems();
									for (var i = 0; i < allItem.length; i++) {
										for (var j = 0; j < stationArray.length; j++) {
											if (allItem[i].stationId == stationArray[j]) {
												allItem[i].checked = true;
												station_grid.selectItem(allItem[i].stationId);
											}
										}
									}
								}

							});

						} else {
							LS.message("info", "请选择城市！");
							return;
						}
					} else {
						$("#stationListTr").hide();
					}
				}
			}


			function judge(str){  //判断最后一位是否为逗号，如果不是逗号加上逗号
				if(str != null && str != ''){
					var c = str.substring(str.length-2,str.length-1);
					if(c != ','){
						return str+",";
					}
				}
				return str;
			}


			window.deleteImg = function () {
			var file = document.getElementById('fileData');
			file.value = ''; //虽然file的value值不能设为有内容的字符，但是可以设置为空字符
			//或者
			file.outerHTML = file.outerHTML; //重新初始化了file的html

			var _isDeleteImg = isDeleteImg.getValue();

			var img = headImg.src = pubPath + '/pub/image/js/add.png';
			file = '';
			var previewImageId = 'couponImage';
			var MAXWIDTH = 200;
			var MAXHEIGHT = 120;
			var imgIdTmp = 'imghead_'+previewImageId;
			var div = document.getElementById(previewImageId);

			var sFilter = 'filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale,src="';
			var src ;//document.selection.createRange().text;
			div.innerHTML = "<img id="+imgIdTmp+" src="+img+" style='width:200px;height:120px'>";
			var img = document.getElementById(imgIdTmp);
			//img.filters.item('DXImageTransform.Microsoft.AlphaImageLoader').src = src;
			var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT, img.offsetWidth,
			img.offsetHeight);
			status = ('rect:' + rect.top + ',' + rect.left + ',' + rect.width
			+ ',' + rect.height);

			isDeleteImg.setValue('1');
			}

		function integralNumChanged() {
    var checked = $("[name='integralNumFlag']").prop("checked");
    if (checked) {
        $("#integralNumSpan").hide();
        $("[name='integralNum']").val('');
    } else {
        $("#integralNumSpan").show();
    }
}
$(function(){
    // 页面初始化时根据后端数据设置checkbox和输入框状态
    integralNumChanged();
});


	</ls:script>

</ls:body>
</html>