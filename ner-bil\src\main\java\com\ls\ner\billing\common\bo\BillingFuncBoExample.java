package com.ls.ner.billing.common.bo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BillingFuncBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public BillingFuncBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNull() {
            addCriterion("BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNotNull() {
            addCriterion("BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBillingNoEqualTo(String value) {
            addCriterion("BILLING_NO =", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotEqualTo(String value) {
            addCriterion("BILLING_NO <>", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThan(String value) {
            addCriterion("BILLING_NO >", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThanOrEqualTo(String value) {
            addCriterion("BILLING_NO >=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThan(String value) {
            addCriterion("BILLING_NO <", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThanOrEqualTo(String value) {
            addCriterion("BILLING_NO <=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLike(String value) {
            addCriterion("BILLING_NO like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotLike(String value) {
            addCriterion("BILLING_NO not like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIn(List<String> values) {
            addCriterion("BILLING_NO in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotIn(List<String> values) {
            addCriterion("BILLING_NO not in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoBetween(String value1, String value2) {
            addCriterion("BILLING_NO between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotBetween(String value1, String value2) {
            addCriterion("BILLING_NO not between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNull() {
            addCriterion("DATA_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNotNull() {
            addCriterion("DATA_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE =", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <>", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThan(String value) {
            addCriterion("DATA_OPER_TYPE >", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE >=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThan(String value) {
            addCriterion("DATA_OPER_TYPE <", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLike(String value) {
            addCriterion("DATA_OPER_TYPE like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotLike(String value) {
            addCriterion("DATA_OPER_TYPE not like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE not in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE not between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_func
     *
     * @mbggenerated do_not_delete_during_merge Tue Mar 15 14:42:10 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}