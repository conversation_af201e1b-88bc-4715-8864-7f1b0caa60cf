<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券使用信息">
<style type="text/css">
.hiddenFlow{
overflow-x:hidden; 
}
</style>
</ls:head>
<ls:body>
	<ls:form name="useForm" cssClass="hiddenFlow">
		<ls:text name="cpnId" property="cpnId" visible="false"></ls:text>
		<ls:text name="alrdyGetNum" property="alrdyGetNum" visible="false"></ls:text>
		<ls:text name="putNum" property="putNum" visible="false"></ls:text>
		<ls:text name="cpnNum" property="cpnNum" visible="false"></ls:text>
		<ls:text name="useNum" property="useNum" visible="false"></ls:text>
		<ls:text name="unUseNum" property="unUseNum" visible="false"></ls:text>
		<ls:text name="totalChargeAmt" property="totalChargeAmt" visible="false"></ls:text>
		<ls:text name="totalServiceAmt" property="totalServiceAmt" visible="false"></ls:text>
		<ls:text name="totalCpnTBal" property="totalCpnTBal" visible="false"></ls:text>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:label name="getInfo" text="领取情况：合计0张，领取0张，发放0张，已使用0张，未使用0张"></ls:label></td>
			</tr>
			<tr>
				<td><ls:label name="actAmtInfo" text="优惠信息：订单总金额0元，服务费金额0元，优惠券优惠金额0元"></ls:label></td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="useGrid" caption="优惠券使用信息" showRowNumber="false"
                    height="265px" showCheckBox="false" singleSelect="true" allowScroll="true">
                    <ls:column caption="客户主键" name="custId" hidden="true"/>
                    <ls:column caption="客户编号" name="custNo"  align="center"/>
                    <ls:column caption="客戶名称" name="custName" align="center" />
                    <ls:column caption="手机号码" name="mobile" align="center" />
                    <ls:column caption="客户分类" name="custSortCode"  hidden="true"/>
                    <ls:column caption="客户分类" name="custSortCodeName" align="center"/>
                    <ls:column caption="来源" name="getSource" hidden="true" />
                    <ls:column caption="来源" name="getSourceName" align="center" />
                    <ls:column caption="获取方式" name="getWay"  hidden="true"/>
                    <ls:column caption="获取方式" name="getWayName" align="center"/>
                    <ls:column caption="获取渠道" name="getChannel" hidden="true"/>
                    <ls:column caption="获取渠道" name="getChannelName" align="center"/>
                    <ls:column caption="获取时间" name="getTime" align="center"/>
                    <ls:column caption="使用时间" name="useTime" align="center"/>
                    <ls:column caption="使用状态" name="useStatus" hidden="true"/>
                    <ls:column caption="使用状态" name="useStatusName" align="center"/>
                    <ls:column caption="订单编号" name="appNo" align="center"/>
                    <ls:column caption="订单金额" name="chargeAmt" align="center"/>
                    <ls:column caption="服务费" name="serviceAmt" align="center"/>
                    <ls:column caption="结算金额" name="settleTBal" align="center"/>
                    <ls:column caption="优惠券优惠金额" name="cpnTBal" align="center"/>
                    <ls:pager pageSize="15,20"></ls:pager>
                </ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
		init();
		function init(){
            var _alrdyGetNum = alrdyGetNum.getValue();
            var _useNum = useNum.getValue();
            var _unUseNum = unUseNum.getValue();
            var _cpnNum = cpnNum.getValue();
            var _putNum = putNum.getValue();
			getInfo.setText("领取情况：合计"+_cpnNum+"张，领取"+ _alrdyGetNum +"张，发放"+ _putNum +"张，已使用" + _useNum + "张，未使用" + _unUseNum + "张");
            var _totalChargeAmt = totalChargeAmt.getValue();
            var _totalServiceAmt = totalServiceAmt.getValue();
            var _totalCpnTBal = totalCpnTBal.getValue();
			actAmtInfo.setText("优惠信息：订单总金额"+ _totalChargeAmt +"元，服务费金额"+ _totalServiceAmt +"元，优惠券优惠金额"+ _totalCpnTBal +"元");
		}

		queryUse();
		function queryUse(){
		  var datas={};
		  datas = useForm.getFormData();
		  useGrid.query("~/billing/coupon/getAccountCoupons",datas);
		}
    </ls:script>
</ls:body>
</html>