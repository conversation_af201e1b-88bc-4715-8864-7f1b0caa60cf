<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.vip.dao.IVipExclusivePutDao">

    <!-- resultMap定义 -->
    <resultMap id="BaseResultMap" type="com.ls.ner.billing.vip.bo.VipExclusivePutBo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cust_id" property="custId" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="put_day" property="putDay" jdbcType="VARCHAR"/>
        <result column="put_times" property="putTimes" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="RpcResultMap" type="com.ls.ner.billing.api.vip.bo.VipExclusivePutRPCBo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cust_id" property="custId" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="put_day" property="putDay" jdbcType="VARCHAR"/>
        <result column="put_times" property="putTimes" jdbcType="INTEGER"/>
    </resultMap>

    <!-- Base_Column_List定义 -->
    <sql id="Base_Column_List">
        id, cust_id, mobile, put_day, put_times
    </sql>

    <!-- insert语句 -->
    <insert id="insert" parameterType="com.ls.ner.billing.vip.bo.VipExclusivePutBo">
        INSERT INTO e_vip_exclusive_put (cust_id, mobile, put_day, put_times)
        VALUES (#{custId,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{putDay,jdbcType=VARCHAR}, #{putTimes,jdbcType=VARCHAR})
    </insert>

    <!-- update语句 -->
    <update id="update" parameterType="com.ls.ner.billing.vip.bo.VipExclusivePutBo">
        UPDATE e_vip_exclusive_put
        <set>
            <if test="custId != null">
                cust_id = #{custId,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="putDay != null">
                put_day = #{putDay,jdbcType=VARCHAR},
            </if>
            <if test="putTimes != null">
                put_times = #{putTimes,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getExclusiveByPutDay" parameterType="String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM e_vip_exclusive_put
        <where>
            <if test="custId != null">
                AND cust_id = #{custId,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null">
                AND mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="putDay != null">
                AND put_day = #{putDay,jdbcType=VARCHAR}
            </if>
                AND put_times >0
        </where>
    </select>

    <select id="getExclusivePut" parameterType="com.ls.ner.billing.vip.bo.VipExclusivePutBo" resultMap="RpcResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM e_vip_exclusive_put
        <where>
            <if test="custId != null">
                AND cust_id = #{custId,jdbcType=VARCHAR}
            </if>
            <if test="mobile != null">
                AND mobile = #{mobile,jdbcType=VARCHAR}
            </if>
            <if test="putDay != null">
                AND put_day = #{putDay,jdbcType=VARCHAR}
            </if>
            <if test="putTimes != null">
                AND put_times = #{putTimes,jdbcType=VARCHAR}
            </if>
        </where>
        limit 1
    </select>

</mapper>