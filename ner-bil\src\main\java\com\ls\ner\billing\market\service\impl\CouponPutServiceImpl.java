package com.ls.ner.billing.market.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.bo.*;
import com.ls.ner.billing.market.dao.ICouponDao;
import com.ls.ner.billing.market.dao.ICouponPutDao;
import com.ls.ner.billing.market.service.ICouponPutService;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.cust.api.service.ICustCenterRpcService;
import com.ls.ner.cust.api.vo.CouponPutCustVo;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.order.api.service.IOrderRpcService;
import com.ls.ner.pub.api.msg.service.IMsgRpcService;
import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.util.BeanUtil;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.exception.BusinessWarning;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 优惠券发放实现类
 * description
 * 创建时间 2016年5月31日下午2:29:41
 * 创建人 lise
 */
@Service(target = {ServiceType.LOCAL}, value = "bilCouponPutService")
public class CouponPutServiceImpl implements ICouponPutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponPutServiceImpl.class);

    @Autowired(required = true)
    private ICouponPutDao dao; // 发放主表

    @Autowired(required = true)
    private ICouponDao couDao; // 发放详细表

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService; // 优惠券表

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "custCenterRpcService")
    private ICustCenterRpcService custCenterRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "accuCouponRpcService")
    private ICouponRpcService accuCouponRpcService; // 支付中心-优惠券接口


    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IMsgRpcService msgRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "orderRpcService")
    private IOrderRpcService orderRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "orgRpcService")
    private IOrgRpcService orgRpcService;

    private RedisCluster redisCluster = RedisCluster.getInstance();


    /**
     * 描述: 保存上传数据
     *
     * @param: [request]
     * @return: java.util.List<java.util.Map>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-04 11:35
     */
    @Override
    public List<Map> saveFile(MultipartHttpServletRequest request) throws IOException {
        Iterator<String> iter = request.getFileNames();
        List<Map> list = new ArrayList<Map>();
        while (iter.hasNext()) {
            MultipartFile file = request.getFile(iter.next());
            if (file != null) {
                HSSFWorkbook hssfWorkbook = new HSSFWorkbook(file.getInputStream());
                // 循环工作表Sheet
                for (int numSheet = 0; numSheet < hssfWorkbook.getNumberOfSheets(); numSheet++) {
                    HSSFSheet hssfSheet = hssfWorkbook.getSheetAt(numSheet);
                    if (hssfSheet == null) {
                        continue;
                    }
                    // 循环行Row
                    for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
                        HSSFRow hssfRow = hssfSheet.getRow(rowNum);
                        if (hssfRow == null) {
                            continue;
                        }
                        //循环列
                        Map<String, Object> bo = new HashMap<String, Object>();
                        for (int colNum = 0; colNum < hssfRow.getLastCellNum(); colNum++) {
                            HSSFCell cell = hssfRow.getCell(colNum);
                            if (cell == null) {
                                continue;
                            } else {
                                try {
                                    if (colNum == 0) {
                                        String mobile = getValue(cell);
                                        if (StringUtil.isNotBlank(mobile)) {
                                            bo.put("mobile", mobile);
                                            Map<String, Object> inMap = new HashMap<String, Object>();
                                            inMap.put("mobile", mobile);
                                            inMap = this.queryMobiByCust(inMap);
                                            if (inMap != null && inMap.size() > 0) {
                                                /*bo.put("custId", inMap.get("custId"));
                                                bo.put("custName", inMap.get("custName"));
                                                bo.put("custSortCode", inMap.get("custSortCode"));
                                                bo.put("custSortCodeName", inMap.get("custSortCodeName"));*/
                                                bo.putAll(inMap);
                                            } else {
                                                bo.put("custId", "");
                                                bo.put("custName", "");
                                                bo.put("custSortCode", "");
                                                bo.put("custSortCodeName", "");
                                            }
                                        } else {
                                            continue;
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            }
                        }
                        list.add(bo);
                    }
                }
            }
        }
        return list;
    }

    @SuppressWarnings("static-access")
    private String getValue(HSSFCell hssfCell) {

        if (hssfCell.getCellType() == HSSFCell.CELL_TYPE_BOOLEAN) {
            // 返回布尔类型的值
            return String.valueOf(hssfCell.getBooleanCellValue());
        } else if (hssfCell.getCellType() == hssfCell.CELL_TYPE_NUMERIC) {
            if (HSSFDateUtil.isCellDateFormatted(hssfCell)) {
                //  如果是date类型则 ，获取该cell的date值
                Date date = HSSFDateUtil.getJavaDate(hssfCell.getNumericCellValue());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                return sdf.format(date);
            }
            DecimalFormat df = new DecimalFormat("##.##");
            // 返回数值类型的值
            return df.format(hssfCell.getNumericCellValue());
        } else {
            // 返回字符串类型的值
            return String.valueOf(hssfCell.getStringCellValue());

        }
    }

    /**
     * 描述:保存发放信息
     *
     * @param: [bo]
     * @return: void
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-04 15:06
     */
    @Override
    public Map<String, String> excuteCouponPut(CouponPutBo bo,boolean isPreSaveCouponPutDet) throws BusinessWarning {
        //不做参数校验，校验在生成优惠卷下发任务时已经校验
        Map<String, String> retMap = new HashMap<String, String>();
        StringBuffer outMsg = new StringBuffer();
        LOGGER.info("优惠卷发放开始执行，参数：{},是否预存：{}" + JSON.toJSONString(bo),isPreSaveCouponPutDet);
        List<Map<String,Object>> newCustList=new ArrayList<>();
               if (!isPreSaveCouponPutDet){
                   JSONArray jsonArray = JSONArray.fromObject(bo.getCustInfoList());
                   //异步调用会出现类型转换问题（newCustList 将custId转化为String类型，异步调用无问题）
                   List<Map> custList = (List<Map>) JSONArray.toCollection(jsonArray, Map.class);
                   newCustList = custList.stream().map((cust) -> Dict.create().set("custId", cust.get("custId").toString()).set("mobile", cust.get("mobile"))).collect(Collectors.toList());
               }


                //若之前已经存储
                if ((!isPreSaveCouponPutDet)&&CollectionUtils.isEmpty(newCustList)){
                    throw new BusinessWarning("请上传客户信息",this.getClass());
                }
                String putId = bo.getPutId();

                if (StringUtil.isBlank(putId)){
                    // 抛异常，发放主表生成失败
                    throw new BusinessWarning("未生成发放标识，请核查", this.getClass());
                }
                String putTime = bo.getPutTime();
                //是否已预存储发放明细
                if (isPreSaveCouponPutDet){
                List<CouponPutDetBo> couponPutCustVoList=dao.queryCustByPutId(putId);
                LOGGER.info("预存储发放明细：{}",JSON.toJSONString(couponPutCustVoList));
                if (CollectionUtil.isEmpty(couponPutCustVoList)){
                    throw new BusinessWarning("预存储发放明细不存在",this.getClass());
                }
                    JSONArray jsonArrayDetBo = JSONArray.fromObject(couponPutCustVoList);
                    newCustList = (List<Map<String, Object>>) JSONArray.toCollection(jsonArrayDetBo, Map.class);
                    //保证string类型防止下游格式报错
                    newCustList=newCustList.stream().map((cust) -> Dict.create().set("custId", cust.get("custId").toString()).set("mobile", cust.get("mobile"))).collect(Collectors.toList());
                }else {
                    //  新增发放明细
                    saveCouponPutDetList(newCustList,putId,putTime);
                }

                String invDate = bo.getInvDate();
                String eftDate = bo.getEftDate();
                String cpnId = bo.getCpnId();
                String limGetNum = bo.getLimGetNum();
                //调用发放RPC
                        Map<String, Object> inMap = new HashMap<String, Object>();
                        inMap.put("cpnId", cpnId);
                        inMap.put("limGetNum", limGetNum);
                        inMap.put("eftDate", eftDate);
                        inMap.put("invDate", invDate);
                        inMap.put("putTime", putTime);
                        inMap.put("custList", newCustList);
                        inMap.put("getChannel", "02");//02现场，领取渠道channel，存d_account_coupon表的领取来源GET_CHANNEL
                        inMap.put("handFlag", "01");
                        // add biaoxiangd 2017-09-27 添加业务类01租车02充电
                        String prodBusiType = bo.getBusiType();
                        inMap.put("prodBusiType",prodBusiType);
                        inMap.put("getSource","09");//09优惠券发放，领取来源actType，存d_account_coupon表的领取来源GET_SOURCE

                        // RPC04-02-003优惠券发放
                        Map<String, Object> prcMap = accuCouponRpcService.couponPut(inMap);
                        LOGGER.info("RPC04-02-003优惠券发放接口返回数据：{}",JSON.toJSONString(prcMap));
                        if (MapUtils.isEmpty(prcMap)){
                            throw new BusinessWarning("RPC04-02-003优惠券发放接口获取数据失败，请核查", this.getClass());
                        }
                            String putNum = String.valueOf(prcMap.get("putNum"));
                            if (StringUtil.isBlank(putNum) || !StringUtils.isNumeric(putNum)) {
                                throw new BusinessWarning("RPC04-02-003优惠券发放接口获取已领数量数据失败，请核查", this.getClass());

                            }
                                int failCount = 0;
                                if (Integer.parseInt(putNum) > 0) {
                                    outMsg.append("发送成功笔数【" + putNum + "】;");
                                }
//                                // 更新优惠券已领数量
                                // 更新发放主表的发放数量
                            if (BooleanUtil.isTrue(bo.getIsAllCust())){
                                int  flag= dao.cumulaAddPutNum(Long.parseLong(putId), Long.parseLong(putNum));
                                LOGGER.info("putId：{}的回写putNum的值为{}",putId,putNum);
                                if (flag<=0){
                                    throw new BusinessWarning("RPC04-02-003优惠券发放接口更新(累加)发放信息失败，请核查", this.getClass());
                                }
                            }else {
                                bo.setPutNum(putNum);
                                int  flag = dao.updateCouponPut(bo);
                                if (flag<=0){
                                    throw new BusinessWarning("RPC04-02-003优惠券发放接口更新发放信息失败，请核查", this.getClass());
                                }
                            }
                                    // 更新发放详细表的成功标志
                                    List<Map<String, Object>> prcCustList = (List<Map<String, Object>>) prcMap.get("custList");
                                    if (CollectionUtils.isEmpty(prcCustList)){
                                        throw new BusinessWarning("RPC04-02-003优惠券发放接口返回用户集为空，请核查", this.getClass());
                                    }
                                for (Map<String, Object> prcCustMap : prcCustList) {

                                    if (MapUtils.isNotEmpty(prcCustMap)) {
                                        String custId = (String) prcCustMap.get("custId");
                                        String mobile = (String) prcCustMap.get("mobile");
                                        if (StringUtil.isBlank(custId) && StringUtil.isBlank(mobile)) {
                                            continue;
                                        } else {
                                            String putFlag = (String) prcCustMap.get("putFlag");
                                            if (StringUtil.isNotBlank(putFlag)) {
                                                CouponPutDetBo det = new CouponPutDetBo();
                                                det.setPutId(putId);
                                                det.setCustId(custId);
                                                det.setMobile(mobile);
                                                det.setPutFlag(putFlag);
                                                det.setFailReason((String) prcCustMap.get("failReason"));
                                                dao.updateCouponPutDet(det);
                                                if ("0".equals(putFlag)) {
                                                    failCount++;
//                                                                outMsg.append("发放失败原因【" + String.valueOf(prcCustMap.get("failReason")) + "】");
                                                } else {
                                                    //发放成功后，调用短信接口
                                                    String noticeType = bo.getNoticeType();
                                                    String noticeCont = bo.getNoticeCont();
                                                    if ("02".equals(noticeType) && StringUtil.isNotBlank(bo.getNoticeCont()) && StringUtil.isNotBlank(mobile)) {
                                                        Map<String, String> msmMap = new HashedMap();
                                                        msmMap.put("mobile", mobile);
                                                        msmMap.put("msgContent", noticeCont);
                                                        msmMap.put("msgTitle", "优惠券发放提醒");
                                                        msgRpcService.doSendWithoutTemplate(msmMap);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                        if (failCount > 0) {
                                            outMsg.append("发送失败笔数【" + failCount + "】,详情请点击【发送数量】");
                                        }

        retMap.put("outMsg", outMsg.toString());

        return retMap;
    }


    @Override
    public Map<String, Object> excuteCouponPutForMyCar(CouponPutBo bo,boolean isPreSaveCouponPutDet) throws BusinessWarning {
        //不做参数校验，校验在生成优惠卷下发任务时已经校验
        Map<String, String> retMap = new HashMap<String, String>();
        StringBuffer outMsg = new StringBuffer();
        LOGGER.info("优惠卷发放开始执行，参数：{},是否预存：{}" + JSON.toJSONString(bo),isPreSaveCouponPutDet);
        List<Map<String,Object>> newCustList=new ArrayList<>();
        if (!isPreSaveCouponPutDet){
            JSONArray jsonArray = JSONArray.fromObject(bo.getCustInfoList());
            //异步调用会出现类型转换问题（newCustList 将custId转化为String类型，异步调用无问题）
            List<Map> custList = (List<Map>) JSONArray.toCollection(jsonArray, Map.class);
            newCustList = custList.stream().map((cust) -> Dict.create().set("custId", cust.get("custId").toString()).set("mobile", cust.get("mobile"))).collect(Collectors.toList());
        }


        //若之前已经存储
        if ((!isPreSaveCouponPutDet)&&CollectionUtils.isEmpty(newCustList)){
            throw new BusinessWarning("请上传客户信息",this.getClass());
        }
        String putId = bo.getPutId();

        if (StringUtil.isBlank(putId)){
            // 抛异常，发放主表生成失败
            throw new BusinessWarning("未生成发放标识，请核查", this.getClass());
        }
        String putTime = bo.getPutTime();
        //是否已预存储发放明细
        if (isPreSaveCouponPutDet){
            List<CouponPutDetBo> couponPutCustVoList=dao.queryCustByPutId(putId);
            LOGGER.info("预存储发放明细：{}",JSON.toJSONString(couponPutCustVoList));
            if (CollectionUtil.isEmpty(couponPutCustVoList)){
                throw new BusinessWarning("预存储发放明细不存在",this.getClass());
            }
            JSONArray jsonArrayDetBo = JSONArray.fromObject(couponPutCustVoList);
            newCustList = (List<Map<String, Object>>) JSONArray.toCollection(jsonArrayDetBo, Map.class);
            //保证string类型防止下游格式报错
            newCustList=newCustList.stream().map((cust) -> Dict.create().set("custId", cust.get("custId").toString()).set("mobile", cust.get("mobile"))).collect(Collectors.toList());
        }else {
            //  新增发放明细
            saveCouponPutDetList(newCustList,putId,putTime);
        }

        String invDate = bo.getInvDate();
        String eftDate = bo.getEftDate();
        String cpnId = bo.getCpnId();
        String limGetNum = bo.getLimGetNum();
        //调用发放RPC
        Map<String, Object> inMap = new HashMap<String, Object>();
        inMap.put("cpnId", cpnId);
        inMap.put("limGetNum", limGetNum);
        inMap.put("eftDate", eftDate);
        inMap.put("invDate", invDate);
        inMap.put("putTime", putTime);
        inMap.put("custList", newCustList);
        inMap.put("getChannel", "02");//02现场，领取渠道channel，存d_account_coupon表的领取来源GET_CHANNEL
        inMap.put("handFlag", "01");
        // add biaoxiangd 2017-09-27 添加业务类01租车02充电
        String prodBusiType = bo.getBusiType();
        inMap.put("prodBusiType",prodBusiType);
        inMap.put("getSource","09");//09优惠券发放，领取来源actType，存d_account_coupon表的领取来源GET_SOURCE

        // RPC04-02-003优惠券发放
        Map<String, Object> prcMap = accuCouponRpcService.couponPut(inMap);
        LOGGER.info("RPC04-02-003优惠券发放接口返回数据：{}",JSON.toJSONString(prcMap));
        if (MapUtils.isEmpty(prcMap)){
            throw new BusinessWarning("RPC04-02-003优惠券发放接口获取数据失败，请核查", this.getClass());
        }
        String putNum = String.valueOf(prcMap.get("putNum"));
        if (StringUtil.isBlank(putNum) || !StringUtils.isNumeric(putNum)) {
            throw new BusinessWarning("RPC04-02-003优惠券发放接口获取已领数量数据失败，请核查", this.getClass());

        }
        int failCount = 0;
        if (Integer.parseInt(putNum) > 0) {
            outMsg.append("发送成功笔数【" + putNum + "】;");
        }
//                                // 更新优惠券已领数量
        // 更新发放主表的发放数量
        if (BooleanUtil.isTrue(bo.getIsAllCust())){
            int  flag= dao.cumulaAddPutNum(Long.parseLong(putId), Long.parseLong(putNum));
            LOGGER.info("putId：{}的回写putNum的值为{}",putId,putNum);
            if (flag<=0){
                throw new BusinessWarning("RPC04-02-003优惠券发放接口更新(累加)发放信息失败，请核查", this.getClass());
            }
        }else {
            bo.setPutNum(putNum);
            int  flag = dao.updateCouponPut(bo);
            if (flag<=0){
                throw new BusinessWarning("RPC04-02-003优惠券发放接口更新发放信息失败，请核查", this.getClass());
            }
        }
        // 更新发放详细表的成功标志
        List<Map<String, Object>> prcCustList = (List<Map<String, Object>>) prcMap.get("custList");
        if (CollectionUtils.isEmpty(prcCustList)){
            throw new BusinessWarning("RPC04-02-003优惠券发放接口返回用户集为空，请核查", this.getClass());
        }
        for (Map<String, Object> prcCustMap : prcCustList) {

            if (MapUtils.isNotEmpty(prcCustMap)) {
                String custId = (String) prcCustMap.get("custId");
                String mobile = (String) prcCustMap.get("mobile");
                if (StringUtil.isBlank(custId) && StringUtil.isBlank(mobile)) {
                    continue;
                } else {
                    String putFlag = (String) prcCustMap.get("putFlag");
                    if (StringUtil.isNotBlank(putFlag)) {
                        CouponPutDetBo det = new CouponPutDetBo();
                        det.setPutId(putId);
                        det.setCustId(custId);
                        det.setMobile(mobile);
                        det.setPutFlag(putFlag);
                        det.setFailReason((String) prcCustMap.get("failReason"));
                        dao.updateCouponPutDet(det);
                        if ("0".equals(putFlag)) {
                            failCount++;
//                                                                outMsg.append("发放失败原因【" + String.valueOf(prcCustMap.get("failReason")) + "】");
                        } else {
                            //发放成功后，调用短信接口
                            String noticeType = bo.getNoticeType();
                            String noticeCont = bo.getNoticeCont();
                            if ("02".equals(noticeType) && StringUtil.isNotBlank(bo.getNoticeCont()) && StringUtil.isNotBlank(mobile)) {
                                Map<String, String> msmMap = new HashedMap();
                                msmMap.put("mobile", mobile);
                                msmMap.put("msgContent", noticeCont);
                                msmMap.put("msgTitle", "优惠券发放提醒");
                                msgRpcService.doSendWithoutTemplate(msmMap);
                            }
                        }
                    }
                }
            }
        }
        if (failCount > 0) {
            outMsg.append("发送失败笔数【" + failCount + "】,详情请点击【发送数量】");
        }

        retMap.put("outMsg", outMsg.toString());

        return prcMap;
    }

    @Override
    public String savePutCouponPut(CouponPutBo bo) throws BusinessWarning {
        String cpnId = bo.getCpnId();
        if (StringUtil.isBlank(cpnId)) {
            throw new BusinessWarning("优惠券标识为空，请核查", this.getClass());
        }
        String limGetNum = bo.getLimGetNum();
        if (StringUtil.isBlank(limGetNum) || !StringUtils.isNumeric(limGetNum)) {
            throw new BusinessWarning("每人限领数量为空，请核查", this.getClass());
        }
        //校验客户信息列表
        if (BooleanUtil.isFalse(bo.getIsAllCust())&& (StringUtil.isBlank(bo.getCustInfoList())|| CollectionUtils.isEmpty( JSONArray.fromObject(bo.getCustInfoList()) ))){
            throw new BusinessWarning("未选择发放的客户，请核查", this.getClass());
        }
        boolean isDelay = "1".equals(bo.getIsDelay().toString());
        if (isDelay&&bo.getDelayPutTime()==null||(!isDelay)&&bo.getDelayPutTime()!=null){
            throw new BusinessWarning("定时参数异常", this.getClass());
        }
        if (isDelay){
            bo.setPutTime(DateTools.dateToStr(bo.getDelayPutTime(),"yyyy-MM-dd HH:mm:ss"));
        }else {
            bo.setPutTime(DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss"));
        }
        // 生效~失效时间计算
        Map<String, String> timeMap = this.calcTimeRanges(bo);
        if (MapUtil.isNotEmpty(timeMap)) {
            String outCode = timeMap.get("outCode");
            if ("0".equals(outCode)) {
              String  eftDate = timeMap.get("eftDate");
              String  invDate = timeMap.get("invDate");
                // 保存发放信息
                bo.setEftDate(StringUtil.isNotBlank(eftDate)?eftDate:null);
                bo.setInvDate(StringUtil.isNotBlank(invDate)?invDate:null);
            } else {
                throw new BusinessWarning(timeMap.get("outMsg"), this.getClass());
            }
        }
     //PutNum
       try {
           JSONArray jsonArrayDetBo = JSONArray.fromObject(bo.getCustInfoList());
           if (BooleanUtil.isFalse(bo.getIsAllCust())){
               bo.setPutNum(String.valueOf(jsonArrayDetBo.size()));
           }else {
               //消费的时候再次填充
               bo.setPutNum("0");
           }
       }catch (Exception exception){
           bo.setPutNum("0");
       }
         bo.setPutEmpType("01");
        //若是全体用户在消费完再会写PutNum
        String getChannel = bo.getGetChannel();
        if (StringUtil.isBlank(getChannel)) {
            getChannel = "01";
        }

        bo.setPutChannel(getChannel);
        dao.insertCouponPut(bo);
        LOGGER.info("券优惠发放信息保存成功,优惠券标识【" + bo.getPutId() + "】,发放实体【" + JSON.toJSONString(bo) + "】");
        return bo.getPutId();
    }

    /**
     * @param bo
     * @description 小鹏优惠券发放
     * <AUTHOR>
     * @create 2018-06-25 15:56:04
     */
    @Override
    public Map<String, String> xpSavePutInfo(CouponPutBo bo) throws BusinessWarning {
        Map<String, String> retMap = new HashMap<String, String>();
        StringBuffer outMsg = new StringBuffer();
        String custInfoList = bo.getCustInfoList();
        if (StringUtil.isNotBlank(custInfoList)) {
            try {
                JSONArray jsonArray = JSONArray.fromObject(custInfoList);
                List<Map<String, String>> custList = (List<Map<String, String>>) JSONArray.toCollection(jsonArray, Map.class);
                String cpnId = bo.getCpnId();
                if (StringUtil.isBlank(cpnId)) {
                    throw new BusinessWarning("优惠券标识为空，请核查", this.getClass());
                }
                String limGetNum = bo.getLimGetNum();
                if (StringUtil.isBlank(limGetNum) || !StringUtils.isNumeric(limGetNum)) {
                    throw new BusinessWarning("每人限领数量为空，请核查", this.getClass());
                }

                String putTime = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                bo.setPutTime(putTime);
                // 生效~失效时间计算
                String eftDate = "", invDate = "";
                Map<String, String> timeMap = this.calcTimeRanges(bo);
                if (timeMap != null && timeMap.size() > 0) {
                    if ("0".equals(timeMap.get("outCode"))) {
                        eftDate = timeMap.get("eftDate");
                        invDate = timeMap.get("invDate");
                    } else {
                        throw new BusinessWarning(timeMap.get("outMsg"), this.getClass());
                    }
                }
                if (StringUtil.isBlank(eftDate)) {
                    bo.setEftDate(null);
                }
                if (StringUtil.isBlank(invDate)) {
                    bo.setInvDate(null);
                }
                if (custList != null && custList.size() > 0) {
                    // 新增发放
                    bo.setPutEmpType("01");
                    bo.setPutNum(custList.size() + "");
                    String getChannel = bo.getGetChannel();
                    if (StringUtil.isBlank(getChannel)) {
                        getChannel = "01";
                    }
                    bo.setPutChannel(getChannel);
                    bo.setNoticeType("01");//通知方式，01无 02短信
                    int flag = dao.insertCouponPut(bo);
                    // 调用订单优惠券发放——小鹏
                    String putId = bo.getPutId();
                    if (StringUtil.isNotBlank(putId)) {
                        //  新增发放明细
                        for (Map<String, String> map : custList) {
                            CouponPutDetBo det = new CouponPutDetBo();
                            det.setPutId(putId);
                            det.setPutTime(putTime);
                            String detCustId = map.get("uid");
                            String detMobile = map.get("mobile");
                            if (StringUtil.isBlank(detCustId) && StringUtil.isBlank(detMobile)) {
                                continue;
                            }
                            if (StringUtil.isBlank(detCustId)) detCustId = null;
                            det.setCustId(detCustId);
                            det.setMobile(detMobile);
                            det.setPutFlag("0");
                            det.setFailReason("未调用【订单域小鹏优惠券发放】接口");
                            dao.insertCouponPutDet(det);
                        }
                        Map<String, Object> inMap = new HashMap<String, Object>();
                        inMap.put("cpnId", cpnId);
                        inMap.put("putTime", putTime);
                        inMap.put("cpnTimeType", "1");//1绝对日期 2相对日期
                        inMap.put("eftDate", eftDate);
                        inMap.put("invDate", invDate);
                        inMap.put("putEmp", bo.getPutEmp());//发放人员
                        inMap.put("custList", custList);
                        inMap.put("handFlag", "01");//01发放  02领取

                        // 小鹏优惠券发放
                        List<Map<String, Object>> prcMap = orderRpcService.couponPut(inMap);
                        if (StringUtil.isNotEmpty(prcMap)&& prcMap.size() > 0) {
                            String putNum = String.valueOf(prcMap.size());
                            // 更新发放主表的发放数量
                            bo.setPutNum(putNum);
                            flag = dao.updateCouponPut(bo);
                            if (flag > 0) {
                                // 更新发放详细表的成功标志
                                for (Map<String, Object> map : prcMap) {
                                    CouponPutDetBo det = new CouponPutDetBo();
                                    det.setPutId(putId);
                                    det.setCustId(StringUtil.getString(map.get("custId")));
                                    det.setMobile(StringUtil.getString(map.get("mobile")));
                                    det.setPutFlag(StringUtil.getString(map.get("putFlag")));
                                    det.setFailReason(StringUtil.getString(map.get("failReason")));
                                    dao.updateCouponPutDet(det);
                                }
                            } else {
                                throw new BusinessWarning("订单域小鹏优惠券发放接口更新发放信息失败，请核查", this.getClass());
                            }
                        } else {
                            throw new BusinessWarning("订单域小鹏优惠券发放接口获取数据失败，请核查", this.getClass());
                        }

                    } else {
                        // 抛异常，发放主表生成失败
                        throw new BusinessWarning("未生成发放标识，请核查", this.getClass());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessWarning(e.getLocalizedMessage(), CouponPutServiceImpl.class);
            }
        } else {
            throw new BusinessWarning("未选择发放的客户，请核查", this.getClass());
        }

        retMap.put("outMsg", outMsg.toString());
        return retMap;
    }

    /**
     * 描述:查询优惠券发放记录
     *
     * @param: [condition]
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPutLogBo>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 14:57
     */
    @Override
    public List<CouponPutBo> getCouponPutLog(MarketCondition condition) {
        List<CouponPutBo> list = dao.getCouponPutLog(condition);
        if (list != null && list.size() > 0) {
            Map<String, Object> param = new HashMap<String, Object>();
            for (int i = 0; i < list.size(); i++) {
                CouponPutBo putBo = list.get(i);
                if (putBo != null) {
                    String noticeType = putBo.getNoticeType();
                    if (StringUtil.isNotBlank(noticeType)) {
                        CodeBO codeBO = codeService.getStandardCode("noticeType", noticeType, null);
                        if (codeBO != null) {
                            list.get(i).setNoticeTypeName(codeBO.getCodeName());
                        }
                    }
                }
            }
        } else {
            list = new ArrayList<CouponPutBo>();
        }
        return list;
    }

    /**
     * 描述:统计优惠券发放记录
     *
     * @param: [condition]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-09 14:39
     */
    public int getCouponPutLogCount(MarketCondition condition) {
        return dao.getCouponPutLogCount(condition);
    }

    /**
     * 获取优惠券发放记录详情
     * description
     * 创建时间 2016年6月3日上午11:17:53
     * 创建人 lise
     */
    public List<CouponPutBo> getCouponPutDetLog(MarketCondition condition) {
        List<CouponPutBo> list = dao.getCouponPutDetLog(condition);
        if (list != null && list.size() > 0) {
            Map<String, Object> param = new HashMap<String, Object>();
            for (int i = 0; i < list.size(); i++) {
                CouponPutBo putBo = list.get(i);
                if (putBo != null) {
                    // 功能调用：查询优惠券发放客户列表
                    String custId = putBo.getCustId();
                    String mobile = putBo.getMobile();
                    if (StringUtil.isNotBlank(custId) || StringUtil.isNotBlank(mobile)) {
                        param.put("custId", custId);
                        param.put("mobile", mobile);
                        Map<String, Object> custInfoMap = custCenterRpcService.getCustInfo(param);
                        if (custInfoMap != null && custInfoMap.size() > 0) {
                            custId = String.valueOf(custInfoMap.get("custId"));
                            String custName = String.valueOf(custInfoMap.get("custName"));
                            mobile = String.valueOf(custInfoMap.get("mobile"));
                            String custSortCode = String.valueOf(custInfoMap.get("custType"));
                            String custSortCodeName = "";
                            if (StringUtil.isNotBlank(custSortCode)) {
                                CodeBO codeBO = codeService.getStandardCode("custType", custSortCode, null);
                                if (codeBO != null) {
                                    custSortCodeName = codeBO.getCodeName();
                                }
                            }
                            list.get(i).setCustName(custName);
                            list.get(i).setMobile(mobile);
                            list.get(i).setCustSortCode(custSortCode);
                            list.get(i).setCustSortCodeName(custSortCodeName);
                        }
                    }
                }
            }
        } else {
            list = new ArrayList<CouponPutBo>();
        }
        return list;
    }

    /**
     * 统计优惠券发放记录详情
     * description
     * 创建时间 2016年6月3日上午11:17:53
     * 创建人 lise
     */
    public int getCouponPutLogDetCount(MarketCondition condition) {
        return dao.getCouponPutLogDetCount(condition);
    }

    /**
     * 获取优惠券
     */
    @Override
    public CouponBo getCoupon(MarketCondition condition) {
        CouponBo bo = new CouponBo();
        List<CouponBo> list = couDao.getCoupons(condition);
        if (list.size() > 0) {
            bo = list.get(0);
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    String timeUit = bo.getTimeUnit();
                    if (StringUtil.isNotBlank(timeUit)) {
                        CodeBO timeUniBo = codeService.getStandardCode("timeUnit", timeUit, null);
                        if (timeUniBo != null) {
                            String effectTime = bo.getEffectTime();
                            bo.setEffectTime(effectTime + "" + timeUniBo.getCodeName());
                        }
                    }
                }
            }
        }
        return bo;
    }

    /**
     * 描述:获取优惠券发放信息
     *
     * @param: [condition]
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 11:09
     */
    @Override
    public List<CouponBo> getCouponPut(MarketCondition condition) {
        List<CouponBo> list = dao.getCouponPut(condition);
        if (list != null && list.size() > 0) {
            for (int i = 0, j = list.size(); i < j; i++) {
                CouponBo bo = list.get(i);
                String cpnType = bo.getCpnType();
//                2018-06-12 @lcl  1、修改列表项文案：“面额/折扣”改为“面额/折扣（元）”2、该列表项的下方数字不显示“元”
//                if (StringUtil.isNotBlank(cpnType)) {
//                    if ("01".equals(cpnType)) {
//                        list.get(i).setCpnAmt(bo.getCpnAmt() + "元");
//                    } else {
//                        list.get(i).setCpnAmt(bo.getCpnAmt() + "折");
//                    }
//                }
                String cpnTimeType = bo.getCpnTimeType();
                if (StringUtil.isNotBlank(cpnTimeType)) {
                    if ("2".equals(cpnTimeType)) {
                        String timeUnit = bo.getTimeUnit();
                        if (StringUtil.isNotBlank(timeUnit)) {
                            CodeBO timeUniBo = codeService.getStandardCode("timeUnit", timeUnit, null);
                            if (timeUniBo != null) {
                                String effectTime = bo.getEffectTime();
                                list.get(i).setEffectTime(effectTime + "" + timeUniBo.getCodeName());
                            }
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * 统计优惠券发放
     * description
     * 创建时间 2016年6月4日下午5:19:52
     * 创建人 lise
     */
    @Override
    public int getCouponPutCount(MarketCondition condition) {
        return dao.getCouponPutCount(condition);
    }

    public String getPlusMinToSomeDate(int mins, String somedate) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse(somedate);
        Long m = mins * 60L * 1000L;//(毫秒)
        Long time = date.getTime() + m;
        Date dateNew = new Date(time);
        return sdf.format(dateNew);
    }

    /**
     * 描述:通过手机号码获取客户信息
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-04 11:38
     */
    @Override
    public Map<String, Object> queryMobiByCust(Map<String, Object> dataParams) {
        String mobile = (String) dataParams.get("mobile");
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("mobile", mobile);
        Map<String, Object> custInfoMap = custCenterRpcService.getCustInfo(param);
        if (custInfoMap != null && custInfoMap.size() > 0) {
            String custProperty = (String) custInfoMap.get("custType");
            if (StringUtil.isNotBlank(custProperty)) {
                custInfoMap.put("custSortCode", custProperty);
                CodeBO codeBO = codeService.getStandardCode("custType", custProperty, null);
                if (codeBO != null) {
                    custInfoMap.put("custSortCodeName", codeBO.getCodeName());
                }
            }
        }
        return custInfoMap;
    }

    /**
     * 描述:优惠券时间范围换算，针对相对时间
     *
     * @param: [bo]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-05 9:41
     */
    public static Map<String, String> calcTimeRanges(CouponPutBo bo) {
        Map<String, String> retMap = new HashMap<String, String>();
        String outCode = "0", outMsg = "ok";
        // 生效日期,失效日期
        String eftDate = "", invDate = "";
        try {
            if (bo != null) {
                String cpnTimeType = bo.getCpnTimeType();
                if (StringUtil.isNotBlank(cpnTimeType)) {
                    if ("2".equals(cpnTimeType)) {
                        // 日、月、年转换
                        SimpleDateFormat bgnSdf = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat endSdf = new SimpleDateFormat("yyyy-MM-dd");
                        Calendar bgnCale = Calendar.getInstance();
                        Calendar endCale = Calendar.getInstance();
                        // 生效时间计算
                        String bgnDate = bo.getPutTime();
                        if (StringUtil.isBlank(bgnDate)) {
                            bgnDate = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                        }
                        bgnCale.setTime(bgnSdf.parse(bgnDate));
                        eftDate = bgnSdf.format(bgnCale.getTime());
                        int iYear = 0, iMonth = 0, iDay = 0;
                        if (!StringUtils.isBlank(eftDate)) {
                            iYear = Integer.parseInt(eftDate.substring(0, 4));
                            iMonth = Integer.parseInt(eftDate.substring(5, 7)) - 1;
                            iDay = Integer.parseInt(eftDate.substring(8, 10));
                        } else {
                            retMap.put("outCode", "-1");
                            retMap.put("outMsg", "生效时间为空，请核查");
                            return retMap;
                        }
                        // 失效时间计算
                        String timeUnit = bo.getTimeUnit();
                        if (StringUtil.isBlank(timeUnit)) {
                            timeUnit = "1";
                        }
                        String timeDuration = bo.getTimeDuration();
                        if (StringUtil.isNotBlank(timeDuration) && StringUtils.isNumeric(timeDuration)) {
                            int iTimeDuration = Integer.parseInt(timeDuration);
                            switch (timeUnit) {
                                case "1": // 日
                                    iTimeDuration = iTimeDuration + iDay;
                                    endCale.set(Calendar.DATE, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "4": // 月
                                    iTimeDuration = iTimeDuration + iMonth;
                                    endCale.set(Calendar.MONTH, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "5": // 年
                                    iTimeDuration = iTimeDuration + iYear;
                                    endCale.set(Calendar.YEAR, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                            }
                        } else {
                            outCode = "-1";
                            outMsg = "生效时间偏移量数据错误，请核查";
                        }
                    } else {
                        eftDate = bo.getEftDate();
                        invDate = bo.getInvDate();
                    }
                } else {
                    outCode = "-1";
                    outMsg = "优惠券生效时间类型为空，请核查";
                }
            } else {
                outCode = "-1";
                outMsg = "转换日期函数参数为空，请核查";
            }
        } catch (ParseException e) {
            e.printStackTrace();
            outCode = "-1";
            outMsg = "日期转换错误，请核查";
        }
        retMap.put("eftDate", eftDate);
        retMap.put("invDate", invDate);
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        return retMap;
    }

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-02 优惠券领取
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    @Override
    public Map<String, Object> couponsRestPOST(Map<String, Object> paramMap) throws BusinessWarning {
        Map<String, Object> retMap = new HashMap<String, Object>();
        StringBuffer outMsg = new StringBuffer();
        if (paramMap != null && paramMap.size() > 0) {
            CouponPutBo bo = new CouponPutBo();
            BeanUtil.populate(bo, paramMap);
            String custInfoList = bo.getCustInfoList();
            if (StringUtil.isNotBlank(custInfoList)) {
                JSONArray jsonArray = JSONArray.fromObject(custInfoList);
                try {
                    String cpnId = bo.getCpnId();
                    if (StringUtil.isBlank(cpnId)) {
                        throw new BusinessWarning("优惠券标识为空，请核查", this.getClass());
                    }
                    String limGetNum = bo.getLimGetNum();
                    if (StringUtil.isBlank(limGetNum) || !StringUtils.isNumeric(limGetNum)) {
                        throw new BusinessWarning("每人限领数量为空，请核查", this.getClass());
                    }

                    String putTime = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                    bo.setPutTime(putTime);
                    // 生效~失效时间计算
                    String eftDate = "", invDate = "";
                    Map<String, String> timeMap = this.calcTimeRanges(bo);
                    if (timeMap != null && timeMap.size() > 0) {
                        String outCode = timeMap.get("outCode");
                        if ("0".equals(outCode)) {
                            eftDate = timeMap.get("eftDate");
                            invDate = timeMap.get("invDate");
                        } else {
                            throw new BusinessWarning(timeMap.get("outMsg"), this.getClass());
                        }
                    }

                    List<Map<String, String>> custList = (List<Map<String, String>>) JSONArray.toCollection(jsonArray, Map.class);
                    if (custList != null && custList.size() > 0) {
                        // 调用RPC支付中心 RPC04-02-003优惠券发放
                        Map<String, Object> inMap = new HashMap<String, Object>();
                        inMap.put("cpnId", cpnId);
                        inMap.put("limGetNum", limGetNum);
                        inMap.put("eftDate", eftDate);
                        inMap.put("invDate", invDate);
                        inMap.put("putTime", putTime);
                        inMap.put("custList", custList);
                        inMap.put("getChannel", bo.getGetChannel());
                        inMap.put("handFlag", "02");
                        // add biaoxiagnd 2017-09-27 添加产品类型01租车02充电
                        String prodBusiType = StringUtil.getString(paramMap.get("prodBusiType"));
                        if(StringUtil.isBlank(prodBusiType) && StringUtil.isNotBlank(cpnId)){
                            MarketCondition conditon = new MarketCondition();
                            conditon.setCpnId(cpnId);
                            conditon.setCpnStatus("1");
                            conditon.setEnd(0); // 不需要分页
                            CouponBo conponBo = this.getCoupon(conditon);
                            prodBusiType = conponBo.getBusiType();
                            if(StringUtil.isBlank(prodBusiType)){
                                prodBusiType = "02";
                            }
                        }
                        inMap.put("prodBusiType",prodBusiType);
                        inMap.put("getSource","01");
                        // RPC04-02-003优惠券发放
                        retMap = accuCouponRpcService.couponPut(inMap);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new BusinessWarning(e.getLocalizedMessage(), CouponPutServiceImpl.class);
                }
            }
        } else {
            throw new BusinessWarning("未选择发放的客户，请核查", this.getClass());
        }

        return retMap;
    }


    @Override
    public void excuteCouponPutAllCust(CouponPutBo couponPutBo) {

        //异步获得custCount
        ThreadUtil.execute(()->{
            // 多线程处理 优惠卷消费
            AtomicReference atomicReference=new AtomicReference<ThreadPageBo>(new ThreadPageBo(3L,1L));
            ReentrantLock reentrantLock = new ReentrantLock();

            //
            LOGGER.info("异步执行ALLCUST开始,请求couponPutBo：{}",JSON.toJSONString(couponPutBo));
            List<String> subOrgCodes=new ArrayList<>();
            try {
                subOrgCodes = orgRpcService.getSubOrgCodes();
                LOGGER.info("subOrgCodes:{}",JSON.toJSONString(subOrgCodes));
            } catch (Exception e) {
                LOGGER.info("获取Org失败");
                throw new BusinessWarning("异步推送ALLCUST失败,获得getSubOrgCodes异常",this.getClass());
            }

            List<CompletableFuture<Long>> futures = new ArrayList();
            for (int i = 0; i < 4; i++) {
                List<String> finalSubOrgCodes = subOrgCodes;
                futures.add(CompletableFuture.supplyAsync(()->{
                            try {
                                return this.threadSavePutInfoAllUser(atomicReference, couponPutBo, reentrantLock, finalSubOrgCodes);
                            } catch (BusinessWarning e) {
                                LOGGER.info("异步执行异常,请求threadPageBo：{},couponPutBo：{},业务异常信息：{}",atomicReference.get(),couponPutBo,ExceptionUtil.stacktraceToString(e));
                            }catch (Exception e){
                                LOGGER.info("异步执行系统异常：请求threadPageBo：{},couponPutBo：{}exception：{}",atomicReference.get(),couponPutBo,ExceptionUtil.stacktraceToString(e));
                            }
                            return 0l;
                        }
                ));
            }
            CompletableFuture.allOf(futures.toArray(new  CompletableFuture[futures.size()])).join();
            long custCount=0;
            for (CompletableFuture<Long> future : futures) {
                try {
                    custCount+=future.get();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                }
            }
            LOGGER.info("全体客户发放putId{}的预计发放数量custCount:{}",couponPutBo.getPutId(),custCount);
            if (custCount>0){
                //会写putNum
              //  dao.updateCouponPutNum(couponPutBo.getPutId(),custCount);
            }

        });

    }

    @Override
    public CouponBo getCouponDetail(MarketCondition condition) {

        CouponBo bo = this.getCoupon(condition);
        if (bo != null) {
            CodeBO codeBO = new CodeBO();
            // 优惠券类型
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                codeBO = codeService.getStandardCode("cpnType", cpnType, null);
                if (codeBO != null) {
                    bo.setCpnTypeName(codeBO.getCodeName());
                }
            }
            // 优惠券内容
            String busiType = bo.getBusiType();
            if (StringUtil.isNotBlank(busiType)) {
                codeBO = codeService.getStandardCode("orderType", busiType, null);
                if (codeBO != null) {
                    bo.setBusiTypeName(codeBO.getCodeName());
                }
            }
            // 优惠券状态
            String cpnStatus = bo.getCpnStatus();
            if (StringUtil.isNotBlank(cpnStatus)) {
                codeBO = codeService.getStandardCode("cpnState", cpnStatus, null);
                if (codeBO != null) {
                    bo.setCpnStatusName(codeBO.getCodeName());
                }
            }
        }
        return bo;
    }

    @Override
    public void saveCouponPutDetList(List<Map<String, Object>> custList, String putId, String putTime) {
        if (CollectionUtil.isNotEmpty(custList)){
            //  新增发放明细
            for (Map<String, Object> map : custList) {
                CouponPutDetBo det = new CouponPutDetBo();
                det.setPutId(putId);
                det.setPutTime(putTime);
                String detCustId = map.get("custId").toString();
                String detMobile = map.get("mobile").toString();
                if (StringUtil.isBlank(detCustId) && StringUtil.isBlank(detMobile)) {
                    continue;
                }
                if (StringUtil.isBlank(detCustId)) detCustId = null;
                det.setCustId(detCustId);
                det.setMobile(detMobile);
                det.setPutFlag("0");
                det.setFailReason("未调用【RPC04-02-003优惠券发放】接口");
                dao.insertCouponPutDet(det);
            }
        }
    }

    @Override
    public boolean updateDelayByPutId(String putId, Integer delayValue,Integer expectDelay) {
       return dao.updateDelayByPutId(putId,delayValue, expectDelay);
    }

    @Override
    public DelayInfo qureyCouponDelayInfo(String cpnId) {
        DelayInfo delayInfo = new DelayInfo();
        if (StringUtil.isNotBlank(cpnId)){
            delayInfo= dao.queryPutDelayByCpnId(cpnId);
        }
        return delayInfo;
    }


    private long threadSavePutInfoAllUser(AtomicReference<ThreadPageBo> pageBoAtomicReference, CouponPutBo couponPutBo, ReentrantLock reentrantLock,List<String> subOrgCodes) throws Exception {
        //统计个数
        long count = 0;
        List<CouponPutCustVo> custVoList = new ArrayList<>();
        //数据查询是串行阻塞的  MaxCustId==null代表第一页，有值代表后续页
        do {
        try {
            reentrantLock.lock();
            try {
                custVoList = custCenterRpcService.queryCouponPutCustVo(pageBoAtomicReference.get().getPageSize(),
                        pageBoAtomicReference.get().getMaxCustId(), subOrgCodes);
                LOGGER.info("用户列表查询结果：{}",JSONObject.toJSONString(custVoList));
            }catch (Exception e){
                LOGGER.info("用户列表查询异常,参数：{}",e.toString());
            }
                  if (CollectionUtils.isNotEmpty(custVoList)){
                //设置最后一个CustId
                CouponPutCustVo couponPutCustVo = custVoList.stream().max(Comparator.comparing(CouponPutCustVo::getCustId)).get();
                ThreadPageBo threadPageBo = pageBoAtomicReference.get();
                threadPageBo.setPageSize(threadPageBo.getPageSize());
                threadPageBo.setMaxCustId(couponPutCustVo.getCustId());
                threadPageBo.setPageIndex(threadPageBo.getPageIndex()+1);
                pageBoAtomicReference.set(threadPageBo);
                count+=custVoList.size();
            }
        }catch (Exception e) {
            LOGGER.info("用户列表查询异常,参数：{}",JSONObject.toJSONString(pageBoAtomicReference.get()));
            custVoList=new ArrayList<>();
        }finally {
            reentrantLock.unlock();
            //查询数据异常，提前结束
        }
            if (CollectionUtils.isNotEmpty(custVoList)){
                //消费逻辑
                couponPutBo.setCustInfoList(JSON.toJSONString(custVoList));
                LOGGER.info("异步请求参数：{},分页请求参数{}",JSON.toJSONString(couponPutBo),JSON.toJSONString(pageBoAtomicReference.get()));
                Map<String, String> reulstMap = this.excuteCouponPut(couponPutBo, false);
                LOGGER.info("threadPageBo参数：{}，异步执行结果：{}",JSON.toJSONString(pageBoAtomicReference.get()),JSON.toJSONString(reulstMap));
            }
        }
        while (custVoList.size()>=pageBoAtomicReference.get().getPageSize());
        return count;


    }

}
