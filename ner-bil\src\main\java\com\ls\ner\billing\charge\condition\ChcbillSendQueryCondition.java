package com.ls.ner.billing.charge.condition;

import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.util.Date;
import java.util.List;

/**
 * 充电资费管理---服务项目维护-查询条件
 * <AUTHOR>
 */
public class ChcbillSendQueryCondition extends QueryCondition {
	private String sendStatus;//下发状态，01未下发 02下发失败 03部分成功 04下发成功
	private String chcNo;//充电计费编号
	private String pileSendStatus;//下发状态，001成功、003失败、002下发中
	private String pileNo;//充电桩编号
	private String orgCode;//单位编号
	private String orgCodeName;
	private String pileSendStatuss;//多个下发状态
	private String stationId;//站点id
	private List pileNos;//桩编号
	private String pileId;//桩ID
	private List pileIds;//桩编号
	private List orgCodeList;
	
	private String subType;//设备子状态

	private String sendTime;

	private String curChcNo;
	private String curChcName;
	private String newChcNo;
	private String newChcName;
	private String groupType;

	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}

	public String getPileId() {
		return pileId;
	}
	public void setPileId(String pileId) {
		this.pileId = pileId;
	}
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getPileSendStatus() {
		return pileSendStatus;
	}
	public void setPileSendStatus(String pileSendStatus) {
		this.pileSendStatus = pileSendStatus;
	}
	public String getPileNo() {
		return pileNo;
	}
	public void setPileNo(String pileNo) {
		this.pileNo = pileNo;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getPileSendStatuss() {
		return pileSendStatuss;
	}
	public void setPileSendStatuss(String pileSendStatuss) {
		this.pileSendStatuss = pileSendStatuss;
	}
	public String[] getPileSendStatussSplit() {
		if(StringUtils.nullOrBlank(pileSendStatuss))
			return null;
		return pileSendStatuss.split(",");
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	public List getPileNos() {
		return pileNos;
	}
	public void setPileNos(List pileNos) {
		this.pileNos = pileNos;
	}

	public List getPileIds() {
		return pileIds;
	}

	public void setPileIds(List pileIds) {
		this.pileIds = pileIds;
	}

	public String getCurChcNo() {
		return curChcNo;
	}

	public void setCurChcNo(String curChcNo) {
		this.curChcNo = curChcNo;
	}

	public String getCurChcName() {
		return curChcName;
	}

	public void setCurChcName(String curChcName) {
		this.curChcName = curChcName;
	}

	public String getNewChcNo() {
		return newChcNo;
	}

	public void setNewChcNo(String newChcNo) {
		this.newChcNo = newChcNo;
	}

	public String getNewChcName() {
		return newChcName;
	}

	public void setNewChcName(String newChcName) {
		this.newChcName = newChcName;
	}

	public String getGroupType() {
		return groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	public List getOrgCodeList() {
		return orgCodeList;
	}

	public void setOrgCodeList(List orgCodeList) {
		this.orgCodeList = orgCodeList;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	
}
