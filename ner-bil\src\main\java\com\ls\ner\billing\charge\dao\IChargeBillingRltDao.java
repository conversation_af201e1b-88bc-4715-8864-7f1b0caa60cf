package com.ls.ner.billing.charge.dao;

import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @dateTime 2016-03-28
 * @description 充电订单计费结果  E_CHARGE_BILLING_RLT
 */
public interface IChargeBillingRltDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 查询充电订单计费结果E_CHARGE_BILLING_RLT
	 */
	public ChargeBillingRltBo queryChargeBillingRlt(String appNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 查询充电订单计费结果条数E_CHARGE_BILLING_RLT
	 */
	public int queryChargeBillingRltNum(String appNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2019-01-19
	 * @description 删除充电订单计费结果E_CHARGE_BILLING_RLT
	 */
	public int deleteChargeBillingRlt(String appNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 新增充电订单计费结果  E_CHARGE_BILLING_RLT
	 */
	public void insertChargeBillingRlt(ChargeBillingRltBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 更新充电订单计费结果  E_CHARGE_BILLING_RLT
	 */
	public void updateChargeBillingRlt(ChargeBillingRltBo bo);

	/**
	 * @param inMap
	 * @description 通过订单查询计费信息
	 * <AUTHOR>
	 * @create 2019-05-16 16:50:01
	 */
    int qryOrderBillConfFlag(Map<String, Object> inMap);

	/**
	 * @param iMap
	 * @description
	 * <AUTHOR> 更新站点下面计费的管理单位
	 * @create 2019-05-27 12:20:44
	 */
	public void updateChargeBillingOrgCode(Map<String, Object> iMap);

	/**
	 * @param inMap
	 * @description 查询订单分时电量
	 * <AUTHOR>
	 * @create 2019-07-08 19:13:29
	 */
	List<Map<String, Object>> getChargePeriodsItemsPq(Map inMap);
}
