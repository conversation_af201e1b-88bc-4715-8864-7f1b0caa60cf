package com.ls.ner.billing.gift.dao;

import com.ls.ner.billing.gift.bo.CarGiftBo;
import com.ls.ner.billing.gift.condition.CarGiftCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description ICarGiftDao
 * @create 2020-04-29 17:21
 */
public interface ICarGiftDao {
    /**
     *
     * 购车券查询
     *
     */
    List<CarGiftBo> listCarGift(CarGiftCondition carGiftCondition);


    int countCarGift(CarGiftCondition carGiftCondition);

    /**
     * @param carGiftBo
     * @description 更新操作
     * <AUTHOR>
     * @create 2020-05-04 12:52:13
     */
    int updateCarGift(CarGiftBo carGiftBo);


    Map queryRecentGiftInfo(Map inMap);
    /**
     * @param carGiftBoList
     * @description 批量插入导入数据
     * <AUTHOR>
     * @create 2020-05-04 14:55:39
     */
    void insertImportData(@Param(value = "carGiftBoList") List<CarGiftBo> carGiftBoList);

    /**
     * @param carGiftBo
     * @description 查询购车礼金申请记录
     * <AUTHOR>
     * @create 2020-05-04 15:00:07
     */
    List<CarGiftBo> listCarGiftByCondition(CarGiftBo carGiftBo);
}
