package com.ls.ner.billing.api.rent.model;

import java.io.Serializable;
import java.math.BigDecimal;

import com.ls.ner.billing.api.rent.model.IChargeItem;
import com.ls.ner.billing.api.rent.model.IChargeTimePoint;
import com.ls.ner.billing.api.rent.model.SubChargeItem;


public class MainChargeItem  implements IChargeItem,IChargeTimePoint,Serializable {
	
	private static final long serialVersionUID = -5028307051807795639L;

	/**
	 * 收费时间点 
	 * @see  com.ls.ner.billing.api.BillingConstants.ChargeTimePoint
	 */
	private String chargeTimePoint;
	
	/**
	 * 计费方式 时间/里程/时间里程混合
	 */
	private String chargeWay;
	/**
	 * 计费模式 标准/分时/阶梯
	 */
	private String chargeMode;
	/**
	 * 基于时间的计费子项 时间 时间里程混合使用
	 */
	private SubChargeItem timeChargeItem;
	/**
	 * 基于里程的计费子项 里程 时间里程混合使用
	 */
	private SubChargeItem millChargeItem;

	//min/max
	
	
	private BigDecimal result;
	
	private BigDecimal timeResult;//租金  分时计费结果
	
	private BigDecimal millResult;//里程 按里程计费结果
	
	public String getChargeWay() {
		return chargeWay;
	}

	public void setChargeWay(String chargeWay) {
		this.chargeWay = chargeWay;
	}

	public String getChargeMode() {
		return chargeMode;
	}

	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}
	
	public SubChargeItem getTimeChargeItem() {
		return timeChargeItem;
	}

	public void setTimeChargeItem(SubChargeItem timeChargeItem) {
		this.timeChargeItem = timeChargeItem;
	}

	public SubChargeItem getMillChargeItem() {
		return millChargeItem;
	}

	public void setMillChargeItem(SubChargeItem millChargeItem) {
		this.millChargeItem = millChargeItem;
	}

	public String getChargeTimePoint() {
		return chargeTimePoint;
	}

	public void setChargeTimePoint(String chargeTimePoint) {
		this.chargeTimePoint = chargeTimePoint;
	}

	public BigDecimal getResult() {
		return result;
	}

	public void setResult(BigDecimal result) {
		this.result = result;
	}

	public BigDecimal getTimeResult() {
		return timeResult;
	}

	public void setTimeResult(BigDecimal timeResult) {
		this.timeResult = timeResult;
	}

	public BigDecimal getMillResult() {
		return millResult;
	}

	public void setMillResult(BigDecimal millResult) {
		this.millResult = millResult;
	}

	public void clearPrice(){
		if(millChargeItem != null){
			millChargeItem.clearPrice();
		}
		if(timeChargeItem != null){
			timeChargeItem.clearPrice();
		}
		
	}
	
	public void clearResult(){
		this.setResult(null);
		if(millChargeItem != null){
			millChargeItem.clearResult();
		}
		if(timeChargeItem != null){
			timeChargeItem.clearResult();
		}
	}
	
}
