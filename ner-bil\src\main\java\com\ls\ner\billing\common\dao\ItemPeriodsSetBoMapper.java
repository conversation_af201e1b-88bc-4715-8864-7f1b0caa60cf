package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.ItemPeriodsSetBo;
import com.ls.ner.billing.common.bo.ItemPeriodsSetBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ItemPeriodsSetBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(ItemPeriodsSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(ItemPeriodsSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(ItemPeriodsSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(ItemPeriodsSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<ItemPeriodsSetBo> selectByExample(ItemPeriodsSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    ItemPeriodsSetBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") ItemPeriodsSetBo record, @Param("example") ItemPeriodsSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") ItemPeriodsSetBo record, @Param("example") ItemPeriodsSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(ItemPeriodsSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(ItemPeriodsSetBo record);
}