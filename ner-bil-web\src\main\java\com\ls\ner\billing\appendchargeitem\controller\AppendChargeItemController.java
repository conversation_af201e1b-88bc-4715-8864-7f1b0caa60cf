package com.ls.ner.billing.appendchargeitem.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.api.BillingConstants.BuyType;
import com.ls.ner.billing.api.BillingConstants.ItemStatus;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.appendchargeitem.service.IAppendChargeItemService;
import com.ls.ner.billing.common.bo.AppendChargeItemBo;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.webcommon.rest.annotation.IdRequestBody;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.IDRequestObject;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

@Controller
@RequestMapping("/billing/appendchargeitem")
public class AppendChargeItemController extends QueryController<AppendChargeItemQueryCondition> {
	private static final String VIEW_BASE = "/bil/appendchargeitem";
	private static final String VIEW_INIT_ATTACH = VIEW_BASE+"/attachChargeItem";
	private static final String VIEW_INIT_DEPOSIT = VIEW_BASE+"/depositChargeItem";
	
	@Autowired
	private ControllerSupport controllerSupport;

	
	@ServiceAutowired("appendChargeItemService")
	private IAppendChargeItemService appendChargeItemService;

	
	@Override
	protected AppendChargeItemQueryCondition initCondition() {

		return new AppendChargeItemQueryCondition();
	}
	@RequestMapping(value="/attachs/init",method = RequestMethod.GET)
	public String initAttach(Model model) {
		controllerSupport.addStCodes(model, PriceUnit.CODE_TYPE);
		controllerSupport.addStCodes(model, BuyType.CODE_TYPE);
		model.addAttribute("appendChargeItemForm", new AppendChargeItemBo());
		return VIEW_INIT_ATTACH;
	}
	@RequestMapping(value="/attachs")
	public @ItemResponseBody QueryResultObject queryAttachItems(@QueryRequestParam("params")
	RequestCondition params) {
//		List<AppendChargeItemBo> list = Lists.newArrayList();
//		list.add(new AppendChargeItemBo());
//		return RestUtils.wrappQueryResult(list);
		AppendChargeItemQueryCondition con = this.rCondition2QCondition(params);
		AppendChargeItemBo bo = new AppendChargeItemBo();
		bo.setItemStatus(con.getItemStatus());
		List<AppendChargeItemBo> list = appendChargeItemService.listAttachChargeItem(bo);
//		utilCacheTemplates.cached(new CacheTemplates.Fetcher<List<AppendChargeItemBo>>(){
//
//			@Override
//			public List<AppendChargeItemBo> fetch() {
//				
//				return list;
//			}
//			
//		}, "AppendChargeItemBo", 30);
		return RestUtils.wrappQueryResult(list);
	}
	@RequestMapping(value="/attachs/post",method = RequestMethod.POST)
	public @ResponseBody WrappedResult createAttachItem(@RequestBody Map<String, Object> dataParams) {
		
		AppendChargeItemBo appendChargeItemBo = getBean(dataParams);
		
		return WrappedResult.successWrapedResult(appendChargeItemService.createAttachChargeItem(appendChargeItemBo));
	}
	protected AppendChargeItemBo getBean(Map<String, Object> dataParams) {
		AppendChargeItemBo appendChargeItemBo = new AppendChargeItemBo();
		controllerSupport.populateSubmitParams(dataParams, appendChargeItemBo);
		
		return appendChargeItemBo;
	}
	@RequestMapping(value="/attachs/post/{itemNo}",method = RequestMethod.POST)
	public @ResponseBody WrappedResult updateAttachChargeItem(@PathVariable("itemNo") String itemNo,@RequestBody Map<String, Object> dataParams) {
		AppendChargeItemBo appendChargeItemBo = getBean(dataParams);
		appendChargeItemBo.setItemNo(itemNo);
		
		appendChargeItemService.updateAttachChargeItem(appendChargeItemBo);
		return WrappedResult.successWrapedResult(itemNo);
	}
	@RequestMapping(value="/attachs/delete/{itemNo}",method = RequestMethod.POST)
	public @ResponseBody WrappedResult deleteAttachChargeItem(@PathVariable("itemNo") String itemNo) {
		appendChargeItemService.deleteAttachChargeItem(itemNo);
		return WrappedResult.successWrapedResult(itemNo);
	}
	
	@RequestMapping(value="/deposits/init",method = RequestMethod.GET)
	public String initDeposit(Model model) {
		controllerSupport.addStCodes(model, PriceUnit.CODE_TYPE);
		model.addAttribute("appendChargeItemForm", new AppendChargeItemBo());
		return VIEW_INIT_DEPOSIT;
	}
	@RequestMapping(value="/deposits")
	public @ItemResponseBody QueryResultObject queryDepositItems(@QueryRequestParam("params")
	RequestCondition params) {
//		List<AppendChargeItemBo> list = Lists.newArrayList();
//		list.add(new AppendChargeItemBo());
//		return RestUtils.wrappQueryResult(list);
		AppendChargeItemQueryCondition con = this.rCondition2QCondition(params);
		AppendChargeItemBo bo = new AppendChargeItemBo();
		bo.setItemStatus(con.getItemStatus());
		List<AppendChargeItemBo> list = appendChargeItemService.listDepositChargeItem(bo);
		return RestUtils.wrappQueryResult(list);
	}
	@RequestMapping(value="/deposits/post",method = RequestMethod.POST)
	public @ResponseBody WrappedResult createDepositItem(@RequestBody Map<String, Object> dataParams) {
		
		AppendChargeItemBo appendChargeItemBo = getBean(dataParams);
		
		return WrappedResult.successWrapedResult(appendChargeItemService.createDepositChargeItem(appendChargeItemBo));
	}

	@RequestMapping(value="/deposits/post/{itemNo}",method = RequestMethod.POST)
	public @ResponseBody WrappedResult updateDepositChargeItem(@PathVariable("itemNo") String itemNo,@RequestBody Map<String, Object> dataParams) {
		AppendChargeItemBo appendChargeItemBo = getBean(dataParams);
		appendChargeItemBo.setItemNo(itemNo);
		
		appendChargeItemService.updateDepositChargeItem(appendChargeItemBo);
		return WrappedResult.successWrapedResult(itemNo);
	}
	@RequestMapping(value="/deposits/delete/{itemNo}",method = RequestMethod.POST)
	public @ResponseBody WrappedResult deleteDepositChargeItem(@PathVariable("itemNo") String itemNo) {
		appendChargeItemService.deleteDepositChargeItem(itemNo);
		return WrappedResult.successWrapedResult(itemNo);
	}
	/**
	 * 
	 * 方法说明：费用项批量启用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月15日 下午12:23:57
	 * History:  2016年4月15日 下午12:23:57   lipf   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/start")
	public @ItemResponseBody
	QueryResultObject start(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		List<AppendChargeItemBo> list = new ArrayList<AppendChargeItemBo>();
		for (String string : ids) {
			AppendChargeItemBo bo = new AppendChargeItemBo();
			bo.setItemNo(string);
			bo.setItemStatus(ItemStatus.ENABLE);
			list.add(bo);
		}
		appendChargeItemService.updateItems(list);
		return RestUtils.wrappQueryResult("Y");
	}
	/**
	 * 
	 * 方法说明：费用项批量停用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月15日 下午12:25:57
	 * History:  2016年4月15日 下午12:25:57   lipf   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/stop")
	public @ItemResponseBody
	QueryResultObject stop(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		List<AppendChargeItemBo> list = new ArrayList<AppendChargeItemBo>();
		for (String string : ids) {
			AppendChargeItemBo bo = new AppendChargeItemBo();
			bo.setItemNo(string);
			bo.setItemStatus(ItemStatus.DISABLE);
			list.add(bo);
		}
		appendChargeItemService.updateItems(list);
		return RestUtils.wrappQueryResult("Y");
	}

}
