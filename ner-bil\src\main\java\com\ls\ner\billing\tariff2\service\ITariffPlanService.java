package com.ls.ner.billing.tariff2.service;

import java.util.List;

import com.ls.ner.billing.common.bo.TariffPlanBo;

public interface ITariffPlanService {

	public TariffPlanBo getByPlanNo(String planNo);
	
	public List<TariffPlanBo> getTariffPlanList(TariffPlanBo bo);
	
	public String create(TariffPlanBo bo);
	
	public void update(TariffPlanBo bo);

	/**
	 * 
	 * 方法说明：资费标准批量删除
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:27:29
	 * History:  2016年4月11日 下午9:27:29   lipf   Created.
	 *
	 * @param ids
	 *
	 */
	public void deletePlans(String[] ids);

	/**
	 * 
	 * 方法说明：资费标准启用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:27:15
	 * History:  2016年4月11日 下午9:27:15   lipf   Created.
	 *
	 * @param ids
	 *
	 */
	public void startTariff(String[] ids);

	/**
	 * 
	 * 方法说明：资费标准启用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:27:25
	 * History:  2016年4月11日 下午9:27:25   lipf   Created.
	 *
	 * @param ids
	 *
	 */
	public void stopTariff(String[] ids);

	/**
	 * 
	 * 方法说明：复制资费模版
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:45:42
	 * History:  2016年4月11日 下午9:45:42   lipf   Created.
	 *
	 * @param bo
	 *
	 */
	public void copyTariff(TariffPlanBo bo);
}
