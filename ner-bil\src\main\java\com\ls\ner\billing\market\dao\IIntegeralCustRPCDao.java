package com.ls.ner.billing.market.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.market.bo.IntegeralCustBo;
import com.ls.ner.billing.market.bo.IntegeralCustLogBo;

/**
 * 账号积分管理
 * <AUTHOR>
 *
 */
public interface IIntegeralCustRPCDao {
	
	/**
	 * 判断用户是否已有积分
	 * @param bo
	 * @return
	 */
	int ixExistsCustNum(IntegeralCustBo bo);
	
	/**
	 * 获取用户已有积分
	 * @param bo
	 * @return
	 */
	String getCustIntegeralNum(IntegeralCustBo bo);
	
	/**
	 * 添加用户积分
	 * @param bo
	 * @return
	 */
	void addIntegeralCust(IntegeralCustBo bo);
	
	/**
	 * 修改用户积分
	 * @param bo
	 * @return
	 */
	void updateIntegeralCust(IntegeralCustBo bo);
	
	/**
	 * 添加用户积分流水记录
	 * @param logBo
	 */
	void addIntegeralCustLog(IntegeralCustLogBo logBo);
	
	/**
	 * 获取用户指定事件获取积分次数
	 * @param logBo
	 * @return
	 */
	int getLogNum(IntegeralCustLogBo logBo);
	
	/**
	 * 获取用户积分流水记录列表数
	 * @param logBo
	 * @return
	 */
	int getIntegeralCustLogCount(IntegeralCustLogBo logBo);
	
	/**
	 * 获取用户积分流水记录列表
	 * @param logBo
	 * @return
	 */
	List<IntegeralCustLogBo> getIntegeralCustLogList(IntegeralCustLogBo logBo);

	List<IntegeralCustLogBo> getIntegeralCustLogListByWX(IntegeralCustLogBo logBo);


	/**
	 * 批量更新用户积分
	 * @param inMap
	 */
	void updateIntegeralCustByTask(Map<String, Object> inMap);
	
	/**
	 * 获取所有用户积分
	 * @return
	 */
	List<IntegeralCustBo> getIntegeralCustList();

	/**
	 * 获取指定用户积分
	 * @return
	 */
	List<IntegeralCustBo> getCustIntegralNumber(Map<String, Object> inMap);

	/**
	 * 获取指定用户积分
	 * @return
	 */
	String getCustIntegralNumberById(String custId);

}
