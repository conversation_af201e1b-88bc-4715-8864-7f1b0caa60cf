package com.ls.ner.billing.market.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.integral.IntegralRuleDTO;
import org.apache.ibatis.annotations.Param;

import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.bo.IntegeralRuleUserBo;

/**
 * 积分规则管理DAO
 * <AUTHOR>
 *
 */
public interface IIntegeralRuleDao {

	/**
	 * 创建积分规则
	 * @param integeralRuleBo
	 * @return
	 */
	String addRule(IntegeralRuleBo integeralRuleBo);

	/**
	 * 创建积分规则用户关联关系
	 * @param ruleUserList
	 * @return
	 */
	void addRuleUser(@Param("dataList") List<IntegeralRuleUserBo> ruleUserList);

	/**
	 * 获取积分规则列表
	 * @param integeralRuleBo
	 * @return
	 */
	List<IntegeralRuleBo> queryRuleList(IntegeralRuleBo integeralRuleBo);

	/**
	 * 获取积分规则列表总数
	 * @param integeralRuleBo
	 * @return
	 */
	int queryRuleListCount(IntegeralRuleBo integeralRuleBo);

	/**
	 * 规则状态变更（启用/停用）
	 * @param integeralRuleBo
	 */
	void updateRuleStatus(IntegeralRuleBo integeralRuleBo);

	/**
	 * 获取规则详情
	 * @return
	 */
	IntegeralRuleBo getRuleInfo(@Param("ruleId") String ruleId);

	/**
	 * 规则变更
	 * @param integeralRuleBo
	 */
	void updateRule(IntegeralRuleBo integeralRuleBo);

	/**
	 * 规则用户关联关系变更
	 * @param integeralRuleBo
	 */
	void updateRuleUser(IntegeralRuleUserBo bo);

	/**
	 * 判断是否存在规则
	 * @param bo
	 * @return
	 */
	int isExistsRule(IntegeralRuleBo bo);

	/**
	 * 判断是否存在规则用户关联关系
	 * @param integeralRuleBo
	 */
	int isExistsRuleUser(IntegeralRuleUserBo bo);

	void deleteRuleUser(IntegeralRuleUserBo bo);

	List<Map<String,Object>> getRuleInfoByUser(IntegeralRuleBo integeralRuleBo);

	List<Map<String, Object>> queryIntegralRule();

	List<IntegeralRuleBo> getIntegralRule2();

}
