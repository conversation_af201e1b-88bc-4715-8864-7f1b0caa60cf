<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="桩电价下发历史" />
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="充电计费配置信息"></ls:title>
		<ls:text type="hidden" name="pileNo" property="pileId"/>
		<table class="tab_search">
			<tr>
				<td width="100px;"><ls:label text="充电计费编号" ref="chcNo" /></td>
				<td colspan="2"><ls:text name="chcNo" property="chcNo" readOnly="true"/></td>
				<td><ls:label text="充电计费名称" ref="chcName" /></td>
				<td colspan="2"><ls:text name="chcName" property="chcName" readOnly="true"/></td>
				<td><ls:label text="计费配置状态" ref="chcStatus" /></td>
				<td colspan="2"><ls:select name="chcStatus" property="chcStatus" readOnly="true">
					<ls:options property="validFlagList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
			</tr>
		</table>
	</ls:form>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="logGrid" height="200px;" width="100%" showCheckBox="false" singleSelect="true" caption="桩电价下发历史">
					<ls:gridToolbar name="detailGridBar">
						<ls:gridToolbarItem imageKey="back" onclick="doReturn" text="返回"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="充电桩编号" name="pileNo" />
					<ls:column caption="充电桩名称" name="pileName" />
					<ls:column caption="下发时间" name="sendTime" format="{sendTime,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="下发失败原因" name="failReason" />
					<ls:column caption="下发状态" name="pileSendStatusName" />
					<ls:pager pageSize="15,20,25" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	<ls:script>
	window.logGrid = logGrid;
    doSearch();
    window.doSearch = doSearch;
    
	function doSearch(){
		var params = searchForm.getFormData();
		logGrid.query('~/billing/station-bill-send/queryChcbillSendLog',params,function(){});
	}
	
 	function doReturn(){
 		LS.window.close();
 	}
    </ls:script>
</ls:body>
</html>