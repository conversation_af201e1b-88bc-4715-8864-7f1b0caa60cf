package com.ls.ner.billing.task.controller;/**
 * Created by mingmingh on 2018/3/7.
 */

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.xpcharge.service.IXpChargeBillingConfService;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @DESCRIPTION
 * @create 2018-03-07 14:40
 **/
@Controller
@RequestMapping("/task/billing")
public class ChargeBillingTaskController {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBillingTaskController.class);

    @ServiceAutowired("xpChargeBillingConfService")
    private IXpChargeBillingConfService xpChargeBillingConfService;

    @ServiceAutowired("peakService")
    private IXpPeakService peakService;

    @ServiceAutowired(serviceTypes= ServiceType.RPC)
    private ISysParamService sysParamService;



    /**
     * @param
     * @description 计费生效任务调度
     * <AUTHOR>
     * @create 2020-10-12 11:06:10
     */
    @RequestMapping("/excuteBillingTask")
    public @ResponseBody Object excuteBillingTask() {
        logger.info("开始执行计费生效任务调度");
        try {
            xpChargeBillingConfService.excuteBillingTask();
            return "0";
        } catch (Exception e) {
            logger.error("执行计费生效任务调度失败", e);
            return "-1";
        }
    }

    /**
     * @param
     * @description 计费下发任务调度
     * <AUTHOR>
     * @create 2020-10-12 11:06:10
     */
    @RequestMapping("/excuteBillingSendTask")
    public @ResponseBody Object excuteBillingSendTask(String stationId,String pileNo) {
        logger.info("开始执行计费下发任务调度");
        try {
            String billSendFlag = sysParamService.getSysParamsValues("billSendFlag");
            if (StringUtils.isNotBlank(billSendFlag)) {
                logger.info("新版计费下发任务");
                xpChargeBillingConfService.executeBillSendTask();
            } else {
                logger.info("原版计费下发任务");
                xpChargeBillingConfService.excuteBillingSendTask(stationId,pileNo);
            }
            return "0";
        } catch (Exception e) {
            logger.error("执行计费下发任务调度失败", e);
            return "-1";
        }
    }

    /**
     * 计费自动下发
     * <AUTHOR>
     * @date 2024/9/29
     */
    @RequestMapping("/executeBillSend")
    public @ResponseBody Object executeBillSend() {
        logger.info("开始执行计费下发任务调度-new");
        try {
            xpChargeBillingConfService.executeBillSendTask();
            return "0";
        } catch (Exception e) {
            logger.error("执行计费下发任务调度失败-new", e);
            return "-1";
        }
    }

    /**
     * @param
     * @description 尖峰计费下发任务调度
     * <AUTHOR>
     * @create 2020-10-12 11:06:10
     */
    @RequestMapping("/excutePeakBillingSendTask")
    public @ResponseBody Object excutePeakBillingSendTask() {
        logger.info("开始执行尖峰计费下发任务调度");
        try {
            peakService.peakEffTast();
            xpChargeBillingConfService.excuteBillingSendTask(null, null);
            return "0";
        } catch (Exception e) {
            logger.error("执行尖峰计费下发任务调度失败", e);
            return "-1";
        }
    }

}
