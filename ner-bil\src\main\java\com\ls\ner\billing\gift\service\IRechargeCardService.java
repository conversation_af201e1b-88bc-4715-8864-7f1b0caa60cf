package com.ls.ner.billing.gift.service;


import com.ls.ner.billing.gift.bo.RechargeCardBo;
import com.ls.ner.billing.gift.bo.RechargeCardDetail;
import com.ls.ner.billing.gift.bo.RechargeCardOrder;
import com.ls.ner.billing.gift.condition.RechargeCardCondition;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.service
 * @ClassName: IRechargeCard
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/4/27 14:27
 * @Version: 1.0
 */
public interface IRechargeCardService {

    /**
     * @param
     * @description 查询领取购车券
     * <AUTHOR>
     * @create 2020-04-29 18:04:24
     */
    List<RechargeCardBo> listRechargeCard(RechargeCardCondition rechargeCardCondition);


    /**
     * @param
     * @description  数量查询
     * <AUTHOR>
     * @create 2020-04-29 18:32:27
     */
    int countRechargeCard(RechargeCardCondition rechargeCardCondition);

    /**
     * 查询明细
     * @param rechargeCardCondition
     * @return
     */
    List<RechargeCardDetail> queryDetails(RechargeCardCondition rechargeCardCondition);


    int queryDetailsCount(RechargeCardCondition rechargeCardCondition);

    List<RechargeCardOrder> qryCardOrderDetail(RechargeCardCondition rechargeCardCondition);

    int queryCardOrderDetailCount(RechargeCardCondition rechargeCardCondition);

    /**
     * 新增储值卡
     * @param rechargeCardBo
     */
    void addRechargeCard(RechargeCardBo rechargeCardBo);


    /**
     * 更新储值卡
     * @param rechargeCardBo
     */
    void updateRechargeCard(RechargeCardBo rechargeCardBo);


    /**
     * 增加卡量
     * @param rechargeCardBo
     */
    void addBatchNoCard(RechargeCardBo rechargeCardBo);

    /**
     * 详情
     * @param rechargeCardBo
     */
    RechargeCardBo queryDetail(RechargeCardBo rechargeCardBo);

    /**
     * 更新充值卡状态
     * @param rechargeCardBo
     */
    void updateCardStatus(RechargeCardBo rechargeCardBo);

    /**
     * 更新明细充值卡状态
     * @param rechargeCardDetail
     */
    void updateCardDetailStatus(RechargeCardDetail rechargeCardDetail);

    /**
     * 卡密兑换
     * @param map
     * @return
     */
    Map<String,Object> rechargeCardCode(Map<String,String> map);

    /**
     * 积分兑换
     * @param map
     * @return
     */
    Map<String,Object> rechargeCardCodeByIntegral(Map<String,String> map);

    /**
     * 我的充值卡
     * @param map
     * @return
     */
    Map<String,Object> myRechargeCardInfos(Map<String,String> map);

    /**
     * 查询充值卡list
     * @return
     */
    public List<Map<String, String>> getRechargeCardList();

    /**
     * 根据订单结算传的信息更新对应充值卡
     */
    public void updateRechargeCardByOrder(Map<String, String> map) throws Exception;

    /**
     * 充值卡明细跳转订单列表
     * @param cardId
     * @return
     */
    public List<String> cardOrderNoList(String cardId, String cardDetailId);

    /**
     * 开票的时候 排除部分使用卡密兑换充值卡的充电订单的金额
     * @param map
     * @return
     */
    public List<Map<String, Object>> queryCardOrderByOrderNo(Map<String, Object> map);
    /**
     * 用于给充电订单列表展示相关充值卡卡号 充值卡实际抵扣金额 相关充值卡卡号 字段使用
     */
    List<Map<String,Object>> queryCardInfosByChargeOrderList(Map<String, Object> map);

    /**
     * 通过充值卡卡号查询对应充电订单
     * @param map
     * @return
     */
    List<String> getOrderListByCardNo(Map<String, Object> map);
}
