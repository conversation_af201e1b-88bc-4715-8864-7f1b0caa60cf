<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
	http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ls.ner.bil</groupId>
		<artifactId>ner-bil-boot</artifactId>
		<version>0.0.1</version>
	</parent>

	<artifactId>ner-bil</artifactId>
	<name>ner-bil</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	
	<dependencies>
		<dependency>
			<groupId>com.ls.ner</groupId>
			<artifactId>ner-base</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ls.ner.pub</groupId>
			<artifactId>ner-pub-api</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ls.ner.bil</groupId>
			<artifactId>ner-bil-api</artifactId>
    		<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ls.ner.ast</groupId>
			<artifactId>ner-ast-api</artifactId>
			<version>0.0.1</version>
		</dependency>
        <dependency>
            <groupId>com.ls.ner.cust</groupId>
            <artifactId>ner-cust-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ls.ner.def</groupId>
            <artifactId>ner-def-api</artifactId>
            <version>0.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ls.ner.ord</groupId>
            <artifactId>ner-ord-api</artifactId>
            <version>0.0.1</version>
        </dependency>
		<dependency>
			<groupId>com.ls.ner.aly</groupId>
			<artifactId>ner-aly-api</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

	<build>
        <plugins>
            <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
                    <!-- 跳过单元测试 -->
					<skipTests>true</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
        </plugins>
    </build>
</project>
