package com.ls.ner.billing.vip.dao;

import com.ls.ner.billing.api.vip.bo.VipExclusivePutRPCBo;
import com.ls.ner.billing.vip.bo.VipExclusivePutBo;
import com.ls.ner.billing.vip.bo.VipSaveRecordBo;

import java.util.List;

/**
 * @description IVipExclusivePutDao
 */
public interface IVipExclusivePutDao {
    List<VipExclusivePutBo> getExclusiveByPutDay(String putDay);

    void insert(VipExclusivePutBo bo);

    void update(VipExclusivePutBo bo);

    VipExclusivePutRPCBo getExclusivePut(VipExclusivePutBo bo);

}
