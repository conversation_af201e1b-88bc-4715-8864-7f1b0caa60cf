<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.vip.dao.IMemberBenefitsDao">

    <sql id="Base_Column_List">
        vip_id vipId, vip_type vipType, vip_level vipLevel, create_time createTime, update_time updateTime,
        is_exchange isExchange, is_double isDouble, is_exclusive_offer isExclusiveOffer, is_exclusive_customer isExclusiveCustomer,
        is_upgrade_package isUpgradePackage, is_member_station isMemberStation, is_welcome_gift isWelcomeGift,member_day memberDay,
        is_member_day_additional_discount isMemberDayAdditionalDiscount,multiple,exclusive_offer exclusiveOffer,
        upgrade_package upgradePackage,member_station memberStation,welcome_gift welcomeGift,member_day_additional_discount memberDayAdditionalDiscount,
        is_exchange_explain isExchangeExplain,is_double_explain isDoubleExplain,is_exclusive_customer_explain isExclusiveCustomerExplain,
        is_upgrade_package_explain isUpgradePackageExplain,is_member_station_explain isMemberStationExplain,is_member_day_additional_discount_explain isMemberDayAdditionalDiscountExplain,
        month_degree_config monthDegreeConfig,charge_degree1 chargeDegree1,charge_degree2 chargeDegree2,
        charge_degree3 chargeDegree3, charge_degree4 chargeDegree4, charge_degree5 chargeDegree5,limit_num limitNum,limit_amt limitAmt,discount_limit discountLimit
    </sql>


    <select id="list" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        SELECT vip_id    vipId,
               vip_type  vipType,
               vip_level vipLevel,
               (
                       CASE WHEN is_exchange = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_double = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_exclusive_offer = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_exclusive_customer = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_upgrade_package = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_member_station = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_welcome_gift = '1' THEN 1 ELSE 0 END +
                       CASE WHEN is_member_day_additional_discount = '1' THEN 1 ELSE 0 END
                   ) AS  benefitsCount
        FROM e_vip_benefits
    </select>

    <select id="configDesc" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo" parameterType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        select
        <include refid="Base_Column_List"/>
        from e_vip_benefits
        <where>
            vip_type = '02' and vip_level = #{vipLevel}
        </where>
    </select>

    <select id="getVipBenefits" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo"
            parameterType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        select
        <include refid="Base_Column_List"/>
        from e_vip_benefits
        <where>
            <if test="isExclusiveOffer != null and isExclusiveOffer != null">
                AND is_exclusive_offer = #{isExclusiveOffer}
            </if>
        </where>
    </select>


    <select id="count" resultType="int">
        SELECT count(1)
        FROM e_vip_benefits
    </select>

    <update id="updateEVipBenefits" parameterType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        UPDATE e_vip_benefits
        <set>
            <if test="vipType != null and vipType != ''">
                vip_type = #{vipType,jdbcType=VARCHAR},
            </if>
            <if test="vipLevel != null and vipLevel != ''">
                vip_level = #{vipLevel,jdbcType=VARCHAR},
            </if>
            <if test="isExchange != null and isExchange != ''">
                is_exchange = #{isExchange,jdbcType=VARCHAR},
            </if>
            <if test="isDouble != null and isDouble != ''">
                is_double = #{isDouble,jdbcType=VARCHAR},
            </if>
            <if test="isExclusiveOffer != null and isExclusiveOffer != ''">
                is_exclusive_offer = #{isExclusiveOffer,jdbcType=VARCHAR},
            </if>
            <if test="isExclusiveCustomer != null and isExclusiveCustomer != ''">
                is_exclusive_customer = #{isExclusiveCustomer,jdbcType=VARCHAR},
            </if>
            <if test="isUpgradePackage != null and isUpgradePackage != ''">
                is_upgrade_package = #{isUpgradePackage,jdbcType=VARCHAR},
            </if>
            <if test="isMemberStation != null and isMemberStation != ''">
                is_member_station = #{isMemberStation,jdbcType=VARCHAR},
            </if>
            <if test="isWelcomeGift != null and isWelcomeGift != ''">
                is_welcome_gift = #{isWelcomeGift,jdbcType=VARCHAR},
            </if>
            <if test="isMemberDayAdditionalDiscount != null and isMemberDayAdditionalDiscount != ''">
                is_member_day_additional_discount = #{isMemberDayAdditionalDiscount,jdbcType=VARCHAR},
            </if>
            <if test="multiple != null">
                multiple = #{multiple,jdbcType=DECIMAL},
            </if>
            <if test="exclusiveOffer != null and exclusiveOffer != ''">
                exclusive_offer = #{exclusiveOffer,jdbcType=VARCHAR},
            </if>
            <if test="upgradePackage != null">
                upgrade_package = #{upgradePackage,jdbcType=INTEGER},
            </if>
            <if test="memberStation != null">
                member_station = #{memberStation,jdbcType=DECIMAL},
            </if>
            <if test="memberDay != null">
                member_day = #{memberDay,jdbcType=VARCHAR},
            </if>
            <if test="welcomeGift != null and welcomeGift != ''">
                welcome_gift = #{welcomeGift,jdbcType=VARCHAR},
            </if>
            <if test="memberDayAdditionalDiscount">
                member_day_additional_discount = #{memberDayAdditionalDiscount,jdbcType=DECIMAL},
            </if>
            <if test="isExchangeExplain != null">
                is_exchange_explain = #{isExchangeExplain},
            </if>
            <if test="isDoubleExplain != null">
                is_double_explain = #{isDoubleExplain},
            </if>
            <if test="isExclusiveCustomerExplain != null">
                is_exclusive_customer_explain = #{isExclusiveCustomerExplain},
            </if>
            <if test="isUpgradePackageExplain != null">
                is_upgrade_package_explain = #{isUpgradePackageExplain},
            </if>
            <if test="isMemberStationExplain != null">
                is_member_station_explain = #{isMemberStationExplain},
            </if>
            <if test="isMemberDayAdditionalDiscountExplain != null">
                is_member_day_additional_discount_explain = #{isMemberDayAdditionalDiscountExplain},
            </if>
            <if test="monthDegreeConfig != null">
                month_degree_config = #{monthDegreeConfig},
            </if>
            <if test="chargeDegree1 != null">
                charge_degree1 = #{chargeDegree1},
            </if>
            <if test="chargeDegree2 != null">
                charge_degree2 = #{chargeDegree2},
            </if>
            <if test="chargeDegree3 != null">
                charge_degree3 = #{chargeDegree3},
            </if>
            <if test="chargeDegree4 != null">
                charge_degree4 = #{chargeDegree4},
            </if>
            <if test="chargeDegree5 != null">
                charge_degree5 = #{chargeDegree5},
            </if>
            <if test="discountLimit != null and discountLimit != ''">
                discount_limit = #{discountLimit},
            </if>
            <if test="limitNum != null">
                limit_num = #{limitNum},
            </if>
            <if test="limitAmt != null">
                limit_amt = #{limitAmt},
            </if>

            update_time = now()
        </set>
        WHERE vip_id = #{vipId,jdbcType=VARCHAR}
    </update>

    <select id="getMemberBenefits" parameterType="com.ls.ner.billing.vip.bo.MemberBenefitsBo"
            resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        select
        <include refid="Base_Column_List"/>
        from e_vip_benefits
        <where>
            <if test="vipId != null and vipId != ''">
                AND vip_id = #{vipId}
            </if>
            <if test="vipType != null and vipType != ''">
                AND vip_type = #{vipType}
            </if>
            <if test="vipLevel != null and vipLevel != ''">
                AND vip_level = #{vipLevel}
            </if>
        </where>
    </select>

    <select id="getUserMemberBenefits" parameterType="string" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        select
        <include refid="Base_Column_List"/>
        from e_vip_benefits b left join e_regular_vip_rank_config c ON b.vip_level = c.rank_no
        <where>
            #{chargePq} &gt;= rank_min AND #{chargePq} &lt; rank_max
        </where>
    </select>
    <select id="getUserMemberBenefitsByAmt" parameterType="string" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        select
        <include refid="Base_Column_List"/>
        from e_vip_benefits b left join e_regular_vip_rank_config c ON b.vip_level = c.rank_no
        <where>
            #{chargeAmt} &gt;= rank_amt_min AND #{chargeAmt} &lt; rank_amt_max
        </where>
    </select>

    <select id="selectRegularVipConfigList" resultType="com.ls.ner.billing.vip.bo.RegularVipRankBo">
        select rank_id rankId, rank_name rankName, rank_no rankNo, rank_min rankMin, rank_max rankMax ,rank_amt_min rankAmtMin, rank_amt_max rankAmtMax, duration_days durationDays
        from e_regular_vip_rank_config
        order by rank_no
    </select>

    <select id="getRegularVipConfigById" resultType="com.ls.ner.billing.vip.bo.RegularVipRankBo">
        select rank_id rankId, rank_name rankName, rank_no rankNo, rank_min rankMin, rank_max rankMax ,rank_amt_min rankAmtMin, rank_amt_max rankAmtMax, duration_days durationDays
        from e_regular_vip_rank_config
        where rank_id = #{rankId}
    </select>

    <update id="editRegularVipConfig">
        update e_regular_vip_rank_config
        set rank_name = #{rankName},
            rank_min  = #{rankMin},
            rank_max  = #{rankMax},
            rank_amt_min = #{rankAmtMin},
            rank_amt_max = #{rankAmtMax},
            duration_days = #{durationDays}
        where rank_no = #{rankNo}
    </update>

    <select id="getVipConfigById" resultType="com.ls.ner.billing.vip.bo.VipBo">
        select vip_id        vipId,
               vip_type      vipType,
               vip_price     vipPrice,
               vip_price_new vipPriceNew,
               vip_price_old vipPriceOld,
               vip_month_desc vipMonthDesc,
               status,
               create_time   createTime,
               update_time   updateTime
        from e_vip_manage
        where vip_id = #{vipId}
    </select>

    <select id="getVipConfigList" resultType="com.ls.ner.billing.vip.bo.VipBo">
        select vip_id        vipId,
               vip_type      vipType,
               vip_price     vipPrice,
               vip_price_new vipPriceNew,
               vip_price_old vipPriceOld,
               vip_month_desc vipMonthDesc,
               status,
               create_time   createTime,
               update_time   updateTime
        from e_vip_manage
        where status = '1'
    </select>

    <select id="getMonthVipConfig" resultType="com.ls.ner.billing.vip.bo.VipBo">
        select vip_id        vipId,
               vip_type      vipType,
               vip_price     vipPrice,
               vip_price_new vipPriceNew,
               vip_price_old vipPriceOld,
               status,
               create_time   createTime,
               update_time   updateTime
        from e_vip_manage
        where status = '1' and vip_type = '01'  limit 1
    </select>

    <insert id="insertVipConfig">
        insert into e_vip_manage(vip_id, vip_type, vip_price, vip_price_new, vip_price_old,vip_month_desc, status)
        values (#{vipId}, #{vipType}, #{vipPrice}, #{vipPriceNew}, #{vipPriceOld}, #{vipMonthDesc},#{status})
    </insert>

    <update id="updateVipConfig">
        update e_vip_manage
        set vip_price     = #{vipPrice},
            vip_price_new = #{vipPriceNew},
            vip_month_desc = #{vipMonthDesc},
            vip_price_old = #{vipPriceOld}
        where vip_id = #{vipId}
    </update>

    <update id="updateVipConfigStatus">
        update e_vip_manage
        set status = #{status}
        where vip_id = #{vipId}
    </update>

    <select id="getRecordList" resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo">
        select record_id recordId,
        mobile,
        vip_type vipType,
        pay_type payType,
        amount,
        integral,
        pay_channel payChannel,
        pay_time payTime,
        date_format(pay_time,'%Y-%m-%d %H:%i:%s') payTimeStr,
        effect_time effectTime,
        date_format(effect_time,'%Y-%m-%d %H:%i:%s') effectTimeStr,
        expire_time expireTime,
        date_format(expire_time,'%Y-%m-%d %H:%i:%s') expireTimeStr
        from e_vip_pay_record
        <where>
            <include refid="queryVipPayRecordWhere"></include>
        </where>
        order by pay_time desc
    </select>

    <select id="qryEffectTime" resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo">
        select record_id recordId,
        mobile,
        vip_type vipType,
        pay_type payType,
        amount,
        integral,
        pay_channel payChannel,
        pay_time payTime,
        date_format(pay_time,'%Y-%m-%d') payTimeStr,
        effect_time effectTime,
        date_format(effect_time,'%Y-%m-%d') effectTimeStr,
        expire_time expireTime,
        date_format(expire_time,'%Y-%m-%d') expireTimeStr
        from e_vip_pay_record
        <where>
            <include refid="queryVipPayRecordWhere"></include>
        </where>
        order by effect_time limit 1
    </select>

    <select id="qryVipTime" resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo">
        select record_id, -- 假设有一个唯一标识符列
        effect_time,
        expire_time,
        DATEDIFF(expire_time, effect_time) AS intervalDays
        from e_vip_pay_record
        <where>
            <include refid="queryVipPayRecordWhere"></include>
        </where>
    </select>

    <select id="getRecordListByMobile" parameterType="java.util.Map"
            resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo">
        select record_id recordId,
        mobile,
        vip_type vipType,
        pay_type payType,
        ifnull(amount, 0) amount,
        integral,
        pay_channel payChannel,
        pay_time payTime,
        date_format(pay_time,'%Y-%m-%d %H:%i:%s') payTimeStr,
        effect_time effectTime,
        date_format(effect_time,'%Y-%m-%d %H:%i:%s') effectTimeStr,
        expire_time expireTime,
        date_format(expire_time,'%Y-%m-%d %H:%i:%s') expireTimeStr
        from e_vip_pay_record where mobile = #{mobile} and effect_time is not null
        order by pay_time desc
        <if test="totalNum != 0">
            limit #{start},#{totalNum}
        </if>
    </select>

    <select id="getRecordListCount" resultType="java.lang.Integer">
        select count(1)
        from e_vip_pay_record
        <where>
            <include refid="queryVipPayRecordWhere"></include>
        </where>
    </select>

    <select id="getRegularVipLevel" resultType="com.ls.ner.billing.vip.bo.RegularVipRankBo">
        select rank_id rankId,
        rank_name rankName,
        rank_no rankNo,
        rank_min rankMin,
        rank_max rankMax
        from e_regular_vip_rank_config
        <where>
            <if test="chargePq !=null">
                #{chargePq} &gt;= rank_min AND #{chargePq} &lt; rank_max
            </if>
            <if test="chargeAmt !=null">
                #{chargeAmt} &gt;= rank_amt_min AND #{chargeAmt} &lt; rank_amt_max
            </if>
        </where>
    </select>

    <sql id="queryVipPayRecordWhere">
        effect_time is not null
        <if test="recordId !=null and recordId !=''">
            and record_id =#{recordId}
        </if>
        <if test="mobile !=null and mobile !=''">
            and mobile =#{mobile}
        </if>
        <if test="vipType !=null and vipType !='' and vipType !='all'">
            and vip_type =#{vipType}
        </if>
        <if test="payType !=null and payType !='' and payType !='all'">
            and pay_type =#{payType}
        </if>
        <if test="payChannel !=null and payChannel !='' and payChannel !='all'">
            and pay_channel =#{payChannel}
        </if>
    </sql>

    <select id="getUserVipLevel" resultType="com.ls.ner.billing.vip.bo.VipLevelBo">
        select id, cust_id custId, vip_level vipLevel, charge_time chargeTime
        from e_vip_level
        <where>
            cust_id = #{custId}
        </where>
    </select>

    <select id="getVipPayRecordById" resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo">
        select record_id                                     recordId,
               mobile,
               vip_type                                      vipType,
               cust_id                                       custId,
               pay_type                                      payType,
               amount,
               integral,
               pay_channel                                   payChannel,
               pay_time                                      payTime,
               date_format(pay_time, '%Y-%m-%d %H:%i:%s')    payTimeStr,
               effect_time                                   effectTime,
               date_format(effect_time, '%Y-%m-%d %H:%i:%s') effectTimeStr,
               expire_time                                   expireTime,
               date_format(expire_time, '%Y-%m-%d %H:%i:%s') expireTimeStr
        from e_vip_pay_record
        where record_id = #{recordId}
    </select>

    <insert id="insertVipLevel" parameterType="com.ls.ner.billing.vip.bo.VipLevelBo">
        INSERT into e_vip_level (cust_id, vip_level, charge_time)
        VALUES (#{custId}, #{vipLevel}, #{chargeTime})
    </insert>

    <insert id="addRecord">
        insert into e_vip_pay_record(record_id, cust_id, mobile, vip_type, pay_type, amount,integral, pay_channel, pay_time,effect_time,expire_time)
            value (#{recordId}, #{custId}, #{mobile}, #{vipType}, #{payType}, #{amount},#{integral}, #{payChannel}, #{payTime}, #{effectTime}, #{expireTime})
    </insert>

    <update id="updateVipLevel" parameterType="com.ls.ner.billing.vip.bo.VipLevelBo">
        update e_vip_level
        set vip_level=#{vipLevel},
            charge_time=#{chargeTime}
        where cust_id = #{custId}
    </update>

    <update id="updateRecord">
        update e_vip_pay_record
        set effect_time = #{effectTime},
            expire_time = #{expireTime},
            reconciliation_no = #{reconciliationNo}
        where record_id = #{recordId}
    </update>

    <select id="getCustVipType" resultType="com.ls.ner.billing.vip.bo.VipPayRecordBo" parameterType="string">
        SELECT cust_id    custId,
               vip_type   vipType,
               mobile
        FROM e_vip_pay_record
        where cust_id = #{custId}
          and expire_time &gt; NOW()
        and  effect_time &lt; NOW()
        ORDER BY effect_time DESC LIMIT 1
    </select>

    <select id="getBenefitsUser" resultType="com.ls.ner.billing.vip.bo.MemberBenefitsBo"
            parameterType="com.ls.ner.billing.vip.bo.MemberBenefitsBo">
        SELECT l.cust_id custId,
        (CASE WHEN l.vip_level > 4 THEN 4
        ELSE l.vip_level END ) vipLevel,
        l.mobile,
        b.exclusive_offer exclusiveOffer,
        b.is_exclusive_offer isExclusiveOffer,
        b.member_station memberStation,
        b.is_member_station isMemberStation,
        b.is_exchange isExchange,
        b.is_double isDouble,
        b.multiple,
        b.is_exclusive_customer isExclusiveCustomer,
        b.is_upgrade_package isUpgradePackage,
        b.upgrade_package upgradePackage,
        b.is_welcome_gift isWelcomeGift,
        b.welcome_gift welcomeGift,
        b.member_day_additional_discount memberDayAdditionalDiscount,
        b.is_member_day_additional_discount isMemberDayAdditionalDiscount
        FROM e_vip_level l LEFT JOIN e_vip_benefits b ON l.vip_level = b.vip_level
        <where>
            <if test="isExchange != null and isExchange != ''">
                AND b.is_exchange = #{isExchange,jdbcType=VARCHAR}
            </if>
            <if test="isDouble != null and isDouble != ''">
                AND b.is_double = #{isDouble,jdbcType=VARCHAR}
            </if>
            <if test="isExclusiveOffer != null and isExclusiveOffer != ''">
                AND b.is_exclusive_offer = #{isExclusiveOffer,jdbcType=VARCHAR}
            </if>
            <if test="isExclusiveCustomer != null and isExclusiveCustomer != ''">
                AND b.is_exclusive_customer = #{isExclusiveCustomer,jdbcType=VARCHAR}
            </if>
            <if test="isUpgradePackage != null and isUpgradePackage != ''">
                AND b.is_upgrade_package = #{isUpgradePackage,jdbcType=VARCHAR}
            </if>
            <if test="isMemberStation != null and isMemberStation != ''">
                AND b.is_member_station = #{isMemberStation,jdbcType=VARCHAR}
            </if>
            <if test="isWelcomeGift != null and isWelcomeGift != ''">
                AND b.is_welcome_gift = #{isWelcomeGift,jdbcType=VARCHAR}
            </if>
            <if test="isMemberDayAdditionalDiscount != null and isMemberDayAdditionalDiscount != ''">
                AND b.is_member_day_additional_discount = #{isMemberDayAdditionalDiscount,jdbcType=VARCHAR}
            </if>
            <if test="custId != null and custId != ''">
                AND l.cust_id = #{custId}
            </if>
            <if test="mobile != null and mobile != ''">
                AND l.mobile = #{mobile}
            </if>
        </where>

    </select>

    <select id="getLevelFlag" resultType="com.ls.ner.billing.vip.bo.VipLevelBo" parameterType="string">
        SELECT cust_id    custId,
               vip_level  vipLevel,
               mobile,
               level_flag levelFlag
        FROM e_vip_level
        where cust_id = #{custId}
    </select>

    <update id="updateLevelFlag" parameterType="com.ls.ner.billing.vip.bo.VipLevelBo">
        update e_vip_level
        set level_flag=#{levelFlag}
        where cust_id = #{custId}
    </update>

    <select id="queryCpnById" resultType="com.ls.ner.billing.market.bo.CouponBo"
            parameterType="com.ls.ner.billing.market.vo.ExclusiveOfferVo">
        SELECT
        a.CPN_ID cpnId,IFNULL(a.CPN_NAME,"") cpnName,IFNULL(a.LIM_GET_NUM,"") limGetNum,IFNULL(a.CPN_TYPE,"")
        cpnType,IFNULL(a.CPN_DCT_TYPE,"") cpnDctType,IFNULL(a.CPN_AMT,"") cpnAmt,IFNULL(a.EFT_DATE,"")
        eftDate,a.CPN_TIME_TYPE cpnTimeType,IFNULL(a.MAXIMUM_DISCOUNT_AMT,"") maximumDiscountAmt,
        IFNULL(a.INV_DATE,"") invDate,IFNULL(a.CPN_MARKS,"") cpnMarks,IFNULL(a.CPN_STATE,"")
        cpnState,IFNULL(a.CPN_AMT,"") cpnSpAmt,IFNULL(a.TIME_DURATION,"") timeDuration,IFNULL(a.TIME_UNIT,"") timeUnit
        FROM e_coupon a
        WHERE a.CPN_ID IN
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item.couponId}
        </foreach>

    </select>

    <select id="getRecordListByMobileCount" resultType="java.lang.Integer">
        select count(1)
        from e_vip_pay_record
        where mobile = #{mobile}
          and effect_time is not null
    </select>

    <update id="updateInvoiceFlag" parameterType="com.ls.ner.billing.api.vip.bo.VipInvoiceUpdateBo">
        update e_vip_pay_record
        <set>
            <if test="invoiceFlag != null">
                INVOICE_FLAG = #{invoiceFlag},
            </if>
        </set>
        WHERE record_id IN
        <foreach collection="vipOrderNos" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
