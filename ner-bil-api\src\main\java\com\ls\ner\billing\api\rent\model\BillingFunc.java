package com.ls.ner.billing.api.rent.model;

import java.io.*;
import java.math.BigDecimal;
import java.util.List;

public class BillingFunc implements IChargeItem,Serializable {


    private static final long serialVersionUID = 5526779761837001264L;
    private MainChargeItem mainChargeItem;
	
//	private StandardChargeItem standardChargeItem;
//	
//	private PeriodChargeItem periodChargeItem;
//	
//	private StepChargeItem stepChargeItem;
	
	private List<DepositChargeItem> depositChargeItems;
	
	private List<AppendChargeItem> attachChargeItems;

	private BigDecimal result;
	
	private String billingNo;
	
	private String version;
	
	/**
	 * 里程数 按米记录
	 */
	private long meters;
	/**
	 * 使用时长 按分钟记录
	 */
	private long minutes;
	
	private String licenseNo ;


	public String getLicenseNo() {
		return licenseNo;
	}

	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}

//	public StandardChargeItem getStandardChargeItem() {
//		return standardChargeItem;
//	}
//
//	public void setStandardChargeItem(StandardChargeItem standardChargeItem) {
//		this.standardChargeItem = standardChargeItem;
//	}
//
//	public PeriodChargeItem getPeriodChargeItem() {
//		return periodChargeItem;
//	}
//
//	public void setPeriodChargeItem(PeriodChargeItem periodChargeItem) {
//		this.periodChargeItem = periodChargeItem;
//	}
//
//	public StepChargeItem getStepChargeItem() {
//		return stepChargeItem;
//	}
//
//	public void setStepChargeItem(StepChargeItem stepChargeItem) {
//		this.stepChargeItem = stepChargeItem;
//	}

	public List<DepositChargeItem> getDepositChargeItems() {
		return depositChargeItems;
	}

	public MainChargeItem getMainChargeItem() {
		return mainChargeItem;
	}

	public void setMainChargeItem(MainChargeItem mainChargeItem) {
		this.mainChargeItem = mainChargeItem;
	}

	public void setDepositChargeItems(List<DepositChargeItem> depositChargeItems) {
		this.depositChargeItems = depositChargeItems;
	}

	public List<AppendChargeItem> getAttachChargeItems() {
		return attachChargeItems;
	}

	public void setAttachChargeItems(List<AppendChargeItem> attachChargeItems) {
		this.attachChargeItems = attachChargeItems;
	}

	public BigDecimal getResult() {
		return result;
	}

	public void setResult(BigDecimal result) {
		this.result = result;
	}
	
	public String getBillingNo() {
		return billingNo;
	}

	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public long getMeters() {
		return meters;
	}

	public void setMeters(long meters) {
		this.meters = meters;
	}

	public long getMinutes() {
		return minutes;
	}

	public void setMinutes(long minutes) {
		this.minutes = minutes;
	}

	/**
	 * 去掉定价和结果信息，从而作为资费标准使用
	 * @return
	 */
	public BillingFunc asTariff(){
		this.clearPrice();
				this.clearResult();
				return this;
	}
	
	/**
	 * 去掉结果信息，从而作为定价方案使用
	 * @return
	 */
	public BillingFunc asBillingConfig(){
		
		this.clearResult();
		return this;
	}

	public void clearPrice(){
		
		mainChargeItem.clearPrice();
		if(depositChargeItems!=null){
			for(DepositChargeItem depositChargeItem:depositChargeItems){
				depositChargeItem.clearPrice();
			}
		}
		if(attachChargeItems!=null){
			for(AppendChargeItem attachChargeItem:attachChargeItems){
				attachChargeItem.clearPrice();
			}
		}
		
	}
	
	public void clearResult(){
		this.setResult(null);
		mainChargeItem.clearResult();
		if(depositChargeItems!=null){
			for(DepositChargeItem depositChargeItem:depositChargeItems){
				depositChargeItem.clearResult();
			}
		}
		if(attachChargeItems!=null){
			for(AppendChargeItem attachChargeItem:attachChargeItems){
				attachChargeItem.clearResult();
			}
		}
	}

    public BillingFunc deepClone() {

        BillingFunc s = null;
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(this);
            oos.flush();
            oos.close();
            byte[] arrByte = baos.toByteArray();
            ByteArrayInputStream bais = new ByteArrayInputStream(arrByte);
            ObjectInputStream ois = new ObjectInputStream(bais);
            s = (BillingFunc)ois.readObject();
            ois.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return s;
    }
}
