<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="会员权益管理">
    <style type="text/css">
        .hiddenFlow {
            overflow-x: hidden;
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            height: 22px;
            width: 200px !important;
        }

    </style>
</ls:head>
<ls:body>
    <ls:form name="infoForm" id="infoForm">
        <table align="center" class="tab_search">
            <ls:grid url="" caption="会员权益管理" showRowNumber="false" name="infoGrid" height="250px"
                     showCheckBox="false"  width="100%">
                <ls:gridToolbar name="myButton">
                    <ls:gridToolbarItem text="详情" imageKey="search" onclick="doPreview" name="search"></ls:gridToolbarItem>
                    <ls:gridToolbarItem text="配置" imageKey="edit" onclick="doEdit" name="edit"></ls:gridToolbarItem>
                </ls:gridToolbar>
                <ls:column name="vipLevelName" caption="会员等级"  />
                <ls:column name="vipTypeName"  caption="会员类型"  />
                <ls:column name="benefitsCount" caption="会员权益数量" />
            </ls:grid>
        </table>
    </ls:form>
    <ls:script>
        window.infoGrid=infoGrid;
        window.query=query;
        infoGrid.itemclick =function() {
        var item = infoGrid.getSelectedItem();
        if(!item){
        return;
        }
        var goodsTypeValue = item.goodsType;
        if(goodsTypeValue=="01"){
        infoGrid.toolbarItems.edit.setEnabled(false);

        }else{
        infoGrid.toolbarItems.edit.setEnabled(true);
        }
        }
        window.onload = function () {

        infoForm.clear();
        query();
        }


        function query(){
        infoGrid.query("~/member/benefits/query");
        }
        function doClear(){
        infoForm.clear();
        }


        //修改
        function doEdit(){
        var item = infoGrid.getCheckedIDs();
        if(item.length>0){
        if(item.length>1){
        LS.message("info","只能选择一条记录");
        return;
        }else{
        var items=infoGrid.getCheckedItems();
        LS.dialog(rootUrl+"/member/benefits/initEdit?id="+item[0], "会员权益配置",1000, 600, true, null);
        }
        }else{
        LS.message("info","请选择一条记录");
        return;
        }

        }
        function doPreview(){
        var item = infoGrid.getCheckedIDs();
        if(item.length>0){
        if(item.length>1){
        LS.message("info","只能选择一条记录");
        return;
        }else{
        LS.dialog(rootUrl+"/member/benefits/initInfo?id="+item[0], "会员权益详情",1000, 600, true, null);
        }
        }else{
        LS.message("info","请选择一条记录");
        return;
        }
        }



    </ls:script>
</ls:body>
</html>
