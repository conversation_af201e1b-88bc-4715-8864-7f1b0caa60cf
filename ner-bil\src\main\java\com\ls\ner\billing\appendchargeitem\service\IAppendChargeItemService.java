package com.ls.ner.billing.appendchargeitem.service;

import java.util.List;

import com.ls.ner.billing.common.bo.AppendChargeItemBo;

public interface IAppendChargeItemService {

	public abstract List<AppendChargeItemBo> listDepositChargeItem(AppendChargeItemBo tempBo);

	public abstract List<AppendChargeItemBo> listAttachChargeItem(AppendChargeItemBo tempBo);

	public abstract void deleteDepositChargeItem(String itemNo);

	public abstract void deleteAttachChargeItem(String itemNo);

	public abstract void updateDepositChargeItem(AppendChargeItemBo appendChargeItemBo);

	public abstract void updateAttachChargeItem(AppendChargeItemBo appendChargeItemBo);

	public abstract String createDepositChargeItem(AppendChargeItemBo appendChargeItemBo);

	public abstract String createAttachChargeItem(AppendChargeItemBo appendChargeItemBo);

	/**
	 * 
	 * 方法说明：费用项批量更新
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月15日 下午12:24:28
	 * History:  2016年4月15日 下午12:24:28   lipf   Created.
	 *
	 * @param list
	 *
	 */
	public abstract void updateItems(List<AppendChargeItemBo> list);

}
