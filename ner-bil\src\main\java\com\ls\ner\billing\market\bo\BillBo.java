package com.ls.ner.billing.market.bo;

import java.io.Serializable;

/**
*描述:订单计费结果
*BillBo.java
*作者：biaoxiangd
*创建日期：2017-06-06 17:58
**/
public class BillBo implements Serializable {

    private static final long serialVersionUID = -5835112270426053538L;
    private String orderNo; // 订单编号
    private String prodBusiType;//产品业务类
    private String custId; // 用户编号
    private String billAmt;// 订单费用金额
    private String spTAmt; // 订单优惠总金额
    private String calcProcess; // 计算过程描述
    private String cpnId; // 优惠券ID
    private String spAmt; // 计算后的优惠值
    private String serviceAmt; // 订单服务费

    public String getServiceAmt() {
        return serviceAmt;
    }

    public void setServiceAmt(String serviceAmt) {
        this.serviceAmt = serviceAmt;
    }

    public String getSpAmt() {
        return spAmt;
    }

    public void setSpAmt(String spAmt) {
        this.spAmt = spAmt;
    }

    public String getCalcProcess() {
        return calcProcess;
    }

    public void setCalcProcess(String calcProcess) {
        this.calcProcess = calcProcess;
    }

    public String getCpnId() {
        return cpnId;
    }

    public void setCpnId(String cpnId) {
        this.cpnId = cpnId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProdBusiType() {
        return prodBusiType;
    }

    public void setProdBusiType(String prodBusiType) {
        this.prodBusiType = prodBusiType;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getBillAmt() {
        return billAmt;
    }

    public void setBillAmt(String billAmt) {
        this.billAmt = billAmt;
    }

    public String getSpTAmt() {
        return spTAmt;
    }

    public void setSpTAmt(String spTAmt) {
        this.spTAmt = spTAmt;
    }
}
