package com.ls.ner.billing.vip.service.impl;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.vip.bo.RegularVipRankBo;
import com.ls.ner.billing.vip.bo.VipBo;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.condition.VipRecordCondition;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.service.IVipService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
@Service(target = {ServiceType.LOCAL}, value = "vipService")
public class VipServiceImpl implements IVipService {

    private static final Logger LOGGER = LoggerFactory.getLogger(VipServiceImpl.class);

    @Autowired
    private IMemberBenefitsDao memberBenefitsDao;

    @Override
    public RegularVipRankBo getRegularVipConfigById(String rankId) {
        return memberBenefitsDao.getRegularVipConfigById(rankId);
    }

    @Override
    public List<RegularVipRankBo> getRegularVipConfigList() {
        return memberBenefitsDao.selectRegularVipConfigList();
    }

    @Override
    public void editRegularVipConfig(RegularVipRankBo regularVipRankBo) {
        memberBenefitsDao.editRegularVipConfig(regularVipRankBo);
    }

    @Override
    public VipBo getVipConfigById(String vipId) {
        return memberBenefitsDao.getVipConfigById(vipId);
    }

    @Override
    public List<VipBo> getVipConfigList() {
        return memberBenefitsDao.getVipConfigList();
    }

    @Override
    public void insertVipConfig(VipBo bo) {
        memberBenefitsDao.insertVipConfig(bo);
    }

    @Override
    public void updateVipConfig(VipBo bo) {
        memberBenefitsDao.updateVipConfig(bo);
    }

    @Override
    public void updateVipConfigStatus(VipBo bo) {
        memberBenefitsDao.updateVipConfigStatus(bo);
    }

    @Override
    public List<VipPayRecordBo> getRecordList(VipRecordCondition condition) {
        return memberBenefitsDao.getRecordList(condition);
    }

    @Override
    public int getRecordListCount(VipRecordCondition condition) {
        return memberBenefitsDao.getRecordListCount(condition);
    }
}
