package com.ls.ner.billing.vip.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
public class VipRecordCondition extends QueryCondition {

    private String recordId;

    private String mobile;

    private String vipType;

    private String payType;

    private String payChannel;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getVipType() {
        return vipType;
    }

    public void setVipType(String vipType) {
        this.vipType = vipType;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

}
