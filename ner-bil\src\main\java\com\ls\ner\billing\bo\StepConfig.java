/**
 *
 * @(#) StepConfig.java
 * @Package com.ls.ner.billing.api.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.bo;

import java.io.Serializable;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: StepConfig.java,v 1.1 2016/02/26 02:35:24 34544 Exp $ 
 *
 *  History:  2016年2月25日 下午11:01:13   lipf   Created.
 *           
 */
public class StepConfig implements Serializable {

	private static final long serialVersionUID = -8571199969587865609L;
	private String mainNo;// 主收费编号
	private String price;//单价
	private String sn;// 序号
	private String startPoint;// 起始
	private String endPoint;// 结束

	public String getMainNo() {
		return mainNo;
	}

	public void setMainNo(String mainNo) {
		this.mainNo = mainNo;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getStartPoint() {
		return startPoint;
	}

	public void setStartPoint(String startPoint) {
		this.startPoint = startPoint;
	}

	public String getEndPoint() {
		return endPoint;
	}

	public void setEndPoint(String endPoint) {
		this.endPoint = endPoint;
	}
}
