/**
 *
 * @(#) EPrcChargeDetBo.java
 * @Package com.ls.ner.billing.api.prccharge
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api.prccharge.bo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年5月1日 上午10:17:09   lipf   Created.
 *           
 */
public class EPrcChargeDetBo implements Serializable{
	private static final long serialVersionUID = 1847515619367299323L;
	private String calcId;
	private String itemCode;
	private String itemName;
	private String itemUnits;
	private String pricingAmt;
	private String amount;
	private String price;
	private String calcProcess;
	private String itemDesc;
	private List<Map<String,Object>> dailyBillDetList;

	public String getCalcId() {
		return calcId;
	}

	public void setCalcId(String calcId) {
		this.calcId = calcId;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getItemUnits() {
		return itemUnits;
	}

	public void setItemUnits(String itemUnits) {
		this.itemUnits = itemUnits;
	}

	public String getPricingAmt() {
		return pricingAmt;
	}

	public void setPricingAmt(String pricingAmt) {
		this.pricingAmt = pricingAmt;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getCalcProcess() {
		return calcProcess;
	}

	public void setCalcProcess(String calcProcess) {
		this.calcProcess = calcProcess;
	}

	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}

	public List<Map<String, Object>> getDailyBillDetList() {
		return dailyBillDetList;
	}

	public void setDailyBillDetList(List<Map<String, Object>> dailyBillDetList) {
		this.dailyBillDetList = dailyBillDetList;
	}

}
