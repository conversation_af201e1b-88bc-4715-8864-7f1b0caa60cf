package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.api.common.bo.BillingOrderRelaBo;
import com.ls.ner.billing.common.bo.BillingOrderRelaBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface BillingOrderRelaBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int countByExample(BillingOrderRelaBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int deleteByExample(BillingOrderRelaBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int insert(BillingOrderRelaBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int insertSelective(BillingOrderRelaBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    List<BillingOrderRelaBo> selectByExample(BillingOrderRelaBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    BillingOrderRelaBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int updateByExampleSelective(@Param("record") BillingOrderRelaBo record, @Param("example") BillingOrderRelaBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int updateByExample(@Param("record") BillingOrderRelaBo record, @Param("example") BillingOrderRelaBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int updateByPrimaryKeySelective(BillingOrderRelaBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    int updateByPrimaryKey(BillingOrderRelaBo record);
}