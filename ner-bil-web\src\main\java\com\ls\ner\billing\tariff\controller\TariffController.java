/**
 *
 * @(#) TariffController.java
 * @Package com.ls.ner.billing.controller
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.tariff.bo.AttachItemBo;
import com.ls.ner.billing.tariff.bo.TariffPlanBo;
import com.ls.ner.billing.tariff.service.ITariffService;
import com.ls.ner.util.ListMapUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: TariffController.java,v 1.1 2016/02/26 02:34:01 34544 Exp $
 * 
 *          History: 2016年2月18日 上午9:44:53 lipf Created.
 * 
 */
@Controller
@RequestMapping("/billing/tariff")
public class TariffController extends QueryController<TariffPlanBo> {

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired("tariffService")
	private ITariffService tariffService;

	/**
	 * 
	 * 方法说明：资费标准管理页面初始化
	 * 
	 * Author： lipf Create Date： 2016年2月18日 上午9:58:46 History: 2016年2月18日
	 * 上午9:58:46 lipf Created.
	 * 
	 * @param m
	 * @return
	 * 
	 */
	@RequestMapping("/init")
	public String init(Model m) {
		List<CodeBO> subBeList = codeService.getStandardCodes(BillConstants.SUB_BE, null);
		m.addAttribute("subBeList", subBeList);
		return "/bil/tariff";
	}

	/**
	 * 
	 * 方法说明：资费标准表格数据查询
	 * 
	 * Author： lipf Create Date： 2016年2月24日 上午9:56:34 History: 2016年2月24日
	 * 上午9:56:34 lipf Created.
	 * 
	 * @param params
	 * @return
	 * 
	 */
	@RequestMapping("/queryTariffList")
	public @ItemResponseBody
	QueryResultObject queryTariffList(@QueryRequestParam("params")
	RequestCondition params) {
		TariffPlanBo bo = this.rCondition2QCondition(params);
		bo.setPageBegin(bo.getPageBegin()-1);
		List<TariffPlanBo> dataList = tariffService.getTariff(bo);
		int count = tariffService.getTariffCount(bo);
		return RestUtils.wrappQueryResult(dataList, count);
	}

	/**
	 * 
	 * 方法说明：资费新增页面初始化
	 * 
	 * Author： lipf Create Date： 2016年2月18日 下午2:39:51 History: 2016年2月18日
	 * 下午2:39:51 lipf Created.
	 * 
	 * @param m
	 * @return
	 * 
	 */
	@RequestMapping(value = "/addTariff", method = RequestMethod.GET)
	public String addTariff(Model m) {
		List<CodeBO> subBeList = codeService.getStandardCodes(BillConstants.SUB_BE, null);
		List<CodeBO> chargeModeList = codeService.getStandardCodes(BillConstants.CHARGE_MODE, null);
		List<CodeBO> chargeTypeList = codeService.getStandardCodes(BillConstants.CHARGE_TYPE, null);
		List<Map<String, Object>> _chargeModeList = ListMapUtil.toCheckListMap(chargeModeList);
		List<Map<String, Object>> _chargeTypeList = ListMapUtil.toCheckListMap(chargeTypeList);
		m.addAttribute("subBeList", subBeList);
		m.addAttribute("chargeModeList", _chargeModeList);
		m.addAttribute("chargeTypeList", _chargeTypeList);
		m.addAttribute("addTariffForm", new TariffPlanBo());
		return "/bil/addTariff";
	}

	/**
	 * 
	 * 方法说明：资费编辑页面初始化
	 * 
	 * Author： lipf Create Date： 2016年2月18日 下午2:45:32 History: 2016年2月18日
	 * 下午2:45:32 lipf Created.
	 * 
	 * @param m
	 * @param infoId
	 * @return
	 * 
	 */
	@RequestMapping(value = "/editTariff", method = RequestMethod.GET)
	public String editTariff(Model m, @RequestParam("id")
	String planNo) {
		try {
			List<CodeBO> subBeList = codeService.getStandardCodes(BillConstants.SUB_BE, null);
			List<CodeBO> chargeModeList = codeService.getStandardCodes(BillConstants.CHARGE_MODE, null);
			List<CodeBO> chargeTypeList = codeService.getStandardCodes(BillConstants.CHARGE_TYPE, null);
			m.addAttribute("subBeList", subBeList);
			m.addAttribute("chargeModeList", chargeModeList);
			m.addAttribute("chargeTypeList", chargeTypeList);
			TariffPlanBo bo = new TariffPlanBo();
			bo.setPlanNo(planNo);
			// List<TariffPlanBo> dataList = tariffService.getTariff(bo);
			// if (dataList != null && dataList.size() > 0) {
			// bo = dataList.get(0);
			// }
			m.addAttribute("editTariffForm", bo);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "/bil/editTariff";
	}

	/**
	 * 
	 * 方法说明：
	 * 
	 * Author： lipf Create Date： 2016年2月23日 下午5:18:26 History: 2016年2月23日
	 * 下午5:18:26 lipf Created.
	 * 
	 * @param dataParams
	 * @return
	 * 
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	public @ItemResponseBody
	QueryResultObject saveOrUpdate(@RequestBody
	Map<String, Object> dataParams) {
		List<Map<String, Object>> itemList = (List<Map<String, Object>>) (dataParams.get("items"));
		Map<String, Object> formMap = (Map<String, Object>) (dataParams.get("formData"));
		List<AttachItemBo> dataList = new ArrayList<AttachItemBo>();
		String mark = "N";
		try {
			for (Map<String, Object> map : itemList) {
				AttachItemBo vo = new AttachItemBo();
				BeanUtils.populate(vo, map);
				vo.setBuyIdentity(map.get("needFlag") == null ? "" : map.get("needFlag").toString());
				vo.setItemNo(map.get("chargeItemCode") == null ? "" : map.get("chargeItemCode").toString());
				dataList.add(vo);
			}
			TariffPlanBo formBo = new TariffPlanBo();
			BeanUtils.populate(formBo, formMap);

			if (StringUtils.nullOrBlank(formBo.getPlanNo())) {
				tariffService.saveTariffPlan(dataList, formBo);
				mark = "saveSucc";
			} else {
				tariffService.updateTariffPlan(dataList, formBo);
				mark = "updateSucc";
			}

		} catch (Exception e) {
			e.printStackTrace();
			mark = e.getMessage();
		}
		return RestUtils.wrappQueryResult(mark);
	}

	@Override
	protected TariffPlanBo initCondition() {
		return new TariffPlanBo();
	}
}
