package com.ls.ner.billing.rest.vip.controller;

import com.ls.ner.base.service.ITokenService;
import com.ls.ner.billing.vip.bo.VipBo;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.condition.VipRecordCondition;
import com.ls.ner.billing.vip.service.IVipService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/9
 */
@Controller
@RequestMapping(value = "/api/v0.1/vip")
public class VipRestController {

    @ServiceAutowired(value = "vipService", serviceTypes = ServiceType.LOCAL)
    private IVipService vipService;

    @ServiceAutowired(value = "tokenService")
    private ITokenService tokenService;

    /**
     * 获取付费会员支付信息
     *
     * @return
     */
    @RequestMapping(value = "/config", method = RequestMethod.POST)
    public ResponseEntity<?> getVipConfig() {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<VipBo> vipConfigList = vipService.getVipConfigList();
        resultMap.put("vipConfigList", vipConfigList);
        return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
    }

    /**
     * 获取会员支付记录信息
     *
     * @return
     */
    @RequestMapping(value = "/record", method = RequestMethod.POST)
    public ResponseEntity<?> getVipRecord() {
        Map<String, String> tokenMsg = tokenService.getMsgFromToken();
        String mobile = tokenMsg.get("mobile");
        Map<String, Object> resultMap = new HashMap<String, Object>();
        VipRecordCondition condition = new VipRecordCondition();
        condition.setMobile(mobile);
        List<VipPayRecordBo> vipRecordList = vipService.getRecordList(condition);
        resultMap.put("vipRecordList", vipRecordList);
        return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
    }

//    /**
//     * 获取普通会员配置信息
//     *
//     * @return
//     */
//    @RequestMapping(value = "/regular", method = RequestMethod.POST)
//    public ResponseEntity<?> getRegularVipConfig() {
//        Map<String, Object> resultMap = new HashMap<String, Object>();
//        List<VipBo> vipConfigList = vipService.getVipConfigList();
//        resultMap.put("vipConfigList", vipConfigList);
//        return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
//    }

}
