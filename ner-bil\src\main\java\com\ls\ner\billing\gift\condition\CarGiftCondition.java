package com.ls.ner.billing.gift.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * <AUTHOR>
 * @description CarGiftCondition
 * @create 2020-04-29 18:16
 */
public class CarGiftCondition extends QueryCondition {
    /** 车牌号 **/
    private String licenseNo;
    /** 车架号 **/
    private String vin;
    /** 身份证号 **/
    private String certNo;
    /** 申请时间 **/
    private String applyTimeBgn;
    /** 申请时间 **/
    private String applyTimeEnd;
    /** 审核时间 **/
    private String auditTimeBegin;
    /** 审核时间 **/
    private String auditTimeEnd;
    /** 领取状态 **/
    private String giftStatus;
    /** 是否营运车辆 **/
    private String carType;
    private String giftId;
    private String mobile;
    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getApplyTimeBgn() {
        return applyTimeBgn;
    }

    public void setApplyTimeBgn(String applyTimeBgn) {
        this.applyTimeBgn = applyTimeBgn;
    }

    public String getApplyTimeEnd() {
        return applyTimeEnd;
    }

    public void setApplyTimeEnd(String applyTimeEnd) {
        this.applyTimeEnd = applyTimeEnd;
    }

    public String getAuditTimeBegin() {
        return auditTimeBegin;
    }

    public void setAuditTimeBegin(String auditTimeBegin) {
        this.auditTimeBegin = auditTimeBegin;
    }

    public String getAuditTimeEnd() {
        return auditTimeEnd;
    }

    public void setAuditTimeEnd(String auditTimeEnd) {
        this.auditTimeEnd = auditTimeEnd;
    }

    public String getGiftStatus() {
        return giftStatus;
    }

    public void setGiftStatus(String giftStatus) {
        this.giftStatus = giftStatus;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }
}
