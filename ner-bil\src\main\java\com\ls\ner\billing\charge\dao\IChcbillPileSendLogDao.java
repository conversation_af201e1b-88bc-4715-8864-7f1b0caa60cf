package com.ls.ner.billing.charge.dao;

import java.util.List;

import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;



/**
 * <AUTHOR>
 * @dateTime 2016-04-08
 * @description 桩电价下发日志   E_CHCBILL_PILESEND_LOG
 */
public interface IChcbillPileSendLogDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志   E_CHCBILL_PILESEND_LOG
	 */
	public List<ChcbillPileSendLogBo> queryChcbillPileSendLogs(ChcbillSendQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志条数   E_CHCBILL_PILESEND_LOG
	 */
	public int queryChcbillPileSendLogsNum(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 新增桩电价下发日志   E_CHCBILL_PILESEND_LOG
	 */
	public void insertChcbillPileSendLog(ChcbillPileSendLogBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-01
	 * @description 更新下发状态
	 */
	public void updateChcbillPileSendLogStatus(ChcbillPileSendLogBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 删除桩电价下发日志   E_CHCBILL_PILESEND_LOG
	 */
	public void deleteChcbillPileSendLog(String chcNo);

}
