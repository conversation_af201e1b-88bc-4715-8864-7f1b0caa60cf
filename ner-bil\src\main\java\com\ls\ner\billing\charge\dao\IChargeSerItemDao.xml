<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeSerItemDao">

	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeSerItemBo">
		<result column="ITEM_NO" property="itemNo" />
		<result column="ITEM_NAME" property="itemName" />
		<result column="P_BE" property="pBe" />
		<result column="ITEM_TYPE" property="itemType" />
		<result column="ITEM_UNIT" property="itemUnit" />
		<result column="REMARKS" property="remarks" />
		<result column="BUY_TYPE" property="buyType" />
		<result column="SN" property="sn" />
		<result column="ITEM_STATUS" property="itemStatus" />
		<result column="SYSTEM_ID" property="systemId" />
	 </resultMap>
 
	 <sql id="queryChargeSerItemsWhere">
		<if test="itemNos !=null and itemNos.length!=0">
			and ITEM_NO in
				<foreach item="item" index="index" collection="itemNos" open="(" separator="," close=")">
					#{item}
				</foreach>
		</if>
		<if test="itemName !=null and itemName !=''">
			and ITEM_NAME like concat('%',#{itemName},'%')
		</if>
		<if test="pBe !=null and pBe !=''">
			and P_BE =#{pBe}
		</if>
		<if test="itemType !=null and itemType !=''">
			and ITEM_TYPE =#{itemType}
		</if>
		<if test="buyType !=null and buyType !=''">
			and BUY_TYPE =#{buyType}
		</if>
		<if test="itemStatus !=null and itemStatus !=''">
			and ITEM_STATUS =#{itemStatus}
		</if>
		<if test="unUseItemNoss !=null">
			and ITEM_NO not in
				<foreach item="item" index="index" collection="unUseItemNoss" open="(" separator="," close=")">
					#{item}
				</foreach>
		</if>
	 </sql>
  
	<sql id="chargeSerItem">
		SYSTEM_ID,ITEM_NO,ITEM_NAME,P_BE,ITEM_TYPE,ITEM_UNIT,REMARKS,BUY_TYPE,SN,ITEM_STATUS
	</sql>
  
	<!-- 查询追加收费项目E_APPEND_CHARGE_ITEM-->
	<select id="queryChargeSerItems" parameterType="com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition" resultMap="BaseResultMap">
		select  
			<include refid="chargeSerItem"></include>
		from E_APPEND_CHARGE_ITEM
		<where>
			<include refid="queryChargeSerItemsWhere"></include>
		</where>
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	
	<!-- 查询追加收费项目条数E_APPEND_CHARGE_ITEM -->
	<select id="queryChargeSerItemsNum" parameterType="com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition" resultType="int">
		select count(1)
		from E_APPEND_CHARGE_ITEM
		<where>
			<include refid="queryChargeSerItemsWhere"></include>
		</where>
	</select>
  
	<!-- 根据itemName查询追加收费项目条数E_APPEND_CHARGE_ITEM -->
	<select id="queryChargeSerItemsNumByName" parameterType="com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition" resultType="int">
		select count(1)
		from E_APPEND_CHARGE_ITEM
		<where>
			P_BE =#{pBe} and ITEM_TYPE =#{itemType}
			<if test="itemNo !=null and itemNo !=''">
				and ITEM_NO = #{itemNo}
			</if>
		</where>
	</select>
  
	<!-- 查询追加收费项目最大的sn值 E_APPEND_CHARGE_ITEM -->
	<select id="queryChargeSerMaxSn" parameterType="com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition" resultType="int">
		select if(max(SN) is not null,max(SN),0)
		from E_APPEND_CHARGE_ITEM
		<where>
			<include refid="queryChargeSerItemsWhere"></include>
		</where>
	</select>
  
	<!-- 新增追加收费项目E_APPEND_CHARGE_ITEM -->
	<insert id="insertChargeSerItem" parameterType="com.ls.ner.billing.charge.bo.ChargeSerItemBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_APPEND_CHARGE_ITEM
		<trim prefix="(" suffix=")">
	     	<if test="itemNo !=null and itemNo !=''">ITEM_NO,</if>
			<if test="itemName !=null and itemName !=''">ITEM_NAME,</if>
			<if test="pBe !=null and pBe !=''">P_BE,</if>
			<if test="itemType !=null and itemType !=''">ITEM_TYPE,</if>
			<if test="itemUnit !=null and itemUnit !=''">ITEM_UNIT,</if>
			<if test="remarks !=null and remarks !=''">REMARKS,</if>
			<if test="buyType !=null and buyType !=''">BUY_TYPE,</if>
			<if test="sn !=null and sn !=''">SN,</if>
			<if test="itemStatus !=null and itemStatus !=''">ITEM_STATUS,</if>
	      	DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
	    	<if test="itemNo !=null and itemNo !=''">#{itemNo},</if>
			<if test="itemName !=null and itemName !=''">#{itemName},</if>
			<if test="pBe !=null and pBe !=''">#{pBe},</if>
			<if test="itemType !=null and itemType !=''">#{itemType},</if>
			<if test="itemUnit !=null and itemUnit !=''">#{itemUnit},</if>
			<if test="remarks !=null and remarks !=''">#{remarks},</if>
			<if test="buyType !=null and buyType !=''">#{buyType},</if>
			<if test="sn !=null and sn !=''">#{sn},</if>
			<if test="itemStatus !=null and itemStatus !=''">#{itemStatus},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新追加收费项目E_APPEND_CHARGE_ITEM -->
	<update id="updateChargeSerItem" parameterType="com.ls.ner.billing.charge.bo.ChargeSerItemBo">
		update E_APPEND_CHARGE_ITEM
		<set>
			<if test="itemName !=null and itemName !=''">ITEM_NAME =#{itemName},</if>
			<if test="pBe !=null and pBe !=''">P_BE =#{pBe},</if>
			<if test="itemType !=null and itemType !=''">ITEM_TYPE =#{itemType},</if>
			<if test="itemUnit !=null and itemUnit !=''">ITEM_UNIT =#{itemUnit},</if>
			<if test="remarks !=null and remarks !=''">REMARKS =#{remarks},</if>
			<if test="buyType !=null and buyType !=''">BUY_TYPE =#{buyType},</if>
			<if test="sn !=null and sn !=''">SN =#{sn},</if>
			<if test="itemStatus !=null and itemStatus !=''">ITEM_STATUS =#{itemStatus},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			ITEM_NO = #{itemNo}
		</where>
	</update>
	
</mapper>