/**
 *
 * @(#) BillingAdjustController.java
 * @Package com.ls.ner.billing.config.controller
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.ls.ner.base.constants.PublicConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.util.json.JsonMapper;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.api.rent.model.SubChargeItem;
import com.ls.ner.billing.config.bo.BillingConfigForm;
import com.ls.ner.billing.config.bo.BillingConfigFunc;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.bo.CalendarDataVo;
import com.ls.ner.billing.config.service.IBillingAdjustService;
import com.ls.ner.billing.config.service.IBillingConfigService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 *  类描述：调整定价controller
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年4月10日 上午11:24:40   lipf   Created.
 *           
 */
@Controller
@RequestMapping("/billing/adjust")
public class BillingAdjustController extends QueryController<BillingConfigQueryCondition> {
	private static final String VIEW_BILLING_ADJUST_INIT = "/bil/adjust/billingAdjustInit";
	private static final String VIEW_BILLING_ADJUST_CALENDAR = "/bil/adjust/billingAdjustCalendar";
	private static final String VIEW_BILLING_ADJUST_FORM = "/bil/adjust/billingAdjustForm";
	@ServiceAutowired("billingAdjustService")
	private IBillingAdjustService billingAdjustService;
	@ServiceAutowired("billingConfigService")
	private IBillingConfigService billingConfigService;
	@Autowired
	private ControllerSupport controllerSupport;

	/**
	 * 
	 * 方法说明：调价页面初始化
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 上午11:28:40
	 * History:  2016年4月10日 上午11:28:40   lipf   Created.
	 *
	 * @param m
	 * @return
	 *
	 */
	/*@RequestMapping("/init")
	public String init(Model m) {
		return VIEW_BILLING_ADJUST_INIT;
	}*/
	/**
	 * 
	 * 方法说明：调价页面表格视图
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午1:24:30
	 * History:  2016年4月10日 下午1:24:30   lipf   Created.
	 *
	 * @param m
	 * @return
	 *
	 */
	@RequestMapping("/initGrid")
	public String initGrid(Model m) {
		//资产域，跨域访问
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return VIEW_BILLING_ADJUST_INIT;
	}
	/**
	 * 
	 * 方法说明：调价页面日历视图
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午3:00:30
	 * History:  2016年4月11日 下午3:00:30   lipf   Created.
	 *
	 * @param m
	 * @return
	 *
	 */
	@RequestMapping("/initCalendar")
	public String initCalendar(Model m) {
		//资产域，跨域访问
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return VIEW_BILLING_ADJUST_CALENDAR;
	}
	/**
	 * 
	 * 方法说明：车辆调价表格数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 上午8:55:48
	 * History:  2016年4月11日 上午8:55:48   lipf   Created.
	 *
	 * @param params
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping("/getBillingAdjustList")
	public @ItemResponseBody
	QueryResultObject getBillingAdjustList(@QueryRequestParam("params") 
	RequestCondition params) throws Exception {
		BillingConfigQueryCondition condition = this.rCondition2QCondition(params);
		List<BillingConfigFunc> dataList = billingAdjustService.getBillingAdjustList(condition);
		return RestUtils.wrappQueryResult(dataList, condition.getRows());
	}
	/**
	 * 
	 * 方法说明：车辆调价日历数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 上午8:55:43
	 * History:  2016年4月11日 上午8:55:43   lipf   Created.
	 *
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping("/getBillingAdjust2Calendar")
	public @ResponseBody WrappedResult getBillingAdjust2Calendar(HttpServletRequest request) throws Exception {
		BillingConfigQueryCondition condition = new BillingConfigQueryCondition();
		List<CalendarDataVo> dataList = billingAdjustService.getBillingAdjustCalendar(condition);
		return WrappedResult.successWrapedResult(dataList);
	}
	
	/**
	 * 
	 * 方法说明：调价表单页面初始化
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午3:01:55
	 * History:  2016年4月11日 下午3:01:55   lipf   Created.
	 *
	 * @param m
	 * @return
	 * @throws UnsupportedEncodingException 
	 *
	 */
	@RequestMapping(value = "/initAdjustForm", method = RequestMethod.GET)
	public String initAdjustForm(BillingConfigForm oriForm,Model m) throws UnsupportedEncodingException {
		BillingConfigForm forms = billingConfigService.initFormByPlanNo(oriForm.getPlanNo());
		forms.setAutoModelNo(oriForm.getAutoModelNo());
		forms.setAutoModelName(URLDecoder.decode(oriForm.getAutoModelName(), "UTF-8"));
		forms.setApplyDate(oriForm.getApplyDate());
		m.addAttribute("billingAdjustForm", forms);
		m.addAttribute("billingConfigFormJson",JsonMapper.nonEmptyMapper().toJson(forms));
		setStCodes(m);
		m.addAttribute("operFlag","adjust");
		return VIEW_BILLING_ADJUST_FORM;
	}
	protected void setStCodes(Model m) {
		//普通的标准代码用
		controllerSupport.addStCodes(m, SubBe.CODE_TYPE,ChargeWay.CODE_TYPE,ChargeMode.CODE_TYPE,PriceUnit.CODE_TYPE,SubChargeItem.Limit.CODE_TYPE);
	}
	@Override
	protected BillingConfigQueryCondition initCondition() {
		return new BillingConfigQueryCondition();
	}

}
