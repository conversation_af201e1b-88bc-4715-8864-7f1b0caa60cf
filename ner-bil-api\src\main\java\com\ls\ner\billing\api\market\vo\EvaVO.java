package com.ls.ner.billing.api.market.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品兑换评价
 * <AUTHOR>
 * @date 2025/1/8
 */
public class EvaVO implements Serializable {

    /**
     * 用户名
     */
    private Integer evaId;

    /**
     * 用户名
     */
    private String evaUserName;
    /**
     * 评分
     */
    private String evaScore;
    /**
     * 用户评价
     */
    private String evaRemark;
    /**
     * 评价图片
     */
    private String evaUrl;
    /**
     * 商家回复
     */
    private String replyRemark;

    private List<String> fileIds;
    /**
     * 评价详情
     */
    private List<Map<String, String>> evaItems;

    public String getEvaUserName() {
        return evaUserName;
    }

    public void setEvaUserName(String evaUserName) {
        this.evaUserName = evaUserName;
    }

    public String getEvaScore() {
        return evaScore;
    }

    public void setEvaScore(String evaScore) {
        this.evaScore = evaScore;
    }

    public String getEvaRemark() {
        return evaRemark;
    }

    public void setEvaRemark(String evaRemark) {
        this.evaRemark = evaRemark;
    }

    public String getReplyRemark() {
        return replyRemark;
    }

    public void setReplyRemark(String replyRemark) {
        this.replyRemark = replyRemark;
    }

    public List<Map<String, String>> getEvaItems() {
        return evaItems;
    }

    public void setEvaItems(List<Map<String, String>> evaItems) {
        this.evaItems = evaItems;
    }

    public Integer getEvaId() {
        return evaId;
    }

    public void setEvaId(Integer evaId) {
        this.evaId = evaId;
    }

    public String getEvaUrl() {
        return evaUrl;
    }

    public void setEvaUrl(String evaUrl) {
        this.evaUrl = evaUrl;
    }

    public List<String> getFileIds() {
        return fileIds;
    }

    public void setFileIds(List<String> fileIds) {
        this.fileIds = fileIds;
    }
}
