package com.ls.ner.billing.vip.controller;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.vip.bo.VipBo;
import com.ls.ner.billing.vip.condition.VipCondition;
import com.ls.ner.billing.vip.service.IVipService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.webcommon.rest.annotation.IdRequestBody;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.object.IDRequestObject;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.UUID;

/**
 * 普通会员
 *
 * <AUTHOR>
 * @date 2024/8/7
 */
@Controller
@RequestMapping("/vip")
public class VipController extends QueryController<VipCondition> {

    private static final Logger logger = LoggerFactory.getLogger(VipController.class);

    @ServiceAutowired(value = "vipService", serviceTypes = ServiceType.LOCAL)
    private IVipService vipService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @RequestMapping("/init")
    public String init(Model m) {
        logger.debug("/bil/vip/vipManage");
        List<CodeBO> itemStatus = codeService.getStandardCodes("vipStatus", null);
        List<CodeBO> vipType = codeService.getStandardCodes("vipType", null);
        m.addAttribute("itemStatus", itemStatus);
        m.addAttribute("vipType", vipType);
        return "/bil/vip/vipManage";
    }

    @RequestMapping("/startConfig")
    public @ItemResponseBody
    QueryResultObject startConfig(@IdRequestBody
                                          IDRequestObject idObject) {
        String[] ids = idObject.getIds();
        for (String id : ids) {
            VipBo bo = new VipBo();
            bo.setVipId(id);
            bo.setStatus("1");
            vipService.updateVipConfigStatus(bo);
        }

        return RestUtils.wrappQueryResult("Y");
    }

    @RequestMapping("/stopConfig")
    public @ItemResponseBody
    QueryResultObject stopConfig(@IdRequestBody
                                         IDRequestObject idObject) {
        String[] ids = idObject.getIds();
        for (String id : ids) {
            VipBo bo = new VipBo();
            bo.setVipId(id);
            bo.setStatus("2");
            vipService.updateVipConfigStatus(bo);
        }
        return RestUtils.wrappQueryResult("Y");
    }

    @RequestMapping("/vipConfigList")
    public @ItemResponseBody
    QueryResultObject vipConfigList() {
        List<VipBo> bos = vipService.getVipConfigList();
        List<CodeBO> itemStatus = codeService.getStandardCodes("vipStatus", null);
        List<CodeBO> vipType = codeService.getStandardCodes(BizConstants.CodeType.VIP_TYPE, null);
        for (VipBo bo : bos) {
            for (CodeBO status : itemStatus) {
                if (bo.getStatus().equals(status.getCodeValue())) {
                    bo.setStatusName(status.getCodeName());
                }
            }
            for (CodeBO codeBO : vipType) {
                if (bo.getVipType().equals(codeBO.getCodeValue())) {
                    bo.setVipTypeName(codeBO.getCodeName());
                }
            }
        }
        return RestUtils.wrappQueryResult(bos, bos.size());
    }

    @RequestMapping(value = "/vipConfigInfo", method = RequestMethod.GET)
    public String vipConfigInfo(@RequestParam("vipId") String vipId, Model m, @RequestParam("flag") String flag) {
        logger.debug("flag" + flag);
        logger.debug("vipId" + vipId);
        if ("add".equals(flag)) {
            VipBo bo = new VipBo();
            m.addAttribute("vipConfigForm", bo);
            List<CodeBO> vipType = codeService.getStandardCodes(BizConstants.CodeType.VIP_TYPE, null);
            m.addAttribute("vipTypeList", vipType);
            return "/bil/vip/vipManageAdd";
        }
        if ("detail".equals(flag)) {
            VipBo bo = vipService.getVipConfigById(vipId);
            List<CodeBO> vipType = codeService.getStandardCodes(BizConstants.CodeType.VIP_TYPE, null);
            for (CodeBO codeBO : vipType) {
                if (bo.getVipType().equals(codeBO.getCodeValue())) {
                    bo.setVipTypeName(codeBO.getCodeName());
                }
            }
            m.addAttribute("vipConfigForm", bo);

            return "/bil/vip/vipManageInfo";
        }
        if ("modify".equals(flag)) {
            VipBo bo = vipService.getVipConfigById(vipId);
            List<CodeBO> vipType = codeService.getStandardCodes(BizConstants.CodeType.VIP_TYPE, null);
            for (CodeBO codeBO : vipType) {
                if (bo.getVipType().equals(codeBO.getCodeValue())) {
                    bo.setVipTypeName(codeBO.getCodeName());
                }
            }
            m.addAttribute("vipConfigForm", bo);

            return "/bil/vip/vipManageEdit";
        }
        return null;
    }

    @RequestMapping(value = "/vipConfigSave", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject vipConfigSave(VipCondition condition) {
        String mark = "success";
        logger.debug("vip save info:" + JSONObject.toJSONString(condition));
        try {
            VipBo bo = new VipBo();
            BeanUtils.copyProperties(condition, bo);
            bo.setVipId(UUID.randomUUID().toString().replace("-", "").substring(0, 16));
            bo.setStatus("1");
            vipService.insertVipConfig(bo);
        } catch (Exception e) {
            mark = "error";
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);
    }

    @RequestMapping(value = "/vipConfigUpdate", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject vipConfigUpdate(VipCondition condition) {
        String mark = "success";
        try {
            VipBo bo = new VipBo();
            BeanUtils.copyProperties(condition, bo);
            vipService.updateVipConfig(bo);
        } catch (Exception e) {
            mark = "error";
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);
    }

    @Override
    protected VipCondition initCondition() {
        return new VipCondition();
    }

}
