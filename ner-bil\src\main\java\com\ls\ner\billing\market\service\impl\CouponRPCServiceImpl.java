package com.ls.ner.billing.market.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.market.service.ICouponRPCService;
import com.ls.ner.billing.api.market.vo.CouponPutRpcBo;
import com.ls.ner.billing.market.bo.BillBo;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponContentBo;
import com.ls.ner.billing.market.bo.CouponPutBo;
import com.ls.ner.billing.market.bo.CouponPutDetBo;
import com.ls.ner.billing.market.dao.ICouponDao;
import com.ls.ner.billing.market.dao.ICouponPutDao;
import com.ls.ner.billing.market.dao.ICouponRPCDao;
import com.ls.ner.billing.market.service.ICouponPutService;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.dao.IMarketActDao;
import com.ls.ner.billing.vip.dao.IVipSaveRecordDao;
import com.ls.ner.cust.api.service.ICustXpRpcService;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.util.AssertUtil;
import com.ls.ner.util.ListSort.ListSort;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.MergeUtil;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.ws.util.JsonUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 描述:RPC05-11 优惠券RPC接口 [实现]
 * CouponRPCServiceImpl.java
 * 作者：biaoxiangd
 * 创建日期：2017-06-06 17:23
 **/
@Service(target = {ServiceType.RPC}, value = "couponRpcService")
public class CouponRPCServiceImpl implements ICouponRPCService {

    private static final Logger logger = LoggerFactory.getLogger(CouponRPCServiceImpl.class);

    @Autowired(required = true)
    private ICouponRPCDao dao;

    @Autowired(required = true)
    private ICouponDao couponDao;

    @Autowired(required = true)
    private ICouponPutDao couponPutDao;

    @Autowired(required=true)
    private IMarketActDao marketActDao;

    @Autowired
    private IVipSaveRecordDao saveRecordDao;

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="attachService")
    private IAttachRpcService attachService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value="archivesRpcService")
    private IArchivesRpcService archivesRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value="areaRpcService")
    private IAreaRpcService areaRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value="attachRpcService")
    private IAttachRpcService attachRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "custXpRpcService")
    private ICustXpRpcService custXpRpcService;

    @ServiceAutowired(value = "accuCouponRpcService", serviceTypes = ServiceType.RPC)
    private ICouponRpcService couponRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "bilCouponPutService")
    private ICouponPutService couponPutService;

    /**
     * 描述:RPC05-11-01 优惠券详情
     *
     * @param: map<"cpnList",List<String(spnId)>>
     * @return: map<"cpnList",Map<cpnId,json(couponBO)>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 22:57
     */
    public Map<String, Object> couponInfo(Map<String, Object> map) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (map != null && map.size() > 0) {
            List<Map<String, String>> list = (List<Map<String, String>>) map.get("cpnList");
            if (list != null && list.size() > 0) {
                List<CouponContentBo> couponList = new ArrayList<>();
                if(PublicConstants.YN.TRUE.equals(StringUtil.nullToString(map.get("timeFlag"))) || StringUtil.isNotBlank(map.get("stationId")) || StringUtil.isNotBlank(map.get("amount")) || StringUtil.isNotBlank(map.get("city"))){
                    Map<String,Object> queryMap=new HashMap<>();
                    queryMap.put("stationId",StringUtil.isNotBlank(map.get("stationId"))?map.get("stationId"):"AXXXXXXA");
                    queryMap.put("amount",StringUtil.isNotBlank(map.get("amount"))?map.get("amount"):"0");
                    queryMap.put("city",StringUtil.isNotBlank(map.get("city"))?map.get("city"):"AXXXXXXA");
                    queryMap.put("list",list);
                    if (StringUtil.isNotEmpty(map.get("totalNum"))){
                        queryMap.put("totalNum", Integer.parseInt(StringUtil.getString(map.get("totalNum"))));
                    }
                    if (StringUtil.isNotEmpty(map.get("startNum"))){
                        queryMap.put("startNum", Integer.parseInt(StringUtil.getString(map.get("startNum"))));
                    }
                    queryMap.put("timeFlag",map.get("timeFlag"));
                    couponList=dao.queryCouponListByCondition(queryMap);
                }else{
                    couponList = dao.queryCouponList(list);
                }
//                logger.error("[bil]queryCouponList查询结果集={}",couponList.toString());
                if (couponList != null && couponList.size() > 0) {
                    Map<String, Object> coupopnMap = new HashMap<String, Object>();
                    CodeBO code = null;
                    for (CouponContentBo bo : couponList) {
                        String cpnId = bo.getCpnId();
                        if (StringUtil.isNotBlank(cpnId)) {
                            Map<String, String> ccMap = new HashMap<String, String>();
                            ccMap.put("cpnId", cpnId);
                            ccMap.put("cpnName", bo.getCpnName());
                            ccMap.put("timeCondBeg",bo.getTimeCondEnd());
                            String cpnType = bo.getCpnType();
                            ccMap.put("cpnType", cpnType);
                            if (StringUtil.isNotBlank(cpnType)) {
                                code = codeService.getStandardCode("cpnType", cpnType, null);
                                if (code != null) {
                                    ccMap.put("cpnTypeName", code.getCodeName());
                                }
                            }
                            String busiType = bo.getProdBusiType();
                            if (StringUtil.isNotBlank(busiType)) {
                                code = codeService.getStandardCode("orderType", busiType, null);
                                if (code != null) {
                                    ccMap.put("prodBusiType", code.getCodeName());
                                }
                            }
                            String cpnAmtUnit = "元";
                            String cpnAmt = "0";
                            String spAmt = "0";
                            if (StringUtil.isNotBlank(bo.getCpnAmt())) {
                                Map<String, String> calcMap = BeanUtils.describe(bo);
                                if (StringUtil.isNotBlank(map.get("billAmt"))){
                                    calcMap.put("billAmt",map.get("billAmt").toString());
                                }
                                if (StringUtil.isNotBlank(map.get("couponFlag"))){
                                    calcMap.put("couponFlag",map.get("couponFlag").toString());
                                }
                                if (StringUtil.isNotBlank(map.get("amount"))){
                                    calcMap.put("amount",map.get("amount").toString());
                                }
                                Map<String, Object> retCalcMap = this.calcDctType(calcMap);
                                if (retCalcMap != null && retCalcMap.size() > 0) {
                                    if ("0".equals(retCalcMap.get("outCode"))) {
                                        cpnAmtUnit = (String) retCalcMap.get("cpnAmtUnit");
                                        spAmt = (String) retCalcMap.get("spAmt");
                                        if (StringUtil.isNotBlank(map.get("couponFlag")) && "true".equals(map.get("couponFlag")) && StringUtil.isNotBlank(retCalcMap.get("couponAmt"))){
                                            calcMap.put("cpnAmt", (String) retCalcMap.get("couponAmt"));
                                        }else{
                                            calcMap.put("cpnAmt", (String) retCalcMap.get("spAmt"));
                                        }
                                        retCalcMap = this.calcPreci(calcMap);
                                        if (retCalcMap != null && retCalcMap.size() > 0) {
                                            if ("0".equals(retCalcMap.get("outCode"))) {
                                                cpnAmt = (String) retCalcMap.get("spAmt");
                                            }
                                        }
                                    }
                                }
                            }
                            if (StringUtil.isNotBlank(map.get("couponFlag")) && "true".equals(map.get("couponFlag"))){
                                ccMap.put("cpnAmt", spAmt + cpnAmtUnit);
                            }else{
                                ccMap.put("cpnAmt", cpnAmt + cpnAmtUnit);
                            }
                            ccMap.put("compareCpnAmt", cpnAmt);
                            ccMap.put("cpnMarks", bo.getCpnMarks());
                            coupopnMap.put(cpnId, JsonUtil.toJson(ccMap));
                        }
                    }
                    retMap.put("cpnList", coupopnMap);
                } else {
//                    throw new RuntimeException("RPC05-11-01【优惠券详情】接口未查询到优惠券信息");
                }
            } else {
                throw new RuntimeException("RPC05-11-01【优惠券详情】接口优惠券ID参数为空");
            }
        } else {
            throw new RuntimeException("RPC05-11-01【优惠券详情】接口参数为空");
        }
        return retMap;
    }

    /**
     * 描述:RPC05-11-02 验证优惠券可用性
     *
     * @param: map<"cpnList",List<String(spnId)>>
     * @return: map<"cpnList",Map<cpnId,json(couponBO)>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 22:57
     */
    @Override
    public Map<String, Object> couponValid(Map<String, Object> map) throws Exception {
        List<Object> cpnObjList = new ArrayList<Object>();
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (map != null && map.size() > 0) {
            List<Map<String, String>> list = (List<Map<String, String>>) map.get("cpnList");
            String billAmt = (String) map.get("billAmt");
            String prodBusiType = (String) map.get("prodBusiType");
            if (StringUtil.isBlank(billAmt)) {
                throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口参数【billAmt】为空");
            }
            if (list != null && list.size() > 0) {
                List<CouponContentBo> couponList = dao.queryCouponList(list);
                if (couponList != null && couponList.size() > 0) {
//                    Map<String, Object> coupopnMap = new HashMap<String, Object>();
//                    CodeBO code = null;
                    for (CouponContentBo bo : couponList) {
                        String cpnId = bo.getCpnId();
                        if (StringUtil.isNotBlank(cpnId)) {
                            Map<String, String> ccMap = new HashMap<String, String>();
                            ccMap.put("cpnId", cpnId);
                            ccMap.put("cpnName", bo.getCpnName());
                            ccMap.put("cpnMarks", bo.getCpnMarks());
                            ccMap.put("cpnAmt", bo.getCpnAmt());
                            String spAmt = "0.00";
                            Map<String, String> calcMap = new HashMap<String, String>();
                            calcMap.put("billAmt", billAmt);
                            calcMap.put("cpnId", cpnId);
                            calcMap.put("prodBusiType", prodBusiType);
                            calcMap.put("paymentType", StringUtil.getString(map.get("paymentType")));
                            Map<String, Object> outCalcMap = this.calc(calcMap);
                            if (outCalcMap != null && outCalcMap.size() > 0) {
                                String outCode = (String) outCalcMap.get("outCode");
                                if ("0".equals(outCode)) {
                                    spAmt = (String) outCalcMap.get("spAmt");
                                }
                            }
                            ccMap.put("spAmt", spAmt);
                            cpnObjList.add(ccMap);
                        }
                    }
                    retMap.put("cpnList", cpnObjList);
                } else {
//                    throw new RuntimeException("RPC05-11-01【优惠券详情】接口未查询到优惠券信息");
                }
            } else {
                throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口优惠券ID参数为空");
            }
        } else {
            throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口参数为空");
        }
        return retMap;
    }

    /**
     * 描述:RPC05-11-03 优惠券优惠计算
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 17:18
     */
    @Override
    public Map<String, Object> couponCalc(Map<String, Object> map) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String spAmt = "0"; // 优惠金额
        int flag = 0; // 数据处理结果标志
        if (map != null && map.size() > 0) {
            String orderNo = (String) map.get("orderNo");
            String cpnId = (String) map.get("cpnId");
            String prodBusiType = (String) map.get("prodBusiType");
            String billAmt = (String)map.get("billAmt");
            if (StringUtil.isNotBlank(orderNo) && StringUtil.isNotBlank(cpnId)) {
                // 验证订单结果中的订单编号是否存在
               if(StringUtil.isBlank(prodBusiType)){
                   throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口中产品业务类【prodBusiType】不能为空");
               }
               // add biaoxiangd 2017-09-27 区分租车充电bill数据来源
                List<BillBo> billList = null;
                // 租车
               if("01".equals(prodBusiType)){
                   billList = dao.queryBillInfo(orderNo);
               }else{
                   if(StringUtil.isBlank(billAmt)){
                       throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口中整的金额【billAmt】不能为空");
                   }
                   // 充电
                   billList = new ArrayList<BillBo>();
                   BillBo billBo = new BillBo();
                   billBo.setBillAmt(billAmt);
                   //@20181213 add 添加服务费
                   billBo.setServiceAmt(StringUtil.getString(map.get("serviceAmt")));
                   billList.add(billBo);
               }
                if (billList != null && billList.size() > 0) {
                    BillBo billBo = billList.get(0);
                    if (billBo != null) {
                        // 计算优惠券
                        Map<String, String> calcMap = BeanUtils.describe(billBo);
                        calcMap.put("cpnId", cpnId);
                        calcMap.put("prodBusiType", prodBusiType);
                        logger.info("优惠卷计算入参：{}",JSON.toJSONString(calcMap));
                        Map<String, Object> outCalcMap = this.calc(calcMap);
                        if (outCalcMap != null && outCalcMap.size() > 0) {
                            //@20181226  add  返回优惠券类型，用于馈赠金只能抵扣服务费的改造
                            retMap.put("cpnDctType", outCalcMap.get("cpnDctType"));
                            String outCode = (String) outCalcMap.get("outCode");
                            logger.debug("=======验证优惠券是否可用返回outCalcMap:"+outCalcMap);
                            if ("0".equals(outCode)) {
                                spAmt = (String) outCalcMap.get("spAmt");
                                // 更新 B_BILL.SP_T_AMT
                                flag = dao.updateBill(spAmt, orderNo);
                                // 存储 B_BILL_COUPON
                                billBo.setCpnId(cpnId);
                                billBo.setSpAmt(spAmt);
                                billBo.setOrderNo(orderNo);
                                billBo.setCalcProcess((String) outCalcMap.get("outMsg"));
                                flag = dao.insertBillCoupon(billBo);
                            }
                        }
                    } else {
                        throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口中订单编号【orderNo】获取订单信息为空");
                    }
                } else {
                    throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口中订单编号【orderNo】获取订单集合信息为空");
                }
            } else {
                throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口参数中订单编号【orderNo】和优惠券标识【cpnId】都为空");
            }
//            try {
//                //根据订单号更新会员优惠券已省金额
//                VipSaveRecordBo bo = new VipSaveRecordBo();
//                bo.setOrderNo(orderNo);
//                CouponBo couponBo = dao.queryCouponInfo(cpnId, prodBusiType);
//                if (couponBo.getCpnType().equals("01")){
//                    //现金券
//                    bo.setCashAmt(new BigDecimal(spAmt));
//                }else if (couponBo.getCpnType().equals("02")){
//                    bo.setDiscountAmt(new BigDecimal(spAmt));
//                }
//                saveRecordDao.updateByOrder(bo);
//            } catch (Exception e) {
//                logger.error("更新用户会员已省出错============>", e);
//            }

        } else {
            throw new RuntimeException("RPC05-11-03【优惠券优惠计算】接口参数为空");
        }
        retMap.put("spAmt", spAmt);
        return retMap;
    }

    /**
     * 描述:优惠券计算
     *
     * @param: [cpnId]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 18:15
     */
    private Map<String, Object> calc(Map<String, String> map) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        String billAmt = map.get("billAmt");
        String cpnId = map.get("cpnId");
        String prodBusiType = map.get("prodBusiType");
        if (StringUtil.isBlank(prodBusiType)) {
            prodBusiType = "";
        }
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok", spAmt = "0";
        //CPN_DCT_TYPE = 0100,DCT_CALC_METHOD= 01 CPN_STATE = 1,条件
        if (StringUtil.isNotBlank(cpnId)) {
            CouponBo bo = dao.queryCouponInfo(cpnId, prodBusiType);
            logger.info("优惠卷查询结果：{}",JSON.toJSONString(bo));
            if (bo != null) {
                //@20181215  add
                if (BillConstants.CpnDctType.SERVICE_AMT.equals(bo.getDctType())){
                    if (StringUtil.isBlank(map.get("serviceAmt")) && "prepay".equals(map.get("paymentType"))){
                        billAmt = map.get("billAmt");
                    } else {
                        billAmt = map.get("serviceAmt");
                    }
                }
                String cpnAmt = bo.getCpnAmt();
                if (StringUtil.isBlank(cpnAmt)) {
                    retMap.put("spAmt", "0");
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "面额/折扣【cpnAmt】数据为空");
                    return retMap;
                }
                // 优惠条件
                String dctCondFlag = bo.getDctCondFlag();// 优惠条件类型 1无条件 2条件
                String dctCondAreaFlag = bo.getDctCondAreaFlag();// 优惠区域限制 1无限制 2有限制

                if (StringUtil.isBlank(dctCondFlag)) {
                    dctCondFlag = "1";
                }
                if (StringUtil.isBlank(dctCondAreaFlag)) {
                    dctCondAreaFlag = "1";
                }
                Map<String, String> paraMap = BeanUtils.describe(bo);
                paraMap.put("billAmt", billAmt);
                paraMap.put("cpnId", cpnId);
                paraMap.put("cpnAmt", cpnAmt);

                if ("2".equals(dctCondFlag)) {
                    retMap = this.calcDctCond(paraMap);
                    logger.debug("判断优惠券是否满足使用条件出参：" , JSON.toJSONString(retMap));
                    outCode = (String) retMap.get("outCode");
                    if ("0".equals(outCode)) {
                        paraMap.put("cpnAmt", (String) retMap.get("spAmt"));
                    } else {
                        return retMap;
                    }
                }
                if ("2".equals(dctCondAreaFlag)) {
                    paraMap.put("city", map.get("city"));
                    paraMap.put("stationId", map.get("stationId"));
                    retMap = this.calcDctAreaCond(paraMap);
                    if (!"0".equals(retMap.get("outCode"))) {
                        return retMap;
                    }
                }
                retMap = this.calcDctType(paraMap);
                logger.info("优惠卷计算结果：{}",JSON.toJSONString(retMap));
                outCode = (String) retMap.get("outCode");
                if ("0".equals(outCode)) {
                    String cpnAmtUnit = StringUtil.getString(retMap.get("cpnAmtUnit"));
                    paraMap.put("cpnAmt", (String) retMap.get("spAmt"));
                    retMap = this.calcPreci(paraMap);
                    retMap.put("cpnAmtUnit",cpnAmtUnit);
                    outCode = (String) retMap.get("outCode");
                    //@20181213 add 判断优惠金额spAmt不能大于被优惠金额billAmt
                    String returnAmt = StringUtil.getString(retMap.get("spAmt"));
                    if(MathUtils.compareTo(returnAmt,billAmt)==1) {
                        retMap.put("spAmt", billAmt);
                    }
                    //@20181226  add
                    retMap.put("cpnDctType", bo.getDctType());
                    return retMap;
                }
            } else {
                retMap.put("spAmt", "0");
                retMap.put("outCode", "-1");
                retMap.put("outMsg", "优惠券信息【dao.queryCouponInfo】为空");
            }
        } else {
            retMap.put("spAmt", "0");
            retMap.put("outCode", "-1");
            retMap.put("outMsg", "优惠券标识【cpnId】为空");
        }
        return retMap;
    }




    /**
     * @param map
     * @description 地区判断重写
     * <AUTHOR>
     * @create 2019-05-26 16:17:40
     */
    private Map<String, Object> myCalc(Map<String, String> map) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        String billAmt = map.get("billAmt");
        String cpnId = map.get("cpnId");
        String prodBusiType = map.get("prodBusiType");
        if (StringUtil.isBlank(prodBusiType)) {
            prodBusiType = "";
        }
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok", spAmt = "0";
        if (StringUtil.isNotBlank(cpnId)) {
            CouponBo bo = dao.queryCouponInfo(cpnId, prodBusiType);
            if (bo != null) {
                //@20181215  add
                if (BillConstants.CpnDctType.SERVICE_AMT.equals(bo.getDctType())){
                    logger.debug("==========================服务费============");
                    billAmt = map.get("serviceAmt");
                }
                String cpnAmt = bo.getCpnAmt();
                if (StringUtil.isBlank(cpnAmt)) {
                    retMap.put("spAmt", "0");
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "面额/折扣【cpnAmt】数据为空");
                    return retMap;
                }
                // 优惠条件
                String dctCondFlag = bo.getDctCondFlag();// 优惠条件类型 1无条件 2条件
                if (StringUtil.isBlank(dctCondFlag)) {
                    dctCondFlag = "1";
                }

                Map<String,Object> inMap = new HashMap();
                inMap.put("cpnId",bo.getCpnId());
                List<Map<String,Object>> couponInfos = dao.queryMyCouponInfo(inMap);
                if (couponInfos == null) {
                    retMap.put("spAmt", "0");
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "优惠券信息【dao.queryMyCouponInfo】为空");
                }

                bo.setDctCondBuild(StringUtil.nullToString(couponInfos.get(0).get("dctCondBuild")));

                Map<String, String> paraMap = BeanUtils.describe(bo);
                paraMap.put("billAmt", billAmt);
                paraMap.put("cpnId", cpnId);
                paraMap.put("cpnAmt", cpnAmt);

                if ("2".equals(dctCondFlag)) {
                    retMap = this.calcDctCond(paraMap);
                    logger.debug("判断优惠券是否满足使用条件出参：" , JSON.toJSONString(retMap));
                    outCode = (String) retMap.get("outCode");
                    if ("0".equals(outCode)) {
                        paraMap.put("cpnAmt", (String) retMap.get("spAmt"));
                    } else {
                        return retMap;
                    }
                }
                logger.debug("-------------------------area judge-------------");
                //地区判断
                paraMap.put("stationId", map.get("stationId"));
                retMap = this.myCalcDctAreaCond(paraMap);
                if (!"0".equals(retMap.get("outCode"))) {
                    return retMap;
                }
                logger.debug("-------------------------youhui judge----------------------");
                retMap = this.calcDctType(paraMap);
                outCode = (String) retMap.get("outCode");
                if ("0".equals(outCode)) {
                    String cpnAmtUnit = StringUtil.getString(retMap.get("cpnAmtUnit"));
                    paraMap.put("cpnAmt", (String) retMap.get("spAmt"));
                    retMap = this.calcPreci(paraMap);
                    retMap.put("cpnAmtUnit",cpnAmtUnit);
                    outCode = (String) retMap.get("outCode");
                    //@20181213 add 判断优惠金额spAmt不能大于被优惠金额billAmt
                    String returnAmt = StringUtil.getString(retMap.get("spAmt"));
                    if(MathUtils.compareTo(returnAmt,billAmt)==1) {
                        retMap.put("spAmt", billAmt);
                    }
                    //@20181226  add
                    retMap.put("cpnDctType", bo.getDctType());
                    return retMap;
                }
            } else {
                retMap.put("spAmt", "0");
                retMap.put("outCode", "-1");
                retMap.put("outMsg", "优惠券信息【dao.queryCouponInfo】为空");
            }
        } else {
            retMap.put("spAmt", "0");
            retMap.put("outCode", "-1");
            retMap.put("outMsg", "优惠券标识【cpnId】为空");
        }
        return retMap;
    }




    /**
     * 描述:优惠类别 计算
     *
     * @param: [Map<String,String>]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 20:41
     */
    public Map<String, Object> calcDctType(Map<String, String> map) {
        logger.debug(">>>>>calcDctType  map:{}",JSON.toJSONString(map));
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok";
        String billAmt = map.get("billAmt");
        String dctCalcMethod = map.get("dctCalcMethod");
        String dctType = map.get("dctType");
        String dctCondFlag = map.get("dctCondFlag");
        if (StringUtil.isBlank(dctCondFlag)) {
            dctCondFlag = "1";
        }
        String cpnAmt = map.get("cpnAmt");
        String cpnType = map.get("cpnType");
        String couponFlag = map.get("couponFlag");
        String amount = map.get("amount");
        String cpnAmtUnit = "元";
        if (StringUtil.isBlank(dctCalcMethod) && StringUtil.isBlank(dctType) && StringUtil.isBlank(cpnAmt)) {
            retMap.put("outCode", "-1");
            retMap.put("outMsg", "计算优惠类别参数【dctCalcMethod/dctType/cpnAmt】为空");
            return retMap;
        }

        // 01减免、02折扣
        if ("01".equals(dctCalcMethod)) {
            // 0100整单金额、0101计价数量、0102计价金额、0201定价费率
            String spAmt="0";
            switch (dctType) {
                case "0100":
                case BillConstants.CpnDctType.SERVICE_AMT:
                    if ("02".equals(cpnType) && StringUtil.isNotBlank(billAmt)){
                        //优惠条件类型 1无条件 2条件
                        if("1".equals(dctCondFlag)){
                            spAmt =  calculateDiscountedPrice(billAmt, cpnAmt);
                            spAmt = calculateDiscountedPriceNext(billAmt,spAmt);
                            retMap.put("spAmt", spAmt);
                        }else{
                            //spAmt = MathUtils.multiply(billAmt, MathUtils.subtract("1", MathUtils.divide(cpnAmt, "10")));
                            spAmt =  calculateDiscountedPrice(billAmt, cpnAmt);
                            spAmt = calculateDiscountedPriceNext(billAmt,spAmt);
                            if (map.containsKey("maximumDiscountAmt")&& ObjectUtil.isNotEmpty(map.get("maximumDiscountAmt"))){
                                spAmt=String.valueOf(Math.min(Double.parseDouble(spAmt),Double.parseDouble(map.get("maximumDiscountAmt"))));
                            }
                            retMap.put("spAmt", spAmt);
                        }
                    }else{
                        retMap.put("spAmt", cpnAmt);
                    }
                    break;
                case "0101":
                    retMap.put("spAmt", "0");
                    outCode = "-1";
                    outMsg = "计价数量【0101】算法暂不支持";
                    break;
                case "0102":
                    retMap.put("spAmt", "0");
                    outCode = "-1";
                    outMsg = "计价金额【0102】算法暂不支持";
                    break;
                default:
                    retMap.put("spAmt", "0");
                    outCode = "-1";
                    outMsg = "计价金额【" + dctType + "】算法暂不支持";
                    break;
            }
        } else if ("02".equals(dctCalcMethod)) {
            switch (dctType) {
                case "0201":
                    retMap.put("spAmt", "0");
                    outCode = "-1";
                    outMsg = "定价费率【0201】算法暂不支持";
                    break;
                default:
                    retMap.put("spAmt", "0");
                    outCode = "-1";
                    outMsg = "定价费率【" + dctType + "】算法暂不支持";
                    break;
            }
        }
        // 单位转换
        if("02".equals(cpnType)){
            cpnAmtUnit = "折";
        }
        if("true".equals(couponFlag) && "02".equals(cpnType) && StringUtil.isNotBlank(amount)){
            String a = calculateDiscountedPrice(billAmt, cpnAmt);
            String b = calculateDiscountedPriceNext(billAmt,a);
            retMap.put("couponAmt", b);
        }
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        retMap.put("cpnAmtUnit", cpnAmtUnit);
        return retMap;
    }

    public static String calculateDiscountedPrice(String originalPrice, String discountRate) {
        return MathUtils.multiply(originalPrice , MathUtils.divide(discountRate , "10"));
    }

    public static String calculateDiscountedPriceNext(String billAmt, String spAmt) {
        return MathUtils.subtract(billAmt , spAmt);
    }

    /**
     * 描述: 精度计算
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 21:00
     */
    private Map<String, Object> calcPreci(Map<String, String> map) {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok";
        String cpnAmt = map.get("cpnAmt");
        String calcPrecision = map.get("calcPrecision");// 1向上取整、2向下取整、3四舍五入
        String calcPrecisionDigits = map.get("calcPrecisionDigits");// 0 整数、1：1位小数、2与参数1类推
        if (StringUtil.isBlank(cpnAmt)) {
            retMap.put("outCode", "-1");
            retMap.put("outMsg", "金额【cpnAmt】精度换算参数为空");
            return retMap;
        }
        if (StringUtil.isBlank(calcPrecision)) {
            calcPrecision = "3";
        }
        if (StringUtil.isBlank(calcPrecisionDigits)) {
            calcPrecisionDigits = "0";
        }
        int iCalcPrecisionDigits = 0;
        if (StringUtils.isNumeric(calcPrecisionDigits)) {
            iCalcPrecisionDigits = Integer.parseInt(calcPrecisionDigits);
        }
        BigDecimal bigAmt = new BigDecimal(cpnAmt);
        // 1向上取整、2向下取整、3四舍五入
        switch (calcPrecision) {
            case "1":
                bigAmt = bigAmt.setScale(iCalcPrecisionDigits, BigDecimal.ROUND_UP);
                break;
            case "2":
                bigAmt = bigAmt.setScale(iCalcPrecisionDigits, BigDecimal.ROUND_DOWN);
                break;
            case "3":
                bigAmt = bigAmt.setScale(iCalcPrecisionDigits, BigDecimal.ROUND_HALF_UP);
                break;
            default:
                bigAmt = bigAmt.setScale(iCalcPrecisionDigits, BigDecimal.ROUND_HALF_UP);
                break;
        }
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        retMap.put("spAmt", bigAmt.toEngineeringString());
        return retMap;
    }

    /**
     * 描述:有条件的计算
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 21:30
     */
    @Override
    public Map<String, Object> calcDctCond(Map<String, String> map) {
        logger.debug(">>>>>calcDctCond   map:{}",map);
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok", cpnAmt = "0";
        if (map != null && map.size() > 0) {
            String dctCondType = map.get("dctCondType");// 优惠条件类型 0110服务费折扣、0100：整单折扣
            String dctCondValue = map.get("fullReductionAmt"); // 最低消费金额
            String billAmt = map.get("billAmt");// 订单总费用金额
            cpnAmt = map.get("cpnAmt");// 优惠面值
            if (StringUtil.isBlank(dctCondType)) {
                dctCondType = "02";
            }
            if (StringUtil.isBlank(billAmt)) {
                billAmt = "0";
            }
            if (StringUtil.isBlank(cpnAmt)) {
                cpnAmt = "0";
            }
            if (StringUtil.isBlank(dctCondValue)) {
                dctCondValue = "0";
            }
            BigDecimal bigBillAmt = new BigDecimal(billAmt);
            BigDecimal bigCondValue = new BigDecimal(dctCondValue);
            logger.debug("=======================dctCondType:"+bigCondValue+"====================bigBillAmt:"+bigBillAmt );

            // 优惠条件类型 0110：服务费、0100：整单金额
            switch (dctCondType) {
                case "0110":
                    // 服务费小于最低消费金额
                    if (bigBillAmt.doubleValue() < bigCondValue.doubleValue()){
                        cpnAmt = "0";
                        outCode = "-1";
                        outMsg = "服务费不满足最低消费金额";
                    }
                    break;
                case "0100":
                    // 订单金额小于最低消费金额
                    if (bigBillAmt.doubleValue() < bigCondValue.doubleValue()){
                        cpnAmt = "0";
                        outCode = "-1";
                        outMsg = "订单金额不满足最低消费金额";
                    }
                    break;
                default:
                    cpnAmt = "0";
                    outCode = "-1";
                    outMsg = "优惠条件类型-计价金额【" + dctCondType + "】算法暂不支持";
                    break;
            }
        }
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        retMap.put("spAmt", cpnAmt);
        return retMap;
    }
    /**
     * @param map
     * @description 优惠券区域条件计算
     * <AUTHOR>
     * @create 2018-06-26 14:46:15
     */
    public Map<String, Object> calcDctAreaCond(Map<String, String> map) {
        logger.debug("优惠券区域条件计算入参："+map);
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok";
        if (map != null && map.size() > 0 && !StringUtil.isNotBlank(map.get("stationId"))) {
            String stationId = StringUtil.getString(map.get("stationId"));
            String city = StringUtil.getString(map.get("city"));
            String dctCondCity = map.get("dctCondCity"); // 优惠券可使用城市
            String dctCondStation = map.get("dctCondStation"); // 优惠券可使用站点
            if (StringUtil.isBlank(city)) {
                Map<String, Object> iMap = new HashMap<String, Object>();
                iMap.put("stationId",stationId);
                Map<String,Object> sMap = archivesRpcService.queryStation(iMap);
                if(sMap!=null){
                    city = StringUtil.isEmpty(sMap.get("city"))?"":StringUtil.getString(sMap.get("city"));
                }
            }
            if (StringUtil.isNotBlank(dctCondCity)&&StringUtil.isNotBlank(city)) {
                if(!dctCondCity.contains(city)){
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "不满足使用城市条件！");
                    return retMap;
                }
            }
            if (StringUtil.isNotBlank(dctCondStation)) {
                if(!dctCondStation.contains(stationId)){
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "不满足使用站点条件！");
                    return retMap;
                }
            }
        }
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        logger.debug("优惠券区域条件计算出参："+retMap);
        return retMap;
    }


    /**
     * @param map
     * @description  区域条件判断
     * <AUTHOR>
     * @create 2019-05-27 01:17:56
     */
    public Map<String, Object> myCalcDctAreaCond(Map<String, String> map) {
        logger.debug("优惠券区域条件计算入参："+map);
        Map<String, Object> retMap = new HashMap<String, Object>();
        String outCode = "0", outMsg = "ok";

        if (map != null && map.size() > 0 && !StringUtil.isNotBlank(map.get("stationId"))) {

            //订单可使用运营商，无运营商限制为1
            String dctCondBuild = map.get("dctCondBuild");
            // 优惠券可使用城市,无城市限制为1
            String dctCondCity = map.get("dctCondCity");
            // 优惠券可使用站点，无站点限制为1
            String dctCondStation = map.get("dctCondStation");

            String stationId = StringUtil.getString(map.get("stationId"));
            String city = "";
            String buildId = "";
            Map<String, Object> iMap = new HashMap<String, Object>();
            iMap.put("stationId",stationId);
            Map<String,Object> sMap = archivesRpcService.queryOperInfoByStationId(iMap);
            if(sMap!=null){
                city = StringUtil.isEmpty(sMap.get("city"))?"":StringUtil.getString(sMap.get("city"));
                buildId = StringUtil.isEmpty(sMap.get("operatorId"))?"":StringUtil.getString(sMap.get("operatorId"));
            }
            if (StringUtil.isNotBlank(dctCondBuild)&&!dctCondBuild.equals("1")&&StringUtil.isNotBlank(buildId)) {
                if(!dctCondCity.contains(buildId)){
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "不满足使用运营商条件！");
                    return retMap;
                }
            }
            if (StringUtil.isNotBlank(dctCondCity)&&!dctCondCity.equals("1")&&StringUtil.isNotBlank(city)) {
                if(!dctCondCity.contains(city)){
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "不满足使用城市条件！");
                    return retMap;
                }
            }
            if (StringUtil.isNotBlank(dctCondStation)&&!dctCondStation.equals("1")) {
                if(!dctCondStation.contains(stationId)){
                    retMap.put("outCode", "-1");
                    retMap.put("outMsg", "不满足使用站点条件！");
                    return retMap;
                }
            }
        }
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        logger.debug("优惠券区域条件计算出参："+retMap);
        return retMap;
    }



    /**
     * 描述:REST05-11 优惠券
     * REST05-11-01 可领用的优惠券
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    @Override
    public List<Map<String, Object>> couponsRestGET(Map<String, String> map) throws Exception {
        return couponService.couponsRestGET(map);
    }

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-02 优惠券领取
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    public Map<String, Object> couponsRestPOST(Map<String, String> map) throws Exception {
        return couponService.couponsRestPOST(map);
    }

    /**
     * 描述: RPC05-11-04验证优惠券可用性（订单）
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/10 3:15
     */
    @Override
    public List<Map<String, Object>> couponValidByOrder(Map<String, Object> map) throws Exception {
        List<Map<String, Object>> retList = new ArrayList<Map<String, Object>>();
        if (map != null && map.size() > 0) {
            String orderNo = String.valueOf(map.get("orderNo"));
            // add biaoxiangd 2017-09-27 新增 prodBusiType,billAmt参数
            String billAmt = String.valueOf(map.get("billAmt"));
            String prodBusiType = String.valueOf(map.get("prodBusiType"));
            if(StringUtil.isBlank(prodBusiType)){
                throw new RuntimeException("[RPC05-11-04验证优惠券可用性（订单）]接口入参[prodBusiType]不能为空");
            }

            List<Map<String, String>> cpnList = (List<Map<String, String>>) map.get("cpnList");
            if (StringUtil.isNotBlank(orderNo) && cpnList != null && cpnList.size() > 0) {
                List<BillBo> billList = null;
                // 租车
                if("01".equals(prodBusiType)){
                    billList = dao.queryBillInfo(orderNo);
                }else{
                    // 充电
                    if(StringUtil.isBlank(billAmt)){
                        throw new RuntimeException("[RPC05-11-04验证优惠券可用性（订单）]接口入参[billAmt]不能为空");
                    }
                    billList = new ArrayList<BillBo>();
                    BillBo billBo = new BillBo();
                    billBo.setBillAmt(billAmt);
                    billList.add(billBo);
                }
                if (billList != null && billList.size() > 0) {
                    BillBo billBo = billList.get(0);
                    if (billBo != null) {
                        // 获取优惠券信息
                        List<String> cpnIdList = new ArrayList<>();
                        for (Map<String, String> stringMap : cpnList) {
                            cpnIdList.add(stringMap.get("cpnId"));
                        }
                        MarketCondition con = new MarketCondition();
                        con.setCpnIdList(cpnIdList);
                        List<CouponBo> coupons = couponDao.getCoupons(con);
                        if (CollectionUtils.isNotEmpty(coupons)) {
                            for (CouponBo bo : coupons) {
                                Map<String, Object> retMap = new HashMap<String, Object>();
                                String cpnId = bo.getCpnId();
                                if (StringUtil.isBlank(cpnId)) {
                                    continue;
                                }

                                retMap.put("cpnId", cpnId);
                                retMap.put("cpnName", bo.getCpnName());
                                retMap.put("cpnMarks", bo.getCpnMarks());
                                retMap.put("cpnAmt", bo.getCpnAmt());

                                // 计算优惠券
                                String spAmt = "0.00";
                                String cpnAmtUnit = "元";
                                String isUseFlag = "1";//0可用 1不可用
                                Map<String, String> calcMap = BeanUtils.describe(billBo);
                                calcMap.put("cpnId", bo.getCpnId());
                                Map<String, Object> outCalcMap = this.calc(calcMap);
                                if (outCalcMap != null && outCalcMap.size() > 0) {
                                    String outCode = (String) outCalcMap.get("outCode");
                                    if ("0".equals(outCode)) {
                                        spAmt = (String) outCalcMap.get("spAmt");
                                        cpnAmtUnit = (String) outCalcMap.get("cpnAmtUnit");
                                        isUseFlag = "0";
                                    }
                                }
                                retMap.put("spAmt", spAmt + cpnAmtUnit);
                                retMap.put("isUseFlag", isUseFlag);
                                retList.add(retMap);
                            }
                        } else {
                            throw new RuntimeException("优惠券信息不存在");
                        }
                    } else {
                        throw new RuntimeException("当前订单[orderNo]无数据，请核查");
                    }
                } else {
                    throw new RuntimeException("当前订单[orderNo]不存在，请核查");
                }
            } else {
                throw new RuntimeException("接口入参错误，未获取[orderNo/cpnList]参数");
            }
        } else {
            throw new RuntimeException("接口入参错误");
        }

        return retList;
    }

    /**
     * 描述:提供给发放功能调用，更新已领属性
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-05 12:07
     */
    public int rpcUpdateCoupon(Map<String, String> map){
        return couponService.rpcUpdateCoupon(map);
    }

    /**
     * @param map 描述:提供给注册送，邀请送发放使用，更新优惠券发放表及优惠券发放明细表
     * @description
     * <AUTHOR>
     * @create 2018-05-24 16:21:10
     */
    @Override
    public void rpcUpdateCouponPutAndDet(Map<String, String> map) {
        logger.debug("==========注册送、邀请送发放入参：" + map);
        try{
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            CouponPutBo bo = new CouponPutBo();
            bo.setCpnId(map.get("cpnId"));
            bo.setPutEmpType(map.get("putEmpType"));
            bo.setPutEmp(map.get("putEmp"));
            bo.setPutTime(df.format(new Date()));
            bo.setPutNum(map.get("putNum"));
            bo.setPutChannel(map.get("putChannel"));
            couponPutDao.insertCouponPut(bo);
            CouponPutDetBo detBo = new CouponPutDetBo();
            detBo.setPutId(bo.getPutId());
            detBo.setPutTime(df.format(new Date()));
            detBo.setCustId(map.get("custId"));
            detBo.setMobile(map.get("mobile"));
            detBo.setPutFlag(map.get("putFlag"));
            couponPutDao.insertCouponPutDet(detBo);
        }catch (Exception e){
            logger.debug("==========注册送、邀请送发放更新表异常：" + e);
        }
    }

    /**
     * @param inMap
     * @description 可用优惠券验证（新增站点城市校验）
     * <AUTHOR>
     * @create 2018-06-26 10:21:37
     */
    @Override
    public List<Map<String, Object>> couponCityAndStationValid(Map<String, Object> inMap) throws Exception {
        logger.debug("可用优惠券验证（新增站点城市校验）入参："+inMap);
        List<Map<String, Object>> cpnList = new ArrayList<Map<String, Object>>();
        String billAmt = AssertUtil.notEmptyForString(inMap.get("billAmt"), "订单金额不能为空");
        String cpnIds = AssertUtil.notEmptyForString(inMap.get("cpnIds"), "优惠券不能为空");
        String busiType = StringUtil.getString(inMap.get("busiType"));
        String city = StringUtil.getString(inMap.get("city"));//优惠券使用城市
        String stationId = StringUtil.getString(inMap.get("stationId"));//优惠券使用站点
        List<Map<String,Object>> cList = new ArrayList<>();
        String[] cpnArr = cpnIds.split(",");
        for(int i = 0; i < cpnArr.length ;i++){
            if(StringUtil.isNotBlank(cpnArr[i])){
                Map map = new HashedMap();
                map.put("cpnId",cpnArr[i].trim());
                cList.add(map);
            }
        }
        List<Map> cpnIdListTime = (List<Map>) inMap.get("cpnIdListTime");
//        MarketCondition con = new MarketCondition();
//        con.setCpnIdList(cList);
//        List<CouponBo> coupons = couponDao.getCoupons(con);

        Map<String,Object> queryMap=new HashMap<>();
        queryMap.put("stationId",stationId);
        queryMap.put("amount",billAmt);
        queryMap.put("city",city);
        queryMap.put("list",cList);
        List<CouponContentBo> couponList = dao.queryCouponListByCondition(queryMap);

        for (Map cpnTime : cpnIdListTime){
            String cpnId = StringUtil.getString(cpnTime.get("cpnId"));
            for (CouponContentBo CouponContentBo: couponList){
                if(cpnId.equals(CouponContentBo.getCpnId())){
                    CouponContentBo.setInvDate(StringUtil.getString(cpnTime.get("invDate")));
                    continue;
                }
            }
        }
        logger.debug("添加失效时间："+ JSON.toJSONString(couponList));
        if (CollectionUtils.isNotEmpty(couponList)) {
            for (CouponContentBo bo : couponList) {

                Map<String, Object> retMap = new HashMap<String, Object>();
                String cpnId = bo.getCpnId();
                if (StringUtil.isBlank(cpnId)) {
                    continue;
                }
                retMap.put("cpnId", cpnId);
                retMap.put("cpnName", bo.getCpnName());
                retMap.put("cpnMarks", bo.getCpnMarks());
                retMap.put("invDate", bo.getInvDate());
                // 计算优惠券
                Map<String, String> calcMap = new HashMap<String, String>();
                calcMap.put("cpnId", cpnId);
                calcMap.put("billAmt",billAmt);
                calcMap.put("prodBusiType",busiType);
                calcMap.put("city",city);
                calcMap.put("stationId",stationId);
                Map<String, Object> outCalcMap = this.calc(calcMap);
                if (outCalcMap != null && outCalcMap.size() > 0) {
                    if ("0".equals(StringUtil.getString(outCalcMap.get("outCode")))) {
                        String spAmt = StringUtil.getString(outCalcMap.get("spAmt"));
                        retMap.put("cpnAmt", spAmt);//经过精度计算过后的优惠券面额
                        BigDecimal bigBillAmt = new BigDecimal(billAmt);
                        BigDecimal bigCpnAmt = new BigDecimal(spAmt);
                        if(bigBillAmt.doubleValue() < bigCpnAmt.doubleValue()){
                            retMap.put("spAmt", bigBillAmt);//优惠券优惠金额
                        }else{
                            retMap.put("spAmt", spAmt);//优惠券优惠金额
                        }
                        cpnList.add(retMap);
                    }
                }
            }
        } else {
            throw new RuntimeException("优惠券信息不存在");
        }
        logger.debug("可用优惠券验证（新增站点城市校验）出参："+ JsonUtil.toJson(cpnList));
        if(cpnList != null && cpnList.size()>0){
            //只有一条不需要排序，大于一条才需要排序
            if(cpnList.size()!=1){
                ListSort<Map<String,Object>> listSort = new ListSort<>();
                listSort.sortByMapKey(cpnList,"spAmt",true,"int");

                //大于等于2需要判断失效日期
                if(cpnList.size()>=2){
                    List<Map<String, Object>> cpnTempList = new ArrayList<Map<String, Object>>();
                    String billAmtCompare = StringUtil.getString(cpnList.get(0).get("spAmt"));
                    for (Map<String,Object> cpnMap : cpnList){
                        if(billAmtCompare.equals(cpnMap.get("spAmt"))){
                            cpnTempList.add(cpnMap);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(cpnTempList) && cpnTempList.size()>=2){
                        listSort.sortByMapKey(cpnTempList,"invDate",false,"String");
                        cpnList = cpnTempList;
                    }
                }
            }
        }
        return cpnList;
    }

    /**
     * @param map
     * @description 查询优惠活动
     * <AUTHOR>
     * @create 2018-11-06 14:51:00
     */
    @Override
    public List<Map<String, Object>> couponActInfo(Map<String, Object> map) {
        logger.debug("优惠活动入参："+map);
        //查询活动
        List<Map<String,Object>> acts=couponDao.queryAct(map);
       List<Map<String,Object>> removeActs=new ArrayList<Map<String,Object>>();
        //如果入参包括活动类型
            if (acts!=null && acts.size()>0) {
                for (Map item : acts) {
                    List<Map<String,Object>>  sections=new ArrayList<>();
                    if ("07".equals(item.get("actType"))) {
                        map.put("actId", item.get("actId"));
                        //查询充值优惠区间列表以及优惠券
                        List<Map<String, Object>> sectionList = couponDao.querySectionList(map);
                        for (Map section:sectionList){
                            List<Map<String,Object>> cpns=new ArrayList<Map<String,Object>>();
                            StringBuffer sectionMacks = new StringBuffer();
                            if (StringUtil.isEmpty(section.get("refFloor")) && StringUtil.isEmpty(section.get("refCeil"))){
                                section.put("sectionMarks","暂无描述");
                            }else {
                                sectionMacks.append("充值");
                                if (StringUtil.isNotEmpty(section.get("refCeil")) && StringUtil.isNotEmpty(section.get("refFloor"))) {
                                    sectionMacks.append(String.valueOf(section.get("refCeil")));
                                    sectionMacks.append("~");
                                    sectionMacks.append(String.valueOf(section.get("refFloor")));
                                } else if (StringUtil.isNotEmpty(section.get("refCeil")) && !StringUtil.isNotEmpty(section.get("refFloor"))) {
                                    sectionMacks.append(String.valueOf(section.get("refCeil")));
                                } else {
                                    sectionMacks.append(String.valueOf(section.get("refFloor")));
                                }
                                sectionMacks.append("送");
                            }
                            //段落描述
                            if ("0701".equals(section.get("actSubType"))){  //馈赠金
                                if (StringUtil.isNotEmpty(section.get("baseValue"))) {
                                    sectionMacks.append(section.get("baseValue"));
                                }
                                if (StringUtil.isNotEmpty(section.get("integralNum"))) {
                                    sectionMacks.append("，赠送积分：").append(section.get("integralNum"));
                                }
                            }else if("0702".equals(section.get("actSubType"))) {  //优惠券信息
                                    //优惠券不为空
                                    if (StringUtil.isNotEmpty(section.get("cpnIds"))){
                                        String[] cpnIds=String.valueOf(section.get("cpnIds")).split(",");
//                                        section.put("baseValue",cpnIds.length);
                                        cpns = couponDao.queryCpnById(cpnIds);
                                        try {
                                            // 过滤出有时间期限的优惠券ID
                                            List<String> cpnIdList = cpns.stream()
                                                    .filter(cpn -> {
                                                        String timeDuration = MapUtils.getValue(cpn, "timeDuration");
                                                        return StringUtil.isNotBlank(timeDuration) && Integer.parseInt(timeDuration) > 0;
                                                    })
                                                    .map(cpn -> MapUtils.getValue(cpn, "cpnId"))
                                                    .collect(Collectors.toList());

                                            // 如果有符合条件的优惠券，查询客户优惠券信息并更新
                                            if (!cpnIdList.isEmpty()) {
                                                List<Map<String, String>> custCpnList = couponDao.qryCustCouponList(cpnIdList);

                                                if (!custCpnList.isEmpty()) {
                                                    // 构建客户优惠券映射，便于快速查找
                                                    Map<String, Map<String, String>> cpnMap = custCpnList.stream()
                                                            .collect(Collectors.toMap(
                                                                    custCpn -> MapUtils.getValue(custCpn, "cpnId"),
                                                                    Function.identity(),
                                                                    (existing, replacement) -> replacement // 如果有重复，保留最新的
                                                            ));

                                                    // 更新优惠券的有效期信息
                                                    cpns.forEach(cpn -> {
                                                        String cpnId = MapUtils.getValue(cpn, "cpnId");
                                                        Map<String, String> custCpn = cpnMap.get(cpnId);
                                                        if (custCpn != null) {
                                                            cpn.put("eftDate", MapUtils.getValue(custCpn, "eftDate"));
                                                            cpn.put("expDate", MapUtils.getValue(custCpn, "expDate"));
                                                        }
                                                    });
                                                }
                                            }
                                        } catch (Exception e) {
                                            logger.error("查询客户优惠券信息出错", e);
                                        }
                                    }
                                    sectionMacks.append(section.get("baseValue")+"张优惠券");
//                                sectionMacks.append(section.get("baseValue")+"张"+cpns.get(0).get("cpnName"));
                                section.put("cpnList",cpns);
                                }
                            section.put("sectionMarks",sectionMacks);
                            sections.add(section);
                            }

                        }
                        item.put("sectionList", sections);
//                        sections.addAll(sectionList);
                    }
                }
                //如果入参包含0701或0702
                if (!(map.get("actSubType")==null)){
                    if (acts.size()>0){
                        for (Map act:acts){
                            //判断充值区间是否有值，若没有，则不显示这个活动
                            if (act.containsKey("sectionList")) {
                                if (((List) act.get("sectionList")).size() ==0) {
                                    removeActs.add(act);
                                }
                            }else {
                                removeActs.add(act);
                            }
                        }
                    }
                }
                acts.removeAll(removeActs);
                logger.debug("优惠活动出参："+JsonUtil.toJson(acts));
                return acts;
            }

    /**
     * @param cpnIds
     * @description 查询优惠券信息
     * <AUTHOR>
     * @create 2018-12-14 11:11:19
     */
    public List<Map> qryCouponList(String[] cpnIds) {
        List<Map> couponList = couponDao.qryCouponList(cpnIds);
        for(Map couponMap:couponList){
            //站点名称
            StringBuilder stationNameSb = new StringBuilder();
            String stationId = StringUtil.nullToString(couponMap.get("stationId"));
            if (StringUtils.isNotBlank(stationId)) {
                List<Map<String, Object>> stationList = archivesRpcService.queryStations(stationId);
                for(Map stationMap:stationList){
                    stationNameSb.append(stationMap.get("stationName")).append(",");
                }
                if(StringUtil.isNotBlank(stationNameSb)) {
                    String stationName = stationNameSb.toString();
                    stationName = stationName.substring(0, stationName.length() - 1);
                    couponMap.put("stationName",stationName);
                }else{
                    couponMap.put("stationName","");
                }
            }

            //省
            StringBuilder provinceNameSb = new StringBuilder();
            String city = StringUtil.nullToString(couponMap.get("city"));
            List cityList = new ArrayList();
            if(StringUtil.isNotBlank(city)){
                for(int i=0;i<city.split(",").length;i++){
                    Map cityMap = new HashMap();
                    cityMap.put("city",city.split(",")[i]);
                    cityList.add(cityMap);
                }
                Map serchMap = new HashMap();
                serchMap.put("cityList", cityList);
                List<Map> provinces = areaRpcService.queryProvinceByCity(serchMap);
                for(Map provinceMap:provinces){
                    provinceNameSb.append(provinceMap.get("areaName")).append(",");
                }
                if(StringUtil.isNotBlank(provinceNameSb)) {
                    String provinceName = provinceNameSb.toString();
                    provinceName = provinceName.substring(0, provinceName.length() - 1);
                    couponMap.put("provinceName",provinceName);
                }else{
                    couponMap.put("provinceName","");
                }

                //城市
                StringBuilder cityNameSb = new StringBuilder();
                List<AreaCondition> citys  = areaRpcService.queryCityByCityCode(city);
                for(AreaCondition cityInfo:citys){
                    cityNameSb.append(cityInfo.getAreaName()).append(",");
                }
                if(StringUtil.isNotBlank(cityNameSb)) {
                    String cityName = cityNameSb.toString();
                    cityName = cityName.substring(0, cityName.length() - 1);
                    couponMap.put("cityName",cityName);
                }else{
                    couponMap.put("cityName","");
                }
            }

            //图片
            String pubPath = PublicConstants.ApplicationPath.getPubPath();
            AttachBo search = new AttachBo();
            search.setRelaTable("coupon");
            search.setContentType("02");
            search.setRelaId(String.valueOf(couponMap.get("cpnId")));
            List<AttachBo> attachList = attachRpcService.getBatchAttach(search);
            if (attachList != null && attachList.size()>0) {
                couponMap.put("cpnImg",pubPath + "/api/v0.1/attachs/" + attachList.get(0).getAttachId());
            }else{
                couponMap.put("cpnImg","");
            }
        }
        return couponList;
    }

    /**
     * @description 记录里发放信息
     * <AUTHOR>
     * @create 2018-12-19 14:35:33
     */
    public void insertPutCpn(Map makActMap) {
        Map<String,Object> inMap = new HashMap<String, Object>();
        Map<String,Object> resultMap = new HashMap<String, Object>();

        Map<String, String> retMap = new HashMap<String, String>();
        StringBuffer outMsg = new StringBuffer();
        CouponPutBo bo = new CouponPutBo();
            try {
                inMap.put("uid",makActMap.get("custNo"));
                resultMap = custXpRpcService.getUserInfoByUid(inMap);

                bo.setPutTime(StringUtil.nullToString(makActMap.get("putTime")));
                // 生效~失效时间计算
                bo.setEftDate(StringUtil.nullToString(makActMap.get("eftDate")));
                bo.setInvDate(StringUtil.nullToString(makActMap.get("invDate")));
                bo.setCpnId(StringUtil.nullToString(makActMap.get("cpnId")));
                bo.setCustId(StringUtil.nullToString(makActMap.get("custNo")));

                // 新增发放
                bo.setPutEmpType("01");
                bo.setPutNum(StringUtil.nullToString(makActMap.get("putNum")));
                String getChannel = bo.getGetChannel();
                if (StringUtil.isBlank(getChannel)) {
                    getChannel = "01";
                }
                bo.setPutChannel(getChannel);
                bo.setNoticeType("01");//通知方式，01无 02短信
                int flag = couponPutDao.insertCouponPut(bo);
                // 调用订单优惠券发放——小鹏
                String putId = bo.getPutId();
                if (StringUtil.isNotBlank(putId)) {
                    //  新增发放明细
                    CouponPutDetBo det = new CouponPutDetBo();
                    det.setPutId(putId);
                    det.setPutTime(StringUtil.nullToString(makActMap.get("putTime")));

                    det.setCustId(StringUtil.nullToString(makActMap.get("custNo")));
                    if(resultMap!=null){
                        det.setMobile(StringUtil.nullToString(resultMap.get("mobile")));
                    }
                    det.setPutFlag("1");
                    det.setFailReason("");
                    couponPutDao.insertCouponPutDet(det);

                }
            }catch (Exception e) {

            }
    }

    /**
     * @param map
     * @description 优惠券详情
     * <AUTHOR>
     * @create 2019-05-27 12:31:29
     */
    @Override
    public Map<String, Object> myCouponInfo(Map<String, Object> map) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (map != null && map.size() > 0) {
            List<Map<String, String>> list = (List<Map<String, String>>) map.get("cpnList");
            if (list != null && list.size() > 0) {
                List<CouponContentBo> couponList = new ArrayList<>();
                if(PublicConstants.YN.TRUE.equals(StringUtil.nullToString(map.get("timeFlag"))) && StringUtil.isNotBlank(map.get("stationId"))){

                    String stationId = StringUtil.nullForString(map.get("stationId"));

                    Map<String, Object> iMap = new HashMap<String, Object>();
                    iMap.put("stationId",stationId);
                    Map<String,Object> sMap = archivesRpcService.queryOperInfoByStationId(iMap);
                    String city = StringUtil.isEmpty(sMap.get("city"))?"":StringUtil.getString(sMap.get("city"));
                    String buildId = StringUtil.isEmpty(sMap.get("operatorId"))?"":StringUtil.getString(sMap.get("operatorId"));


                    Map<String,Object> queryMap=new HashMap<>();
                    queryMap.put("stationId",stationId);
                    queryMap.put("city",city);
                    queryMap.put("buildId",buildId);
                    queryMap.put("amount",StringUtil.isNotBlank(map.get("amount"))?map.get("amount"):"0");
                    queryMap.put("list",list);
                    if (StringUtil.isNotEmpty(map.get("totalNum"))){
                        queryMap.put("totalNum", Integer.parseInt(StringUtil.getString(map.get("totalNum"))));
                    }
                    if (StringUtil.isNotEmpty(map.get("startNum"))){
                        queryMap.put("startNum", Integer.parseInt(StringUtil.getString(map.get("startNum"))));
                    }
                    queryMap.put("timeFlag",map.get("timeFlag"));
                    couponList = dao.queryMyCouponListByCondition(queryMap);
                }else{
                    couponList = dao.queryCouponList(list);
                }

                if (couponList != null && couponList.size() > 0) {
                    Map<String, Object> coupopnMap = new HashMap<String, Object>();
                    CodeBO code = null;
                    for (CouponContentBo bo : couponList) {
                        String cpnId = bo.getCpnId();
                        if (StringUtil.isNotBlank(cpnId)) {
                            Map<String, String> ccMap = new HashMap<String, String>();
                            ccMap.put("cpnId", cpnId);
                            ccMap.put("cpnName", bo.getCpnName());
                            ccMap.put("timeCondBeg",bo.getTimeCondEnd());
                            String cpnAmtUnit = "元";
                            String cpnAmt = "0";
                            if (StringUtil.isNotBlank(bo.getCpnAmt())) {
                                Map<String, String> calcMap = BeanUtils.describe(bo);
                                if (StringUtil.isNotBlank(map.get("billAmt"))){
                                    calcMap.put("billAmt",map.get("billAmt").toString());
                                }
                                Map<String, Object> retCalcMap = this.calcDctType(calcMap);
                                if (retCalcMap != null && retCalcMap.size() > 0) {
                                    if ("0".equals(retCalcMap.get("outCode"))) {
                                        calcMap.put("cpnAmt", (String) retCalcMap.get("spAmt"));
                                        cpnAmtUnit = (String) retCalcMap.get("cpnAmtUnit");
                                        retCalcMap = this.calcPreci(calcMap);
                                        if (retCalcMap != null && retCalcMap.size() > 0) {
                                            if ("0".equals(retCalcMap.get("outCode"))) {
                                                cpnAmt = (String) retCalcMap.get("spAmt");
                                            }
                                        }
                                    }
                                }
                            }
                            if (StringUtil.isNotBlank(cpnAmt) && cpnAmt.split(".").length == 2){
                                ccMap.put("cpnAmt","00".equals(cpnAmt.split(".")[1])?cpnAmt.split(".")[0]:cpnAmt);
                            }else{
                                ccMap.put("cpnAmt",cpnAmt);
                            }
                            ccMap.put("compareCpnAmt", cpnAmt);
                            ccMap.put("cpnMarks", bo.getCpnMarks());
                            coupopnMap.put(cpnId, JsonUtil.toJson(ccMap));
                        }
                    }
                    retMap.put("cpnList", coupopnMap);
                } else {
//                    throw new RuntimeException("RPC05-11-01【优惠券详情】接口未查询到优惠券信息");
                }
            } else {
                throw new RuntimeException("RPC05-11-01【优惠券详情】接口优惠券ID参数为空");
            }
        } else {
            throw new RuntimeException("RPC05-11-01【优惠券详情】接口参数为空");
        }
        return retMap;
    }

    /**
     * @param map
     * @description  优惠券可用性
     * <AUTHOR>
     * @create 2019-05-26 15:55:23
     */
    @Override
    public Map<String, Object> myCouponValid(Map<String, Object> map) throws Exception {
        List<Object> cpnObjList = new ArrayList<Object>();
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (map != null && map.size() > 0) {
            List<Map<String, String>> list = (List<Map<String, String>>) map.get("cpnList");
            String billAmt = (String) map.get("billAmt");
            String prodBusiType = (String) map.get("prodBusiType");
            if (StringUtil.isBlank(billAmt)) {
                throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口参数【billAmt】为空");
            }
            if (list != null && list.size() > 0) {
                List<CouponContentBo> couponList = dao.queryCouponList(list);
                logger.debug("----------------------couponList----------------"+JsonUtil.toJson(couponList));
                if (couponList != null && couponList.size() > 0) {
//                    Map<String, Object> coupopnMap = new HashMap<String, Object>();
//                    CodeBO code = null;
                    for (CouponContentBo bo : couponList) {
                        String cpnId = bo.getCpnId();
                        if (StringUtil.isNotBlank(cpnId)) {
                            Map<String, String> ccMap = new HashMap<String, String>();
                            ccMap.put("cpnId", cpnId);
                            ccMap.put("cpnName", bo.getCpnName());
                            ccMap.put("cpnMarks", bo.getCpnMarks());
                            ccMap.put("cpnAmt", bo.getCpnAmt());
                            String spAmt = "0.00";
                            Map<String, String> calcMap = new HashMap<String, String>();
                            calcMap.put("billAmt", billAmt);
                            calcMap.put("cpnId", cpnId);
                            calcMap.put("prodBusiType", prodBusiType);
                            calcMap.put("stationId",StringUtil.nullToString(map.get("stationId")));
                            Map<String, Object> outCalcMap = this.myCalc(calcMap);
                            if (outCalcMap != null && outCalcMap.size() > 0) {
                                String outCode = (String) outCalcMap.get("outCode");
                                if ("0".equals(outCode)) {
                                    spAmt = (String) outCalcMap.get("spAmt");
                                }
                            }
                            ccMap.put("spAmt", spAmt);
                            cpnObjList.add(ccMap);
                        }
                    }
                    retMap.put("cpnList", cpnObjList);
                } else {
//                    throw new RuntimeException("RPC05-11-01【优惠券详情】接口未查询到优惠券信息");
                }
            } else {
                throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口优惠券ID参数为空");
            }
        } else {
            throw new RuntimeException("RPC05-11-02【验证优惠券可用性】接口参数为空");
        }
        return retMap;
    }



    /**
     * @param map
     * @description  站点可领取优惠券
     * <AUTHOR>
     * @create 2019-05-26 20:58:04
     */
    @Override
    public List<Map<String, Object>> couponsGET(Map<String, Object> map) throws Exception {
        List<Map<String, Object>> retList = new ArrayList<Map<String, Object>>();
        MarketCondition condition = new MarketCondition();
        String prodBusiType = StringUtil.nullToString(map.get("prodBusiType"));
        if (StringUtil.isNotBlank(prodBusiType)) {
            condition.setBusiType(prodBusiType);
        }
        String actType = StringUtil.nullToString(map.get("actType"));
        if(StringUtil.isNotEmpty(actType)){
            condition.setActType(actType);
        }

        if("0".equals(StringUtil.nullToString(map.get("allFlag")))){
            String stationId = StringUtil.checkParam(map.get("stationId"),"站点不能为空");
            Map<String, Object> iMap = new HashMap<String, Object>();
            iMap.put("stationId",stationId);
            Map<String,Object> sMap = archivesRpcService.queryOperInfoByStationId(iMap);
            String city = StringUtil.isEmpty(sMap.get("city"))?"":StringUtil.getString(sMap.get("city"));
            String buildId = StringUtil.isEmpty(sMap.get("operatorId"))?"":StringUtil.getString(sMap.get("operatorId"));
            condition.setStationId(stationId);
            condition.setBuildId(buildId);
            condition.setCity(city);
            condition.setAllFlag("0");
        }else{
            //所有可领取的站点优惠券
            condition.setAllFlag("1");
        }
        condition.setCpnStatus(BillConstants.CpnState.IN_USE);
        condition.setInvDate("now");
        List<CouponBo> couponList = couponDao.getAllowCoupons(condition);
        logger.debug("--------------------------couponList:"+couponList);
        for (int i = 0; i < couponList.size(); i++) {
            logger.debug("---------------------优惠券信息赋值");
            CouponBo bo = couponList.get(i);
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                if ("01".equals(cpnType)) {
                    couponList.get(i).setCpnAmt(bo.getCpnAmt());
                } else {
                    couponList.get(i).setCpnAmt(bo.getCpnAmt());
                }
            }
            CodeBO code = codeService.getStandardCode("cpnStatus",
                    bo.getCpnStatus(), null);
            if (code != null) {
                couponList.get(i).setCpnStatusName(code.getCodeName());
            }
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = bo.getTimeUnit();
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime = bo.getEffectTime();
                                couponList.get(i).setEffectTime(effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }
            }
            String filePath = bo.getFilePath();
            if (StringUtil.isNotBlank(filePath)) {
                Map<String, String> jsonMap = (Map<String, String>) JsonUtil.fromJson(filePath, Map.class);
                if (jsonMap != null && jsonMap.size() > 0) {
                    filePath = jsonMap.get("filePath");
                    couponList.get(i).setFilePath(jsonMap.get("filePath"));
                    couponList.get(i).setAttachId(jsonMap.get("attachId"));
                }
            }

            if (!com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(bo.getOrgCode())) {
                OrgBo org = orgService.getOrgByNo(bo.getOrgCode());
                if (org != null) {
                    couponList.get(i).setCrtOrgName(org.getOrgShortName());
                }
            }
        }
        if (couponList != null && couponList.size() > 0) {
            // 通过cpnId,custId过滤已领用的优惠券
            ArrayList<Map<String, String>> cpnIdList = new ArrayList<Map<String, String>>();
            for (int i = 0; i < couponList.size(); i++) {
                String cpnId = couponList.get(i).getCpnId();
                if (StringUtil.isNotBlank(cpnId)) {
                    Map<String, String> cMap = new HashMap<String, String>();
                    cMap.put("cpnId", cpnId);
                    cpnIdList.add(cMap);
                }
            }
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("cpnIdList", cpnIdList);
            paramMap.put("custId", String.valueOf(map.get("custId")));
            List<Map<String, Object>> accCouponList = couponRpcService.couponByAcct(paramMap);
            CodeBO codeBO = null;
            if (accCouponList != null && accCouponList.size() > 0) {
                MergeUtil.mergeList(couponList, accCouponList, "cpnId", new String[]{"getNum"});
            }

            for (int i = 0; i < couponList.size(); i++) {
                CouponBo bo = couponList.get(i);
                String limGetNum = bo.getLimGetNum();
                if (StringUtil.isBlank(limGetNum)){
                    limGetNum = "0";
                }
                String getNum = bo.getGetNum();
                if (StringUtil.isBlank(getNum)){
                    getNum = "0";
                }
                // 已领数量和限领数量对比
                if (Integer.parseInt(getNum) >= Integer.parseInt(limGetNum)) {
                    continue;
                }
                // 优惠券发行数量与已领数量+已发放数量做对比
                String cpnNum = bo.getCpnNum();
                if (StringUtil.isBlank(cpnNum)) cpnNum = "0";
                String alrdyGetNum = bo.getAlrdyGetNum();
                if (StringUtil.isBlank(alrdyGetNum)) alrdyGetNum = "0";
                String putNum = bo.getPutNum();
                if (StringUtil.isBlank(putNum)) putNum = "0";
                logger.debug("cpnNum:{},alrdyGetNum:{},putNum:{}",cpnNum,alrdyGetNum,putNum);
                if (Integer.parseInt(alrdyGetNum) + Integer.parseInt(putNum)>= Integer.parseInt(cpnNum)) {
                    continue;
                }
                Map<String, Object> boMap = new HashMap<String, Object>();
                Map<String, String> couponMap = MapUtils.beanToMap(bo);
                if (couponMap != null && couponMap.size() > 0) {
                    if (StringUtil.isNotBlank(couponMap.get("cpnAmt")) && couponMap.get("cpnAmt").split(".").length == 2){
                        boMap.put("cpnAmt","00".equals(couponMap.get("cpnAmt").split(".")[1])?couponMap.get("cpnAmt").split(".")[0]:couponMap.get("cpnAmt"));
                    }else{
                        boMap.put("cpnAmt",couponMap.get("cpnAmt"));
                    }
                    boMap.put("cpnName", couponMap.get("cpnName"));
                    boMap.put("cpnId", couponMap.get("cpnId"));
                    boMap.put("cpnMarks", couponMap.get("cpnMarks"));
                    boMap.put("cpnObtainStatus","02");
                    boMap.put("effectTime",couponMap.get("eftDate")+"-"+couponMap.get("invDate"));
                    boMap.put("eftDate",couponMap.get("eftDate"));
                    boMap.put("expDate",couponMap.get("invDate"));
                    boMap.put("eftDateStr","");
                    boMap.put("timeLimit", couponMap.get("effectTime"));
                    boMap.put("isUseFlag","0");
                    boMap.put("getTime","");
                    boMap.put("getSource","");
                    retList.add(boMap);
                }
            }
        }
        logger.debug("---------------------优惠券信息赋值"+retList);
        return retList;
    }

    /**
     * @param map
     * @description 优惠券详情（新）
     * <AUTHOR>
     * @create 2019-06-10 10:51:26
     */
    @Override
    public List<Map<String, Object>> couponsDetailInfo(Map<String, Object> map) {
        return couponDao.couponsDetailInfo(map);
    }
    @Override
    public List<Map<String, Object>> getGoodsVrCoupon()  {

        List<Map<String, Object>> couponList = couponDao.getGoodsVrCoupon();
        if(CollectionUtils.isNotEmpty(couponList)){
            List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
            for(Map<String, Object> couponBo:couponList){

                if ("2".equals( MapUtils.getValue(couponBo,"cpn_time_type"))) {

                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = MapUtils.getValue(couponBo,"time_unit");
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime =  MapUtils.getValue(couponBo,"effect_time");
                                couponBo.put("effect_time",effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }

            }

        }

        return couponList;
    }

    @Override
    public Map<String, Object> getGoodsVrCouponDetail(Map<String, Object> map)  {
      Map<String, Object> retMap =new HashMap<>();
        MarketCondition condition = new MarketCondition();
        condition.setCpnId(MapUtils.getValue(map,"cpnId"));
        List<CouponBo> couponList = couponDao.getCoupons(condition);

        if(CollectionUtils.isNotEmpty(couponList)){
            CouponBo couponBo=   couponList.get(0);
            retMap.put("cpnId",couponBo.getCpnId());
            retMap.put("cpnName",couponBo.getCpnName());
            retMap.put("cpnTimeType",couponBo.getCpnTimeType());
            retMap.put("timeUnit",couponBo.getTimeUnit());
            retMap.put("effectTime",couponBo.getEffectTime());
            retMap.put("cpnMarks",couponBo.getCpnMarks());
        }
        return retMap;
    }

    @Override
    public Map<String, Object> queryCpnByActId(Map<String, String> map) throws Exception {
        Map<String, Object> hashMap = Maps.newHashMap();
        // 用户id
        String custId = MapUtils.getValue(map, "custId");
        // 活动id
        String actId = MapUtils.getValue(map, "aId");
        logger.info("活动id", actId);

        AttachBo attach = new AttachBo();
        attach.setRelaTable("actImg");
        attach.setRelaId(actId);
        AttachBo oldAttach = attachService.qryAttachInfo(attach);
        logger.info("活动图片获取: {}", JSONObject.toJSONString(oldAttach));
        if (oldAttach != null) {
            hashMap.put("fileId", oldAttach.getAttachId());
        }
        Map<String, Object> actBo = marketActDao.queryActById(actId);
        hashMap.put("act", actBo);
        // 获取用户可用优惠券
        map.put("sourceApi","qrcode");
        List<Map<String, Object>> cpnList = couponService.couponsRestGET(map);
        hashMap.put("cpnList", cpnList);
        return hashMap;
    }

    @Override
    public Map<String, Object> queryCpnNum(Map<String, Object> map) throws Exception {
        logger.info("获取用户可领优惠券入参：{}", JSONObject.toJSONString(map));
        String cpnIds = MapUtils.getValue(map, "cpnIds");
        String custId = MapUtils.getValue(map, "custId");
        logger.info("获取用户可领优惠券入参cpnIds={},custId={}", cpnIds, custId);
        if (StringUtil.isBlank(cpnIds)) {
            throw new BusinessWarning("优惠券id为空", this.getClass());
        }
        List<String> cpnIdList = Arrays.asList(cpnIds.split(","));
        map.put("cpnIdList", cpnIdList);
        // 获取优惠券每人限领张数
        List<Map<String, String>> list = couponDao.queryCpnNum(map);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessWarning("未查到待领取优惠券内容", this.getClass());
        }
        logger.info("获取优惠券每人限领张数：{}", JSONObject.toJSONString(list));
        list.forEach(item -> {
            String cpnId = MapUtils.getValue(item, "cpnId");
            String limitNum = MapUtils.getValue(item, "limitNum");
            // 获取优惠券每人已领张数
            int getCpnNum = couponDao.queryGetCpnNum(cpnId, custId);
            int num = Integer.parseInt(limitNum) - getCpnNum;
            item.put("limitNum", String.valueOf(num));
        });
        List<String> finalList = list.stream()
                .flatMap(item -> {
                    int limitNum = Integer.parseInt(MapUtils.getValue(item, "limitNum"));
                    String cpnId = MapUtils.getValue(item, "cpnId");
                    return IntStream.range(0, limitNum).boxed().map(i -> cpnId);
                })
                .collect(Collectors.toList());
        logger.info("最终返回每人限领张数：{}", JSONObject.toJSONString(finalList));
        Map<String, Object> hashmap = new HashMap<>();
        hashmap.put("cpnIds", String.join(",", finalList));
        return hashmap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> couponPut(CouponPutRpcBo couponPutRpcBo) {
                logger.debug("RPC优惠券发放入参：{}", JSONObject.toJSONString(couponPutRpcBo));
                Map<String, String> resultMap = new HashMap<>();
                try {
                    //根据优惠卷id 查询优惠卷详情
                    MarketCondition condition = new MarketCondition();
                    condition.setCpnId(String.valueOf(couponPutRpcBo.getCpnId()));
                    CouponBo bo = couponPutService.getCouponDetail(condition);
                    if (bo==null){
                        throw new BusinessWarning("优惠卷不存在,无法消费",this.getClass());
                    }
                        //  优惠卷实体to 调用实体
                        CouponPutBo   couponPutBo = BeanUtil.toBean(bo, CouponPutBo.class);
                        // 请求详情实体 to 调用实体
                        BeanUtil.copyProperties(couponPutRpcBo,couponPutBo);
                        //补充BeanUtil.copyPropertiesBug （is开头的字段无法拷贝）
                    couponPutBo.setIsDelay(couponPutRpcBo.getDelay());
                    couponPutBo.setIsAllCust(couponPutRpcBo.getAllCust());
                    couponPutBo.setPutTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(couponPutRpcBo.getPutTime()));
                    // 生效参数补充
                    Map<String, String> timeMap = CouponPutServiceImpl.calcTimeRanges(couponPutBo);
                    //参数已经校验过，不会出现问题
                    String  eftDate = timeMap.get("eftDate");
                    String   invDate = timeMap.get("invDate");
                    // 保存发放信息
                    couponPutBo.setEftDate(StringUtils.isNotBlank(eftDate)?eftDate:null);
                    couponPutBo.setInvDate(StringUtils.isNotBlank(invDate)?invDate:null);
                    //期望将延迟发放的任务修改为已完成
                    boolean delayByPutId = couponPutService.updateDelayByPutId(couponPutBo.getPutId(), 2, 1);
                    if (!delayByPutId){
                        throw new BusinessWarning("延时任务无法执行,已经被取消或完成",this.getClass());
                    }
                    logger.info("优惠卷请求对象搬迁后：{}",JSON.toJSONString(couponPutBo));
                    if (BooleanUtil.isTrue(couponPutBo.getIsAllCust())){
                        couponPutService.excuteCouponPutAllCust(couponPutBo);
                        resultMap.put("mark","aysncAllUser");
                    }else {
                        //
                        Map<String, String> result = couponPutService.excuteCouponPut(couponPutBo, true);
                        resultMap.put("mark","success");
                        resultMap.put("result",JSON.toJSONString(result));
                    }
                }catch (BusinessWarning businessWarning){
                    logger.error("优惠券发放失败businessWarning:{}", ExceptionUtil.stacktraceToString(businessWarning));
                    resultMap.put("mark",ExceptionUtil.stacktraceToString(businessWarning));
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }catch (Exception exception){
                    logger.error("exception:{}", ExceptionUtil.stacktraceToString(exception));
                    resultMap.put("mark",ExceptionUtil.stacktraceToString(exception));
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
                return resultMap;

    }

    @Override
    public List<CouponPutRpcBo> pollDelayCouponPutInfo(Date delayTime) {
        return couponPutDao.queryCouponPutByDelay(delayTime);
    }

    @Override
    public Map<String, String> getSuperpositionFlag(String cpnId) {
        return couponPutDao.getSuperpositionFlag(cpnId);
    }

    @Override
    public Map<String, Object> getMyCarCoupon() {
        return couponDao.getMyCarCoupon();
    }

    @Override
    public Map<String, String> myCarCouponPut(CouponPutRpcBo couponPutRpcBo) {
        logger.debug("我的爱车RPC优惠券发放入参：{}", JSONObject.toJSONString(couponPutRpcBo));
        Map<String, String> resultMap = new HashMap<>();
        try {
            //根据优惠卷id 查询优惠卷详情
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(String.valueOf(couponPutRpcBo.getCpnId()));
            CouponBo bo = couponPutService.getCouponDetail(condition);
            if (bo==null){
                throw new BusinessWarning("优惠卷不存在,无法消费",this.getClass());
            }

            //  优惠卷实体to 调用实体
            CouponPutBo   couponPutBo = BeanUtil.toBean(bo, CouponPutBo.class);
            // 请求详情实体 to 调用实体
            BeanUtil.copyProperties(couponPutRpcBo,couponPutBo);
            //补充BeanUtil.copyPropertiesBug （is开头的字段无法拷贝）
            couponPutBo.setIsDelay(couponPutRpcBo.getDelay());
            couponPutBo.setIsAllCust(couponPutRpcBo.getAllCust());
            couponPutBo.setPutTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(couponPutRpcBo.getPutTime()));

            // 生效参数补充
            Map<String, String> timeMap = CouponPutServiceImpl.calcTimeRanges(couponPutBo);
            //参数已经校验过，不会出现问题
            String  eftDate = timeMap.get("eftDate");
            String   invDate = timeMap.get("invDate");
            // 保存发放信息
            couponPutBo.setEftDate(StringUtils.isNotBlank(eftDate)?eftDate:null);
            couponPutBo.setInvDate(StringUtils.isNotBlank(invDate)?invDate:null);

            logger.info("优惠卷请求对象搬迁后：{}",JSON.toJSONString(couponPutBo));
            //存储优惠券发放日志(含Coupon参数检查逻辑)
            logger.info("savePutInfo请求对象{}"+JSON.toJSONString(couponPutBo));;
            couponPutBo.setPutEmp("SYSADMIN");
            couponPutService.savePutCouponPut(couponPutBo);

            Map<String, Object> result = couponPutService.excuteCouponPutForMyCar(couponPutBo, false);
            List<Map<String, Object>> prcCustList = (List<Map<String, Object>>) result.get("custList");
            Map<String, Object> proCust = prcCustList.get(0);
            String putNum = String.valueOf(result.get("putNum"));
            resultMap.put("mark","success");
            resultMap.put("putNum",putNum);
            resultMap.put("failReason", String.valueOf(proCust.get("failReason")));
        }catch (BusinessWarning businessWarning){
            logger.error("我的爱车优惠券发放失败businessWarning:{}", ExceptionUtil.stacktraceToString(businessWarning));
            resultMap.put("mark",ExceptionUtil.stacktraceToString(businessWarning));
        }catch (Exception exception){
            logger.error("我的爱车exception:{}", ExceptionUtil.stacktraceToString(exception));
            resultMap.put("mark",ExceptionUtil.stacktraceToString(exception));
        }
        return resultMap;
    }

}

