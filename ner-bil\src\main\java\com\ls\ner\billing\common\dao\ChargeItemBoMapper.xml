<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.ChargeItemBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.ChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="ITEM_ID" jdbcType="BIGINT" property="itemId" />
    <result column="CHARGE_ITEM_CODE" jdbcType="VARCHAR" property="chargeItemCode" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="CHARGE_TYPE" jdbcType="VARCHAR" property="chargeType" />
    <result column="NEED_FLAG" jdbcType="VARCHAR" property="needFlag" />
    <result column="ITEM_DESC" jdbcType="VARCHAR" property="itemDesc" />
    <result column="EFFECT_FLAG" jdbcType="VARCHAR" property="effectFlag" />
    <result column="TIME_LINE" jdbcType="VARCHAR" property="timeLine" />
    <result column="BUILD_DATE" jdbcType="TIMESTAMP" property="buildDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, ITEM_ID, CHARGE_ITEM_CODE, ITEM_NAME, CHARGE_TYPE, NEED_FLAG, ITEM_DESC, 
    EFFECT_FLAG, TIME_LINE, BUILD_DATE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.ChargeItemBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.ChargeItemBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.ChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_charge_item (SYSTEM_ID, ITEM_ID, CHARGE_ITEM_CODE, 
      ITEM_NAME, CHARGE_TYPE, NEED_FLAG, 
      ITEM_DESC, EFFECT_FLAG, TIME_LINE, 
      BUILD_DATE)
    values (#{systemId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{chargeItemCode,jdbcType=VARCHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{chargeType,jdbcType=VARCHAR}, #{needFlag,jdbcType=VARCHAR}, 
      #{itemDesc,jdbcType=VARCHAR}, #{effectFlag,jdbcType=VARCHAR}, #{timeLine,jdbcType=VARCHAR}, 
      #{buildDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.ChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_charge_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="itemId != null">
        ITEM_ID,
      </if>
      <if test="chargeItemCode != null">
        CHARGE_ITEM_CODE,
      </if>
      <if test="itemName != null">
        ITEM_NAME,
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE,
      </if>
      <if test="needFlag != null">
        NEED_FLAG,
      </if>
      <if test="itemDesc != null">
        ITEM_DESC,
      </if>
      <if test="effectFlag != null">
        EFFECT_FLAG,
      </if>
      <if test="timeLine != null">
        TIME_LINE,
      </if>
      <if test="buildDate != null">
        BUILD_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="chargeItemCode != null">
        #{chargeItemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="needFlag != null">
        #{needFlag,jdbcType=VARCHAR},
      </if>
      <if test="itemDesc != null">
        #{itemDesc,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        #{effectFlag,jdbcType=VARCHAR},
      </if>
      <if test="timeLine != null">
        #{timeLine,jdbcType=VARCHAR},
      </if>
      <if test="buildDate != null">
        #{buildDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.ChargeItemBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_charge_item
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.itemId != null">
        ITEM_ID = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.chargeItemCode != null">
        CHARGE_ITEM_CODE = #{record.chargeItemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeType != null">
        CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.needFlag != null">
        NEED_FLAG = #{record.needFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.itemDesc != null">
        ITEM_DESC = #{record.itemDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.effectFlag != null">
        EFFECT_FLAG = #{record.effectFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.timeLine != null">
        TIME_LINE = #{record.timeLine,jdbcType=VARCHAR},
      </if>
      <if test="record.buildDate != null">
        BUILD_DATE = #{record.buildDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_charge_item
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      ITEM_ID = #{record.itemId,jdbcType=BIGINT},
      CHARGE_ITEM_CODE = #{record.chargeItemCode,jdbcType=VARCHAR},
      ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      NEED_FLAG = #{record.needFlag,jdbcType=VARCHAR},
      ITEM_DESC = #{record.itemDesc,jdbcType=VARCHAR},
      EFFECT_FLAG = #{record.effectFlag,jdbcType=VARCHAR},
      TIME_LINE = #{record.timeLine,jdbcType=VARCHAR},
      BUILD_DATE = #{record.buildDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.ChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_charge_item
    <set>
      <if test="itemId != null">
        ITEM_ID = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="chargeItemCode != null">
        CHARGE_ITEM_CODE = #{chargeItemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="needFlag != null">
        NEED_FLAG = #{needFlag,jdbcType=VARCHAR},
      </if>
      <if test="itemDesc != null">
        ITEM_DESC = #{itemDesc,jdbcType=VARCHAR},
      </if>
      <if test="effectFlag != null">
        EFFECT_FLAG = #{effectFlag,jdbcType=VARCHAR},
      </if>
      <if test="timeLine != null">
        TIME_LINE = #{timeLine,jdbcType=VARCHAR},
      </if>
      <if test="buildDate != null">
        BUILD_DATE = #{buildDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.ChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_charge_item
    set ITEM_ID = #{itemId,jdbcType=BIGINT},
      CHARGE_ITEM_CODE = #{chargeItemCode,jdbcType=VARCHAR},
      ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      NEED_FLAG = #{needFlag,jdbcType=VARCHAR},
      ITEM_DESC = #{itemDesc,jdbcType=VARCHAR},
      EFFECT_FLAG = #{effectFlag,jdbcType=VARCHAR},
      TIME_LINE = #{timeLine,jdbcType=VARCHAR},
      BUILD_DATE = #{buildDate,jdbcType=TIMESTAMP}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>