package com.ls.ner.billing.api.vip.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
public class RegularVipRankRPCBo implements Serializable {

    private static final long serialVersionUID = 2963443829874938118L;

    private String rankId;

    private String rankNo;

    private String rankName;

    private Long rankMin;

    private Long rankMax;

    private Long rankAmtMin;

    private Long rankAmtMax;

    public Long getRankAmtMin() {
        return rankAmtMin;
    }

    public void setRankAmtMin(Long rankAmtMin) {
        this.rankAmtMin = rankAmtMin;
    }

    public Long getRankAmtMax() {
        return rankAmtMax;
    }

    public void setRankAmtMax(Long rankAmtMax) {
        this.rankAmtMax = rankAmtMax;
    }

    public String getRankId() {
        return rankId;
    }

    public void setRankId(String rankId) {
        this.rankId = rankId;
    }

    public String getRankNo() {
        return rankNo;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }

    public Long getRankMin() {
        return rankMin;
    }

    public void setRankMin(Long rankMin) {
        this.rankMin = rankMin;
    }

    public Long getRankMax() {
        return rankMax;
    }

    public void setRankMax(Long rankMax) {
        this.rankMax = rankMax;
    }

}
