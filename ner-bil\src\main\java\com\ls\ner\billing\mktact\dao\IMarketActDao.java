package com.ls.ner.billing.mktact.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.ls.ner.billing.mktact.bo.MarketActBo;


public interface IMarketActDao {

	/**
	 * 获取活动列表 by condition
	 *
	 * @param condition
	 * @return
	 * <AUTHOR>
	 */
	public List<MarketActBo> qryMarketActives(MarketActBo condition);

	/**
	 * 获取活动 list
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryMarketActivesMap(Map<String, Object> map);

	/**
	 * 获取活动数量 by condition
	 *
	 * @param condition
	 * @return
	 * <AUTHOR>
	 */
	public int qryMarketActivesNums(MarketActBo condition);

	/**
	 * 新增/创建活动
	 *
	 * @param marketActBo
	 * <AUTHOR>
	 */
	public void insertMarketActive(MarketActBo marketActBo);

	/**
	 * 更新活动
	 *
	 * @param marketActBo
	 * <AUTHOR>
	 */
	public void updateMarketActive(MarketActBo marketActBo);

	/**
	 * 删除活动
	 *
	 * @param actId
	 * <AUTHOR>
	 */
	public void deleteByPrimaryKey(Long actId);

	/**
	 * 活动状态轮询
	 * @param actStatus
	 * <AUTHOR>
	 */
	public int updateMarketActiveState(@Param("actState") String actState);

	/**
	 * @param inMap
	 * @description 查询活动关联的优惠券
	 * <AUTHOR>
	 * @create 2018-04-24 15:52:56
	 */
	public List<Map<String, Object>> queryActCoupon(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 查询活动适用范围
	 * @create 2019/5/28 14:43
	 *
	 * @return
	 */
	public List<Map<String,Object>> queryActScope(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 新版查询活动
	 * @create 2019/5/29 17:48
	 *
	 */
	public List<Map<String,Object>> qryMarketActivesNew(MarketActBo condition);

	/**
	 * <AUTHOR>
	 * @description 新版查询活动数量
	 * @create 2019/5/29 17:48
	 *
	 */
	public int qryMarketActivesNumNew(MarketActBo condition);

	/**
	 * <AUTHOR>
	 * @description  查询活动参与人数
	 * @create 2019/5/29 23:15
	 *
	 * @return
	 */
	public List<Map<String,Object>> qryJoinActNumAndOrder(@Param(value = "actIdList") List<String> actIdList);
	/**
	 *@param
	 *@description 查询已赠送金额
	 *<AUTHOR>
	 *@create 2019/5/31 14:59
	 */
	Map<String,Object> qryGiftBalanceValue(Map<String, Object> map1);

	Map<String, Object> queryActById(String actId);

	Map<String, Object> queryActInProgress(MarketActBo condition);


}
