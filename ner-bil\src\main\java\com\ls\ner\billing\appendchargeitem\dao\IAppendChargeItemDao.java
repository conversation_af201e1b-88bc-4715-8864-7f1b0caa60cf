package com.ls.ner.billing.appendchargeitem.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.common.bo.AppendChargeItemBo;

public interface IAppendChargeItemDao {

	public List<AppendChargeItemBo> selectByItemNoAndItemType(AppendChargeItemBo appendChargeItemBo);
	public int updateByItemNoAndItemType(AppendChargeItemBo appendChargeItemBo);
	public int deleteByItemNoAndItemType(AppendChargeItemBo appendChargeItemBo);
}
