<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="预存赠送"/>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<table class="tab_search">

			<tr>
				<td><ls:label text="活动名称"/></td>
				<td><ls:text name="actName"/></td>
				<td><ls:label text="活动状态"/></td>
				<td>
					<ls:select name="actState" property="actState">
						<ls:options property="actStateList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td><ls:label text="活动时间"/></td>
				<td>
					<table>
						<tr>
							<td width="45%"><ls:date name="effTime" format="yyyy-MM-dd HH:mm"/></td>
							<td width="10%"><ls:label text="至" textAlign="center"/></td>
							<td width="45%"><ls:date name="expTime" format="yyyy-MM-dd HH:mm"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>

				<td colspan="6">
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch"/>
						<ls:button text="清空" onclick="doClear"/>
					</div>
				</td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="mktactGrid" height="330px;" width="100%" showRowNumber="true"
							 showCheckBox="false" singleSelect="true" caption="活动列表" primaryKey="actId">
					<ls:gridToolbar name="mktactGridBar">
						<ls:gridToolbarItem name="addBtn" imageKey="add" onclick="doAdd" text="新增"/>
						<ls:gridToolbarItem name="editBtn" imageKey="edit" onclick="doEdit" text="修改"/>
						<ls:gridToolbarItem name="closeBtn" imageKey="stop" onclick="doClose" text="关闭"/>
						<ls:gridToolbarItem name="deleteBtn" imageKey="delete" onclick="doDel" text="删除"/>
					</ls:gridToolbar>
					<ls:column caption="" name="actId" hidden="true"/>
					<ls:column caption="" name="actState" hidden="true"/>
					<ls:column caption="活动名称" name="actName" formatFunc="linkFunc"/>
					<ls:column caption="生效时间" name="effTime"/>
					<ls:column caption="失效时间" name="expTime"/>
					<ls:column caption="活动状态" name="actStateName"/>
					<ls:column caption="已参与人数" name="joinNum"/>
					<ls:column caption="已赠送金额" name="presentValue"/>
					<ls:column caption="创建时间" name="dataOperTime"/>
					<ls:pager pageSize="15"></ls:pager>
				</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
        window.mktactGrid = mktactGrid;

        window.onload = function () {
            var _actType = actType.getValue();
            if (!LS.isEmpty(_actType) && _actType != "null") {
                actType.setReadOnly(true);
            }
        }

        mktactGrid.itemclick = function () {
            var item = mktactGrid.getSelectedItem();
            if (!item) {
                return;
            }
            var actStateNameValue = item.actStateName;
            if (actStateNameValue == "草稿") {
                mktactGrid.toolbarItems.editBtn.setEnabled(true);
                mktactGrid.toolbarItems.closeBtn.setEnabled(false);
                mktactGrid.toolbarItems.deleteBtn.setEnabled(true);
                mktactGrid.toolbarItems.editActBtn.setEnabled(true);
            }
            else if (actStateNameValue == "未开始") {
                mktactGrid.toolbarItems.editBtn.setEnabled(false);
                mktactGrid.toolbarItems.closeBtn.setEnabled(true);
                mktactGrid.toolbarItems.deleteBtn.setEnabled(false);
                mktactGrid.toolbarItems.editActBtn.setEnabled(true);
            }
            else if (actStateNameValue == "进行中") {
                mktactGrid.toolbarItems.editBtn.setEnabled(false);
                mktactGrid.toolbarItems.closeBtn.setEnabled(true);
                mktactGrid.toolbarItems.deleteBtn.setEnabled(false);
                mktactGrid.toolbarItems.editActBtn.setEnabled(true);
            }
            else {
                mktactGrid.toolbarItems.editBtn.setEnabled(false);
                mktactGrid.toolbarItems.closeBtn.setEnabled(false);
                mktactGrid.toolbarItems.deleteBtn.setEnabled(false);
                mktactGrid.toolbarItems.editActBtn.setEnabled(false);
            }
        }

        window.doSearch = doSearch;
        doSearch();

        function doSearch() {
            var eff = effTime.getValue();
            var exp = expTime.getValue();
            if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff > exp) {
                LS.message("info", "开始时间不能大于截止时间。");
                return;
            }
            var params = searchForm.getFormData();
            params.actType = "07";
            mktactGrid.query('~/markertAct/marketacts', params, function () {
            });
        }

        function doClear() {
            searchForm.clear();
        }

        function doAdd() {
            LS.dialog("~/markertAct/edit?actId=&actType=07", "预存赠送", 1000, 600, true, null);
        }

        function doEdit() {
            var item = mktactGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择一条记录！");
                return;
            }
            if (item.actState != '0') {
                LS.message("error", "只能对草稿状态的活动进行修改！");
                return;
            }
            LS.dialog("~/markertAct/edit?actId=" + item.actId + "&actType=07", "修改活动", 1000, 600, true, null);
        }

        function doClose() {
            var item = mktactGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择一条记录！");
                return;
            }
            if (item.actState != '1' && item.actState != '2') {
                LS.message("error", "只能对未开始和进行中活动进行关闭！");
                return;
            }
            LS.confirm('该活动关闭后，将无法重新开启，确定关闭该活动吗？', function (result) {
                if (result) {
                    LS.ajax("~/markertAct/closeDel/close?actId=" + item.actId, null, function (e) {
                        if (e.items[0] == "T") {
                            LS.message("info", "关闭成功！");
                            doSearch();
                            return;
                        } else {
                            LS.message("info", "操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function doDel() {
            var item = mktactGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择一条记录！");
                return;
            }
            if (item.actState != '0') {
                LS.message("error", "只能对草稿状态的活动进行删除！");
                return;
            }
            LS.confirm('确定要删除该活动？', function (result) {
                if (result) {
                    LS.ajax("~/markertAct/closeDel/delete?actId=" + item.actId, null, function (e) {
                        if (e.items[0] == "T") {
                            LS.message("info", "删除成功！");
                            doSearch();
                            return;
                        } else {
                            LS.message("info", "操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }


        function doEditInfo() {
            var item = mktactGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择一条记录！");
                return;
            }
            var _actId = item.actId;
            var _actType = item.actType;
            if (LS.isEmpty(_actId)) {
                LS.message("error", "选中的记录不存在活动！");
                return;
            }
            if (LS.isEmpty(_actType)) {
                LS.message("error", "选中的记录不存在活动类型！");
                return;
            }

            LS.dialog("~/marketacts/actInfo?actType=" + _actType + "&actId=" + _actId, "活动资讯发布", 850, 530, true, null);
        }

        window.onload = function () {
            initGridHeight('searchForm', 'mktactGrid');
        }

        function linkFunc(rowdata) {
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + rowdata.actId + "\",\"" + rowdata.actType + "\",\"" + rowdata.actStateName + "\")'>" + rowdata.actName + "</a></u>";
        }

        window.showCouponDetail = showCouponDetail;

        function showCouponDetail(actId, actType, actStateName) {
            if (actStateName == '草稿') {
                LS.dialog("~/markertAct/edit?actId=" + actId + "&actType=07", "修改活动", 1000, 600, true, null);
            } else {
                LS.dialog("~/markertAct/detail/" + actId + "/" + actType, "预存赠送", 1000, 510, true);
            }

        }
	</ls:script>
</ls:body>
</html>