package com.ls.ner.billing.vip.controller;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.vip.bo.RegularVipRankBo;
import com.ls.ner.billing.vip.bo.VipLevelCalcConfig;
import com.ls.ner.billing.vip.condition.RegularVipCondition;
import com.ls.ner.billing.vip.service.IVipLevelCalcConfigService;
import com.ls.ner.billing.vip.service.IVipService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/14
 */
@Controller
@RequestMapping("/vip/regular")
public class RegularVipController extends QueryController<RegularVipCondition> {

    private static final Logger logger = LoggerFactory.getLogger(RegularVipController.class);

    @ServiceAutowired(value = "vipService", serviceTypes = ServiceType.LOCAL)
    private IVipService vipService;

    @ServiceAutowired(value = "vipLevelCalcConfigService", serviceTypes = ServiceType.LOCAL)
    private IVipLevelCalcConfigService configService;

    @RequestMapping("/init")
    public String regularInit(Model m) {
        return "/bil/vip/regularVipRank";
    }

    @RequestMapping(value = "/vipRankConfigEdit", method = RequestMethod.GET)
    public String regularVipRankConfigEdit(@RequestParam("rankId") String rankId, Model m) {
        RegularVipRankBo bo = vipService.getRegularVipConfigById(rankId);
        m.addAttribute("rankConfigForm", bo);
        return "/bil/vip/regularVipRankEdit";
    }

    @RequestMapping(value = "/vipRankConfigSave", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject regularVipRankConfigSave(RegularVipCondition condition) {
        String mark = "success";
        try {
            RegularVipRankBo bo = new RegularVipRankBo();
            BeanUtils.copyProperties(condition, bo);
            vipService.editRegularVipConfig(bo);
        } catch (Exception e) {
            mark = "error";
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);
    }

    @RequestMapping("/vipRankConfigList")
    public @ItemResponseBody
    QueryResultObject regularVipRankConfigList() {
        List<RegularVipRankBo> bos = vipService.getRegularVipConfigList();
        return RestUtils.wrappQueryResult(bos, bos.size());
    }


    @RequestMapping(value = "/getConfig", method = RequestMethod.POST)
    public@ResponseBody WrappedResult getConfig(Model m) {
        try {
            VipLevelCalcConfig config = configService.getLatestConfig();
            if (config != null && config.getCalcMode() != null) {
                return WrappedResult.successWrapedResult(config.getCalcMode());
            }
        } catch (Exception e) {}
        return WrappedResult.successWrapedResult("quantity");
    }

    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    @ResponseBody
    public QueryResultObject saveConfig(VipLevelCalcConfig condition) {
        String mark = "success";
        try {
            configService.saveConfig(condition);
        } catch (Exception e) {
            mark = "error";
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);
    }

    @RequestMapping(value = "/calcRatio", method = RequestMethod.GET)
    public String calcRatio(@RequestParam("mode") String mode, Model m) {
        VipLevelCalcConfig bo = new VipLevelCalcConfig();
        bo.setCalcMode(mode);
        m.addAttribute("rankConfigForm", bo);
        return "/bil/vip/calcRatio";
    }

    @Override
    protected RegularVipCondition initCondition() {
        return new RegularVipCondition();
    }
}
