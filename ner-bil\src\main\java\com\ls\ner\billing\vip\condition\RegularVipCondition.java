package com.ls.ner.billing.vip.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/14
 */
public class RegularVipCondition extends QueryCondition implements Serializable {

    private static final long serialVersionUID = 2963443823364938118L;

    private String rankId;

    private String rankNo;

    private String rankName;

    private Long rankMin;

    private Long rankMax;

    private Long rankAmtMin;

    private Long rankAmtMax;

    private Integer durationDays;

    public Long getRankAmtMin() {
        return rankAmtMin;
    }

    public void setRankAmtMin(Long rankAmtMin) {
        this.rankAmtMin = rankAmtMin;
    }

    public Long getRankAmtMax() {
        return rankAmtMax;
    }

    public void setRankAmtMax(Long rankAmtMax) {
        this.rankAmtMax = rankAmtMax;
    }

    public Integer getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(Integer durationDays) {
        this.durationDays = durationDays;
    }


    public String getRankId() {
        return rankId;
    }

    public void setRankId(String rankId) {
        this.rankId = rankId;
    }

    public String getRankNo() {
        return rankNo;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }

    public Long getRankMin() {
        return rankMin;
    }

    public void setRankMin(Long rankMin) {
        this.rankMin = rankMin;
    }

    public Long getRankMax() {
        return rankMax;
    }

    public void setRankMax(Long rankMax) {
        this.rankMax = rankMax;
    }

}
