package com.ls.ner.billing.config.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.api.common.bo.BillingOrderRelaBo;
import com.ls.ner.billing.config.bo.BillingConfigFunc;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;

public interface IBillingConfigDao {

	/**
	 * 
	 * 方法说明：根据订单计费信息查询相应订单计费详细配置
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月9日 下午3:11:59
	 * History:  2016年4月9日 下午3:11:59   lipf   Created.
	 *
	 * @param billingOrderRelaBo
	 * @return
	 *
	 */
	public List<BillingConfigBo> selectByOrderRela(BillingOrderRelaBo billingOrderRelaBo);

	/**
	 * 
	 * 方法说明：定价配置记录数查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月9日 下午3:11:17
	 * History:  2016年4月9日 下午3:11:17   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public int countBillingConfig(BillingConfigQueryCondition condition);

	/**
	 * 
	 * 方法说明：定价配置查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月9日 下午3:11:46
	 * History:  2016年4月9日 下午3:11:46   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public List<BillingConfigBo> selectBillingConfig(BillingConfigQueryCondition condition);

	/**
	 * 
	 * 方法说明：车型调价表格数据查询 根据orgCode、subBe、autoModelNo、applyDate
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:15:21
	 * History:  2016年4月10日 下午2:15:21   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public List<BillingConfigFunc> getBillingAdjustList(BillingConfigQueryCondition condition);

	/**
	 * 
	 * 方法说明：车型调价表格数据记录数查询  暂无用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:15:33
	 * History:  2016年4月10日 下午2:15:33   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public int getBillingAdjustListCount(BillingConfigQueryCondition condition);

	/**
	 * 
	 * 方法说明：新增车型定价的时候更新e_auto_model_rental 标识此车型已经定价
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午4:46:38
	 * History:  2016年4月12日 下午4:46:38   lipf   Created.
	 *
	 * @param inMap
	 *
	 */
	public void updateAutoModelRental(Map<String, String> inMap);

	/**
	 * 
	 * 方法说明：预收计费查询计划取车时间和计划还车时间之间的 在当前时间之前生效的定价配置
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月30日 下午2:43:48
	 * History:  2016年4月30日 下午2:43:48   lipf   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public List<BillingConfigBo> selectPrepayByRela(Map<String, Object> inMap);

	/**
	 * 
	 * 方法说明：
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月24日 下午10:22:24
	 * History:  2016年5月24日 下午10:22:24   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public List<BillingConfigBo> getAutoBillingList(BillingConfigQueryCondition condition);
}
