package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.DepositConfig;
import com.ls.ner.billing.common.bo.DepositConfigExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DepositConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(DepositConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(DepositConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(DepositConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(DepositConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<DepositConfig> selectByExample(DepositConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    DepositConfig selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") DepositConfig record, @Param("example") DepositConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") DepositConfig record, @Param("example") DepositConfigExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(DepositConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(DepositConfig record);
}