package com.ls.ner.billing.common.bo;

import java.util.Date;

public class ChargeItemBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.ITEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long itemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.CHARGE_ITEM_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeItemCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.NEED_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String needFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.ITEM_DESC
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.EFFECT_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String effectFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.TIME_LINE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String timeLine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_charge_item.BUILD_DATE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date buildDate;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.SYSTEM_ID
     *
     * @return the value of e_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.SYSTEM_ID
     *
     * @param systemId the value for e_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.ITEM_ID
     *
     * @return the value of e_charge_item.ITEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getItemId() {
        return itemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.ITEM_ID
     *
     * @param itemId the value for e_charge_item.ITEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.CHARGE_ITEM_CODE
     *
     * @return the value of e_charge_item.CHARGE_ITEM_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeItemCode() {
        return chargeItemCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.CHARGE_ITEM_CODE
     *
     * @param chargeItemCode the value for e_charge_item.CHARGE_ITEM_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeItemCode(String chargeItemCode) {
        this.chargeItemCode = chargeItemCode == null ? null : chargeItemCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.ITEM_NAME
     *
     * @return the value of e_charge_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.ITEM_NAME
     *
     * @param itemName the value for e_charge_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.CHARGE_TYPE
     *
     * @return the value of e_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeType() {
        return chargeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.CHARGE_TYPE
     *
     * @param chargeType the value for e_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.NEED_FLAG
     *
     * @return the value of e_charge_item.NEED_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getNeedFlag() {
        return needFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.NEED_FLAG
     *
     * @param needFlag the value for e_charge_item.NEED_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setNeedFlag(String needFlag) {
        this.needFlag = needFlag == null ? null : needFlag.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.ITEM_DESC
     *
     * @return the value of e_charge_item.ITEM_DESC
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemDesc() {
        return itemDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.ITEM_DESC
     *
     * @param itemDesc the value for e_charge_item.ITEM_DESC
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemDesc(String itemDesc) {
        this.itemDesc = itemDesc == null ? null : itemDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.EFFECT_FLAG
     *
     * @return the value of e_charge_item.EFFECT_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getEffectFlag() {
        return effectFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.EFFECT_FLAG
     *
     * @param effectFlag the value for e_charge_item.EFFECT_FLAG
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setEffectFlag(String effectFlag) {
        this.effectFlag = effectFlag == null ? null : effectFlag.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.TIME_LINE
     *
     * @return the value of e_charge_item.TIME_LINE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getTimeLine() {
        return timeLine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.TIME_LINE
     *
     * @param timeLine the value for e_charge_item.TIME_LINE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setTimeLine(String timeLine) {
        this.timeLine = timeLine == null ? null : timeLine.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_charge_item.BUILD_DATE
     *
     * @return the value of e_charge_item.BUILD_DATE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getBuildDate() {
        return buildDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_charge_item.BUILD_DATE
     *
     * @param buildDate the value for e_charge_item.BUILD_DATE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBuildDate(Date buildDate) {
        this.buildDate = buildDate;
    }
}