<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑优惠券发放">
    <script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
    <style type="text/css">
        .hiddenFlow {
            overflow-x: hidden;
            overflow-y: hidden;
        }
        .om-calendar {
            white-space: nowrap;
            display: inline-block;
            width: 100%;
        }
        .custom-span{

        }
        .custom-span:hover {
            cursor: pointer; /* 鼠标悬浮时显示手型光标 */

        }

    </style>
</ls:head>
<ls:body>
    <ls:form name="couponPutForm" id="couponPutForm" cssClass="hiddenFlow">
        <ls:text name="eftDate" visible="false" property="eftDate"></ls:text>
        <ls:text name="invDate" visible="false" property="invDate"></ls:text>
        <ls:text name="limGetNum" visible="false" property="limGetNum"></ls:text>
        <ls:text name="cpnTimeType" visible="false" property="cpnTimeType"></ls:text>
        <ls:text name="timeDuration" visible="false" property="timeDuration"></ls:text>
        <ls:text name="timeUnit" visible="false" property="timeUnit"></ls:text>

        <ls:text name="custInfoList" visible="false"></ls:text>
        <ls:text name="noticeType" visible="false"></ls:text>
        <ls:text name="noticeCont" visible="false"></ls:text>
        <ls:text name="isAllCust" visible="false"></ls:text>
        <ls:text name="delayPutTime" visible="false"></ls:text>
        <ls:text name="isDelay" visible="false"></ls:text>

        <ls:title text="优惠券信息"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="优惠券名称"/></td>
                <td>
                    <ls:text name="cpnName" enabled="false" property="cpnName" noclear="true"
                             imageKey="search" onClickImage="choiceCoupon"></ls:text>
                    <ls:text name="cpnId" visible="false" property="cpnId"></ls:text>
                </td>

                <td><ls:label text="优惠内容"/></td>
                <td>
                    <ls:text name="busiTypeName" property="busiTypeName" enabled="false"></ls:text>
                    <ls:text name="busiType" property="busiType" visible="false"></ls:text>
                </td>

                <td><ls:label text="优惠券类型"/></td>
                <td>
                    <ls:text name="cpnTypeName" property="cpnTypeName" enabled="false"></ls:text>
                    <ls:text name="cpnType" property="cpnType" visible="false"></ls:text>
                </td>
            </tr>
            <tr>
                <td><ls:label text="面额/折扣"/></td>
                <td><ls:text name="cpnAmt" property="cpnAmt" enabled="false"></ls:text></td>

                <td><ls:label text="有效时间"/></td>
                <td>
                    <ls:text name="effectTime" enabled="false" property="effectTime"/>
                </td>

                <td><ls:label text="优惠券状态"/></td>
                <td>
                    <ls:text name="cpnStatusName" enabled="false" property="cpnStatusName"></ls:text>
                    <ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
                </td>
            </tr>

            <tr>
                <td><ls:label text="发行数量"/></td>
                <td><ls:text name="cpnNum" enabled="false" property="cpnNum"></ls:text>
                </td>

                <td><ls:label text="已领数量"/></td>
                <td>
                    <ls:text name="alrdyGetNum" enabled="false" property="alrdyGetNum"></ls:text>
                </td>
                <td><ls:label text="已发放数量"/></td>
                <td>
                    <ls:text name="putNum" enabled="false" property="putNum"></ls:text>
                </td>
            </tr>
        <tr>

        </tr>
        </table>
        <ls:title text="预约时间"></ls:title>
<table align="center" class="tab_search" >
    <tr style="height: 35px">
        <td style="width: 16ch;">
            <ls:label text="发放时间" needColon="true" style="margin-left: 15px;width: 5ch;  " textAlign="left"/>
        </td>
<%--        background-color: yellow;--%>
        <td style="  width: 25ch ">
            <ls:checklist type="radio"  name="couponPutTime"  required="true" onchanged="isHiddlDelayDate" value="putTimeTypeStr">
                <ls:checkitem value="excutePut" text="立即发放" ></ls:checkitem>
                <ls:checkitem value="delayPut" text="预约发放" ></ls:checkitem>
            </ls:checklist>
        </td>
<%--                background-color: rgba(255, 87, 51, 0.5);--%>
        <td style=" width: 35ch ;" >
<%--            readOnly="${delayTime!=null?ture:false}" property="delayTime"onchanged="timeChangeHandler"--%>
        <ls:date name="delayTime"  format="yyyy-MM-dd HH:mm:ss" visible="ture" id="delayTime" ></ls:date>
        </td>
<%--        background-color: rgba(255, 87, 51, 2);--%>
    <td style=" " class="custom-span" id="cancelDelay"  >
        <ls:button  onclick="cancelDelayFunc" text="取消预约"  style=" text-align: left;float: left;margin-left: 10px;  font-size: 15px;color: #00BFFF; background-color: transparent;
" />
    </td>
    </tr>


<%--    <tr style="">--%>
<%--        <td style="">--%>
<%--            <ls:label text="发放时间" needColon="true" style="margin-left: 15px;width: 5ch;  " textAlign="left"/>--%>
<%--        </td>--%>
<%--            &lt;%&ndash;        background-color: yellow;&ndash;%&gt;--%>
<%--        <td style="  ">--%>
<%--            <ls:checklist type="radio"  name="couponPutTime"  required="true" onchanged="isHiddlDelayDate" value="putTimeTypeStr">--%>
<%--                <ls:checkitem value="excutePut" text="立即发放" ></ls:checkitem>--%>
<%--                <ls:checkitem value="delayPut" text="预约发放" ></ls:checkitem>--%>
<%--            </ls:checklist>--%>
<%--        </td>--%>
<%--            &lt;%&ndash;                background-color: rgba(255, 87, 51, 0.5);&ndash;%&gt;--%>
<%--        <td style=" width: 35ch ;" >--%>
<%--                &lt;%&ndash;            readOnly="${delayTime!=null?ture:false}" property="delayTime"onchanged="timeChangeHandler"&ndash;%&gt;--%>
<%--            <ls:date name="delayTime"  format="yyyy-MM-dd HH:mm:ss" visible="ture" id="delayTime" ></ls:date>--%>
<%--        </td>--%>
<%--            &lt;%&ndash;        background-color: rgba(255, 87, 51, 2);&ndash;%&gt;--%>
<%--        <td style=" " class="" id="cancelDelay"  >--%>
<%--            <ls:button  onclick="cancelDelayFunc" text="取消预约"  style=" text-align: left;float: left;margin-left: 10px;  font-size: 15px;color: #00BFFF; background-color: transparent;--%>
<%--" />--%>
<%--        </td>--%>
<%--       --%>
<%--    </tr>--%>


</table>

        <ls:title text="发放客户"></ls:title>
        <ls:checklist type="radio"  name="custTypeCheckBox" required="true" onchanged="chooseCustType" value="putTypeStr" style="margin-left: 15px;">
            <ls:checkitem value="allCust" text="全部客户" ></ls:checkitem>
            <ls:checkitem value="chooseCust" text="部分客户" ></ls:checkitem>
        </ls:checklist>
<div id="custList">
            <table align="center" class="tab_search" >

                <ls:grid url="" name="custGrid" caption="" primaryKey="sn" width="100%" singleSelect="false"
                         height="180px" showRowNumber="false" showCheckBox="true" cellEdit="true">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="choice" text="选择客户" imageKey="search"
                                            onclick="choiceCust"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="add" text="新增" imageKey="add" onclick="add"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="import" text="导入" imageKey="import"
                                            onclick="importData"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="del" text="删除" imageKey="delete"
                                            onclick="del"></ls:gridToolbarItem>
                        <%--                    <ls:gridToolbarItem name="put" text="发放" imageKey="put" onclick="put"></ls:gridToolbarItem>--%>
                    </ls:gridToolbar>
                    <ls:column caption="sn" name="sn" hidden="true"/>
                    <ls:column caption="客户id" name="custId" hidden="true"/>
                    <ls:column caption="客户名称" name="custName" align="left" readOnly="true"/>
                    <ls:column caption="手机号码" name="mobile">
                        <ls:textEditor onblur="queryMobiByCust" required="true" onValidate="valiMobl();"
                                       type="number"></ls:textEditor>
                    </ls:column>
                    <ls:column caption="客户分类" name="custSortCodeName" readOnly="true"/>
                    <ls:column caption="客户分类" name="custSortCode" hidden="true"/>
                </ls:grid>
            </table>
        </div>
    </ls:form>
<table align="center" class="tab_search" >
    <tr>
        <td colspan="6">
            <div class="pull-right">
                <ls:button text="取消"  onclick="doCancel"/>
                <ls:button text="确认" onclick="put"/>
            </div>
        </td>
    </tr>

</table>


    <ls:script>
        var cstPath = '${cstPath }';
        var cpnPojo={};

        $('#custList').hide();
        function  chooseCustType(){
        var items = custTypeCheckBox.getCheckedItems();
        var _custType= items[0].value;
        if("allCust"==_custType){
           $('#custList').hide();
        }else{
            $('#custList').show();
        }
        }

        $('#cancelDelay').hide();
<%--        单选框若为延迟发放展示发放时间--%>
        function isHiddlDelayDate(){
       var _putTimeTypeStr = couponPutTime.getValue();
        if("delayPut"==_putTimeTypeStr){
        $('#delayTime').show();
        $('#cancelDelay').show();
<%--        填充日期--%>
       var cpnPojoFlag=  LS.isEmpty(cpnPojo.cpnId);
        if((!cpnPojoFlag)&&cpnPojo.cpnId==cpnId.getValue()){
        delayTime.setValue(cpnPojo.delayTime);
        delayTime.setEnabled(false);
        return;
        }
        LS.ajax("~/billing/couponPut/queryCouponDelay?cpnId=" + cpnId.getValue(), '', function (data) {

        if(data==null||data.putId==null){
        $('#cancelDelay').hide();
        delayTime.setValue(null);
        delayTime.setEnabled(true);
        cpnPojo= {};
        return;
        }
        cpnPojo.cpnId=cpnId.getValue();
        cpnPojo.delayTime=data.delayTime;
        cpnPojo.putId=data.putId;

        if(data.putId != undefined && data.putId!=null){
        delayTime.setValue(data.delayTime);
        delayTime.setEnabled(false);
        }else{
        LS.message("error",'调用失败');
        }
        });

        }else{
        $('#delayTime').hide();
        $('#cancelDelay').hide();
        }
        }


        function doCancel(){
        LS.window.close();
        }


        function cancelDelayFunc(){
        LS.confirm('确认是否取消？',function(result){
        if(result){
        LS.ajax("~/billing/couponPut/cancelDelay?putId=" + cpnPojo.putId, '', function (data) {
<%--        {"items":["success"],"itemCount":0,"dicts":[]}--%>
        if(data.items[0] == "success"){
        LS.message("info","取消预约成功！");
        delayTime.setValue(null);
        delayTime.setEnabled(true);
        $('#cancelDelay').hide();
        LS.parent().query();
        }else{
        LS.message("error",data.items[0]);
        }
        });
        }
        });
        }
        <%-- 描述:删除按钮  创建人:biaoxiangd  创建时间:2017-06-04 15:00 --%>
        function del(){
        var items = custGrid.getCheckedItems();
        if(LS.isEmpty(items)){
        LS.message("info","请选择记录");
        return;
        }
        for(var i=0; i < items.length; i++){
        custGrid.removeItem(items[i]);
        }
        }
        <%-- 描述:选择优惠券并回调  创建人:biaoxiangd  创建时间:2017-06-07 20:44 --%>
        function choiceCoupon(){
        LS.dialog("~/billing/coupon/couponManage/05","选择优惠券",800,600,true, null);
        }
        window.setCoupon = function(item){
        cpnName.setValue(item.cpnName);
        cpnId.setValue(item.cpnId);
        busiTypeName.setValue(item.busiTypeName);
        cpnType.setValue(item.cpnType);
        cpnTypeName.setValue(item.cpnTypeName);
        cpnAmt.setValue(item.cpnAmt);
        effectTime.setValue(item.effectTime);
        cpnStatusName.setValue(item.cpnStatusName);
        cpnStatus.setValue(item.cpnStatus);
        cpnNum.setValue(item.cpnNum);
        alrdyGetNum.setValue(item.alrdyGetNum);
        eftDate.setValue(item.eftDate);
        invDate.setValue(item.invDate);
        limGetNum.setValue(item.limGetNum);
        cpnTimeType.setValue(item.cpnTimeType);
        timeDuration.setValue(item.timeDuration);
        timeUnit.setValue(item.timeUnit);

<%--        重新选择优惠卷置空单选框--%>
        $('#delayTime').hide();
        $('#cancelDelay').hide();

        couponPutTime.uncheckItem( couponPutTime.items[0]);
        couponPutTime.uncheckItem(  couponPutTime.items[1] );
        }

        <%-- 描述:客户公共组件  创建人:biaoxiangd  创建时间:2017-06-04 10:52 --%>
        function choiceCust(){
        LS.dialog(cstPath+"/ner/cust/custselect?isSingle=false","选择客户",800,635,true);
        }
        <%-- 描述:导入按钮  创建人:biaoxiangd  创建时间:2017-06-04 10:56 --%>
        function importData(){
        LS.dialog("~/billing/couponPut/importFile","导入文件",600,150,true, null);
        }
        <%-- 描述:导入数据后触发  创建人:biaoxiangd  创建时间:2017-06-04 11:52 --%>
        window.setCust = function(items){
            var itemList = [];
            var allItem = custGrid.getItems();
            var len = allItem.length;
            var flag = true;
            if(!LS.isArray(items)){
                for(var j=0; j< allItem.length;j++){
                    if(items.mobile==allItem[j].mobile){
                        flag = false;
                        break;
                    }
                }
                if(!items.custId){
                    items.custSortCodeName = "未注册用户";
                }
                if(flag){
                    itemList.push(items);
                }
            }else{
                for(var i=0; i< items.length;i++){
                    flag = true;
                    for(var j=0; j< allItem.length;j++){
                        if(items[i].mobile==allItem[j].mobile){
                            flag = false;
                            break;
                        }
                    }
                    if(!items[i].custId){
                        items[i].custSortCodeName = "未注册用户";
                        items[i].custTypeName = "未注册用户";
                    }
                    if(flag){
                        itemList.push(items[i]);
                    }
                }
            }
            for(var i=0; i< itemList.length; i++){
                itemList[i].custSortCode = itemList[i].custType;
                itemList[i].custSortCodeName = itemList[i].custTypeName;
                custGrid.addRow(itemList[i],"last");
            }
        }

        <%-- 描述:发放  创建人:biaoxiangd  创建时间:2017-06-04 15:00 --%>
        function put(){
            var allItem = custGrid.getItems();
            var len = allItem.length;
<%--        重置--%>
        delayPutTime.setValue(null);
        isDelay.setValue(null);
        isAllCust.setValue(null);
        custInfoList.setValue(null);
        if(couponPutTime.value=='putTimeTypeStr'){
            LS.message("info","请选择推送时间！");
            return;
        }
        if(custTypeCheckBox.value=='putTypeStr'){
        LS.message("info","请选择推送客户类型！");
        return;
        }
        isAllCust.setValue(custTypeCheckBox.getValue()=='allCust'?true:false);
        var disabledFlag=delayTime.$input.attr('disabled');
        if(disabledFlag == 'disabled' && couponPutTime.value=='delayPut'){
           LS.message("info","请先取消預約！");
           return;
        }else if(disabledFlag==undefined&&couponPutTime.value=='delayPut'){
        if(LS.isEmpty(delayTime.getValue())){
        LS.message("info","请选择预约时间！");
        return;
        }
        delayPutTime.setValue(delayTime.getValue());
   }
        isDelay.setValue(couponPutTime.value=='excutePut'?0:1);

            if((!isAllCust.getValue())&&len <= 0){
                LS.message("info","请选择客户！");
                return;
            }
            if(LS.isEmpty(cpnId.getValue())){
            LS.message("info","请选择优惠券！");
                return;
            }

            var _alrdyGetNum = parseInt(alrdyGetNum.getValue());
            var _cpnNum = parseInt(cpnNum.getValue());
            var _putNum = parseInt(putNum.getValue());

            if(_alrdyGetNum + _putNum + len > _cpnNum){
                LS.message("info","发放+领取数量不能大于发行数量！");
                return;
            }
            for(var i = 0; i < len; i++){
                var _mobile = allItem[i].mobile;
                var _custId = allItem[i].custId;
                if(valiMobl(_mobile).isValid == false){
                    LS.message("info","手机号码格式不对！");
                    return;
                }
                if(!_custId){
                    LS.message("info","存在未注册用户，请删除后再发放！");
                    return;
                }
            }
            LS.confirm('确认是否发放？',function(result){
                if(result){
<%--                    LS.dialog("~/billing/couponPut/showSms","优惠券发放",400,200,true, null);--%>
                    if(!isAllCust.getValue()){
        custInfoList.setValue(JSON.stringify(allItem));
                 }
                    couponPutForm.submit("~/billing/couponPut/savePutInfo",function(data){

                        var _msg = data.items[0];
                        if(_msg == "success"){
                            LS.message("info","发放成功");
                            LS.parent().query();
                            LS.window.close();
                        }else if(_msg =='aysncAllUser'){
                            LS.message("info","全体客户发放成功");
                            LS.parent().query();
                            LS.window.close();
                        }
                        else{
                            LS.message("error",_msg);
                        }
                    });
                }
            });
        }

        <%-- 描述:选择通知方式后回调  创建人:biaoxiangd  创建时间:2017-06-04 15:53 --%>
        window.setPutMsg = function(msg){
            var allItem = custGrid.getItems();
            noticeType.setValue(msg.noticeType);
            noticeCont.setValue(msg.noticeCont);
            custInfoList.setValue(JSON.stringify(allItem));
            couponPutForm.submit("~/billing/couponPut/savePutInfo",function(data){
                var _msg = data.items[0];
                if(_msg == "success"){
                    LS.message("info","发放成功");
                    LS.parent().query();
                    LS.window.close();
                }else{
                    LS.message("error",_msg);
                }
            });
        }


        <%-- 描述:新增发放，只能编辑手机号码  创建人:biaoxiangd  创建时间:2017-06-03 16:59 --%>
        function add(){
        var allItem = custGrid.getItems();
        var len = allItem.length;
        var item = {};
        item.mobile="";
        item.sn = len+1;
        custGrid.addRow(item,0);
        }
        <%-- 描述:验证手机号码  创建人:biaoxiangd  创建时间:2017-06-04 10:57 --%>
        function valiMobl(data){
        var _falg = true;
        <%--var isMob = /^1(3|4|5|7|8)\d{9}$/;
        if(!isMob.test(data)){
        _falg = false;
        }--%>
        return {
        isValid: _falg,
        message: '手机号码格式错误'
        }
        }

        <%-- 描述:编辑手机号码查询客户明细  创建人:biaoxiangd  创建时间:2017-06-03 17:06 --%>
        function queryMobiByCust(rowId){
            var _mobile = custGrid.getCell(rowId,"mobile");
            if(!_mobile){
                return false;
            }
           <%-- var isMob = /^1(3|4|5|7|8)\d{9}$/;
            if(!isMob.test(_mobile)){
            return false;
            }--%>
            var params = {
                mobile: _mobile
            };
            LS.ajax("~/billing/couponPut/queryMobiByCust",params,function(data){
            var _obj = data.items[0];
            if(_obj){
                var _outCode = _obj.outCode;
                if('0' === _outCode){
                    var _custId = _obj.custId;
                    custGrid.setCell(rowId,"custId",_custId);
                    var _custName = _obj.custName;
                    custGrid.setCell(rowId,"custName",_custName);
                    var _custSortCode = _obj.custSortCode;
                    custGrid.setCell(rowId,"custSortCode",_custSortCode);
                    var _custSortCodeName = _obj.custSortCodeName;
                    custGrid.setCell(rowId,"custSortCodeName",_custSortCodeName);
                }
            } else {
                custGrid.setCell(rowId,"custSortCodeName","未注册用户");
            }
        })
        }

    </ls:script>

</ls:body>
</html>