<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充电计费配置" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="管理单位" /></td>
				<td><ls:text imageKey="search" onClickImage="getOrgs" name="orgCodeName"  property="orgCodeName" enabled="false" onchanged="changeOrgName" /></td>
				<td><ls:label text="计费编号" /></td>
				<td><ls:text name="chcNo"/></td>
				<td><ls:label text="计费名称" /></td>
				<td><ls:text name="chcName"/></td>
			</tr>
			<tr>
				<td><ls:label text="状态" /></td>
				<td><ls:select name="chcStatus" value="1">
					<ls:options property="validFlagList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
				<td><ls:label text="上个版本" /></td>
				<td><ls:text name="upChcNo"/></td>
				<td>
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" width="100%" showCheckBox="true" singleSelect="true" caption="充电计费配置列表" primaryKey="chcNo" >
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doAdd" text="新增"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doCopy" text="复制"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doModifyPrice" text="调价"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="search" onclick="doShow" text="详情" visible="false"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="bottom" onclick="doSend" name="sendBarItem" text="分时下发" visible="false"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="计费编号" name="chcNo" formatFunc="showChcNoDetail"/>
					<ls:column caption="计费名称" name="chcName" />
					<ls:column caption="采集数据类型" name="gatherDataTypeName" hidden="true"/>
					<ls:column caption="费控模型" name="billCtlModeName" />
					<ls:column caption="费控模型" name="billCtlMode" hidden="true"/>
					<ls:column caption="计费模式" name="chargeModeName" />
				   	<ls:column caption="计费模式" name="chargeMode" hidden="true" />
					<ls:column caption="状态" name="chcStatus" hidden="true" />
					<ls:column caption="状态" name="chcStatusName" />
					<ls:column caption="桩生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="操作" name="opr"  formatFunc="opr"/>
					<ls:column caption="上个版本" name="upChcName" formatFunc="showDetail"/>
					<ls:column caption="上个版本"  name="upChcNo" hidden="true"/>
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</ls:form>
	<ls:script>
	var astPath = '${astPath }';
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		chargeGrid.query('~/billing/xpcbc/queryChargeBillingConfs',params,function(){});
	}

    function doAdd(){
		LS.dialog("~/billing/xpcbc/addChargeBillingConf?chcNo=","新增充电计费配置",1000, 600,true, null);
    }



 	function doShow(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要查看详情的记录");
    		return;
	   }
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+item.chcNo,"查看充电费用配置",1000, 600,true, null);
 	}



		chargeGrid.onitemclick = function () {
		var item = chargeGrid.getSelectedItem();
		if (!item) {
		LS.message("info", "请选择一条记录！");
		return;
		}
		var billCtlMode = item.billCtlMode;
		var chcStatus = item.chcStatus;
		var chargeMode = item.chargeMode;

		if (billCtlMode == '2' && chcStatus == '1' && chargeMode == '0202' ) {//远程、分时、生效
		chargeGrid.toolbarItems.sendBarItem.setEnabled(true);
		}else{
		chargeGrid.toolbarItems.sendBarItem.setEnabled(false);
		}
		}


	function doSend(){
		var item = chargeGrid.getSelectedItem();
		if(LS.isEmpty(item)){
		LS.message("error","请选择要下发的记录");
		return;
		}
		var billCtlMode = item.billCtlMode;
		var chcStatus = item.chcStatus;
		var chargeMode = item.chargeMode;

		if(billCtlMode!= '2'){
		LS.message("error","请选择费控模型为远程的记录");
		return;
		}

		if(chargeMode!= '0202'){
		LS.message("error","请选择计费模式为分时的记录");
		return;
		}

		if(chcStatus!= '1'){
		LS.message("error","请选择费状态为有效的记录");
		return;
		}

		LS.confirm('确定要下发该记录?',function(result){
		if(result){
		LS.ajax("~/billing/xpcbc/sendChargeBillingConf?chcNo="+item.chcNo,null,function(data){
		if(data==true){
		LS.message("info","操作成功");
		return;
		}else{
		LS.message("info","操作失败或网络异常，请重试！");
		return;
		}

		});
		}
		});

		}


	function showDetail(rowdata){
		if(rowdata.upChcNo==null){
		 return '--'
		}else{
		var chcName = escapeHtml(rowdata.upChcName);
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowDetail(\"" + rowdata.upChcNo +"\")'>"+chcName+"</a>";
		}
		}
	function escapeHtml(unsafe) {
		if (typeof unsafe !== 'string') {
			return unsafe;
		}
		const map = {
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;',
			'"': '&quot;',
			"'": '&#039;'
		};
		const reg = /[&<>"']/g;
		return unsafe.replace(reg, match => map[match]);
	}

	window.doShowDetail=doShowDetail;
	function doShowDetail(upChcNo){
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+upChcNo,"查看充电费用配置",1000, 600,true, null);
		}

	function showChcNoDetail(rowdata){
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowChcNoDetail(\"" + rowdata.chcNo +"\")'>"+rowdata.chcNo+"</a>";
	}

	window.doShowChcNoDetail=doShowChcNoDetail;
	function doShowChcNoDetail(chcNo){
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+chcNo,"查看充电费用配置",1000, 600,true, null);
	}

 	function doCopy(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要复制的记录");
    		return;
	   }
	   LS.confirm('确定要复制该记录?',function(result){
			if(result){
				LS.ajax("~/billing/xpcbc/copyChargeBillingConf?chcNo="+item.chcNo,null,function(data){
					if(data!=null){
						LS.message("info","复制成功，新的充电计费编号为"+data.chcNo);
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
 	}

	function doModifyPrice(){
		var item = chargeGrid.getSelectedItem();
		if(LS.isEmpty(item)){
			LS.message("error","请选择要复制的记录");
			return;
		}
		if(item.chcStatus != '1'){
			LS.message("error","非有效计费不允许调价");
			return;
		}
		LS.confirm('确定要调价该记录?',function(result){
			if(result){
				LS.ajax("~/billing/xpcbc/modifyPriceChargeBillingConf?chcNo="+item.chcNo,null,function(data){
					if(data!=null){
						LS.message("info","复制成功，新的充电计费编号为"+data.chcNo);
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
	}
 	window.doModify =doModify;
 	function doModify(chcNo){
		LS.dialog("~/billing/xpcbc/addChargeBillingConf?chcNo="+chcNo,"修改充电计费配置",1000, 600,true, null);
 	}
    window.doDel =doDel;
	function doDel(chcNo){
	   LS.confirm('确定要删除该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargebillingconf/delChargeBillingConf?chcNo="+chcNo,null,function(data){
					if(data==true){
						LS.message("info","删除成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
	}
    window.doRelease =doRelease;
	function doRelease(chcNo){
	   var preEndTime = chargeGrid.getCell(chcNo, "eftDate");
	   var endTime = preEndTime.split(":");
	   preEndTime = endTime[0]+":"+endTime[1];
	   var current = (new Date()).Format("yyyy-MM-dd hh:mm");
	   if(current >= preEndTime){
		  LS.message("error","生效时间小于当前时间，不允许发布");
	   	  return;
	   }
	   LS.confirm('确定要发布该记录?',function(result){
			if(result){
				LS.ajax("~/billing/xpcbc/releaseChargeBillingConf?chcNo="+chcNo,null,function(data){
					if(data==true){
						LS.message("info","发布成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
	}

    function doClear(){
      orgCodeName.setValue();
      orgCode.setValue();
	  chcNo.setValue();
	  chcName.setValue();
	  chcStatus.setValue();
	  upChcNo.setValue();
    }

	function getOrgs(){
		var initValue = [];
		js_util.selectOrgTree(false, null, true, initValue, false, function(node){
			orgCodeName.setValue(node.text);
			orgCode.setValue(node.id);
		});
	}

	function changeOrgName(){
		if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
		 orgCode.setValue();
		}
	}

	function opr(rowdata){
		if(rowdata.chcStatus=='2'){//草稿
			return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>"
				+"&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>删除</a>"
				+"&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doRelease(\"" + rowdata.chcNo + "\")'>发布</a>";
		}else if(rowdata.chcStatus=='0'){//无效
			return "";
		}else if(rowdata.chcStatus=='3'){//待生效
			return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>"
			+"&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>删除</a>";
		}else{
            return "";
        }
	}

	//====================================充电站查询=============================================================
    function getPile(){
       LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
    }

    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }

    window.setStation = setStation;
    function setStation(item) {
       stationId.setValue(item.stationId);
       stationName.setValue(item.stationName);
    }
		function init(){
		chargeGrid.toolbarItems.sendBarItem.setEnabled(false);
		}

		window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
		init();
		}



    </ls:script>
</ls:body>
</html>
