<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="付费会员管理"/>
<ls:body>
    <table  align="center" class="tab_search">
        <tr>
            <td>
                <ls:grid url="" caption="付费会员管理" showRowNumber="false" name="vipManage" height="250px" showCheckBox="false"
                         allowScroll="true">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="addButton" text="创建" imageKey="add" onclick="addOne" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="editButton" text="修改" imageKey="edit" onclick="modifyOne" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="detailButton" text="详情" imageKey="search" onclick="detailOne" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="startButton" text="启用" imageKey="start" onclick="startOne" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="stopButton" text="停用" imageKey="stop" onclick="stopOne" ></ls:gridToolbarItem>
                    </ls:gridToolbar>
                    <ls:column caption="主键" name="vipId" hidden="true" width="100px"></ls:column>
                    <ls:column caption="购买类型" name="vipType" width="100px" hidden="true"></ls:column>
                    <ls:column caption="购买类型" name="vipTypeName" width="100px"></ls:column>
                    <ls:column caption="会员价格" name="vipPrice" width="150px"></ls:column>
                    <ls:column caption="新客优惠价" name="vipPriceNew" width="200px"></ls:column>
                    <ls:column caption="老客优惠价" name="vipPriceOld" width="200px"></ls:column>
                    <ls:column caption="状态" name="status" width="200px" hidden="true"></ls:column>
                    <ls:column caption="状态" name="statusName" width="200px"></ls:column>
                </ls:grid>
            </td>
        </tr>
    </table>
    <ls:script>

        query();
        window.query=query;

        function query(){
        vipManage.query("~/vip/vipConfigList");
        }

        function addOne (){
        LS.dialog("~/vip/vipConfigInfo?vipId="+"0"+"&flag="+"add","新增付费会员配置",900,500,true,null);
        }

        function detailOne(){
        var item = vipManage.getSelectedItem();
        if(item==null){
        LS.message("info","请选择一条记录!");
        return;
        }
        LS.dialog("~/vip/vipConfigInfo?vipId="+item.vipId+"&flag="+"detail","详情付费会员配置",900,500,true,null);
        }

        function modifyOne() {
        var item = vipManage.getSelectedItem();
        if(item==null){
        LS.message("info","请选择一条记录!");
        return;
        }
        LS.dialog("~/vip/vipConfigInfo?vipId="+item.vipId+"&flag="+"modify","修改付费会员配置",900,500,true,null);
        }

        function startOne(){
        var item = vipManage.getSelectedItem();
        if(LS.isEmpty(item)){
        LS.message("info","请选择一条记录");
        return;
        }

        if(item.status!="2"){
        LS.message("info","只能对已停用的内容进行启用");
        return;
        }

        var params = {};
        params.ids = [item.vipId];
        LS.ajax("~/vip/startConfig", params, function(e) {
        if (e.items[0]=="Y") {
        LS.message("info", "启用成功！");
        query();
        } else {
        LS.message("error", e.items[0]);
        }
        });
        }

        function stopOne(){
        var item = vipManage.getSelectedItem();
        if(LS.isEmpty(item)){
        LS.message("info","请选择一条记录");
        return;
        }

        if(item.status!="1"){
        LS.message("info","只能对已启用的内容进行停用");
        return;
        }

        var params = {};
        params.ids = [item.vipId];
        LS.ajax("~/vip/stopConfig", params, function(e) {
        if (e.items[0]=="Y") {
        LS.message("info", "停用成功！");
        query();
        } else {
        LS.message("error", e.items[0]);
        }
        });
        }

    </ls:script>
</ls:body>
</html>