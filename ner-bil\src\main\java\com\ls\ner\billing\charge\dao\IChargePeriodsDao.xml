<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargePeriodsDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargePeriodsBo">
		<result column="CHC_NO" property="chcNo" />
		<result column="SN" property="sn" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
		<result column="PRICE" property="price" />
		<result column="TIME_FLAG" property="timeFlag" />
		<result column="ITEM_NO" property="itemNo" />
	 </resultMap>
	 
	 <sql id="queryChargePeriodsWhere">
		<if test="chcNo !=null and chcNo !=''">
		    and b.CHC_NO =#{chcNo}
		</if>
		 <if test="itemNo !=null and itemNo !=''">
			 and b.ITEM_NO =#{itemNo}
		 </if>
	 </sql>
	<sql id="chargePeriodsItem">
		b.CHC_NO,b.SN,b.BEGIN_TIME,b.END_TIME,b.PRICE,b.TIME_FLAG,b.ITEM_NO
	</sql>
	
	<!-- 查询充电分时设置E_CHARGE_PERIODS-->
	<select id="queryChargePeriods" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select  
			<include refid="chargePeriodsItem"></include>
		from E_CHARGE_PERIODS b
		<where>
			<include refid="queryChargePeriodsWhere"></include>
			order by b.sn asc
		</where>
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="selectChargePeriodsByChcNo" resultMap="BaseResultMap">
		select
		<include refid="chargePeriodsItem"></include>
		from E_CHARGE_PERIODS b
		where b.CHC_NO = #{chcNo}
		order by b.sn asc, b.ITEM_NO desc
	</select>

	<!-- 查询充电分时设置条数E_CHARGE_PERIODS -->
	<select id="queryChargePeriodsNum" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultType="int">
		select count(1)
		from E_CHARGE_PERIODS b
		<where>
			<include refid="queryChargePeriodsWhere"></include>
		</where>
	</select>
  
	<!-- 新增充电分时设置E_CHARGE_PERIODS-->
	<insert id="insertChargePeriods" parameterType="com.ls.ner.billing.charge.bo.ChargePeriodsBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_PERIODS
		<trim prefix="(" suffix=")">
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sn !=null and sn !=''">SN,</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME,</if>
			<if test="endTime !=null and endTime !=''">END_TIME,</if>
			<if test="timeFlag !=null and timeFlag !=''">TIME_FLAG,</if>
			<if test="price !=null and price !=''">PRICE,</if>
			<if test="itemNo !=null and itemNo !=''">ITEM_NO,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sn !=null and sn !=''">#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">#{endTime},</if>
			<if test="timeFlag !=null and timeFlag !=''">#{timeFlag},</if>
			<if test="price !=null and price !=''">#{price},</if>
			<if test="itemNo !=null and itemNo !=''">#{itemNo},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电分时设置E_CHARGE_PERIODS -->
	<update id="updateChargePeriods" parameterType="com.ls.ner.billing.charge.bo.ChargePeriodsBo">
		update E_CHARGE_PERIODS
		<set>
			<if test="chcNo !=null and chcNo !=''">CHC_NO =#{chcNo},</if>
			<if test="sn !=null and sn !=''">SN =#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME =#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">END_TIME =#{endTime},</if>
			<if test="timeFlag !=null and timeFlag !=''">TIME_FLAG =#{timeFlag},</if>
			<if test="price !=null and price !=''">PRICE =#{price},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			SYSTEM_ID =#{systemId}
		</where>
	</update>
	
	<!-- 删除充电分时设置E_CHARGE_PERIODS -->
	<delete id="deleteChargePeriods" parameterType="com.ls.ner.billing.charge.bo.ChargePeriodsBo">
		delete from E_CHARGE_PERIODS where CHC_NO =#{chcNo}
	</delete>
</mapper>