package com.ls.ner.billing.gift.bo;

import java.math.BigDecimal;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.bo
 * @ClassName: RechargeCardBo
 * @Author: bdBo<PERSON>enYang
 * @Description:
 * @Date: 2025/4/27 9:22
 * @Version: 1.0
 */
public class RechargeCardBo {

    private String custId;

    /** 充值卡ID */
    private Long cardId;

    /** 充值卡名称 **/
    private String cardName;

    /** 充值卡批次号 */
    private String cardBatchNo;

    /** 总面值 */
    private BigDecimal totalAmt;

    /** 实际面值 */
    private BigDecimal actualAmt;

    /** 赠送部分面值 */
    private BigDecimal giftAmt;

    /** 备注 */
    private String remark;

    /** 充值卡状态：1 启用 0 禁用 */
    private String cardStatus;


    private String cardStatusName;

    /** 充值卡有效期时间类型，1永久有效 2限时有效 */
    private String cardTimeType;

    /** 生效日期 */
    private String eftDate;

    /** 失效日期 */
    private String invDate;

    /** 创建时间 */
    private String createTime;

    /** 创建人员 */
    private String createUser;

    /** 操作时间 */
    private String updateTime;

    /** 操作人员 */
    private String updateUser;

    // Getters and Setters

    /** 总卡量 */
    private Integer totalCardCount;

    /** 有余额卡量 */
    private Integer remainingCardCount;

    /** 已用完卡量 */
    private Integer usedUpCardCount;

    /** 总核销金额 */
    private BigDecimal totalClearCount;

    /** 实际核销金额 */
    private BigDecimal realClearCount;

    /** 赠送部分核销金额 */
    private BigDecimal giftClearCount;

    /** 总余额 */
    private BigDecimal totalBalance;

    /** 实际余额 */
    private BigDecimal actualBalance;


    /** 赠送部分余额 */
    private BigDecimal giftBalance;

    /** 制卡数量 */
    private Integer cardCount;

    /** 每张赠送积分 */
    private String integralNum;

    /**
     * 充值卡ID 修改充值卡校验使用
     */
    private Long upCardId;

    public String getCardStatusName() {
        return cardStatusName;
    }

    public void setCardStatusName(String cardStatusName) {
        this.cardStatusName = cardStatusName;
    }

    public Long getUpCardId() {
        return upCardId;
    }

    public void setUpCardId(Long upCardId) {
        this.upCardId = upCardId;
    }

    public Integer getCardCount() {
        return cardCount;
    }

    public void setCardCount(Integer cardCount) {
        this.cardCount = cardCount;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public Integer getTotalCardCount() {
        return totalCardCount;
    }

    public void setTotalCardCount(Integer totalCardCount) {
        this.totalCardCount = totalCardCount;
    }

    public Integer getRemainingCardCount() {
        return remainingCardCount;
    }

    public void setRemainingCardCount(Integer remainingCardCount) {
        this.remainingCardCount = remainingCardCount;
    }

    public Integer getUsedUpCardCount() {
        return usedUpCardCount;
    }

    public void setUsedUpCardCount(Integer usedUpCardCount) {
        this.usedUpCardCount = usedUpCardCount;
    }

    public BigDecimal getTotalClearCount() {
        return totalClearCount;
    }

    public void setTotalClearCount(BigDecimal totalClearCount) {
        this.totalClearCount = totalClearCount;
    }

    public BigDecimal getRealClearCount() {
        return realClearCount;
    }

    public void setRealClearCount(BigDecimal realClearCount) {
        this.realClearCount = realClearCount;
    }

    public BigDecimal getGiftClearCount() {
        return giftClearCount;
    }

    public void setGiftClearCount(BigDecimal giftClearCount) {
        this.giftClearCount = giftClearCount;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getActualBalance() {
        return actualBalance;
    }

    public void setActualBalance(BigDecimal actualBalance) {
        this.actualBalance = actualBalance;
    }

    public BigDecimal getGiftBalance() {
        return giftBalance;
    }

    public void setGiftBalance(BigDecimal giftBalance) {
        this.giftBalance = giftBalance;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getCardBatchNo() {
        return cardBatchNo;
    }

    public void setCardBatchNo(String cardBatchNo) {
        this.cardBatchNo = cardBatchNo;
    }

    public BigDecimal getActualAmt() {
        return actualAmt;
    }

    public void setActualAmt(BigDecimal actualAmt) {
        this.actualAmt = actualAmt;
    }

    public BigDecimal getGiftAmt() {
        return giftAmt;
    }

    public void setGiftAmt(BigDecimal giftAmt) {
        this.giftAmt = giftAmt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getCardTimeType() {
        return cardTimeType;
    }

    public void setCardTimeType(String cardTimeType) {
        this.cardTimeType = cardTimeType;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getIntegralNum() { return integralNum; }
    public void setIntegralNum(String integralNum) { this.integralNum = integralNum; }
}
