package com.ls.ner.billing.api.market.vo;

import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;
import java.math.BigDecimal;

public class IntegralExchangeBo extends QueryCondition implements Serializable {
    private static final long serialVersionUID = 1L;
    private String integralId; //'订单编号'
    private String goodsId;//'商品id'
    private String custId;//'用户唯一标识'
    private String custMobile;//'用户手机号'
    private String exchangeTime;//兑换时间'
    private String status;//'兑换状态'
    @DicAttribute(dicName = "codeDict", key = "status", subType = "exchangeStatus")
    private String statusName;
    private String number;//兑换数量
    private String integral;//'实付积分'
    private String consignee;//'收货人'
    private String address;//'收货地址'
    private String logisticsName;//'物流公司'
    private String logisticsNo;//'快递单号'
    private String goodsType;//'商品类型'
    private String goodsVrType;//'商品类型'
    private String goodsMode;//'兑换模式'
    private BigDecimal goodsAmt;//'兑换金额'
    @DicAttribute(dicName = "codeDict", key = "goodsType", subType = "goodsType")
    private String goodsTypeName;//'商品类型'
    private String goodsName;//'商品名称'
    private String mobile;//'收货人手机号'
    private String goodsFreight;//商品运费
    @DicAttribute(dicName = "codeDict", key = "goodsFreight", subType = "goodsFreight")
    private String goodsFreightName;//商品运费
    private String startTime;
    private String endTime;
    private String fileId; //图片id

    private int pageNum;//页码
    private int totalNum;//记录数

    private String payChannel; // app 01  小程序 02

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * 地址标识
     */
    private String custAddrId;
    private String getChannel; // 获取来源（putChannel）

    public String getGetChannel() {
        return getChannel;
    }

    public void setGetChannel(String getChannel) {
        this.getChannel = getChannel;
    }

    public String getCustAddrId() {
        return custAddrId;
    }

    public void setCustAddrId(String custAddrId) {
        this.custAddrId = custAddrId;
    }

    public String getIntegralId() {
        return integralId;
    }

    public void setIntegralId(String integralId) {
        this.integralId = integralId;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getGoodsVrType() {
        return goodsVrType;
    }

    public void setGoodsVrType(String goodsVrType) {
        this.goodsVrType = goodsVrType;
    }

    public String getCustMobile() {
        return custMobile;
    }

    public void setCustMobile(String custMobile) {
        this.custMobile = custMobile;
    }

    public String getExchangeTime() {
        return exchangeTime;
    }

    public void setExchangeTime(String exchangeTime) {
        this.exchangeTime = exchangeTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getIntegral() {
        return integral;
    }

    public void setIntegral(String integral) {
        this.integral = integral;
    }

    public String getConsignee() {
        return consignee;
    }

    public void setConsignee(String consignee) {
        this.consignee = consignee;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLogisticsName() {
        return logisticsName;
    }

    public void setLogisticsName(String logisticsName) {
        this.logisticsName = logisticsName;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getGoodsFreight() {
        return goodsFreight;
    }

    public void setGoodsFreight(String goodsFreight) {
        this.goodsFreight = goodsFreight;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getGoodsTypeName() {
        return goodsTypeName;
    }

    public void setGoodsTypeName(String goodsTypeName) {
        this.goodsTypeName = goodsTypeName;
    }

    public String getGoodsFreightName() {
        return goodsFreightName;
    }

    public void setGoodsFreightName(String goodsFreightName) {
        this.goodsFreightName = goodsFreightName;
    }

    public String getGoodsMode() {
        return goodsMode;
    }

    public void setGoodsMode(String goodsMode) {
        this.goodsMode = goodsMode;
    }

    public BigDecimal getGoodsAmt() {
        return goodsAmt;
    }

    public void setGoodsAmt(BigDecimal goodsAmt) {
        this.goodsAmt = goodsAmt;
    }

    @Override
    public String toString() {
        return "IntegralExchangeVo{" +
                "integralId='" + integralId + '\'' +
                ", goodsId='" + goodsId + '\'' +
                ", custId='" + custId + '\'' +
                ", custMobile='" + custMobile + '\'' +
                ", exchangeTime='" + exchangeTime + '\'' +
                ", status='" + status + '\'' +
                ", statusName='" + statusName + '\'' +
                ", number='" + number + '\'' +
                ", integral='" + integral + '\'' +
                ", consignee='" + consignee + '\'' +
                ", address='" + address + '\'' +
                ", logisticsName='" + logisticsName + '\'' +
                ", logisticsNo='" + logisticsNo + '\'' +
                ", goodsType='" + goodsType + '\'' +
                ", goodsTypeName='" + goodsTypeName + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", mobile='" + mobile + '\'' +
                ", goodsFreight='" + goodsFreight + '\'' +
                ", goodsFreightName='" + goodsFreightName + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}
