/**
 *
 * @(#) IBillingService.java
 * @Package com.ls.ner.billing.api
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.service;

import java.util.Map;

/**
 * 类描述：租车产品计费API
 * 
 * @author: lipf
 * @version $Id: IBillingService.java,v 1.1 2016/02/26 02:35:23 34544 Exp $
 * 
 *          History: 2016年2月24日 上午8:55:02 lipf Created.
 * 
 */
public interface IBillingService {
	/**
	 * 
	 * 方法说明：获取车辆租赁收费项目信息
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:41:55
	 * History:  2016年2月24日 上午9:41:55   lipf   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public Map<String, Object> getCarTariff(Map<String, String> inMap);
}
