<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券管理">
    <script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
    <ls:form name="couponForm" id="couponForm">


        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>

                <td ><ls:label text="优惠券名称"></ls:label></td>
                <td><ls:text name="cpnName"></ls:text></td>
                <td><ls:label text="适用运营商"></ls:label></td>
                <td>
                    <ls:select name="buildId"  property="buildId" style="width:100%" readOnly="true">
                        <ls:options  property="buildList" scope="request" text="buildName" value="buildId" />
                    </ls:select>
                </td>

                <td><ls:label text="优惠券状态" ></ls:label></td>
                <td >
                    <ls:select name="cpnStatus" >
                        <ls:options property="cpnStatusList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
            </tr>
            <tr>
                <td><ls:label text="面额区间"></ls:label></td>
                <td>
                    <table style="width:120px" >
                        <tr>
                            <td>
                                <ls:text name="cpnAmtBgn"></ls:text>
                            </td>
                            <td width="5%" style="text-align: center;">
                                至
                            </td>
                            <td>
                                <ls:text name="cpnAmtEnd"></ls:text>
                            </td>
                        </tr>
                    </table>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td >
                    <div class="pull-right">
                        <ls:button text="查询" onclick="query"/>
                        <ls:button text="清空" onclick="clearAll"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="8"><ls:grid url="" name="couponGrid" caption="优惠券列表" showRowNumber="true"
                                         height="350px" showCheckBox="true" singleSelect="${(singleSelect=='' || singleSelect == null)?'true':singleSelect}">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="add" text="新增" imageKey="add"
                                            onclick="addCoupon" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="modify" text="修改" imageKey="edit"
                                            onclick="modifyCoupon" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="delete" text="删除" imageKey="delete"
                                            onclick="deleteCoupon" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="cancel" text="作废" imageKey="cancel"
                                            onclick="cancelCoupon" ></ls:gridToolbarItem>
                    </ls:gridToolbar>
                    <ls:column caption="优惠券编号" name="cpnNo" hidden="true"/>
                    <ls:column caption="主键" name="cpnId" hidden="true"/>
                    <ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="20%"/>
                    <ls:column caption="优惠券类型" name="cpnType" align="center" hidden="true"/>
                    <ls:column caption="优惠券内容" name="busiType" align="center" hidden="true"/>
                    <ls:column caption="减免面额"  formatFunc="cpnAmtFunc" name="cpnAmt" align="center" width="10%"/>
                    <ls:column caption="发行数量" name="cpnNum" align="center" width="6%"/>
                    <ls:column caption="每人限领（张）" name="limGetNum" align="center" width="6%"/>
                    <ls:column caption="已领（张）" name="alrdyGetNum" align="center" width="6%"/>
                    <ls:column caption="有效时间范围" name="effectTime" align="center" width="20%"/>
                    <ls:column caption="状态" name="cpnStatusName" align="center" width="5%"/>
                    <ls:column caption="状态" name="cpnStatus" hidden="true"/>
                    <ls:column caption="创建时间" name="creTime" align="center" width="15%"/>
                    <ls:column caption="生效时间" name="eftDate" hidden="true"/>
                    <ls:column caption="失效时间" name="invDate" hidden="true"/>
                    <ls:column caption="每人限领数量" name="limGetNum" hidden="true"/>
                    <ls:column caption="优惠券生效时间类型" name="cpnTimeType" hidden="true"/>
                    <ls:column caption="生效时间偏移量" name="timeDuration" hidden="true"/>
                    <ls:column caption="生效时间偏移单位" name="timeUnit" hidden="true"/>
                    <ls:pager pageSize="15,20"></ls:pager>
                </ls:grid></td>
            </tr>
        </table>
    </ls:form>

    <ls:script>
        query();

        function addCoupon(){
            LS.dialog("~/billing/coupon/newEditCoupon?handleType=01","创建优惠券",900,500,true, null);
        }

        function modifyCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            if("1"==item.cpnStatus){
                LS.message("info","启用状态不可修改!");
                return;
            }
            LS.dialog("~/billing/coupon/newEditCoupon?cpnId="+item.cpnId,"修改优惠券",950,450,true, null);
        }


        <%-- 描述:点击数据行事件  创建人:biaoxiangd  创建时间:2017-06-07 20:35 --%>
        couponGrid.onitemclick=function(item, event, rowid){
            if(item.cpnStatus == "1" ){ //在用
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.delete.setEnabled(false);
        couponGrid.toolbarItems.cancel.setEnabled(true);
            }else if(item.cpnStatus == "0"){ // 草稿
                couponGrid.toolbarItems.modify.setEnabled(true);
                couponGrid.toolbarItems.delete.setEnabled(true);
                couponGrid.toolbarItems.cancel.setEnabled(false);
            }else if(item.cpnStatus == "2"){ // 失效
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.delete.setEnabled(false);
                couponGrid.toolbarItems.cancel.setEnabled(false);
            }else if(item.cpnStatus == "3"){ // 作废
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.delete.setEnabled(false);
                couponGrid.toolbarItems.cancel.setEnabled(false);
            }
        }


        function enableCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'1'
            };
            LS.ajax("~/billing/coupon/updateStatus",params,function(data){
                if(data.items[0]=="success"){
                    LS.message("info","启用成功!");
                    query();
                }else{
                    LS.message("info","启用失败!");
                }
            });
        }

        function deleteCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            if(item.cpnStatus != '0'){
                LS.message("info","请在草稿状态下删除优惠券!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'0'
            };
            LS.ajax("~/billing/coupon/deleteCoupon",params,function(data){
                if(data.items[0]=="success"){
                    LS.message("info","删除成功!");
                    query();
                }else{
                    LS.message("info","删除失败!");
                }
        });
        }



        window.query=query;
        function query(){
            var _cpnAmtBgn = cpnAmtBgn.getValue();
            var _cpnAmtEnd = cpnAmtEnd.getValue();

            if(!LS.isEmpty(_cpnAmtBgn) && !isValidNum(_cpnAmtBgn)){
                LS.message("info","面额需为数字");
                return;
            }

            if(!LS.isEmpty(_cpnAmtEnd) && !isValidNum(_cpnAmtEnd)){
                LS.message("info","面值需要数字");
                return;
            }

            if(!LS.isEmpty(_cpnAmtBgn)&&!LS.isEmpty(_cpnAmtEnd)&&_cpnAmtBgn>_cpnAmtEnd){
                LS.message("info","请输入正确的面值区间");
                return;
            }

            var flag = '0';
            <%--if(buildId.getValue() == "all"){--%>
                <%--buildId.setValue("");--%>
                <%--flag = '1';--%>
            <%--}--%>

            var datas={};
            datas = couponForm.getFormData();
            couponGrid.query("~/billing/coupon/newGetCoupons",datas);
            <%--if(flag == '1'){--%>
                <%--buildId.setValue("all");--%>
            <%--}--%>
        }

        function isValidNum(n) {
        var reg = /^[+-]?\d+(\.\d+)?$/;
        <%--var reg = /^[0-9]+$/--%>
        return reg.test(n);
        }


        function clearAll(){
            couponForm.clear();
            <%--buildId.setValue("all");--%>
        };

        function cpnAmtFunc(rowdata){
            var _cpnAmt = rowdata.cpnAmt;
            if(_cpnAmt != null && _cpnAmt != ''){
                return _cpnAmt.split(".")[0];
            }else{
                return _cpnAmt;
            }
        }


        function linkFunc(rowdata){
        var _cpnId = rowdata.cpnId;
        var _cpnName = rowdata.cpnName;

        var _cpnStatus = rowdata.cpnStatus;
        if(_cpnStatus == '0'){  //草稿状态下
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showEditDetail(\"" + _cpnId + "\")'>"+_cpnName+"</a></u>";
        }else{
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnId + "\")'>"+_cpnName+"</a></u>";
        }
        }
        window.showEditDetail = function(_cpnId){
            LS.dialog("~/billing/coupon/newEditCoupon?cpnId="+_cpnId,"修改优惠券",950,450,true, null);
        }


        window.showCouponDetail = function(_cpnId){
            LS.dialog("~/billing/coupon/newInitCouponDetail?cpnId="+_cpnId,"优惠券详细信息",950, 550,true);
        }

        function cancelCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'3'
            };
            LS.ajax("~/billing/coupon/updateStatus",params,function(data){
                if(data.items[0]=="success"){
                    LS.message("info","作废成功!");
                    query();
                }else{
                    LS.message("info","作废失败!");
                }
            });
        }

        window.onload = function() {
            initGridHeight('couponForm','couponGrid');
        }
    </ls:script>
</ls:body>
</html>