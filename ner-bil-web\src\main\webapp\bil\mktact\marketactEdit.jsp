<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑活动" />
<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/themes/default/default.css"/>
<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.css"/>
<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/kindeditor-all.js"></script>
<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/lang/zh-CN.js"></script>
<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.js"></script>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="marketactForm" name="marketactForm" enctype="multipart/form-data">
		<ls:text name="actId" property="actId" type="hidden" />
		<ls:text name="fileId" property="fileId" visible="false"></ls:text>
		<ls:text name="allowWithdraw" property="allowWithdraw" type="hidden" />
		<table>
			<tr>
				<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step1.png" alt=""></td>
			</tr>
		</table>
		<table class="tab_search">
			<tr>
				<td><ls:label text="产品业务类" /></td>
				<td>
					<ls:select name="prodBusiType" property="prodBusiType">
<%--						<ls:options property="orderTypeList" scope="request" text="codeName" value="codeValue" />--%>
						<ls:option text="充电" value="02"></ls:option>
					</ls:select>
				</td>
				<td>
					<div style="display: ${actSubTypeVisible}"><ls:label text="赠送类型" /></div>
				</td>
				<td>
					<div style="display: ${actSubTypeVisible}">
						<ls:select name="actSubType" property="actSubType">
							<ls:options property="actSubTypeList" scope="request" text="codeName" value="codeValue"/>
						</ls:select>
					</div>
				</td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="活动名称" ref="actName" /></td>
				<td><ls:text name="actName" property="actName" required="true" /></td>
				<td><ls:label text="活动标签" ref="actLbl" /></td>
				<td><ls:text name="actLbl" property="actLbl" required="true" /></td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="生效时间" ref="effTime" /></td>
				<td>
					<ls:date name="effTime" property="effTime" format="yyyy-MM-dd HH:mm" required="true" />
				</td>
				<td><ls:label text="失效时间" ref="expTime" /></td>
				<td>
					<ls:date name="expTime" property="expTime" format="yyyy-MM-dd HH:mm" required="true" />
				</td>
				<td></td>
			</tr>
			<tr>
				<td>
					<ls:label text="活动描述" ref="actMarks"/>
				</td>
				<td colspan="3">
					<textarea style="height:255px;" id="actMarks"></textarea>
					<ls:text name="actMarks" visible="false" property="actMarks" type="textarea"
							 required="true"></ls:text>
				</td>
				<td></td>
			</tr>

			<tr>
				<td>
					<span class="label">活动图片</span>
				</td>
				<td>
					<input id="fileOriginPath" class="text" name="fileOriginPath" type="file" onchange="javascript:changes(this);"/>
					<td><span style="color: red">* 建议分辨率:750x250，大小<10M</span></td>
				</td>
			</tr>
			<tr >
				<td></td>
				<td id="image" ></td>
				<td>
					<ls:button id="del" text="删除" onclick="delImg" />
				</td>
			</tr>
			<%--<tr id="allow_withdraw">
				<td>
					<div class="withdraw"><ls:label text="充值金是否允许提现" ref="allowWithdraw"/></div>
				</td>
				<td>
					<div class="withdraw">
						<ls:checklist name="allowWithdraw" property="allowWithdraw" type="radio" required="true">
							<ls:checkitem value="0" text="否"/>
							<ls:checkitem value="1" text="是"/>
						</ls:checklist>
					</div>
				</td>
				<td></td>
				<td></td>
				<td></td>
			</tr>--%>
			<tr>
				<td><div class="point"><ls:label text="所需积分" ref="needPointNum" /></div></td>
				<td><div class="point"><ls:text name="needPointNum" type="number" property="needPointNum" required="true" /></div></td>
				<td colspan="3"></td>
			</tr>
			<tr style="display: none">
				<td><ls:label text="活动渠道" ref="actChannel" /></td>
				<td colspan="3">
					<ls:checklist name="actChannel" property="actChannel" type="checkbox" required="true" columns="4">
						<ls:checkitems property="channelCheckList" scope="request" text="text" value="value"/>
					</ls:checklist>
				</td>
				<td></td>
			</tr>
			<tr style="display: none">
				<td><ls:label text="活动类型" ref="actType" /></td>
				<td>
					<ls:select name="actType" property="actType" onchanged="actTypeChg" required="true" >
						<ls:options property="actTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td colspan="3"></td>
			</tr>
			<tr>
				<td>
					<ls:label text="活动详情描述" ref="actDetailMarks"/>
				</td>
				<td colspan="3">
					<textarea style="height:255px;" id="actDetailMarks"></textarea>
					<ls:text name="actDetailMarks" visible="false" property="actDetailMarks" type="textarea"
							 required="true"></ls:text>
				</td>
				<td></td>
			</tr>
		</table>
	</ls:form>
	<table>
		<tr>
			<td>
				<div class="pull-right" style="margin-top: 150px">
					<ls:button text="下一步" onclick="doNextStep" />
				</div>
			</td>
		</tr>
	</table>
	<script type="text/javascript" >
		function changes(imgFile){

			var file =document.getElementById("fileOriginPath").files[0];
			var strImage='<img src="'+ createObjectURL(file)+'" style="max-width: 300px;max-height: 180px;"></img>';
			document.getElementById("image").innerHTML=strImage;
		}

		function createObjectURL(blob) {
			if (window.URL) {
				return window.URL.createObjectURL(blob);
			} else if (window.webkitURL) {
				return window.webkitURL.createObjectURL(blob);
			} else {
				return null;
			}
		}
	</script>
	<ls:script>

		var editor;
		var editor2;
		var pref = "<%=pubPath %>/pub/kindeditor";
		var pubPath = '${pubPath }';

		KindEditor.ready(function(K) {
		editor = K.create('#actDetailMarks', {
		cssPath : pref + '/plugins/code/prettify.css',
		uploadJson : pubPath + '/pub/picture/upload',
		items : [
		'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
		'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
		'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
		'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
		'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
		'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
		'table', 'hr', 'emoticons', 'pagebreak',
		'link', 'unlink'
		],
		extraFileUploadParams : {
		relaTable: 'pOpenInfo'
		},
		afterUpload : function(data) {

		},
		<%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
		allowFileManager : true,
		afterCreate : function() {
		}
		});

		editor2 = K.create('#actMarks', {
		cssPath : pref + '/plugins/code/prettify.css',
		uploadJson : pubPath + '/pub/picture/upload',
		items : [
		'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
		'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
		'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
		'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
		'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
		'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
		'table', 'hr', 'emoticons', 'pagebreak',
		'link', 'unlink'
		],
		extraFileUploadParams : {
		relaTable: 'pOpenInfo'
		},
		afterUpload : function(data) {

		},
		<%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
		allowFileManager : true,
		afterCreate : function() {
		}
		});

		prettyPrint();
		});

	window.onload = function(){
		if(!LS.isEmpty(fileId.getValue())){
			var strImage='<img src="<%=request.getAttribute("pubPath") %>/attach/'+fileId.getValue()+'" style="max-width: 300px;max-height: 180px;"></img>';
			document.getElementById("image").innerHTML=strImage;
			$("#del").show();
		}else{
			$("#del").hide();
		}
		var _actType = actType.getValue();
		if(!LS.isEmpty(_actType)){
			actType.setReadOnly(true);
		}
		if(!LS.isEmpty(actDetailMarks.getValue())){
			editor.html(actDetailMarks.getValue());
		}
		if(!LS.isEmpty(actMarks.getValue())){
			editor2.html(actMarks.getValue());
		}


       <%-- if (_actType == '07') {
            $(".withdraw").css("display", "block");
        } else {
            $(".withdraw").css("display", "none");
        }--%>
	}
	if(actType.getValue() == '08'){
		$(".point").css("display", "block");
	}else{
		$(".point").css("display", "none");
	}
	function actTypeChg() {
		var actTypeValue = actType.getValue();
		<%--if (actTypeValue!="07") {
			LS.message("info", "目前活动类型只支持预存赠送。");
			actType.setValue('07');
		}--%>
	}

	function delImg(){
	if(!LS.isEmpty(fileId.getValue())){
	LS.ajax(pubPath+"/attach/delete?id=" + fileId.getValue(), {}, function(result) {
	if (result=="SUCCESS") {
	LS.message("info", "删除成功！");
	location.reload();
	} else {
	LS.message("error", result);
	}
	});
	}
	}

	function doNextStep() {
		if(!marketactForm.valid()){
        	return;
      	}
      	var eff = effTime.getValue();
		var exp = expTime.getValue();
		if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff>exp) {
			LS.message("info", "生效时间不能大于失效时间。");
      		return;
		}

		if(typeof(fileOriginPath) != "undefined"){
			if(!fileOriginPath.value==null&&!fileOriginPath.value==''&&!/\.(gif|jpg|jpeg|png|bmp)$/.test(document.getElementById("fileOriginPath").value.toLowerCase())){
				LS.message("warn","图片类型必须是gif,jpeg,jpg,png中的一种");
				return;
			}
		}

<%--		var _filename=document.getElementById("fileOriginPath").value;--%>

		//将富文本内容设置回文本框
		var html = editor.html();
		actDetailMarks.setValue(html);
		var html2 = editor2.html();
		actMarks.setValue(html2);

		if(actDetailMarks.getValue().length>3000){
		LS.message("info","活动详情描述长度不能超过2000个字符");
		return;
		}

		if(actMarks.getValue().length>3000){
		LS.message("info","活动描述长度不能超过3000个字符");
		return;
		}
		<%--		if(LS.isEmpty(fileId.getValue())  &&  LS.isEmpty(_filename)){--%>
<%--			LS.message("info","请上传活动图片");--%>
<%--			return;--%>
<%--		}--%>
      	<%--var actTypeValue = actType.getValue();
      	if (actTypeValue != "07") {
      		LS.message("info", "目前活动类型只支持预存赠送。");
      		return;
      	}--%>
		marketactForm.submit("~/marketacts/saving",function(e){
        	if(e.items[0][0]=="T"){
        		LS.parent().doSearch();
	          	<%--window.location.href="<%=request.getContextPath() %>/sections/init?actId="+e.items[0][1];--%>
		<%-- 描述:转发地址变更  创建人:biaoxiangd  创建时间:2017/7/9 0:12 --%>
				window.location.href = e.items[0][1];
        	}
        	else {
          		LS.message("info", "操作失败或网络异常，请重试！");
        	}
      	});
	}

	function doReturn() {
		LS.parent().doSearch();
		setTimeout("LS.window.close()",200);
	}
    </ls:script>
</ls:body>
</html>
