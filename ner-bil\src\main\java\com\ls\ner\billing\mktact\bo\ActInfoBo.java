package com.ls.ner.billing.mktact.bo;

import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;
import java.util.List;

/**
 * 描述:资讯信息
 * ActInfoBo.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 10:06
 **/
public class ActInfoBo  extends QueryCondition implements Serializable{

	private static final long serialVersionUID = 6963529168544552864L;

	private String actId; // 活动ID
	private String actType; // 活动类型
	private String actInfoId; // 活动资讯ID
	private String coberPic; // 封面图片，文件路径
	private String attachId;
	private String infoType; //资讯类别，1图文资讯 2链接资讯
	private String content; // 内容
	private String contentUrl; // 资讯类别=1时有效，图文网址，为CONTENT生成的静态html
	private String linkUrl;// 资讯类别=2时有效，链接网址
	private String picFlag; // 0新增；1删除
	private String relaId;		//图片relaId



	public String getRelaId() {
		return relaId;
	}

	public void setRelaId(String relaId) {
		this.relaId = relaId;
	}

	public String getPicFlag() {
		return picFlag;
	}

	public void setPicFlag(String picFlag) {
		this.picFlag = picFlag;
	}

	public String getActId() {
		return actId;
	}

	public void setActId(String actId) {
		this.actId = actId;
	}

	public String getActType() {
		return actType;
	}

	public void setActType(String actType) {
		this.actType = actType;
	}

	public String getActInfoId() {
		return actInfoId;
	}

	public void setActInfoId(String actInfoId) {
		this.actInfoId = actInfoId;
	}

	public String getCoberPic() {
		return coberPic;
	}

	public void setCoberPic(String coberPic) {
		this.coberPic = coberPic;
	}

	public String getAttachId() {
		return attachId;
	}

	public void setAttachId(String attachId) {
		this.attachId = attachId;
	}

	public String getInfoType() {
		return infoType;
	}

	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getContentUrl() {
		return contentUrl;
	}

	public void setContentUrl(String contentUrl) {
		this.contentUrl = contentUrl;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}
}
