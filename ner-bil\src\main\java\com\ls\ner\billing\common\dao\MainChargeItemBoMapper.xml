<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.MainChargeItemBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.MainChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="MAIN_NO" jdbcType="VARCHAR" property="mainNo" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="CHARGE_TYPE" jdbcType="VARCHAR" property="chargeType" />
    <result column="CHARGE_UNIT" jdbcType="VARCHAR" property="chargeUnit" />
    <result column="CHARGE_NUM" jdbcType="DECIMAL" property="chargeNum" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, MAIN_NO, PLAN_NO, CHARGE_MODE, CHARGE_TYPE, CHARGE_UNIT, CHARGE_NUM, DATA_OPER_TIME, 
    DATA_OPER_TYPE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_main_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_main_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_main_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_main_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_main_charge_item (SYSTEM_ID, MAIN_NO, PLAN_NO, 
      CHARGE_MODE, CHARGE_TYPE, CHARGE_UNIT, 
      CHARGE_NUM, DATA_OPER_TIME, DATA_OPER_TYPE
      )
    values (#{systemId,jdbcType=BIGINT}, #{mainNo,jdbcType=VARCHAR}, #{planNo,jdbcType=VARCHAR}, 
      #{chargeMode,jdbcType=VARCHAR}, #{chargeType,jdbcType=VARCHAR}, #{chargeUnit,jdbcType=VARCHAR}, 
      #{chargeNum,jdbcType=DECIMAL}, #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_main_charge_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="mainNo != null">
        MAIN_NO,
      </if>
      <if test="planNo != null">
        PLAN_NO,
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE,
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE,
      </if>
      <if test="chargeUnit != null">
        CHARGE_UNIT,
      </if>
      <if test="chargeNum != null">
        CHARGE_NUM,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="mainNo != null">
        #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="planNo != null">
        #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="chargeUnit != null">
        #{chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="chargeNum != null">
        #{chargeNum,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_main_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_item
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.mainNo != null">
        MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      </if>
      <if test="record.planNo != null">
        PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeMode != null">
        CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeType != null">
        CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeUnit != null">
        CHARGE_UNIT = #{record.chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeNum != null">
        CHARGE_NUM = #{record.chargeNum,jdbcType=DECIMAL},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_item
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      CHARGE_UNIT = #{record.chargeUnit,jdbcType=VARCHAR},
      CHARGE_NUM = #{record.chargeNum,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_item
    <set>
      <if test="mainNo != null">
        MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="planNo != null">
        PLAN_NO = #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="chargeUnit != null">
        CHARGE_UNIT = #{chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="chargeNum != null">
        CHARGE_NUM = #{chargeNum,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.MainChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_item
    set MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      PLAN_NO = #{planNo,jdbcType=VARCHAR},
      CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      CHARGE_UNIT = #{chargeUnit,jdbcType=VARCHAR},
      CHARGE_NUM = #{chargeNum,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>