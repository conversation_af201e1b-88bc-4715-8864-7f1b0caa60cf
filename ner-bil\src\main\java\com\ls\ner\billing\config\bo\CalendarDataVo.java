/**
 *
 * @(#) CalendarDataVo.java
 * @Package com.ls.ner.billing.api.config.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.bo;

/**
 * 类描述：日历视图返回结果VO
 * 
 * @author: lipf
 * @version $Id: Exp$
 * 
 *          History: 2016年4月11日 上午9:26:42 lipf Created.
 * 
 */
public class CalendarDataVo {
	String id;
	String title;
	String start;
	String end;
	String url;

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getStart() {
		return this.start;
	}

	public void setStart(String start) {
		this.start = start;
	}

	public String getEnd() {
		return this.end;
	}

	public void setEnd(String end) {
		this.end = end;
	}

	public String getUrl() {
		return this.url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
}
