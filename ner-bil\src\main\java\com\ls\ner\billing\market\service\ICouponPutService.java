package com.ls.ner.billing.market.service;

import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPutBo;
import com.ls.ner.billing.market.bo.DelayInfo;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.pt.poseidon.common.exception.BusinessWarning;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;


public interface ICouponPutService {

	/*
	 * 保存导入的数据
	 */
	List<Map> saveFile(MultipartHttpServletRequest request) throws IOException;
	/**
	 * 保存发放信息
	 * description
	 * 创建时间 2016年6月2日下午5:30:48
	 * 创建人 lise
	 * @throws ParseException 
	 * @throws NumberFormatException 
	 */
	Map<String,String> excuteCouponPut(CouponPutBo bo,boolean isPreSaveCouponPutDet) throws BusinessWarning;


	Map<String,Object> excuteCouponPutForMyCar(CouponPutBo bo,boolean isPreSaveCouponPutDet) throws BusinessWarning;
	String savePutCouponPut(CouponPutBo bo) throws BusinessWarning;
	/**
	 * @param bo
	 * @description 小鹏优惠券发放
	 * <AUTHOR>
	 * @create 2018-06-25 15:55:52
	 */
	Map<String,String> xpSavePutInfo(CouponPutBo bo) throws BusinessWarning;
	/**
	 * 查询发放日记 
	 * description
	 * 创建时间 2016年6月3日上午11:02:24
	 * 创建人 lise
	 */
	List<CouponPutBo> getCouponPutLog(MarketCondition condition);
	/**
	 * 统计发放日记数量
	 * description
	 * 创建时间 2016年6月3日上午11:03:09
	 * 创建人 lise
	 */
	int getCouponPutLogCount(MarketCondition condition);
	/**
	 * 获取优惠券
	 * description
	 * 创建时间 2016年6月3日下午3:53:27
	 * 创建人 lise
	 */
	CouponBo getCoupon(MarketCondition condition);
	/**
	 * 获取优惠券发放
	 * description
	 * 创建时间 2016年6月4日下午5:17:44
	 * 创建人 lise
	 */
	List<CouponBo> getCouponPut(MarketCondition condition);
	/**
	 * 统计优惠券发放数量
	 * description
	 * 创建时间 2016年6月4日下午5:18:04
	 * 创建人 lise
	 */
	int getCouponPutCount(MarketCondition condition);

	/**
	*描述:通过手机号码获取客户信息
	* @param:
	*@return: 
	*创建人:biaoxiangd
	*创建时间: 2017-06-04 11:38
	*/
	Map<String,Object> queryMobiByCust(Map<String, Object> dataParams);


	/**
	 * 获取优惠券发放记录详情
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public List<CouponPutBo> getCouponPutDetLog(MarketCondition condition);

	/**
	 * 统计优惠券发放记录详情
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public int getCouponPutLogDetCount(MarketCondition condition);

	/**
	 * 描述:REST05-11 优惠券
	 * REST05-11-02 优惠券领取
	 *
	 * @param: [map]
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 * 创建人:biaoxiangd
	 * 创建时间: 2017-06-13 14:27
	 */
	public Map<String, Object> couponsRestPOST(Map<String, Object> paramMap) throws BusinessWarning;

	/**
	 *  为所有用户发放优惠券
	 * @param bo
	 */
	void excuteCouponPutAllCust(CouponPutBo bo);


	CouponBo getCouponDetail(MarketCondition condition);

	void saveCouponPutDetList(List<Map<String, Object>> custList, String putId, String putTime);

	boolean updateDelayByPutId(String putId, Integer delayValue,Integer expectDelay);


	DelayInfo qureyCouponDelayInfo(String cpnId);
}
