package com.ls.ner.billing.common.bo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehiclePriceBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public VehiclePriceBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNull() {
            addCriterion("BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNotNull() {
            addCriterion("BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBillingNoEqualTo(String value) {
            addCriterion("BILLING_NO =", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotEqualTo(String value) {
            addCriterion("BILLING_NO <>", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThan(String value) {
            addCriterion("BILLING_NO >", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThanOrEqualTo(String value) {
            addCriterion("BILLING_NO >=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThan(String value) {
            addCriterion("BILLING_NO <", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThanOrEqualTo(String value) {
            addCriterion("BILLING_NO <=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLike(String value) {
            addCriterion("BILLING_NO like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotLike(String value) {
            addCriterion("BILLING_NO not like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIn(List<String> values) {
            addCriterion("BILLING_NO in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotIn(List<String> values) {
            addCriterion("BILLING_NO not in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoBetween(String value1, String value2) {
            addCriterion("BILLING_NO between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotBetween(String value1, String value2) {
            addCriterion("BILLING_NO not between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBatchIsNull() {
            addCriterion("BATCH is null");
            return (Criteria) this;
        }

        public Criteria andBatchIsNotNull() {
            addCriterion("BATCH is not null");
            return (Criteria) this;
        }

        public Criteria andBatchEqualTo(Long value) {
            addCriterion("BATCH =", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotEqualTo(Long value) {
            addCriterion("BATCH <>", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchGreaterThan(Long value) {
            addCriterion("BATCH >", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchGreaterThanOrEqualTo(Long value) {
            addCriterion("BATCH >=", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchLessThan(Long value) {
            addCriterion("BATCH <", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchLessThanOrEqualTo(Long value) {
            addCriterion("BATCH <=", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchIn(List<Long> values) {
            addCriterion("BATCH in", values, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotIn(List<Long> values) {
            addCriterion("BATCH not in", values, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchBetween(Long value1, Long value2) {
            addCriterion("BATCH between", value1, value2, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotBetween(Long value1, Long value2) {
            addCriterion("BATCH not between", value1, value2, "batch");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNull() {
            addCriterion("ORG_CODE is null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIsNotNull() {
            addCriterion("ORG_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andOrgCodeEqualTo(String value) {
            addCriterion("ORG_CODE =", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotEqualTo(String value) {
            addCriterion("ORG_CODE <>", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThan(String value) {
            addCriterion("ORG_CODE >", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_CODE >=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThan(String value) {
            addCriterion("ORG_CODE <", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLessThanOrEqualTo(String value) {
            addCriterion("ORG_CODE <=", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeLike(String value) {
            addCriterion("ORG_CODE like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotLike(String value) {
            addCriterion("ORG_CODE not like", value, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeIn(List<String> values) {
            addCriterion("ORG_CODE in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotIn(List<String> values) {
            addCriterion("ORG_CODE not in", values, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeBetween(String value1, String value2) {
            addCriterion("ORG_CODE between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andOrgCodeNotBetween(String value1, String value2) {
            addCriterion("ORG_CODE not between", value1, value2, "orgCode");
            return (Criteria) this;
        }

        public Criteria andRtNoIsNull() {
            addCriterion("RT_NO is null");
            return (Criteria) this;
        }

        public Criteria andRtNoIsNotNull() {
            addCriterion("RT_NO is not null");
            return (Criteria) this;
        }

        public Criteria andRtNoEqualTo(String value) {
            addCriterion("RT_NO =", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotEqualTo(String value) {
            addCriterion("RT_NO <>", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoGreaterThan(String value) {
            addCriterion("RT_NO >", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoGreaterThanOrEqualTo(String value) {
            addCriterion("RT_NO >=", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLessThan(String value) {
            addCriterion("RT_NO <", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLessThanOrEqualTo(String value) {
            addCriterion("RT_NO <=", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLike(String value) {
            addCriterion("RT_NO like", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotLike(String value) {
            addCriterion("RT_NO not like", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoIn(List<String> values) {
            addCriterion("RT_NO in", values, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotIn(List<String> values) {
            addCriterion("RT_NO not in", values, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoBetween(String value1, String value2) {
            addCriterion("RT_NO between", value1, value2, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotBetween(String value1, String value2) {
            addCriterion("RT_NO not between", value1, value2, "rtNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIsNull() {
            addCriterion("AUTO_MODEL_NO is null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIsNotNull() {
            addCriterion("AUTO_MODEL_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO =", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO <>", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoGreaterThan(String value) {
            addCriterion("AUTO_MODEL_NO >", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoGreaterThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO >=", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLessThan(String value) {
            addCriterion("AUTO_MODEL_NO <", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLessThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO <=", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLike(String value) {
            addCriterion("AUTO_MODEL_NO like", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotLike(String value) {
            addCriterion("AUTO_MODEL_NO not like", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIn(List<String> values) {
            addCriterion("AUTO_MODEL_NO in", values, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotIn(List<String> values) {
            addCriterion("AUTO_MODEL_NO not in", values, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NO between", value1, value2, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NO not between", value1, value2, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andEffectTimeIsNull() {
            addCriterion("EFFECT_TIME is null");
            return (Criteria) this;
        }

        public Criteria andEffectTimeIsNotNull() {
            addCriterion("EFFECT_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andEffectTimeEqualTo(Date value) {
            addCriterion("EFFECT_TIME =", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeNotEqualTo(Date value) {
            addCriterion("EFFECT_TIME <>", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeGreaterThan(Date value) {
            addCriterion("EFFECT_TIME >", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("EFFECT_TIME >=", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeLessThan(Date value) {
            addCriterion("EFFECT_TIME <", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeLessThanOrEqualTo(Date value) {
            addCriterion("EFFECT_TIME <=", value, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeIn(List<Date> values) {
            addCriterion("EFFECT_TIME in", values, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeNotIn(List<Date> values) {
            addCriterion("EFFECT_TIME not in", values, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeBetween(Date value1, Date value2) {
            addCriterion("EFFECT_TIME between", value1, value2, "effectTime");
            return (Criteria) this;
        }

        public Criteria andEffectTimeNotBetween(Date value1, Date value2) {
            addCriterion("EFFECT_TIME not between", value1, value2, "effectTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIsNull() {
            addCriterion("INVALID_TIME is null");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIsNotNull() {
            addCriterion("INVALID_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeEqualTo(Date value) {
            addCriterion("INVALID_TIME =", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotEqualTo(Date value) {
            addCriterion("INVALID_TIME <>", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeGreaterThan(Date value) {
            addCriterion("INVALID_TIME >", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("INVALID_TIME >=", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeLessThan(Date value) {
            addCriterion("INVALID_TIME <", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeLessThanOrEqualTo(Date value) {
            addCriterion("INVALID_TIME <=", value, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeIn(List<Date> values) {
            addCriterion("INVALID_TIME in", values, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotIn(List<Date> values) {
            addCriterion("INVALID_TIME not in", values, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeBetween(Date value1, Date value2) {
            addCriterion("INVALID_TIME between", value1, value2, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andInvalidTimeNotBetween(Date value1, Date value2) {
            addCriterion("INVALID_TIME not between", value1, value2, "invalidTime");
            return (Criteria) this;
        }

        public Criteria andIsCoverIsNull() {
            addCriterion("IS_COVER is null");
            return (Criteria) this;
        }

        public Criteria andIsCoverIsNotNull() {
            addCriterion("IS_COVER is not null");
            return (Criteria) this;
        }

        public Criteria andIsCoverEqualTo(String value) {
            addCriterion("IS_COVER =", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverNotEqualTo(String value) {
            addCriterion("IS_COVER <>", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverGreaterThan(String value) {
            addCriterion("IS_COVER >", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverGreaterThanOrEqualTo(String value) {
            addCriterion("IS_COVER >=", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverLessThan(String value) {
            addCriterion("IS_COVER <", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverLessThanOrEqualTo(String value) {
            addCriterion("IS_COVER <=", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverLike(String value) {
            addCriterion("IS_COVER like", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverNotLike(String value) {
            addCriterion("IS_COVER not like", value, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverIn(List<String> values) {
            addCriterion("IS_COVER in", values, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverNotIn(List<String> values) {
            addCriterion("IS_COVER not in", values, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverBetween(String value1, String value2) {
            addCriterion("IS_COVER between", value1, value2, "isCover");
            return (Criteria) this;
        }

        public Criteria andIsCoverNotBetween(String value1, String value2) {
            addCriterion("IS_COVER not between", value1, value2, "isCover");
            return (Criteria) this;
        }

        public Criteria andPriorityIsNull() {
            addCriterion("PRIORITY is null");
            return (Criteria) this;
        }

        public Criteria andPriorityIsNotNull() {
            addCriterion("PRIORITY is not null");
            return (Criteria) this;
        }

        public Criteria andPriorityEqualTo(Integer value) {
            addCriterion("PRIORITY =", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotEqualTo(Integer value) {
            addCriterion("PRIORITY <>", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityGreaterThan(Integer value) {
            addCriterion("PRIORITY >", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityGreaterThanOrEqualTo(Integer value) {
            addCriterion("PRIORITY >=", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityLessThan(Integer value) {
            addCriterion("PRIORITY <", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityLessThanOrEqualTo(Integer value) {
            addCriterion("PRIORITY <=", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityIn(List<Integer> values) {
            addCriterion("PRIORITY in", values, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotIn(List<Integer> values) {
            addCriterion("PRIORITY not in", values, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityBetween(Integer value1, Integer value2) {
            addCriterion("PRIORITY between", value1, value2, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotBetween(Integer value1, Integer value2) {
            addCriterion("PRIORITY not between", value1, value2, "priority");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNull() {
            addCriterion("DATA_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNotNull() {
            addCriterion("DATA_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE =", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <>", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThan(String value) {
            addCriterion("DATA_OPER_TYPE >", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE >=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThan(String value) {
            addCriterion("DATA_OPER_TYPE <", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLike(String value) {
            addCriterion("DATA_OPER_TYPE like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotLike(String value) {
            addCriterion("DATA_OPER_TYPE not like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE not in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE not between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("VERSION is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("VERSION is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("VERSION =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("VERSION <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("VERSION >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("VERSION >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("VERSION <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("VERSION <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("VERSION like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("VERSION not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("VERSION in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("VERSION not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("VERSION between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("VERSION not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("STATUS is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("STATUS =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("STATUS <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("STATUS >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("STATUS >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("STATUS <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("STATUS <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("STATUS like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("STATUS not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("STATUS in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("STATUS not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("STATUS between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("STATUS not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceIsNull() {
            addCriterion("UNIFIED_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceIsNotNull() {
            addCriterion("UNIFIED_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceEqualTo(String value) {
            addCriterion("UNIFIED_PRICE =", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceNotEqualTo(String value) {
            addCriterion("UNIFIED_PRICE <>", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceGreaterThan(String value) {
            addCriterion("UNIFIED_PRICE >", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceGreaterThanOrEqualTo(String value) {
            addCriterion("UNIFIED_PRICE >=", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceLessThan(String value) {
            addCriterion("UNIFIED_PRICE <", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceLessThanOrEqualTo(String value) {
            addCriterion("UNIFIED_PRICE <=", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceLike(String value) {
            addCriterion("UNIFIED_PRICE like", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceNotLike(String value) {
            addCriterion("UNIFIED_PRICE not like", value, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceIn(List<String> values) {
            addCriterion("UNIFIED_PRICE in", values, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceNotIn(List<String> values) {
            addCriterion("UNIFIED_PRICE not in", values, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceBetween(String value1, String value2) {
            addCriterion("UNIFIED_PRICE between", value1, value2, "unifiedPrice");
            return (Criteria) this;
        }

        public Criteria andUnifiedPriceNotBetween(String value1, String value2) {
            addCriterion("UNIFIED_PRICE not between", value1, value2, "unifiedPrice");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_vehicle_price
     *
     * @mbggenerated do_not_delete_during_merge Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}