package com.ls.ner.billing.charge.bo;


/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 充电分时设置   E_CHARGE_PERIODS
 */
public class ChargePeriodsBo {
	private String systemId;//主键
	private String chcNo;//充电计费编号
	private String sn;//排序号
	private String beginTime;//开始时段 00:00
	private String endTime;//结束时段 00:00
	private String price;//计费单价
	private String timeFlag;//分时段

	private String itemNo;//费用项编号
	private String serPrice; //服务费单价


	
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getTimeFlag() {
		return timeFlag;
	}
	public void setTimeFlag(String timeFlag) {
		this.timeFlag = timeFlag;
	}
	public void notNull(){
		notNull(systemId,chcNo,sn,beginTime,endTime,price,timeFlag);
	}
	private void notNull(String ... str){
		for(String s:str){
			if(s == null)
				s = "";
		}
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getSerPrice() {
		return serPrice;
	}

	public void setSerPrice(String serPrice) {
		this.serPrice = serPrice;
	}
}