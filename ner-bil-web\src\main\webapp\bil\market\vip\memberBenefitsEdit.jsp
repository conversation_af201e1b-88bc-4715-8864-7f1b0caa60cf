<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="" >
	<style>
		.amber .label{
			text-align: left;
		}
		.vip-limit-text {
			white-space: nowrap;
		}
		.vip-limit-amount {
			margin-left: 20px;
		}
	</style>
</ls:head>
<ner:head/>
<ls:body>
	<ls:form name="infoForm" id="infoForm" enctype="multipart/form-data" >
		 <ls:title text="会员权益配置" expand="true">
		<table class="tab_search">
			<tr>
				<td width="20%">
					<ls:text name="vipId" property="vipId" type="hidden"/>
					<ls:text name="vipType" property="vipType" type="hidden"/>
				</td>
			</tr>
			<tr>
				<td width="10%">
					<ls:label text="积分兑换" ref="isExchange"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isExchange" property="isExchange" onchanged="isPointExchange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td width="10%"></td>
				<td width="20%"></td>
				<td width="10%"></td>
				<td width="20%"></td>
			</tr>
			<tr id="isExchangeExplain">
				<td width="10%">
					<ls:label text="积分兑换说明" ref="isExchangeExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isExchangeExplain" property="isExchangeExplain"/>
				</td>
			</tr>

			<tr>
				<td width="10%">
					<ls:label text="加倍积分" ref="isDouble"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isDouble" property="isDouble" onchanged="isDoubleChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td id="isDouble1"  width="100%" style="display: flex;justify-content: space-between;">
					<ls:label text=""/>
					<ls:text name="multiple" property="multiple"/>
					<ls:label text="倍"/>
				</td>
			</tr>
			<tr id="isDoubleExplain">
				<td width="10%">
					<ls:label text="加倍积分说明" ref="isDoubleExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isDoubleExplain" property="isDoubleExplain"/>
				</td>
			</tr>
			<tr>
				<td width="10%">
					<ls:label text="专享优惠" ref="isExclusiveOffer"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isExclusiveOffer" property="isExclusiveOffer" onchanged="isExclusiveOfferChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td width="80%">
					<ls:text name="exclusiveOffer" property="exclusiveOffer" type="hidden"></ls:text>
					<table id="dataTable1" align="right" class="tab_search">
						<tr>
							<td>
								<ls:grid url="" name="couponGrid1" primaryKey="couponId" height="100px;" width="99%">
                                    <ls:gridToolbar name="operGridToolBar">
                                        <ls:gridToolbarItem name="doAdd1" text="新增" imageKey="add" onclick="doAdd1"></ls:gridToolbarItem>
                                        <ls:gridToolbarItem name="doDel1" text="删除" imageKey="delete" onclick="doDel1"></ls:gridToolbarItem>
                                    </ls:gridToolbar>
								    <ls:column caption="优惠券id" name="couponId" hidden="true" />
								    <ls:column caption="优惠券数量" name="couponNum"/>
								    <ls:column caption="优惠券" name="couponName">
                                        <ls:selectEditor property="couponList" name="couponSelectEditor1" displayMember="name" valueMember="value" required="true"/>
                                    </ls:column>
								</ls:grid>
							</td>
						</tr>
					</table>
				</td>
			</tr>

			<tr>
				<td width="10%">
					<ls:label text="专属客服" ref="isExclusiveCustomer"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isExclusiveCustomer" property="isExclusiveCustomer" onchanged="isExclusiveCustomerChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td width="10%"></td>
				<td width="20%"></td>
				<td width="10%"></td>
				<td width="20%"></td>
			</tr>
			<tr id="isExclusiveCustomerExplain">
				<td width="10%">
					<ls:label text="专属客服说明" ref="isExclusiveCustomerExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isExclusiveCustomerExplain" property="isExclusiveCustomerExplain"/>
				</td>
			</tr>

			<tr>
				<td width="10%">
					<ls:label text="升级礼包" ref="isUpgradePackage"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isUpgradePackage" property="isUpgradePackage" onchanged="isUpgradePackageChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td id="isUpgradePackage1" width="100%" style="display: flex;justify-content: space-between;">
					<ls:label text="赠送"/>
					<ls:text name="upgradePackage" property="upgradePackage"/>
					<ls:label text="积分"/>
				</td>
			</tr>
			<tr id="isUpgradePackageExplain">
				<td width="10%">
					<ls:label text="升级礼包说明" ref="isUpgradePackageExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isUpgradePackageExplain" property="isUpgradePackageExplain"/>
				</td>
			</tr>

			<tr id="row1">
				<td width="10%">
					<ls:label text="会员场站" ref="isMemberStation"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isMemberStation" property="isMemberStation" onchanged="isMemberStationChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td id="isMemberStation1" width="100%" style="display: flex;justify-content: space-between;">
					<ls:label text="服务费享受" />
					<ls:text name="memberStation" property="memberStation"/>
					<ls:label text="折"/>
				</td>
			</tr>
			<tr id="row4">
				<td width="10%">
					<ls:label text="优惠限制" ref="discountLimit"/>
				</td>
				<td width="10%">
					<ls:checklist type="radio" name="discountLimit" property="discountLimit" onchanged="discountLimitChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td id="vipLimitNum" width="80%" style="display: flex; justify-content: space-between; white-space: nowrap;">
					<span class="vip-limit-text">会员场站月限定次数<ls:text name="limitNum" property="limitNum" style="width: 50px; margin-right: 4px;"/></span>次
					<span class="vip-limit-text vip-limit-amount">会员场站单次充电最大额度<ls:text name="limitAmt" property="limitAmt" style="width: 50px; margin-right: 4px;"/></span>元
				</td>
			</tr>
			<tr id="isMemberStationExplain">
				<td width="10%">
					<ls:label text="会员场站说明" ref="isMemberStationExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isMemberStationExplain" property="isMemberStationExplain"/>
				</td>
			</tr>

			<tr id="row2">
				<td width="10%">
					<ls:label text="开卡礼" ref="isWelcomeGift"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isWelcomeGift" property="isWelcomeGift" onchanged="isWelcomeGiftChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
                <td width="80%">
					<ls:text name="welcomeGift" property="welcomeGift" type="hidden"/>
					<table id="dataTable2" align="right" class="tab_search">
                        <tr>
                            <td>
                                <ls:grid url="" name="couponGrid2" primaryKey="couponId" height="100px;" width="99%">
                                    <ls:gridToolbar name="operGridToolBar">
                                        <ls:gridToolbarItem name="doAdd2" text="新增" imageKey="add" onclick="doAdd2"></ls:gridToolbarItem>
                                        <ls:gridToolbarItem name="doDel2" text="删除" imageKey="delete" onclick="doDel2"></ls:gridToolbarItem>
                                    </ls:gridToolbar>
                                    <ls:column caption="优惠券id" name="couponId" hidden="true"/>
                                    <ls:column caption="优惠券数量" name="couponNum"/>
                                    <ls:column caption="优惠券" name="couponName">
										<ls:selectEditor property="couponList" name="couponSelectEditor2" displayMember="name" valueMember="value" required="true"/>
                                    </ls:column>
                                </ls:grid>
                            </td>
                        </tr>
                    </table>
                </td>
			</tr>

			<tr id="row3">
				<td width="10%">
					<ls:label text="会员日折上折" ref="isMemberDayAdditionalDiscount"/>
				</td>
				<td width="20%">
					<ls:checklist type="radio" name="isMemberDayAdditionalDiscount" property="isMemberDayAdditionalDiscount" onchanged="isMemberDayAdditionalDiscountChange">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>
				</td>
				<td id="memberDay1" width="100%" style="display: flex;justify-content: space-between;">
					<ls:label text="会员日 每月"/>
					<ls:text name="memberDay" property="memberDay"/>
					<ls:label text="号"/>
				</td>
				<td id="isMemberDayAdditionalDiscount1" width="100%" style="display: flex;justify-content: space-between;">
					<ls:label text="服务费享受"/>
					<ls:text name="memberDayAdditionalDiscount" property="memberDayAdditionalDiscount"/>
					<ls:label text="折"/>
				</td>
			</tr>
			<tr id="isMemberDayAdditionalDiscountExplain">
				<td width="10%">
					<ls:label text="会员日折上折说明" ref="isMemberDayAdditionalDiscountExplain"/>
				</td>
				<td></td>
				<td width="90%">
					<ls:text name="isMemberDayAdditionalDiscountExplain" property="isMemberDayAdditionalDiscountExplain"/>
				</td>
			</tr>

			<tr>
				<td width="10%">
					<ls:label text="省钱攻略充电度数配置" ref="chargeDegreeConfig"/>
				</td>
				<td id="chargeDegreeConfig"  width="100%" style="display: flex;justify-content: space-between;">
					<ls:text name="chargeDegree1" property="chargeDegree1"/>
					<ls:text name="chargeDegree2" property="chargeDegree2"/>
					<ls:text name="chargeDegree3" property="chargeDegree3"/>
					<ls:text name="chargeDegree4" property="chargeDegree4"/>
					<ls:text name="chargeDegree5" property="chargeDegree5"/>
				</td>
				<td width="10%">
					<ls:label text="度(建议填写为30的倍数)"/>
				</td>
			</tr>

			<tr id="monthDegreeConfig">
				<td width="10%">
					<ls:label text="最高月省度数配置" ref="monthDegreeConfig"/>
				</td>
				<td width="10%"></td>
				<td  width="60%" style="display: flex;justify-content: space-between;">
					<ls:text name="monthDegreeConfig" property="monthDegreeConfig"/>
					<ls:label text="度(建议填写为30的倍数)"/>
				</td>
			</tr>

		</table>
	</ls:title>

	<table class="tab_search">
		<tr >
			<div class="pull-right">
				<ls:button text="保存" onclick="doSave" />
				<ls:button text="取消" onclick="doClear" />
			</div>
		</tr>
	</table>

	</ls:form>

	<ls:script>

		window.onload = function(){
		isPointExchange();
		isExclusiveCustomerChange();
		isDoubleChange();
		isExclusiveOfferChange();
		isUpgradePackageChange();
		isMemberStationChange();
		isWelcomeGiftChange();
		isMemberDayAdditionalDiscountChange();
		discountLimitChange();

		var list1 = JSON.parse(exclusiveOffer.getValue());
		for(var i=0;i< list1.length;i++){
		if(list1[i]){
		couponGrid1.appendItem(list1[i]);
		}
		}
		var list2 = JSON.parse(welcomeGift.getValue());
		for(var i=0;i< list1.length;i++){
		if(list2[i]){
		couponGrid2.appendItem(list2[i]);
		}
		}
		if(vipType.getValue()=="01"){
		isMemberStation.setValue("0");
		discountLimit.setValue("0");
		isWelcomeGift.setValue("0");
		isMemberDayAdditionalDiscount.setValue("0");
		$('#monthDegreeConfig').hide();
		$('#row1').hide();
		$('#row2').hide();
		$('#row3').hide();
		}
		}

		function doSave(){

		var gridData1 = couponGrid1.getItems();
		exclusiveOffer.setValue(JSON.stringify(gridData1));

		var gridData2 = couponGrid2.getItems();;
		welcomeGift.setValue(JSON.stringify(gridData2));

		infoForm.submit("~/member/benefits/save",function(e){
		if("Y" ==e.items[0]){
		LS.message("info","保存成功");
		LS.parent().query();
		LS.window.close();
		}else {
		if("N" ==e.items[0]){
		LS.message("error", "保存失败");
		}else{
		LS.message("info", e.items[0]);
		}
		}
		});
		}

		function doClear(){
			LS.window.close();
		}
		function isExclusiveCustomerChange(){
		if (isExclusiveCustomer.getValue() == "1") {
		$('#isExclusiveCustomerExplain').show();
		} else {
		$('#isExclusiveCustomerExplain').hide();
		}
		}
		function isPointExchange(){
		if (isExchange.getValue() == "1") {
		$('#isExchangeExplain').show();
		} else {
		$('#isExchangeExplain').hide();
		}
		}

		function isDoubleChange(){
		if (isDouble.getValue() == "1") {
		$('#isDouble1').show();
		$('#isDoubleExplain').show();
		} else {
		$('#isDoubleExplain').hide();
		$('#isDouble1').hide();
		}
		}
        function isExclusiveOfferChange(){
        if (isExclusiveOffer.getValue() == "1") {
        $('#dataTable1').show();
		var div = document.getElementById("pager_id_couponGrid1");
		if (div) {
		div.style.display = 'none';
		}
        } else {
        $('#dataTable1').hide();
        }
        }
		function isUpgradePackageChange(){
		if (isUpgradePackage.getValue() == "1") {
		$('#isUpgradePackage1').show();
		$('#isUpgradePackageExplain').show();
		} else {
		$('#isUpgradePackage1').hide();
		$('#isUpgradePackageExplain').hide();
		}
		}
		function isMemberStationChange(){
		if (isMemberStation.getValue() == "1") {
		$('#isMemberStation1').show();
		$('#isMemberStationExplain').show();
		} else {
		$('#isMemberStationExplain').hide();
		$('#isMemberStation1').hide();
		}
		}
		function discountLimitChange(){
		console.log(discountLimit.getValue())
		if (discountLimit.getValue() == "1") {
		$('#vipLimitNum').show();
		} else {
		$('#vipLimitNum').hide();
		}
		}

		function isWelcomeGiftChange(){
		if (isWelcomeGift.getValue() == "1") {
		$('#dataTable2').show();
		var div = document.getElementById("pager_id_couponGrid2");
		if (div) {
		div.style.display = 'none';
		}
		} else {
		$('#dataTable2').hide();
		}
		}
		function isMemberDayAdditionalDiscountChange(){
		if (isMemberDayAdditionalDiscount.getValue() == "1") {
		$('#isMemberDayAdditionalDiscount1').show();
		$('#memberDay1').show();
		$('#isMemberDayAdditionalDiscountExplain').show();
		} else {
		$('#isMemberDayAdditionalDiscount1').hide();
		$('#memberDay1').hide();
		$('#isMemberDayAdditionalDiscountExplain').hide();
		}
		}

		//新增
		function doAdd1(){
		couponGrid1.addRow();
		};

        //删除
        function doDel1(){
        var item = couponGrid1.getSelectedItem();
        if(LS.isEmpty(item)){
        LS.message("info","请选择要删除的记录");
        return;
        }
        couponGrid1.removeItem(item);
        }

        //新增
        function doAdd2(){
        couponGrid2.addRow();
        };

        //删除
        function doDel2(){
        var item = couponGrid2.getSelectedItem();
        if(LS.isEmpty(item)){
        LS.message("info","请选择要删除的记录");
        return;
        }
        couponGrid2.removeItem(item);
        }


	</ls:script>
</ls:body>
</html>
