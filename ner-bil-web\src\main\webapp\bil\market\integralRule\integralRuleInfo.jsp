<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="积分规则详情" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script src="<%=request.getContextPath()%>/bil/comm/js/DateUtils.js" type="text/javascript"></script>

<ls:body>
	<ls:form name="ruleForm" id="ruleForm" >
		<ls:text name="ruleId" property="ruleId" visible="false"></ls:text>
		<ls:text name="ruleStatus" property="ruleStatus" visible="false"></ls:text>
		<ls:text name="eventType" property="eventType" visible="false"></ls:text>
		<ls:text name="userTypeListStr" property="userTypeListStr" visible="false"></ls:text>
		<ls:text name="handlyType" property="handlyType" visible="false"></ls:text>
		
		<ls:text name="maxTimes" property="maxTimes" visible="false"></ls:text>
		<ls:layout-use>
			<ls:layout-put into="container">
				<table align="center" class="tab_search">
					<colgroup>
						<col width="15%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="15%" />
					</colgroup>
					<tr>
						<td/>
						<td>
							<ls:label text="积分规则名称" ref="ruleName"/>
						</td>
						<td colspan="2">
							<ls:text name="ruleName" property="ruleName" required="true" readOnly="true"></ls:text>
						</td>
						<td>
							<ls:label text="触发事件" ref="eventType" />
						</td>
						<td colspan="2">
							<ls:select name="eventType" required="true" property="eventType" noClear="true" readOnly="true">
								<ls:options property="eventTypeList" scope="request" text="codeName" value="codeValue" />
							</ls:select>
						</td>
						<td/>
					</tr>
					<tr id="eventType01">
						<td/>
						<td>
							<ls:label text="发放数额" ref="money1"/>
						</td>
						<td colspan="2">
							<ls:text name="money1" property="money" required="true" hint="积分"  readOnly="true"></ls:text>
						</td>
						<td ><ls:label text="积分" textAlign="left"/></td>
						<td colspan="4">
					</tr>
					<tr style="display:none"  id="eventType02">
						<td/>
						<td>
							<ls:label text="区分身份" ref="isDiscriminateUser"/>
						</td>
						<td colspan="3">
							<ls:checklist type="radio" name="isDiscriminateUser" property="isDiscriminateUser"  readOnly="true" required="true">
								<ls:checkitem value="0" text="否"></ls:checkitem>
								<ls:checkitem value="1" text="是"></ls:checkitem>
							</ls:checklist>
						</td>
						<td colspan="4"/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser02">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName" textAlign="center"/>
									</td>
									<td><ls:label text="发放数额（积分）" ref="money0201" textAlign="center"  /></td>
									<ls:text name="money" property="money" visible="false" ></ls:text>
									<td><ls:label text="上限次数（次）" ref="maxTimes0201" textAlign="center"  /></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName" property="userTypeName" value="普通身份" style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType" property="userType" visible="false" value="0"  readOnly="true"></ls:text>
									</td>
									<td>
										<ls:text name="money0201" property="money" required="true" style="text_align:center"  readOnly="true"></ls:text>
										<ls:text name="money" property="money" visible="false"  readOnly="true"></ls:text>
									</td>
									<td>
										<ls:text name="maxTimes0201" property="maxTimes" required="true" style="text_align:center"  readOnly="true"></ls:text>
									</td>
								</tr>
							</table>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser04">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName1" textAlign="center"/>
									</td>
									<td><ls:label text="发放  1  积分需充电" ref="chargingPq1" textAlign="center"  /></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName1" property="userTypeName" value="普通身份" style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType1" property="userType" visible="false" value="0"  readOnly="true"></ls:text>
									</td>
									<td>
										<ls:text name="chargingPq1" property="chargingPq" style="text_align:center" required="true"  readOnly="true"></ls:text>
									</td>
								</tr>
							</table>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser05">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName2" textAlign="center"/>
									</td>
									<td><ls:label text="最低赠送积分（积分）" ref="money0501" textAlign="center"/></td>
									<td><ls:label text="最高赠送积分（积分）" ref="money0502" textAlign="center"/></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName2" property="userTypeName" value="普通身份"  style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType2" property="userType" visible="false" value="0"  readOnly="true"></ls:text>
									</td>
									<td>
										<ls:text name="money0501" property="minMoney" required="true" style="text_align:center"  readOnly="true"></ls:text>
									</td>
									<td>
										<ls:text name="money0502" property="maxMoney" required="true" style="text_align:center"  readOnly="true"></ls:text>
									</td>
								</tr>
							</table>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="eventType03">
						<td/>
						<td>
							<ls:label text="发放数额" ref="money2"/>
						</td>
						<td colspan="2">
							<ls:text name="money2" property="money"  readOnly="true" hint="积分" ></ls:text>
							<ls:text name="money" property="money" visible="false"  readOnly="true"></ls:text>
						</td>
						<td ><ls:label text="积分" textAlign="left"/></td>
						<td>
							<ls:label text="上限次数" ref="maxTimes2"/>
						</td>
						<td colspan="2">
							<ls:text name="maxTimes2" property="maxTimes"  readOnly="true" hint="次" ></ls:text>
						</td>
						<td ><ls:label text="次" textAlign="left"/></td>
					</tr>
					<tr style="display:none"  id="eventType04">
						<td/>
						<td  colspan="1">
							<ls:label text="发放  1  积分需充电" ref="chargingPq"/>
						</td>
						<td colspan="2">
							<ls:text name="chargingPq" property="chargingPq"  readOnly="true" ></ls:text>
						</td>
						<td ><ls:label text="kWh" textAlign="left"/></td>
						<td colspan="3"/>
					</tr>
					<tr style="display:none"  id="eventType05">

							<td/>
							<td>
								<ls:label text="最低赠送积分" ref="money501"/>
							</td>
							<td colspan="2">
								<ls:text name="money501" property="minMoney" required="true"  readOnly="true"></ls:text>
							</td>
							<td><ls:label text="积分" textAlign="left"/></td>

							<td>
								<ls:label text="最高赠送积分" ref="money502"/>
							</td>
							<td colspan="2">
								<ls:text name="money502" property="maxMoney" required="true"  readOnly="true"></ls:text>
							</td>
							<td><ls:label text="积分" textAlign="left"/></td>
	                       <td/>
					</tr>
				</table>
			</ls:layout-put>

		</ls:layout-use>
	</ls:form>
	<ls:script>
	
		window.onload = function(){
			changeEventType();
		}
	
		function chargeUser(){
			changeEventType();
		}
		
		function changeEventType(){
			if(eventType.getValue() == "01"){
				$("#eventType01").show();
				$("#eventType02").hide();
				$("#eventType03").hide();
				$("#eventType04").hide();
				$("#eventType05").hide();
			}else if(eventType.getValue() == "02"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").show();
				$("#eventType04").hide();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").show();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
					$("#eventType03").hide();
				}
			}else if(eventType.getValue() == "03"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").show();
				$("#eventType04").hide();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").show();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
					$("#eventType03").hide();
				}
			}else if(eventType.getValue() == "04"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").hide();
				$("#eventType04").show();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").show();
					$("#isDiscriminateUser05").hide();
					$("#eventType04").hide();
				}
			}else if(eventType.getValue() == "05"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").hide();
				$("#eventType04").hide();
				$("#eventType05").show();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").show();
					$("#eventType05").hide();
				}
			}
		}
		
		
		function cancel(){
			LS.window.close();
		}
		
		
	</ls:script>
</ls:body>
</html>