/**
 *
 * @(#) tariffPlan.java
 * @Package com.ls.ner.billing.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.bo;

import java.io.Serializable;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * 类描述：资费方案
 * 
 * @author: lipf
 * @version $Id: TariffPlanBo.java,v 1.1 2016/02/26 02:34:00 34544 Exp $
 * 
 *          History: 2016年2月17日 下午4:20:00 lipf Created.
 * 
 */
public class TariffPlanBo  extends QueryCondition implements Serializable{
	private static final long serialVersionUID = 4375179422371149734L;
	private String systemId;//资费方案标识
	private String planName;//方案名称
	private String planNo;//方案编号
	private String operName;//创建人员
	private String createTime;//创建时间
	private String peBe;//业务大类
	private String subBe;//业务小类
	private String planRemark;//资费方案说明
	private String dataOperTime;//数据操作时间
	private String dataOperType;//数据操作类型
	private String mainNo;//主收费项目编号
	private String chargeMode;//计费方式
	private String chargeType;//计费模式
	private String chargeUnit;//计费单位
	private String chargeNum;//计费数
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getPlanName() {
		return planName;
	}
	public void setPlanName(String planName) {
		this.planName = planName;
	}
	public String getPlanNo() {
		return planNo;
	}
	public void setPlanNo(String planNo) {
		this.planNo = planNo;
	}
	public String getOperName() {
		return operName;
	}
	public void setOperName(String operName) {
		this.operName = operName;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getPeBe() {
		return peBe;
	}
	public void setPeBe(String peBe) {
		this.peBe = peBe;
	}
	public String getSubBe() {
		return subBe;
	}
	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}
	public String getPlanRemark() {
		return planRemark;
	}
	public void setPlanRemark(String planRemark) {
		this.planRemark = planRemark;
	}
	public String getDataOperTime() {
		return dataOperTime;
	}
	public void setDataOperTime(String dataOperTime) {
		this.dataOperTime = dataOperTime;
	}
	public String getDataOperType() {
		return dataOperType;
	}
	public void setDataOperType(String dataOperType) {
		this.dataOperType = dataOperType;
	}
	public String getMainNo() {
		return mainNo;
	}
	public void setMainNo(String mainNo) {
		this.mainNo = mainNo;
	}
	public String getChargeMode() {
		return chargeMode;
	}
	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}
	public String getChargeType() {
		return chargeType;
	}
	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}
	public String getChargeUnit() {
		return chargeUnit;
	}
	public void setChargeUnit(String chargeUnit) {
		this.chargeUnit = chargeUnit;
	}
	public String getChargeNum() {
		return chargeNum;
	}
	public void setChargeNum(String chargeNum) {
		this.chargeNum = chargeNum;
	}
}
