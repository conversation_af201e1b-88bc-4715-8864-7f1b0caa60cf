package com.ls.ner.billing.config.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.ls.ner.base.constants.PublicConstants;
import org.apache.commons.beanutils.BeanUtils;
import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.util.json.JsonMapper;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.api.rent.model.SubChargeItem;
import com.ls.ner.billing.api.rent.service.IRentbillService;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.config.bo.BillingConfigForm;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.service.IBillingConfigService;
import com.ls.ner.billing.models.bo.ModelsBillBo;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.ui.taglib.utils.TagUtil;
import com.pt.poseidon.webcommon.rest.annotation.IdRequestBody;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.IDRequestObject;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 车型定价controller
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/billing/configs")
public class BillingConfigController extends QueryController<BillingConfigQueryCondition> {
	private static final Logger LOGGER = LoggerFactory.getLogger(BillingConfigController.class);
	private static final String VIEW_BILLING_CONFIG_GRID = "/bil/config/billingConfigGrid2";
	@Deprecated
	private static final String VIEW_BILLING_CONFIG_FORM = "/bil/config/billingConfigForm";
	@Deprecated
	private static final String BILLING_CONFIG_SECOND = "/bil/config/billingConfigSecond";
	private static final String BILLING_CONFIG_COPY_FORM = "/bil/config/billingConfigCopy";
	private static final String BILLING_CONFIG_ADD_FORM = "/bil/config/billingConfigAdd";
	private static final String BILLING_CONFIG_SHOWEDIT_FORM = "/bil/config/billingConfigEdit2";
	private static final String BILLING_CONFIG_PAGE = "/bil/config/billingConfigPage2";
	
	private static final String BILLING_CONFIG_FORM = "billingConfigForm";
	private static final String BILLING_CONFIG_FORM_JSON = "billingConfigFormJson";
	@Autowired
	private ControllerSupport controllerSupport;
	
	@ServiceAutowired("billingConfigService")
	private IBillingConfigService billingConfigService;
	
	@ServiceAutowired(serviceTypes = {ServiceType.APPLICATION},value="rentbillService")
	private IRentbillService rentbillRpcService;
	
	@ServiceAutowired
	Authentication authentication;
	
	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	@ServiceAutowired(serviceTypes=ServiceType.RPC, value="accountService")
	private IAccountService accountService;
	
	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="archivesRpcService")
	private IArchivesRpcService archivesRpcService;
	
	@Override
	protected BillingConfigQueryCondition initCondition() {
	
		return new BillingConfigQueryCondition();
	}
	
	/**
	 * 车型定价表格页面初始化
	 * @param m
	 * @return
	 */
	@RequestMapping(value="/init",method = RequestMethod.GET)
	public String init(Model m) {
		controllerSupport.addStCodes(m, SubBe.CODE_TYPE,BillingConstants.JUDGE_FLAG);
		BillingConfigQueryCondition bo = new BillingConfigQueryCondition();
		AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getRootOrgs().get(0);
		if(orgBo != null){
			bo.setOrgCode(orgBo.getOrgCode());
			bo.setOrgCodeName(orgBo.getOrgShortName());
			m.addAttribute("orgCode", orgBo.getOrgCode());
		}else{
			m.addAttribute("orgCode", "root");
		}
		m.addAttribute("queryForm", bo);
		//资产域，跨域访问
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return VIEW_BILLING_CONFIG_GRID;
	}
	
	/**
	 * 车型定价详情页面
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/show/{billingNo}", method = RequestMethod.GET)
	public String show(Model m, @PathVariable("billingNo")
	String billingNo) {
		setStCodes(m);
		m.addAttribute("operFlag","show");
		m.addAttribute("readOnly","true");
		setFormBean(m, billingNo);
		setBillingNo(m, billingNo);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return BILLING_CONFIG_SHOWEDIT_FORM;
	}

	protected void setFormBean(Model m, String billingNo) {
		BillingConfigForm form = billingConfigService.getFormByBillingNo(billingNo);
		m.addAttribute(BILLING_CONFIG_FORM, form);
		m.addAttribute(BILLING_CONFIG_FORM_JSON,JsonMapper.nonEmptyMapper().toJson(form));
	}

	/**
	 * 车型定价表格数据 查询
	 * @param params
	 * @return
	 */
	@RequestMapping(value="/getBillingConfigList")
	public @ItemResponseBody
	QueryResultObject getBillingConfigList(@QueryRequestParam("params")
	RequestCondition params) {
		BillingConfigQueryCondition q = this.rCondition2QCondition(params);
		q.setPageBegin(q.getPageBegin()-1);
		List<BillingConfigBo> list = billingConfigService.pagi(q);
		return RestUtils.wrappQueryResult(list, q.getRows());
	}

	/**
	 * 车型定价新增选择资费模版动态加载表单页面 @RequestMapping(value = "/new/{planNo:[\\w]+}", method = RequestMethod.GET) 此种权限项配置不支持
	 * @param m
	 * @return
	 */
	@RequestMapping(value = "/new/{planNo}", method = RequestMethod.GET)
	public String add(Model m,@PathVariable("planNo")
	String planNo) {
		setStCodes(m);
		m.addAttribute("operFlag","add");
		BillingConfigForm form = billingConfigService.initFormByPlanNo(planNo);
		m.addAttribute("billingConfigPageForm", form);
		m.addAttribute(BILLING_CONFIG_FORM_JSON,JsonMapper.nonEmptyMapper().toJson(form));
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return BILLING_CONFIG_PAGE;
	}

	protected void setStCodes(Model m) {

		//普通的标准代码用
		controllerSupport.addStCodes(m, SubBe.CODE_TYPE,ChargeWay.CODE_TYPE,ChargeMode.CODE_TYPE,PriceUnit.CODE_TYPE,SubChargeItem.Limit.CODE_TYPE);
	}
	/**
	 * 车型定价新增保存（含调整）
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/post", method = RequestMethod.POST,consumes={MediaType.APPLICATION_JSON_VALUE})
	public @ResponseBody WrappedResult save(Model m, @RequestBody BillingConfigForm form) {
		String billingNo = null;
		if("1".equals(form.getIsAdjusted())){//调价保存
			LOGGER.info("调整定价保存开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(form));
			billingConfigService.createAdjust(form);
		} else {//正常保存
			LOGGER.info("正常定价保存开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(form));
			billingNo = billingConfigService.create(form);
		}
		return WrappedResult.successWrapedResult(billingNo);
	}

	/**
	 * 修改 表单页面（暂不用）
	 * @param m
	 * @param planNo
	 * @return
	 */
	@Deprecated
	@RequestMapping(value = "/edit/{billingNo}", method = RequestMethod.GET)
	public String edit(Model m, @PathVariable("billingNo")
	String billingNo) {
		setStCodes(m);
		m.addAttribute("operFlag","edit");
		setBillingNo(m, billingNo);
		setFormBean(m,billingNo);
		return VIEW_BILLING_CONFIG_FORM;
	}

	protected void setBillingNo(Model m, String billingNo) {
		m.addAttribute("billingNo", ESAPI.encoder().encodeForJavaScript(billingNo));
	}
	/**
	 * 车型定价更新保存
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/post/{billingNo}", method = RequestMethod.POST)
	public @ResponseBody WrappedResult update(Model m, @PathVariable("billingNo")
	String billingNo, @RequestBody BillingConfigForm form) {
		
		form.setBillingNo(billingNo);
		billingConfigService.update(form);
		return WrappedResult.successWrapedResult(billingNo);
	}
	/**
	 * 
	 * 方法说明：租赁定价新增页面初始化
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月9日 下午3:51:23
	 * History:  2016年4月9日 下午3:51:23   lipf   Created.
	 *
	 * @param m
	 * @param planNo
	 * @return
	 *
	 */
	@RequestMapping(value = "/initAdd", method = RequestMethod.GET)
	public String initAdd(Model m) {
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return BILLING_CONFIG_ADD_FORM;
	}
	/**
	 * 
	 * 方法说明：租赁定价编辑页面初始化
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 上午9:43:04
	 * History:  2016年4月12日 上午9:43:04   lipf   Created.
	 *
	 * @param m
	 * @param billingNo
	 * @return
	 *
	 */
	@RequestMapping(value = "/initEdit/{billingNo}", method = RequestMethod.GET)
	public String initEdit(Model m, @PathVariable("billingNo")
	String billingNo) {
		setStCodes(m);
		m.addAttribute("operFlag","edit");
		m.addAttribute("readOnly","false");
		setBillingNo(m, billingNo);
		setFormBean(m,billingNo);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return BILLING_CONFIG_SHOWEDIT_FORM;
	}

	/**
	 * 
	 * 方法说明：租赁定价第二步页面初始化（进入此方法之前页面必须已经选择autoModelNo和planNo）  暂不用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月9日 下午4:21:55
	 * History:  2016年4月9日 下午4:21:55   lipf   Created.
	 *
	 * @param m
	 * @param planNo
	 * @return
	 * @throws UnsupportedEncodingException 
	 *
	 */
	@Deprecated
	@RequestMapping(value = "/second", method = RequestMethod.GET)
	public String second(Model m, @RequestParam("planNo")
	String planNo, @RequestParam("autoModelNo")
	String autoModelNo, @RequestParam("autoModelName")
	String autoModelName) throws UnsupportedEncodingException {
		setStCodes(m);
		m.addAttribute("operFlag", "add");
		BillingConfigForm form = billingConfigService.initFormByPlanNo(planNo);
		form.setAutoModelNo(autoModelNo);
		form.setAutoModelName(URLDecoder.decode(autoModelName, "UTF-8"));
		m.addAttribute(BILLING_CONFIG_FORM, form);
		m.addAttribute(BILLING_CONFIG_FORM_JSON, JsonMapper.nonEmptyMapper().toJson(form));
		return BILLING_CONFIG_SECOND;
	}
	/**
	 * 
	 * 方法说明：租赁定价 删除
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午12:56:19
	 * History:  2016年4月12日 下午12:56:19   lipf   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/delete")
	public @ItemResponseBody
	QueryResultObject delete(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		billingConfigService.deleteBillings(ids);
		return RestUtils.wrappQueryResult("Y");
	}
	/**
	 * 
	 * 方法说明：定价复制选择生效日期页面
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午3:43:24
	 * History:  2016年4月12日 下午3:43:24   lipf   Created.
	 *
	 * @param m
	 * @param autoModelNos
	 * @return
	 *
	 */
	@RequestMapping(value = "/initCopy")
	public String initCopy(Model m, @RequestParam("billingNo")
	String billingNo) {
		BillingConfigForm form = new BillingConfigForm();
		form.setBillingNo(billingNo);
		m.addAttribute("configCopyForm", form);
		return BILLING_CONFIG_COPY_FORM;
	}
	/**
	 * 
	 * 方法说明：定价配置复制保存
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午1:08:54
	 * History:  2016年4月12日 下午1:08:54   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	@RequestMapping(value="/copyConfig")
	public @ItemResponseBody
	QueryResultObject copyConfig(@RequestBody BillingConfigBo bo) {
		String flag = billingConfigService.copyConfig(bo);
		return RestUtils.wrappQueryResult(flag);
	}
	/**
	 * 
	 * 方法说明：租赁定价树根节点数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月30日 上午9:48:12
	 * History:  2016年5月30日 上午9:48:12   lipf   Created.
	 *
	 * @param request
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping(value = "/getBillingTree", method = RequestMethod.GET)
	@ResponseBody
	public Object getBillingTree(HttpServletRequest request) throws Exception {
		List<OrgBo> subOrgList = new ArrayList<OrgBo>();
		AccountBo accountBo = authentication.getCurrentAccount();
//		if (this.accountService.isAdmin(accountBo.getAccountName())) {
			subOrgList = this.orgService.getRootOrgs();
//		} else {
//			OrgBo orgBo = orgService.getOrgByAccountName(accountBo.getAccountName());
//			if(orgBo != null){
//				subOrgList.add(this.orgService.getOrgByNo(orgBo.getOrgCode()));
//			} else {
//				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
//			}
//		}
		Map<String,Object> returnMap = new HashMap<String, Object>();
		returnMap.put("nodes", tranOrg2tree(subOrgList, request));
		return WrappedResult.successWrapedResult(returnMap);
	}
	/**
	 * 
	 * 方法说明：租赁定价树子节点数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月30日 上午9:49:06
	 * History:  2016年5月30日 上午9:49:06   lipf   Created.
	 *
	 * @param request
	 * @param orgCode
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping(value = "/getBillingTree/{orgCode}", method = RequestMethod.GET)
	@ResponseBody
	public Object getOrgSubTree(HttpServletRequest request, @PathVariable("orgCode")
	String orgCode) throws Exception {
		List<OrgBo> subOrgList = null;
		Map<String,Object> returnMap = new HashMap<String, Object>();
		List<Map<String,Object>> subTreeList = new ArrayList<Map<String,Object>>();
		BillingConfigQueryCondition condition = new BillingConfigQueryCondition();
		Map<String,Object> subNode = null ;
		if(orgCode.split("_").length>1){
			condition.setOrgCode(orgCode.split("_")[0]);
			condition.setSubBe(orgCode.split("_")[1]);
			List<BillingConfigBo> list= billingConfigService.getAutoBillingList(condition);
			String bundleUrl = TagUtil.getBundleUrl(request);
			if(list!=null&&list.size()>0){
				Map<String, Object> inMap = Maps.newHashMap();
				List<Map<String,Object>> listMap = Lists.newArrayList();
				Map<String, Object> temp = null;
				for (BillingConfigBo billingConfigBo : list) {
					temp = Maps.newHashMap();
					temp.put("modelId", billingConfigBo.getAutoModelNo());
					listMap.add(temp);
				}
				inMap.put("modelList", listMap);
				List<Map<String,Object>> retMap = archivesRpcService.queryModels(inMap);
				Map<String, ModelsBillBo> tempBo = new HashMap<String, ModelsBillBo>();
				if(retMap!=null&&retMap.size()>0){
					for (Map<String, Object> map : retMap) {
						ModelsBillBo bo = new ModelsBillBo();
						BeanUtils.populate(bo, map);
						tempBo.put(map.get("modelId")+"", bo);
					}
				}
				for (BillingConfigBo billingConfigBo : list) {
					if(tempBo.get(billingConfigBo.getAutoModelNo())==null){
						continue;
					}
					billingConfigBo.setAutoModelName(tempBo.get(billingConfigBo.getAutoModelNo()).getModelName());
					subNode = new HashMap<String, Object>();
					subNode.put("id", orgCode.split("_")[0]+"_"+billingConfigBo.getAutoModelNo());
					subNode.put("text", tempBo.get(billingConfigBo.getAutoModelNo()).getModelName());
					subNode.put("hasChildren", false);
					subNode.put("parentCode", orgCode);
					subNode.put("item", billingConfigBo);
//					subNode.put("icon", bundleUrl + "source/image/org.gif");
					subNode.put("itemType", "autoModel");
					subNode.put("orgCode", orgCode.split("_")[0]);
					subNode.put("subBe", orgCode.split("_")[1]);
					subTreeList.add(subNode);
				}
			}
			returnMap.put("nodes",subTreeList);
			return WrappedResult.successWrapedResult(returnMap);
		} else {
			if ("root".equals(orgCode)) {
				subOrgList = this.orgService.getRootOrgs();
			} else {
				subOrgList = this.orgService.getChildOrgs(orgCode);
			}
		}
		
		condition.setOrgCode(orgCode);
		List<CodeBO> rentTypeList = codeService.getAllStandardCodes(SubBe.CODE_TYPE,null);
		for (int i = 0; i < rentTypeList.size(); i++) {
			CodeBO code = rentTypeList.get(i);
			if("02,03,04".indexOf(code.getCodeValue())>-1){
				rentTypeList.remove(code);
				i--;
			}
		}
		List<BillingConfigBo> list= billingConfigService.getAutoBillingList(condition);
		Map<String,Integer> map = new HashMap<String, Integer>();
		if(list!=null&&list.size()>0){
			for (BillingConfigBo billingConfigBo : list) {
				if(map.get(billingConfigBo.getSubBe())==null||map.get(billingConfigBo.getSubBe())==0){
					map.put(billingConfigBo.getSubBe(), 1);
				}else{
					map.put(billingConfigBo.getSubBe(), map.get(billingConfigBo.getSubBe())+1);
				}
			}
		}
		String bundleUrl = TagUtil.getBundleUrl(request);
		if(rentTypeList!=null&&rentTypeList.size()>0){
			for (CodeBO codeBo : rentTypeList) {
				subNode = new HashMap<String, Object>();
				subNode.put("id", orgCode+"_"+codeBo.getCodeValue());
				int num = map.get(codeBo.getCodeValue())==null?0:map.get(codeBo.getCodeValue());
				subNode.put("text", codeBo.getCodeName()+"("+num+")");
				subNode.put("hasChildren", num==0?false:true);
				subNode.put("parentCode", orgCode);
				subNode.put("item", orgCode);
//				subNode.put("icon", bundleUrl + "source/image/org.gif");
				subNode.put("itemType", "subBe");
				subNode.put("orgCode", orgCode);
				subNode.put("subBe", codeBo.getCodeValue());
				subTreeList.add(subNode);
			}
		}
//		subTreeList.addAll(tranOrg2tree(subOrgList, request));
		returnMap.put("nodes",subTreeList);
		return WrappedResult.successWrapedResult(returnMap);
	}
	
	private List<Map<String,Object>> tranOrg2tree(List<OrgBo> subOrgList,HttpServletRequest request){
		List<Map<String,Object>> subTreeList = new ArrayList<Map<String,Object>>();
		for (int i = 0; i < subOrgList.size(); i++) {
			OrgBo orgBo = subOrgList.get(i);
			Map<String,Object> subNode = new HashMap<String, Object>();
			String bundleUrl = TagUtil.getBundleUrl(request);
			subNode.put("id", orgBo.getOrgCode());
			subNode.put("text", orgBo.getOrgShortName());
			subNode.put("hasChildren", true);
			subNode.put("parentCode", orgBo.getOrgParentCode());
			subNode.put("item", orgBo);
//			subNode.put("icon", bundleUrl + "source/image/org.gif");
			subNode.put("itemType", "org");
			subNode.put("orgCode", orgBo.getOrgCode());
			subNode.put("subBe", "");
			subTreeList.add(subNode);
		}
		return subTreeList;
	}
	
	@RequestMapping(value = "/initAdd2", method = RequestMethod.GET)
	public String initAdd2(Model m,@RequestParam("params") String params) throws UnsupportedEncodingException {
		Map<String,String> map = JsonUtil.parseJsonToMap(URLDecoder.decode(params, "UTF-8"));
		BillingConfigForm form = new BillingConfigForm();
		form.setAutoModelNo(map.get("autoModelNo"));
		form.setAutoModelName(URLDecoder.decode(map.get("autoModelName"), "UTF-8"));
		form.setSubBe(map.get("subBe"));
		CodeBO code = codeService.getStandardCode(SubBe.CODE_TYPE, map.get("subBe"), null);
		form.setSubBeName(code==null?"":code.getCodeName());
		form.setOrgCode(map.get("orgCode"));
		form.setOrgCodeName(URLDecoder.decode(map.get("orgCodeName"), "UTF-8"));
		m.addAttribute(BILLING_CONFIG_FORM, form);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/config/billingConfigAdd2";
	}
	
//	@RequestMapping(value = "/order-billing-test", method = RequestMethod.GET)
//	public @ResponseBody WrappedResult update(Model m) {
//		BillingLocateCondition billingLocateCondition = new BillingLocateCondition();
//		String rentType = SubBe.DAY;
//		String rtNo = "4600001";
//		String orgCode = "4600001";
//		String autoModelNo = "12438";
//		
//		billingLocateCondition.setRtNo(rtNo);
//		billingLocateCondition.setOrgCode(orgCode);
//		billingLocateCondition.setAutoModelNo(autoModelNo);
//		OrderBillingResponse obr = rentbillRpcService.getOrderBillingInfo(rentType, billingLocateCondition);
//		return WrappedResult.successWrapedResult(obr);
//	}
//	
//	@RequestMapping(value = "/order-billing-calc-test", method = RequestMethod.GET)
//	public @ResponseBody WrappedResult getOrderBilling(Model m) {
//		BillingLocateCondition billingLocateCondition = new BillingLocateCondition();
//		String rentType = SubBe.HOUR;
//		String rtNo = "4600001";
//		String orgCode = "4600001";
//		String autoModelNo = "12438";
//		
//		RentRun rentRun = new RentRun();
//		rentRun.setAppNo("2016031609210001");
//		DateItem dateItem1 = new DateItem();
//		dateItem1.setRecordDate("20160316");
//		dateItem1.setMinutes(600);
//		DateItem dateItem2 = new DateItem();
//		dateItem2.setRecordDate("20160317");
//		dateItem2.setMinutes(300);
//		List<DateItem> dateItems = Lists.newArrayList(dateItem1,dateItem2);
//		rentRun.setDateItems(dateItems);
//		BigDecimal bigDecimalResult = rentbillRpcService.calculate(rentRun, ChargeTimePoint.ALL);
//		return WrappedResult.successWrapedResult(bigDecimalResult);
//	}
	

}
