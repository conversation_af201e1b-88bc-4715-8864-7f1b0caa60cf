/**
 *
 * @(#) ITariffDao.java
 * @Package com.ls.ner.billing.tariff.dao
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff2.dao;

import java.util.List;

import com.ls.ner.billing.common.bo.TariffPlanBo;



/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: ITariffPlanDao.java,v 1.2 2016/03/10 13:22:39 32495 Exp $
 * 
 *          History: 2016年2月23日 下午5:33:48 lipf Created.
 * 
 */
public interface ITariffPlanDao {


	/**
	 * 分页查询用 计算数据总数
	 * @param bo
	 * @return
	 */
	int countTariff(TariffPlanBo bo);
	/**
	 * 
	 * 方法说明：分页查询资费标准
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:58:22
	 * History:  2016年2月24日 上午9:58:22   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	List<TariffPlanBo> pagiTariff(TariffPlanBo bo);

	public int updateByPlanNo(TariffPlanBo bo);

}
