package com.ls.ner.billing.task.controller;/**
 * Created by mingmingh on 2018/3/7.
 */

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.service.ICouponService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @DESCRIPTION
 * @create 2018-03-07 14:40
 **/
@Controller
@RequestMapping("/task/coupon")
public class CouponTaskController {

    private static final Logger logger = LoggerFactory.getLogger(CouponTaskController.class);

    @ServiceAutowired("couponService")
    private ICouponService couponService;

    @RequestMapping("/excuteCouponTask")
    public @ResponseBody Object excuteCouponTask() {
        logger.info("开始执行优惠券失效更新任务调度");
        try {
            couponService.excuteCouponTask();
            return "0";
        } catch (Exception e) {
            logger.error("执行优惠券失效更新任务调度失败", e);
            return "-1";
        }
    }

}
