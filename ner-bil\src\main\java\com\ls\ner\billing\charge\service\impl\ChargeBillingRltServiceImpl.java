package com.ls.ner.billing.charge.service.impl;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.charge.dao.IChargeBillRpcDao;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.service.IChargeBillingRltService;
import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.util.FormatUtil;
import com.ls.ner.util.JodaDateTime;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.dao.IChargeBillingConfDao;
import com.ls.ner.billing.charge.dao.IChargeBillingDao;
import com.ls.ner.billing.charge.dao.IChargeBillingPeriodsDao;
import com.ls.ner.billing.charge.dao.IChargeBillingRltDao;
import com.ls.ner.billing.charge.dao.IChargeBillingRltPeriodsDao;
import com.ls.ner.billing.charge.dao.IChargePeriodsDao;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
/**
 * 充电计费结果
 * <AUTHOR>
 */
@Service(target = {ServiceType.LOCAL}, value = "chargeBillingRltService")
public class ChargeBillingRltServiceImpl implements IChargeBillingRltService {
	private static final Logger LOGGER = LoggerFactory.getLogger(ChargeBillingRltServiceImpl.class);

	@Autowired
	private IChargeBillingPeriodsDao chargeBillingPeriodsDao;

	@Autowired
	private IChargeBillingConfDao chargeBillingConfDao;

	@Autowired
	private IChargePeriodsDao chargePeriodsDao;

	@Autowired
	private IChargeBillingRltPeriodsDao chargeBillingRltPeriodsDao;

	@Autowired
	private IChargeBillingRltDao chargeBillingRltDao;

	@Autowired
	private IChargeBillingDao chargeBillingDao;

	@ServiceAutowired("chargeSerItemService")
	private IChargeSerItemService chargeSerItemService;

	@Autowired
	private ChargeBillingRltAdapter chargeBillingRltAdapter;

	@Autowired
	private ChargeBillingRltCalculator chargeBillingRltCalculator;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(serviceTypes= ServiceType.RPC)
	private ISysParamService sysParamService;

	@Autowired
	private ChargeBillingRltUtil cbRltUtil;

	@Autowired
	private IChargeBillRpcDao chargeBillRpcDao;
	@ServiceAutowired("servicePriceService")
	private ITimeShareServicePriceService servicePriceService;
	@ServiceAutowired("peakService")
	private IXpPeakService peakService;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 	订单结算计费 ---*******.4	刷卡结算new时，调用此方法，传入刷卡结算数据
	 * @param inMap (
	 * 	APP_NO 订单编号
	 * 	DATA_TYPE 数据类型
	 *  。。。。
	 * )
	 * @return Map(
	 * 		tAmt;//充电金额。如果是分时，则为分时金额总和
	 * 		itemTAmt;//服务项目总金额
	 * 		amt;//总金额
	 * )
	 * @throws Exception 错误信息
	 */
	public Map<String,Object> createOrderStatements(Map<String,Object> inMap){
		//1、判断传参是否正确
		String appNo = cbRltUtil.isNotEmpty(inMap.get("APP_NO"), "订单编号不能为空");
		//2、查询该订单的充电计费信息
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(appNo);
		Map orderMap = new HashMap();
		orderMap.put("appNo",appNo);
		if(cbrBo == null)
			throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");
		List<ChargeBillingRltPeriodsBo> cbrList = chargeBillingRltPeriodsDao.queryChargeBillingRltPeriod(appNo);
		cbrBo.setRltPeriods(cbrList);
		//---如果boss计费，推送数据为示数，并且计费方式为分时则获取充电实时分析记录表信息
		if(ChargeConstant.dateType.BOSS_ONE.equals(inMap.get("DATA_TYPE")) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//boss计费，推送数据为示数
			List<ChargeBillingRltPeriodsBo> newPeriods = chargeBillingPeriodsDao.queryChargeBillingPeriod(orderMap);
			cbrBo.setNewPeriods(newPeriods);
		}
		//3、装配数据
		chargeBillingRltAdapter.adapter(inMap, cbrBo);
		//4、计算费用
		if(ChargeConstant.dateType.BOSS_ONE.equals(inMap.get("DATA_TYPE"))){//boss计费，推送数据为示数
			chargeBillingRltCalculator.calculator(cbrBo);
		}else if(ChargeConstant.dateType.BOSS_TWO.equals(inMap.get("DATA_TYPE"))){//boss计费，推送电量
			chargeBillingRltCalculator.calculator(cbrBo);
		}else if(ChargeConstant.dateType.REMOTE_ONE.equals(inMap.get("DATA_TYPE"))){//桩计费

		}
		//5、存储充电计费信息
		chargeBillingRltDao.updateChargeBillingRlt(cbrBo);
		chargeBillingRltPeriodsDao.deleteChargeBillingRltPeriod(appNo);
		List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
		if(periods !=null)
			for(ChargeBillingRltPeriodsBo period:periods)
				chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(period);
		//返回数据
		Map<String,Object> returnMap = new HashMap<String,Object> ();
		returnMap.put("tAmt", cbrBo.gettAmt());//充电金额。如果是分时，则为分时金额总和
		returnMap.put("itemTAmt", cbrBo.getItemTAmt());//服务项目总金额
		returnMap.put("amt", cbrBo.getAmt());//总金额
		returnMap.put("detail", confTransToList(cbrBo));//收费明细
		return returnMap;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 	√生成订单时调用，创建充电计费版本
	 * @param inMap (
	 * 	stationNo 充电站编号
	 * 	appNo 订单编号
	 * )
	 * @throws Exception 错误信息
	 */
	public void createChargeOrderVersionForOrder(Map<String,Object> inMap){
		//1、判断传参是否正确
		String stationId = cbRltUtil.isNotEmpty(inMap.get("stationId"), "没有传入充电站编号");
		String appNo = cbRltUtil.isNotEmpty(inMap.get("appNo"), "没有传入订单编号");
		String pileId = StringUtil.nullToString(inMap.get("pileId"));
		String custId = StringUtil.nullToString(inMap.get("custId"));
		String groupId = StringUtil.nullToString(inMap.get("groupId"));
		String pCustId = StringUtil.nullToString(inMap.get("pCustId"));
		//2、判断是否二次提交
		int count = chargeBillingRltDao.queryChargeBillingRltNum(appNo);
		if(count > 0)
			throw new ChargeBillingRltException("多条充电订单计费版本生成");

		ChargeBillingConfBo cbcBo = obtainChargeBillingConf(stationId, pileId, null,custId,groupId,pCustId);

		//初始化itemchargemode
		servicePriceService.initItemChargeModeConfBo(cbcBo);

		//3、如果数据类型 = 3桩计费，  插入【充电订单计费结果/E_  [CHARGE_BILLING_RLT】记录，包括：订单编号、充电计费编号、费控模式、计费模式、服务项目编号、服务项目单价、服务项目单位
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setAppNo(appNo);//订单编号
		cbrBo.setBillCtlMode(cbcBo.getBillCtlMode());//费控模式
		cbrBo.setChcNo(cbcBo.getChcNo());//充电计费编号
		cbrBo.setChargeMode(cbcBo.getChargeMode());//计费模式
		cbrBo.setAttachItemNos(cbcBo.getAttachItemNos());//服务项目编号
		cbrBo.setAttachItemPrices(cbcBo.getAttachItemPrices());//服务项目单价
		cbrBo.setAttachItemUnits(chargeSerItemService.queryUnitsByNo(cbcBo.getAttachItemNos()));//服务项目单位
		cbrBo.setFreeNum(cbcBo.getFreeNum());//免费量
		cbrBo.setItemChargeMode(cbcBo.getItemChargeMode());//费用项计费模式

		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbcBo.getChargeMode())){//如果是标准计费方式，需要写入chargePrice计费单价
			cbrBo.setChargePrice(cbcBo.getChargePrice());
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbcBo.getChargeMode())){//如果是分时计费方式，需要写入订单计费结果分时明细
			//插入【充电计费结果分时明细】，信息包括：订单编号、充电计费编号、排序号、开始时段、结束时段、计费单价；
			ChargeBillingConfQueryCondition periodsCondition = new ChargeBillingConfQueryCondition();
			periodsCondition.setChcNo(cbRltUtil.isNotEmpty(cbcBo.getChcNo(), "查询不到充电计费编号"));
			List<ChargePeriodsBo> cperiods = peakService.queryPeriodListCheckPeak(periodsCondition);
			if(cperiods!=null){
				for(ChargePeriodsBo cpBo:cperiods){
					ChargeBillingRltPeriodsBo cbrpBo = new ChargeBillingRltPeriodsBo();
					BeanUtils.copyProperties(cpBo, cbrpBo);
					cbrpBo.setAppNo(appNo);
					chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(cbrpBo);
				}
			}
		}
		chargeBillingRltDao.insertChargeBillingRlt(cbrBo);

		//20220705判断是否开启尖峰计费，如果有则添加订单计费关联
		peakService.saveOrderExtend(cbrBo.getAppNo(), cbrBo.getChcNo());
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 	√生成订单后，启动充电时，修改充电计费版本(加入充电开始时间、充电开始电量等)
	 * @param inMap (
	 * 	appNo 订单编号
	 * 	dataTime 数据时间
	 *  dataType 数据类型     1BOSS计费，推送数据为示数    2BOSS计费，推送数据为电量      3桩计费，推送数据为量、价、费
	 *  timeSharFlag 分时标志(未用，预留)
	 *  bgnTime 开始时间
	 *  mrNum 示数
	 * )
	 * @throws Exception 错误信息
	 */
	public void updateChargeOrderVersionForOrder(Map<String,Object> inMap){
		//1、判断传参是否正确
		String appNo = cbRltUtil.isNotEmpty(inMap.get("appNo"), "没有传入订单编号");
		String dataType = cbRltUtil.isNotEmpty(inMap.get("dataType"), "没有传入数据类型");
		String dataTime = cbRltUtil.isNotEmpty(inMap.get("dataTime"), "没有传入数据时间");
		//2、查询该订单的充电计费信息
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(appNo);
		if(cbrBo == null)
			throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");
		//3、如果数据类型 = 3桩计费，  插入【充电订单计费结果/E_CHARGE_BILLING_RLT】记录，包括：请求生成时间、充电开始时间（如果为空，=数据时间）
		//并且清空服务费编号、服务费单价、服务费单位
		cbrBo.setBgnTime(StringUtils.isEmpty(inMap.get("bgnTime"))?String.valueOf(inMap.get("dataTime")):String.valueOf(inMap.get("bgnTime")));
		if(ChargeConstant.dateType.REMOTE_ONE.equals(dataType)){//桩计费
			cbrBo.setAttachItemNos("");//服务项目编号
			cbrBo.setAttachItemPrices("");//服务项目单价
			cbrBo.setAttachItemUnits("");//服务项目单位
			chargeBillingRltPeriodsDao.deleteChargeBillingRltPeriod(appNo);
		}else{
			//4、如果数据类型 !=3 boss计费，插入【充电订单计费结果/E_CHARGE_BILLING_RLT】记录，包括：请求生成时间、充电开始时间（如果为空，=数据时间）
			//5、如果数据类型 = 1boss计费，插入【充电订单计费结果/E_CHARGE_BILLING_RLT】记录，示数
			if(ChargeConstant.dateType.BOSS_ONE.equals(dataType)){
				String mrNum = cbRltUtil.isNotEmpty(inMap.get("mrNum"), "数据类型为1，示数不能为空");
				cbrBo.setBgnMrNum(mrNum);
			}
		}
		chargeBillingRltDao.updateChargeBillingRlt(cbrBo);
	}

	//============================================接口部分===========================================
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-02
	 * @description 	*******	获取充电计费版本（价格信息）
	 * @param inMap (
	 * 	stationNo 充电站编号
	 * )
	 * @throws Exception 错误信息
	 */
	public Map<String,Object> obtainChargeOrderVersion(Map<String,Object> inMap) throws Exception{
		String stationId = cbRltUtil.isNotEmpty(inMap.get("stationId"), "充电站id不能为空");
		String custId = StringUtil.nullToString(inMap.get("custId"));
		String groupId = StringUtil.nullToString(inMap.get("groupId"));

		Map<String,Object> returnMap = new HashMap<String,Object> ();
//		ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
//		cbcCondition.setStationNo(stationNo);
//		cbcCondition.setChcStatus(ChargeConstant.validFlag.ENABLE);
//		ChargeBillingConfBo cbcBo = null;
//		 List<ChargeBillingConfBo> cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
		ChargeBillingConfBo cbcBo = obtainChargeBillingConf(stationId, null,null,custId,groupId,null);
		returnMap.put("chcNo", cbRltUtil.isNotEmpty(cbcBo.getChcNo(), "充电计费编号为空"));//充电计费编号
		returnMap.put("chcRemark", cbRltUtil.returnNotNull(cbcBo.getChcRemark()));//充电计费说明
		returnMap.put("chargeMode", cbRltUtil.returnNotNull(cbcBo.getChargeMode()));//充电计费模式
		returnMap.put("chargeMethod", cbRltUtil.returnNotNull(cbcBo.getChargeMethod()));//充电收费方式
		//获取服务费用项
		if(!StringUtils.isEmpty(cbcBo.getAttachItemNos())){
			ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
			csCondition.setItemNo(cbcBo.getAttachItemNos());
			csCondition.setPrice(cbcBo.getAttachItemPrices());
			List<ChargeSerItemBo> csList = chargeSerItemService.querySerItemsByNo(csCondition);
			if(csList != null)
				for(ChargeSerItemBo bo : csList){
					CodeBO code = codeService.getStandardCode(BizConstants.CodeType.CHC_BILLING_PRICE_UNIT,bo.getItemUnit(), null);
					bo.setItemUnitName(code!=null?code.getCodeName().replace("千瓦时", "度"):"");
					bo.setServicePrice(FormatUtil.formatNumber(bo.getServicePrice(),4));
					bo.notNull();
				}
			returnMap.put("attachlist", csList);
		}
		//分时明细
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbcBo.getChargeMode())){//如果是标准计费方式
			List<ChargePeriodsBo> cperiodsList = new ArrayList<ChargePeriodsBo>();
			ChargePeriodsBo cperiods = new ChargePeriodsBo();
			cperiods.setSn("1");
			cperiods.setBeginTime("00:00");
			cperiods.setEndTime("24:00");
			cperiods.setPrice(FormatUtil.formatNumber(cbcBo.getChargePrice(),4));
			cperiods.notNull();
			cperiodsList.add(cperiods);
			returnMap.put("periodslist", cperiodsList);
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbcBo.getChargeMode())){//如果是分时计费方式
			ChargeBillingConfQueryCondition cperiodsCondition = new ChargeBillingConfQueryCondition();
			cperiodsCondition.setChcNo(cbcBo.getChcNo());
			List<ChargePeriodsBo> cperiodsList = peakService.queryPeriodListCheckPeak(cperiodsCondition);
			if(cperiodsList!=null)
				for(ChargePeriodsBo cpBo : cperiodsList){
					cpBo.setPrice(FormatUtil.formatNumber(cpBo.getPrice(),4));
					cpBo.notNull();
				}
			returnMap.put("periodslist", cperiodsList);
		}else
			throw new ChargeBillingRltException("没有该计费模式");
		return returnMap;
	}


	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-02
	 * @description 	获取充电实时计费信息或者充电结算计费信息
	 * @param inMap (
	 * 	appNo 订单编号
	 * 	chargeState 充电状态   02 充电中、03 已完成
	 * )
	 * @throws Exception 错误信息
	 * @return Map(
	 * 		tAmt;//充电金额。如果是分时，则为分时金额总和
	 * 		itemTAmt;//服务项目总金额
	 * 		amt;//总金额
	 * 		attachlist:[{
	 * 			itemNo;//收费项目编号
	 * 			itemName;//收费项目名称
	 * 			itemAmt;//服务项目金额
	 * 		}]
	 * )
	 */
	public Map<String,Object> obtainChargeOrderSettlement(Map<String,Object> inMap)throws Exception{
		String appNo = cbRltUtil.isNotEmpty(inMap.get("appNo"), "入参的订单编号不能为空");
		String chargeState = cbRltUtil.isNotEmpty(inMap.get("chargeState"), "入参的充电状态不能为空");
		Map<String,Object> returnMap = new HashMap<String,Object> ();
		ChargeBillingRltBo cbRltBo = null;
		if("02".equals(chargeState)){//获取充电结算费用
			cbRltBo = chargeBillingDao.queryChargeBilling(appNo);
		}else if("03".equals(chargeState)){//获取实时充电费用
			cbRltBo = chargeBillingRltDao.queryChargeBillingRlt(appNo);
		}else
			throw new ChargeBillingRltException("入参的充电状态不对，不是充电中或 已完成");
		returnMap.put("tAmt", cbRltUtil.returnNotNull(cbRltBo==null?"0.00":cbRltBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和
		returnMap.put("itemTAmt", cbRltUtil.returnNotNull(cbRltBo==null?"0.00":cbRltBo.getItemTAmt()));//服务项目总金额
		returnMap.put("amt", cbRltUtil.returnNotNull(cbRltBo==null?"0.00":cbRltBo.getAmt()));//总金额
		//获取服务费用项
		if(!StringUtils.isEmpty(cbRltBo==null?null:cbRltBo.getAttachItemNos())){
			List<Map> attachlist = new ArrayList<Map> ();
			ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
			csCondition.setItemNo(cbRltBo.getAttachItemNos());
			csCondition.setPrice(cbRltBo.getAttachItemAmts());
			List<ChargeSerItemBo> csList = chargeSerItemService.querySerItemsByNo(csCondition);
			if(csList != null)
				for(ChargeSerItemBo bo : csList){
					Map<String,Object> itemMap = new HashMap<String,Object>();
					itemMap.put("itemNo", bo.getItemNo());
					itemMap.put("itemName", bo.getItemName());
					itemMap.put("itemAmt", bo.getServicePrice());
					attachlist.add(itemMap);
				}
			returnMap.put("attachlist", attachlist);
		}
		return returnMap;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
	 */
	public int queryChargeBillingConfsNum(ChargeBillingConfQueryCondition bo){
		return chargeBillingConfDao.queryChargeBillingConfsNum(bo);
	}

	//============================================private部分===========================================

	/**
	 * 获取充电计费配置，判断是否充电计费配置有效,如果已失效，修改状态
	 * @param
	 * @param
	 * @param endEftDate 生效日期 小于的时间点   格式：yyyy-MM-dd HH:mm
	 * @return ChargeBillingConfBo 当前充电计费版本
	 * */
	public ChargeBillingConfBo obtainChargeBillingConf(String stationId,String pileId,String endEftDate,String custId,String groupId,String pCustId){
		if(StringUtils.isEmpty(stationId))
			throw new ChargeBillingRltException("没有传入充电站id，无法获取充电计费配置");
		ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
		if(StringUtils.isEmpty(endEftDate))
			endEftDate=JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
		cbcCondition.setEndEftDate(endEftDate);
		cbcCondition.setChcStatus(ChargeConstant.validFlag.ENABLE);
		if(!StringUtils.isEmpty(stationId))
			cbcCondition.setStationId(stationId);

		//按照生效时间排序，取与当前时间节点最接近的一条返回
		List<ChargeBillingConfBo> cbcList=new ArrayList<ChargeBillingConfBo>();
		String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
		if("toMoreStation".equals(chargeVesion)){//新版计费
			if(!StringUtils.isEmpty(custId)){
				//先获取用户单独计价
				cbcCondition.setStationId(null);
				cbcCondition.setCustId(custId);
				cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
				LOGGER.info("======获取用户单独计费：" + JsonUtil.obj2Json(cbcList));
				// 企业用户自身账号（非子账号）如果存在企业计费，判断是否在站点可用
				if(cbcList != null && cbcList.size() > 0){
					ChargeBillingConfBo confBo = cbcList.get(0);
					if(BillConstants.RANGE_FLAG.PART.equals(confBo.getRangeFlag())){
						ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
						condition.setChcNo(confBo.getChcNo());
						condition.setStationId(stationId);
						condition.setCustId(custId);
						List<ChargeBillingConfBo> clist = chargeBillingConfDao.queryBillingStation(condition);
						if(clist == null || clist.size() == 0){
							cbcList = new ArrayList<ChargeBillingConfBo>();
							LOGGER.debug("======"+stationId+":该站点不满足企业计费的使用站点====");
						}
					}
				}
			}
			if(!CollectionUtils.isNotEmpty(cbcList)){
				//获取企业账户计费
				if(!StringUtils.isEmpty(pCustId)){
					cbcCondition.setStationId(null);
					cbcCondition.setCustId(pCustId);
					cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
					LOGGER.info("======获取企业账户计费：" + JsonUtil.obj2Json(cbcList));
					// 如果存在企业计费，判断是否在站点可用
					if(cbcList != null && cbcList.size() > 0){
						ChargeBillingConfBo confBo = cbcList.get(0);
						if(BillConstants.RANGE_FLAG.PART.equals(confBo.getRangeFlag())){
							ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
							condition.setChcNo(confBo.getChcNo());
							condition.setStationId(stationId);
							condition.setCustId(custId);
							List<ChargeBillingConfBo> clist = chargeBillingConfDao.queryBillingStation(condition);
							if(clist == null || clist.size() == 0){
								cbcList = new ArrayList<ChargeBillingConfBo>();
								LOGGER.debug("======"+stationId+":该站点不满足企业计费的使用站点====");
							}
						}
					}
				}
			}
			if(!CollectionUtils.isNotEmpty(cbcList)){
				//获取客户分组计费
				if(!StringUtils.isEmpty(groupId)){
					Map inMap = new HashMap();
					inMap.put("chcStatus",ChargeConstant.validFlag.ENABLE);
					inMap.put("endEftDate",endEftDate);
					inMap.put("groupId",groupId);
					cbcList = chargeBillingConfDao.queryCustGroupChargeBilling(inMap);
					LOGGER.info("======获取大客户分组计费：" + JsonUtil.obj2Json(cbcList));
				}
			}
			if(!CollectionUtils.isNotEmpty(cbcList)){
				//获取桩计费
				String isAssistPileBilling=sysParamService.getSysParamsValues("isAssistPileBilling");
				if(!StringUtils.isEmpty(pileId) && PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
					cbcCondition.setStationId(stationId);
					cbcCondition.setPileId(pileId);
					cbcList = chargeBillingConfDao.queryPileChargeBillingConfs(cbcCondition);
					LOGGER.info("======获取桩计费：" + JsonUtil.obj2Json(cbcList));
				}
			}
			if(!CollectionUtils.isNotEmpty(cbcList)){
				cbcCondition.setStationId(stationId);
				cbcList = chargeBillingConfDao.queryNewChargeBillingConfs(cbcCondition);
				LOGGER.info("======获取站点计费：" + JsonUtil.obj2Json(cbcList));
			}

		}else{
			if(!StringUtils.isEmpty(custId)){
				//先获取用户计费
				cbcCondition.setStationId(null);
				cbcCondition.setCustId(custId);
				cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
				// 如果存在企业计费，判断是否在站点可用
				if(cbcList != null && cbcList.size() > 0){
					ChargeBillingConfBo confBo = cbcList.get(0);
					if(BillConstants.RANGE_FLAG.PART.equals(confBo.getRangeFlag())){
						ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
						condition.setChcNo(confBo.getChcNo());
						condition.setStationId(stationId);
						condition.setCustId(custId);
						List<ChargeBillingConfBo> clist = chargeBillingConfDao.queryBillingStation(condition);
						if(clist == null || clist.size() == 0){
							cbcList = new ArrayList<ChargeBillingConfBo>();
							LOGGER.debug("======"+stationId+":该站点不满足企业计费的使用站点====");
						}
					}
				}
			}
			if(cbcList != null && cbcList.size()>0){

			}else{
				cbcCondition.setCustId(null);
				cbcCondition.setStationId(stationId);
				cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
			}
		}
		if(cbcList == null || cbcList.size() == 0)
			throw new ChargeBillingRltException("该充电站没有有效的充电计费配置");
		//将多余的充电计费配置状态改为无效
		if(cbcList.size()>1){
			for(int i = 1;i<cbcList.size();i++){
				ChargeBillingConfBo cbc = new ChargeBillingConfBo();
				cbc.setChcNo(cbcList.get(i).getChcNo());
				cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
				chargeBillingConfDao.updateChargeBillingConf(cbc);
			}
		}
		return cbcList.get(0);
	}

	private List<Map<String,Object>>  confTransToList(ChargeBillingRltBo cbrBo){
		try {
			List<Map<String,Object>> returnMap = chargeSerItemService.querySerItemsByNo(cbrBo);
			if(cbrBo.getRltPeriods()==null || cbrBo.getRltPeriods().size()==0){//不分时
				Map<String,Object> m = new HashMap<String,Object>();
				m.put("itemUnits","元/度");//费用项单位
				m.put("pricingAmt",cbrBo.gettAmt());//费用金额
				m.put("amount",cbrBo.gettPq());//计费项量值
				m.put("price",cbrBo.getChargePrice());//计费单价
				m.put("itemName","充电费");//费用项名称
				returnMap.add(m);
			}else{//分时
				List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
				for(ChargeBillingRltPeriodsBo cbrpBo:periods){
					Map<String,Object> m = new HashMap<String,Object>();
					m.put("itemUnits","元/度");//费用项单位
					m.put("pricingAmt",cbrpBo.getAmt());//费用金额
					m.put("amount",cbrpBo.getPq());//计费项量值
					m.put("price",cbrpBo.getPrice());//计费单价
					m.put("itemName","充电费("+cbrpBo.getBeginTime()+"~"+cbrpBo.getEndTime()+")");//费用项名称
					returnMap.add(m);
				}
			}
			return returnMap;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
