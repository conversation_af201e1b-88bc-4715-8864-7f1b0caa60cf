<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="选择生效日期" />
<ls:body>
		<ls:form id="configCopyForm" name="configCopyForm">
		    <ls:text name="billingNo" property="billingNo" visible="false"></ls:text>
				<table class="tab_search">
						<tr>
								<td>
										<ls:label text="生效日期" ref="eftDate" />
								</td>
								<td>
										<ls:date name="eftDate" format="yyyy-MM-dd HH:mm" required="true"></ls:date>
								</td>
						</tr>
						<tr>
                <td></td>
                <td>
                    <ls:button text="保存" onclick="doSave" />
                </td>
            </tr>
				</table>
		</ls:form>
		<ls:script>
		function doSave(){
		  if(LS.isEmpty(billingNo.getValue())){
		    LS.message("info","请先选择一条定价信息");
		    return;
		  }
		  
		  LS.ajax("~/billing/configs/copyConfig", configCopyForm.getFormData(), function(result) {
        if(result.items[0]=="isExist"){
          LS.message("info","该生效日期已经存在相同的定价，请重新选择");
          return;
        }
        LS.message("info","复制成功");
        LS.parent().doSearch();
        LS.window.close();
      });
    }
    </ls:script>
</ls:body>
</html>