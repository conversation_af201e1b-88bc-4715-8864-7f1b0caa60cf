package com.ls.ner.billing.market.dao;

import com.ls.ner.billing.api.market.vo.CouponPutRpcBo;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPutDetBo;
import com.ls.ner.billing.market.bo.CouponPutBo;
import com.ls.ner.billing.market.bo.DelayInfo;
import com.ls.ner.billing.market.vo.MarketCondition;
import org.apache.ibatis.annotations.Param;


import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 优惠券发放Dao层实现
 * description
 * 创建时间 2016年5月31日下午2:30:23
 * 创建人 lise
 */
public interface ICouponPutDao {
	/**
	 * 保存优惠券发放日记
	 * description
	 * 创建时间 2016年6月2日下午5:40:32
	 * 创建人 lise
	 */
	public int insertCouponPut(CouponPutBo bo);
	/**
	 * 保存优惠券发放明细
	 * description
	 * 创建时间 2016年6月2日下午5:43:20
	 * 创建人 lise
	 */
	public int insertCouponPutDet(CouponPutDetBo det);

	/**
	*描述:调用RPC04-02-003优惠券发放接口后更新发放主表
	* @param: 
	*@return: 
	*创建人:biaoxiangd
	*创建时间: 2017-06-05 11:40
	*/
	public int updateCouponPut(CouponPutBo bo);
	/**
	*描述:调用RPC04-02-003优惠券发放接口后更新发放详细表
	* @param: 
	*@return: 
	*创建人:biaoxiangd
	*创建时间: 2017-06-05 11:40
	*/
	public int updateCouponPutDet(CouponPutDetBo det);


	/**
	 * 获取优惠券发放记录
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public List<CouponPutBo> getCouponPutLog(MarketCondition condition);
	/**
	 * 统计优惠券发放记录
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public int getCouponPutLogCount(MarketCondition condition);

	/**
	 * 获取优惠券发放记录详情
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public List<CouponPutBo> getCouponPutDetLog(MarketCondition condition);

	/**
	 * 统计优惠券发放记录详情
	 * description
	 * 创建时间 2016年6月3日上午11:17:53
	 * 创建人 lise
	 */
	public int getCouponPutLogDetCount(MarketCondition condition);

	/**
	 *描述:获取优惠券发放
	 * @param: [condition]
	 *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
	 *创建人:biaoxiangd
	 *创建时间: 2017-06-03 11:09
	 */
	public List<CouponBo> getCouponPut(MarketCondition condition);
	/**
	 * 统计优惠券发放
	 * description
	 * 创建时间 2016年6月4日下午5:19:38
	 * 创建人 lise
	 */
	public int getCouponPutCount(MarketCondition condition);

	List<CouponPutDetBo> queryCustByPutId(String putId);


	List<CouponPutRpcBo> queryCouponPutByDelay(Date delayTime);

    boolean updateDelayByPutId(@Param("putId") String putId, @Param("delayValue") Integer delayValue,@Param("expectDelay") Integer expectDelay);

	/**
	 *  上MYSQL读锁，与isDelayUpdate 产生阻塞关系
	 * @param cpnId
	 * @return
	 */
	DelayInfo queryPutDelayByCpnId(String cpnId);

	boolean updateCouponPutNum(@Param("putId") String putId, @Param("custCount") long custCount);

	Map<String, String> getSuperpositionFlag(@Param("cpnId") String cpnId);


	int cumulaAddPutNum(@Param("putId") Long putId, @Param("addPutNum") Long addPutNum);
}
