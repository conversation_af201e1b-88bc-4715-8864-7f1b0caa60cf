package com.ls.ner.billing.xpcharge.dao;


import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @dateTime 2018-03-21
 * @description 充电计费下发
 */
public interface IXpStationSendDao {

    /**
     * @description 查询最新计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheNewChc(Map m);

    /**
     * @description 查询当前计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheCurrentChc(Map m);

    /**
     * 新增计费配置下发记录
     * @param cSendBo
     */
    public void insertChcbillSend(ChcbillSendBo cSendBo);

    int  queryPileBillCount(Map sercher);

    /**
     * 查询下发记录数
     * @param cSendBo
     * @return
     */
    int queryChcbillSend(ChcbillSendBo cSendBo);

    /**
     * 更新下发记录
     * @param cSendBo
     */
    void updateChcbillSend(ChcbillSendBo cSendBo);

    int countByStationIdAndChcNo(@Param("stationId") String stationId, @Param("chcNo")String chcNo);
}
