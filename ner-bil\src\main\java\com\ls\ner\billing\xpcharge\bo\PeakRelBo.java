package com.ls.ner.billing.xpcharge.bo;

import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.DicAttribute;

/**
 * <AUTHOR>
 * @description 尖峰电价关联
 * @date 2022/6/20 14:44
 */
public class PeakRelBo {

    /**
     * 关联日志主键
     */
    private String id;
    /**
     * 关联主键
     */
    private String relId;
    /**
     * 充电计费编号
     */
    private String chcNo;
    /**
     * 上一次状态 0-停用 1-启用
     */
    private String lastState;
    /**
     * 当前状态 0-停用 1-启用
     */
    private String currentState;
    /**
     * 生效日期
     */
    private String eftDate;
    /**
     * 失效日期
     */
    private String invDate;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 单位组织编码
     */
    private String orgCode;
    /**
     * 计费名称
     */
    private String chcName;
    /**
     * 计费状态
     */
    private String chcStatus;
    /**
     * 费控模式，1本地 2远程
     */
    private String billCtlMode;
    /**
     * 计费模式
     */
    private String chargeMode;

    private String orgCodeName;//管理单位名称

    /**
     * 费控模式名称
     */
    @DicAttribute(dicName = "codeDict", key = "billCtlMode", subType = BizConstants.CodeType.BILL_CTL_MODE)
    private String billCtlModeName;

    /**
     * 计费模式名称
     */
    @DicAttribute(dicName = "codeDict", key = "chargeMode", subType = BizConstants.CodeType.CHC_BILLING_CHARGE_MODE)
    private String chargeModeName;

    /**
     * 状态名称，01有效、02无效、03草稿
     */
    @DicAttribute(dicName = "codeDict", key = "chcStatus", subType = BizConstants.CodeType.VALID_FLAG)
    private String chcStatusName;

    public String getOrgCodeName() {
        return orgCodeName;
    }

    public void setOrgCodeName(String orgCodeName) {
        this.orgCodeName = orgCodeName;
    }

    public String getBillCtlModeName() {
        return billCtlModeName;
    }

    public void setBillCtlModeName(String billCtlModeName) {
        this.billCtlModeName = billCtlModeName;
    }

    public String getChargeModeName() {
        return chargeModeName;
    }

    public void setChargeModeName(String chargeModeName) {
        this.chargeModeName = chargeModeName;
    }

    public String getChcStatusName() {
        return chcStatusName;
    }

    public void setChcStatusName(String chcStatusName) {
        this.chcStatusName = chcStatusName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getChcName() {
        return chcName;
    }

    public void setChcName(String chcName) {
        this.chcName = chcName;
    }

    public String getChcStatus() {
        return chcStatus;
    }

    public void setChcStatus(String chcStatus) {
        this.chcStatus = chcStatus;
    }

    public String getBillCtlMode() {
        return billCtlMode;
    }

    public void setBillCtlMode(String billCtlMode) {
        this.billCtlMode = billCtlMode;
    }

    public String getChargeMode() {
        return chargeMode;
    }

    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelId() {
        return relId;
    }

    public void setRelId(String relId) {
        this.relId = relId;
    }

    public String getChcNo() {
        return chcNo;
    }

    public void setChcNo(String chcNo) {
        this.chcNo = chcNo;
    }

    public String getLastState() {
        return lastState;
    }

    public void setLastState(String lastState) {
        this.lastState = lastState;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
