package com.ls.ner.billing.mktact.controller;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.bo.MarketTypeBo;
import com.ls.ner.billing.mktact.service.IActCondService;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:通过活动类型控制不同优惠活动
 * MarketTypeController.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/29 15:55
 **/
@Controller
public class MarketTypeController extends QueryController<MarketTypeBo> {
    private static final Logger logger = LoggerFactory.getLogger(MarketTypeController.class);

    @Autowired
    private ControllerSupport controllerSupport;

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "actCondService", serviceTypes = ServiceType.LOCAL)
    private IActCondService actCondService;


    /**
     * 描述:  首单免/满减满送
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage")
    public String init(Model m, @RequestParam("actId") String actId ,@RequestParam("actType") String actType,@RequestParam("busiType") String busiType) throws Exception {
        MarketTypeBo marketTypeBo = new MarketTypeBo();
        marketTypeBo.setActId(actId);
        MarketTypeBo typeBo = actCondService.queryActCond(marketTypeBo);
        if (typeBo != null && StringUtil.isNotBlank(typeBo.getActCondId())) {
            marketTypeBo = typeBo;
//            marketTypeBo.setActType(actType);
        } else {
            // 默认 整单金额
            marketTypeBo.setDctCondType("00");
        }

        marketTypeBo.setActType(actType);
        marketTypeBo.setBusiType(busiType);
        // 优惠条件类别
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("dctCondType", null);
        m.addAttribute("dctCondTypeList", cpnTypeList);
        // 优惠条件对象
        List<Map> prodList = new ArrayList<Map>();
        Map<String, String> inMap = new HashMap<String, String>();
        inMap.put("prodId", "");
        prodList = actCondService.setProdInfo(inMap);
        List<CodeBO> dctProdIdList = new ArrayList<CodeBO>();
        if (prodList != null && prodList.size() > 0) {
            for (Map map : prodList) {
                CodeBO codeBo = new CodeBO();
                codeBo.setCodeName(StringUtil.getString(map.get("PROD_NAME")));
                codeBo.setCodeValue(StringUtil.getString(map.get("PROD_ID")));
                dctProdIdList.add(codeBo);
            }
        } else {
            CodeBO codeBo = new CodeBO();
            codeBo.setCodeName("无");
            codeBo.setCodeValue("0");
            dctProdIdList.add(codeBo);
        }

        m.addAttribute("prodIdList", dctProdIdList);
        // 数值单位
        List<CodeBO> dctCondUnitList = codeService.getStandardCodes("tariffUnit", null);
        m.addAttribute("dctCondUnitList", dctCondUnitList);
        logger.debug(">>>>>/marketacts/firstFreeManage firstFreeForm  :{}",
                JsonUtils.toJson(marketTypeBo));
        m.addAttribute("firstFreeForm", marketTypeBo);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/firstFreeManage";
    }

    /**
     * 描述: 首单免/满减满送 -- 优惠条件明细
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/actDet")
    public String actDet(Model m,@RequestParam("actCondId") String actCondId, @RequestParam("dctLvl") String dctLvl) throws Exception {
        MarketTypeBo marketTypeBo = new MarketTypeBo();
        marketTypeBo.setActCondId(actCondId);
        List<MarketTypeBo> list = actCondService.queryMktActCondDet(marketTypeBo);
        if (list != null && list.size() > 0) {
            marketTypeBo = list.get(0);
        }
        marketTypeBo.setDctType("0100");
        marketTypeBo.setDctCondValue(null);
        marketTypeBo.setActCondId(actCondId);
        marketTypeBo.setDctLvl(dctLvl);
        // 优惠内容 - 优惠对象
        m.addAttribute("actDctProdIdList", actCondService.setProdInfo(new HashMap<String, String>()));
        // 优惠类别
        List<CodeBO> dctTypeList = codeService.getStandardCodes("dctType", null);
        m.addAttribute("dctTypeList", dctTypeList);
        //优惠内容 - 单位
        List<CodeBO> actDctCondUnitList = codeService.getStandardCodes("tariffUnit", null);
        m.addAttribute("actDctCondUnitList", actDctCondUnitList);

        m.addAttribute("actCondDetForm", marketTypeBo);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/actCondDet";
    }

    /**
     * 描述: 首单免/满减满送 -- 优惠条件保存
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/saveActCond")
    public @ResponseBody
    WrappedResult saveActCond(@RequestBody Map<String, Object> dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        MarketTypeBo bo = new MarketTypeBo();
        BeanUtils.populate(bo, dataParams);
        if (StringUtil.isBlank(bo.getActId())) {
            retMap.put("successful", false);
            retMap.put("resultValue", "未选择活动类型");
            return WrappedResult.successWrapedResult(retMap);
        }

        boolean successful = true;
        String actCondId = bo.getActCondId(), resultHint = "", resultValue = "";
        try {
            if (StringUtil.isBlank(bo.getActCondId())) {
                bo.setDctCondFlag("2"); // 存在活动条件
                String condId = actCondService.saveActCond(bo);
                actCondId = String.valueOf(condId);
            }else{
                bo.setDctCondFlag("2"); // 存在活动条件
                actCondService.updateActCond(bo);
            }
            if (StringUtil.isNotBlank(actCondId)) {
                resultValue = actCondId;
            } else {
                successful = false;
                resultHint = "新增优惠条件失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 首单免/满减满送 -- 获取优惠明细
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/queryActDctAll")
    public @ItemResponseBody
    QueryResultObject queryActDctAll(@QueryRequestParam("params") RequestCondition params) throws Exception {
        MarketTypeBo condition = this.rCondition2QCondition(params);
        List<MarketTypeBo> list = actCondService.queryActDctAll(condition);
        return RestUtils.wrappQueryResult(list, list.size());
    }


    /**
     * 描述: 首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/saveActCondDet", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveActCondDet(@RequestBody Map dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String resultHint = "", resultValue = "";
        try {

            if (dataParams != null && dataParams.size() > 0) {
                logger.debug("首单免/满减满送 -- 优惠条件明细保存 入参：{}",dataParams);
                int flag = actCondService.saveActCondDet(dataParams);
                if (flag > 0) {
                    successful = true;
                } else {
                    successful = false;
                    resultHint = "保存优惠内容失败";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 首单免/满减满送 -- 优惠条件明细删除
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/delActCondDet", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult delActCondDet(@RequestBody Map dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String actCondId = "", resultHint = "", resultValue = "";
        try {

            if (dataParams != null && dataParams.size() > 0) {
                int flag = actCondService.delActCondDet(dataParams);
                if (flag > 0) {
                    successful = true;
                } else {
                    successful = false;
                    resultHint = "删除优惠内容失败";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 首单免/满减满送 -- 新增优惠内容
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/addActDctProd")
    public @ItemResponseBody
    WrappedResult addActDctProd(@RequestParam("actDctProdObj") String actDctProdObj) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String resultHint = "ok";
        // 验证是否优惠内容是否重复
        if (StringUtil.isNotBlank(actDctProdObj)) {
            //优惠内容 - 单位
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("actDctProdObj", actDctProdObj);
            List<Map> prodInfoList = actCondService.setProdInfo(paramMap);
            if (prodInfoList != null && prodInfoList.size() > 0) {
                retMap.put("resultValue", prodInfoList);
            } else {
                successful = false;
                resultHint = "获取优惠内容异常";
            }
        } else {
            successful = false;
            resultHint = "获取优惠内容异常";
        }

        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 首单免/满减满送 -- 变更优惠内容
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/changeActDctProd")
    public @ItemResponseBody
    WrappedResult changeActDctProd(@RequestBody Map dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String resultHint = "ok";
        if (dataParams != null && dataParams.size() > 0) {
            String methodCode = dataParams.get("methodCode").toString();
            String actDctProdObj = dataParams.get("actDctProdObj").toString();
            String actDctProdJson = dataParams.get("actDctProdJson").toString();
            // 验证是否优惠内容是否重复
            if (StringUtil.isBlank(actDctProdObj)) {
                retMap.put("actDctProdObj", actDctProdJson);
            } else {
                if (actDctProdObj.indexOf(actDctProdJson) > -1) {
                    retMap.put("successful", false);
                    retMap.put("resultHint", "当前选择的优惠对象已存在，请重新选择");
                    return WrappedResult.successWrapedResult(retMap);
                } else {
                    retMap.put("actDctProdObj", actDctProdObj + "," + actDctProdJson);
                }
            }

            if (StringUtil.isNotBlank(methodCode)) {
                //优惠内容 - 单位
                List<CodeBO> actDctCondUnitList = codeService.getStandardCodes("tariffUnit", null);
                if (actDctCondUnitList != null && actDctCondUnitList.size() > 0) {
                    List<Map<String, String>> returnlist = new ArrayList<Map<String, String>>();
                    for (CodeBO codeBo : actDctCondUnitList) {
                        Map<String, String> map = new HashMap<String, String>();
                        if (methodCode.equals(codeBo.getCodeValue().substring(0, 2))) {
                            map.put("codeValue", codeBo.getCodeValue());
                            map.put("codeName", codeBo.getCodeName());
                            returnlist.add(map);
                        }
                    }
                    retMap.put("resultValue", returnlist);
                } else {
                    successful = false;
                    resultHint = "获取优惠单位标准代码[tariffUnit]异常";
                }
            } else {
                successful = false;
                resultHint = "获取优惠内容异常";
            }
        } else {
            successful = false;
            resultHint = "获取参数异常";
        }

        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 限时打折 --  主界面
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/limDisCountManage")
    public String limDisCount(Model m, @RequestParam("actId") String actId) throws Exception {
        MarketTypeBo marketTypeBo = new MarketTypeBo();
        marketTypeBo.setActId(actId);
        MarketTypeBo typeBo = actCondService.queryActCond(marketTypeBo);
        if (typeBo != null && StringUtil.isNotBlank(typeBo.getActDctId())) {
            marketTypeBo = typeBo;
        }
        marketTypeBo.setDctType("0201");
//        marketTypeBo.setActType(actType);

        // 优惠内容 - 优惠对象
        m.addAttribute("actDctProdIdList", actCondService.setProdInfo(new HashMap<String, String>()));


        m.addAttribute("limDisCountForm", marketTypeBo);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/limDisCountManage";
    }

    /**
     * 描述: 限时打折 --  优惠条件明细保存
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/limDisCountManage/saveActAll", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveActAll(@RequestBody Map dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String resultHint = "", resultValue = "";
        try {

            if (dataParams != null && dataParams.size() > 0) {
                int flag = actCondService.saveAll(dataParams);
                if (flag > 0) {
                    successful = true;
                } else {
                    successful = false;
                    resultHint = "保存优惠内容失败";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 描述: 限时打折 -- 获取优惠明细
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15/marketacts/limDisCountManage/queryActDctAll
     */
    @RequestMapping(value = "/marketacts/limDisCountManage/queryActAll")
    public @ItemResponseBody
    QueryResultObject queryActAll(@QueryRequestParam("params") RequestCondition params) throws Exception {
        MarketTypeBo condition = this.rCondition2QCondition(params);
        List<MarketTypeBo> list = actCondService.queryActAll(condition);
        if (StringUtil.isNotBlank(condition.getIsFromNewDetail()) && "1".equals(condition.getIsFromNewDetail())){
            for (MarketTypeBo marketTypeBo : list){
                marketTypeBo.setDctValue(marketTypeBo.getDctValue()+"折");
            }
        }
        return RestUtils.wrappQueryResult(list, list.size());
    }


    /**
     * 描述: 注册送/邀请送 --- 主界面
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/registerSendManage")
    public String registerSend(Model m, @RequestParam("actId") String actId) throws Exception {
        MarketTypeBo marketTypeBo = new MarketTypeBo();
        marketTypeBo.setActId(actId);
        MarketTypeBo typeBo = actCondService.queryActCond(marketTypeBo);
        logger.debug(">>>>>registerSendManage typeBo出参：{}", JsonUtils.toJson(typeBo));

        logger.debug(">>>>>registerSendManage typeBo != null出参：{},   {}", typeBo != null,typeBo.getActCondId());
        if (typeBo != null && typeBo.getActCondId()!= null) {
            marketTypeBo = typeBo;
        }
        marketTypeBo.setDctType("");
//        marketTypeBo.setActType(actType);
        logger.debug(">>>>>registerSendManage marketTypeBo出参：{}", JsonUtils.toJson(marketTypeBo));
        m.addAttribute("registerSendForm", marketTypeBo);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/registerSendManage";
    }

    /**
     * 描述: 首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [m]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/29 20:15
     */
    @RequestMapping(value = "/marketacts/registerSendManage/saveMktActGive", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveMktActGive(@RequestBody Map dataParams) throws Exception {
        logger.debug(">>>>>saveMktActGive  入参：{}",dataParams);
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String actCondId = "", resultHint = "", resultValue = "";
        try {

            if (dataParams != null && dataParams.size() > 0) {
                int flag = actCondService.saveMktActGive(dataParams);
                if (flag > 0) {
                    successful = true;
                } else {
                    successful = false;
                    resultHint = "保存优惠内容失败";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }
    /**
     * @param dataParams
     * @description 首单免/满减满送 -- 优惠条件明细删除
     * <AUTHOR>
     * @create 2017-08-09 10:48:05
     */
    @RequestMapping(value = "/marketacts/registerSendManage/delMktActGive", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult delMktActGive(@RequestBody Map dataParams) throws Exception {
        logger.debug(">>>>>/marketacts/registerSendManage/delMktActGive 入参：{}",dataParams);
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String actCondId = "", resultHint = "", resultValue = "";
        try {

            if (dataParams != null && dataParams.size() > 0) {
                int flag = actCondService.delMktActGive(dataParams);
                if (flag > 0) {
                    successful = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * @param dataParams
     * @description 保存活动站点关联表
     * <AUTHOR>
     * @create 2019-05-09 17:35:22
     */
    @RequestMapping(value = "/marketacts/firstFreeManage/saveActStation", method = RequestMethod.POST)
    public @ItemResponseBody WrappedResult saveXpcust(@RequestBody Map<String, Object> dataParams) throws Exception {
        try {
            Map<String, String> actMap = (Map<String, String>) dataParams.get("firstFreeForm");
            List<Map<String, Object>> itemList = (List<Map<String, Object>>) (dataParams.get("items"));
            List<Map<String, String>> saveList = new ArrayList<>();
            String flag = "true";
            if (CollectionUtils.isNotEmpty(itemList)) {
                StringBuilder stationIdSb = new StringBuilder();
                for (Map<String, Object> map : itemList) {
                    Map saveMap = new HashMap();
                    stationIdSb.append(StringUtil.nullToString(map.get("stationId"))).append(",");
                    saveMap.put("stationId", StringUtil.nullToString(map.get("stationId")));
                    saveMap.put("stationName", StringUtil.nullToString(map.get("stationName")));
                    saveMap.put("actId",actMap.get("actId"));
                    saveList.add(saveMap);
                }
                String stationIds = stationIdSb.toString();
                if(!StringUtils.nullOrBlank(stationIds)){
                    Map<String, Object> serchMap = new HashMap<>();
                    stationIds = stationIds.substring(0, stationIds.length() - 1);
                    serchMap.put("stationIds",stationIds.split(","));
                    serchMap.put("actId",actMap.get("actId"));
                    //查询站点是否已经存在
                    List<MarketTypeBo> actStationList = actCondService.qryActStationList(serchMap);
                    if (!CollectionUtils.isNotEmpty(actStationList)) {
                        actCondService.saveActStation(saveList);
                    } else {
                        flag = actStationList.get(0).getStationName()+"已经添加,不能重复添加!";//已经存在
                    }
                }
                }
            return WrappedResult.successWrapedResult(flag);

        } catch (Exception e) {
            logger.error("", e);
            return WrappedResult.failedWrappedResult("保存失败，请联系管理员：" + e.getMessage());
        }
    }

    /**
     * @param params
     * @description 查询活动和站点关联记录条数
     * <AUTHOR>
     * @create 2019-05-09 17:34:48
     */
    @RequestMapping("/marketacts/firstFreeManage/qryActStation")
    public @ItemResponseBody QueryResultObject qryActStation(@QueryRequestParam("params") RequestCondition params) {
        MarketTypeBo marketTypeBo = this.rCondition2QCondition(params);
        Map<String, Object> serchMap = new HashMap<>();
        serchMap.put("actId",marketTypeBo.getActId());
        List<MarketTypeBo> marketList = actCondService.qryActStationList(serchMap);
        int count = actCondService.qryActStationCount(serchMap);
        return RestUtils.wrappQueryResult(marketList, count);
    }

    /**
     * @param m
     * @param dataParams
     * @description 删除站点
     * <AUTHOR>
     * @create 2019-05-09 17:42:00
     */
    @RequestMapping("/marketacts/firstFreeManage/del")
    public
    @ResponseBody
    WrappedResult del(Model m, @RequestBody Map<String, Object> dataParams) throws Exception {
        try {
            actCondService.delActStation(dataParams);
            return WrappedResult.successWrapedResult(dataParams);
        } catch (Exception e) {
            logger.error("", e);
            return WrappedResult.failedWrappedResult("删除失败，请联系管理员：" + e.getMessage());
        }
    }

    @RequestMapping("/marketacts/firstFreeManage/actStationSelect")
    public String couponStationSelect(Model m,@RequestParam("params")String params) throws Exception{
        MarketCondition form=new MarketCondition();

        String accountName = authentication.getCurrentAccount().getAccountName();
        OrgBo orgBo = orgService.getOrgByAccountName(accountName);
        if (orgBo != null){
            form.setOrgCode(orgBo.getOrgCode());
            form.setOrgName(orgBo.getOrgShortName());
        }
        Map<String,String> info = JsonUtil.parseJsonToMap(params);
        String busiType=info.get("busiType");
        m.addAttribute("busiType",busiType);
        form.setBusiType(busiType);
        m.addAttribute("stationForm",form);
        return "/bil/mktact/actStationSelect";
    }

    /**
     * @param
     * @description 描述: 限时打折 -- 获取优惠明细
     * <AUTHOR>
     * @create 2019/6/11 9:48
     */
    @RequestMapping(value = "/marketAct/limDisCountManage/queryActAllNew")
    public @ItemResponseBody
    QueryResultObject queryActAllNew(@QueryRequestParam("params") RequestCondition params) throws Exception {
        MarketTypeBo condition = this.rCondition2QCondition(params);
        List<MarketTypeBo> list = actCondService.queryActAllNew(condition);
        if (StringUtil.isNotBlank(condition.getIsFromNewDetail()) && "1".equals(condition.getIsFromNewDetail())){
            for (MarketTypeBo marketTypeBo : list){
                marketTypeBo.setDctValue(marketTypeBo.getDctValue()+"折");
            }
        }
        return RestUtils.wrappQueryResult(list, list.size());
    }
    @Override
    protected MarketTypeBo initCondition() {
        return new MarketTypeBo();
    }
}
