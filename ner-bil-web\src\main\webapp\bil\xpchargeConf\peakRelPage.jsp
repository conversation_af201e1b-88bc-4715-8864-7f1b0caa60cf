<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="计费版本选择" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="searchUnPeakRelForm" name="searchUnPeakRelForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="管理单位" /></td>
				<td><ls:text imageKey="search" onClickImage="getOrgs" name="orgCodeName"  property="orgCodeName" enabled="false" onchanged="changeOrgName" /></td>
				<td><ls:label text="计费编号" /></td>
				<td><ls:text name="chcNo"/></td>
				<td><ls:label text="计费名称" /></td>
				<td><ls:text name="chcName"/></td>
			</tr>
			<tr>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
                <td>
                    <div class="pull-right">
                        <ls:button text="查询" onclick="doSearch" />
                        <ls:button text="清空" onclick="doClear" />
                    </div>
                </td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeUnPeakGrid" height="360px;" width="100%" showCheckBox="true" singleSelect="false" caption="计费版本选择列表" primaryKey="chcNo" >
					<ls:gridToolbar name="chargeUnPeakGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doSelect" text="选择"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="计费编号" name="chcNo" />
					<ls:column caption="计费名称" name="chcName" formatFunc="showDetail"/>
<%--					<ls:column caption="采集数据类型" name="gatherDataTypeName" />--%>
					<ls:column caption="计费模式" name="chargeModeName" />
					<ls:column caption="状态" name="chcStatusName"/>
					<ls:column caption="桩生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</ls:form>
	<ls:script>
        var astPath = '${astPath }';
        window.chargeUnPeakGrid = chargeUnPeakGrid;
        window.doSearch = doSearch;
        function doSearch(){
            var params = searchUnPeakRelForm.getFormData();
            chargeUnPeakGrid.query('~/billing/xpcbc/unpeakrellist',params,function(){});
        }

        function doSelect(){
            var item = chargeUnPeakGrid.getCheckedItems();
            var chcNos = '';
            for(var i=0;i< item.length;i++){
                chcNos+= "," + item[i].chcNo;
            }
            if(chcNos != ''){
                chcNos = chcNos.substr(1);
            }
            LS.ajax("~/billing/xpcbc/savepeakrel?chcNos="+chcNos,null,function(data){
                if(data == true){
                    LS.message("info","操作成功");
                    doSearch();
                    LS.parent().doSearchRel();
                    return;
                }else{
                    if(data){
                        LS.message("info","计费【" + data + "】选择失败，峰时时段无法与配置对应");
                        doSearch();
                        LS.parent().doSearchRel();
                        return;
                    }
                    LS.message("info","操作失败或网络异常，请重试！");
                    return;
                }
            });
<%--            LS.dialog("~/billing/xpcbc/newAddChargeBillingConf?chcNo=","新增充电计费配置",1000, 600,true, null);--%>
        }

        function showDetail(rowdata){
            return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowDetail(\"" + rowdata.chcNo +"\")'>"+rowdata.chcName+"</a>";
        }

        window.doShowDetail=doShowDetail;
        function doShowDetail(upChcNo){
            LS.dialog("~/billing/xpcbc/newShowChargeBillingConf?chcNo="+upChcNo,"查看充电费用配置",1000, 600,true, null);
        }

        function doClear(){
            orgCodeName.setValue();
            orgCode.setValue();
            chcNo.setValue();
            chcName.setValue();
        }

        function getOrgs(){
            var initValue = [];
            js_util.selectOrgTree(false, null, true, initValue, false, function(node){
                orgCodeName.setValue(node.text);
                orgCode.setValue(node.id);
            });
        }

        function changeOrgName(){
            if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
                orgCode.setValue();
            }
        }

        function opr(rowdata){
            if(rowdata.chcStatus=='2'){//草稿
                return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>"
                    +"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>删除</a>"
                    +"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doRelease(\"" + rowdata.chcNo + "\")'>发布</a>";
            }else if(rowdata.chcStatus=='0'){//无效
                return "";
            }else{//有效
                //var endTime = rowdata.eftDate.split(":");
                //var rowDate = endTime[0]+":"+endTime[1];
                //var current = (new Date()).Format("yyyy-MM-dd hh:mm");
                //if(rowDate > current){
                //	return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>";
                //}else{
                return "";
                //}
            }
        }

        //====================================充电站查询=============================================================
		function init(){
			chargeUnPeakGrid.toolbarItems.sendBarItem.setEnabled(false);
		}

        window.onload = function() {
                initGridHeight('searchUnPeakRelForm','chargeUnPeakGrid');
                <%--init();--%>
				doSearch();
            }
		</ls:script>
</ls:body>
</html>