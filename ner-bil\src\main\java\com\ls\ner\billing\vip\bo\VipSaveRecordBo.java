package com.ls.ner.billing.vip.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员折扣记录
 * <AUTHOR>
 * @date 2025/1/7 15:36
 */
public class VipSaveRecordBo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 会员折扣记录id
     */
    private String recordId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 现金券金额
     */
    private BigDecimal cashAmt;

    /**
     * 折扣券金额
     */
    private BigDecimal discountAmt;

    /**
     * 会员折扣
     */
    private BigDecimal vipDiscount;

    /**
     * 会员日折上折
     */
    private BigDecimal vipDiscountUp;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付日期
     */
    private String payMonth;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getCashAmt() {
        return cashAmt;
    }

    public void setCashAmt(BigDecimal cashAmt) {
        this.cashAmt = cashAmt;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }

    public BigDecimal getVipDiscount() {
        return vipDiscount;
    }

    public void setVipDiscount(BigDecimal vipDiscount) {
        this.vipDiscount = vipDiscount;
    }

    public BigDecimal getVipDiscountUp() {
        return vipDiscountUp;
    }

    public void setVipDiscountUp(BigDecimal vipDiscountUp) {
        this.vipDiscountUp = vipDiscountUp;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getPayMonth() {
        return payMonth;
    }

    public void setPayMonth(String payMonth) {
        this.payMonth = payMonth;
    }
}
