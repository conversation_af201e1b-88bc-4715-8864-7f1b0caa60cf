<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="修改积分规则" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script src="<%=request.getContextPath()%>/bil/comm/js/DateUtils.js" type="text/javascript"></script>

<ls:body>
	<ls:form name="ruleForm" id="ruleForm" enctype="multipart/form-data">
		<ls:text name="ruleId" property="ruleId" visible="false"></ls:text>
		<ls:text name="ruleStatus" property="ruleStatus" visible="false"></ls:text>
		<ls:text name="eventType" property="eventType" visible="false"></ls:text>
		<ls:text name="userTypeListStr" property="userTypeListStr" visible="false"></ls:text>
		<ls:text name="handlyType" property="handlyType" visible="false"></ls:text>
		
		<ls:text name="maxTimes" property="maxTimes" visible="false"></ls:text>
		<ls:layout-use>
			<ls:layout-put into="container">
				<table align="center" class="tab_search">
					<colgroup>
						<col width="15%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="10%" />
						<col width="15%" />
					</colgroup>
					<tr>
						<td/>
						<td>
							<ls:label text="积分规则名称" ref="ruleName"/>
						</td>
						<td colspan="2">
							<ls:text name="ruleName" property="ruleName" required="true" readOnly="true"></ls:text>
						</td>
						<td>
							<ls:label text="触发事件" ref="eventType"/>
						</td>
						<td colspan="2">
							<ls:select name="eventType" required="true" property="eventType" noClear="true" onchanged="changeEventType" readOnly="true">
								<ls:options property="eventTypeList" scope="request" text="codeName" value="codeValue" />
							</ls:select>
						</td>
						<td/>
					</tr>
					<tr id="eventType01">
						<td/>
						<td>
							<ls:label text="发放数额" ref="money1"/>
						</td>
						<td colspan="2">
							<ls:text name="money1" property="money" required="true" hint="积分"  type="number" formatType="money" ></ls:text>
						</td>
						<td ><ls:label text="积分" textAlign="left"/></td>
						<td colspan="4">
					</tr>
					<tr style="display:none"  id="eventType02">
						<td/>
						<td>
							<ls:label text="区分身份" ref="isDiscriminateUser"/>
						</td>
						<td colspan="3">
							<ls:checklist type="radio" name="isDiscriminateUser" property="isDiscriminateUser" onchanged="chargeUser" required="true">
								<ls:checkitem value="0" text="否"></ls:checkitem>
								<ls:checkitem value="1" text="是"></ls:checkitem>
							</ls:checklist>
						</td>
						<td colspan="4"/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser02">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName" textAlign="center"/>
									</td>
									<td><ls:label text="发放数额（积分）" ref="money0201" textAlign="center"/></td>
									<ls:text name="money" property="money" visible="false"></ls:text>
									<td><ls:label text="上限次数（次）" ref="maxTimes0201" textAlign="center"/></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName" property="userTypeName" value="普通身份" style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType" property="userType" visible="false" value="0"></ls:text>
									</td>
									<td>
										<ls:text name="money0201" property="money" required="true" style="text_align:center"></ls:text>
										<ls:text name="money" property="money" visible="false"></ls:text>
									</td>
									<td>
										<ls:text name="maxTimes0201" property="maxTimes" required="true" style="text_align:center"></ls:text>
									</td>
								</tr>
							</table>
<%-- 							<ls:grid url="" name="userTypeList" showRowNumber="false" primaryKey="userType" --%>
<%-- 	                             showCheckBox="false" cellEdit="true" whith = "60%"> --%>
<%-- 			                    <ls:column caption="身份" name="userType" hidden="true"/> --%>
<%-- 			                    <ls:column caption="身份" name="userTypeName" readOnly="true" align="center" width="20%"/> --%>
<%-- 			                    <ls:column caption="发放数额（积分）" name="money" readOnly="false" align="center" width="20%" > --%>
<%-- 			                    <ls:textEditor name="money" imageKey="money" ></ls:column> --%>
<%-- 			                    <ls:column caption="上限次数（次）" name="maxTimes" readOnly="false" align="center" width="20%"> --%>
<%-- 			                    <ls:textEditor name="maxTimes" imageKey="maxTimes" ></ls:column> --%>
<%-- 			                    <ls:pager pageSize="2"></ls:pager> --%>
<%-- 			                </ls:grid> --%>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser04">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName1" textAlign="center"/>
									</td>
									<td><ls:label text="发放  1  积分需充电" ref="chargingPq1" textAlign="center"/></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName1" property="userTypeName" value="普通身份" style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType1" property="userType" visible="false" value="0"></ls:text>
									</td>
									<td>
										<ls:text name="chargingPq1" property="chargingPq" style="text_align:center" required="true" ></ls:text>
									</td>
								</tr>
							</table>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="isDiscriminateUser05">
						<td/>
						<td colspan="7">
							<table  class="tab_search">
								<tr>
									<td>
										<ls:label text="身份" ref="userTypeName2" textAlign="center"/>
									</td>
									<td><ls:label text="最低赠送积分（积分）" ref="money0501" textAlign="center"/></td>
									<td><ls:label text="最高赠送积分（积分）" ref="money0502" textAlign="center"/></td>
								</tr>
								<tr>
									<td>
										<ls:text name="userTypeName2" property="userTypeName" value="普通身份" style="text_align:center" readOnly="true">
										</ls:text>
										<ls:text name="userType2" property="userType" visible="false" value="0"></ls:text>
									</td>
									<td>
										<ls:text name="money0501" property="minMoney" required="true" style="text_align:center" ></ls:text>
									</td>
									<td>
										<ls:text name="money0502" property="maxMoney" required="true" style="text_align:center" ></ls:text>
									</td>
								</tr>
							</table>
			            </td>
			            <td/>
					</tr>
					<tr style="display:none"  id="eventType03">
						<td/>
						<td>
							<ls:label text="发放数额" ref="money2"/>
						</td>
						<td colspan="2">
							<ls:text name="money2" property="money" required="true" hint="积分" ></ls:text>
							<ls:text name="money" property="money" visible="false"></ls:text>
						</td>
						<td><ls:label text="积分" textAlign="left"/></td>
						<td>
							<ls:label text="上限次数" ref="maxTimes2"/>
						</td>
						<td colspan="2">
							<ls:text name="maxTimes2" property="maxTimes" required="true" hint="次" ></ls:text>
						</td>
						<td ><ls:label text="次" textAlign="left"/></td>
					</tr>
					<tr style="display:none"  id="eventType04">
						<td/>
						<td  colspan="1">
							<ls:label text="发放  1  积分需充电" ref="chargingPq"/>
						</td>
						<td colspan="2">
							<ls:text name="chargingPq" property="chargingPq" required="true" ></ls:text>
						</td>
						<td ><ls:label text="kWh" textAlign="left"/></td>
						<td colspan="4"/>
					</tr>
					<tr style="display:none"  id="eventType05">

						<td/>
						<td>
							<ls:label text="最低赠送积分" ref="money501"/>
						</td>
						<td colspan="2">
							<ls:text name="money501" property="minMoney" required="true" ></ls:text>
						</td>
						<td><ls:label text="积分" textAlign="left"/></td>


						<td>
							<ls:label text="最高赠送积分" ref="money502"/>
						</td>
						<td colspan="2">
							<ls:text name="money502" property="maxMoney" required="true" ></ls:text>
						</td>
						<td><ls:label text="积分" textAlign="left"/></td>
	                  <td/>
					</tr>
				</table>
			</ls:layout-put>
			<ls:layout-put into="footer">
				<div class="pull-right">
					<ls:button text="确认" onclick="save"></ls:button>
					<ls:button text="取消" onclick="cancel"></ls:button>
				</div>
			</ls:layout-put>
		</ls:layout-use>
	</ls:form>
	<ls:script>
	
		window.onload = function(){
			chargeUser();
		}
	
		function chargeUser(){
			changeEventType();
		}
		
		function changeEventType(){
			if(eventType.getValue() == "01"){
				$("#eventType01").show();
				$("#eventType02").hide();
				$("#eventType03").hide();
				$("#eventType04").hide();
				$("#eventType05").hide();
			}else if(eventType.getValue() == "02"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").show();
				$("#eventType04").hide();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").show();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
					$("#eventType03").hide();
				}
			}else if(eventType.getValue() == "03"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").show();
				$("#eventType04").hide();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").show();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
					$("#eventType03").hide();
				}
			}else if(eventType.getValue() == "04"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").hide();
				$("#eventType04").show();
				$("#eventType05").hide();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").show();
					$("#isDiscriminateUser05").hide();
					$("#eventType04").hide();
				}
			}else if(eventType.getValue() == "05"){
				$("#eventType01").hide();
				$("#eventType02").show();
				$("#eventType03").hide();
				$("#eventType04").hide();
				$("#eventType05").show();
				if(isDiscriminateUser.getValue() == "0"){
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").hide();
				} else {
					$("#isDiscriminateUser02").hide();
					$("#isDiscriminateUser04").hide();
					$("#isDiscriminateUser05").show();
					$("#eventType05").hide();
				}
			}
		}
		
		
		function save(){
			var data = {};
			var userTypeData = {};
			if(eventType.getValue() != "01"){
				if(isDiscriminateUser.getValue() == "1"){
					userTypeData.userType = userType.getValue();
					if(eventType.getValue() == "02"){
						if(parseFloat(money0201.getValue()) < 0){
							LS.message("info","发放数额必须为正整数！");
							return false;
						}
						if(parseFloat(maxTimes0201.getValue()) < 0){
							LS.message("info","上限次数必须为正整数！");
							return false;
						}
						userTypeData.money = money0201.getValue();
						userTypeData.maxTimes = maxTimes0201.getValue();
						data.money = money0201.getValue();
						data.maxTimes = maxTimes0201.getValue();
					}else if(eventType.getValue() == "03"){
						if(parseFloat(money0201.getValue()) < 0){
							LS.message("info","发放数额必须为正整数！");
							return false;
						}
						if(parseFloat(maxTimes0201.getValue()) < 0){
							LS.message("info","上限次数必须为正整数！");
							return false;
						}
						userTypeData.money = money0201.getValue();
						userTypeData.maxTimes = maxTimes0201.getValue();
						data.money = money0201.getValue();
						data.maxTimes = maxTimes0201.getValue();
					}else if(eventType.getValue() == "04"){
						if(parseFloat(chargingPq1.getValue()) < 0){
							LS.message("info","充电量必须为正整数！");
							return false;
						}
						userTypeData.chargingPq = chargingPq1.getValue();
						data.chargingPq = chargingPq1.getValue();
					}else if(eventType.getValue() == "05"){
						if(parseFloat(money0501.getValue()) < 0){
							LS.message("info","发放数额必须为正整数！");
							return false;
						}
<%--						userTypeData.money = money0501.getValue();--%>
<%--						data.money = money0501.getValue();--%>

						if(parseFloat(money0501.getValue()) < 0){
						LS.message("info","发放数额必须为正整数！");
						return false;
						}
						if(parseFloat(money0502.getValue()) < 0){
						LS.message("info","发放数额必须为正整数！");
						return false;
						}

						if(parseFloat(money0502.getValue()) < parseFloat(money0501.getValue())){
						LS.message("info","最大发放数额不得小于最小发放数额！");
						return false;
						}

						data.extendJson = {};
						data.extendJson.minMoney = money0501.getValue();
						data.extendJson.maxMoney = money0502.getValue();
						userTypeData.extendJson = data.extendJson;
					}
				}else{
					if(eventType.getValue() == "02"){
						if(parseFloat(money2.getValue()) < 0){
							LS.message("info","发放数额必须为正整数！");
							return false;
						}
						if(parseFloat(maxTimes2.getValue()) < 0){
							LS.message("info","上限次数必须为正整数！");
							return false;
						}
						data.money = money2.getValue();
						data.maxTimes = maxTimes2.getValue();
					}else if(eventType.getValue() == "03"){
						if(parseFloat(money2.getValue()) < 0){
							LS.message("info","发放数额必须为正整数！");
							return false;
						}
						if(parseFloat(maxTimes2.getValue()) < 0){
							LS.message("info","上限次数必须为正整数！");
							return false;
						}
						data.money = money2.getValue();
						data.maxTimes = maxTimes2.getValue();
					}else if(eventType.getValue() == "04"){
						if(parseFloat(chargingPq.getValue()) < 0){
							LS.message("info","充电量必须为正整数！");
							return false;
						}
						data.chargingPq = chargingPq.getValue();
					}else if(eventType.getValue() == "05"){
<%--						if(parseFloat(money5.getValue()) < 0){--%>
<%--							LS.message("info","发放数额必须为正整数！");--%>
<%--							return false;--%>
<%--						}--%>
<%--						data.money = money5.getValue();--%>

						if(parseFloat(money501.getValue()) < 0){
						LS.message("info","发放数额必须为正整数！");
						return false;
						}
						if(parseFloat(money502.getValue()) < 0){
						LS.message("info","发放数额必须为正整数！");
						return false;
						}
						if(parseFloat(money502.getValue()) < parseFloat(money501.getValue())){
						LS.message("info","最大发放数额不得小于最小发放数额！");
						return false;
						}
						data.extendJson = {};
						data.extendJson.minMoney = money501.getValue();
						data.extendJson.maxMoney = money502.getValue();
					}
				}
			} else {
				if(parseFloat(money1.getValue()) < 0){
					LS.message("info","发放数额必须为正整数！");
					return false;
				}
				data.money = money1.getValue();;
			}
			data.userTypeListStr = JSON.stringify(userTypeData);
		if(data.extendJson){
		data.extendJson = JSON.stringify(data.extendJson);
		}
			data.ruleId = ruleId.getValue();
			data.ruleName = ruleName.getValue();
			data.eventType = eventType.getValue();
			data.ruleStatus = ruleStatus.getValue();
			data.userType = userType.getValue();
			data.isDiscriminateUser = isDiscriminateUser.getValue();
			LS.ajax("~/billing/integralRule/saveRule",data,function(result){
   		 		if(result.items[0].successful==true){
	   		 		LS.message("info","保存成功");
	   		 		LS.parent().query();
	   		 		LS.window.close();
	   		 	}else{
	   		 		LS.message("error",result.items[0].resultValue);
	   		 	}
		    });
		}
		
		
		function cancel(){
			LS.window.close();
		}
		
	</ls:script>
</ls:body>
</html>