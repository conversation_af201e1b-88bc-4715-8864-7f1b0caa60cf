<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型定价" />
<ls:body>
		<ls:form id="queryForm" name="queryForm">
				<ls:title text="查询条件 "></ls:title>
				<table class="tab_search">
						<tr>
						    <td>
                    <ls:label text="管理单位" />
                </td>
                <td>
                    <ls:text type="hidden" name="orgCode" property="orgCode"/>
                    <ls:text imageKey="search" onClickImage="getOrg" name="orgCodeName"  property="orgCodeName" enabled="false" onchanged="changeOrgName" />
                </td>
								<td>
										<ls:label text="车型" />
								</td>
								<td>
								    <ls:text name="autoModelNo" type="hidden"></ls:text>
                    <ls:text imageKey="search" onClickImage="getModelRental" name="autoModelName" enabled="false" onchanged="changeAutoModelName"></ls:text>
										<%-- 考虑用选择页面或是auto complete 空间 --%>
										<%-- <ls:text name="autoModelNo" required="true"></ls:text>
										<ls:text name="autoModelName" required="true"></ls:text> --%>
								</td>						
								<td>
										<ls:label text="租赁方式" />
								</td>
								<td>
										<ls:select name="subBe">
												<ls:options property="rentTypeList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
								<%-- <td>
								TODO 如果有选择租赁点，这里变得不可用
										<ls:label text="是否统一定价" />
								</td>
								<td>
										<ls:select name="unionPrice">
												<ls:options property="ynJudgeFlagList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>		 --%>							
								<td colspan="2">
										<div class="pull-right">
												<ls:button text="清空" onclick="doClear" />
												<ls:button text="查询" onclick="doSearch" />
										</div>
								</td>
						</tr>
				</table>
		</ls:form>
		<table align="center" class="tab_search">
				<tr>
						<td>
								<ls:grid height="340px" width="100%" url="" name="billingConfigGrid" primaryKey="billingNo" showCheckBox="true" singleSelect="true">
										<ls:gridToolbar name="code_grid_bar">
												<ls:gridToolbarItem name="addBillingConfigBtn" text="新增" imageKey="add" onclick="addBillingConfig" />
												<ls:gridToolbarItem name="editBillingConfigBtn" text="修改" imageKey="edit" onclick="editBillingConfig" />
												<ls:gridToolbarItem name="deleteBillingConfigBtn" text="删除" imageKey="delete" onclick="deleteBillingConfig" />
                        <ls:gridToolbarItem name="showBillingConfigBtn" text="详情" imageKey="look" onclick="showBillingConfig" />
                        <ls:gridToolbarItem name="copyBillingConfigBtn" text="复制" imageKey="copy" onclick="copyBillingConfig" />
												<%-- <ls:gridToolbarItem name="adjustBillingConfig" text="调整价格" imageKey="start" onclick="adjustBillingConfig" /> --%>
										</ls:gridToolbar>
										<ls:column caption="管理单位" name="operOrgCode" />
										<ls:column caption="定价编码" name="billingNo" />
										<ls:column caption="定价方案" name="billingConfigName" />
										<ls:column caption="资费模版" name="planName" />
										<ls:column caption="车型名称" name="autoModelName" />
										<ls:column caption="租赁方式" name="subBeName" />
                    <ls:column caption="计费方式" name="chargeWayName" />
                    <ls:column caption="计费模式" name="chargeModeName" />
										<%-- <ls:column caption="是否统一定价" name="unionPriceName" /> --%>
										<ls:column caption="生效时间" name="eftDate"/>
										<%-- <ls:column caption="是否最新版本" name="isLatestVersionName"/> --%>
										<ls:pager pageSize="15"/>
								</ls:grid>
						</td>
				</tr> 
		</table>
		<ls:script>
		var baseUrl = '~/billing/configs/getBillingConfigList';
		window.doSearch = doSearch;
		doSearch();
	    
		function doSearch(){
			var params = queryForm.getFormData();
			billingConfigGrid.query(baseUrl,params);
		}
		
	  function doClear(){
	    queryForm.clear();
	    autoModelNo.setValue();
	    autoModelName.setValue();
	    orgCode.setValue();
      orgCodeName.setValue();
	  }
    billingConfigGrid.itemclick = function(){
        var item = billingConfigGrid.getSelectedItem();
        if(!item){
          return;
        }
        var isLatestVersionValue = item.isLatestVersion;
        var eftDateValue = item.eftDate;
        var sysdate = nowDateFormat("yyyy-MM-dd hh:mm");
        if(isLatestVersionValue=="1"&&eftDateValue>sysdate){
          billingConfigGrid.toolbarItems.editBillingConfigBtn.setEnabled(true);
          billingConfigGrid.toolbarItems.deleteBillingConfigBtn.setEnabled(true);
        }else{
          billingConfigGrid.toolbarItems.editBillingConfigBtn.setEnabled(false);
          billingConfigGrid.toolbarItems.deleteBillingConfigBtn.setEnabled(false);
        }
    }
    function addBillingConfig(){
       LS.dialog("~/billing/configs/initAdd","车型定价新增",1000,600,true);
       //window.location.href=rootUrl+"billing/configs/initAdd";
    }
    
    function editBillingConfig(){
    	var item = billingConfigGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.dialog("~/billing/configs/initEdit/"+item[0],"车型定价修改",1000,600,true);
          //window.location.href=rootUrl+"billing/configs/edit/"+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function deleteBillingConfig(){
      var items = billingConfigGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var billingNos = [];
      for(var i=0;i < items.length;i++){
        var isLatestVersionValue = items[0].isLatestVersion;
        var eftDateValue = items[0].eftDate;
        var sysdate = nowDateFormat("yyyy-MM-dd hh:mm");
        if(!(isLatestVersionValue=="1"&&eftDateValue>sysdate)){
          LS.message('info','只有最新版本且生效日期大于当前日期的定价才可以删除，请重新选择');
          return;
        } else {
          billingNos.push(items[i].billingNo);
        }
      }
      LS.confirm('确认删除吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = billingNos;
              LS.ajax("~/billing/configs/delete", params, function(result) {
                LS.message("info","删除成功");
                doSearch();
              });
          }
      });
    }
    
    function showBillingConfig(){
      var item = billingConfigGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.dialog("~/billing/configs/show/"+item[0],"车型定价详情",1000,600,true);
          //window.location.href=rootUrl+"billing/configs/show/"+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    function copyBillingConfig(){
      var item = billingConfigGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.dialog("~/billing/configs/initCopy?billingNo="+item[0],"请选择生效日期",300, 150,true);
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    function getModelRental(){
      	LS.dialog("<%=astPath %>"+"/ast/brand/init?flag=1","选择车型",1000,500,true);
    }
    function changeAutoModelName(){
      if(LS.isEmpty(autoModelName.getValue())){
        autoModelNo.setValue();
      }
    }
    window.setModelRental = setModelRental;
    function setModelRental(item) {
       autoModelNo.setValue(item.modelId);  
       autoModelName.setValue(item.modelName);
       //subBe.setValue(item.subBe);
    }
    function getOrg(){
      var initValue=null;
      if(!LS.isEmpty(orgCode.getValue())){
        initValue=orgCode.getValue().split(",");
      }
      js_util.selectOrgTree(true, null, true, initValue, false, setOrg);
    }
    function setOrg(node){
      if(node==null){
        return;
      }
      var orgCodes="";
      var orgCodeNames="";
      if(node.length==undefined){
        orgCodes=node.id;
        orgCodeNames=node.text;
      }else{
        for(var i=0;i< node.length;i++){
          if(node.length==i+1){
            orgCodes+=node[i].id;
            orgCodeNames+=node[i].text;
          }else{
            orgCodes+=node[i].id+',';
            orgCodeNames+=node[i].text+',';
          }
        }
      }
      orgCode.setValue(orgCodes);
      orgCodeName.setValue(orgCodeNames);
    }
    function changeOrgName(){
      if(LS.isEmpty(orgCodeName.getValue())){
         orgCode.setValue();
      }
    }
    
    function nowDateFormat(fmt){
      var d = new Date();
      var o = {
	        "M+": d.getMonth() + 1, //月份 
	        "d+": d.getDate(), //日 
	        "h+": d.getHours(), //小时 
	        "m+": d.getMinutes(), //分 
	        "s+": d.getSeconds(), //秒 
	        "q+": Math.floor((d.getMonth() + 3) / 3), //季度 
	        "S": d.getMilliseconds() //毫秒 
	    };
	    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
	    for (var k in o)
	     if (new RegExp("(" + k + ")").test(fmt)) 
	     fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	    return fmt;
    }
    </ls:script>
</ls:body>
</html>