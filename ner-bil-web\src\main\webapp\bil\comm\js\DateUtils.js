Date.prototype.Format = function(formatStr)   
	{   
	    var str = formatStr;   
	    var Week = ['日','一','二','三','四','五','六'];  
	  
	    str=str.replace(/yyyy|YYYY/,this.getFullYear());   
	    str=str.replace(/yy|YY/,(this.getYear() % 100)>9?(this.getYear() % 100).toString():'0' + (this.getYear() % 100));   
	    
	    str=str.replace(/MM/,(this.getMonth()+1)>9?(this.getMonth()+1).toString():'0' + (this.getMonth()+1));   
	    str=str.replace(/M/g,this.getMonth());   
	  
	    str=str.replace(/w|W/g,Week[this.getDay()]);   
	  
	    str=str.replace(/dd|DD/,this.getDate()>9?this.getDate().toString():'0' + this.getDate());   
	    str=str.replace(/d|D/g,this.getDate());   
	  
	    str=str.replace(/hh|HH/,this.getHours()>9?this.getHours().toString():'0' + this.getHours());   
	    str=str.replace(/h|H/g,this.getHours());   
	    str=str.replace(/mm/,this.getMinutes()>9?this.getMinutes().toString():'0' + this.getMinutes());   
	    str=str.replace(/m/g,this.getMinutes());   
	  
	    str=str.replace(/ss|SS/,this.getSeconds()>9?this.getSeconds().toString():'0' + this.getSeconds());   
	    str=str.replace(/s|S/g,this.getSeconds());   
	    return str;   
};


var DateUtils = {
	getTime : function(format) {
		return new Date().Format(format);
	},
	getLastDay : function(format,n,dateStr) {
		n = parseInt(n);
		var date;
		if(dateStr) {
			date = this.parse(dateStr);
		}else {
			date = new Date();
		}
	    return new Date(date.getTime()-1000*60*60*24*(n)).Format(format);
	},
	addMinute : function(m, format, dateStr) {
		var date = null;
		if(dateStr) {
			date = this.parse(dateStr);
		}else {
			date = new Date();
		}
		m = parseInt(m);
		return new Date(date.getTime()+1000*60*m).Format(format);
	},
	getLastMonth : function(targetFormat,dateStr,format) {
		if(format==null) {
			format = 'yyyy-MM';
		}
		if(dateStr==null) {
			dateStr = new Date().Format(format);
		}
		var year =dateStr.substring(0,4);            //获取当前日期的年
	    var month = dateStr.substring(5,7);              //获取当前日期的月
	 
	    var year2 = year;
	    var month2 = parseInt(month)-1;
	    if(month2==0) {
	        year2 = parseInt(year2)-1;
	        month2 = 12;
	    }
	    
	    if(month2<10) {
	        month2 = '0'+month2;
	    }
	    var m = year2.toString();
	    var n= month2.toString();
	    var t2 = m+'-'+n;
	    return this.parse(t2).Format(targetFormat || 'yyyy-MM');
	},
  getNextMonth : function(targetFormat,dateStr,format) {
    if(format==null) {
      format = 'yyyy-MM';
    }
    if(dateStr==null) {
      dateStr = new Date().Format(format);
    }
    var year =dateStr.substring(0,4);            //获取当前日期的年
    var month = dateStr.substring(4,6);              //获取当前日期的月

    var year2 = year;
    var month2 = parseInt(month)+1;
    if(month2==13) {
      year2 = parseInt(year2)+1;
      month2 = 1;
    }

    if(month2<10) {
      month2 = '0'+month2;
    }
    var m = year2.toString();
    var n= month2.toString();
    var t2 = m+'-'+n;
    return this.parse(t2).Format(targetFormat || 'yyyy-MM');
  },
	parse : function(dateStr) {
		dateStr = dateStr.replace(/-/g,"/");
		if(dateStr.indexOf('/')==-1) {
			var length = dateStr.length;
			dateStr = dateStr.substr(0,4)+(length>=6 ? '/'+dateStr.substr(4,2):'/01')+(length==8?'/'+dateStr.substr(6,2):'/01');
		}
		if(dateStr.split('/').length==2) {
            dateStr += '/01';
		}
		return new Date(dateStr);
	},
	getMonthLastDay : function(dateStr,format) {
		if(format==null) {
			format = 'yyyy-MM-dd';
		}
		var date = this.parse(dateStr);
		var new_year = date.getFullYear();  //取当前的年份   
		var month = date.getMonth()+1;
		 var new_month = month++;//取下一个月的第一天，方便计算（最后一天不固定）   
		 if(month>12)      //如果当前大于12月，则年份转到下一年   
		 {   
		 new_month -=12;    //月份减   
		 new_year++;      //年份增   
		 }   
		 var new_date = new Date(new_year,new_month,1); 
		 return (new Date(new_date.getTime()-1000*60*60*24)).Format(format);
	},
    getWeekFirstDay : function(dateStr,format) {
        if(format==null) {
            format = 'yyyy-MM-dd';
        }
        var date = this.parse(dateStr);
        var day = date.getDay() || 7;
        var oneDayLong = 24*60*60*1000 ;
        return (new Date(date.getTime()-(day-1)*oneDayLong)).Format(format);
    },
    getWeekLastDay: function(dateStr,format) {
        if(format==null) {
            format = 'yyyy-MM-dd';
        }
        var date = this.parse(dateStr);
        var day = date.getDay() || 7;
        var oneDayLong = 24*60*60*1000 ;
        return (new Date(date.getTime()+(7-day)*oneDayLong)).Format(format);
    }
}
	