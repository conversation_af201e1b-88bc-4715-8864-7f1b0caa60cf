package com.ls.ner.billing.vip.bo;

import java.io.Serializable;
import java.util.Date;

public class VipLevelCalcConfig implements Serializable {
    private Integer id;
    private String calcMode; // amount/quantity
    private java.math.BigDecimal amountPerKwh; // 元/kWh
    private java.math.BigDecimal kwhPerAmount; // kWh/元
    private Date updateTime;

    // getter/setter
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    public String getCalcMode() { return calcMode; }
    public void setCalcMode(String calcMode) { this.calcMode = calcMode; }
    public java.math.BigDecimal getAmountPerKwh() { return amountPerKwh; }
    public void setAmountPerKwh(java.math.BigDecimal amountPerKwh) { this.amountPerKwh = amountPerKwh; }
    public java.math.BigDecimal getKwhPerAmount() { return kwhPerAmount; }
    public void setKwhPerAmount(java.math.BigDecimal kwhPerAmount) { this.kwhPerAmount = kwhPerAmount; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
} 