<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="新增">
</ls:head>
<ls:body>
            <ls:form id="addCardForm" name="addCardForm">
                <table class="tab_search">
                    <tr>
                        <td><ls:label text="充值卡批次号" ref="cardBatchNo"/></td>
                        <td><ls:text name="cardBatchNo" property="cardBatchNo" hint="自动生成" readOnly="true"></ls:text></td>
                    </tr>
                    <tr>
                        <td><ls:label text="充值卡名称" ref="cardName"/></td>
                        <td><ls:text name="cardName" property="cardName" required="true"></ls:text></td>
                        <td><ls:label text="实际面值" ref="actualAmt"/></td>
                        <td><ls:text name="actualAmt" property="actualAmt" required="true"></ls:text></td>
                        <td><ls:label text="赠送面值" ref="giftAmt"/></td>
                        <td><ls:text name="giftAmt" property="giftAmt" required="true"></ls:text></td>
                    </tr>
                    <tr>
                        <td><ls:label text="备注" ref="remark"/></td>
                        <td colspan="5"><ls:text  name="remark" property="remark"  type="textarea" maxlength="200"></ls:text></td>
                    </tr>
                    <tr>
                        <td><ls:label text="制卡数量" ref="cardCount"/></td>
                        <td><ls:text name="cardCount" property="cardCount" required="true"></ls:text></td>
                    </tr>
                    <tr>
                        <td><ls:label text="每张赠送积分：" ref="integralNum"/></td>
                        <td colspan="3" style="white-space: nowrap;">
                          <div style="display: flex; align-items: center;">
                            <ls:text name="integralNum" property="integralNum" required="false" type="number" min="0" style="width: 33%;" />
                            <span style="margin-left: 2px;">积分</span>
                          </div>
                          <div style="color: #888; font-size: 12px;">填0或留空为不赠送</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <ls:label text="兑换有效期" ref="cardTimeType"/>
                        </td>
                        <td>
                            <ls:checklist type="radio" name="cardTimeType" property="cardTimeType"  required="true">
                                <ls:checkitem value="1" text="永久有效"></ls:checkitem>
                                <ls:checkitem value="2" text="限时有效"></ls:checkitem>
                            </ls:checklist>
                        </td>
                        <td colspan="4">
                            <table>
                                <tr id="dateType01">
                                    <td >
                                        <ls:date name="eftDate" property="eftDate" format="yyyy-MM-dd HH:mm:ss" ></ls:date>
                                    </td>
                                    <td >
                                        <ls:label text="至" ref="invDate"/>
                                    </td>
                                    <td >
                                        <ls:date name="invDate"  property="invDate" format="yyyy-MM-dd HH:mm:ss" ></ls:date>
                                    </td>
                                </tr>
                            </table>
                        </td>


                    </tr>

                </table>
            </ls:form>
            <table>
                <tr>
                    <td>
                        <div class="pull-right">
                            <ls:button text="保存" onclick="save" name="save"/>
                            <ls:button text="取消" onclick="close" />
                        </div>
                    </td>
                </tr>
            </table>

    <ls:script>

        function save(){
            addCardForm.submit("~/bil/rechargeCard/save",function(e){
                if(e.cardId != null && e.cardId != ''){
                    LS.message("info","保存成功");
                    LS.parent().doSearch();
                    LS.window.close();
                }else{
                    LS.message("info","保存失败");
                }
            });
        }

        function close(){
            LS.window.close();
        }


    </ls:script>
</ls:body>
</html>