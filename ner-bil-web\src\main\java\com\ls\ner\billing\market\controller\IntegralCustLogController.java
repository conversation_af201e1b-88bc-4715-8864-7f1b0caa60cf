package com.ls.ner.billing.market.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.bo.IntegeralCustLogBo;
import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.service.IIntegeralCustService;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 描述:用户积分流水记录
 * 创建人:nijiajing
 * 创建时间: 2024-04-10 12:08
 */
@Controller
@RequestMapping("/billing/integralCustLog")
public class IntegralCustLogController extends QueryController<IntegeralCustLogBo>{

	private static final Logger log = LoggerFactory.getLogger(IntegralCustLogController.class);
	
	@ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(value = "IntegeralCustService", serviceTypes = ServiceType.LOCAL)
    private IIntegeralCustService IntegeralCustService;

    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;
	
    /**
     * 初始化用户积分流水记录界面
     * @param m
     * @return
     */
    @RequestMapping("/init")
    public String init(Model m, HttpServletRequest request) {
    	List<CodeBO> integralChargeTypeList = codeService.getStandardCodes("integralChargeType", null);
        m.addAttribute("chargeTypeList", integralChargeTypeList);
        IntegeralCustLogBo bo = initCondition();
        bo.setChargeType("all");
        Map<String,String> chargeTypeMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(integralChargeTypeList)){
			for (CodeBO codeBO : integralChargeTypeList) {
				chargeTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
        List<IntegeralCustLogBo> dataList = new ArrayList<IntegeralCustLogBo>();
        
        dataList =  IntegeralCustService.getIntegeralCustLogList(bo);
        if (CollectionUtils.isNotEmpty(dataList)){
	        for (IntegeralCustLogBo logBo : dataList) {
	        	logBo.setChargeTypeName(chargeTypeMap.get(logBo.getChargeType()));
			}
        }
        m.addAttribute("custLogForm", dataList);
        m.addAttribute("custLogForm", bo);
        
    	return "/bil/market/integralCust/integralCustInit";
    }
    
    /**
     * 获取用户积分流水记录列表
     * @param params
     * @return
     */
    @RequestMapping("/getIntegralCustLogList")
    public @ItemResponseBody
    QueryResultObject getIntegralCustLogList(@QueryRequestParam("params")
                                         RequestCondition params) {
    	int count = 0;
    	List<IntegeralCustLogBo> dataList = new ArrayList<IntegeralCustLogBo>();
    	IntegeralCustLogBo bo = this.rCondition2QCondition(params);
    	Map<String,String> eventTypeMap = new HashMap<>();
		List<CodeBO> eventTypeList = codeService.getStandardCodes("integralEventType", null);
		if (CollectionUtils.isNotEmpty(eventTypeList)){
			for (CodeBO codeBO : eventTypeList) {
				eventTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		List<CodeBO> integralChargeTypeList = codeService.getStandardCodes("integralChargeType", null);
		Map<String,String> chargeTypeMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(integralChargeTypeList)){
			for (CodeBO codeBO : integralChargeTypeList) {
				chargeTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		count = IntegeralCustService.getIntegeralCustLogCount(bo);
		if (count>0) {
			dataList =  IntegeralCustService.getIntegeralCustLogList(bo);
	        if (CollectionUtils.isNotEmpty(dataList)){
		        for (IntegeralCustLogBo logBo : dataList) {
		        	logBo.setChargeTypeName(chargeTypeMap.get(logBo.getChargeType()));

					DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
					DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
					// 解析输入的时间字符串
					LocalDateTime dateTime = LocalDateTime.parse(logBo.getChargeTime(), inputFormatter);
					// 格式化输出，去掉毫秒部分
					String formattedDateTime = dateTime.format(outputFormatter);
					logBo.setChargeTime(formattedDateTime);
				}
	        }
		}
		
    	log.info("====获取积分规则列表===dataList:"+dataList);
    	
    	return RestUtils.wrappQueryResult(dataList, count);
    }
	
	@Override
	protected IntegeralCustLogBo initCondition() {
		
		return new IntegeralCustLogBo();
	}

}
