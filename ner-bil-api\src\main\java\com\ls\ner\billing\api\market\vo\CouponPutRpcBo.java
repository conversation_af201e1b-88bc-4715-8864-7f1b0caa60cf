package com.ls.ner.billing.api.market.vo;

import java.io.Serializable;
import java.util.Date;

public class CouponPutRpcBo implements Serializable {

    private Long putId; // 发放ID
    private Long cpnId; // 优惠券ID
    private Integer isDelay; // 是否预约 1-预约 0-不预约 2-完成
    private Boolean isAllCust; // 是否发放所有用户 1-发放所有用户 0-发放指定用户列表

    private Date putTime;

    private String custInfoList;

    public String getCustInfoList() {
        return custInfoList;
    }

    public void setCustInfoList(String custInfoList) {
        this.custInfoList = custInfoList;
    }

    public Date getPutTime() {
        return putTime;
    }

    public void setPutTime(Date putTime) {
        this.putTime = putTime;
    }

    public Long getPutId() {
        return putId;
    }

    public void setPutId(Long putId) {
        this.putId = putId;
    }

    public Long getCpnId() {
        return cpnId;
    }

    public void setCpnId(Long cpnId) {
        this.cpnId = cpnId;
    }

    public Integer getDelay() {
        return isDelay;
    }

    public void setDelay(Integer delay) {
        isDelay = delay;
    }

    public Boolean getAllCust() {
        return isAllCust;
    }

    public void setAllCust(Boolean allCust) {
        isAllCust = allCust;
    }
}
