package com.ls.ner.billing.charge.bo;

import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.DicAttribute;



/**
 * <AUTHOR>
 * @dateTime 2016-04-08
 * @description 桩电价下发信息   E_CHCBILL_PILESEND
 */
public class ChcbillPileSendBo {
	private String systemId;//主键列
	private String chcNo;//充电计费编号
	private String pileNo;//充电桩外部标识，产生规则为流水号
	private String pileId;//充电桩外部标识，产生规则为流水号
	private String pileSendId;//下发日志ID
	private String sendTime;//下发时间
	private String failReason;//下发失败原因
	private String pileSendStatus;//下发状态，001成功、003失败、002下发中
	private String pileName;//充电桩名称

	@DicAttribute(dicName = "codeDict", key = "pileSendStatus", subType = BizConstants.CodeType.SUCCESS_FLAG)
	private String pileSendStatusName;//下发状态名称，下发状态，001成功、003失败、002下发中

	private String operator;	//账户
	private String intranetIp;	//内网IP
	private String networkIp;	//外网IP
	
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getPileNo() {
		return pileNo;
	}
	public void setPileNo(String pileNo) {
		this.pileNo = pileNo;
	}
	public String getPileSendId() {
		return pileSendId;
	}
	public void setPileSendId(String pileSendId) {
		this.pileSendId = pileSendId;
	}
	public String getSendTime() {
		return sendTime;
	}
	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}
	public String getFailReason() {
		return failReason;
	}
	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}
	public String getPileSendStatus() {
		return pileSendStatus;
	}
	public void setPileSendStatus(String pileSendStatus) {
		this.pileSendStatus = pileSendStatus;
	}
	public String getPileName() {
		return pileName;
	}
	public void setPileName(String pileName) {
		this.pileName = pileName;
	}
	public String getPileSendStatusName() {
		return pileSendStatusName;
	}
	public void setPileSendStatusName(String pileSendStatusName) {
		this.pileSendStatusName = pileSendStatusName;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getIntranetIp() {
		return intranetIp;
	}

	public void setIntranetIp(String intranetIp) {
		this.intranetIp = intranetIp;
	}

	public String getNetworkIp() {
		return networkIp;
	}

	public void setNetworkIp(String networkIp) {
		this.networkIp = networkIp;
	}

	public String getPileId() {
		return pileId;
	}

	public void setPileId(String pileId) {
		this.pileId = pileId;
	}
}