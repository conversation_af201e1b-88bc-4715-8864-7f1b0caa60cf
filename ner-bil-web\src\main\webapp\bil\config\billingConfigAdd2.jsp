<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String astPath = (String) request.getAttribute("astPath");
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型定价" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
		<ls:form id="billingConfigForm" name="billingConfigForm">
				<ls:title text="基本信息 "></ls:title>
				<table class="tab_search">
						<tr>
						    <td>
                    <ls:label text="管理单位" ref="orgCodeName" />
                </td>
                <td>
                    <ls:text name="orgCode" property="orgCode" type="hidden"></ls:text>
                    <ls:text name="orgCodeName" property="orgCodeName" readOnly="true" required="true"></ls:text>
                </td>
								<td>
										<ls:label text="车型" ref="autoModelName" />
								</td>
								<td>
										<ls:text name="autoModelNo" property="autoModelNo" type="hidden"></ls:text>
										<ls:text required="true" name="autoModelName" property="autoModelName" readOnly="true" onchanged="changeAutoModelName"></ls:text>
								</td>
								<td>
										<ls:label text="租赁方式" ref="subBeName" />
								</td>
								<td>
										<ls:text name="subBe" property="subBe" type="hidden"></ls:text>
										<ls:text name="subBeName" property="subBeName" readOnly="true" required="true"></ls:text>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="资费模版" ref="planName" />
								</td>
								<td>
										<ls:text name="planNo" type="hidden"></ls:text>
										<ls:text imageKey="search" onClickImage="getPlan" required="true" name="planName" readOnly="true" onchanged="changePlanName"></ls:text>
								</td>
								<td>
										<ls:label text="计费方式" ref="chargeWayName" />
								</td>
								<td>
										<ls:text name="chargeWay" type="hidden"></ls:text>
										<ls:text name="chargeWayName" required="true" readOnly="true"></ls:text>
								</td>
								<td>
										<ls:label text="计费模式" ref="chargeModeName" />
								</td>
								<td>
										<ls:text name="chargeMode" type="hidden"></ls:text>
										<ls:text name="chargeModeName" required="true" readOnly="true"></ls:text>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="定价方案名称" ref="billingConfigName" />
								</td>
								<td colspan="3">
										<ls:text name="billingConfigName" required="true"></ls:text>
								</td>
								<td>
										<ls:label text="生效日期" ref="eftDate" />
								</td>
								<td>
										<ls:date name="eftDate" format="yyyy-MM-dd HH:mm" required="true" onValidate="VD.dateTimeOfTodayValidate(eftDate)"></ls:date>
								</td>
						</tr>
				</table>
		</ls:form>
		<ls:includePage name="billingConfigPage" url="" ></ls:includePage>
				<!-- <iframe id="billingConfigIframe" name="billingConfigIframe" frameborder="0" style="overflow-x: auto; overflow-y: auto" name="afeIns" src="" allowTransparency="true"> </iframe> -->
		
		<ls:script>
	  function getModelRental(){
      LS.dialog("<%=astPath %>"+"/ast/brand/init?flag=1&subBe="+subBe.getValue(),"选择车型",1000,500,true);
    }
    function changeAutoModelName(){
      if(LS.isEmpty(autoModelName.getValue())){
        autoModelNo.setValue();
      }
    }
    window.setModelRental = setModelRental;
    function setModelRental(item) {
       autoModelNo.setValue(item.modelId);  
       autoModelName.setValue(item.modelName);
       subBe.setValue(item.subBe);
       subBeName.setValue(item.subBeName);
    }
    function getPlan(){
      LS.dialog("~/billing/tariffplans/init?flag=1&subBe="+subBe.getValue(),"选择资费模版",1000,500,true);
    }
    function changePlanName(){
      if(LS.isEmpty(autoModelName.getValue())){
        planNo.setValue();
      }
    }
    window.setPlan = setPlan;
    function setPlan(item) {
       planNo.setValue(item.planNo);  
       planName.setValue(item.planName);
       subBe.setValue(item.subBe);
       subBeName.setValue(item.subBeName);
       chargeWay.setValue(item.chargeWay);
       chargeWayName.setValue(item.chargeWayName);
       chargeMode.setValue(item.chargeMode);
       chargeModeName.setValue(item.chargeModeName);
       billingConfigPage.load("~/billing/configs/new/"+item.planNo);
       
       //billingConfigIframe.location.href = "<%=request.getContextPath()%>/billing/configs/new/"+item.planNo;          
    }
    </ls:script>
</ls:body>
</html>