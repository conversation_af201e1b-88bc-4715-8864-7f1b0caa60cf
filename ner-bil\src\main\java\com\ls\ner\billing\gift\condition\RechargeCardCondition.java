package com.ls.ner.billing.gift.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.math.BigDecimal;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.condition
 * @ClassName: RechargeCardCondition
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/4/27 10:01
 * @Version: 1.0
 */
public class RechargeCardCondition extends QueryCondition {

    /** 充值卡ID */
    private Long cardId;

    private Long cardDetailId;

    /** 充值卡名称 **/
    private String cardName;

    /** 充值卡批次号 */
    private Long cardBatchNo;

    /** 实际面值 */
    private BigDecimal actualAmt;

    /** 赠送部分面值 */
    private BigDecimal giftAmt;

    /** 备注 */
    private String remark;

    /** 充值卡状态：1 启用 0 禁用 */
    private String cardStatus;

    /** 充值卡有效期时间类型，1永久有效 2限时有效 */
    private String cardTimeType;

    /** 生效日期 */
    private String eftDate;

    /** 失效日期 */
    private String invDate;

    /** 创建时间开始 */
    private String createTimeBegin;

    /** 创建时间结束 */
    private String createTimeEnd;

    /** 充电订单结算时间开始 */
    private String settleTimeBegin;

    /** 充电订单结算时间结束 */
    private String settleTimeEnd;

    /** 创建人员 */
    private String createUser;

    /** 操作时间 */
    private String updateTime;

    /** 操作人员 */
    private String updateUser;

    /**
     * 充值卡号
     */
    private String cardNo;

    /**
     * 充值卡密
     */
    private String cardSecret;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 卡号状态：0 待激活 1 已激活 2已冻结 3已退款 4使用完毕
     */
    private String cardDetailStatus;

    /**
     * 用户id
     */
    private String custId;

    /** 每张赠送积分 */
    private String integralNum;

    public String getIntegralNum() { return integralNum; }
    public void setIntegralNum(String integralNum) { this.integralNum = integralNum; }

    public Long getCardDetailId() {
        return cardDetailId;
    }

    public void setCardDetailId(Long cardDetailId) {
        this.cardDetailId = cardDetailId;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardSecret() {
        return cardSecret;
    }

    public void setCardSecret(String cardSecret) {
        this.cardSecret = cardSecret;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCardDetailStatus() {
        return cardDetailStatus;
    }

    public void setCardDetailStatus(String cardDetailStatus) {
        this.cardDetailStatus = cardDetailStatus;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Long getCardBatchNo() {
        return cardBatchNo;
    }

    public void setCardBatchNo(Long cardBatchNo) {
        this.cardBatchNo = cardBatchNo;
    }

    public BigDecimal getActualAmt() {
        return actualAmt;
    }

    public void setActualAmt(BigDecimal actualAmt) {
        this.actualAmt = actualAmt;
    }

    public BigDecimal getGiftAmt() {
        return giftAmt;
    }

    public void setGiftAmt(BigDecimal giftAmt) {
        this.giftAmt = giftAmt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getCardTimeType() {
        return cardTimeType;
    }

    public void setCardTimeType(String cardTimeType) {
        this.cardTimeType = cardTimeType;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getCreateTimeBegin() {
        return createTimeBegin;
    }

    public void setCreateTimeBegin(String createTimeBegin) {
        this.createTimeBegin = createTimeBegin;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getSettleTimeBegin() {
        return settleTimeBegin;
    }

    public void setSettleTimeBegin(String settleTimeBegin) {
        this.settleTimeBegin = settleTimeBegin;
    }

    public String getSettleTimeEnd() {
        return settleTimeEnd;
    }

    public void setSettleTimeEnd(String settleTimeEnd) {
        this.settleTimeEnd = settleTimeEnd;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}
