package com.ls.ner.billing.xpcharge.controller;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.util.IPUtil;
import com.ls.ner.util.MergeUtil;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.*;

/**
 * 充电电价下发
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/station-bill-send")
public class XpStationBillSendController extends QueryController<ChcbillSendQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(XpStationBillSendController.class);

	@ServiceAutowired("xpStationSendService")
	private IXpStationSendService xpStationSendService;

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired
	Authentication authentication;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="areaRpcService")
	private IAreaRpcService areaRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
	private ISysParamService sysParamService;
	
	@Override
	protected ChcbillSendQueryCondition initCondition() {
		return new ChcbillSendQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化站点电价下发
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.CHC_BILL_SEND_STATUS);
		ChcbillSendQueryCondition condition = new ChcbillSendQueryCondition();
		 AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				condition.setOrgCode(orgBo.getOrgCode());
				condition.setOrgCodeName(orgBo.getOrgShortName());			
			}
			m.addAttribute("searchForm", condition);
			m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/xpchargeConf/stationSend/stationSendIndex";
	}

	/**
	 * @param params
	 * @description 查询充电电价下发
	 * <AUTHOR>
	 * @create 2018-04-19 16:37:07
	 */
	@RequestMapping(value = "/queryChcbillSends")
	public @ItemResponseBody QueryResultObject queryChcbillSends(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		Map<String,Object> searchMap = new HashedMap();
		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				searchMap.put("orgCodeList",orgCodeList);
			}
		}
		searchMap.put("stationId",condition.getStationId());
		searchMap.put("billingConfig",AssetConstants.billingConfig.BILLING_BY_STATION);
		searchMap.put("linkMode","01");
		searchMap.put("begin",condition.getBegin());
		searchMap.put("end",condition.getEnd());
		List<Map> list =archivesRpcService.queryStationsByMap(searchMap);
		if(list != null && list.size() > 0){
			List<String> stationList = new LinkedList<>();
			for(Map station:list){
				OrgBo org = orgService.getOrgByNo(StringUtil.nullForString(station.get("orgCode")));
				if(org != null)
					station.put("orgCodeName",org.getOrgShortName());
				stationList.add(StringUtil.nullForString(station.get("stationId")));
			}
			Map<String,Object> searchChcMap = new HashedMap();
			searchChcMap.put("stationList",stationList);
			List<Map> curentChcs = xpStationSendService.queryTheCurrentChc(searchChcMap);
			MergeUtil.mergeList(list, curentChcs, "stationId",  new String[]{"curChcNo","curChcName","sendTime","sendStatus"},  new String[]{"chcNo","chcName","sendTime","sendStatus"});
			List<Map> newChcs = xpStationSendService.queryTheNewChc(searchChcMap);
			MergeUtil.mergeList(list, newChcs, "stationId",  new String[]{"newChcNo","newChcName","eftDate"},  new String[]{"chcNo","chcName","eftDate"});
			AreaCondition areaCondition = new AreaCondition();
			for(Map station:list){
				CodeBO superCode = codeService.getStandardCode("chcBillSendStatus", StringUtil.nullForString(station.get("sendStatus")), null);
				if (superCode != null) {
					station.put("sendStatusName",superCode.getCodeName());
				}
				areaCondition.setAreaCode(StringUtil.nullForString(station.get("city")));
				Map areaMap = areaRpcService.queryCityByCode(areaCondition);
				if (areaMap !=null ){
					station.put("city",areaMap.get("AREA_NAME"));
				}
			}
		}
		int recordCount = archivesRpcService.queryStationsNumByMap(searchMap);
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-30
	 * @description 下发充电电价
	 */
	@RequestMapping(value = "/issured", method = RequestMethod.POST)
	public @ResponseBody WrappedResult issured(Model m, @RequestParam("chcNo") String chcNo , @RequestParam("stationId") String stationId, HttpServletRequest request) {
		try {
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			xpStationSendService.issuredStation(chcNo,stationId,map);
			return WrappedResult.successWrapedResult("正在下发");
		} catch (Exception e) {
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发明细
	 */
	@RequestMapping(value = "/detail", method = RequestMethod.GET)
	public String detail(Model m, @RequestParam("chcNo") String chcNo,@RequestParam("stationId") String stationId) {
		addCodes(m, BizConstants.CodeType.CHC_BILL_SEND_STATUS,BizConstants.CodeType.VALID_FLAG);
		if (!StringUtils.nullOrBlank(chcNo)) {
			List<CodeBO> pileSendStatusList = codeService.getAllStandardCodes(BizConstants.CodeType.CHC_BILL_SEND_STATUS, null);
			m.addAttribute("issuedPileStatusList", pileSendStatusList);

			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0) {
				ChargeBillingConfBo bo=list.get(0);
				bo.setStationId(stationId);

				//查询下发状态
				List<String> stationList = new LinkedList<>();
				stationList.add(stationId);
				Map<String,Object> searchChcMap = new HashedMap();
				searchChcMap.put("stationList",stationList);
				List<Map> curentChcs = xpStationSendService.queryTheCurrentChc(searchChcMap);
				if (curentChcs!=null && curentChcs.size()>0){
					bo.setSendStatus(StringUtil.nullForString(curentChcs.get(0).get("sendStatus")));
					bo.setSendTime(StringUtil.nullForString(curentChcs.get(0).get("sendTime")));
				}

				Map<String, Object> searchMap = new HashMap<String, Object>();
				searchMap.put("stationId", stationId);
				Map<String,Object> stationMap = archivesRpcService.queryStation(searchMap);
				if(stationMap!=null && stationMap.get("stationName")!=null)
					bo.setStationName(String.valueOf(stationMap.get("stationName")));

				OrgBo org = orgService.getOrgByNo(bo!=null?bo.getOrgCode():"");
				if(org != null)
					bo.setOrgCodeName(org.getOrgShortName());

				m.addAttribute("searchForm", bo);
			}else
				m.addAttribute("searchForm", new ChargeBillingConfBo());
		}else {
			m.addAttribute("searchForm", new ChargeBillingConfBo());
		}
		String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
		m.addAttribute("chargingPriingSendTimeOut", StringUtil.isEmpty(sendTimeOut)?"20":sendTimeOut);
		return "/bil/xpchargeConf/stationSend/chcbillSendDetail";
	}

	/**
	 * <AUTHOR>
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SecurityException
	 * @throws NoSuchFieldException
	 * @dateTime 2016-04-09
	 * @description 查询桩电价下发明细
	 */
	@RequestMapping(value = "/queryChcbillSendDetail")
	public @ItemResponseBody QueryResultObject queryChcbillSendDetail(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendBo> list = null;
		int recordCount = 0;
		if(!StringUtils.nullOrBlank(condition.getChcNo())){
			//2、查询充电站下所有未下发成功的桩
			List<Map<String, String>> piles = archivesRpcService.queryPilesByStationId(condition.getStationId(), null);
			//查询桩是否存在单独计费配置---如果存在则不展示
			List<Map<String, String>> newPiles= new ArrayList<Map<String, String>>();
			Map sercher=new HashMap();
			for (int i=0;i<piles.size();i++){
				sercher.put("pileId",piles.get(i).get("pileId"));
				int count =xpStationSendService.queryPileBillCount(sercher);
				if (count<1){
					newPiles.add(piles.get(i));
				}
			}

			List pileNoList=new ArrayList();
			for(Map pile : newPiles) {
				pileNoList.add(pile.get("pileNo"));
			}
			condition.setPileNos(pileNoList.size()>0 ? pileNoList : null);
			list =chcbillSendService.queryChcbillPileSends(condition);
			if(list != null && list.size() > 0){
				StringBuilder pileNoSb = new StringBuilder();
				for(ChcbillPileSendBo cbcBo:list){
					if(!StringUtils.nullOrBlank(cbcBo.getPileNo())){
						pileNoSb.append(cbcBo.getPileNo()).append(",");
					}
				}
				String pileNos = pileNoSb.toString();
				if(!StringUtils.nullOrBlank(pileNos)){
					pileNos = pileNos.substring(0, pileNos.length() - 1);
					ChargeBillingConfQueryCondition cbQCondition = new ChargeBillingConfQueryCondition();
					cbQCondition.setChcNo(condition.getChcNo());
					List<ChargeBillingConfBo> cbcs = chargeBillingConfService.queryChargeBillingConfs(cbQCondition);
					String stationId = null;
					if(cbcs.size() > 0)
						stationId = condition.getStationId();
					List<Map<String,Object>> stationList = archivesRpcService.queryEquipByStationIdAndEquipNos(stationId,pileNos);
					MergeUtil.mergeList(list, stationList, "pileNo", "equipNo",  new String[]{"pileName"},  new String[]{"equipName"});
				}
			}
			recordCount = chcbillSendService.queryChcbillPileSendsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-10
	 * @description 重新下发桩电价
	 */
	@RequestMapping(value = "/issuredPile", method = RequestMethod.POST)
	public @ResponseBody WrappedResult issuredPile(Model m, @RequestParam("chcNo") String chcNo,@RequestParam("pileNo") String pileNo,@RequestParam("stationId") String stationId,HttpServletRequest request) {
		try {
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			xpStationSendService.issuredPile(chcNo, pileNo,stationId,map);
			return WrappedResult.successWrapedResult(true);
		} catch (Exception e) {
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发历史
	 */
	@RequestMapping(value = "/log", method = RequestMethod.GET)
	public String log(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo) {
		addCodes(m,BizConstants.CodeType.VALID_FLAG);
		if (!StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			if(list != null && list.size() > 0)
				bo = list.get(0);
			bo.setPileNo(pileNo);
			m.addAttribute("searchForm", bo);
		}
		return "/bil/xpchargeConf/stationSend/chcbillSendLog";
	}

	/**
	 * @param params
	 * @description 查询电价下发历史
	 * <AUTHOR>
	 * @create 2018-04-18 11:14:29
	 */
	@RequestMapping(value = "/queryChcbillSendLog")
	public @ItemResponseBody QueryResultObject queryChcbillSendLog(@QueryRequestParam("params") RequestCondition params) {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendLogBo> list = null;
		int recordCount = 0;
		if(!StringUtils.nullOrBlank(condition.getChcNo())){
			list =chcbillSendService.queryChcbillPileSendLogs(condition);
			recordCount = chcbillSendService.queryChcbillPileSendLogsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}

	/**
	 * @Description:获取下级
	 * @Author: tianyoupeng
	 * @Time: 2017/6/19 10:13
	 */
	private List<String> getSubOrg(String orgCode) {
		List<String> orgList = new ArrayList<String>();
		if (StringUtil.isNotBlank(orgCode)) {
			String[] orgArr = orgCode.split(",");
			for (String orgCodeTemp : orgArr) {
				List<OrgBo> orgBoList = orgService.getSubOrg(orgCodeTemp, null, null);
				if (!CollectionUtils.isEmpty(orgBoList)) {
					for (OrgBo bo : orgBoList) {
						orgList.add(bo.getOrgCode());
					}
				}
			}
		} else {
			return null;
		}
		orgList.add(orgCode);
		return orgList;
	}
}
