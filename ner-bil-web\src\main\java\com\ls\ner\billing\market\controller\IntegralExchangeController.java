package com.ls.ner.billing.market.controller;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.service.IIntegralExchangeService;
import com.ls.ner.billing.market.vo.IntegralExchangeVo;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/billing/integralExchange")
public class IntegralExchangeController extends QueryController<IntegralExchangeVo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(IntegralExchangeController.class);

    @ServiceAutowired(value = "integralExchangeService", serviceTypes = ServiceType.LOCAL)
    private IIntegralExchangeService integralExchangeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private ICodeService codeService;


    @RequestMapping(value = "/init", method = RequestMethod.GET)
    public String init(Model m) {
        List<CodeBO> goodsTypeList = codeService.getAllStandardCodes("goodsType", null);

        List<CodeBO> exchangeStatusList = codeService.getAllStandardCodes("exchangeStatus", null);


        m.addAttribute("goodsTypeList", code2ItemList(goodsTypeList, true));

        m.addAttribute("exchangeStatusList", code2ItemList(exchangeStatusList, true));

        return "/bil/market/integralExchange/info";
    }

    @RequestMapping({"/queryInfo"})
    public @ItemResponseBody
    QueryResultObject queryInfo(@QueryRequestParam("params") RequestCondition params) throws Exception {

        IntegralExchangeVo vo = this.rCondition2QCondition(params);
        List<IntegralExchangeVo> dataList = integralExchangeService.getIntegralExchangeList(vo);
        int count = integralExchangeService.getIntegralExchangeListCount(vo);

        return RestUtils.wrappQueryResult(dataList, count);
    }

    @RequestMapping(value = "/initEdit", method = RequestMethod.GET)
    public String initEdit(Model m, @RequestParam("id") String integralId) {
        IntegralExchangeVo vo = new IntegralExchangeVo();
        vo.setIntegralId(integralId);
        List<IntegralExchangeVo> dataList = integralExchangeService.getIntegralExchangeList(vo);
        if (dataList != null && dataList.size() > 0) {
            vo = dataList.get(0);
            CodeBO goodsTypeCode = codeService.getStandardCode("goodsType", vo.getGoodsType(), null);
            vo.setGoodsTypeName(goodsTypeCode == null ? vo.getGoodsType() : goodsTypeCode.getCodeName());
            CodeBO goodsFreightCode = codeService.getStandardCode("goodsFreight", vo.getGoodsFreight(), null);
            vo.setGoodsFreightName(goodsFreightCode == null ? vo.getGoodsFreight() : goodsFreightCode.getCodeName());


        }
        m.addAttribute("infoForm", vo);
        List<CodeBO> logisticsList = codeService.getAllStandardCodes("logisticsType", null);

        m.addAttribute("logisticsList", code2ItemList(logisticsList, false));

        return "/bil/market/integralExchange/integralExchangeEdit";
    }

    @RequestMapping(value = "/initInfo", method = RequestMethod.GET)
    public String initInfo(Model m, @RequestParam("id") String integralId) {
        IntegralExchangeVo vo = new IntegralExchangeVo();
        vo.setIntegralId(integralId);
        List<IntegralExchangeVo> dataList = integralExchangeService.getIntegralExchangeList(vo);
        if (dataList != null && dataList.size() > 0) {
            vo = dataList.get(0);
            CodeBO goodsTypeCode = codeService.getStandardCode("goodsType", vo.getGoodsType(), null);
            vo.setGoodsTypeName(goodsTypeCode == null ? vo.getGoodsType() : goodsTypeCode.getCodeName());
            CodeBO goodsFreightCode = codeService.getStandardCode("goodsFreight", vo.getGoodsFreight(), null);
            vo.setGoodsFreightName(goodsFreightCode == null ? vo.getGoodsFreight() : goodsFreightCode.getCodeName());
        }
        m.addAttribute("infoForm", vo);
        List<CodeBO> logisticsList = codeService.getAllStandardCodes("logisticsType", null);

        m.addAttribute("logisticsList", code2ItemList(logisticsList, false));

        return "/bil/market/integralExchange/integralExchangeRead";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject save(IntegralExchangeVo integralExchangeVo) {
        LOGGER.info("/queryInfo,params:{}", integralExchangeVo);
        String mark = "N";
        try {
            integralExchangeService.save(integralExchangeVo);
            mark = "Y";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);

    }


    public List<Map<String, Object>> code2ItemList(List<CodeBO> codeList, Boolean needAll) {
        if (codeList != null && codeList.size() > 0) {
            List<Map<String, Object>> tempMapList = new ArrayList<Map<String, Object>>();

            if (needAll) {
                Map<String, Object> _tempMap = new HashMap<String, Object>();
                _tempMap.put("name", "全部");
                _tempMap.put("value", "");
                tempMapList.add(_tempMap);

            }

            for (CodeBO codeBO : codeList) {
                if (StringUtils.nullOrBlank(codeBO.getpCodeValue())) {
                    Map<String, Object> tempMap = new HashMap<String, Object>();
                    tempMap.put("name", codeBO.getCodeName());
                    tempMap.put("value", codeBO.getCodeValue());
                    tempMapList.add(tempMap);
                }
            }
        /*    for (Map<String, Object> map : tempMapList) {
                List<Map<String, String>> tempList = new ArrayList<Map<String, String>>();
                for (CodeBO codeBO : codeList) {
                    if (String.valueOf(codeBO.getpCodeValue()).equals(map.get("value"))) {
                        Map<String, String> _tempMap = new HashMap<String, String>();
                        _tempMap.put("name", codeBO.getCodeName());
                        _tempMap.put("value", codeBO.getCodeValue());
                        tempList.add(_tempMap);
                    }
                }
                map.put("items", tempList);
            }*/
            return tempMapList;
        } else {
            return null;
        }
    }

    @Override
    protected IntegralExchangeVo initCondition() {
        return new IntegralExchangeVo();
    }
}
