<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.xpcharge.dao.IXpStationSendDao">

	<!--查询最新计费版本-->
	<select id="queryTheNewChc" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT a.CHC_NO chcNo,a.STATION_ID stationId,b.CHC_NAME chcName,b.EFT_DATE eftDate
		FROM E_STATION_BILLING a
			LEFT JOIN e_charge_billing_conf b on a.CHC_NO = b.CHC_NO
		<where>
			<if test="stationList !=null">
				and a.STATION_ID in
				<foreach item="item" index="index" collection="stationList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<!--查询当前计费版本-->
	<select id="queryTheCurrentChc" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT b.STATION_ID stationId,b.CHC_NO chcNo,c.CHC_NAME chcName,b.SEND_TIME sendTime,b.SEND_STATUS sendStatus
		from(
			SELECT a.STATION_ID,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
			FROM(
				SELECT a.STATION_ID,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
				FROM E_CHCBILL_SEND a
				<where>
					<if test="stationList !=null">
						and a.STATION_ID in
						<foreach item="item" index="index" collection="stationList" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
				</where>
				having 1
				ORDER BY a.SEND_TIME desc
			) a GROUP BY a.STATION_ID
		) b LEFT JOIN E_CHARGE_BILLING_CONF c on b.CHC_NO = c.CHC_NO
	</select>

	<!-- 新增充电计费配置下发信息 E_CHCBILL_SEND -->
	<insert id="insertChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHCBILL_SEND
		<trim prefix="(" suffix=")">
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sendTime !=null and sendTime !=''">SEND_TIME,</if>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS,</if>
			<if test="stationId !=null and stationId !=''">STATION_ID,</if>
			<if test="pileNo !=null and pileNo !=''">PILE_NO,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
		</trim>
		<trim prefix="values (" suffix=")">
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sendTime !=null and sendTime !=''">#{sendTime},</if>
			<if test="sendStatus !=null and sendStatus !=''">#{sendStatus},</if>
			<if test="stationId !=null and stationId !=''">#{stationId},</if>
			<if test="pileNo !=null and pileNo !=''">#{pileNo},</if>
			now(),'I'
		</trim>
	</insert>

	<!-- 更新充电计费配置下发信息 E_CHCBILL_SEND -->
	<update id="updateChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo">
		update E_CHCBILL_SEND
		<set>
			<choose>
				<when test="sendTime != null and sendTime != ''">
					SEND_TIME =#{sendTime},
				</when>
			</choose>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS =#{sendStatus},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			<if test="chcNo !=null and chcNo !=''">
				AND CHC_NO =#{chcNo}
			</if>
			<if test="stationId !=null and stationId !=''">
				AND STATION_ID =#{stationId}
			</if>
			<if test="pileNo !=null and pileNo !=''">
				AND PILE_NO =#{pileNo}
			</if>
		</where>
	</update>

	<select id="queryChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" resultType="int">
		SELECT  count(1) from E_CHCBILL_SEND
		<where>
			<if test="chcNo !=null and chcNo !=''">
				AND CHC_NO =#{chcNo}
			</if>
			<if test="stationId !=null and stationId !=''">
				AND STATION_ID =#{stationId}
			</if>
			<if test="pileNo !=null and pileNo !=''">
				AND PILE_NO =#{pileNo}
			</if>
		</where>
	</select>


	<select id="queryPileBillCount" parameterType="java.util.Map" resultType="int">
		SELECT
		count(1)
		FROM
			e_pile_billing a
		<where>
			<if test="pileId !=null and  pileId != '' ">
				a.PILE_ID=#{pileId}
			</if>
		</where>
	</select>

	<select id="countByStationIdAndChcNo" resultType="int">
		select count(*) from
			E_STATION_BILLING
		where STATION_ID = #{stationId}
		and CHC_NO = #{chcNo}
	</select>
</mapper>