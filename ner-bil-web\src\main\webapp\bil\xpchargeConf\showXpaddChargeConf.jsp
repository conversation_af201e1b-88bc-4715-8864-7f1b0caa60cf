<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费基本信息" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
<div style="width: 99.50%">
	<ls:form id="chargeForm" name="chargeForm">
		<ls:title text="资费基本信息"></ls:title>
			<ls:text type="hidden" name="systemId" property="systemId"/>
			<ls:text type="hidden" name="orgCode" property="orgCode"></ls:text>
			<ls:text type="hidden" name="chcStatus" property="chcStatus"></ls:text>
			<ls:text type="hidden" name="attachItemNos" property="attachItemNos"/>
			<ls:text type="hidden" name="attachItemPrices" property="attachItemPrices"/>
			<ls:text type="hidden" name="freeNum" property="freeNum"/>
			<ls:text type="hidden" name="chargePeriodsStr"/>
			<table class="tab_search">
				<tr>
					<td width="130px;"><ls:label text="充电计费编号" ref="chcNo" /></td>
					<td colspan="2"><ls:text name="chcNo" property="chcNo" readOnly="true"/></td>
					<td><ls:label text="充电计费名称" ref="chcName" /></td>
					<td colspan="2"><ls:text name="chcName" property="chcName" readOnly="true" maxlength="50"/></td>
                </tr>
                <tr>
					<td><ls:label text="桩生效日期" ref="eftDate" /></td>
					<td colspan="2"><ls:date name="eftDate" property="eftDate" readOnly="true" format="yyyy-MM-dd HH:mm" onValidate="VD.dateTimeOfTodayValidate(eftDate)"/></td>
					<td><ls:label text="上个计费编号" ref="upChcNo" /></td>
					<td colspan="2"><ls:text name="upChcNo" property="upChcNo" readOnly="true"/></td>
				</tr>
				<tr>
					<td><ls:label text="计费模板标签" ref="billingTag" /></td>
					<td colspan="2">
						<ls:select name="billingTag" property="billingTag" required="true">
							<ls:options property="billingTagList" scope="request" text="codeName" value="codeValue"/>
						</ls:select>
					</td>
					<td colspan="3"></td>
				</tr>
				<tr style="display: none">
					<td><ls:label text="终端显示的充电服务费" ref="chcRemark"/></td>
					<td colspan="5"><ls:text  name="chcRemark" property="chcRemark" readOnly="true" type="textarea" maxlength="200"></ls:text></td>
				</tr>
			</table>
		<ls:title text="充电计费规则"></ls:title>
			<table class="tab_search">
				<tr>
					<td width="130px;"><ls:label text="费控模式" ref="billCtlMode" /></td>
					<td colspan="2">
						<ls:checklist type="radio" name="billCtlMode" property="billCtlMode" onchanged="changeBillCtlMode" readOnly="true">
							<ls:checkitems property="billCtlModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeShowOne" width="100px;"><ls:label text="桩推送数据类型" ref="gatherDataType" /></td>
					<td id="gatherDataTypeShowTwo" colspan="2">
						<ls:checklist type="radio" name="gatherDataType" property="gatherDataType" readOnly="true" onchanged="changePriceTrShow">
							<ls:checkitem value="1" text="计量表示数"></ls:checkitem>
							<ls:checkitem value="2" text="电量"></ls:checkitem>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeHide" colspan="3" style="display: none;"></td>
				</tr>
				<tr>
					<td width="100px;"><ls:label text="计费模式" ref="chargeMode" /></td>
					<td colspan="5">
						<ls:checklist type="radio" name="chargeMode" property="chargeMode" readOnly="true" onchanged="changePriceTrShow">
							<ls:checkitems property="chcBillingChargeModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
				</tr>
				<tr id="priceTr">
					<td><ls:label text="计费单价" ref="chargePrice" /></td>
					<td colspan="4"><ls:text name="chargePrice" property="chargePrice" readOnly="true" type="number" maxlength="11"  max="9999" onValidate="chargePriceValidate()"/></td>
					<td>元/kWh</td>
				</tr>
				<tr id="timeTr">
					<td><ls:label text="分时时段设置"/></td>
					<td colspan="5">
						<ls:grid url="" name="timeShareGrid" primaryKey="sn" cellEdit="false" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="100%">
							<ls:column caption="排序号" name="sn" hidden="true"/>
							<ls:column caption="开始时间" name="beginTime" editableEdit="false" readOnly="true"/>
							<ls:column caption="结束时间" name="endTime" >
								<ls:textEditor onValidate="endTimeValidate()" required="true"/>
							</ls:column>
							<ls:column caption="时段标识" name="timeFlag" >
								<ls:selectEditor property="timeFlagList" name="timeFlag" valueMember="codeValue" required="true" displayMember="codeName" onchanged="changeTimeFlag">
								</ls:selectEditor>
								<%-- <ls:selectEditor required="true" onchanged="changeTimeFlag">
									<ls:optionEditor value="1" text="尖" ></ls:optionEditor>
									<ls:optionEditor value="2" text="峰"></ls:optionEditor>
									<ls:optionEditor value="3" text="平"></ls:optionEditor>
									<ls:optionEditor value="4" text="谷"></ls:optionEditor>
								</ls:selectEditor> --%>
							</ls:column>
							<ls:column caption="计费单价（元）" name="price">
								<ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
							</ls:column>
						</ls:grid>
					</td>
				</tr>
			</table>
			<ls:title text="服务项目"></ls:title>
			<table align="center" class="tab_search">
				<tr>
					<td><ls:grid url="" name="serviceGrid" primaryKey="itemNo"  cellEdit="false" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="99%">
							<ls:column caption="费用项" name="itemNo"  hidden="true" />
							<ls:column caption="费用项名称" name="itemName"  readOnly="true"/>
							<ls:column caption="费用项编号" name="itemNo"  readOnly="true" />
							<ls:column caption="费用单价（元）" name="servicePrice"> 
								<ls:textEditor  maxlength="11" required="true" type="number"  max="9999"  onValidate="priceValidate()"/>
							</ls:column>
							<ls:column caption="免费量" name="freeNum" hidden="true">
								<ls:textEditor  maxlength="11" required="true" type="number"  max="9999"  onValidate="priceValidate()"/>
							</ls:column>
							<ls:column caption="费用项单位" name="itemUnitName" readOnly="true"/>
						</ls:grid></td>
				</tr>
			</table>
	</ls:form>
</div>
	<ls:script>
	var astPath = '${astPath }';
	init();
	function init(){
		if(systemId.getValue() == null || systemId.getValue() == ''){
			var item = {
				sn:1,
				beginTime:"00:00",
				endTime:"24:00",
				timeFlag:""
			};
			timeShareGrid.addRow(item,"last");
			chargeMode.setValue('0201');//标准
			billCtlMode.setValue('1');//费控模式   1表示本地，2远程
		}else{
			var mode = chargeMode.getValue();
			if(mode != '0201'){
				timeShareGridSearch();
			}else{
				var item = {
				sn:1,
				beginTime:"00:00",
				endTime:"24:00"
				};
				timeShareGrid.addRow(item,"last");
			}
			serviceGridSearch();
		}
		changePriceTrShow();
		changeBillCtlMode();
		$("#pager_id_timeShareGrid").remove();
		$("#pager_id_serviceGrid").remove();
	}
	
	function doSave(){
		 if(!chargeForm.valid())
			return ;
		 var mode = chargeMode.getValue();
		 if(mode == '0202'){//分时
		 	var timeShareItems = timeShareGrid.getItems();
		 	for(var i = 0;i < timeShareItems.length;i++){
		 		var endTime = timeShareItems[i].endTime;
		 		var price = timeShareItems[i].price;
		 		if(LS.isEmpty(endTime)){
			 		LS.message("error","分时时段设置中的结束时间不能为空");
			 		return;
		 		}
		 		if(LS.isEmpty(price)){
			 		LS.message("error","分时时段设置中的计费单价不能为空");
			 		return;
		 		}
		 		if(timeShareItems[i].timeFlag=="尖"){
		    		timeShareItems[i].timeFlag="1";
		    	}else if(timeShareItems[i].timeFlag=="峰"){
		    		timeShareItems[i].timeFlag="2";
		    	}else if(timeShareItems[i].timeFlag=="平"){
		    		timeShareItems[i].timeFlag="3";
		    	}else if(timeShareItems[i].timeFlag=="谷"){
		    		timeShareItems[i].timeFlag="4";
		    	}
		 	}
		 	chargePeriodsStr.setValue(JSON.stringify(timeShareItems));
		 }
		 var serviceItems = serviceGrid.getItems();
		 var itemNos = "";
		 var itemPrices = "";
		 var freeNums = "";
		 for(var i=0;i < serviceItems.length;i++){
		 	if(LS.isEmpty(serviceItems[i].servicePrice)){
		 		LS.message("error","服务项目中的费用单价不能为空");
		 		return;
		 	}
		 	if(i+1!=serviceItems.length){
		 		itemNos+=serviceItems[i].itemNo+",";
		 		itemPrices+=serviceItems[i].servicePrice+",";
				freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0") + ",";
		 	}else{
		 		itemNos+=serviceItems[i].itemNo;
		 		itemPrices+=serviceItems[i].servicePrice;
				freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0");
		 	}
		 }
		 attachItemNos.setValue(itemNos);
		 attachItemPrices.setValue(itemPrices);
		 freeNum.setValue(freeNums);
		 chargeForm.submit("~/billing/xpcbc/saveChargeBillingConf",function(data){
   		 	if(data.systemId){
   		 		if(systemId.getValue()==null || systemId.getValue()==''){
         				systemId.setValue(data.systemId);
         				chcNo.setValue(data.chcNo);
         				chcStatus.setValue(data.chcStatus);
         				LS.message("info","新建成功"); 
         				LS.parent().doSearch();			
   		 		}else{
   		 			LS.message("info","修改成功"); 
   		 			LS.parent().doSearch();
   		 		}
   		 	}else{
   		 		LS.message("error","保存失败");
   		 	}
      });
	}
	
	function doReturn(){
		setTimeout("LS.window.close()",200);
	}
	//========================================费控模式===========================================================
	function changeBillCtlMode(){
    	var value = billCtlMode.getValue();
    	if(value == '1'){//本地
    		$('#gatherDataTypeHide').show();
    		$('#gatherDataTypeShowOne').hide();
    		$('#gatherDataTypeShowTwo').hide();
    	}else{//远程
    		$('#gatherDataTypeHide').hide();
    		$('#gatherDataTypeShowOne').hide();
    		$('#gatherDataTypeShowTwo').hide();
    	}
	}
	
	//========================================计费单价===========================================================
	function chargePriceValidate(){
    	var bMoney = chargePrice.getValue();
		var isValid = true;
    	if(!LS.isEmpty(bMoney)){
	    	var i = bMoney.indexOf(".");
	    	var str = "";
	    	if(i>=0){
	    		str = bMoney.substring(i+1);
	    	}
	    	isValid = (str.length <= 4);
    	}
    	return {
            isValid : isValid,
            message : "小数位不能超过四位"
        }
	}
	//========================================分时时段设置=========================================================
	function timeShareGridSearch(){
		var params = {
			chcNo:chcNo.getValue()
		};
		LS.ajax("~/billing/xpcbc/queryChargePeriods",params,function(data){
				timeShareGrid.removeAllItems();
				if(data != null){
					for(var i = 0 ; i < data.length ; i++){
						timeShareGrid.addRow(data[i],"last");
					}
				}
		});
	}
	
	function doAddTimeShareGrid(){
		var data = timeShareGrid.getItems();
		
		var item = {
			sn:data[data.length-1].sn+1,
			endTime:"24:00",
			timeFlag:""
		};
		timeShareGrid.addRow(item,"last");
		timeShareGrid.setCell(data[data.length-1].sn, "endTime", "");
	}
	
	function endTimeValidate(value,eValidator){
		var returnValid = {
			isValid : true,
			message : ""
		}
		if(LS.isEmpty(value)){
			returnValid.message = "必选填字段";
			returnValid.isValid = false;
			return returnValid;
		}
		if(!/((([0-1]\d)|(2[0-3])):[0-5]\d)|24:00/.test(value)){
			returnValid.message = "请输入正确的时间格式，如:03:00";
			returnValid.isValid = false;
			return returnValid;
		}
		var data = timeShareGrid.getItems();
		var j = 0;
		for(var i = 0;i < data.length;i++){
			if(data[i].sn == eValidator.rowid ){
				j = i;
				break;
			}
		}
		if(j+1 == data.length && value !='24:00'){
				returnValid.message = "分时时段最终的结束时间必须为24:00,不能更改";
				returnValid.isValid = false;
				return returnValid;
		}
		if(j!=0){
			var preEndTime = timeShareGrid.getCell(data[j-1].sn, "endTime");
			if(!LS.isEmpty(preEndTime) && !tranTime(preEndTime,value)){
					returnValid.message = "当前的结束时间必须大于开始时间";
					returnValid.isValid = false;
					return returnValid;
			}
		}
		if(j+1!=data.length){
			var nextEndTime = timeShareGrid.getCell(data[j+1].sn, "endTime");
			if(!LS.isEmpty(nextEndTime) && !tranTime(value,nextEndTime)){
					returnValid.message = "当前的结束时间必须小于下一个时间段的结束时间";
					returnValid.isValid = false;
					return returnValid;
			}
		}
		if(returnValid.isValid && j+1 != data.length){
			timeShareGrid.setCell(data[i+1].sn, "beginTime", value);
		}
		return returnValid;
	}
	
	function tranTime(minTime,maxTime){
		var min = minTime.split(":");
		var max = maxTime.split(":");
		var returnValue = true;
		if(Number(max[0]) < Number(min[0]))
			returnValue = false;
		if(Number(max[0]) == Number(min[0]) && Number(max[1]) <= Number(min[1]))
			returnValue = false;
		return returnValue;
	}
	
	function doDelTimeShareGrid(){
	   var item = timeShareGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要删除的分时时段记录");
    		return;
	   }
	   var items = timeShareGrid.getItems();
	   if(items.length == 1){
    		LS.message("error","至少需要有一条记录");
    		return;
	   }
	   var newItems = [];
	   for(var i=0;i < items.length;i++){
	   		if(item.sn == items[i].sn){
	   			if(i==0){
	   				items[1].beginTime = '00:00';
	   			}else if(i==items.length-1){
	   				newItems[i-1].endTime = '24:00';
	   			}else{
	   				items[i+1].beginTime = items[i-1].endTime;
	   			}
	   		}else{
	   			newItems.push(items[i]);
	   		}
	   }
	   timeShareGrid.removeAllItems();
		for(var i=0;i < newItems.length;i++){
			timeShareGrid.addRow(newItems[i],"last");
		}
	}
	
	//分时时段设置--计费单价验证
	function priceValidate(value){
    	var bMoney = value;
		var isValid = true;
    	if(!LS.isEmpty(bMoney)){
	    	var i = bMoney.indexOf(".");
	    	var str = "";
	    	if(i>=0){
	    		str = bMoney.substring(i+1);
	    	}
	    	isValid = (str.length <= 4);
    	}
    	return {
            isValid : isValid,
            message : "小数位不能超过四位"
        }
    }
    
	//========================================服务项目=========================================================
	function serviceGridSearch(){
		if(!LS.isEmpty(attachItemNos.getValue())){
			var params = {
				itemNo:attachItemNos.getValue(),
				price:attachItemPrices.getValue(),
				freeNum:freeNum.getValue()
			};
			serviceGrid.query('~/billing/chargeServiceItem/querySerItemsByNo',params,function(){
			var items = serviceGrid.getItems();
			if(items) {
				for(var i=0;i< items.length;i++) {
					serviceGrid.editRow(items[i].itemNo);
				}
			}
			});
		}
	}
	
	function doAddSerGrid(){
		var items = serviceGrid.getItems();
		var nos = "";
		for(var i =0;i < items.length;i++){
			if(i!=items.length-1){
				nos += items[i].itemNo+",";
			}else
				nos += items[i].itemNo;
		}
		LS.dialog("~/billing/chargeServiceItem/select?itemStatus=1&unUseItemNos="+nos,"选择服务项目",1000, 400,true, null);
	}
	
	window.setChargeSer = setChargeSer;
	function setChargeSer(item){
		serviceGrid.appendItem(item);
		var items = serviceGrid.getItems();
		if(items) {
			for(var i=0;i< items.length;i++) {
				serviceGrid.editRow(items[i].itemNo);
			}
		}
	}
	
	function doDelSerGrid(){
		var item = serviceGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要删除的服务项目");
    		return;
	   }
	   serviceGrid.removeItem(item);
	}
	
	//服务项目设置--计费单价验证
	function servicePriceValidate(value){
    	var bMoney = value;
		var isValid = true;
    	if(!LS.isEmpty(bMoney)){
	    	var i = bMoney.indexOf(".");
	    	var str = "";
	    	if(i>=0){
	    		str = bMoney.substring(i+1);
	    	}
	    	isValid = (str.length <= 2);
    	}
    	return {
            isValid : isValid,
            message : "小数位不能超过两位"
        }
    }
    
	//====================================其他=============================================================
    function getPile(){
       LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
      // LS.dialog("~/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
    }
    
    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }
    
    window.setStation = setStation;
    function setStation(item) {
       stationId.setValue(item.stationId);  
       stationName.setValue(item.stationName);
	   orgCode.setValue(item.orgCode);
    }
    
    function changePriceTrShow(){
    	var mode = chargeMode.getValue();
    	if(mode == '0201'){//标准、显示priceTr
    		$('#priceTr').show();
    		$('#timeTr').hide();
    	}else{//隐藏priceTr
    		$('#priceTr').hide();
    		$('#timeTr').show();
    	}
    }
    
    function changeTimeFlag(sn,item,itemElement){

    	var flag = itemElement.timeFlag?itemElement.timeFlag.value:"";
    	if(LS.isEmpty(flag)){
    		return;
    	}
    	var price = null;
    	var items = timeShareGrid.getItems();
		for(var i =0;i < items.length;i++){
			if( items[i].timeFlag == flag && !LS.isEmpty(items[i].price)){
				price = items[i].price;
				break;
			}
		}
		for(var i =0;i < items.length;i++){
			if( items[i].timeFlag == flag && LS.isEmpty(items[i].price)){
				timeShareGrid.setCell(items[i].sn, "price", price);
			}
		}
    }
    
    function changePrice(sn,item,itemElement){

    	var flag = itemElement.timeFlag?itemElement.timeFlag.value:"";
    	if(LS.isEmpty(flag)){
    		return;
    	}
		var price = null;
		var items = timeShareGrid.getItems();
		for(var i =0;i < items.length;i++){
			if( items[i].timeFlag == flag && !LS.isEmpty(items[i].price)){
				price = items[i].price;
				break;
			}
		}
		for(var i =0;i < items.length;i++){
			if( items[i].timeFlag == flag && LS.isEmpty(items[i].price)){
				timeShareGrid.setCell(items[i].sn, "price", price);
			}
		}
    }
    
    </ls:script>
</ls:body>
</html>