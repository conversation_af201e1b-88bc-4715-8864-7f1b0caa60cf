<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.xpcharge.dao.IXpChargeBillingConfDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		<result column="SYSTEM_ID" property="systemId" />
		<result column="ORG_CODE" property="orgCode" />
		<result column="STATION_ID" property="stationId" />
		<result column="PILE_ID" property="pileId" />
		<result column="CHC_NO" property="chcNo" />
		<result column="CHC_NAME" property="chcName" />
		<result column="CHC_REMARK" property="chcRemark" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="CHARGE_METHOD" property="chargeMethod" />
		<result column="PERIOD_SPLIT_NUM" property="periodSplitNum" />
		<result column="ATTACH_ITEM_NOS" property="attachItemNos" />
		<result column="ATTACH_ITEM_PRICES" property="attachItemPrices" />
		<result column="DEPOSIT_ITEM_NOS" property="depositItemNos" />
		<result column="DEPOSIT_ITEM_PRICES" property="depositItemPrices" />
		<result column="CHARGE_NUM" property="chargeNum" />
		<result column="CHARGE_UNIT" property="chargeUnit" />
		<result column="CHARGE_PRICE" property="chargePrice" />
		<result column="OPER_NO" property="operNo" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="CHC_STATUS" property="chcStatus" />
		<result column="STATION_NAME" property="stationName" />
		<result column="BILL_CTL_MODE" property="billCtlMode" />
		<result column="GATHER_DATA_TYPE" property="gatherDataType" />
		<result column="EFT_DATE" property="eftDate" />
		<result column="INV_DATE" property="invDate" />
		 <result column="FREE_NUM" property="freeNum" />
		 <result column="UP_CHC_NO" property="upChcNo" />
		 <result column="BILLING_TAG" property="billingTag" />
		 <result column="ITEM_CHARGE_MODE" property="itemChargeMode" />
		 <result column="DATA_OPER_TIME" property="dataOperTime" />

	 </resultMap>
	 
	 <sql id="queryXpChargeBillingConfWhere">
		<if test="chcNo !=null and chcNo !=''">
		    and a.CHC_NO = #{chcNo}
		</if>
		 <if test="chcName !=null and chcName !=''">
			 and a.CHC_NAME like concat('%',#{chcName},'%')
		 </if>
		<if test="chcStatus !=null and chcStatus !=''">
		    and a.CHC_STATUS =#{chcStatus}
		</if>
		 <if test="chcStatusList !=null and chcStatusList.size() > 0">
			 and a.CHC_STATUS IN
			 <foreach item="item" index="index" collection="chcStatusList" open="(" separator="," close=")">
				 #{item}
			 </foreach>
		 </if>
		 <if test="billCtlMode !=null and billCtlMode !=''">
			 and a.BILL_CTL_MODE =#{billCtlMode}
		 </if>
		 <if test="upChcNo !=null and upChcNo !=''">
			 and a.UP_CHC_NO like concat('%',#{upChcNo},'%')
		 </if>
		<if test="endEftDate !=null and endEftDate !=''">
		    and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
		</if>
		<if test="stationIds !=null">
		    and a.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
		</if>
		 <if test="orgCode !=null and orgCode !=''">
			 and a.ORG_CODE like concat(#{orgCode},'%')
		 </if>
		 <if test="unChcStatus !=null and unChcStatus !=''">
			 and <![CDATA[ a.CHC_STATUS <> #{unChcStatus} ]]>
		 </if>
	 </sql>

	 <sql id="xpChargeBillingConfItems">
	 	a.STATION_ID,a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
	 	a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
	 	a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
	 	a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
	 	a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO,ITEM_CHARGE_MODE,
	 	a.BILLING_TAG,a.DATA_OPER_TIME,
	 	(SELECT b.CHC_NAME from e_charge_billing_conf b WHERE  b.chc_no=a.UP_CHC_NO) upChcName
	 </sql>
	 
	<!-- 查询充电计费配置E_CHARGE_BILLING_CONF-->
	<select id="queryXpChargeBillingConfs" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select  
			<include refid="xpChargeBillingConfItems"></include>
		from E_CHARGE_BILLING_CONF a
		<where>
			<include refid="queryXpChargeBillingConfWhere"></include>
			<if test="stationIdIsNull !=null and stationIdIsNull !=''">
				and (a.STATION_ID IS NULL or STATION_ID ='')
			</if>
			<if test="custIdIsNull !=null and custIdIsNull !=''">
				and (a.CUST_ID IS NULL or CUST_ID ='')
			</if>
			<if test="orgCodeList !=null">
				and a.ORG_CODE in
				<foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	
	<!-- 查询充电计费配置条数E_CHARGE_BILLING_CONF -->
	<select id="queryXpChargeBillingConfsNum" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition" resultType="int">
		select count(1)
		from E_CHARGE_BILLING_CONF a 
		<!--fenge LEFT JOIN C_PILE_STATION b on a.STATION_ID = b.STATION_ID -->
		<where>
			<include refid="queryXpChargeBillingConfWhere"></include>
			<if test="stationIdIsNull !=null and stationIdIsNull !=''">
				and (a.STATION_ID IS NULL or STATION_ID ='')
			</if>
			<if test="custIdIsNull !=null and custIdIsNull !=''">
				and (a.CUST_ID IS NULL or CUST_ID ='')
			</if>
			<if test="orgCodeList !=null">
				and a.ORG_CODE in
				<foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<!--fenge <include refid="queryPileStationWhere"></include> -->
		</where>
	</select>


	<!--通过站点查询计费配置-->
	<select id="queryXpChargeBillingConfInfo" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select
			b.STATION_ID,a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
			a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
			a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
			a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
			a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO
		from E_CHARGE_BILLING_CONF a ,e_station_billing b
		<where>
			 a.CHC_NO=b.CHC_NO
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO = #{chcNo}
			</if>
			<if test="chcName !=null and chcName !=''">
				and a.CHC_NAME like concat('%',#{chcName},'%')
			</if>
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="upChcNo !=null and upChcNo !=''">
				and a.UP_CHC_NO like concat('%',#{upChcNo},'%')
			</if>
			<if test="stationId !=null and stationId !=''">
				and b.STATION_ID =#{stationId}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="stationIds !=null">
				and b.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE like concat(#{orgCode},'%')
			</if>
			<if test="unChcStatus !=null and unChcStatus !=''">
				and <![CDATA[ a.CHC_STATUS <> #{unChcStatus} ]]>
			</if>
		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>


	<!--通过站点查询计费配置-->
	<select id="queryEffectChargeBillingConfs" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select
			a.CHC_NO,
			a.CHC_NAME,
			a.CHC_REMARK,
			a.CHARGE_MODE,
			a.CHARGE_METHOD,
			a.PERIOD_SPLIT_NUM,
			a.ATTACH_ITEM_NOS,
			a.ATTACH_ITEM_PRICES,
			a.DEPOSIT_ITEM_NOS,
			a.DEPOSIT_ITEM_PRICES,
			a.CHARGE_NUM,
			a.CHARGE_UNIT,
			a.CHARGE_PRICE,
			a.OPER_NO,
			a.CREATE_TIME,
			a.CHC_STATUS,
			a.SYSTEM_ID,
			a.BILL_CTL_MODE,
			a.GATHER_DATA_TYPE,
			a.EFT_DATE,
			a.INV_DATE,
			a.ORG_CODE,
			a.FREE_NUM,
			a.UP_CHC_NO
		from E_CHARGE_BILLING_CONF a
		<where>
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS = #{chcStatus}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[ a.EFT_DATE  <= #{endEftDate} ]]>
			</if>
		</where>
	</select>


</mapper>