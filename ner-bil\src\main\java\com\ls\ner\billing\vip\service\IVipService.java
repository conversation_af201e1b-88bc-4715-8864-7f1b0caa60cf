package com.ls.ner.billing.vip.service;

import com.ls.ner.billing.vip.bo.RegularVipRankBo;
import com.ls.ner.billing.vip.bo.VipBo;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.condition.VipRecordCondition;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
public interface IVipService {

    RegularVipRankBo getRegularVipConfigById(String rankId);

    List<RegularVipRankBo> getRegularVipConfigList();

    void editRegularVipConfig(RegularVipRankBo regularVipRankBo);

    VipBo getVipConfigById(String vipId);

    List<VipBo> getVipConfigList();

    void insertVipConfig(VipBo bo);

    void updateVipConfig(VipBo bo);

    void updateVipConfigStatus(VipBo bo);

    List<VipPayRecordBo> getRecordList(VipRecordCondition condition);

    int getRecordListCount(VipRecordCondition condition);

}
