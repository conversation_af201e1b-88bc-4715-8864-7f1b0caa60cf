package com.ls.ner.billing.charge.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;



/**
 * <AUTHOR>
 * @dateTime 2016-03-31
 * @description 充电实时计费分时明细  E_CHARGE_BILLING_PERIODS
 */
public interface IChargeBillingPeriodsDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 查询充电实时计费分时明细  E_CHARGE_BILLING_PERIODS by appNo
	 */
//	public List<ChargeBillingRltPeriodsBo> queryChargeBillingPeriod(String appNo);
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 查询充电实时计费分时明细  E_CHARGE_BILLING_PERIODS by appNo
	 */
	public List<ChargeBillingRltPeriodsBo> queryChargeBillingPeriod(Map inMap);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 新增充电实时计费分时明细  E_CHARGE_BILLING_PERIODS
	 */
	public void insertChargeBillingPeriods(ChargeBillingRltPeriodsBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 更新充电实时计费分时明细  E_CHARGE_BILLING_PERIODS
	 */
	public void updateChargeBillingPeriods(ChargeBillingRltPeriodsBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 删除充电实时计费分时明细  E_CHARGE_BILLING_PERIODS
	 */
	public void deleteChargeBillingPeriod(String appNo);

}
