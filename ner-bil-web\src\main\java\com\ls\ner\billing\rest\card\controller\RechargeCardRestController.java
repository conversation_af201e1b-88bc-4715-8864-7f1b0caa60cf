package com.ls.ner.billing.rest.card.controller;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.base.service.ITokenService;
import com.ls.ner.billing.gift.service.IRechargeCardService;
import com.ls.ner.def.api.account.vo.IntegralCondition;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.rest.card.controller
 * @ClassName: RechargeCardController
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/5/9 11:37
 * @Version: 1.0
 */
@Controller
@RequestMapping(value = "/api/rechargeCard")
public class RechargeCardRestController {

    private static final Logger logger = LoggerFactory.getLogger(RechargeCardRestController.class);
    @ServiceAutowired(value="rechargeCardService")
    private IRechargeCardService iRechargeCardService;

    @ServiceAutowired(value = "tokenService")
    private ITokenService tokenService;


    @RequestMapping(value = "/v0.1/doCharge")
    public @ResponseBody
    Object doCharge(@RequestBody Map<String,String> map, HttpServletRequest request) {
        Map<String, Object> resultMap = MapUtils.createSucResult();
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String mobile = tokenMsg.get("mobile");
            String custId = tokenMsg.get("custId").toString();
            map.put("mobile",mobile);
            map.put("custId",custId);
            map.put("reqTyp","01"); //channl 渠道 01	APP  07微信小程序
            resultMap = iRechargeCardService.rechargeCardCode(map);
        }catch (Exception e) {
            logger.error("APP充值卡卡密兑换失败:{}", e);
            resultMap = MapUtils.createFailResult("兑换失败！");
        }
        return resultMap;
    }


    /**
     * 我的充值卡
     * @param map
     * @param request
     * @return
     */
    @RequestMapping(value = "/v0.1/myRechargeCards")
    public @ResponseBody
    Object myRechargeCards() {
        Map<String, Object> resultMap = MapUtils.createSucResult();
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            Map<String,String> imap = new HashMap<>();
            String custId = tokenMsg.get("custId").toString();
            imap.put("custId",custId);
            resultMap = iRechargeCardService.myRechargeCardInfos(imap);
        }catch (Exception e) {
            logger.error("我的充值卡查询失败:{}", e);
            resultMap = MapUtils.createFailResult("查询失败！");
        }
        return resultMap;
    }
}
