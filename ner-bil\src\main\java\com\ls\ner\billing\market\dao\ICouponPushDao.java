package com.ls.ner.billing.market.dao;

import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPushBo;
import com.ls.ner.billing.market.bo.CouponPutBo;
import com.ls.ner.billing.market.bo.CouponPutDetBo;
import com.ls.ner.billing.market.vo.MarketCondition;

import java.util.List;

/**
 * 描述:优惠券推广
 * ICouponPushService.java
 * 作者：biaoxiangd
 * 创建日期：2017/7/6 14:48
 **/
public interface ICouponPushDao {

	/**
	 * 描述:获取已推广的优惠券
	 *
	 * @param: [condition]
	 * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
	 * 创建人:biaoxiangd
	 * 创建时间:2017/7/6 14:50
	 */
	List<CouponPushBo> getCouponsPush(MarketCondition condition) throws Exception;

	int getCouponsPushCount(MarketCondition condition) throws Exception;

	/**
	 * 描述:保存推广的优惠券
	 *
	 * @param: [condition]
	 * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
	 * 创建人:biaoxiangd
	 * 创建时间:2017/7/6 14:50
	 */
	int saveCouponPush(CouponPushBo bo) throws Exception;

	/**
	 * 描述:删除已推广的优惠券
	 *
	 * @param: [condition]
	 * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
	 * 创建人:biaoxiangd
	 * 创建时间:2017/7/6 14:50
	 */
	int delCouponPush(MarketCondition condition) throws Exception;

}
