package com.ls.ner.billing.charge.service.impl;

import com.alibaba.rocketmq.client.consumer.DefaultMQPushConsumer;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerOrderly;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.mq.MqConstants;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.dao.IChcbillPileSendDao;
import com.ls.ner.billing.charge.dao.IChcbillPileSendLogDao;
import com.ls.ner.billing.charge.dao.IChcbillSendDao;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.billing.xpcharge.dao.IXpStationSendDao;
import com.ls.ner.util.AssertUtil;
import com.ls.ner.util.RocketMQTools;
import com.ls.ner.util.SpringRpcBeanUtil;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.ls.ner.util.tenant.TenantUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.framework.config.FrameworkPropertyConfigurer;
import com.pt.poseidon.param.api.ISysParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Scope;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Scope("singleton")
public class ChcbillSendResultMain implements ApplicationListener<ContextRefreshedEvent> {
	private static final Logger logger = LoggerFactory.getLogger(ChcbillSendResultMain.class);
	private static String CHARGE_VERSON;
	@Autowired
	private IChcbillPileSendLogDao chcbillPileSendLogDao;

	@Autowired
	private IChcbillPileSendDao chcbillPileSendDao;
	
	@Autowired
	private IChcbillSendDao chcbillSendDao;
	
	@Autowired
	private ChargeBillingRltUtil cbRltUtil;

	@Autowired
	private IXpStationSendDao xpStationSendDao;

	@ServiceAutowired(value="archivesRpcService", serviceTypes=ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;
	
	@Autowired
	private ChargingPriingRequestHandler handler;

	@ServiceAutowired(serviceTypes= ServiceType.RPC)
	private ISysParamService sysParamService;

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
        if ("Root WebApplicationContext".equals(event.getApplicationContext().getDisplayName())) {
			logger.info(">>>>> ChcbillSendResultMain init begin...");
			try {
				String name = event.getApplicationContext().getDisplayName();
				logger.info(">>>>> applicationContext displayName:{}", name);
				initChargingPriingSendRlt();

				initChargingPriingRequest();

				initChargingPriingVerify();

				initPushPileLoginMsg();

				initPileCharginPeriodsRlt();
				logger.info(">>>>> ChcbillSendResultMain init end...");
			} catch (Exception e) {
				logger.error(">>>>> ChcbillSendResultMain init error:", e);
			}
		}
	}

	/**
	 * @param
	 * @description 充电桩时段参数下发结果
	 * <AUTHOR>
	 * @create 2018-08-14 15:18:42
	 */
	private void initPileCharginPeriodsRlt() {
		logger.info(">>>>> PileCharginPeriodsRlt-充电桩时段参数下发结果-监听 begin...");
		try {
			String namesrvAddr = MqConstants.getMQ_NAMESRV_ADDRESS();
			final String topic = MqConstants.PILE_CHARGEIN_PERIODS_RLT;
			final String consumerGropName = MqConstants.CPMQ_CONSUMER_GROUPNAME + topic;
			String consumerName = topic + "_consumer";
			DefaultMQPushConsumer pushConsumer = RocketMQTools.connPushConsumer(namesrvAddr,consumerGropName, consumerName, MqConstants.CPMQ_PRE_MSG_SIZE, null);
			RocketMQTools.pushConsumer(pushConsumer, topic, null, new MessageListenerOrderly() {
				@Override
				public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
					context.setAutoCommit(true);
					try {
						if (msgs != null && msgs.size() > 0) {
							for (Message msg : msgs) {
								String msgBody = StringUtil.nullToString(new String(msg.getBody()));
								if (!msgBody.equals("")) {
									Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
									logger.debug(">>>>> PileCharginPeriodsRlt-充电桩时段参数下发结果监听-消息非空，consumerGropName:{}, msgs:{}", consumerGropName, lmap);
								}
							}
						}
					} catch (Exception e) {
						logger.error("",e);
					}
					return ConsumeOrderlyStatus.SUCCESS;
				}
			});
		} catch (Exception e) {
			logger.error(">>>>> PileCharginPeriodsRlt-充电桩时段参数下发结果-监听 error:", e);
		}
	}

	/**
	 * 判断是否用新版计费
	 * @return
	 */
	public  boolean isNewChargeVerson(){
		Boolean flag=false;
			CHARGE_VERSON = sysParamService.getSysParamsValues("chargeVesion");
		if ("toMoreStation".equals(CHARGE_VERSON)){
			flag=true;
		}
		return  flag;
	}

	/**
	  *@Description: 充电计费模型下发结果-旧版
	  *@Author: qianghuang
	  *@Time: 2017/11/16 14:35
	  */
	public void initChargingPriingSendRlt() {
		logger.info(">>>>> chargingPriingSendRlt充电计费模型下发结果-监听 begin...");
		try {
			String namesrvAddr = MqConstants.getMQ_NAMESRV_ADDRESS();
			final String topic = MqConstants.CPMQ_CHARGING_PRIING_SEND_RLT;
			final String consumerGropName = MqConstants.CPMQ_CONSUMER_GROUPNAME + topic;
			String consumerName = topic + "_consumer";
			DefaultMQPushConsumer pushConsumer = RocketMQTools.connPushConsumer(namesrvAddr,consumerGropName, consumerName, MqConstants.CPMQ_PRE_MSG_SIZE, null);
			RocketMQTools.pushConsumer(pushConsumer, topic, null, new MessageListenerOrderly() {
				@Override
				public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
					context.setAutoCommit(true);
					try {
						if (msgs != null && msgs.size() > 0) {
							logger.debug(">>>>> chargingPriingSendRlt充电计费模型下发结果监听-消息非空，consumerGropName:{}, msgs.size:{}", consumerGropName, msgs.size());
							if(isNewChargeVerson()){
								handelChargingPriingSendRltNew(msgs);
							}else{
								handelChargingPriingSendRltOld(msgs);
							}
						}
					} catch (Exception e) {
						logger.error("",e);
					}
					return ConsumeOrderlyStatus.SUCCESS;
				}
			});
		} catch (Exception e) {
			logger.error(">>>>> 充电计费模型下发结果-监听 error:", e);
		}
	}

	/**
	 * 处理充电计费模型下发结果-旧版
	 * @param msgs
	 */
	public void  handelChargingPriingSendRltOld(List<MessageExt> msgs){
		for (Message msg : msgs) {
			String msgBody = StringUtil.nullToString(new String(msg.getBody()));
			if (!msgBody.equals("")) {
				Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
				TenantUtil.setTenantId(StringUtil.nullToString(lmap.get("tenantId")));
				if (lmap != null && lmap.size() > 0) {
					String equipType = cbRltUtil.returnNotNull(lmap.get("equipType"));
					String dataSource = cbRltUtil.returnNotNull(lmap.get("dataSource"));
					String ret = cbRltUtil.returnNotNull(lmap.get("ret"));
					String msgStr = cbRltUtil.returnNotNull(lmap.get("msg"));
					if("0202".equals(equipType)){//充电枪
						if("1".equals(dataSource)){//枪口ID

						}else if("2".equals(dataSource)){//桩编号
							ChcbillPileSendLogBo logBo = new ChcbillPileSendLogBo();
							logBo.setChcNo(cbRltUtil.returnNotNull(lmap.get("pricingId")));
							logBo.setPileNo(cbRltUtil.returnNotNull(lmap.get("equipNo")));
							if("200".equals(ret)){//下发成功
								logBo.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);
							}else{//下发失败
								logBo.setPileSendStatus(ChargeConstant.successFlag.FAIL);
								logBo.setFailReason(msgStr);
							}
							chcbillPileSendLogDao.updateChcbillPileSendLogStatus(logBo);
							Map successMap = chcbillPileSendDao.queryPileSendSuccessCount(logBo.getChcNo(),null);
							int success = successMap.get("success")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("success")));
							int count = successMap.get("count")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("count")));
							ChcbillSendBo sendBo = new ChcbillSendBo();
							sendBo.setChcNo(logBo.getChcNo());
							if(success == 0){//发送失败
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.FAIL);
							}else if(success < count){//部分成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.PART);
							}else{//成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.SUCCESS);
							}
							chcbillSendDao.updateChcbillSend(sendBo);
						}else if("3".equals(dataSource)){//桩id
							String pileId = cbRltUtil.returnNotNull(lmap.get("equipId"));
							ChcbillPileSendLogBo logBo = new ChcbillPileSendLogBo();
							logBo.setChcNo(cbRltUtil.returnNotNull(lmap.get("pricingId")));
							logBo.setPileNo(archivesRpcService.queryNoByEquipId(pileId));
							if("200".equals(ret)){//下发成功
								logBo.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);
							}else{//下发失败
								logBo.setPileSendStatus(ChargeConstant.successFlag.FAIL);
								logBo.setFailReason(msgStr);
							}
							chcbillPileSendLogDao.updateChcbillPileSendLogStatus(logBo);
							Map successMap = chcbillPileSendDao.queryPileSendSuccessCount(logBo.getChcNo(),null);
							int success = successMap.get("success")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("success")));
							int count = successMap.get("count")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("count")));
							ChcbillSendBo sendBo = new ChcbillSendBo();
							sendBo.setChcNo(logBo.getChcNo());
							if(success == 0){//发送失败
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.FAIL);
							}else if(success < count){//部分成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.PART);
							}else{//成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.SUCCESS);
							}
							chcbillSendDao.updateChcbillSend(sendBo);
						}
					}else if("0302".equals(equipType)){//补电枪

					}
				}
			}
		}

	}

	/**
	 * 处理充电计费模型下发结果-新版
	 * @param msgs
	 */
	public void  handelChargingPriingSendRltNew(List<MessageExt> msgs){
		for (Message msg : msgs) {
			String msgBody = StringUtil.nullToString(new String(msg.getBody()));
			if (!msgBody.equals("")) {
				Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
				logger.debug("=============充电计费模型下发结果lmap:{}", lmap);
				TenantUtil.setTenantId(StringUtil.nullToString(lmap.get("tenantId")));
				if (lmap != null && lmap.size() > 0) {
					String equipType = cbRltUtil.returnNotNull(lmap.get("equipType"));
					String dataSource = cbRltUtil.returnNotNull(lmap.get("dataSource"));
					String ret = cbRltUtil.returnNotNull(lmap.get("ret"));
					String msgStr = cbRltUtil.returnNotNull(lmap.get("msg"));
					if("0202".equals(equipType)){//充电枪
						if("1".equals(dataSource)){//枪口ID

						}else if("2".equals(dataSource)){//桩编号
							ChcbillPileSendLogBo logBo = new ChcbillPileSendLogBo();
							logBo.setChcNo(cbRltUtil.returnNotNull(lmap.get("pricingId")));
							logBo.setPileNo(cbRltUtil.returnNotNull(lmap.get("equipNo")));
							if("200".equals(ret)){//下发成功
								logBo.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);
							}else{//下发失败
								logBo.setPileSendStatus(ChargeConstant.successFlag.FAIL);
								logBo.setFailReason(msgStr);
							}
							chcbillPileSendLogDao.updateChcbillPileSendLogStatus(logBo);
							//判断是桩计费还是站点计费
							Map<String, Object> searchMap = new HashMap<String, Object>();
							searchMap.put("pileNo",lmap.get("equipNo"));
							Map pileMap=archivesRpcService.queryPileMap(searchMap);

							Map sercher=new HashMap();
							sercher.put("pileId",pileMap.get("pileId"));
							int pileCfgNum =xpStationSendDao.queryPileBillCount(sercher);
							Map successMap= new HashMap();
							ChcbillSendBo sendBo = new ChcbillSendBo();
							sendBo.setChcNo(logBo.getChcNo());
							if (pileCfgNum>=1){//桩计费配置--只有成功或失败
								successMap = chcbillPileSendDao.queryPileSendSuccessCount(logBo.getChcNo(),lmap.get("equipNo").toString());
								sendBo.setPileNo(lmap.get("equipNo").toString());
							}else{//站点计费
								List<Map<String, String>> piles = archivesRpcService.queryPilesByStationId(pileMap.get("stationId").toString(), null);
								List pileNoList=new ArrayList();
								for(Map pile : piles) {
									pileNoList.add(pile.get("pileNo"));
								}
								Map<String, Object> queryMap = new HashMap<String, Object>();
								queryMap.put("pileNos",pileNoList);
								queryMap.put("chcNo",logBo.getChcNo());
								successMap = chcbillPileSendDao.queryXpPileSendSuccessCount(queryMap);
								sendBo.setStationId(pileMap.get("stationId").toString());
							}

							int success = successMap.get("success")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("success")));
							int count = successMap.get("count")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("count")));
							if(success == 0){//发送失败
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.FAIL);
							}else if(success < count){//部分成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.PART);
							}else{//成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.SUCCESS);
							}
							chcbillSendDao.updateChcbillSend(sendBo);
						}else if("3".equals(dataSource)){//桩id
							String pileId = cbRltUtil.returnNotNull(lmap.get("equipId"));
							ChcbillPileSendLogBo logBo = new ChcbillPileSendLogBo();
							logBo.setChcNo(cbRltUtil.returnNotNull(lmap.get("pricingId")));
							String pileNo=archivesRpcService.queryNoByEquipId(pileId);
							logBo.setPileNo(pileNo);
							if("200".equals(ret)){//下发成功
								logBo.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);
							}else{//下发失败
								logBo.setPileSendStatus(ChargeConstant.successFlag.FAIL);
								logBo.setFailReason(msgStr);
							}
							chcbillPileSendLogDao.updateChcbillPileSendLogStatus(logBo);

							//判断是桩计费还是站点计费
							Map<String, Object> searchMap = new HashMap<String, Object>();
							searchMap.put("pileNo",pileNo);
							Map pileMap=archivesRpcService.queryPileMap(searchMap);

							Map sercher=new HashMap();
							sercher.put("pileId",pileId);
							int pileCfgNum =xpStationSendDao.queryPileBillCount(sercher);
							Map successMap= new HashMap();
							ChcbillSendBo sendBo = new ChcbillSendBo();
							sendBo.setChcNo(logBo.getChcNo());
							if (pileCfgNum>=1){//桩计费配置--只有成功或失败
								successMap = chcbillPileSendDao.queryPileSendSuccessCount(logBo.getChcNo(),pileNo);
								sendBo.setPileNo(pileNo);
							}else{//站点计费
								List<Map<String, String>> piles = archivesRpcService.queryPilesByStationId(pileMap.get("stationId").toString(), null);
								List pileNoList=new ArrayList();
								for(Map pile : piles) {
									pileNoList.add(pile.get("pileNo"));
								}
								Map<String, Object> queryMap = new HashMap<String, Object>();
								queryMap.put("pileNos",pileNoList);
								queryMap.put("chcNo",logBo.getChcNo());
								successMap = chcbillPileSendDao.queryXpPileSendSuccessCount(queryMap);
								sendBo.setStationId(pileMap.get("stationId").toString());
							}

							int success = successMap.get("success")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("success")));
							int count = successMap.get("count")==null?0:Integer.parseInt(cbRltUtil.returnNotNull(successMap.get("count")));
							if(success == 0){//发送失败
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.FAIL);
							}else if(success < count){//部分成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.PART);
							}else{//成功
								sendBo.setSendStatus(ChargeConstant.chcBillSendStatus.SUCCESS);
							}
							chcbillSendDao.updateChcbillSend(sendBo);
						}
					}else if("0302".equals(equipType)){//补电枪

					}
				}
			}
		}

	}


	/**
	 * 请求下发计费模型。topic:chargingPriingRequest
	 * */
	private void initChargingPriingRequest() throws Exception{
		logger.info(">>>>> chargingPriingRequest请求下发计费模型-监听 begin...");
		String namesrvAddr = MqConstants.getMQ_NAMESRV_ADDRESS();
		final String topic = MqConstants.CPMQ_CHARGING_PRIING_REQUEST;
		final String consumerGropName = MqConstants.CPMQ_CONSUMER_GROUPNAME + topic;
		String consumerName = topic + "_consumer";
		DefaultMQPushConsumer pushConsumer = RocketMQTools.connPushConsumer(namesrvAddr,consumerGropName, consumerName, MqConstants.CPMQ_PRE_MSG_SIZE, null);
		RocketMQTools.pushConsumer(pushConsumer, topic, null, new MessageListenerOrderly() {
			@Override
			public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
				context.setAutoCommit(true);
				try {
					if (msgs != null && msgs.size() > 0) {
						logger.debug(">>>>> chargingPriingRequest请求下发计费模型监听-消息非空，consumerGropName:{}, msgs.size:{}", consumerGropName, msgs.size());
						for (Message msg : msgs) {
							String msgBody = StringUtil.nullToString(new String(msg.getBody()));
							if (!msgBody.equals("")) {
								logger.debug(">>>>> 消息非空，msgBody:{}", msgBody);
								Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
								if (lmap != null && lmap.size() > 0) {
									TenantUtil.setTenantId(StringUtil.nullToString(lmap.get("tenantId")));
									String equipType = cbRltUtil.returnNotNull(lmap.get("equipType"));
									String dataSource = cbRltUtil.returnNotNull(lmap.get("dataSource"));
									if("0202".equals(equipType)){//充电枪
										if("1".equals(dataSource)){//枪口ID 
											String gunId = cbRltUtil.returnNotNull(lmap.get("equipId"));
											Map m = archivesRpcService.queryPileAndStationIdByGunId(gunId);
											String pileNo = cbRltUtil.returnNotNull(m.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(m.get("stationId"));
											if(isNewChargeVerson()){
												handler.newHandle(stationId, pileNo);
											}else{
												handler.handle(stationId, pileNo);
											}

										}else if("2".equals(dataSource)){//桩编号
											Map<String,Object> m = new HashMap<String, Object>();
											m.put("equipNo", cbRltUtil.returnNotNull(lmap.get("equipNo")));
											m.put("operatorId", cbRltUtil.returnNotNull(lmap.get("operatorId")));
											Map pile = archivesRpcService.queryPileByPileCondition(m);
											String pileNo = cbRltUtil.returnNotNull(pile.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(pile.get("stationId"));
											if(isNewChargeVerson()){
												handler.newHandle(stationId, pileNo);
											}else{
												handler.handle(stationId, pileNo);
											}
										}else if("3".equals(dataSource)){//桩id
											Map<String,Object> m = new HashMap<String, Object>();
											m.put("equipId", cbRltUtil.returnNotNull(lmap.get("equipId")));
											m.put("operatorId", cbRltUtil.returnNotNull(lmap.get("operatorId")));
											Map pile = archivesRpcService.queryPileByPileCondition(m);
											String pileNo = cbRltUtil.returnNotNull(pile.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(pile.get("stationId"));
											if(isNewChargeVerson()){
												handler.newHandle(stationId, pileNo);
											}else{
												handler.handle(stationId, pileNo);
											}
										}
									}else if("0302".equals(equipType)){//补电枪
										
									}
								}
							}
						}
					}
				} catch (Exception e) {
					logger.error(">>>>> chargingPriingRequest请求下发计费模型-监听 error",e);
				}
				return ConsumeOrderlyStatus.SUCCESS;
			}
		});
		
	}

	/**
	 * 验证充电设备的计费模型：充电设备向运营管理系统发送当前的“计费模型ID”，运营管理系统判断是否为当前的计费模型ID，如果不是，则自动下发计费模型（即：chargingPriingSend）。
	 * */
	private void initChargingPriingVerify() throws Exception{
		logger.info(">>>>> chargingPriingVerify验证充电设备的计费模型-监听 begin...");
		String namesrvAddr = MqConstants.getMQ_NAMESRV_ADDRESS();
		final String topic = MqConstants.CPMQ_CHARGING_PRIING_VERIFY;
		final String consumerGropName = MqConstants.CPMQ_CONSUMER_GROUPNAME + topic;
		String consumerName = topic + "_consumer";
		DefaultMQPushConsumer pushConsumer = RocketMQTools.connPushConsumer(namesrvAddr,consumerGropName, consumerName, MqConstants.CPMQ_PRE_MSG_SIZE, null);
		RocketMQTools.pushConsumer(pushConsumer, topic, null, new MessageListenerOrderly() {
			@Override
			public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
				context.setAutoCommit(true);
				try {
					if (msgs != null && msgs.size() > 0) {
						//logger.debug(">>>>> 消息非空，consumerGropName:{}, msgs.size:{}", consumerGropName, msgs.size());
						for (Message msg : msgs) {
							String msgBody = StringUtil.nullToString(new String(msg.getBody()));
							logger.debug(">>>>> chargingPriingVerify验证充电设备的计费模型监听-消息非空，msgBody:{}", msgBody);
							if (!msgBody.equals("")) {
								Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
								if (lmap != null && lmap.size() > 0) {
									TenantUtil.setTenantId(StringUtil.nullToString(lmap.get("tenantId")));
									String equipType = cbRltUtil.returnNotNull(lmap.get("equipType"));
									String dataSource = cbRltUtil.returnNotNull(lmap.get("dataSource"));
									String pricingId = AssertUtil.notEmptyForString(lmap.get("pricingId"), "定价ID/计费模型ID不能为空");
									if("0202".equals(equipType)){//充电枪
										if("1".equals(dataSource)){//枪口ID 
											String gunId = cbRltUtil.returnNotNull(lmap.get("equipId"));
											Map m = archivesRpcService.queryPileAndStationIdByGunId(gunId);
											String pileNo = cbRltUtil.returnNotNull(m.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(m.get("stationId"));
											ChargeBillingConfBo conf = handler.obtainChargeBillingConf(stationId, null);
											if(!pricingId.equals(conf.getChcNo())){
												if(isNewChargeVerson()){
													handler.newHandle(stationId, pileNo);
												}else{
													handler.handle(stationId, pileNo);
												}
											}
										}else if("2".equals(dataSource)){//桩编号
											Map<String,Object> m = new HashMap<String, Object>();
											m.put("equipNo", cbRltUtil.returnNotNull(lmap.get("equipNo")));
											m.put("operatorId", cbRltUtil.returnNotNull(lmap.get("operatorId")));
											Map pile = archivesRpcService.queryPileByPileCondition(m);
											String pileNo = cbRltUtil.returnNotNull(pile.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(pile.get("stationId"));
											ChargeBillingConfBo conf = handler.obtainChargeBillingConf(stationId, null);
											if(!pricingId.equals(conf.getChcNo())){
												if(isNewChargeVerson()){
													handler.newHandle(stationId, pileNo);
												}else{
													handler.handle(stationId, pileNo);
												}
											}
										}else if("3".equals(dataSource)){//桩id
											Map<String,Object> m = new HashMap<String, Object>();
											m.put("equipId", cbRltUtil.returnNotNull(lmap.get("equipId")));
											m.put("operatorId", cbRltUtil.returnNotNull(lmap.get("operatorId")));
											Map pile = archivesRpcService.queryPileByPileCondition(m);
											String pileNo = cbRltUtil.returnNotNull(pile.get("pileNo"));
											String stationId = cbRltUtil.returnNotNull(pile.get("stationId"));
											ChargeBillingConfBo conf = handler.obtainChargeBillingConf(stationId, null);
											if(!pricingId.equals(conf.getChcNo())){
												if(isNewChargeVerson()){
													handler.newHandle(stationId, pileNo);
												}else{
													handler.handle(stationId, pileNo);
												}
											}
										}
									}else if("0302".equals(equipType)){//补电枪
										
									}
								}
							}
						}
					}
				} catch (Exception e) {
					logger.error(">>>>> chargingPriingVerify验证充电设备的计费模型-监听 error",e);
				}
				return ConsumeOrderlyStatus.SUCCESS;
			}
		});
		
	}

/**
  *@Description:
  * 充电桩登录时，采集通过指定topic把桩号传到业务boss，bil自动识别并判断是否有计费规则需要下发。
  * 前提条件：桩是否投运、所属站点是否配置有效的计费规则。
  * 判断规则：
  * 如果当前桩找到的电价下发日志，且最后一条是成功，下发时间是当日，则不处理。其他情况则下发最新计费配置。
  *@Author: qianghuang
  *@Time: 2017/11/16 14:23
  */
	public void initPushPileLoginMsg() throws Exception{
		logger.info(">>>>> pushPileLoginMsg桩上线消息推送-监听 begin...");
		String namesrvAddr = MqConstants.getMQ_NAMESRV_ADDRESS();
		final String topic = MqConstants.CPMQ_PUSH_PILE_LOGIN_MSG;
		final String consumerGropName = MqConstants.CPMQ_CONSUMER_GROUPNAME + topic;
		String consumerName = topic + "_consumer";
		DefaultMQPushConsumer pushConsumer = RocketMQTools.connPushConsumer(namesrvAddr,consumerGropName, consumerName, MqConstants.CPMQ_PRE_MSG_SIZE, null);
		RocketMQTools.pushConsumer(pushConsumer, topic, null, new MessageListenerOrderly() {
			@Override
			public ConsumeOrderlyStatus consumeMessage(List<MessageExt> msgs, ConsumeOrderlyContext context) {
				context.setAutoCommit(true);
				try {
					if (msgs != null && msgs.size() > 0) {
						//logger.debug(">>>>> 消息非空，consumerGropName:{}, msgs.size:{}", consumerGropName, msgs.size());
						for (Message msg : msgs) {
							String msgBody = StringUtil.nullToString(new String(msg.getBody()));
							logger.debug(">>>>> pushPileLoginMsg桩上线消息推送监听-消息非空，msgBody:{}", msgBody);
							if (!msgBody.equals("")) {
								Map lmap = IJsonUtil.json2Obj(msgBody, Map.class);
								if (lmap != null && lmap.size() > 0) {
									if(isNewChargeVerson()){
										chcbillSendService.newAutoIssuredPile(lmap);
									}else{
										chcbillSendService.autoIssuredPile(lmap);
									}

								}
							}
						}
					}
				} catch (Exception e) {
					logger.error(">>>>> pushPileLoginMsg桩上线消息推送-监听 error",e);
				}
				return ConsumeOrderlyStatus.SUCCESS;
			}
		});

	}

}
