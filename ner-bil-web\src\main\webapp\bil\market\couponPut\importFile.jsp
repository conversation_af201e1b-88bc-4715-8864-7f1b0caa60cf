<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="导入手机号">
	<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
</ls:head>
<ls:body>
	<ls:form name="fileForm" enctype="multipart/form-data">
		<table class="tab_search">
			<tr>
				<td align="center">
					<input id="fileData" class="text" name="fileData" type="file" />
				</td>
			</tr>
			<tr>
				<td>
					<div class="pull-right">
						<ls:button text="导入" onclick="uploadFile"></ls:button>
						<ls:button text="模板下载" onclick="downExcel"></ls:button>
					</div>
				</td>
			</tr>
		</table>
	</ls:form>

	<ls:script>
		function downExcel(){
			window.location.href = rootUrl+"billing/couponPut/downExcel";
		}

	 function validateFile(){
	   	  if(typeof(fileData) != "undefined"){
		        if(fileData.value==null || fileData.value==''){
		          return true;
		        }
		        if(!/\.(xls)$/.test(document.getElementById("fileData").value.toLowerCase())){
		         	LS.message("warn","上传文件类型必须是.xls");
		        	return true;
		      	}
		      	if(isIE()){
		      		 fileData.value=fileData.value;
		      	}else{
		      		fileData.files = fileData.files;
		      	}
	       
	      }
	   }
	 function isIE() { //ie?
     if (navigator.appName.indexOf("Explorer") > -1)
       return true;
     else
       return false;
   }
	function uploadFile(){
		if(validateFile()){
			
			return;
		}
		fileForm.submit("~/billing/couponPut/saveFile",function(data){
   		 		if(data){
	   		 		LS.message("info","导入成功");
	   		 		LS.parent().setCust(data);
	   		 		LS.window.close();
	   		 	}else{
	   		 		LS.message("error","系统异常");
	   		 	}	
		    });
	}
	</ls:script>

</ls:body>
</html>