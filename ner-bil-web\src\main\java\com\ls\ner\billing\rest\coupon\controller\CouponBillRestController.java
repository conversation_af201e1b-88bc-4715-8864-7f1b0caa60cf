package com.ls.ner.billing.rest.coupon.controller;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.base.service.ITokenService;
import com.ls.ner.billing.api.market.service.ICouponRPCService;
import com.ls.ner.billing.api.market.vo.BilCouponCondition;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.http.HttpClientUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:REST05接口 优惠券
 * CouponBillRestController.java
 * 作者：biaoxiangd
 * 创建日期：2017-06-08 17:30
 **/
@Controller
@RequestMapping(value = "/api")
public class CouponBillRestController {

    private static final Logger logger = LoggerFactory.getLogger(CouponBillRestController.class);

    @ServiceAutowired(value = "tokenService")
    private ITokenService tokenService;

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "couponRpcService")
    private ICouponRPCService couponRPCService;

    /**
     * 描述:REST05-11-01可领取的优惠券
     *
     * @param: [request]
     * @return: org.springframework.http.ResponseEntity<?>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 11:57
     */
    @RequestMapping(value = "/v0.1/coupons", method = RequestMethod.GET)
    public ResponseEntity<?> coupons(BilCouponCondition bil, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String, String> map = new HashMap<String, String>();

            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId").toString();
            String mobile = tokenMsg.get("mobile").toString();
//            String custId = "20344";
//            String mobile = "15135029957";
            map.put("custId",custId);
            map.put("mobile",mobile);

            String prodBusiType = bil.getProdBusiType();
            if (StringUtil.isNotBlank(prodBusiType)) {
                map.put("prodBusiType", prodBusiType);
            }
            String actType = bil.getActType();
            if(StringUtil.isNotEmpty(actType)){
                map.put("actType", actType);
            }else{
                map.put("actType","01");
            }
            logger.error("[REST05-11-01]可领取的优惠券接口map", JsonUtil.obj2Json(map));
            List<Map<String, Object>> list = couponService.couponsRestGET(map);
            resultMap = MapUtils.createSucResult();
            resultMap.put("cpnList", list);
            return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("[REST05-11-01]可领取的优惠券接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult(e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 描述:REST05-11-01可领取的优惠券的数量
     *
     * @param: [request]
     * @return: org.springframework.http.ResponseEntity<?>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 11:57
     */
    @RequestMapping(value = "/v0.1/coupons/getCount", method = RequestMethod.GET)
    public ResponseEntity<?> couponsGetCount(BilCouponCondition bil, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String, String> map = new HashMap<String, String>();

            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId").toString();
            String mobile = tokenMsg.get("mobile").toString();
//            String custId = "20344";
//            String mobile = "15135029957";
            map.put("custId",custId);
            map.put("mobile",mobile);

            String prodBusiType = bil.getProdBusiType();
            if (StringUtil.isNotBlank(prodBusiType)) {
                map.put("prodBusiType", prodBusiType);
            }
            String actType = bil.getActType();
            if(StringUtil.isNotEmpty(actType)){
                map.put("actType", actType);
            }else{
                map.put("actType","01");
            }
            logger.error("[REST05-11-01]可领取的优惠券接口map", JsonUtil.obj2Json(map));
            List<Map<String, Object>> list = couponService.couponsRestGET(map);
            resultMap = MapUtils.createSucResult();
            if(CollectionUtils.isEmpty(list)){
                resultMap.put("cpnCount", 0);
            }else{
                resultMap.put("cpnCount", list.size());
            }

            return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("[REST05-11-01]可领取的优惠券接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult(e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 描述:REST05-11-02 优惠券领取
     *
     * @param: [request]
     * @return: org.springframework.http.ResponseEntity<?>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 11:57
     */
    @RequestMapping(value = "/v0.1/coupons", method = RequestMethod.POST)
    public ResponseEntity<?> couponsPost(BilCouponCondition bil, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId").toString();
//            String custId = "20344";
//            String mobile = "15135029957";
            if (bil != null) {
                String mobile = String.valueOf(bil.getMobile());
                String getChannel = String.valueOf(bil.getGetChannel());
                String cpnId = String.valueOf(bil.getCpnId());
                if(StringUtil.isBlank(mobile)){
                    mobile = tokenMsg.get("mobile").toString();
                }
                logger.error("[REST05-11-02]可领取的优惠券接口mobile={},getChannel={},cpnId={}", mobile,getChannel,cpnId);
                if (StringUtil.isNotBlank(mobile) && StringUtil.isNotBlank(getChannel) && StringUtil.isNotBlank(cpnId)) {
                    Map<String, String> map = new HashMap<String, String>();
                    map.put("mobile", mobile);
                    map.put("getChannel", getChannel);
                    map.put("cpnId", cpnId);
                    map.put("custId", custId);
                    Map<String, Object> retMap = couponService.couponsRestPOST(map);
                    if (retMap != null && retMap.size() > 0) {
                        List<Map<String, Object>> custList = ( List<Map<String, Object>>) retMap.get("custList");
                        if (custList != null && custList.size() > 0) {
                            Map<String, Object> custMap = (Map<String, Object>)custList.get(0);
                            if (!"1".equals(String.valueOf(custMap.get("putFlag")))) {
                                resultMap = MapUtils.createFailResult(String.valueOf(custMap.get("failReason")));
                                return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
                            }
                        }
                    }
//                    resultMap = MapUtils.createSucResult();
                    resultMap.put("ret", 200);
                    resultMap.put("msg", "领取成功");
                    return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
                } else {
                    logger.error("[REST05-11-02]优惠券领取接口获取异常:mobile={},getChannel={},cpnId={}", mobile, getChannel, cpnId);
                    resultMap = MapUtils.createFailResult("[REST05-11-02]优惠券领取接口必填字段为空");
                    return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
                }
            } else {
                resultMap = MapUtils.createFailResult("[REST05-11-02]优惠券领取接口参数为空");
                return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            logger.error("[REST05-11-01]可领取的优惠券接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult( e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 优惠券一键领取
     * <AUTHOR>
     * @date 2025/3/14
     */
    @RequestMapping(value = "/v0.1/coupons/allPick")
    public @ResponseBody
    Object allPick(BilCouponCondition bil, HttpServletRequest request) {
        Map<String, Object> resultMap = MapUtils.createSucResult();
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId");
            if (bil != null) {
                String mobile = String.valueOf(bil.getMobile());
                String getChannel = String.valueOf(bil.getGetChannel());
                String cpnIds = String.valueOf(bil.getCpnId());
                try {
                    Map<String, Object> hashmap = new HashMap<>();
                    hashmap.put("custId", custId);
                    hashmap.put("cpnIds", cpnIds);
                    Map<String, Object> objectMap = couponRPCService.queryCpnNum(hashmap);
                    logger.info("用户剩余可领取优惠券：{}", JSONObject.toJSONString(objectMap));
                    if (objectMap != null && objectMap.containsKey("cpnIds")) {
                        cpnIds = MapUtils.getValue(objectMap, "cpnIds");
                    } else {
                        logger.warn("用原来优惠券id");
                    }
                } catch (Exception e) {
                    logger.error("获取可领取优惠券id异常: {}", e);
                }
                if(StringUtil.isBlank(mobile)){
                    mobile = tokenMsg.get("mobile");
                }
                logger.info("一键可领取的优惠券接口mobile={},getChannel={},cpnId={}", mobile, getChannel, cpnIds);
                if (StringUtil.isNotBlank(mobile) && StringUtil.isNotBlank(getChannel) && StringUtil.isNotBlank(cpnIds)) {
                    Map<String, String> map = new HashMap<String, String>();
                    map.put("mobile", mobile);
                    map.put("getChannel", getChannel);
                    map.put("custId", custId);
                    List<String> cpnIdList = Arrays.asList(cpnIds.split(","));

                    cpnIdList.forEach(cpnId -> {
                        map.put("cpnId", cpnId);
                        Map<String, Object> retMap = null;
                        try {
                            retMap = couponRPCService.couponsRestPOST(map);
                        } catch (Exception e) {
                            logger.error("一键领取失败", e);
                        }
                        if (retMap != null && retMap.size() > 0) {
                            List<Map<String, Object>> custList = ( List<Map<String, Object>>) retMap.get("custList");
                            if (custList != null && custList.size() > 0) {
                                Map<String, Object> custMap = (Map<String, Object>)custList.get(0);
                                if (!"1".equals(String.valueOf(custMap.get("putFlag")))) {
                                    logger.error("领取失败原因", String.valueOf(custMap.get("failReason")));
                                }
                            }
                        }
                    });
                } else {
                    logger.error("优惠券一键领取接口获取异常:mobile={},getChannel={},cpnIds={}", mobile, getChannel, cpnIds);
                    resultMap = MapUtils.createFailResult("优惠券一键领取接口必填字段为空");
                }
            } else {
                resultMap = MapUtils.createFailResult("优惠券一键领取接口参数为空");
            }
            return resultMap;
        } catch (Exception e) {
            logger.error("一键可领取的优惠券接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult( "查询失败！");
            return resultMap;
        }
    }

    /**
     * @param request
     * @description 积分兑换优惠券
     * <AUTHOR>
     * @create 2018-04-24 15:33:06
     */
    @RequestMapping(value = "/v0.1/point-coupon", method = RequestMethod.POST)
    public ResponseEntity<?> pointCoupon(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId").toString();
            String mobile = tokenMsg.get("mobile").toString();
            Map<String, Object> inMap = HttpClientUtil.getReqParams(request,false);
            inMap.put("custId", custId);
            inMap.put("mobile", mobile);
            logger.error("积分兑换优惠券  接口inMap：{}", inMap);
            if(StringUtil.isEmpty(inMap.get("actId"))){
                resultMap.put("ret", 406);
                resultMap.put("msg", "兑换优惠券活动Id不能为空");
                return new ResponseEntity<Map>(resultMap, HttpStatus.BAD_REQUEST);
            }
            if(StringUtil.isEmpty(inMap.get("getChannel"))){
                resultMap.put("ret", 406);
                resultMap.put("msg", "兑换优惠券渠道不能为空");
                return new ResponseEntity<Map>(resultMap, HttpStatus.BAD_REQUEST);
            }
            resultMap = couponService.pointCoupon(inMap);
            return new ResponseEntity<Map>(resultMap, HttpStatus.OK);

        } catch (Exception e) {
            logger.error("积分兑换优惠券接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult( e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
