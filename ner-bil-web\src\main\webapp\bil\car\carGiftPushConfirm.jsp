<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="购车礼金发放">
    <script type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ner:head/>
<ls:body>
    <ls:form id="searchForm" name="searchForm">
        <table align="center" class="tab_search">
            <tr>
                <td>
                    <ls:label text="车牌号:"/>
                </td>
                <td>
                    <ls:text name="giftId" property="giftId" visible="false"></ls:text>
                    <ls:text name="licenseNo" property="licenseNo" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="车架号:"/>
                </td>
                <td>
                    <ls:text name="vin" property="vin" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="身份证:"/>
                </td>
                <td>
                    <ls:text name="certNo" property="certNo" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="手机号:"/>
                </td>
                <td>
                    <ls:text name="mobile" property="mobile" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="支付宝账号:"/>
                </td>
                <td>
                    <ls:text name="alipayNo" property="alipayNo" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="购车4S店:"/>
                </td>
                <td>
                    <ls:text name="buyAddr" property="buyAddr" enabled="false"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="领取状态:"/>
                </td>
                <td>
                    <ls:select name="giftStatus" property="giftStatus" enabled="false">
                        <ls:options property="carGiftGetStatusList" scope="request" text="codeName" value="codeValue" />
                    </ls:select>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="发放金额:"/>
                </td>
                <td>
                    <ls:text name="giftAmt" value="${carGiftDefaultAmt}"/>
                </td>
            </tr>
        </table>
    </ls:form>
    <table>
        <tr>
            <td>
                <div class="pull-right">
                    <ls:button text="审核通过，直充馈赠金" onclick="doCharge"/>
                    <ls:button text="发放成功" onclick="doSubmit"/>
                    <ls:button text="发放失败" onclick="doCancel"/>
                </div>
            </td>
        </tr>
    </table>
    <ls:script>
        function doSubmit(){
            var params = searchForm.getFormData();
            LS.ajax("~/bil/carGift/doSucceed",params,function (e) {
                if (e.successful) {
                    LS.message("info", "处理成功");
                    LS.parent().doSearch();
                    LS.window.close();
                } else {
                    LS.message('info', e.items[0].resultValue.resultValue);
                }
            });
        }

        function doCancel(){
            var params = searchForm.getFormData();
            LS.ajax("~/bil/carGift/doFail",params,function (e) {
                if (e.successful) {
                    LS.message("info", "处理成功");
                    LS.parent().doSearch();
                    LS.window.close();
                } else {
                    LS.message('info', e.items[0].resultValue.resultValue);
                }
            });
        }

        function doCharge(){
            LS.confirm("确定充值？", function (rs) {
                if (rs) {
                    var params = searchForm.getFormData();
                    //params.giftAmt = '${carGiftDefaultAmt}';
                    LS.ajax("~/bil/carGift/doCharge",params,function (e) {
                        if (e.successful) {
                            LS.message("info", "处理成功");
                            LS.parent().doSearch();
                            LS.window.close();
                        } else {
                            LS.message('info', e.resultValue);
                        }
                    });
                }
            })
        }
    </ls:script>
</ls:body>
</html>