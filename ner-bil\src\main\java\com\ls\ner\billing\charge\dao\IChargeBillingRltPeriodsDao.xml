<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillingRltPeriodsDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo">
		<result column="APP_NO" property="appNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="SN" property="sn" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
		<result column="PRICE" property="price" />
		<result column="PQ" property="pq" />
		<result column="AMT" property="amt" />
		<result column="TIME_FLAG" property="timeFlag" />
		<result column="REL_BEGIN_TIME" property="relBeginTime" />
		<result column="REL_END_TIME" property="relEndTime" />
		<result column="ITEM_NO" property="itemNo" />
	 </resultMap>

	<resultMap id="BaseResultMapRlt" type="com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo">
		<result column="APP_NO" property="appNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="SN" property="sn" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
		<result column="PRICE" property="price" />
		<result column="PQ" property="pq" />
		<result column="AMT" property="amt" />
		<result column="TIME_FLAG" property="timeFlag" />
		<result column="ITEM_NO" property="itemNo" />
	</resultMap>
	 
	 <sql id="chargeBillingRltPeriodItems">
	 	a.APP_NO,a.CHC_NO,a.SN,a.BEGIN_TIME,a.END_TIME,ROUND(a.PRICE,4) PRICE,ROUND(a.PQ,3) PQ,ROUND(a.AMT,2) AMT,a.TIME_FLAG,a.REL_BEGIN_TIME,a.REL_END_TIME,a.ITEM_NO
	 </sql>
	 
	<!-- 查询充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS by appNo-->
	<select id="queryChargeBillingRltPeriod" parameterType="string" resultMap="BaseResultMap">
		select  
			<include refid="chargeBillingRltPeriodItems"></include>
		from E_CHARGE_BILLING_RLT_PERIODS a
		<where>
			a.APP_NO = #{appNo}
		</where>
		order by a.SN
	</select>
	 
	<!-- 新增充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS-->
	<insert id="insertChargeBillingRltPeriods" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_BILLING_RLT_PERIODS
		<trim prefix="(" suffix=")">
			<if test="appNo !=null and appNo !=''">APP_NO,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sn !=null and sn !=''">SN,</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME,</if>
			<if test="endTime !=null and endTime !=''">END_TIME,</if>
			<if test="price !=null and price !=''">PRICE,</if>
			<if test="pq !=null and pq !=''">PQ,</if>
			<if test="amt !=null and amt !=''">AMT,</if>
			<if test="timeFlag !=null and timeFlag !=''">TIME_FLAG,</if>
			<if test="relBeginTime !=null and relBeginTime !=''">REL_BEGIN_TIME,</if>
			<if test="relEndTime !=null and relEndTime !=''">REL_END_TIME,</if>
			<if test="itemNo !=null and itemNo !=''">ITEM_NO,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="appNo !=null and appNo !=''">#{appNo},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sn !=null and sn !=''">#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">#{endTime},</if>
			<if test="price !=null and price !=''">#{price},</if>
			<if test="pq !=null and pq !=''">#{pq},</if>
			<if test="amt !=null and amt !=''">#{amt},</if>
			<if test="timeFlag !=null and timeFlag !=''">#{timeFlag},</if>
			<if test="relBeginTime !=null and relBeginTime !=''">#{relBeginTime},</if>
			<if test="relEndTime !=null and relEndTime !=''">#{relEndTime},</if>
			<if test="itemNo !=null and itemNo !=''">#{itemNo},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS -->
	<update id="updateChargeBillingRltPeriods" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo">
		update E_CHARGE_BILLING_RLT_PERIODS
		<set>
			<if test="chcNo !=null and chcNo !=''">CHC_NO =#{chcNo},</if>
			<if test="sn !=null and sn !=''">SN =#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME =#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">END_TIME =#{endTime},</if>
			<if test="price !=null and price !=''">PRICE =#{price},</if>
			<if test="pq !=null and pq !=''">PQ =#{pq},</if>
			<if test="amt !=null and amt !=''">AMT =#{amt},</if>
			<if test="timeFlag !=null and timeFlag !=''">TIME_FLAG =#{timeFlag},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			APP_NO =#{appNo}
		</where>
	</update>
	
	<delete id="deleteChargeBillingRltPeriod" parameterType="string">
		delete from E_CHARGE_BILLING_RLT_PERIODS where APP_NO =#{appNo}
	</delete>

	<!-- 查询充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS by appNo-->
	<select id="getChargeBillRltPeriod" resultMap="BaseResultMapRlt">
		select
		<include refid="chargeBillingRltPeriodItems"></include>
		from E_CHARGE_BILLING_RLT_PERIODS a
		<where>
			a.APP_NO = #{orderNo}
			<if test="itemNo !=null and itemNo !=''">
				and a.ITEM_NO = #{itemNo}
			</if>
		</where>
		order by a.SN
	</select>

	<select id="getChargeBillRltPeriodByOrder" resultMap="BaseResultMapRlt">
		select
		<include refid="chargeBillingRltPeriodItems"></include>
		from E_CHARGE_BILLING_RLT_PERIODS a
		<where>
			<if test="orderNoList != null and orderNoList.size() > 0">
				AND a.APP_NO IN
				<foreach collection="orderNoList" item="item"  open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="orderNo != null and orderNo != ''">
				AND a.APP_NO = #{orderNo}
			</if>
		</where>
	</select>

	<select id="getChargeBillRltModel" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT
			aa.APP_NO appNo,
			aa.CHC_NO chcNo,
			aa.BILL_CTL_MODE billCtlMode,
			aa.CHARGE_MODE chargeMode,
			Round(aa.CHARGE_PRICE,2) chargePrice,
			Round(aa.price,2) periodPrice,
			aa.TIME_FLAG timeFlag,
			GROUP_CONCAT( aa.time ) times,
			aa.ITEM_CHARGE_MODE itemChargeMode,
			aa.ATTACH_ITEM_NOS attachItemNos
		FROM
		( SELECT
			b.APP_NO,
			b.CHC_NO,
			c.BILL_CTL_MODE,
			c.CHARGE_MODE,
			c.CHARGE_PRICE,
			a.BEGIN_TIME,
			a.END_TIME,
			a.PRICE,
			a.TIME_FLAG,
			CONCAT( a.BEGIN_TIME, '~', a.END_TIME ) time,
			c.ITEM_CHARGE_MODE,
			c.ATTACH_ITEM_NOS
		FROM
		e_charge_billing_rlt b
		LEFT JOIN e_charge_billing_conf c ON b.CHC_NO = c.CHC_NO
		LEFT JOIN e_charge_periods a ON b.CHC_NO = a.CHC_NO
		<where>
			b.APP_NO = #{orderNo}
			<if test="itemNo != null and itemNo != ''">
				AND a.ITEM_NO = #{itemNo}
			</if>
		</where>
		) aa
		GROUP BY
			aa.TIME_FLAG;
	</select>

	<!-- 查询充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS by appNo-->
	<select id="queryBillingRltPeriod" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="chargeBillingRltPeriodItems"></include>
		from E_CHARGE_BILLING_RLT_PERIODS a
		<where>
			a.APP_NO = #{appNo}
			<if test="itemType !=null and itemType == '01'.toString()">
				and a.ITEM_NO = '1000000000'
			</if>
			<if test="itemType !=null and itemType == '02'.toString()">
				and a.ITEM_NO = '1000001000'
			</if>
			<if test="itemType !=null and itemType == '03'.toString()">
				and a.ITEM_NO not in ('1000001000','1000000000')
			</if>
		</where>
		order by a.TIME_FLAG
	</select>
</mapper>