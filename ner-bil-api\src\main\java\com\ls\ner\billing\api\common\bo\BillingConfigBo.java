package com.ls.ner.billing.api.common.bo;

import java.util.Date;

import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.pt.poseidon.api.framework.DicAttribute;

public class BillingConfigBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.IS_LATEST_VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String isLatestVersion;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.IS_ADJUSTED
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String isAdjusted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.BILLING_CONFIG_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String billingConfigName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.UNIFORM_PRICE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String uniformPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.PE_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String peBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.SUB_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String subBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.CHARGE_WAY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String chargeWay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.CHARGE_MODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String chargeMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.MIN_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String minPriceUnitDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.MAX_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String maxPriceUnitDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String orgAutoModelKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.OPER_ORG_CODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String operOrgCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.RT_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String rtNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.AUTO_MODEL_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String autoModelNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.AUTO_MODEL_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String autoModelName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.APPLY_DATE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String applyDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_config.REMARK
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String remark;
    
    @DicAttribute(dicName = "codeDict", key = "subBe", subType = SubBe.CODE_TYPE)
	private String subBeName;
    @DicAttribute(dicName = "codeDict", key = "chargeWay", subType = ChargeWay.CODE_TYPE)
	private String chargeWayName;
    @DicAttribute(dicName = "codeDict", key = "chargeMode", subType = ChargeMode.CODE_TYPE)
	private String chargeModeName;
    @DicAttribute(dicName = "codeDict", key = "isLatestVersion", subType = BillingConstants.JUDGE_FLAG)
	private String isLatestVersionName;
    private String planNo;
    private String planName;
    private String eftDate;
    
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.SYSTEM_ID
     *
     * @return the value of e_billing_config.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.SYSTEM_ID
     *
     * @param systemId the value for e_billing_config.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.BILLING_NO
     *
     * @return the value of e_billing_config.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.BILLING_NO
     *
     * @param billingNo the value for e_billing_config.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setBillingNo(String billingNo) {
        this.billingNo = billingNo == null ? null : billingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.VERSION
     *
     * @return the value of e_billing_config.VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.VERSION
     *
     * @param version the value for e_billing_config.VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.IS_LATEST_VERSION
     *
     * @return the value of e_billing_config.IS_LATEST_VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getIsLatestVersion() {
        return isLatestVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.IS_LATEST_VERSION
     *
     * @param isLatestVersion the value for e_billing_config.IS_LATEST_VERSION
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setIsLatestVersion(String isLatestVersion) {
        this.isLatestVersion = isLatestVersion == null ? null : isLatestVersion.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.IS_ADJUSTED
     *
     * @return the value of e_billing_config.IS_ADJUSTED
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getIsAdjusted() {
        return isAdjusted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.IS_ADJUSTED
     *
     * @param isAdjusted the value for e_billing_config.IS_ADJUSTED
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setIsAdjusted(String isAdjusted) {
        this.isAdjusted = isAdjusted == null ? null : isAdjusted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.BILLING_CONFIG_NAME
     *
     * @return the value of e_billing_config.BILLING_CONFIG_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getBillingConfigName() {
        return billingConfigName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.BILLING_CONFIG_NAME
     *
     * @param billingConfigName the value for e_billing_config.BILLING_CONFIG_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setBillingConfigName(String billingConfigName) {
        this.billingConfigName = billingConfigName == null ? null : billingConfigName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.UNIFORM_PRICE
     *
     * @return the value of e_billing_config.UNIFORM_PRICE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getUniformPrice() {
        return uniformPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.UNIFORM_PRICE
     *
     * @param uniformPrice the value for e_billing_config.UNIFORM_PRICE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setUniformPrice(String uniformPrice) {
        this.uniformPrice = uniformPrice == null ? null : uniformPrice.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.PE_BE
     *
     * @return the value of e_billing_config.PE_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getPeBe() {
        return peBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.PE_BE
     *
     * @param peBe the value for e_billing_config.PE_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setPeBe(String peBe) {
        this.peBe = peBe == null ? null : peBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.SUB_BE
     *
     * @return the value of e_billing_config.SUB_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getSubBe() {
        return subBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.SUB_BE
     *
     * @param subBe the value for e_billing_config.SUB_BE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setSubBe(String subBe) {
        this.subBe = subBe == null ? null : subBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.CHARGE_WAY
     *
     * @return the value of e_billing_config.CHARGE_WAY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getChargeWay() {
        return chargeWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.CHARGE_WAY
     *
     * @param chargeWay the value for e_billing_config.CHARGE_WAY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setChargeWay(String chargeWay) {
        this.chargeWay = chargeWay == null ? null : chargeWay.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.CHARGE_MODE
     *
     * @return the value of e_billing_config.CHARGE_MODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getChargeMode() {
        return chargeMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.CHARGE_MODE
     *
     * @param chargeMode the value for e_billing_config.CHARGE_MODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode == null ? null : chargeMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.MIN_PRICE_UNIT_DESC
     *
     * @return the value of e_billing_config.MIN_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getMinPriceUnitDesc() {
        return minPriceUnitDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.MIN_PRICE_UNIT_DESC
     *
     * @param minPriceUnitDesc the value for e_billing_config.MIN_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setMinPriceUnitDesc(String minPriceUnitDesc) {
        this.minPriceUnitDesc = minPriceUnitDesc == null ? null : minPriceUnitDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.MAX_PRICE_UNIT_DESC
     *
     * @return the value of e_billing_config.MAX_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getMaxPriceUnitDesc() {
        return maxPriceUnitDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.MAX_PRICE_UNIT_DESC
     *
     * @param maxPriceUnitDesc the value for e_billing_config.MAX_PRICE_UNIT_DESC
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setMaxPriceUnitDesc(String maxPriceUnitDesc) {
        this.maxPriceUnitDesc = maxPriceUnitDesc == null ? null : maxPriceUnitDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.DATA_OPER_TIME
     *
     * @return the value of e_billing_config.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_billing_config.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.DATA_OPER_TYPE
     *
     * @return the value of e_billing_config.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_billing_config.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.ORG_AUTO_MODEL_KEY
     *
     * @return the value of e_billing_config.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getOrgAutoModelKey() {
        return orgAutoModelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.ORG_AUTO_MODEL_KEY
     *
     * @param orgAutoModelKey the value for e_billing_config.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setOrgAutoModelKey(String orgAutoModelKey) {
        this.orgAutoModelKey = orgAutoModelKey == null ? null : orgAutoModelKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.OPER_ORG_CODE
     *
     * @return the value of e_billing_config.OPER_ORG_CODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getOperOrgCode() {
        return operOrgCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.OPER_ORG_CODE
     *
     * @param operOrgCode the value for e_billing_config.OPER_ORG_CODE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setOperOrgCode(String operOrgCode) {
        this.operOrgCode = operOrgCode == null ? null : operOrgCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.RT_NO
     *
     * @return the value of e_billing_config.RT_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getRtNo() {
        return rtNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.RT_NO
     *
     * @param rtNo the value for e_billing_config.RT_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setRtNo(String rtNo) {
        this.rtNo = rtNo == null ? null : rtNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.AUTO_MODEL_NO
     *
     * @return the value of e_billing_config.AUTO_MODEL_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getAutoModelNo() {
        return autoModelNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.AUTO_MODEL_NO
     *
     * @param autoModelNo the value for e_billing_config.AUTO_MODEL_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setAutoModelNo(String autoModelNo) {
        this.autoModelNo = autoModelNo == null ? null : autoModelNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.AUTO_MODEL_NAME
     *
     * @return the value of e_billing_config.AUTO_MODEL_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getAutoModelName() {
        return autoModelName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.AUTO_MODEL_NAME
     *
     * @param autoModelName the value for e_billing_config.AUTO_MODEL_NAME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setAutoModelName(String autoModelName) {
        this.autoModelName = autoModelName == null ? null : autoModelName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.APPLY_DATE
     *
     * @return the value of e_billing_config.APPLY_DATE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getApplyDate() {
        return applyDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.APPLY_DATE
     *
     * @param applyDate the value for e_billing_config.APPLY_DATE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate == null ? null : applyDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_config.REMARK
     *
     * @return the value of e_billing_config.REMARK
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_config.REMARK
     *
     * @param remark the value for e_billing_config.REMARK
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

	public String getSubBeName() {
		return subBeName;
	}

	public void setSubBeName(String subBeName) {
		this.subBeName = subBeName;
	}

	public String getChargeWayName() {
		return chargeWayName;
	}

	public void setChargeWayName(String chargeWayName) {
		this.chargeWayName = chargeWayName;
	}

	public String getChargeModeName() {
		return chargeModeName;
	}

	public void setChargeModeName(String chargeModeName) {
		this.chargeModeName = chargeModeName;
	}

	public String getIsLatestVersionName() {
		return isLatestVersionName;
	}

	public void setIsLatestVersionName(String isLatestVersionName) {
		this.isLatestVersionName = isLatestVersionName;
	}

	public String getPlanNo() {
		return planNo;
	}

	public void setPlanNo(String planNo) {
		this.planNo = planNo;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}

	public String getEftDate() {
		return eftDate;
	}

	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}
}