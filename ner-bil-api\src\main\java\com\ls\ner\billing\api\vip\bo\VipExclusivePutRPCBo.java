package com.ls.ner.billing.api.vip.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class VipExclusivePutRPCBo implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id **/
    private String id;
    // 用户id
    private String custId;
    private String putDay;//下次发放日期
    private String mobile;
    private Integer putTimes; //剩余发放次数

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getPutDay() {
        return putDay;
    }

    public void setPutDay(String putDay) {
        this.putDay = putDay;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getPutTimes() {
        return putTimes;
    }

    public void setPutTimes(Integer putTimes) {
        this.putTimes = putTimes;
    }

}
