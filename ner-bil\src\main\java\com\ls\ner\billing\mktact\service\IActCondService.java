package com.ls.ner.billing.mktact.service;

import com.ls.ner.billing.mktact.bo.MarketTypeBo;

import java.util.List;
import java.util.Map;

/**
 * 描述:活动类型处理
 * IActInfoService.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 11:47
 **/
public interface IActCondService {

    /**
     * 描述: 保存优惠条件主表
     *
     * @param: [bo]
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    String saveActCond(MarketTypeBo bo) throws Exception;

    /**
     * 描述: 更新优惠条件主表
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int updateActCond(MarketTypeBo bo) throws Exception;

    /**
     * 描述: :有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    List<MarketTypeBo> queryActDctAll(MarketTypeBo bo) throws Exception;

    /**
     * 描述: :限时打折 -- 有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    public List<MarketTypeBo> queryActAll(MarketTypeBo bo) throws Exception;

    /**
     * 描述: :获取活动优惠条件
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    MarketTypeBo queryActCond(MarketTypeBo bo) throws Exception;

    /**
     * 描述:首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    int saveActCondDet(Map<String, Object> paramMap) throws Exception;

    /**
     * 描述:首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    int delActCondDet(Map<String, Object> paramMap) throws Exception;

    /**
     * 描述:查询优惠条件明细
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    List<MarketTypeBo> queryMktActCondDet(MarketTypeBo bo) throws Exception;

    /**
     * 描述:查询优惠内容
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    List<MarketTypeBo> queryMktActDct(MarketTypeBo bo) throws Exception;

    /**
     * 描述: 获取优惠内容
     *
     * @param: [paraMap]
     * @return: java.util.List<java.util.Map>
     * 创建人:biaoxiangd
     * 创建时间:2017/6/30 21:07
     */
    public List<Map> setProdInfo(Map<String, String> paramMap) throws Exception;

    /**
     * 描述:保存活动的优惠条件、优惠内容、优惠条件明细
     *
     * @param: [bo]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/7 1:29
     */
    public int saveAll(Map<String, Object> paramMap) throws Exception;

    /**
     * @param
     * @description
     * <AUTHOR>
     * @create 2019/6/2 4:19
     */
    public int saveAllNew(Map<String, Object> paramMap) throws Exception;

    /**
     * 描述:注册送/邀请送 -- 赠送内容保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    public int saveMktActGive(Map<String, Object> paramMap) throws Exception;

    /**
     * @param paramMap
     * @description 注册送/邀请送 -- 优惠券删除
     * <AUTHOR>
     * @create 2017-08-09 10:50:01
     */
    int delMktActGive(Map<String, Object> paramMap) throws Exception;

    /**
     * @param actMap
     * @description 查询活动关联的站点
     * <AUTHOR>
     * @create 2019-05-09 16:42:09
     */
    List<MarketTypeBo> qryActStationList(Map<String, Object> actMap);

    /**
     * @param actMap
     * @description 保存活动和站点关联
     * <AUTHOR>
     * @create 2019-05-09 16:57:34
     */
    void saveActStation(List<Map<String, String>> actMap);

    /**
     * @param serchMap
     * @description 查询活动关联的站点条数
     * <AUTHOR>
     * @create 2019-05-09 17:33:02
     */
    int qryActStationCount(Map<String, Object> serchMap);

    /**
     * @param dataParams
     * @description 删除关联站点
     * <AUTHOR>
     * @create 2019-05-09 17:43:18
     */
    void delActStation(Map<String, Object> dataParams);

    /**
     * @param
     * @description 限时打折 -- 有条件查询活动明细
     * <AUTHOR>
     * @create 2019/6/11 9:54
     */
    public List<MarketTypeBo> queryActAllNew(MarketTypeBo bo) throws Exception;

    /**
     * @param
     * @description 获取优惠内容
     * <AUTHOR>
     * @create 2019/6/11 9:58
     */
    public Map setItemInfoNew(Map<String, String> paramMap) throws Exception;
}
