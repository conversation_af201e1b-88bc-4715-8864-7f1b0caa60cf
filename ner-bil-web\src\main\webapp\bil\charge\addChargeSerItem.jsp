<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="新增服务项目" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="chargeForm" name="chargeForm">
		<ls:title text="服务项目信息"></ls:title>
			<ls:text type="hidden" name="systemId" property="systemId"/>
			<table class="tab_search">
				<tr>
					<td><ls:label text="费用项编号" ref="itemNo"/></td>
					<td colspan="2"><ls:text name="itemNo" property="itemNo"  required="true" type="number"  ></ls:text></td>
					<td><ls:label text="费用项名称" ref="itemName"/></td>
					<td colspan="2"><ls:text name="itemName" property="itemName"  required="true" maxlength="50"></ls:text></td>
				</tr>
				<tr>
					<td><ls:label text="排序号" ref="sn" /></td>
					<td colspan="2"><ls:text name="sn" property="sn" required="true" type="number" maxlength="8" onValidate="VD.positiveInteger(sn)"/></td>
					<td><ls:label text="费用项单位" ref="itemUnit"/></td>
					<td colspan="2">
						<ls:select name="itemUnit" required="true" property="itemUnit">
							<ls:options property="chcBillingPriceUnitList" scope="request" text="codeName" value="codeValue" ></ls:options>
						</ls:select>
					</td>
				</tr>
				<tr>
					<td><ls:label text="费用项描述" ref="remarks"/></td>
					<td colspan="5"><ls:text  name="remarks" property="remarks" type="textarea" maxlength="200"></ls:text></td>
				</tr>
			</table>
			<table>
				<tr>
					<td>
						<div class="pull-right">
							<ls:button text="保存" onclick="doSave" />
							<ls:button text="返回" onclick="doReturn" />
						</div>
					</td>
				</tr>
			</table>
	</ls:form>
	<ls:script>
	init();
	function init(){
		if(itemNo.getValue()!=null && itemNo.getValue()!=''){
			itemName.setEnabled(false);
			itemUnit.setEnabled(false);
			itemNo.setEnabled(false);
		}
	}
	
	function doSave(){
		 chargeForm.submit("~/billing/chargeServiceItem/saveChargeSerItem",function(data){
   		 	if(data.itemNo){
   		 		if(systemId.getValue()==null || systemId.getValue()==''){
         				LS.message("info","新建成功");
         				LS.parent().doSearch();			
   		 		}else{
   		 			LS.message("info","修改成功"); 
   		 			LS.parent().doSearch();
   		 		}
   		 		LS.window.close();
   		 	}else{
   		 		LS.message("error","保存失败");
   		 	}
      });
	}
	
	function doReturn(){
		setTimeout("LS.window.close()",200);
	}
	
    </ls:script>
</ls:body>
</html>