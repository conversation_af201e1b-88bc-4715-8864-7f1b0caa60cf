
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>

<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费配置" />
<ls:body>
	<ls:form id="tariffPlanForm" name="tariffPlanForm">
		
	
		<ls:title text="资费标准基本信息 "></ls:title>
		<table class="tab_search">
			<ls:text name="peBe" value="01" visible="false"></ls:text>
			<ls:text name="planNo" property="planNo" visible="false"></ls:text>
			<tr>
			  <td>
            <ls:label text="管理单位" />
        </td>
        <td>
            <ls:text type="hidden" name="orgCode" property="orgCode"/>
            <ls:text name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" />
<%--             <ls:text imageKey="search" onClickImage="getOrg" name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" />
 --%>        </td>
				<td><ls:label text="资费名称" ref="planName" /></td>
				<td><ls:text name="planName" property="planName" required="true" readOnly="${readOnly }">
					</ls:text></td>
				<td><ls:label text="租赁方式" ref="subBe"/></td>
				<td><ls:select name="subBe" property="subBe" required="true" onchanged="changeSubBe" readOnly="${readOnly }">
						<ls:options property="rentTypeList" scope="request" text="name" value="value" groupName="items"/>
					</ls:select></td>
			</tr>
		</table>
		<ls:title text="车辆租赁费规则"></ls:title>
		<table class="tab_search">
			<tr>
				<td width="200px"><ls:label text="计费方式" ref="chargeWay" /></td>
				<td colspan="3"><ls:checklist type="radio" name="chargeWay"  readOnly="${readOnly }"
						required="true" columns="20" property="chargeWay" onchanged="changeChargeWay">
						<ls:checkitems property="billingChargeWayCheckList" scope="request"
							text="codeName" value="codeValue"></ls:checkitems>
					</ls:checklist></td>
			</tr>
			<tr>
				<td width="200px"><ls:label text="计费模式" ref="chargeMode"/></td>
				<td colspan="3"><ls:checklist type="radio" name="chargeMode"  readOnly="${readOnly }"
						required="true" columns="20" property="chargeMode" onchanged="changeChargeMode">
						<ls:checkitems scope="request" property="billingChargeModeCheckList"
							text="codeName" value="codeValue"></ls:checkitems>
					</ls:checklist></td>
			</tr>

			<tr id="timePerTR" >
				<td width="200px"><ls:label text="最小计费时间" ref="timePerPriceValue"/></td>
				<td width="150px"><ls:text name="timePerPriceValue"  readOnly="${readOnly }" required="true"
						property="timePerPriceValue" onchanged="changeTimePriceValue" type="number" min="1" max="1000000"></ls:text></td>
				<td width="100px"><ls:select name="timePerPriceUnit"  readOnly="${readOnly }" required="true"
						property="timePerPriceUnit" onchanged="changeTimePriceUnit">
						<ls:option value="01" text="分钟"></ls:option>
												<ls:option value="02" text="小时"></ls:option>
												<ls:option value="03" text="天"></ls:option>
												<ls:option value="06" text="月"></ls:option>
												<ls:option value="07" text="年"></ls:option>
											
					</ls:select></td>
				<td>
					<div class="pull-left">
						<span>说明：不足<span class="time-price-value-label">最小</span><span class="time-price-unit-label">计费单元</span>，按<span class="time-price-value-label">最小</span><span class="time-price-unit-label">计费单元</span>收费</span>
					</div>
				</td>
			</tr>
			
			<tr id="millPerTR" style="display:none">
        <td width="200px"><ls:label text="最小计费里程" ref="millPerPriceValue"/></td>
        <td width="150px"><ls:text name="millPerPriceValue" readOnly="${readOnly }" required="true"
            property="millPerPriceValue" onchanged="changeMillPriceValue" type="number" min="1" max="1000000"></ls:text></td>
        <td width="100px"><ls:select name="millPerPriceUnit" readOnly="${readOnly }" required="true"
            property="millPerPriceUnit" onchanged="changeMillPriceUnit">
              <ls:option value="04" text="米"></ls:option>
                        <ls:option value="05" text="公里"></ls:option>
          </ls:select></td>
        <td>
          <div class="pull-left">
            <span>说明：不足<span class="mill-price-value-label">最小</span><span class="mill-price-unit-label">计费单元</span>，按<span class="mill-price-value-label">最小</span><span class="mill-price-unit-label">计费单元</span>收费</span>
          </div>
        </td>
      </tr>
			
			<tr id="periodSetTR" style="display:none">
        <td width="200px"><ls:label text="分时设置" ref="periodSplitPoints"/></td>
        <td colspan="3">
          <ls:grid url="" name="timeGrid" primaryKey="sn" cellEdit="true" height="100px;" width="100%">
            <ls:gridToolbar name="timeGridBar">
              <ls:gridToolbarItem imageKey="add" onclick="doAddPeriodGrid" text="新增" enabled="${!readOnly }"></ls:gridToolbarItem>
              <ls:gridToolbarItem imageKey="delete" onclick="doDelPeriodGrid" text="删除" enabled="${!readOnly }"></ls:gridToolbarItem>
            </ls:gridToolbar>
            <ls:column caption="排序号" name="sn" editableEdit="false"/>
            <ls:column caption="开始时间" name="periodBegin" editableEdit="${!readOnly }">
              <ls:textEditor onValidate="endTimeValidate()" required="true"/>
            </ls:column>
            <ls:column caption="结束时间" name="periodEnd" editableEdit="${!readOnly }">
              <ls:textEditor onValidate="endTimeValidate()" required="true"/>
            </ls:column>
          </ls:grid>
          <ls:text name="periodSplitPoints" property="periodSplitPoints" required="true"  readOnly="${readOnly }" type="hidden"></ls:text>
        </td>     
      </tr>
      
			<tr id="timeStepSetTR" style="display:none">
				<td width="200px"><ls:label text="时间阶梯设置" ref="timeStepSplitPoints"/></td>
				<td colspan="3">
				  <ls:grid url="" name="timeStepGrid" primaryKey="sn" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="100%">
            <ls:gridToolbar name="timeStepGridBar">
              <ls:gridToolbarItem imageKey="add" onclick="doAddTimeStepGrid" text="新增" enabled="${!readOnly }"></ls:gridToolbarItem>
              <ls:gridToolbarItem imageKey="delete" onclick="doDelTimeStepGrid" text="删除" enabled="${!readOnly }"></ls:gridToolbarItem>
            </ls:gridToolbar>
            <ls:column caption="排序号" name="sn" editableEdit="false"/>
            <ls:column caption="开始" name="timeStepBegin" editableEdit="false"/>
            <ls:column caption="结束" name="timeStepEnd" editableEdit="${!readOnly }">
              <ls:textEditor required="true" type="number" onValidate="endTimeStepValidate()"/>
            </ls:column>
          </ls:grid>
					<ls:text name="timeStepSplitPoints" property="timeStepSplitPoints" readOnly="${readOnly }" required="true" type="hidden"></ls:text>
				</td>			
			</tr>
			
			<tr id="millStepSetTR" style="display:none">
				<td width="200px"><ls:label text="里程阶梯设置" ref="millStepSplitPoints"/></td>
				<td colspan="3">
				  <ls:grid url="" name="millStepGrid" primaryKey="sn" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency1" height="100px;" width="100%">
            <ls:gridToolbar name="millStepGridBar">
              <ls:gridToolbarItem imageKey="add" onclick="doAddMillStepGrid" text="新增" enabled="${!readOnly }"></ls:gridToolbarItem>
              <ls:gridToolbarItem imageKey="delete" onclick="doDelMillStepGrid" text="删除" enabled="${!readOnly }"></ls:gridToolbarItem>
            </ls:gridToolbar>
            <ls:column caption="排序号" name="sn" editableEdit="false"/>
            <ls:column caption="开始" name="millStepBegin" editableEdit="false"/>
            <ls:column caption="结束" name="millStepEnd" editableEdit="${!readOnly }">
              <ls:textEditor required="true" type="number" onValidate="endMillStepValidate()"/>
            </ls:column>
          </ls:grid>
					<ls:text name="millStepSplitPoints" property="millStepSplitPoints" required="true" readOnly="${readOnly }" type="hidden"></ls:text>
				</td>			
			</tr>			
		</table>
		<ls:text type="hidden" name="depositItemNos" property="depositItemNos"></ls:text>
		<ls:text type="hidden" name="attachItemNos" property="attachItemNos"></ls:text>
	</ls:form>

	<ls:title text="押金项目"></ls:title>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="depositItemGrid" height="80" primaryKey="itemNo" 
					showCheckBox="true">
					<%-- <ls:gridToolbar name="code_grid_bar">
						<ls:gridToolbarItem name="maintainDepositItems" text="维护"
							imageKey="edit" onclick="maintainDepositItems" />

					</ls:gridToolbar> --%>
					<ls:column name="itemNo" caption="" hidden="true" />
					<ls:column name="itemName" caption="费用项名称" />
					<ls:column name="sn" caption="排序" hidden="false" />

				</ls:grid></td>
		</tr>

	</table>


	<ls:title text="其他服务项目"></ls:title>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="attachItemGrid" height="100" width="100%" primaryKey="itemNo"
					showCheckBox="true">
					<%-- <ls:gridToolbar name="code_grid_bar">
						<ls:gridToolbarItem name="maintainAttachItems" text="维护"
							imageKey="edit" onclick="maintainAttachItems" />

					</ls:gridToolbar> --%>
					<ls:column name="itemNo" caption="" hidden="true" />
					<ls:column name="itemName" caption="费用项名称" />
					<ls:column name="itemUnit" caption="费用项单位" hidden="true" />
					<ls:column name="itemUnitName" caption="费用项单位" />
					<ls:column name="sn" caption="排序" hidden="false" />

				</ls:grid></td>
		</tr>
	</table>
	<c:if test="${operFlag=='add'|| operFlag=='edit' }">
	<table>	
		<tr>
			<td><ls:button text="保存" onclick="saveTariffPlan"></ls:button></td>
		</tr>
	</table>
	</c:if>
	<ls:script>
	var baseUrl = "~/billing/tariffplans";
  <%-- var planNo = '<c:out value="${requestScope.planNo}" />'; --%>
	<%-- 租赁方式修改 --%>
	function changeSubBe(){
		if(!subBe.getValue()){
			return;
		}
		if(subBe.getValue()=='11'){
			//物流
			chargeMode.items[1].setEnabled(false);
			chargeWay.items[0].setEnabled(false);
			chargeWay.items[2].setEnabled(false);
			return;
		} else {
			chargeMode.items[1].setEnabled(true);
			chargeWay.items[0].setEnabled(true);
			chargeWay.items[2].setEnabled(true);
		}
		<%-- 只有时租有分时 --%>
		if(subBe.getValue().indexOf('01')!='0'){
			if('02'==chargeMode.getValue()){
				chargeMode.setValue('01');
				changeChargeMode();
			}
			chargeMode.items[1].setEnabled(false);
			if(subBe.getValue().indexOf('02')=='0'){
				timePerPriceUnit.appendItems([{text:"分钟",value:"01"},{text:"小时",value:"02"},{text:"天",value:"03"}]);
			} else {
				timePerPriceUnit.appendItems([{text:"分钟",value:"01"},{text:"小时",value:"02"},{text:"天",value:"03"},{text:"月",value:"06"},{text:"年",value:"07"}]);
			}
		}
		else if(subBe.getValue().indexOf('01')=='0'){
			chargeMode.items[1].setEnabled(true);
			timePerPriceUnit.clearItems();
			timePerPriceUnit.appendItems([{text:"分钟",value:"01"},{text:"小时",value:"02"},{text:"天",value:"03"}]);
		}
		if(subBe.getValue().indexOf('03')=='0'){
			timePerPriceUnit.setValue("06");
			timePerPriceUnit.setReadOnly(true);
		} else 	if(subBe.getValue().indexOf('04')=='0'){
			timePerPriceUnit.setValue("07");
			timePerPriceUnit.setReadOnly(true);
		} else {
			timePerPriceUnit.setValue("");
			timePerPriceUnit.setReadOnly(false);
		}
	}
	function changeChargeWay(){
		//alert(chargeWay.getCheckedValue);
		//alert(chargeWay.getCheckedItems);
		var chargeWayValue = chargeWay.getCheckedValue();
		//alert(chargeModeValue);
		
		if(chargeWayValue == '0101'){
			$('#timePerTR').show();
			$('#millPerTR').hide();
		}
		else if(chargeWayValue == '0102'){
			$('#timePerTR').hide();
			$('#millPerTR').show();
		}
		else if(chargeWayValue == '0103'){
			$('#timePerTR').show();
			$('#millPerTR').show();
		}
		else {
		
			$('#timePerTR').hide();
			$('#millPerTR').hide();
		}
		toggleStepSetDisplay();
	}

	function changeChargeMode(){
		//alert(chargeMode.getCheckedValue);
		//alert(chargeMode.getCheckedItems);
		var chargeModeValue = chargeMode.getCheckedValue();
		//alert(chargeModeValue);
		
		if(chargeModeValue == '02'){
			$('#periodSetTR').show();			
		}
		else{
			$('#periodSetTR').hide();
		}
		toggleStepSetDisplay();
	}
	
	function toggleStepSetDisplay(){
		var chargeWayValue = chargeWay.getCheckedValue();
		var chargeModeValue = chargeMode.getCheckedValue();
		if(chargeModeValue == '03'){
				if(chargeWayValue == '0101'){
			$('#timeStepSetTR').show();
			$('#millStepSetTR').hide();
		}
		else if(chargeWayValue == '0102'){
			$('#timeStepSetTR').hide();
			$('#millStepSetTR').show();
		}
		else if(chargeWayValue == '0103'){
			$('#timeStepSetTR').show();
			$('#millStepSetTR').show();
		}
		else {
		
			$('#timeStepSetTR').hide();
			$('#millStepSetTR').hide();
		}
		}
		else{
			$('#timeStepSetTR').hide();
			$('#millStepSetTR').hide();
		}
	}
	
	function changeTimePriceValue(){
		var priceValueValue = timePerPriceValue.getValue();
		if(!priceValueValue){
			priceValueValue = '最小';
		}
		$('.time-price-value-label').html(priceValueValue);
	}
	
	function changeTimePriceUnit(){
		var priceUnitText = timePerPriceUnit.getText();
		if(!priceUnitText){
			priceUnitText = '计费单元';
		}
		$('.time-price-unit-label').html(priceUnitText);
	}
	
	function changeMillPriceValue(){
		var priceValueValue = millPerPriceValue.getValue();
		if(!priceValueValue){
			priceValueValue = '最小';
		}
		$('.mill-price-value-label').html(priceValueValue);
	}
	
	function changeMillPriceUnit(){
		var priceUnitText = millPerPriceUnit.getText();
		if(!priceUnitText){
			priceUnitText = '计费单元';
		}
		$('.mill-price-unit-label').html(priceUnitText);
	}
	
	function maintainDepositItems(){
       LS.dialog("~/billing/appendchargeitem/deposits/init","押金费用项信息",800,400,true,{
       	afterClose : function(){
       		refreshDepositItemGrid();
       	}
       	
       });
       
    }	 
	
    function refreshDepositItemGrid(){
     depositItemGrid.query("~/billing/appendchargeitem/deposits",{itemStatus:"1"});
    }
    
    depositItemGrid.onload = function(){
     	syncDepositItemNos();
     };
     depositItemGrid.onSelectAll = function(){
     	depositItemNosGrid2Field();
     };
      depositItemGrid.onitemclick = function(){
      	depositItemNosGrid2Field();
      };
     
    function syncDepositItemNos(){
    	depositItemNosField2Grid();
    	depositItemNosGrid2Field();
    }
	
	function depositItemNosGrid2Field(){
		var selectedDepositItemNoArray = depositItemGrid.getCheckedIDs();
    	depositItemNos.setValue(selectedDepositItemNoArray.join(','));
	}
	
	function depositItemNosField2Grid(){
		var depositItemNosValue = depositItemNos.getValue();
		if(!depositItemNosValue){
			depositItemNosValue = '';
		}  
		var selectedDepositItemNoArray = depositItemNosValue.split(',');
		for(var i=0 ;i < selectedDepositItemNoArray.length ;i++){
			var itemNo = selectedDepositItemNoArray[i];
			depositItemGrid.checkItem(itemNo);
		}

	}    
	
		
		

    function maintainAttachItems(){
       LS.dialog("~/billing/appendchargeitem/attachs/init","其他服务费用项信息",800,400,true,{
       	afterClose : function(){
       		 refreshAttachItemGrid();
       	}
       	
       });
      
    }
    function refreshAttachItemGrid(){
     attachItemGrid.query("~/billing/appendchargeitem/attachs",{itemStatus:"1"});
    }   
    
     attachItemGrid.onload = function(){
     	syncAttachItemNos();
     };
     
    attachItemGrid.onSelectAll = function(){
     	attachItemNosGrid2Field();
     };
      attachItemGrid.onitemclick = function(){
      	attachItemNosGrid2Field();
      };     
     
    function syncAttachItemNos(){
    	attachItemNosField2Grid();
    	attachItemNosGrid2Field();
    }
	
	function attachItemNosGrid2Field(){
		var selectedAttachItemNoArray = attachItemGrid.getCheckedIDs();
    	attachItemNos.setValue(selectedAttachItemNoArray.join(','));
	}
	
	function attachItemNosField2Grid(){
		var attachItemNosValue = attachItemNos.getValue();
		if(!attachItemNosValue){
			attachItemNosValue = '';
		}  
		var selectedAttachItemNoArray = attachItemNosValue.split(',');
		for(var i=0 ;i < selectedAttachItemNoArray.length ;i++){
			var itemNo = selectedAttachItemNoArray[i];
			attachItemGrid.checkItem(itemNo);
		}

	}   

    
    function saveTariffPlan(){
	    var url = baseUrl;
	    url += '/saveTariffPlan';
	    if(!LS.isEmpty(planNo.getValue())){
	    	url += '/'+planNo.getValue();
	    }
	    if(chargeMode.getValue()=="02"){
	      if(!timeGrid){
	        LS.message("error","出错了，请联系管理员");
	        return;
	      }
	      var items =timeGrid.getItems();
	      if(items.length < 2){
	        LS.message("info","请至少设置两条分时记录！");
          return;
	      }
	      var tempBegin = items[0].periodBegin;
	      var tempEnd = items[items.length-1].periodEnd;
	      if(tempBegin=='00:00'&&tempEnd!='24:00'){
	        LS.message("info","分时设置中第"+items.length+"条记录的结束时段须为24:00");
          return;
	      }
	      if(tempBegin!='00:00'&&tempEnd=='24:00'){
          LS.message("info","分时设置中第1条记录的开始时段须为00:00");
          return;
        }
	      if(tempBegin!='00:00'&&tempEnd!='24:00'&&tempBegin != tempEnd){
	        LS.message("info","分时设置中第1条记录的开始时段必须等于第"+items.length+"条记录的结束时段");
          return;
	      }
	      var tempValue = items[0].periodBegin+" "+ items[0].periodEnd;
	      for(var m=1;m < items.length-1;m++){
	        tempValue = tempValue + " "+items[m].periodEnd;
	      }
	      if(tempBegin=='00:00'){
	        tempValue = tempValue + " "+items[items.length-1].periodEnd;
	      }
	      periodSplitPoints.setValue(tempValue);
	    }else if(chargeMode.getValue()=="03"){
	      if(chargeWay.getValue()=="0101"||chargeWay.getValue()=="0103"){
	        if(!timeStepGrid){
		        LS.message("error","出错了，请联系管理员");
		        return;
		      }
	        var items = timeStepGrid.getItems();
	        if(items.length ==0){
	          LS.message("info","时间阶梯请至少设置一条记录！");
	          return;
	        }
	        var tempValue = '';
	        for(var m=0;m < items.length;m++){
		        tempValue = tempValue + " "+items[m].timeStepEnd.replace('∞','');
		      }
	        timeStepSplitPoints.setValue(tempValue);
	      }
	      if(chargeWay.getValue()=="0102"||chargeWay.getValue()=="0103"){
	        if(!millStepGrid){
	          LS.message("error","出错了，请联系管理员");
	          return;
	        }
	        var items = millStepGrid.getItems();
	        if(items.length ==0){
            LS.message("info","里程阶梯请至少设置一条记录！");
            return;
          }
	        var tempValue = '';
	        for(var m=0;m < items.length;m++){
	          tempValue = tempValue + " "+items[m].millStepEnd.replace('∞','');
	        }
	        millStepSplitPoints.setValue(tempValue);
	      }
	    }
    	
       tariffPlanForm.submit(url, {valid : true},function(result){
     		  planNo.setValue(result);
       		LS.message("info","保存成功");
       		LS.parent().doSearch();
          LS.window.close();
      });
      
    }
    
    changeSubBe();
   chargeWay.setValue('<c:out value="${tariffPlanForm.chargeWay}" />');
   
   chargeMode.setValue('<c:out value="${tariffPlanForm.chargeMode}" />');
   changeChargeWay();
    changeChargeMode();
    if(!LS.isEmpty(periodSplitPoints.getValue())){
      var periodSplitPointsValue = periodSplitPoints.getValue().split(" ");
      var items = [];
      var len = periodSplitPointsValue.length;
      if(periodSplitPointsValue[0]=="00:00"&&periodSplitPointsValue[len-1]=="24:00"){
        for(var i = 0;i < periodSplitPointsValue.length-1;i++){
	        var item = {
	          sn :i+1,
	          periodBegin :periodSplitPointsValue[i],
	          periodEnd :periodSplitPointsValue[i+1]
	        };
	        items.push(item);
	      }
      }else {
	      for(var i = 0;i < len;i++){
	        var periodEndValue = periodSplitPointsValue[i+1];
	        if(i==len-1){
	          periodEndValue = periodSplitPointsValue[0];
	        }
	        var item = {
	          sn :i+1,
	          periodBegin :periodSplitPointsValue[i],
	          periodEnd :periodEndValue
	        };
	        items.push(item);
	      }
      }
      timeGrid.appendItem(items);
    }
    if(!LS.isEmpty(timeStepSplitPoints.getValue())){
      var periodSplitPointsValue = timeStepSplitPoints.getValue().split(" ");
      var items = [];
      var item = {
        sn :1,
        timeStepBegin :0,
        timeStepEnd :periodSplitPointsValue[0]
      };
      items.push(item);
      for(var i = 0;i < periodSplitPointsValue.length;i++){
        var periodEndValue = '';
        if(i==periodSplitPointsValue.length-1){
          periodEndValue = '∞';
        }else{
          periodEndValue = periodSplitPointsValue[i+1];
        }
        item = {
          sn :i+2,
          timeStepBegin :periodSplitPointsValue[i],
          timeStepEnd :periodEndValue
        };
        items.push(item);
      }
      timeStepGrid.appendItem(items);
    }
    if(!LS.isEmpty(millStepSplitPoints.getValue())){
      var periodSplitPointsValue = millStepSplitPoints.getValue().split(" ");
      var items = [];
      var item = {
        sn :1,
        millStepBegin :0,
        millStepEnd :periodSplitPointsValue[0]
      };
      items.push(item);
      for(var i = 0;i < periodSplitPointsValue.length;i++){
        var periodEndValue = '';
        if(i==periodSplitPointsValue.length-1){
          periodEndValue = '∞';
        }else{
          periodEndValue = periodSplitPointsValue[i+1];
        }
        item = {
          sn :i+2,
          millStepBegin :periodSplitPointsValue[i],
          millStepEnd :periodEndValue
        };
        items.push(item);
      }
      millStepGrid.appendItem(items);
    }
    refreshDepositItemGrid();
    refreshAttachItemGrid();
     function getOrg(){
      var initValue=null;
      if(!LS.isEmpty(orgCode.getValue())){
        initValue=orgCode.getValue().split(",");
      }
      js_util.selectOrgTree(false, null, true, initValue, false, setOrg);
    }
    function setOrg(node){
      if(node==null){
        return;
      }
      var orgCodes="";
      var orgCodeNames="";
      if(node.length==undefined){
        orgCodes=node.id;
        orgCodeNames=node.text;
      }else{
        for(var i=0;i< node.length;i++){
          if(node.length==i+1){
            orgCodes+=node[i].id;
            orgCodeNames+=node[i].text;
          }else{
            orgCodes+=node[i].id+',';
            orgCodeNames+=node[i].text+',';
          }
        }
      }
      orgCode.setValue(orgCodes);
      orgCodeName.setValue(orgCodeNames);
    }
    function changeOrgName(){
      if(LS.isEmpty(orgCodeName.getValue())){
         orgCode.setValue();
      }
    }
    function endTimeValidate(value,eValidator){

      var currentColumn = eValidator.currentElements.context.name.split("_")[1];
      var rowid = parseInt(eValidator.rowid);
	    var returnValid = {
	      isValid : true,
	      message : ""
	    }
	    if(LS.isEmpty(value)){
	      returnValid.message = "必选填字段";
	      returnValid.isValid = false;
	      return returnValid;
	    }
	    if(!/((([0-1]\d)|(2[0-3])):[0-5]\d)|24:00/.test(value)){
	      returnValid.message = "请输入正确的时间格式，如:03:00";
	      returnValid.isValid = false;
	      return returnValid;
	    }
      if(rowid > 1 ){
        if(currentColumn == "periodBegin"){
          var preBeginTime = timeGrid.getCell(rowid-1, "periodBegin");
	        if(!LS.isEmpty(preBeginTime) && !tranTime(preBeginTime,value)){
	            returnValid.message = "当前的开始时间必须大于上一时段的开始时间";
	            returnValid.isValid = false;
	            return returnValid;
	        }else{
	           timeGrid.setCell(rowid-1, "periodEnd", value);
	        }
        } else if(currentColumn == "periodEnd"){
          var nextEndTime = timeGrid.getCell(rowid+1, "periodEnd");
          if(!LS.isEmpty(nextEndTime) && !tranTime(value,nextEndTime)){
              returnValid.message = "当前的结束时间必须小于下一时段的结束时间";
              returnValid.isValid = false;
              return returnValid;
          }else{
            if(nextEndTime){
              timeGrid.setCell(rowid+1, "periodBegin", value);
            }
          }
        }
      }
      var currentBeginTime = timeGrid.getCell(rowid, "periodBegin");
      var currentEndTime = timeGrid.getCell(rowid, "periodEnd");
      if(currentEndTime != timeGrid.getCell(1, "periodBegin")){
        if(!LS.isEmpty(currentBeginTime)&&!LS.isEmpty(currentEndTime) && !tranTime(currentBeginTime,currentEndTime)){
	          returnValid.message = "当前的结束时间必须大于开始时间";
	          returnValid.isValid = false;
	          return returnValid;
	      }
      } else if(rowid==1 && currentEndTime == timeGrid.getCell(1, "periodBegin")){
        returnValid.message = "当前的结束时间必须大于开始时间";
        returnValid.isValid = false;
        return returnValid;
      }
      return returnValid;
	  }
	  
	  function tranTime(minTime,maxTime){
	    var min = minTime.split(":");
	    var max = maxTime.split(":");
	    var returnValue = true;
	    if(Number(max[0]) < Number(min[0]))
	      returnValue = false;
	    if(Number(max[0]) == Number(min[0]) && Number(max[1]) <= Number(min[1]))
	      returnValue = false;
	    return returnValue;
	  }
	  function doAddPeriodGrid(){
	    var data = timeGrid.getItems();
	    if(data.length==0){
	      var item = {
		      sn:'1',
		      periodBegin:'',
		      periodEnd:''
		    };
		    timeGrid.appendItem(item);
	    }else{
	      var periodEnd = data[data.length-1].periodEnd;
	      if(periodEnd=='24:00'){
	       return;
	      }
	      var item = {
	        sn:data.length+1,
	        periodBegin:data[data.length-1].periodEnd,
	        periodEnd:''
	      }; 
	      timeGrid.appendItem(item);
	    }
	    return;
	  }
	  function doDelPeriodGrid(){
	     var item = timeGrid.getSelectedItem();
	     if(LS.isEmpty(item)){
	        LS.message("error","请选择要删除的分时时段记录");
	        return;
	     }
	     var items = timeGrid.getItems();
	     if(items.length == 1){
	        LS.message("error","至少需要有一条记录");
	        return;
	     }
	     if(item.sn == items[0].sn){
	        timeGrid.removeItem(item);
	        items = timeGrid.getItems();
	        for(var i=0;i < items.length;i++){
	          timeGrid.setCell(items[i].sn,'sn',i+1);
	        }
	        timeGrid.load();
	        return;
	     }
	     if(item.sn == items[items.length-1].sn){
          timeGrid.removeItem(item);
          timeGrid.load();
          return;
       }
	     var newItems = [];
	     timeGrid.setCell(parseInt(item.sn)+1, "periodBegin", items[parseInt(item.sn)-2].periodEnd);
	     items = timeGrid.getItems();
	     for(var i=0;i < items.length;i++){
	        if(item.sn != items[i].sn){
	          newItems.push(items[i]);
	        }
	     }
	     timeGrid.removeAllItems();
	     timeGrid.load();
	     for(var i=0;i < newItems.length;i++){
          newItems[i].sn=i+1;
       }
	     timeGrid.appendItem(newItems);
	     timeGrid.load();
	  }
	  
	  function doAddTimeStepGrid(){
	    var data = timeStepGrid.getItems();
	    if(data.length==0){
        var item = {
          sn:'1',
          timeStepBegin:'0',
          timeStepEnd:''
        };
        timeStepGrid.appendItem(item);
      }else{
        var item = {
          sn:data.length+1,
          timeStepBegin:data[data.length-1].timeStepEnd,
          timeStepEnd:''
        }; 
        timeStepGrid.appendItem(item);
      }
	  }
	  function doDelTimeStepGrid(){
      var item = timeStepGrid.getSelectedItem();
      doDelStepGrid(timeStepGrid,item);
    }
	  function doAddMillStepGrid(){
      var data = millStepGrid.getItems();
      if(data.length==0){
        var item = {
          sn:'1',
          millStepBegin:'0',
          millStepEnd:''
        };
        millStepGrid.appendItem(item);
      }else{
        var item = {
          sn:data.length+1,
          millStepBegin:data[data.length-1].millStepEnd,
          millStepEnd:''
        }; 
        millStepGrid.appendItem(item);
      }
      millStepGrid.load();
    }
    function doDelMillStepGrid(){
      var item = millStepGrid.getSelectedItem();
      doDelStepGrid(millStepGrid,item);
    }
    function doDelStepGrid(grid,item){
      if(LS.isEmpty(item)){
          LS.message("error","请选择要删除的记录");
          return;
       }
       var items = grid.getItems();
       if(items.length == 1 || item.sn == items[0].sn){
          LS.message("error","第一条记录不能删除");
          return;
       }
       if(item.sn == items[items.length-1].sn){
          var newItems = [];
          for(var i=0;i < items.length-1;i++){
	           newItems.push(items[i]);
	        }
	        grid.removeAllItems();
	        grid.load();
	        grid.appendItem(newItems);
          return;
       }
       var newItems = [];
       if(grid.name == 'millStepGrid'){
        grid.setCell(parseInt(item.sn)+1, "millStepBegin", items[parseInt(item.sn)-2].millStepEnd);
       }else if(grid.name == 'timeStepGrid'){
        grid.setCell(parseInt(item.sn)+1, "timeStepBegin", items[parseInt(item.sn)-2].timeStepEnd);
       }
       
       items = grid.getItems();
       for(var i=0;i < items.length;i++){
          if(item.sn != items[i].sn){
            newItems.push(items[i]);
          }
       }
       grid.removeAllItems();
       grid.load();
       for(var i=0;i < newItems.length;i++){
          newItems[i].sn=i+1;
       }
       grid.appendItem(newItems);
       grid.load();
    }
    function endTimeStepValidate(value,eValidator){
      var rowid = parseInt(eValidator.rowid);
      return endValidate(timeStepGrid,rowid,value);
    }
    function endMillStepValidate(value,eValidator){
      var rowid = parseInt(eValidator.rowid);
      return endValidate(millStepGrid,rowid,value);
    }
    function endValidate(grid,rowid,value){
      var beginValue ='',endValue=value;
      var isValid = true;
      if(!LS.isEmpty(endValue)){
        isValid = /^\d{1,}$/.test(value);
      }
      return {
          isValid : isValid,
          message : "请输入整数"
      }
      if(grid.name == 'millStepGrid'){
        beginValue = grid.getCell(rowid, "millStepBegin");
	    }else if(grid.name == 'timeStepGrid'){
	      beginValue = grid.getCell(rowid, "timeStepBegin");
	    }
      if(LS.isEmpty(beginValue) || LS.isEmpty(endValue))
        isValid = true;
      else{
        var begin = parseFloat(beginValue);
        var end = parseFloat(endValue);
        isValid = (beginValue < endValue);
      }
      if(isValid){
        if(grid.name == 'millStepGrid'){
	        grid.setCell(rowid+1, "millStepBegin",value);
	      }else if(grid.name == 'timeStepGrid'){
	        grid.setCell(rowid+1, "timeStepBegin",value);
	      }
      }
      return {isValid : isValid,message : "结束数值必须大于开始数值"}
    }
    </ls:script>
</ls:body>
</html>