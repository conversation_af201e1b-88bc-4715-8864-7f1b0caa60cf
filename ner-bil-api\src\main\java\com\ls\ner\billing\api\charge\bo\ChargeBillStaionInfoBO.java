package com.ls.ner.billing.api.charge.bo;

import java.io.Serializable;
import java.math.BigDecimal;

public class ChargeBillStaionInfoBO implements Serializable {

    private String chcNo;
    private String chcName;
    private String sendTime;//下发时间

    private String eftDate;//生效时间

    private BigDecimal serviceAmt;//服务费单价

    private BigDecimal elecAmt; //电价

    private String chargeMode;//电价类型
    private BigDecimal chargePricePoint;        //尖时电费单价
    private BigDecimal chargePricePeak;     //峰时电费单价
    private BigDecimal chargePriceFlat;     //平时电费单价
    private BigDecimal chargePriceVally;      //谷时电费单价

    public String getChcName() {
        return chcName;
    }

    public void setChcName(String chcName) {
        this.chcName = chcName;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getChargeMode() {
        return chargeMode;
    }

    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode;
    }

    public String getChcNo() {
        return chcNo;
    }

    public void setChcNo(String chcNo) {
        this.chcNo = chcNo;
    }

    public BigDecimal getServiceAmt() {
        return serviceAmt;
    }

    public void setServiceAmt(BigDecimal serviceAmt) {
        this.serviceAmt = serviceAmt;
    }

    public BigDecimal getElecAmt() {
        return elecAmt;
    }

    public void setElecAmt(BigDecimal elecAmt) {
        this.elecAmt = elecAmt;
    }

    public BigDecimal getChargePricePoint() {
        return chargePricePoint;
    }

    public void setChargePricePoint(BigDecimal chargePricePoint) {
        this.chargePricePoint = chargePricePoint;
    }

    public BigDecimal getChargePricePeak() {
        return chargePricePeak;
    }

    public void setChargePricePeak(BigDecimal chargePricePeak) {
        this.chargePricePeak = chargePricePeak;
    }

    public BigDecimal getChargePriceFlat() {
        return chargePriceFlat;
    }

    public void setChargePriceFlat(BigDecimal chargePriceFlat) {
        this.chargePriceFlat = chargePriceFlat;
    }

    public BigDecimal getChargePriceVally() {
        return chargePriceVally;
    }

    public void setChargePriceVally(BigDecimal chargePriceVally) {
        this.chargePriceVally = chargePriceVally;
    }
}
