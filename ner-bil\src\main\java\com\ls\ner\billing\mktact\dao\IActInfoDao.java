package com.ls.ner.billing.mktact.dao;

import com.ls.ner.billing.mktact.bo.ActInfoBo;

/**
 * 描述:资讯信息DAO
 * IActInfoDao.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 17:52
 **/
public interface IActInfoDao {

	/**
	 * 描述: 获取资讯信息
	 *
	 * @param: [bo]
	 * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
	 * 创建人:biaoxiangd
	 * 创建时间:2017/6/27 20:42
	 */
	public ActInfoBo queryActInfo(ActInfoBo bo);
	/**
	 * 描述:新增资讯信息

	 * @param: [bo]
	 * @return: int
	 * 创建人:biaoxiangd
	 * 创建时间:2017/6/27 17:52
	 */
	public int saveActInfo(ActInfoBo bo);

	/**
	 * 描述:更新资讯信息
	 *
	 * @param: [bo]
	 * @return: int
	 * 创建人:biaoxiangd
	 * 创建时间:2017/6/27 17:52
	 */
	public int updateActInfo(ActInfoBo bo);

	/**
	 * @param
	 * @description
	 * <AUTHOR>
	 * @create 2019/5/31 17:29
	 */
	public void deleteActInfoByActId(Long actId);

}