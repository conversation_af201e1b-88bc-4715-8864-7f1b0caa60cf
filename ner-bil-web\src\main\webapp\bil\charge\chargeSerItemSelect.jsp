<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="服务项目维护" />
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text type="hidden" name="itemStatus" property="itemStatus"/>
		<ls:text type="hidden" name="unUseItemNos" property="unUseItemNos"/>
		<table class="tab_search">
			<tr>
				<td><ls:label text="费用项编号" ref="itemNo" /></td>
				<td><ls:text name="itemNo"></ls:text></td>
				<td><ls:label text="费用项名称" ref="itemName" /></td>
				<td><ls:text name="itemName"></ls:text></td>
				<td colspan="3">
					<div class="pull-right">
						<ls:button text="清空" onclick="doClear" />
						<ls:button text="查询" onclick="doSearch" />
					</div>
				</td>
			</tr>
		</table>
	</ls:form>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="150px;" width="100%" showCheckBox="true" singleSelect="false" caption="服务项目列表">
					<ls:column caption="排序号" name="sn" />
					<ls:column caption="费用项编号" name="itemNo"/>
					<ls:column caption="费用项名称" name="itemName" />
					<ls:column caption="费用项单位" name="itemUnitName" />
					<ls:column caption="是否必购" name="buyTypeName" hidden="true"/>
					<ls:column caption="费用项描述" name="remarks" />
					<ls:column caption="状态" name="itemStatusName" />
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	<table>
		<tr>
			<td>
				<div class="pull-right">
					<ls:button text="保存" onclick="doSave" />
					<ls:button text="返回" onclick="doReturn" />
				</div>
			</td>
		</tr>
	</table>
	<ls:script>
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		chargeGrid.query('~/billing/chargeServiceItem/queryChargeSerItems',params,function(){});
	}
	
	function doSave(){
     	//var item = chargeGrid.getSelectedItem();
     	var item = chargeGrid.getCheckedItems();
	    if(item!=null){
	      LS.parent().setChargeSer(item);
	      doReturn();
	    }else{
	      LS.message("info","请选择一条记录!");
	    }
	}
	
	function doReturn(){
		setTimeout('LS.window.close()',200);
	}
    function doClear(){
      searchForm.clear();
    }
    
    </ls:script>
</ls:body>
</html>