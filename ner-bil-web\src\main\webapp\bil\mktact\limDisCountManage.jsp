<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="限时打折">
	<script type="text/javascript" charset="utf-8" src="<%=pubPath %>/pub/validate/validation.js"></script>
	<style>

	</style>
</ls:head>

<ls:body>
	<ls:layout-use>
		<ls:form id="limDisCountForm" name="limDisCountForm">
			<ls:text name="actId" property="actId" type="hidden"/>
			<ls:text name="actType" property="actType" type="hidden"/>
			<ls:text name="dctType" property="dctType" type="hidden"/>
			<ls:text name="actCondId" property="actCondId" type="hidden"/>
			<ls:text name="actCondDetId" property="actCondDetId" type="hidden"/>
			<ls:text name="actDctProdObj" value="" type="hidden"/>

			<ls:layout-put into="container">
				<table>
					<tr>
						<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
					</tr>
				</table>
				<table class="tab_search">
					<tr>
						<td>
							<ls:grid url="" height="200px" name="actDctGrid" caption="优惠内容" showRowNumber="true" primaryKey="id" showCheckBox="false" singleSelect="true" >
								<ls:gridToolbar name="operation">
									<ls:gridToolbarItem name="addBtn" text="添加" imageKey="add" onclick="addAct"></ls:gridToolbarItem>
									<ls:gridToolbarItem name="delBtn" text="删除" imageKey="delete" onclick="delAct"></ls:gridToolbarItem>
								</ls:gridToolbar>
								<ls:column name="actCondDetId" caption="优惠条件明细ID" hidden="true" ></ls:column>

								<ls:column caption="优惠对象" name="actDctProdName" editableEdit="false" width="50%">
									<ls:selectEditor property="actDctProdIdList"
													 valueMember="PROD_ID" name="actDctProdId" required="true"
													 displayMember="PROD_NAME">
									</ls:selectEditor>
								</ls:column>
								<ls:column name="actDctAllFree" caption="" hidden="true"></ls:column>
								<ls:column name="dctValue" caption="优惠数值" editableEdit="false" width="25%" >
									<ls:textEditor type="number"/>
								</ls:column>
								<ls:column name="unitZ" caption=""  width="15%">
									<ls:textEditor value="折" readOnly="true" />
								</ls:column>
							</ls:grid>
						</td>
					</tr>
				</table>
			</ls:layout-put>
			<ls:layout-put into="footer">
				<div class="pull-right">
					<ls:button text="上一步" onclick="doUp"/>
					<%--<ls:button text="下一步" onclick="doPut"/>--%>
					<ls:button text="保存" onclick="doSave" />
					<ls:button text="发布" onclick="doPut" />
				</div>
			</ls:layout-put>
		</ls:form>
	</ls:layout-use>

	<ls:script>

	window.actDctGrid = actDctGrid;
	window.queryGrid = queryGrid;

	window.onload = function(){
		$('#_id_actDctGrid_unitZ').removeClass("ui-state-default ui-th-column ui-th-ltr");
		queryGrid();
	}

	function queryGrid(){
		var params =  limDisCountForm.getFormData();
		actDctGrid.query("~/marketacts/limDisCountManage/queryActAll",params);
	}

<%-- 描述:添加  创建人:biaoxiangd  创建时间:2017/7/7 1:10 --%>
		function addAct(){
			actDctGrid.addRow();
		}

		<%-- 描述:删除  创建人:biaoxiangd  创建时间:2017/6/30 11:18 --%>
		function delAct(){
			var checkedItems = actDctGrid.getCheckedItems();
			if(checkedItems==null || checkedItems.length!=1){
				LS.message("info","请选择一条记录!");
				return;
			}
			var allItems = actDctGrid.getItems();
			if(allItems && allItems.length>0) {
				for(var i=0; i< allItems.length; i++) {
					if(checkedItems[0].id == allItems[i].id){
						var _actDctProdId = allItems[i].actDctProdId;
						var _actDctProdObj = actDctProdObj.getValue();
						if(!LS.isEmpty(_actDctProdId) && !LS.isEmpty(_actDctProdObj)){
							var bk = _actDctProdObj.replace(_actDctProdId,'');
							actDctProdObj.setValue(bk);
						}
					actDctGrid.removeItem(allItems[i]);
					}
				}
			}
		}

		<%-- 描述:保存  创建人:biaoxiangd  创建时间:2017/6/29 15:24 --%>
		function doSave(){
			if(!limDisCountForm.valid()){
				return false;
			}

			var dataParams = limDisCountForm.getFormData();
			var changeItems = actDctGrid.getItems();
			for(var i=0;i < changeItems.length;i++){
				if(LS.isEmpty(changeItems[i].actDctProdName) || LS.isEmpty(changeItems[i].dctValue)){
					LS.message("error","存在未选择的优惠内容或者优惠数值");
					return false;
				}
			}
			dataParams.detItems = changeItems;
			LS.ajax("~/marketacts/limDisCountManage/saveActAll",dataParams,function(data) {
				if(data.successful){
					LS.message("info","保存成功");
					return "";
				}else{
					LS.message("error",data.resultHint);
					return "0";
				}
			});
		}
		<%-- 描述:发布doPut 创建人:biaoxiangd  创建时间:2017/6/29 15:17 --%>
		function doPut(){
			<%--LS.window.close();--%>
			if(!limDisCountForm.valid()){
				return false;
			}

			var dataParams = limDisCountForm.getFormData();
			var changeItems = actDctGrid.getItems();
			for(var i=0;i < changeItems.length;i++){
			if(LS.isEmpty(changeItems[i].actDctProdName) || LS.isEmpty(changeItems[i].dctValue)){
				LS.message("error","存在未选择的优惠内容或者优惠数值");
					return false;
				}
			}
			dataParams.detItems = changeItems;
			LS.ajax("~/marketacts/limDisCountManage/saveActAll",dataParams,function(data) {
					if(data.successful){
						var params = limDisCountForm.getFormData();
						LS.ajax("~/marketacts/releaseAct",params,function(data){
							if(data.successful){
								LS.message("info",data.resultHint);
								LS.parent().doSearch();
								LS.window.close();
							}else{
								LS.message("info",data.resultHint);
							}
					});
				}else{
					LS.message("error",data.resultHint);
				}
			});
			<%--if(doSave()){
				var actIdValue = actId.getValue();
				window.location.href="<%=request.getContextPath() %>/marketacts/actInfo?actId="+actIdValue+"&actType=";
			}--%>
		}

		<%-- 描述:上一步  创建人:biaoxiangd  创建时间:2017/6/29 15:19 --%>
		function doUp(){
			var actIdValue = actId.getValue();
			window.location.href="<%=request.getContextPath() %>/marketacts/edit?actId="+actIdValue;
		}
	</ls:script>
</ls:body>
</html>