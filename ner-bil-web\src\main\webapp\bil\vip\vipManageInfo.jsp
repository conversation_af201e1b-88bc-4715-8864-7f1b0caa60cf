<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
    String astPath = (String) request.getAttribute("astPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="详情" />
<script type="text/javascript" src="../../comm/validate/validation.js"></script>
<ls:body>
    <ls:form id="vipConfigForm" name="vipConfigForm" enctype="multipart/form-data">
        <table class="tab_search">
            <tr>
                <td>
                    <ls:label text="购买类型" ref="vipType"/>
                </td>
                <td>
                    <ls:text name="vipTypeName" property="vipTypeName" required="true" enabled="false"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="会员价格" ref="vipPrice"/>
                </td>
                <td>
                    <ls:text name="vipPrice" property="vipPrice" required="true" enabled="false"></ls:text>
                </td>
                <td>元</td>
            </tr>
            <tr>
                <td>
                    <ls:label text="新客优惠价" ref="vipPriceNew"/>
                </td>
                <td>
                    <ls:text name="vipPriceNew" property="vipPriceNew" required="true" enabled="false"></ls:text>
                </td>
                <td>元</td>
            </tr>
            <tr>
                <td>
                    <ls:label text="老友优惠价" ref="vipPriceOld" />
                </td>
                <td>
                    <ls:text name="vipPriceOld" property="vipPriceOld" required="true" enabled="false"></ls:text>
                </td>
                <td>元</td>
            </tr>
            <tr>
                <td>
                    <ls:label text="连续包月说明" ref="vipMonthDesc" />
                </td>
                <td>
                    <ls:text name="vipMonthDesc" property="vipMonthDesc" required="true" enabled="false"></ls:text>
                </td>
                <td>元</td>
            </tr>
            <tr>
                <td>
                    <ls:button text="取消" onclick="cancel"></ls:button>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>

        function cancel(){
            LS.window.close();
        }

    </ls:script>
</ls:body>
</html>