package com.ls.ner.billing.api.rent.model;


import java.io.Serializable;

/**
 *区间
 * 时间段的话约定默认左闭右开？ 如
 *  计费 00:00:00<=x<06:00:00
 *  阶梯 0<=x<30<=y<50<=z
 * <AUTHOR>
 *
 */
public class RangeChargeItem extends AbstractChargeItem implements IRangeAware,Serializable {
	private static final long serialVersionUID = -7997206376553887279L;
	private Range range;
	private String isPack;
	private String priceAmt;
	public Range getRange() {
		return range;
	}

	@Override
	public void setRange(Range range) {
		this.range = range;
		
	}

	public String getIsPack() {
		return isPack;
	}

	public void setIsPack(String isPack) {
		this.isPack = isPack;
	}

	public String getPriceAmt() {
		return priceAmt;
	}

	public void setPriceAmt(String priceAmt) {
		this.priceAmt = priceAmt;
	}


	
}
