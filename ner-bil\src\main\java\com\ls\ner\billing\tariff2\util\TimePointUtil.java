package com.ls.ner.billing.tariff2.util;

import java.util.Arrays;
import java.util.Comparator;

public class TimePointUtil {

	/**
	 * 
	 * 方法说明：适用日期格式转换 如"22:00,12:00,18:00,08:00,00:00"
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 上午10:34:30
	 * History:  2016年4月10日 上午10:34:30   lipf   Created.
	 *
	 * @param splitPoints
	 * @return
	 *
	 */
	public String getResStr(String splitPoints) {

		StringBuffer buff = new StringBuffer();
		String[] pointArray = splitPoints.split("[\\s]");
		Arrays.sort(pointArray);
		if (splitPoints == null || " ".equals(splitPoints)) {
			return "";
		} else {
			for (int i = 0; i < pointArray.length; i++) {
				/*if ("00:00".equals(pointArray[i])|| "24:00".equals(pointArray[i])|| "0".equals(pointArray[i])) {
					continue;
				} else {*/
					buff.append(pointArray[i] + " ");
				//}
			}
			return buff.toString();
		}
	}
	/**
	 * 
	 * 方法说明：适用字符格式转换 如"100,10,1,0,15,20"
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 上午10:34:19
	 * History:  2016年4月10日 上午10:34:19   lipf   Created.
	 *
	 * @param splitPoints
	 * @return
	 *
	 */
	public String getResStrCom(String splitPoints) {

		StringBuffer buff = new StringBuffer();
		String[] pointArray = splitPoints.split("[\\s]");
		Arrays.sort(pointArray,new Comparator<String>() {
			public int compare(String o1, String o2) {
		        int i = Integer.parseInt(String.valueOf(o1));
		        int j = Integer.parseInt(String.valueOf(o2));
		        if (i > j) return 1;
		        if (i < j) return -1;
		        return 0;
		    }
		});
		if (splitPoints == null || " ".equals(splitPoints)) {
			return "";
		} else {
			for (int i = 0; i < pointArray.length; i++) {
				if ("00:00".equals(pointArray[i])|| "24:00".equals(pointArray[i])|| "0".equals(pointArray[i])) {
					continue;
				} else {
					buff.append(pointArray[i] + " ");
				}
			}
			return buff.toString();
		}
	}
}
