<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.tariff.dao.ITariffDao">
	<resultMap type="com.ls.ner.billing.tariff.bo.TariffPlanBo" id="myBo">
		<result column="PLAN_NAME" property="planName" />
		<result column="PLAN_NO" property="planNo" />
		<result column="OPER_NAME" property="operName" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="PE_BE" property="peBe" />
		<result column="SUB_BE" property="subBe" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="PLAN_REMARK" property="planRemark" />
	</resultMap>

	<insert id="saveAttachItem" parameterType="com.ls.ner.billing.tariff.bo.AttachItemBo">
		insert into e_attach_item(
		PLAN_NO, ATTACH_NO, ITEM_NAME, ITEM_NO, CHARGE_TYPE, BUY_IDENTITY, REMARK)
		values (
		#{planNo}, #{attachNo}, #{itemName}, #{itemNo}, #{chargeType}, #{buyIdentity}, #{remark});
	</insert>

	<insert id="saveTariff" parameterType="com.ls.ner.billing.tariff.bo.TariffPlanBo">
		insert into e_tariff_plan(
		PLAN_NAME, PLAN_NO, OPER_NAME, CREATE_TIME, PE_BE, SUB_BE, CHARGE_MODE, PLAN_REMARK, DATA_OPER_TIME, DATA_OPER_TYPE)
		values (
		#{planName}, #{planNo}, #{operName}, sysdate(), #{peBe}, #{subBe}, #{chargeMode}, #{planRemark}, sysdate(), "I");
	</insert>

	<insert id="saveMainItem" parameterType="com.ls.ner.billing.tariff.bo.TariffPlanBo">
		insert into e_main_charge_item(
		MAIN_NO, PLAN_NO, CHARGE_MODE, CHARGE_TYPE, CHARGE_UNIT, CHARGE_NUM, DATA_OPER_TIME, DATA_OPER_TYPE)
		values (
		#{mainNo}, #{planNo}, #{chargeMode}, #{chargeType}, #{chargeUnit}, #{chargeNum}, sysdate(), "I");
	</insert>

	<select id="getTariff" resultMap="myBo" parameterType="com.ls.ner.billing.tariff.bo.TariffPlanBo">
		select
		a.plan_name,
		a.plan_no,
		a.oper_name,
		a.create_time,
		a.pe_be,
		a.sub_be,
		a.charge_mode,
		a.plan_remark
		from e_tariff_plan a
		<where>
			<if test="subBe != null and subBe != ''">
				AND a.SUB_BE =#{subBe}
			</if>
		</where>
		<if test="pageEnd!=null and pageEnd!=0">
			limit #{pageBegin} ,#{pageEnd}
		</if>
	</select>

	<select id="getTariffCount" resultType="int" parameterType="com.ls.ner.billing.tariff.bo.TariffPlanBo">
		select count(1) from e_tariff_plan a
		<where>
			<if test="subBe != null and subBe != ''">
        AND a.SUB_BE =#{subBe}
      </if>
		</where>
	</select>

</mapper>