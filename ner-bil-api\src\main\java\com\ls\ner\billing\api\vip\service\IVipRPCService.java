package com.ls.ner.billing.api.vip.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ls.ner.billing.api.vip.bo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
public interface IVipRPCService {

    Map configDesc(String vipLevel) throws JsonProcessingException;

    List<RegularVipRankRPCBo> getRegularVipConfigList();

    List<VipPayRecordRPCBo> getVipPayRecord(Map<String, Object> inMap);

    Integer getRecordListByMobileCount(Map<String, Object> inMap);

    List<VipRPCBo> getVipConfig();

    void addRecord(VipPayRecordRPCBo record);

    void updateRecord(VipPayRecordRPCBo record);

    VipPayRecordRPCBo getVipPayRecordById(String recordId);

    MemberBenefitsRPCBo getMemberBenefitByVip(MemberBenefitsRPCBo bo);

    String getCustVipType(String custId);

    String updateVipLevel(VipRPCBo vipRPCBo);

    String getLevelFlag(VipRPCBo vipRPCBo);

    Map updateLevelFlag(VipRPCBo vipRPCBo);

    void openGift(VipRPCBo vipRPCBo);

    void exclusiveOffer(VipRPCBo vipRPCBo);

    void upgradeGift(VipRPCBo vipRPCBo);


    Map saveMoneyStrategy(String custVipType) throws JsonProcessingException;

    List<VipSaveRecordRPCBo> getSaveRecordByMobile(Map<String, Object> inMap);

    Map getUserSaveRecord(Map<String, Object> inMap);

    void insertVipSaveRecord(VipSaveRecordRPCBo bo);

    void updateByOrder(VipSaveRecordRPCBo bo);

    Map qryEffectTime(Map<String, Object> inMap);

    void insertVipExclusive(VipExclusivePutRPCBo rpcBo);

    void updateVipExclusive(VipExclusivePutRPCBo rpcBo);

    VipExclusivePutRPCBo getVipExclusivePut(Map<String, Object> inMap);

    /**
     * 查询会员可开票列表
     * @param map
     * @return
     */
    List<Map<String,Object>> vipListEnabledInvoices(Map<String,Object> map);

    void updateInvoiceFlag(VipInvoiceUpdateBo bo);

    String getLevelConfig();

}
