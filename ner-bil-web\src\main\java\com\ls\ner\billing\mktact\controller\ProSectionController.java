package com.ls.ner.billing.mktact.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.mktact.bo.PreSectionDetBo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.bo.PreSectionBo;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 
 *<pre><b><font color="blue">ProSectionController</font></b></pre>
 *
 *<pre><b>&nbsp;--定义余额预存的赠送段落/规则--</b></pre>
 * <pre></pre>
 * <pre>
 * <b>--样例--</b>
 * 
 * </pre>
 * JDK版本：JDK1.7
 * <AUTHOR>
 */
@Controller
public class ProSectionController extends QueryController<PreSectionBo> {
	private static final Logger logger = LoggerFactory.getLogger(ProSectionController.class);
					
	@Autowired
	private ControllerSupport controllerSupport;
	
	@ServiceAutowired("preSectionService")
	private IPreSectionService preSectionService;

	/**==========================================================赠送馈赠金==============================================*/
	/**
	 * 段落/规则 --页面初始化
	 * @param m
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/init", method = RequestMethod.GET)
	public String init(Model m, @RequestParam Long actId,@RequestParam String actSubType) {
		MarketActBo model = new MarketActBo();
		model.setActId(actId);
		model.setActSubType(actSubType);
		model.setRelease("false");

		m.addAttribute("sectionForm", model);
		return "/bil/mktact/presection";
	}

	/**
	 * 获取段落列表
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections")
	public @ItemResponseBody QueryResultObject qryPreSections(@RequestParam("actId") long actId,@RequestParam("presentBalType") String presentBalType) {
		PreSectionBo condition = new PreSectionBo();
		condition.setActId(actId);
		condition.setPresentBalType(presentBalType);
		List<PreSectionBo> list = preSectionService.qryPreSections(condition);
		return RestUtils.wrappQueryResult(list, list.size());
	}
	
	/**
	 * 创建 段落 -- 页面初始化
	 * @param m
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/edit", method = RequestMethod.GET)
	public String editPreSectionForm(Model m, @RequestParam("actId") long actId,@RequestParam String actSubType) {
		PreSectionBo model = new PreSectionBo();
		PreSectionBo condition = new PreSectionBo();
		condition.setActId(actId);
		condition.setPresentBalType(actSubType);
		List<PreSectionBo> list = preSectionService.qryPreSections(condition);
		if(CollectionUtils.isNotEmpty(list)){
			model = list.get(0);
			model.setRelease("false");
		}else{
			model.setActId(actId);
			model.setPresentBalType(actSubType);
			model.setRelease("false");
			model.setSectionRelaType("2");//1依赖 2互斥 3并存
			model.setSectionType("5");//5单向单段 6循环
		}
		m.addAttribute("preSectionForm", model);
		//普通下拉框
		controllerSupport.addStCodes(m, 
						BillConstants.CALC_PRECISION, 
						BillConstants.PresentCycleType.CODE_TYPE, 
						BillConstants.CYCLE_UNIT, 
						BillConstants.TIME_UNIT);
		//单选按钮组
		controllerSupport.addCheckListMapsCodes(m, 
						BillConstants.SECTION_RELA_TYPE, 
						BillConstants.SECTION_TYPE, 
						BillConstants.DATE_TYPE);
		
		return "/bil/mktact/presectionEdit";
	}
	
	/**
	 * 保存 -- 段落规则
	 * @param dataParams
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/saving", method = RequestMethod.POST)
	public @ItemResponseBody QueryResultObject savePreSection(@RequestBody Map<String, Object> dataParams) {
		String mark = "T";
		long presentSectionId = 0;
		try {
			PreSectionBo model = getBean(dataParams);
			presentSectionId = preSectionService.savePreSection(model);
		} catch (Exception e) {
			logger.error("", e);
			mark = "F";
		}
		return RestUtils.wrappQueryResult(new Object[]{mark, presentSectionId});
	}
	
	protected PreSectionBo getBean(Map<String, Object> dataParams) {
		PreSectionBo model = new PreSectionBo();
		controllerSupport.populateSubmitParams(dataParams, model);
		return model;
	}
	
	/**
	 * 删除 段落规则
	 * 
	 * @param presentSectionId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/delete")
	public @ItemResponseBody QueryResultObject operatePreSection(@RequestParam Long presentSectionId) {
		String mark = "T";
		try {
			preSectionService.operatePreSection(presentSectionId);
		} catch (Exception e) {
			logger.error("", e);
			mark = "F";
		}
		return RestUtils.wrappQueryResult(mark);
	}

	/**==========================================================赠送优惠券==============================================*/
	/**
	 * @param m
	 * @param actId
	 * @description 段落/规则 --赠送优惠券--页面初始化
	 * <AUTHOR>
	 * @create 2018-06-29 17:24:24
	 */
	@RequestMapping(value = "/sections/cpn/init", method = RequestMethod.GET)
	public String actinit(Model m, @RequestParam Long actId,@RequestParam String actSubType) {
		MarketActBo model = new MarketActBo();
		model.setActId(actId);
		model.setActSubType(actSubType);
		model.setRelease("false");
		m.addAttribute("sectionForm", model);
		return "/bil/mktact/marketCpnPrestore";
	}
	/**
	 * 创建 段落-- 优惠券 -- 页面初始化
	 * @param m
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/cpn/edit", method = RequestMethod.GET)
	public String editCouponForm(Model m, @RequestParam("actId") long actId,@RequestParam String actSubType) {
		PreSectionBo model = new PreSectionBo();
		PreSectionBo condition = new PreSectionBo();
		condition.setActId(actId);
		condition.setPresentBalType(actSubType);
		List<PreSectionBo> list = preSectionService.qryPreSections(condition);
		if(CollectionUtils.isNotEmpty(list)){
			model = list.get(0);
			model.setRelease("false");
		}else{
			model.setPresentBalType(actSubType);
			model.setActId(actId);
			model.setRelease("false");
			model.setSectionRelaType("2");//1依赖 2互斥 3并存
			model.setSectionType("5");//5单向单段 6循环
			model.setPresentCycleType("1");//1一次性 2周期性
			model.setMaxValue(-1);
		}
		m.addAttribute("preSectionForm", model);
		//普通下拉框
		controllerSupport.addStCodes(m,
				BillConstants.CALC_PRECISION,
				BillConstants.PresentCycleType.CODE_TYPE,
				BillConstants.CYCLE_UNIT,
				BillConstants.TIME_UNIT);
		//单选按钮组
		controllerSupport.addCheckListMapsCodes(m,
				BillConstants.SECTION_RELA_TYPE,
				BillConstants.SECTION_TYPE,
				BillConstants.DATE_TYPE);

		return "/bil/mktact/cpnPresectionEdit";
	}

	/**
	 * 获取充值段落明细
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/cpn/sections")
	public @ItemResponseBody QueryResultObject qryCpnPreSections(@RequestParam("actId") long actId,
																 @RequestParam("presentSectionId") long presentSectionId,
																 @RequestParam("presentBalType") String presentBalType) {
		Map<String, Object> map= new HashMap<String, Object>();
		map.put("actId",actId);
		map.put("presentSectionId",presentSectionId);
		map.put("presentBalType",presentBalType);
		List<Map<String,Object>> list = preSectionService.qryPreSectionsDetInfo(map);
		return RestUtils.wrappQueryResult(list, list.size());
	}

	/**
	 * 删除 段落规则
	 *
	 * @param inMap
	 * @return
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/sections/cpn/delete")
	public @ItemResponseBody QueryResultObject preSectionDetDel(@RequestBody Map<String,Object> inMap) {
		String mark = "T";
		try {
			preSectionService.preSectionDetDel(inMap);
		} catch (Exception e) {
			logger.error("", e);
			mark = "F";
		}
		return RestUtils.wrappQueryResult(mark);
	}
	@Override
	protected PreSectionBo initCondition() {
		return new PreSectionBo();
	}
}
