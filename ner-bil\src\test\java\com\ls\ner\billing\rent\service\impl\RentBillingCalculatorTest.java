package com.ls.ner.billing.rent.service.impl;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.rent.model.AbstractChargeItem;
import com.ls.ner.billing.api.rent.model.AppendChargeItem;
import com.ls.ner.billing.api.rent.model.DepositChargeItem;
import com.ls.ner.billing.api.rent.model.MainChargeItem;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.api.rent.model.Range;
import com.ls.ner.billing.api.rent.model.RangeChargeItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem;
import com.ls.ner.billing.api.rent.model.RentRun.DateItem;
import com.ls.ner.billing.api.rent.model.RentRun.PeriodItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem.Limit;
import com.pt.poseidon.ws.util.JsonUtil;

public class RentBillingCalculatorTest {

//	RentBillingCalculator rentBillingCalculator;
//	@Before
//	public void setUp() throws Exception {
//		rentBillingCalculator = new RentBillingCalculator();
//	}
//
//	@After
//	public void tearDown() throws Exception {
//		rentBillingCalculator = null;
//	}
//
////	@Test
////	public void testCalculateBillingFuncDateItemString() {
////		fail("Not yet implemented");
////	}
//
//
//
////	@Test
////	public void testCalculateMainChargeItemDateItem() {
//////		fail("Not yet implemented");
////	}
//
//	@Test
//	public void testCalculateStandardChargeItemByMill() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		
//		mainChargeItem.setChargeWay(ChargeWay.BY_MILL);
//		mainChargeItem.setChargeMode(ChargeMode.STANDARD);//其实是已经判断过才会到这了吧
//		setStandardMillChargeItem(mainChargeItem);
//		
//		System.out.println(JsonMapper.nonEmptyMapper().toJson(mainChargeItem));
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMeters(100000);
//		// 50 25 75
//		BigDecimal result = rentBillingCalculator.calculateStandardChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("88.80")));
//	}
//
//	protected void setStandardMillChargeItem(MainChargeItem mainChargeItem) {
//		SubChargeItem millChargeItem = new SubChargeItem();
//		mainChargeItem.setMillChargeItem(millChargeItem);
//		millChargeItem.setPrice("8.88");
//		millChargeItem.setPriceUnit(new PriceUnit(10,PriceUnit.KM));
//	}
//
//	@Test
//	public void testCalculateStandardChargeItemByTime() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME);
//		mainChargeItem.setChargeMode(ChargeMode.STANDARD);//其实是已经判断过才会到这了吧
//		setStandardTimeChargeItem(mainChargeItem);
//		
//	
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMinutes(200);//4H
//		
//		// 18.88 * 4 = 75.52 8.88 * 30 266.4
//		BigDecimal result = rentBillingCalculator.calculateStandardChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("75.52")));
//	}
//	
//	protected void setStandardTimeChargeItem(MainChargeItem mainChargeItem) {
//		SubChargeItem timeChargeItem = new SubChargeItem();
//		mainChargeItem.setTimeChargeItem(timeChargeItem);
//		timeChargeItem.setPrice("18.88");
//		timeChargeItem.setPriceUnit(new PriceUnit(1,PriceUnit.HOUR));
//	}
//	
//	@Test
//	public void testCalculateStandardChargeItemByTimeAndMill() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME_AND_MILL);
//		mainChargeItem.setChargeMode(ChargeMode.STANDARD);//其实是已经判断过才会到这了吧
//		setStandardMillChargeItem(mainChargeItem);
//		setStandardTimeChargeItem(mainChargeItem);
//		
//	
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMinutes(200);//4H
//		dateItem.setMeters(300000);
//		// 18.88 * 4
//		BigDecimal result = rentBillingCalculator.calculateStandardChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("341.92")));
//	}
//	
//	@Test
//	public void testCalculatePeriodChargeItemByMill() {
//		Range range1 = new Range();
//		range1.setFrom("00:00");
//		range1.setTo("08:00");
//		Range range2 = new Range();
//		range2.setFrom("08:00");
//		range2.setTo("12:00");
//		Range range3 = new Range("12:00","20:00");
//		Range range4 = new Range("20:00","24:00");
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		mainChargeItem.setChargeWay(ChargeWay.BY_MILL);
//		mainChargeItem.setChargeMode(ChargeMode.PERIOD);//其实是已经判断过才会到这了吧
//		SubChargeItem millChargeItem = new SubChargeItem();
//		mainChargeItem.setMillChargeItem(millChargeItem);
//		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
//		millChargeItem.setRangeChargeItems(rangeChargeItems);
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.KM);
//		
//		RangeChargeItem rangeChargeItem1 = new RangeChargeItem();
//		rangeChargeItem1.setRange(range1);
//		rangeChargeItem1.setPrice("0.5");
//		rangeChargeItem1.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem1);
//		
//		RangeChargeItem rangeChargeItem2 = new RangeChargeItem();
//		rangeChargeItem2.setRange(range2);
//		rangeChargeItem2.setPrice("0.8");
//		rangeChargeItem2.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem2);
//		
//		RangeChargeItem rangeChargeItem3 = new RangeChargeItem();
//		rangeChargeItem3.setRange(range3);
//		rangeChargeItem3.setPrice("1");
//		rangeChargeItem3.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem3);
//
//		RangeChargeItem rangeChargeItem4 = new RangeChargeItem();
//		rangeChargeItem4.setRange(range4);
//		rangeChargeItem4.setPrice("0.6");
//		rangeChargeItem4.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem4);		
////		List<RangeChargeItem> rangeChargeItemCopy = Lists.newArrayList(
////				rangeChargeItems);
////				Collections.sort(rangeChargeItemCopy, new Comparator<RangeChargeItem>() {
////
////					@Override
////					public int compare(RangeChargeItem o1, RangeChargeItem o2) {
////					
////						return new BigDecimal(o1.getPrice()).compareTo(new BigDecimal(o2.getPrice()));
////					}
////				});
//		DateItem dateItem = new DateItem();
//		PeriodItem periodItem = new PeriodItem();
//		periodItem.setMeters(160000);//80
//		periodItem.setRange(range1);
////		periodItemMap.put(periodItem.getRange().toString(), periodItem);
//		PeriodItem periodItem2 = new PeriodItem();
//		periodItem2.setMeters(200000);//160
//		periodItem2.setRange(range2);
//		PeriodItem periodItem3 = new PeriodItem();
//		periodItem3.setMeters(500000);//500
//		periodItem3.setRange(range3);
//		PeriodItem periodItem4 = new PeriodItem();
//		periodItem4.setMeters(100000);//60
//		periodItem4.setRange(range4);
//		List<PeriodItem> periodItems = Lists.newArrayList(periodItem,periodItem2,periodItem3,periodItem4);
//		
//		dateItem.setPeriodItems(periodItems);
//		BigDecimal result = rentBillingCalculator.calculatePeriodChargeItem(mainChargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("800.00")));
//	}
//	
//	@Test
//	public void testCalculatePeriodChargeItemByTime() {
//		Range range1 = new Range();
//		range1.setFrom("00:00");
//		range1.setTo("08:00");
//		Range range2 = new Range();
//		range2.setFrom("08:00");
//		range2.setTo("12:00");
//		Range range3 = new Range("12:00","20:00");
//		Range range4 = new Range("20:00","24:00");
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME);
//		mainChargeItem.setChargeMode(ChargeMode.PERIOD);//其实是已经判断过才会到这了吧
//		SubChargeItem timeChargeItem = new SubChargeItem();
//		mainChargeItem.setTimeChargeItem(timeChargeItem);
//		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
//		timeChargeItem.setRangeChargeItems(rangeChargeItems);
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(2);
//		priceUnit.setUnit(PriceUnit.MINUTE);
//		
//		RangeChargeItem rangeChargeItem1 = new RangeChargeItem();
//		rangeChargeItem1.setRange(range1);
//		rangeChargeItem1.setPrice("0.5");
//		rangeChargeItem1.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem1);
//		
//		RangeChargeItem rangeChargeItem2 = new RangeChargeItem();
//		rangeChargeItem2.setRange(range2);
//		rangeChargeItem2.setPrice("0.8");
//		rangeChargeItem2.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem2);
//		
//		RangeChargeItem rangeChargeItem3 = new RangeChargeItem();
//		rangeChargeItem3.setRange(range3);
//		rangeChargeItem3.setPrice("1");
//		rangeChargeItem3.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem3);
//
//		RangeChargeItem rangeChargeItem4 = new RangeChargeItem();
//		rangeChargeItem4.setRange(range4);
//		rangeChargeItem4.setPrice("0.6");
//		rangeChargeItem4.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem4);		
//		
//		DateItem dateItem = new DateItem();
//		PeriodItem periodItem = new PeriodItem();
//		periodItem.setMinutes(30);;//7.5
//		periodItem.setRange(range1);
////		periodItemMap.put(periodItem.getRange().toString(), periodItem);
//		PeriodItem periodItem2 = new PeriodItem();
//		periodItem2.setMinutes(100);;//40
//		periodItem2.setRange(range2);
//		PeriodItem periodItem3 = new PeriodItem();
//		periodItem3.setMinutes(200);;//100
//		periodItem3.setRange(range3);
//		PeriodItem periodItem4 = new PeriodItem();
//		periodItem4.setMinutes(10);;//3
//		periodItem4.setRange(range4);
//		List<PeriodItem> periodItems = Lists.newArrayList(periodItem,periodItem2,periodItem3,periodItem4);
//		
//		dateItem.setPeriodItems(periodItems);
//		BigDecimal result = rentBillingCalculator.calculatePeriodChargeItem(mainChargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("150.50")));
//	}
//	/**
//	 * 分时 时间+里程混合 测试
//	 */
//	@Test
//	public void testCalculatePeriodChargeItemByMillTime() {
//		//第一个分时区间 00:00-08:00 这个格式其实不重要，是以整个为整体作为KEY的
//		Range range1 = new Range();
//		range1.setFrom("00:00");
//		range1.setTo("08:00");
//		Range range2 = new Range();
//		range2.setFrom("08:00");
//		range2.setTo("12:00");
//		Range range3 = new Range("12:00","20:00");
//		Range range4 = new Range("20:00","24:00");
//		//主要资费
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		//计费方式是 按时间 + 里程
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME_AND_MILL);
//		//计费模式 分时
//		mainChargeItem.setChargeMode(ChargeMode.PERIOD);//其实是已经判断过才会到这了吧
//		//里程部分的计费项
//		SubChargeItem millChargeItem = new SubChargeItem();
//		mainChargeItem.setMillChargeItem(millChargeItem);
//		//里程部分计费区间设置
//		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
//		millChargeItem.setRangeChargeItems(rangeChargeItems);
//		//记录里程单位 这里设为1KM
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.KM);
//		//第一个计费区间设置 0.5元/公里
//		RangeChargeItem rangeChargeItem1 = new RangeChargeItem();
//		rangeChargeItem1.setRange(range1);
//		rangeChargeItem1.setPrice("0.5");
//		rangeChargeItem1.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem1);
//		//第二个计费区间设置 0.8元/公里
//		RangeChargeItem rangeChargeItem2 = new RangeChargeItem();
//		rangeChargeItem2.setRange(range2);
//		rangeChargeItem2.setPrice("0.8");
//		rangeChargeItem2.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem2);
//		
//		RangeChargeItem rangeChargeItem3 = new RangeChargeItem();
//		rangeChargeItem3.setRange(range3);
//		rangeChargeItem3.setPrice("1");
//		rangeChargeItem3.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem3);
//
//		RangeChargeItem rangeChargeItem4 = new RangeChargeItem();
//		rangeChargeItem4.setRange(range4);
//		rangeChargeItem4.setPrice("0.6");
//		rangeChargeItem4.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem4);	
//		//时间部分的计费区间设置
//		SubChargeItem timeChargeItem = new SubChargeItem();
//		mainChargeItem.setTimeChargeItem(timeChargeItem);
//		List<RangeChargeItem> rangeChargeItems2 = Lists.newArrayList();
//		timeChargeItem.setRangeChargeItems(rangeChargeItems2);
//		PriceUnit priceUnit2 = new PriceUnit();
//		//记录时间单位 这里设为10分钟
//		priceUnit2.setValue(10);
//		priceUnit2.setUnit(PriceUnit.MINUTE);
//		//第一个计费区间设置 5元/10分钟
//		RangeChargeItem rangeChargeItem5 = new RangeChargeItem();
//		rangeChargeItem5.setRange(range1);
//		rangeChargeItem5.setPrice("5");
//		rangeChargeItem5.setPriceUnit(priceUnit2);
//		rangeChargeItems2.add(rangeChargeItem5);
//		
//		RangeChargeItem rangeChargeItem6 = new RangeChargeItem();
//		rangeChargeItem6.setRange(range2);
//		rangeChargeItem6.setPrice("8");
//		rangeChargeItem6.setPriceUnit(priceUnit2);
//		rangeChargeItems2.add(rangeChargeItem6);
//		
//		RangeChargeItem rangeChargeItem7 = new RangeChargeItem();
//		rangeChargeItem7.setRange(range3);
//		rangeChargeItem7.setPrice("10");
//		rangeChargeItem7.setPriceUnit(priceUnit2);
//		rangeChargeItems2.add(rangeChargeItem7);
//
//		RangeChargeItem rangeChargeItem8 = new RangeChargeItem();
//		rangeChargeItem8.setRange(range4);
//		rangeChargeItem8.setPrice("6");
//		rangeChargeItem8.setPriceUnit(priceUnit2);
//		rangeChargeItems2.add(rangeChargeItem8);		
//		
//		//当日的时间里程记录
//		DateItem dateItem = new DateItem();
//		//第一段（00:00-08:00期间)的行驶记录 30分钟 40000米（40公里）
//		PeriodItem periodItem = new PeriodItem();
//		periodItem.setMinutes(30);;//15
//		periodItem.setMeters(40000);//20
//		periodItem.setRange(range1);
////		periodItemMap.put(periodItem.getRange().toString(), periodItem);
//		PeriodItem periodItem2 = new PeriodItem();
//		periodItem2.setMinutes(100);;//80
//		periodItem2.setMeters(150000);//120
//		periodItem2.setRange(range2);
//		PeriodItem periodItem3 = new PeriodItem();
//		periodItem3.setMinutes(200);;//200
//		periodItem3.setMeters(200000);//200
//		periodItem3.setRange(range3);
//		PeriodItem periodItem4 = new PeriodItem();
//		periodItem4.setMinutes(10);;//6
//		periodItem4.setMeters(10000);//6
//		periodItem4.setRange(range4);
//		List<PeriodItem> periodItems = Lists.newArrayList(periodItem,periodItem2,periodItem3,periodItem4);
//		
//		dateItem.setPeriodItems(periodItems);
//		//计算结果
//		BigDecimal result = rentBillingCalculator.calculatePeriodChargeItem(mainChargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("647.00")));
//	}
//	
//
//	@Test
//	public void testPeriodItems2Map() {
//		Range range1 = new Range();
//		range1.setFrom("00:00");
//		range1.setTo("08:00");
//		Range range2 = new Range();
//		range2.setFrom("08:00");
//		range2.setTo("12:00");
//		
//		PeriodItem periodItem = new PeriodItem();
//		periodItem.setRange(range1);
////		periodItemMap.put(periodItem.getRange().toString(), periodItem);
//		PeriodItem periodItem2 = new PeriodItem();
//		periodItem2.setRange(range2);
//		List<PeriodItem> periodItems = Lists.newArrayList(periodItem,periodItem2);
//		DateItem dateItem = new DateItem();
//		dateItem.setPeriodItems(periodItems);
//		Map<String,PeriodItem> periodItemMap = rentBillingCalculator.periodItems2Map(dateItem);
//		assertThat(periodItemMap.get(range1.toString()), is(periodItem));
//		assertThat(periodItemMap.get(range2.toString()), is(periodItem2));
//	}
//
//	@Test
//	public void testFindMatch() {
//		RangeChargeItem rangeChargeItem = new RangeChargeItem();
//		Range range1 = new Range();
//		range1.setFrom("00:00");
//		range1.setTo("08:00");
//		Range range2 = new Range();
//		range2.setFrom("08:00");
//		range2.setTo("12:00");
//		rangeChargeItem.setRange(range1);
//		com.google.common.collect.Range<String> guavaRange = com.google.common.collect.Range.<String>closedOpen("00:00", "08:00");
//		System.out.println(JsonUtil.toJson(guavaRange));
//		com.google.common.collect.Range<String> guavaRange2 = com.google.common.collect.Range.<String>open("00:00", "08:00");
//		System.out.println(JsonUtil.toJson(guavaRange2));
//		Map<String,PeriodItem> periodItemMap = Maps.newHashMap();
//	
//		PeriodItem periodItem = new PeriodItem();
//		periodItem.setRange(range1);
//		periodItemMap.put(periodItem.getRange().toString(), periodItem);
//		PeriodItem periodItem2 = new PeriodItem();
//		periodItem2.setRange(range2);
//		periodItemMap.put(periodItem2.getRange().toString(), periodItem2);
//		PeriodItem periodItemFound = rentBillingCalculator.findMatch(periodItemMap, rangeChargeItem);
//		assertThat(periodItemFound,is(periodItem));
//	}
//
//	@Test
//	public void testCalculateStepChargeItemByMill() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		
//		mainChargeItem.setChargeWay(ChargeWay.BY_MILL);
//		mainChargeItem.setChargeMode(ChargeMode.STEP);//其实是已经判断过才会到这了吧
//		setStepMillChargeItemTestData(mainChargeItem);
//		
//	
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMeters(100000);
//		// 50 25 75
//		BigDecimal result = rentBillingCalculator.calculateStepChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("75.00")));
//	}
//
//	protected void setStepMillChargeItemTestData(MainChargeItem mainChargeItem) {
//		Range range1 = new Range();
//		range1.setFrom("0");
//		range1.setTo("50");
//		Range range2 = new Range();
//		range2.setFrom("50");
//		range2.setTo("");
//		
//	
//		SubChargeItem millChargeItem = new SubChargeItem();
//		mainChargeItem.setMillChargeItem(millChargeItem);
//		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
//		millChargeItem.setRangeChargeItems(rangeChargeItems);
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.KM);
//		
//		RangeChargeItem rangeChargeItem1 = new RangeChargeItem();
//		rangeChargeItem1.setRange(range1);
//		rangeChargeItem1.setPrice("1");
//		rangeChargeItem1.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem1);		
//		
//		RangeChargeItem rangeChargeItem2 = new RangeChargeItem();
//		rangeChargeItem2.setRange(range2);
//		rangeChargeItem2.setPrice("0.5");
//		rangeChargeItem2.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem2);
//	}
//	
//	@Test
//	public void testCalculateStepChargeItemByTime() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME);
//		mainChargeItem.setChargeMode(ChargeMode.STEP);//其实是已经判断过才会到这了吧
//		setStepTimeChargeItemTestData(mainChargeItem);
//		
//	
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMinutes(400);//7H
//		// 150 40 190
//		BigDecimal result = rentBillingCalculator.calculateStepChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("190.00")));
//	}
//
//	protected void setStepTimeChargeItemTestData(MainChargeItem mainChargeItem) {
//		Range range1 = new Range();
//		range1.setFrom("0");
//		range1.setTo("5");
//		Range range2 = new Range();
//		range2.setFrom("5");
//		range2.setTo("");
//		
//		
//		
//		SubChargeItem timeChargeItem = new SubChargeItem();
//		mainChargeItem.setTimeChargeItem(timeChargeItem);
//		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
//		timeChargeItem.setRangeChargeItems(rangeChargeItems);
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.HOUR);
//		
//		RangeChargeItem rangeChargeItem1 = new RangeChargeItem();
//		rangeChargeItem1.setRange(range1);
//		rangeChargeItem1.setPrice("30");
//		rangeChargeItem1.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem1);		
//		
//		RangeChargeItem rangeChargeItem2 = new RangeChargeItem();
//		rangeChargeItem2.setRange(range2);
//		rangeChargeItem2.setPrice("20");
//		rangeChargeItem2.setPriceUnit(priceUnit);
//		rangeChargeItems.add(rangeChargeItem2);
//	}
//	
//	@Test
//	public void testCalculateStepChargeItemByTimeAndMill() {
//		MainChargeItem mainChargeItem = new MainChargeItem();
//		mainChargeItem.setChargeWay(ChargeWay.BY_TIME_AND_MILL);
//		mainChargeItem.setChargeMode(ChargeMode.STEP);//其实是已经判断过才会到这了吧
//		
//		
//		setStepMillChargeItemTestData(mainChargeItem);
//		setStepTimeChargeItemTestData(mainChargeItem);
//		
//	
//		
//		DateItem dateItem = new DateItem();
//		dateItem.setMeters(100000);
//		dateItem.setMinutes(400);//7H
//		// 50 25 75
//		// 150 40 190
//		BigDecimal result = rentBillingCalculator.calculateStepChargeItem(mainChargeItem, dateItem);
//		assertThat(result, equalTo(new BigDecimal("265.00")));
//	}
//
//	@Test
//	public void testCalculateMillChargeItemAbstractChargeItemItem() {
//		Limit min = new Limit();
//		min.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		min.setLimitQuantity(10);
//		Limit max = new Limit();
//		max.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		max.setLimitQuantity(50);
//		AbstractChargeItem millChargeItem = new AbstractChargeItem() {
//		};
//		millChargeItem.setPrice("8");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.KM);
//		millChargeItem.setPriceUnit(priceUnit);
//		// 1200m 1.2km 2km 2*8 16
//		DateItem dateItem1 = new DateItem();
//		dateItem1.setMeters(1200);
//		BigDecimal result1 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem1);
//		assertThat(result1, equalTo(new BigDecimal("16.00")));
//	}
//
//	@Test
//	public void testCalculateMillChargeItemAbstractChargeItemItemOptionalOfLimitOptionalOfLimit() {
//		Limit min = new Limit();
//		min.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		min.setLimitQuantity(10);
//		Limit max = new Limit();
//		max.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		max.setLimitQuantity(50);
//		AbstractChargeItem millChargeItem = new AbstractChargeItem() {
//		};
//		millChargeItem.setPrice("8");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.KM);
//		millChargeItem.setPriceUnit(priceUnit);
//		// 1200m 1.2km 2km <10km as 10km 10*8 80
//		DateItem dateItem1 = new DateItem();
//		dateItem1.setMeters(1200);
//		BigDecimal result1 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem1, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result1, equalTo(new BigDecimal("80.00")));
//		// 1200m 1.2km 2km no min as 2km 2*8 16
//		BigDecimal result11 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem1, Optional.<Limit>absent(),
//				Optional.<Limit> of(max));
//		assertThat(result11, equalTo(new BigDecimal("16.00")));
//		// 10001m 11km >10km <50km as 11km 11*8 88
//		DateItem dateItem2 = new DateItem();
//		dateItem2.setMeters(10001);
//		BigDecimal result2 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem2, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result2, equalTo(new BigDecimal("88.00")));
//
//		// 50001m 51km >50km 51km 50*8 400
//		DateItem dateItem3 = new DateItem();
//		dateItem3.setMeters(50001);
//		BigDecimal result3 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem3, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result3, equalTo(new BigDecimal("400.00")));
//		// 50001m 51km >50km no max 51*8 408
//		BigDecimal result31 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem3, Optional.<Limit> of(min),
//				Optional.<Limit>absent());
//		assertThat(result31, equalTo(new BigDecimal("408.00")));
//		// 9000m 9km <10km <50km as 10km 10*8 80
//		DateItem dateItem4 = new DateItem();
//		dateItem1.setMeters(9000);
//		BigDecimal result4 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem4, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result4, equalTo(new BigDecimal("80.00")));
//		// 9999m 10km =10km <50km as 10km 10*8 80
//		DateItem dateItem5 = new DateItem();
//		dateItem1.setMeters(9999);
//		BigDecimal result5 = rentBillingCalculator.calculateMillChargeItem(
//				millChargeItem, dateItem5, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result5, equalTo(new BigDecimal("80.00")));
//	}
//
//	@Test
//	public void testLimitByUnit() {
//		Limit min = new Limit();
//		min.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		min.setLimitQuantity(10);
//		Limit max = new Limit();
//		max.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		max.setLimitQuantity(50);
//		long result1 = rentBillingCalculator.limitByUnit(Optional.<Limit>of(min), Optional.<Limit>of(max), 15);
//		assertThat(result1,equalTo(15L));
//		long result2 = rentBillingCalculator.limitByUnit(Optional.<Limit>of(min), Optional.<Limit>of(max), 9);
//		assertThat(result2,equalTo(10L));
//		long result3 = rentBillingCalculator.limitByUnit(Optional.<Limit>of(min), Optional.<Limit>of(max), 51);
//		assertThat(result3,equalTo(50L));
//		
//		long result4 = rentBillingCalculator.limitByUnit(Optional.<Limit>absent(), Optional.<Limit>of(max), 9);
//		assertThat(result4,equalTo(9L));
//		
//		long result5 = rentBillingCalculator.limitByUnit(Optional.<Limit>of(min), Optional.<Limit>absent(), 51);
//		assertThat(result5,equalTo(51L));
//	}
//
//	@Test
//	public void testLimitByAmount() {
//		Limit min = new Limit();
//		min.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
//		min.setLimitQuantity(10);
//		Limit max = new Limit();
//		max.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
//		max.setLimitQuantity(50);
//		BigDecimal result1 = rentBillingCalculator.limitByAmount(Optional.<Limit>of(min), Optional.<Limit>of(max), new BigDecimal("15.55"));
//		assertThat(result1,equalTo(new BigDecimal("15.55")));
//		BigDecimal result2 = rentBillingCalculator.limitByAmount(Optional.<Limit>of(min), Optional.<Limit>of(max), new BigDecimal("9"));
//		assertThat(result2,equalTo(new BigDecimal("10.00")));
//		BigDecimal result3 = rentBillingCalculator.limitByAmount(Optional.<Limit>of(min), Optional.<Limit>of(max), new BigDecimal("51"));
//		assertThat(result3,equalTo(new BigDecimal("50.00")));
//		
//		BigDecimal result4 = rentBillingCalculator.limitByAmount(Optional.<Limit>absent(), Optional.<Limit>of(max), new BigDecimal("9"));
//		assertThat(result4,equalTo(new BigDecimal("9.00")));
//		BigDecimal result5 = rentBillingCalculator.limitByAmount(Optional.<Limit>of(min), Optional.<Limit>absent(), new BigDecimal("51"));
//		assertThat(result5,equalTo(new BigDecimal("51.00")));
//	}
//
//	@Test
//	public void testCalculateTimeChargeItemAbstractChargeItemItem() {
//		
//		AbstractChargeItem millChargeItem = new AbstractChargeItem() {
//		};
//		millChargeItem.setPrice("8");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.HOUR);
//		millChargeItem.setPriceUnit(priceUnit);
//		// 120min 2h 1< 10h 2*8 16
//		DateItem dateItem1 = new DateItem();
//		dateItem1.setMinutes(120);
//		BigDecimal result1 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem1);
//		assertThat(result1, equalTo(new BigDecimal("16.00")));
//	}
//
//	@Test
//	public void testCalculateTimeChargeItemAbstractChargeItemItemOptionalOfLimitOptionalOfLimit() {
//		Limit min = new Limit();
//		min.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		min.setLimitQuantity(10);
//		Limit max = new Limit();
//		max.setLimitType(Limit.LIMIT_TYPE_BY_UNIT);
//		max.setLimitQuantity(50);
//		AbstractChargeItem millChargeItem = new AbstractChargeItem() {
//		};
//		millChargeItem.setPrice("8");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.HOUR);
//		millChargeItem.setPriceUnit(priceUnit);
//		// 120min 2h 1< 10h as 10h 10*8 80
//		DateItem dateItem1 = new DateItem();
//		dateItem1.setMinutes(120);
//		BigDecimal result1 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem1, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result1, equalTo(new BigDecimal("80.00")));
//		// 120min 2h no min 2*8 16
//		BigDecimal result11 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem1, Optional.<Limit>absent(),
//				Optional.<Limit> of(max));
//		assertThat(result11, equalTo(new BigDecimal("16.00")));
//		// 601min 11km >10km <50km as 11km 11*8 88
//		DateItem dateItem2 = new DateItem();
//		dateItem2.setMinutes(601);
//		BigDecimal result2 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem2, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result2, equalTo(new BigDecimal("88.00")));
//
//		// 3001min 51h >50h 51h 50*8 400
//		DateItem dateItem3 = new DateItem();
//		dateItem3.setMinutes(3001);
//		BigDecimal result3 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem3, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result3, equalTo(new BigDecimal("400.00")));
//		// 3001min 51h >50h no max 51*8 408
//		BigDecimal result31 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem3, Optional.<Limit> of(min),
//				Optional.<Limit>absent());
//		assertThat(result31, equalTo(new BigDecimal("408.00")));
//		// 540min 9h <10h <50h as 10h 10*8 80
//		DateItem dateItem4 = new DateItem();
//		dateItem1.setMinutes(540);
//		BigDecimal result4 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem4, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result4, equalTo(new BigDecimal("80.00")));
//		// 599min 10h =10h  as 10h 10*8 80
//		DateItem dateItem5 = new DateItem();
//		dateItem1.setMinutes(599);
//		BigDecimal result5 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem5, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result5, equalTo(new BigDecimal("80.00")));
//		
//		// 临界值。。。
//		DateItem dateItem10 = new DateItem();
//		dateItem10.setMinutes(600);
//		BigDecimal result10 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem10, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result10, equalTo(new BigDecimal("80.00")));
//		DateItem dateItem101 = new DateItem();
//		dateItem101.setMinutes(3000);
//		BigDecimal result101 = rentBillingCalculator.calculateTimeChargeItem(
//				millChargeItem, dateItem101, Optional.<Limit> of(min),
//				Optional.<Limit> of(max));
//		assertThat(result101, equalTo(new BigDecimal("400.00")));
//	}
//
//	@Test
//	public void testGetResult() {
//		BigDecimal result = rentBillingCalculator.getResult("9.99", 12);
//		assertThat(result,equalTo(new BigDecimal(119.88).setScale(2, BigDecimal.ROUND_HALF_UP)));
//	}
//	/**
//	 * 换算使用量  整除
//	 */
//	@Test
//	public void testTransQuantityExactly() {
//		long result = rentBillingCalculator.transQuantity(60, 5);
//		assertThat(result,equalTo(12L));
//	}
//	/**
//	 * 换算使用量 不整除 不足
//	 */
//	@Test
//	public void testTransQuantity1() {
//		long result = rentBillingCalculator.transQuantity(59, 5);
//		assertThat(result,equalTo(12L));
//	}
//	/**
//	 * 换算使用量 不整除 多出
//	 */
//	@Test
//	public void testTransQuantity2() {
//		long result = rentBillingCalculator.transQuantity(61, 5);
//		assertThat(result,equalTo(13L));
//	}
//	/**
//	 * 结果转字符串 - 四舍
//	 */
//	@Test
//	public void testBigDecimal2StringDown() {
//		BigDecimal bd = new BigDecimal("2.644");
//		String result = rentBillingCalculator.bigDecimal2String(bd);
//		assertThat(result,is("2.64"));
//	}
//	/**
//	 * 结果转字符串 - 五入
//	 */
//	@Test
//	public void testBigDecimal2StringUp() {
//		BigDecimal bd = new BigDecimal("2.645");
//		String result = rentBillingCalculator.bigDecimal2String(bd);
//		assertThat(result,is("2.65"));
//	}
//
//	@Test
//	public void testCalculateDepositChargeItemDateItemFirstDay() {
//		DepositChargeItem chargeItem = new DepositChargeItem();
//		chargeItem.setPrice("80");
//		chargeItem.setPriceUnit(PriceUnit.ONCE);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(true);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("80")));
//	}
//	
//	@Test
//	public void testCalculateDepositChargeItemDateItemNotFirstDay() {
//		DepositChargeItem chargeItem = new DepositChargeItem();
//		chargeItem.setPrice("80");
//		chargeItem.setPriceUnit(PriceUnit.ONCE);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("0.00")));
//	}
//
//	@Test
//	public void testCalculateAppendChargeItemDateItemFirstDayOnce() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("13.45");
//		chargeItem.setPriceUnit(PriceUnit.ONCE);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(true);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("13.45")));
//	}
//	
//	@Test
//	public void testCalculateAppendChargeItemDateItemNotFirstDayOnce() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("13.45");
//		chargeItem.setPriceUnit(PriceUnit.ONCE);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("0.00")));
//	}
//
//	@Test
//	public void testCalculateAppendChargeItemDateItemFirstDayNotOnce() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("13.45");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(2);
//		priceUnit.setUnit(PriceUnit.KM);
//		chargeItem.setPriceUnit(priceUnit);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		dateItem.setMeters(5200);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(rentBillingCalculator.scale(new BigDecimal(13.45*3))));
//	}	
//	
//	@Test
//	public void testCalculateAppendChargeItemDateItemNotFirstDayNotOnce() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("13.45");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(2);
//		priceUnit.setUnit(PriceUnit.KM);
//		chargeItem.setPriceUnit(priceUnit);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		dateItem.setMeters(5200);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(rentBillingCalculator.scale(new BigDecimal(13.45*3))));
//	}
//	
//	@Test
//	public void testCalculateAppendChargeItemDateItemNotFirstDayNotOnceMinutes() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("0.99");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(5);
//		priceUnit.setUnit(PriceUnit.MINUTE);
//		chargeItem.setPriceUnit(priceUnit);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		dateItem.setMinutes(56);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(rentBillingCalculator.scale(new BigDecimal(0.99*12))));
//	}
//	
//	@Test
//	public void testCalculateAppendChargeItemDateItemNotFirstDayNotOnceDay() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("50");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.DAY);
//		chargeItem.setPriceUnit(priceUnit);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		dateItem.setMinutes(56);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(rentBillingCalculator.scale(new BigDecimal("50"))));
//	}
//	
//	@Test
//	public void testCalculateAppendChargeItemDateItemNotFirstDayNotOnceDays() {
//		AppendChargeItem chargeItem = new AppendChargeItem();
//		chargeItem.setPrice("50");
//		PriceUnit priceUnit = new PriceUnit();
//		priceUnit.setValue(1);
//		priceUnit.setUnit(PriceUnit.DAY);
//		chargeItem.setPriceUnit(priceUnit);
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(false);
//		dateItem.setMinutes(2000);
//		BigDecimal result = rentBillingCalculator.calculate(chargeItem, dateItem);
//		System.out.println(result);
//		assertThat(result,is(rentBillingCalculator.scale(new BigDecimal("100"))));
//	}
//
//	@Test
//	public void testCalculateOncePerRent() {
//		AbstractChargeItem chargeItem = new AbstractChargeItem() {
//		};
//		DateItem dateItem = new DateItem();
//		dateItem.setFirstDay(true);
//		BigDecimal result = rentBillingCalculator.calculateOncePerRent(chargeItem, dateItem, "13.45");
//		System.out.println(result);
//		assertThat(result,is(new BigDecimal("13.45")));
//	}

}
