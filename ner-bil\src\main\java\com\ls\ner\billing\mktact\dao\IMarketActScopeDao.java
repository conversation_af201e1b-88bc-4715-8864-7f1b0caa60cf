package com.ls.ner.billing.mktact.dao;


import com.ls.ner.billing.mktact.bo.MarketActScopeBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IMarketActScopeDao {

	public void insertMarketActScopeBatch(@Param("list") List<MarketActScopeBo> list);

	public List<MarketActScopeBo> queryScopeList(Map<String, Object> inMap);

	int deleteByActId(@Param("actId") String actId);
}