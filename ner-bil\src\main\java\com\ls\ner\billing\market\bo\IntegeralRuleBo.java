package com.ls.ner.billing.market.bo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;


/**
 * 积分规则
 * <AUTHOR>
 *
 */
public class IntegeralRuleBo  extends QueryCondition  implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1476937504993424732L;

	private String ruleId;//积分规则Id

	private String ruleNo;//积分规则编号

	private String ruleName;//积分规则名称

	private String eventType;//触发事件

	private String eventTypeName;

	private String ruleStatus;//规则状态

	private String ruleStatusName;

	private String createTime;//创建时间

	private String updateTime;//修改时间

	private String money;//发放金额

	private String userType;//用户类型

	private String isDiscriminateUser;//是否区分用户

	private String userTypeName;

	private List<Map<String,Object>> userTypeList;//用户集合
	private String userTypeListStr;

	private String maxTimes;//上限次数

	private String chargingPq;//发放  1  积分需充电?kWh

	private String handlyType;//01：创建保存，02：创建发布，03：修改


	private String extendJson;//扩展字段，与integralEventType相关\r\n若integralEventType == 5 含maxMoney,minMoney
	//积分专用属性
	private Long maxMoney;
	private Long minMoney;
	private IntegeralRuleSignExtend ruleSignExtend;

	public Long getMaxMoney() {
		return maxMoney;
	}

	public void setMaxMoney(Long maxMoney) {
		this.maxMoney = maxMoney;
	}

	public Long getMinMoney() {
		return minMoney;
	}

	public void setMinMoney(Long minMoney) {
		this.minMoney = minMoney;
	}

	public IntegeralRuleSignExtend getRuleSignExtend() {
		return ruleSignExtend;
	}

	public void setRuleSignExtend(IntegeralRuleSignExtend ruleSignExtend) {
		this.ruleSignExtend = ruleSignExtend;
	}

	public String getExtendJson() {
		return extendJson;
	}

	public void setExtendJson(String extendJson) {
		this.extendJson = extendJson;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getRuleNo() {
		return ruleNo;
	}

	public void setRuleNo(String ruleNo) {
		this.ruleNo = ruleNo;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getEventTypeName() {
		return eventTypeName;
	}

	public void setEventTypeName(String eventTypeName) {
		this.eventTypeName = eventTypeName;
	}

	public String getRuleStatus() {
		return ruleStatus;
	}

	public void setRuleStatus(String ruleStatus) {
		this.ruleStatus = ruleStatus;
	}

	public String getRuleStatusName() {
		return ruleStatusName;
	}

	public void setRuleStatusName(String ruleStatusName) {
		this.ruleStatusName = ruleStatusName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public String getMoney() {
		return money;
	}

	public void setMoney(String money) {
		this.money = money;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}

	public String getMaxTimes() {
		return maxTimes;
	}

	public void setMaxTimes(String maxTimes) {
		this.maxTimes = maxTimes;
	}

	public String getChargingNum() {
		return chargingPq;
	}

	public void setChargingNum(String chargingPq) {
		this.chargingPq = chargingPq;
	}

	public String getIsDiscriminateUser() {
		return isDiscriminateUser;
	}

	public void setIsDiscriminateUser(String isDiscriminateUser) {
		this.isDiscriminateUser = isDiscriminateUser;
	}

	public List<Map<String, Object>> getUserTypeList() {
		return userTypeList;
	}

	public void setUserTypeList(List<Map<String, Object>> userTypeList) {
		this.userTypeList = userTypeList;
	}

	public String getChargingPq() {
		return chargingPq;
	}

	public void setChargingPq(String chargingPq) {
		this.chargingPq = chargingPq;
	}

	public String getHandlyType() {
		return handlyType;
	}

	public void setHandlyType(String handlyType) {
		this.handlyType = handlyType;
	}

	public String getUserTypeListStr() {
		return userTypeListStr;
	}

	public void setUserTypeListStr(String userTypeListStr) {
		this.userTypeListStr = userTypeListStr;
	}

	@Override
	public String toString() {
		return "IntegeralRuleBo [ruleId=" + ruleId + ", ruleNo=" + ruleNo + ", ruleName=" + ruleName + ", eventType="
				+ eventType + ", eventTypeName=" + eventTypeName + ", ruleStatus=" + ruleStatus + ", ruleStatusName="
				+ ruleStatusName + ", createTime=" + createTime + ", updateTime=" + updateTime + ", money=" + money
				+ ", userType=" + userType + ", isDiscriminateUser=" + isDiscriminateUser + ", userTypeName="
				+ userTypeName + ", userTypeList=" + userTypeList + ", userTypeListStr=" + userTypeListStr
				+ ", maxTimes=" + maxTimes + ", chargingPq=" + chargingPq + ", handlyType=" + handlyType + "]";
	}



}
