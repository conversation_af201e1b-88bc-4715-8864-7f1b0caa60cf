package com.ls.ner.billing.market.bo;

import java.io.Serializable;

/**
*描述:优惠券内容，用于RPC接口
*CouponContentBo.java
*作者：biaoxiangd
*创建日期：2017-06-08 14:53
**/
public class CouponContentBo implements Serializable{
	
	private static final long serialVersionUID = -4831516380738165319L;

	private String cpnId;
	private String cpnName;
	private String cpnType;
	private String prodBusiType;
	private String cpnAmt;
	private String cpnMarks;
	private String dctType;
	private String prodId;
	private String dctCalcMethod;
	private String calcPrecision;
	private String calcPrecisionDigits;
	private String timeCondFlag;//优惠时段条件标识 1全天 2 区间
	private String timeCondBeg;//开始时间
	private String timeCondEnd;//结束时间
	private String invDate;//失效时间

	public String getCalcPrecision() {
		return calcPrecision;
	}

	public void setCalcPrecision(String calcPrecision) {
		this.calcPrecision = calcPrecision;
	}

	public String getCalcPrecisionDigits() {
		return calcPrecisionDigits;
	}

	public void setCalcPrecisionDigits(String calcPrecisionDigits) {
		this.calcPrecisionDigits = calcPrecisionDigits;
	}

	public String getDctType() {
		return dctType;
	}

	public void setDctType(String dctType) {
		this.dctType = dctType;
	}

	public String getProdId() {
		return prodId;
	}

	public void setProdId(String prodId) {
		this.prodId = prodId;
	}

	public String getDctCalcMethod() {
		return dctCalcMethod;
	}

	public void setDctCalcMethod(String dctCalcMethod) {
		this.dctCalcMethod = dctCalcMethod;
	}

	public String getCpnId() {
		return cpnId;
	}

	public void setCpnId(String cpnId) {
		this.cpnId = cpnId;
	}

	public String getCpnName() {
		return cpnName;
	}

	public void setCpnName(String cpnName) {
		this.cpnName = cpnName;
	}

	public String getCpnType() {
		return cpnType;
	}

	public void setCpnType(String cpnType) {
		this.cpnType = cpnType;
	}

	public String getProdBusiType() {
		return prodBusiType;
	}

	public void setProdBusiType(String prodBusiType) {
		this.prodBusiType = prodBusiType;
	}

	public String getCpnAmt() {
		return cpnAmt;
	}

	public void setCpnAmt(String cpnAmt) {
		this.cpnAmt = cpnAmt;
	}

	public String getCpnMarks() {
		return cpnMarks;
	}

	public void setCpnMarks(String cpnMarks) {
		this.cpnMarks = cpnMarks;
	}

	public String getTimeCondFlag() {
		return timeCondFlag;
	}

	public void setTimeCondFlag(String timeCondFlag) {
		this.timeCondFlag = timeCondFlag;
	}

	public String getTimeCondBeg() {
		return timeCondBeg;
	}

	public void setTimeCondBeg(String timeCondBeg) {
		this.timeCondBeg = timeCondBeg;
	}

	public String getTimeCondEnd() {
		return timeCondEnd;
	}

	public void setTimeCondEnd(String timeCondEnd) {
		this.timeCondEnd = timeCondEnd;
	}

	public String getInvDate() {
		return invDate;
	}

	public void setInvDate(String invDate) {
		this.invDate = invDate;
	}
}
