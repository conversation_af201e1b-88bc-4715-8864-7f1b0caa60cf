<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.IIntegeralRuleDao">

	<select id="addRule" resultType="string"  parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo"  >
		insert into e_integral_rule(
			rule_name,
			event_type,
			rule_status,
			create_time,
			is_discriminate_user,
			money,
			user_type,
			max_times,
			charging_pq,
		    extend_json
		)
		values(
			#{ruleName},#{eventType},#{ruleStatus},
			<choose>
				<when test="createTime != null and createTime != '' ">
					#{createTime},
				</when>
				<otherwise>now(),</otherwise>
			</choose>
			<choose>
				<when test="isDiscriminateUser != null and isDiscriminateUser != '' ">
					#{isDiscriminateUser},
				</when>
				<otherwise>0,</otherwise>
			</choose>
			<choose>
				<when test="money != null and money != '' ">
					#{money},
				</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="isDiscriminateUser != null and isDiscriminateUser == '1' ">
					#{userType},
				</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="maxTimes != null and maxTimes != '' ">
					#{maxTimes},
				</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="chargingPq != null and chargingPq != '' ">
					#{chargingPq},
				</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="extendJson != null and extendJson != '' ">
					#{extendJson}
				</when>
				<otherwise>null</otherwise>
			</choose>
		);
		SELECT last_insert_id()
	</select>

	<insert id="addRuleUser" parameterType="java.util.List">
		insert into e_integral_rule_user
		(rule_id,user_type,money,max_times,charging_pq,extend_json)
		values
		<foreach collection="dataList" item="item" separator=",">
		(
		<choose>
			<when test="item.ruleId != null and item.ruleId != '' ">
				#{item.ruleId},
			</when>
			<otherwise>null,</otherwise>
		</choose>
		<choose>
			<when test="item.userType != null and item.userType != '' ">
				#{item.userType},
			</when>
			<otherwise>null,</otherwise>
		</choose>
		<choose>
			<when test="item.money != null and item.money != '' ">
				#{item.money},
			</when>
			<otherwise>null,</otherwise>
		</choose>
		<choose>
			<when test="item.maxTimes != null and item.maxTimes != '' ">
				#{item.maxTimes},
			</when>
			<otherwise>null,</otherwise>
		</choose>
		<choose>
			<when test="item.chargingPq != null and item.chargingPq != '' ">
				#{item.chargingPq},
			</when>
			<otherwise>null,</otherwise>
		</choose>
		<choose>
			<when test="item.extendJson != null and item.extendJson != '' ">
				#{item.extendJson}
			</when>
			<otherwise>null</otherwise>
		</choose>
		)
		</foreach>
	</insert>



	<select id="queryRuleList" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo"
		resultType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
		select
			rule_id ruleId,
			rule_name ruleName,
			event_type eventType,
			rule_status ruleStatus,
			create_time createTime,
			update_time updateTime
		from e_integral_rule
		<where>
			<if test="ruleName != null and ruleName !=''">
	            AND rule_name like concat('%',#{ruleName},'%')
	        </if>
	        <if test="eventType != null and eventType !='' and eventType !='all'">
	            AND event_type = #{eventType}
	        </if>
			<if test="ruleStatus != null and ruleStatus !=''and ruleStatus !='all'">
				AND rule_status  = #{ruleStatus}
			</if>
		</where>
		order by create_time desc
	</select>

	<select id="queryRuleListCount" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo" resultType="int">
		select count(1) from e_integral_rule
		<where>
			<if test="ruleName != null and ruleName !=''">
	            AND rule_name like concat('%',#{ruleName},'%')
	        </if>
	        <if test="eventType != null and eventType !='' and eventType !='all'">
	            AND event_type = #{eventType}
	        </if>
			<if test="ruleStatus != null and ruleStatus !=''and ruleStatus !='all'">
				AND rule_status  = #{ruleStatus}
			</if>
		</where>
	</select>

	<update id="updateRuleStatus" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
		update e_integral_rule
		set rule_status = #{ruleStatus}
		where rule_id = #{ruleId}
	</update>

	<select id="getRuleInfo" parameterType="java.lang.String" resultType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
		select
			a.rule_id ruleId,
			a.rule_name ruleName,
			a.event_type eventType,
			a.rule_status ruleStatus,
			a.is_discriminate_user isDiscriminateUser,
			(case a.is_discriminate_user
				when '0' then a.user_type
				else b.user_type
			end) userType,
			(case a.is_discriminate_user
				when '0' then a.money
				else b.money
			end)money,
			(case a.is_discriminate_user
				when '0' then a.max_times
				else b.max_times
			end) maxTimes,
			(case a.is_discriminate_user
				when '0' then a.charging_pq
				else b.charging_pq
			end) chargingPq,
			(case a.is_discriminate_user
				 when '0' then a.extend_json
				 else b.extend_json
				end) extendJson
		from
			e_integral_rule a
		left join e_integral_rule_user b on a.rule_id  = b.rule_id
		where
			a.rule_id = #{ruleId}
	</select>

	<update id="updateRule" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
		update e_integral_rule
		<set>
			<if test="eventType != null">
			    event_type = #{eventType} ,
			</if>
			<if test="ruleStatus != null">
			    rule_status = #{ruleStatus} ,
			</if>
			<if test="money != null">
			    money = #{money} ,
			</if>
			<if test="isDiscriminateUser != null">
			    is_discriminate_user = #{isDiscriminateUser} ,
			</if>
			<if test="userType != null">
			    user_type = #{userType} ,
			</if>
			<if test="chargingPq != null">
			    charging_pq = #{chargingPq} ,
			</if>
			<if test="maxTimes != null">
			    max_times = #{maxTimes} ,
			</if>
			<if test="extendJson != null and extendJson !=''">
				extend_json = #{extendJson} ,
			</if>
			update_time = now()
		</set>
		WHERE  rule_id = #{ruleId}
	</update>

	<update id="updateRuleUser" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleUserBo">
		update e_integral_rule_user
		<set>
			<if test="money != null">
			    money = #{money} ,
			</if>
			<if test="chargingPq != null">
			    charging_pq = #{chargingPq} ,
			</if>
			<if test="maxTimes != null">
			    max_times = #{maxTimes},
			</if>
			<if test="extendJson != null and extendJson !=''">
				extend_json = #{extendJson} ,
			</if>
		</set>
		WHERE  rule_id = #{ruleId}
			and user_type  = #{userType}
	</update>

	<select id="isExistsRule" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo" resultType="int">
		select count(1) from e_integral_rule
		where event_type = #{eventType}
	</select>

	<select id="isExistsRuleUser" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleUserBo" resultType="int">
		select count(1) from e_integral_rule_user
		where rule_id=#{ruleId} and user_type = #{userType}
	</select>

	<delete id="deleteRuleUser" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleUserBo">
		delete from e_integral_rule_user
		where rule_id=#{ruleId}
	</delete>

	<select id="getRuleInfoByUser" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo"
		resultType = "java.util.Map">
		select
			a.rule_id ruleId,
			a.rule_name ruleName,
			a.event_type eventType,
			a.rule_status ruleStatus,
			a.is_discriminate_user isDiscriminateUser,
			(case a.is_discriminate_user
				when '0' then a.user_type
				else b.user_type
			end) userType,
			(case a.is_discriminate_user
				when '0' then a.money
				else b.money
			end)money,
			(case a.is_discriminate_user
				when '0' then a.max_times
				else b.max_times
			end) maxTimes,
			(case a.is_discriminate_user
				when '0' then a.charging_pq
				else b.charging_pq
			end) chargingPq,
			(case a.is_discriminate_user
			when '0' then a.extend_json
			else b.extend_json
			end) extendJson
		from
			e_integral_rule a
		left join e_integral_rule_user b on a.rule_id  = b.rule_id
		<where>
			<if test="eventType != null and eventType !=''">
			    and a.event_type = #{eventType}
			</if>
			<if test="ruleStatus != null and ruleStatus !=''">
				AND a.rule_status  = #{ruleStatus}
			</if>
			<if test="userType != null and userType !=''">
				AND b.user_type  = #{userType}
			</if>
		</where>
	</select>

	<select id="queryIntegralRule" resultType="java.util.Map">
		select
			rule_id ruleId,
			rule_name ruleName,
			event_type eventType,
			ifnull(money, 0) integralNo
		from e_integral_rule
		where rule_status = '02'
	</select>
	<select id="getIntegralRule2" resultType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
		select
			a.rule_id ruleId,
			a.rule_name ruleName,
			a.event_type eventType,
			a.rule_status ruleStatus,
			a.is_discriminate_user isDiscriminateUser,
			(case a.is_discriminate_user
				 when '0' then a.user_type
				 else b.user_type
				end) userType,
			(case a.is_discriminate_user
				 when '0' then a.money
				 else b.money
				end)money,
			(case a.is_discriminate_user
				 when '0' then a.max_times
				 else b.max_times
				end) maxTimes,
			(case a.is_discriminate_user
				 when '0' then a.charging_pq
				 else b.charging_pq
				end) chargingPq,
			(case a.is_discriminate_user
				 when '0' then a.extend_json
				 else b.extend_json
				end) extendJson
		from
			e_integral_rule a
				left join e_integral_rule_user b on a.rule_id  = b.rule_id
	</select>


</mapper>
