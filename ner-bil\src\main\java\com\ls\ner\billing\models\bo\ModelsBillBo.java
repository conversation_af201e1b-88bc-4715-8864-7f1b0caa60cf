/**
 *
 * @(#) ModelsBillBo.java
 * @Package com.ls.ner.billing.models.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.models.bo;

import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 *  Description : 
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月28日 下午4:26:06   qixiyu   Created.
 *           
 */
public class ModelsBillBo extends QueryCondition{
	private String modelId;//型号ID
	private String brandId;//品牌ID
	private String classSortCode;//业务类分类编码
	private String subType;//子分类编码，引用标准代码在【设备分类】中定义。如：充电桩 01交流式 02直流式 03交直流一体，车 01租赁/02物流/03大巴
	private String modelName;//型号
	private String spec;//规格
	private String manufacturer;//生产厂家
	private String voltage;//额定电压，单位V
	private String current;//额定电流，单位A
	private String power;//额定功率，单位kW
	private String driveMode;//准驾驾照级别，01-A1 02-A2 03-A3 04-B1 05-B2 06-B3 06-C1 07-C2 08-C3
	private String minSoc;//允许租车最低SOC，单位 %
	private String fuelType;//燃料类型
	private String loadNum;//核载人数
	private String dataOperType;//I' comment '数据操作类型
	private String dataOperTime;//数据操作时间
	private String classAndAttrData;//类及其属性
	private String prodNo;
	private String subBe;
	private String billconfFlag;
	private String orgCode;
	private String subBeFlag;
	@DicAttribute(dicName = "codeDict", key = "subBe", subType = "rentType")
	private String subBeName;
	@DicAttribute(dicName = "codeDict", key = "billconfFlag", subType = "YN")
	private String billconfFlagName;
	private String brandName;//品牌名称
	private String orgCodeName;
	public String getModelId() {
		return modelId;
	}
	public void setModelId(String modelId) {
		this.modelId = modelId;
	}
	public String getBrandId() {
		return brandId;
	}
	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}
	public String getClassSortCode() {
		return classSortCode;
	}
	public void setClassSortCode(String classSortCode) {
		this.classSortCode = classSortCode;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getModelName() {
		return modelName;
	}
	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	public String getSpec() {
		return spec;
	}
	public void setSpec(String spec) {
		this.spec = spec;
	}
	public String getManufacturer() {
		return manufacturer;
	}
	public void setManufacturer(String manufacturer) {
		this.manufacturer = manufacturer;
	}
	public String getVoltage() {
		return voltage;
	}
	public void setVoltage(String voltage) {
		this.voltage = voltage;
	}
	public String getCurrent() {
		return current;
	}
	public void setCurrent(String current) {
		this.current = current;
	}
	public String getPower() {
		return power;
	}
	public void setPower(String power) {
		this.power = power;
	}
	public String getDriveMode() {
		return driveMode;
	}
	public void setDriveMode(String driveMode) {
		this.driveMode = driveMode;
	}
	public String getMinSoc() {
		return minSoc;
	}
	public void setMinSoc(String minSoc) {
		this.minSoc = minSoc;
	}
	public String getFuelType() {
		return fuelType;
	}
	public void setFuelType(String fuelType) {
		this.fuelType = fuelType;
	}
	public String getLoadNum() {
		return loadNum;
	}
	public void setLoadNum(String loadNum) {
		this.loadNum = loadNum;
	}
	public String getDataOperType() {
		return dataOperType;
	}
	public void setDataOperType(String dataOperType) {
		this.dataOperType = dataOperType;
	}
	public String getDataOperTime() {
		return dataOperTime;
	}
	public void setDataOperTime(String dataOperTime) {
		this.dataOperTime = dataOperTime;
	}
	public String getClassAndAttrData() {
		return classAndAttrData;
	}
	public void setClassAndAttrData(String classAndAttrData) {
		this.classAndAttrData = classAndAttrData;
	}
	public String getProdNo() {
		return prodNo;
	}
	public void setProdNo(String prodNo) {
		this.prodNo = prodNo;
	}
	public String getSubBe() {
		return subBe;
	}
	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}
	public String getBillconfFlag() {
		return billconfFlag;
	}
	public void setBillconfFlag(String billconfFlag) {
		this.billconfFlag = billconfFlag;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getSubBeFlag() {
		return subBeFlag;
	}
	public void setSubBeFlag(String subBeFlag) {
		this.subBeFlag = subBeFlag;
	}
	public String getSubBeName() {
		return subBeName;
	}
	public void setSubBeName(String subBeName) {
		this.subBeName = subBeName;
	}
	public String getBillconfFlagName() {
		return billconfFlagName;
	}
	public void setBillconfFlagName(String billconfFlagName) {
		this.billconfFlagName = billconfFlagName;
	}
	public String getBrandName() {
		return brandName;
	}
	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	
}
