package com.ls.ner.billing.api.market.service;

import com.ls.ner.billing.api.market.vo.IntegralExchangeBo;
import com.ls.ner.billing.api.market.vo.IntegralInvoiceUpdateBo;

import java.util.List;
import java.util.Map;

public interface IIntegralExchangeRPCService {
    /**
     * 兑换记录列表查询
     *
     * @param bo
     * @return
     */
    Map queryList(IntegralExchangeBo bo);

    /**
     * 兑换记录详情查询
     * @param bo
     * @return
     */
    Map<String, Object> queryDetail(IntegralExchangeBo bo);

    /**
     * 兑换操作
     * @param bo
     */
    Map deal(IntegralExchangeBo bo);

    /**
     * 保存兑换记录
     * @param bo
     */
    void insert(IntegralExchangeBo bo);

    void updateStatus(String orderNo);

    /**
     * 查询积分商品可开票列表
     * @param map
     * @return
     */
    List<Map<String,Object>> integralListEnabledInvoices(Map<String,Object> map);

    void updateInvoiceFlag(IntegralInvoiceUpdateBo bo);
}
