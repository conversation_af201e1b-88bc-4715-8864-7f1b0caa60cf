<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
    String astPath = (String) request.getAttribute("astPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="会员等级配置修改" />
<script type="text/javascript" src="../../comm/validate/validation.js"></script>
<ls:body>
    <ls:form id="rankConfigForm" name="rankConfigForm" enctype="multipart/form-data">
        <ls:title text="基本信息 "></ls:title>
        <table class="tab_search">
            <tr>
                <td>
                    <ls:label text="等级" ref="rankNo" />
                </td>
                <td>
                    <ls:text name="rankNo" property="rankNo" required="true" enabled="false"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="等级名称" ref="rankName" />
                </td>
                <td>
                    <ls:text name="rankName" property="rankName" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="累计充电量下限" ref="rankMin" />
                </td>
                <td>
                    <ls:text name="rankMin" property="rankMin" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="累计充电量上限" ref="rankAmtMax" />
                </td>
                <td>
                    <ls:text name="rankMax" property="rankMax" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="累计充电金额下限" ref="rankAmtMin" />
                </td>
                <td>
                    <ls:text name="rankAmtMin" property="rankAmtMin" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="累计充电金额上限" ref="rankMax" />
                </td>
                <td>
                    <ls:text name="rankAmtMax" property="rankAmtMax" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="会员持续时间(天)" ref="durationDays" />
                </td>
                <td>
                    <ls:text name="durationDays" property="durationDays" required="true"></ls:text>
                </td>
            </tr>
            <tr>
                <td>
                </td>
                <td>
                    <ls:button text="保存" onclick="saveVipConfigs"></ls:button>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>

        window.onload = function(){
            if(rankConfigForm.rankNo=="4"){
            rankConfigForm.rankMax = "无上限"
        }
        }

        function saveVipConfigs(){
        if(!rankConfigForm.valid()){
        return false;
        }
        LS.confirm('确定要修改该配置?',function(result){
        if(result){
        rankConfigForm.submit("~/vip/regular/vipRankConfigSave",function(data){
        if(data){
        LS.message("info","修改成功");
        LS.parent().query();
        LS.window.close();
        return;
        }else{
        LS.message("info","操作失败或网络异常，请重试！");
        return;
        }
        });
        }
        });
        }

    </ls:script>
</ls:body>
</html>