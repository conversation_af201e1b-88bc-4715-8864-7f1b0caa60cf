<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.BillingOrderPricingBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="APP_NO" jdbcType="VARCHAR" property="appNo" />
    <result column="PRICING_DETAIL_NO" jdbcType="VARCHAR" property="pricingDetailNo" />
    <result column="BILLING_NO" jdbcType="VARCHAR" property="billingNo" />
    <result column="RELA_NO" jdbcType="VARCHAR" property="relaNo" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="ORG_AUTO_MODEL_KEY" jdbcType="VARCHAR" property="orgAutoModelKey" />
    <result column="VERSION" jdbcType="VARCHAR" property="version" />
    <result column="APPLY_DATE" jdbcType="VARCHAR" property="applyDate" />
    <result column="PRICING_AMT" jdbcType="DECIMAL" property="pricingAmt" />
    <result column="DATE_OPER_TYPE" jdbcType="VARCHAR" property="dateOperType" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    <result column="DETAILS" jdbcType="LONGVARCHAR" property="details" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    SYSTEM_ID, APP_NO, RELA_NO, PRICING_DETAIL_NO, BILLING_NO, SUB_BE, ORG_AUTO_MODEL_KEY, VERSION, 
    APPLY_DATE, PRICING_AMT, DATE_OPER_TYPE, DATA_OPER_TIME
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    DETAILS
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBoExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from e_billing_order_rela_pricing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_billing_order_rela_pricing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from e_billing_order_rela_pricing
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    delete from e_billing_order_rela_pricing
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    delete from e_billing_order_rela_pricing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    insert into e_billing_order_rela_pricing (SYSTEM_ID, APP_NO, PRICING_DETAIL_NO, 
      BILLING_NO, SUB_BE, ORG_AUTO_MODEL_KEY, 
      VERSION, APPLY_DATE, PRICING_AMT, 
      DATE_OPER_TYPE, DATA_OPER_TIME, DETAILS
      )
    values (#{systemId,jdbcType=BIGINT}, #{appNo,jdbcType=VARCHAR}, #{pricingDetailNo,jdbcType=VARCHAR}, 
      #{billingNo,jdbcType=VARCHAR}, #{subBe,jdbcType=VARCHAR}, #{orgAutoModelKey,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{applyDate,jdbcType=VARCHAR}, #{pricingAmt,jdbcType=DECIMAL}, 
      #{dateOperType,jdbcType=VARCHAR}, #{dataOperTime,jdbcType=TIMESTAMP}, #{details,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    insert into e_billing_order_rela_pricing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="appNo != null">
        APP_NO,
      </if>
      <if test="relaNo != null">
        RELA_NO,
      </if>
      <if test="pricingDetailNo != null">
        PRICING_DETAIL_NO,
      </if>
      <if test="billingNo != null">
        BILLING_NO,
      </if>
      <if test="subBe != null">
        SUB_BE,
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="applyDate != null">
        APPLY_DATE,
      </if>
      <if test="pricingAmt != null">
        PRICING_AMT,
      </if>
      <if test="dateOperType != null">
        DATE_OPER_TYPE,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="details != null">
        DETAILS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="appNo != null">
        #{appNo,jdbcType=VARCHAR},
      </if>
      <if test="relaNo != null">
        #{relaNo,jdbcType=VARCHAR},
      </if>
      <if test="pricingDetailNo != null">
        #{pricingDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="billingNo != null">
        #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="pricingAmt != null">
        #{pricingAmt,jdbcType=DECIMAL},
      </if>
      <if test="dateOperType != null">
        #{dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="details != null">
        #{details,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    select count(*) from e_billing_order_rela_pricing
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.appNo != null">
        APP_NO = #{record.appNo,jdbcType=VARCHAR},
      </if>
      <if test="record.pricingDetailNo != null">
        PRICING_DETAIL_NO = #{record.pricingDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billingNo != null">
        BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subBe != null">
        SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      </if>
      <if test="record.orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        VERSION = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        APPLY_DATE = #{record.applyDate,jdbcType=VARCHAR},
      </if>
      <if test="record.pricingAmt != null">
        PRICING_AMT = #{record.pricingAmt,jdbcType=DECIMAL},
      </if>
      <if test="record.dateOperType != null">
        DATE_OPER_TYPE = #{record.dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.details != null">
        DETAILS = #{record.details,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      APP_NO = #{record.appNo,jdbcType=VARCHAR},
      PRICING_DETAIL_NO = #{record.pricingDetailNo,jdbcType=VARCHAR},
      BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      VERSION = #{record.version,jdbcType=VARCHAR},
      APPLY_DATE = #{record.applyDate,jdbcType=VARCHAR},
      PRICING_AMT = #{record.pricingAmt,jdbcType=DECIMAL},
      DATE_OPER_TYPE = #{record.dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DETAILS = #{record.details,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      APP_NO = #{record.appNo,jdbcType=VARCHAR},
      PRICING_DETAIL_NO = #{record.pricingDetailNo,jdbcType=VARCHAR},
      BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      VERSION = #{record.version,jdbcType=VARCHAR},
      APPLY_DATE = #{record.applyDate,jdbcType=VARCHAR},
      PRICING_AMT = #{record.pricingAmt,jdbcType=DECIMAL},
      DATE_OPER_TYPE = #{record.dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    <set>
      <if test="appNo != null">
        APP_NO = #{appNo,jdbcType=VARCHAR},
      </if>
      <if test="pricingDetailNo != null">
        PRICING_DETAIL_NO = #{pricingDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="billingNo != null">
        BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        SUB_BE = #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        APPLY_DATE = #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="pricingAmt != null">
        PRICING_AMT = #{pricingAmt,jdbcType=DECIMAL},
      </if>
      <if test="dateOperType != null">
        DATE_OPER_TYPE = #{dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="details != null">
        DETAILS = #{details,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    set APP_NO = #{appNo,jdbcType=VARCHAR},
      PRICING_DETAIL_NO = #{pricingDetailNo,jdbcType=VARCHAR},
      BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      VERSION = #{version,jdbcType=VARCHAR},
      APPLY_DATE = #{applyDate,jdbcType=VARCHAR},
      PRICING_AMT = #{pricingAmt,jdbcType=DECIMAL},
      DATE_OPER_TYPE = #{dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DETAILS = #{details,jdbcType=LONGVARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.BillingOrderPricingBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:34:41 CST 2016.
    -->
    update e_billing_order_rela_pricing
    set APP_NO = #{appNo,jdbcType=VARCHAR},
      PRICING_DETAIL_NO = #{pricingDetailNo,jdbcType=VARCHAR},
      BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      VERSION = #{version,jdbcType=VARCHAR},
      APPLY_DATE = #{applyDate,jdbcType=VARCHAR},
      PRICING_AMT = #{pricingAmt,jdbcType=DECIMAL},
      DATE_OPER_TYPE = #{dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>