/**
 *
 * @(#) IRentBillRpcService.java
 * @Package com.ls.ner.billing.api.rent.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api.rent.service;

import java.util.Map;

/**
 *  Description : 租车定价RPC接口
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月27日 上午10:15:02   qixiyu   Created.
 *           
 */
public interface IRentBillRpcService {

	/**
	 * 
	 * Method description : 获取租车产品定价描述
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月27日 上午10:45:17
	 * History:  2016年10月27日 上午10:45:17   qixiyu   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public Map<String, Object> getRentPrice(Map<String, Object> inMap);
	
	/**
	 * 
	 * Method description : 租车计费（含预收、结算）
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月28日 上午11:10:45
	 * History:  2016年10月28日 上午11:10:45   qixiyu   Created.
	 *
	 * @param inMap
	 * @return
	 * @throws Exception 
	 *
	 */
	public Map<String, Object> rentBilling(Map<String, Object> inMap) throws Exception;
	
	/**
	 * 
	 * Method description : 查询订单计费结果
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月29日 下午3:35:39
	 * History:  2016年10月29日 下午3:35:39   qixiyu   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public Map<String, Object> getOrderPrice(Map<String, Object> inMap);
	
	/**
	 * 
	 * Method description : 查询物流定价
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月29日 下午3:54:01
	 * History:  2016年10月29日 下午3:54:01   qixiyu   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public Map<String, Object> getDeliverPrice(Map<String, Object> inMap);
	
	/**
	 * 
	 * Method description : 物流计费（含预收、结算）
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月29日 下午4:38:57
	 * History:  2016年10月29日 下午4:38:57   qixiyu   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	public Map<String, Object> deliverBilling(Map<String, Object> inMap);
}
