package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.Date;

public class BillingOrderPricingBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String appNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.PRICING_DETAIL_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String pricingDetailNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String subBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String orgAutoModelKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.VERSION
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.APPLY_DATE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String applyDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private BigDecimal pricingAmt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String dateOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela_pricing.DETAILS
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    private String details;

    private String relaNo;
    /**
     * 段落起始值
     */
    private String startValue;
    /**
     * 段落终止值
     */
    private String endValue;
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.SYSTEM_ID
     *
     * @return the value of e_billing_order_rela_pricing.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.SYSTEM_ID
     *
     * @param systemId the value for e_billing_order_rela_pricing.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.APP_NO
     *
     * @return the value of e_billing_order_rela_pricing.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getAppNo() {
        return appNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.APP_NO
     *
     * @param appNo the value for e_billing_order_rela_pricing.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setAppNo(String appNo) {
        this.appNo = appNo == null ? null : appNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.PRICING_DETAIL_NO
     *
     * @return the value of e_billing_order_rela_pricing.PRICING_DETAIL_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getPricingDetailNo() {
        return pricingDetailNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.PRICING_DETAIL_NO
     *
     * @param pricingDetailNo the value for e_billing_order_rela_pricing.PRICING_DETAIL_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setPricingDetailNo(String pricingDetailNo) {
        this.pricingDetailNo = pricingDetailNo == null ? null : pricingDetailNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.BILLING_NO
     *
     * @return the value of e_billing_order_rela_pricing.BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.BILLING_NO
     *
     * @param billingNo the value for e_billing_order_rela_pricing.BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setBillingNo(String billingNo) {
        this.billingNo = billingNo == null ? null : billingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.SUB_BE
     *
     * @return the value of e_billing_order_rela_pricing.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getSubBe() {
        return subBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.SUB_BE
     *
     * @param subBe the value for e_billing_order_rela_pricing.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setSubBe(String subBe) {
        this.subBe = subBe == null ? null : subBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.ORG_AUTO_MODEL_KEY
     *
     * @return the value of e_billing_order_rela_pricing.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getOrgAutoModelKey() {
        return orgAutoModelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.ORG_AUTO_MODEL_KEY
     *
     * @param orgAutoModelKey the value for e_billing_order_rela_pricing.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setOrgAutoModelKey(String orgAutoModelKey) {
        this.orgAutoModelKey = orgAutoModelKey == null ? null : orgAutoModelKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.VERSION
     *
     * @return the value of e_billing_order_rela_pricing.VERSION
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.VERSION
     *
     * @param version the value for e_billing_order_rela_pricing.VERSION
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.APPLY_DATE
     *
     * @return the value of e_billing_order_rela_pricing.APPLY_DATE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getApplyDate() {
        return applyDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.APPLY_DATE
     *
     * @param applyDate the value for e_billing_order_rela_pricing.APPLY_DATE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate == null ? null : applyDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.PRICING_AMT
     *
     * @return the value of e_billing_order_rela_pricing.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public BigDecimal getPricingAmt() {
        return pricingAmt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.PRICING_AMT
     *
     * @param pricingAmt the value for e_billing_order_rela_pricing.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setPricingAmt(BigDecimal pricingAmt) {
        this.pricingAmt = pricingAmt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.DATE_OPER_TYPE
     *
     * @return the value of e_billing_order_rela_pricing.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getDateOperType() {
        return dateOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.DATE_OPER_TYPE
     *
     * @param dateOperType the value for e_billing_order_rela_pricing.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setDateOperType(String dateOperType) {
        this.dateOperType = dateOperType == null ? null : dateOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.DATA_OPER_TIME
     *
     * @return the value of e_billing_order_rela_pricing.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_billing_order_rela_pricing.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela_pricing.DETAILS
     *
     * @return the value of e_billing_order_rela_pricing.DETAILS
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getDetails() {
        return details;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela_pricing.DETAILS
     *
     * @param details the value for e_billing_order_rela_pricing.DETAILS
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setDetails(String details) {
        this.details = details == null ? null : details.trim();
    }

	public String getRelaNo() {
		return relaNo;
	}

	public void setRelaNo(String relaNo) {
		this.relaNo = relaNo;
	}

	public String getStartValue() {
		return startValue;
	}

	public void setStartValue(String startValue) {
		this.startValue = startValue;
	}

	public String getEndValue() {
		return endValue;
	}

	public void setEndValue(String endValue) {
		this.endValue = endValue;
	}
}