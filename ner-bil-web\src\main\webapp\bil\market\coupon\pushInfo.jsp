<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="">
<style type="text/css">
.hiddenFlow{
overflow-x:hidden; 
}
</style>
</ls:head>
<ls:body>
	<ls:form name="couponPushForm" cssClass="hiddenFlow">
		<ls:text name="cpnNo" property="cpnNo" visible="false"></ls:text>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="couponGrid" caption="推广日记列表" showRowNumber="false"
						height="410px" width="100%" showCheckBox="false" singleSelect="true" >
						
						<ls:column caption="操作类型" name="pushOperTypeName" align="center" />
						<ls:column caption="推广渠道" name="pushChannelName" align="center" />
						<ls:column caption="推广方式" name="pushWayName" align="center" />
						<ls:column caption="操作时间" name="pushTime" align="center" />
						<ls:column caption="操作人" name="pushEmp" align="center" />
						<ls:pager pageSize="15,20"></ls:pager>
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
	query();
    function query(){
      var datas={};
      datas = couponPushForm.getFormData();
      couponGrid.query("~/billing/coupon/getPushLog",datas);
    }
    </ls:script>
</ls:body>
</html>