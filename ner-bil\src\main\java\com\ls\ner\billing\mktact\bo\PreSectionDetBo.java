package com.ls.ner.billing.mktact.bo;

import java.math.BigDecimal;

public class PreSectionDetBo {

	/**
	  * 预存段落ID
	  */
	private long sectionDetId;
	/**
	  * 赠送规则标识
	  */
	private long presentSectionId;
	/**
	  * 起始值
	  */
	private long refCeil;
	/**
	  * 终止值
	  */
	private long refFloor;
	/**
	  * 赠送基值
	  */
	private long baseValue;
	/**
	  * 赠送积分
	  */
	private long integralNum;
	/**
	  * 调整计算方法
	  */
	private String calcMethod;
	/**
	  * 调整计算值
	  */
	private long calcValue;
	/**
	  * 调整比例
	  */
	private BigDecimal calcRate;
	/**
	  * 段落明细序号
	  */
	private int sectionDetSn;
	
	/**  
	 * 获取预存段落ID  
	 * @return sectionDetId 预存段落ID  
	 */
	public long getSectionDetId() {
		return sectionDetId;
	}
	/**  
	 * 设置预存段落ID  
	 * @param sectionDetId 预存段落ID  
	 */
	public void setSectionDetId(long sectionDetId) {
		this.sectionDetId = sectionDetId;
	}
	/**  
	 * 获取赠送规则标识  
	 * @return presentSectionId 赠送规则标识  
	 */
	public long getPresentSectionId() {
		return presentSectionId;
	}
	/**  
	 * 设置赠送规则标识  
	 * @param presentSectionId 赠送规则标识  
	 */
	public void setPresentSectionId(long presentSectionId) {
		this.presentSectionId = presentSectionId;
	}
	/**  
	 * 获取起始值
	 * @return refCeil 起始值
	 */
	public long getRefCeil() {
		return refCeil;
	}
	/**  
	 * 设置起始值
	 * @param refCeil 起始值
	 */
	public void setRefCeil(long refCeil) {
		this.refCeil = refCeil;
	}
	/**  
	 * 获取终止值  
	 * @return refFloor 终止值
	 */
	public long getRefFloor() {
		return refFloor;
	}
	/**  
	 * 设置终止值 
	 * @param refFloor 终止值
	 */
	public void setRefFloor(long refFloor) {
		this.refFloor = refFloor;
	}
	/**  
	 * 获取赠送基值  
	 * @return baseValue 赠送基值  
	 */
	public long getBaseValue() {
		return baseValue;
	}
	/**  
	 * 设置赠送基值  
	 * @param baseValue 赠送基值  
	 */
	public void setBaseValue(long baseValue) {
		this.baseValue = baseValue;
	}
	/**  
	 * 获取赠送积分  
	 * @return integralNum 赠送积分  
	 */
	public long getIntegralNum() {
		return integralNum;
	}
	/**  
	 * 设置赠送积分  
	 * @param integralNum 赠送积分  
	 */
	public void setIntegralNum(long integralNum) {
		this.integralNum = integralNum;
	}
	/**  
	 * 获取调整计算方法  
	 * @return calcMethod 调整计算方法  
	 */
	public String getCalcMethod() {
		return calcMethod;
	}
	/**  
	 * 设置调整计算方法  
	 * @param calcMethod 调整计算方法  
	 */
	public void setCalcMethod(String calcMethod) {
		this.calcMethod = calcMethod;
	}
	/**  
	 * 获取调整计算值  
	 * @return calcValue 调整计算值  
	 */
	public long getCalcValue() {
		return calcValue;
	}
	/**  
	 * 设置调整计算值  
	 * @param calcValue 调整计算值  
	 */
	public void setCalcValue(long calcValue) {
		this.calcValue = calcValue;
	}
	/**  
	 * 获取调整比例  
	 * @return calcRate 调整比例  
	 */
	public BigDecimal getCalcRate() {
		return calcRate;
	}
	/**  
	 * 设置调整比例  
	 * @param calcRate 调整比例  
	 */
	public void setCalcRate(BigDecimal calcRate) {
		this.calcRate = calcRate;
	}
	/**  
	 * 获取段落明细序号  
	 * @return sectionDetSn 段落明细序号  
	 */
	public int getSectionDetSn() {
		return sectionDetSn;
	}
	/**  
	 * 设置段落明细序号  
	 * @param sectionDetSn 段落明细序号  
	 */
	public void setSectionDetSn(int sectionDetSn) {
		this.sectionDetSn = sectionDetSn;
	}
	
}
