<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券管理"></ls:head>
<ls:body>
    <ls:form name="cityForm" id="cityForm">
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <ls:text visible="false"  name="removeCitys" property="removeCitys"></ls:text>
            <tr>
                <td><ls:label text="城市"/></td>
                <td><ls:text name="cityName"></ls:text></td>
                <td>
                    <div class="pull-right">
                        <ls:button text="查询" onclick="cityQuery"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <ls:grid url="" name="cityGrid" caption="城市列表" height="230px" showCheckBox="true" singleSelect="false">
                        <ls:gridToolbar name="operation">
                            <ls:gridToolbarItem name="add" text="选择" imageKey="add" onclick="addCity" ></ls:gridToolbarItem>
                        </ls:gridToolbar>
                        <ls:column caption="城市编码" name="areaCode" hidden="true"/>
                        <ls:column caption="城市" name="areaName" />
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        cityQuery();
        function cityQuery(){
            var data = cityForm.getFormData();
            cityGrid.query("~/billing/coupon/queryCityInfo", data);
        }
        function addCity(){
            var items = cityGrid.getCheckedItems();
            LS.parent().selectCpnCity(items);
            LS.window.close();
        }
    </ls:script>
</ls:body>
</html>