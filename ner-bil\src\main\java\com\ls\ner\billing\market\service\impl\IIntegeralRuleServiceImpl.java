package com.ls.ner.billing.market.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONUtil;
import com.ls.ner.billing.market.bo.IntegeralRuleSignExtend;
import com.ls.ner.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.bo.IntegeralRuleUserBo;
import com.ls.ner.billing.market.dao.IIntegeralRuleDao;
import com.ls.ner.billing.market.service.IIntegeralRuleService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;

@Service(target = {ServiceType.LOCAL}, value = "integeralRuleService")
public class IIntegeralRuleServiceImpl implements IIntegeralRuleService {
	
	private static final Logger log = LoggerFactory.getLogger(IIntegeralRuleServiceImpl.class);

	@Autowired(required = true)
    private IIntegeralRuleDao dao;
	
	@Override
	public List<IntegeralRuleBo> queryRuleList(IntegeralRuleBo integeralRuleBo) {
		
		return dao.queryRuleList(integeralRuleBo);
	}

	@Override
	public int queryRuleListCount(IntegeralRuleBo integeralRuleBo) {
		
		return dao.queryRuleListCount(integeralRuleBo);
	}

	@Override
	public void addRule(IntegeralRuleBo integeralRuleBo) throws Exception {
		IntegeralRuleBo bo = new IntegeralRuleBo();
		bo.setRuleName(integeralRuleBo.getRuleName());
		bo.setEventType(integeralRuleBo.getEventType());
		String eventType = integeralRuleBo.getEventType();
		boolean isDiscriminateUser = "1".equals(integeralRuleBo.getIsDiscriminateUser());
		
		if ("02".equals(integeralRuleBo.getHandlyType())) {//发布
			bo.setRuleStatus("02");
		} else {
			bo.setRuleStatus("01");
		}
		int count = 0;
		count = dao.isExistsRule(integeralRuleBo);
		if (count > 0) {
			throw new RuntimeException("该事件已存在规则，请在原规则上做修改！");
		}
		if ("01".equals(eventType)) {//注册
			bo.setMoney(integeralRuleBo.getMoney());
			dao.addRule(bo);
		} else if ("02".equals(eventType) || "03".equals(eventType) ) {//绑定车辆/邀新
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			if (isDiscriminateUser) {
				String ruleId = dao.addRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---addRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					userBo.setMoney(map.get("money").toString());
					userBo.setMaxTimes(map.get("maxTimes").toString());
					ruleUserList.add(userBo);
				}
				dao.addRuleUser(ruleUserList);
			} else {
				bo.setMoney(integeralRuleBo.getMoney());
				bo.setMaxTimes(integeralRuleBo.getMaxTimes());
				dao.addRule(bo);
			}
		} else if ("04".equals(eventType) ) {//充电消费
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			if (isDiscriminateUser) {
				String ruleId = dao.addRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---addRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					userBo.setChargingPq(map.get("chargingPq").toString());
					ruleUserList.add(userBo);
				}
				dao.addRuleUser(ruleUserList);
			} else {
				bo.setChargingPq(integeralRuleBo.getChargingPq());
				dao.addRule(bo);
			}
		} else if ("05".equals(eventType) ) {//签到
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			bo.setExtendJson(integeralRuleBo.getExtendJson());
			if (isDiscriminateUser) {
				String ruleId = dao.addRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---addRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					IntegeralRuleSignExtend signExtend = JSONUtil.parseObj(map.get("extendJson")).toBean(IntegeralRuleSignExtend.class);
					userBo.setExtendJson(JSONUtil.toJsonStr(signExtend));
					ruleUserList.add(userBo);
				}
				dao.addRuleUser(ruleUserList);
			} else {
				bo.setMoney(integeralRuleBo.getMoney());
				dao.addRule(bo);
			}
		}
	}

	@Override
	public void editRule(IntegeralRuleBo integeralRuleBo) {
		
		log.info("---updateRule---入参:"+JsonUtil.obj2Json(integeralRuleBo));
		IntegeralRuleBo bo = new IntegeralRuleBo();
		bo.setRuleName(integeralRuleBo.getRuleName());
		bo.setEventType(integeralRuleBo.getEventType());
		String eventType = integeralRuleBo.getEventType();
		String ruleId = integeralRuleBo.getRuleId();
		int count = 0;
		bo.setRuleId(ruleId);
		boolean isDiscriminateUser = "1".equals(integeralRuleBo.getIsDiscriminateUser());
		if ("01".equals(eventType)) {//注册
			bo.setMoney(integeralRuleBo.getMoney());
			dao.updateRule(bo);
		} else if ("02".equals(eventType) || "03".equals(eventType) ) {//绑定车辆/邀新
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			if (isDiscriminateUser) {
				dao.updateRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---updateRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					count = 0;
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					userBo.setMoney(map.get("money").toString());
					userBo.setMaxTimes(map.get("maxTimes").toString());
					count = dao.isExistsRuleUser(userBo);
					if (count>0) {
						dao.updateRuleUser(userBo);
					} else {
						ruleUserList.add(userBo);
					}
				}
				if (ruleUserList.size()>0) {
					dao.addRuleUser(ruleUserList);
				}
			} else {
				bo.setMoney(integeralRuleBo.getMoney());
				bo.setMaxTimes(integeralRuleBo.getMaxTimes());
				dao.updateRule(bo);
				
				IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
				count = 0;
				userBo.setRuleId(ruleId);
				userBo.setUserType("0");//普通用户
				count = dao.isExistsRuleUser(userBo);
				if (count>0) {
					dao.deleteRuleUser(userBo);
				}
			}
		} else if ("04".equals(eventType) ) {//充电消费
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			if (isDiscriminateUser) {
				dao.updateRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---updateRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					userBo.setChargingPq(map.get("chargingPq").toString());
					count = 0;
					count = dao.isExistsRuleUser(userBo);
					if (count>0) {
						dao.updateRuleUser(userBo);
					} else {
						ruleUserList.add(userBo);
					}
				}
				if (ruleUserList.size()>0) {
					dao.addRuleUser(ruleUserList);
				}
			} else {
				bo.setChargingPq(integeralRuleBo.getChargingPq());
				dao.updateRule(bo);
				
				IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
				count = 0;
				userBo.setRuleId(ruleId);
				userBo.setUserType("0");//普通用户
				count = dao.isExistsRuleUser(userBo);
				if (count>0) {
					dao.deleteRuleUser(userBo);
				}
				
			}
		} else if ("05".equals(eventType) ) {//签到
			bo.setIsDiscriminateUser(integeralRuleBo.getIsDiscriminateUser());
			bo.setExtendJson(integeralRuleBo.getExtendJson());

			if (isDiscriminateUser) {
				dao.updateRule(bo);
				
				List<Map<String,Object>> userTypeList =  integeralRuleBo.getUserTypeList();
				log.info("---updateRule---userTypeList:"+userTypeList);
				List<IntegeralRuleUserBo> ruleUserList = new ArrayList<IntegeralRuleUserBo>();
				for (Map<String, Object> map : userTypeList) {
					IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
					userBo.setRuleId(ruleId);
					userBo.setUserType("0");//普通用户
					IntegeralRuleSignExtend signExtend = JSONUtil.parseObj(map.get("extendJson")).toBean(IntegeralRuleSignExtend.class);
					userBo.setExtendJson(JSONUtil.toJsonStr(signExtend));
					count = 0;
					count = dao.isExistsRuleUser(userBo);
					if (count>0) {
						dao.updateRuleUser(userBo);
					} else {
						ruleUserList.add(userBo);
					}
				}
				if (ruleUserList.size()>0) {
					dao.addRuleUser(ruleUserList);
				}
			} else {
				bo.setMoney(integeralRuleBo.getMoney());
				dao.updateRule(bo);
				
				IntegeralRuleUserBo userBo = new IntegeralRuleUserBo();
				count = 0;
				userBo.setRuleId(ruleId);
				userBo.setUserType("0");//普通用户
				count = dao.isExistsRuleUser(userBo);
				if (count>0) {
					dao.deleteRuleUser(userBo);
				}
			}
		}
		
	}

	@Override
	public void updateRuleStatus(IntegeralRuleBo integeralRuleBo) {
		dao.updateRuleStatus(integeralRuleBo);
	}

	@Override
	public IntegeralRuleBo getRuleInfo(String ruleId) {
		IntegeralRuleBo bo = dao.getRuleInfo(ruleId);
		if ("05".equals(bo.getEventType()) && StringUtils.isNotBlank(bo.getExtendJson())) {
			String extendJson = bo.getExtendJson();
			IntegeralRuleSignExtend signExtend = JSONUtil.parseObj(extendJson).toBean(IntegeralRuleSignExtend.class);
			bo.setRuleSignExtend(signExtend);
			bo.setMaxMoney(signExtend.getMaxMoney());
			bo.setMinMoney(signExtend.getMinMoney());
		}
		return bo;
	}

	
	
}
