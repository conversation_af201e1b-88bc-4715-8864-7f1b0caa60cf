<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.config.dao.IBillingConfigDao">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="BILLING_NO" jdbcType="VARCHAR" property="billingNo" />
    <result column="VERSION" jdbcType="VARCHAR" property="version" />
    <result column="IS_LATEST_VERSION" jdbcType="VARCHAR" property="isLatestVersion" />
    <result column="IS_ADJUSTED" jdbcType="VARCHAR" property="isAdjusted" />
    <result column="BILLING_CONFIG_NAME" jdbcType="VARCHAR" property="billingConfigName" />
    <result column="UNIFORM_PRICE" jdbcType="VARCHAR" property="uniformPrice" />
    <result column="PE_BE" jdbcType="VARCHAR" property="peBe" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="CHARGE_WAY" jdbcType="VARCHAR" property="chargeWay" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="MIN_PRICE_UNIT_DESC" jdbcType="VARCHAR" property="minPriceUnitDesc" />
    <result column="MAX_PRICE_UNIT_DESC" jdbcType="VARCHAR" property="maxPriceUnitDesc" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ORG_AUTO_MODEL_KEY" jdbcType="VARCHAR" property="orgAutoModelKey" />
    <result column="OPER_ORG_CODE" jdbcType="VARCHAR" property="operOrgCode" />
    <result column="RT_NO" jdbcType="VARCHAR" property="rtNo" />
    <result column="AUTO_MODEL_NO" jdbcType="VARCHAR" property="autoModelNo" />
    <result column="AUTO_MODEL_NAME" jdbcType="VARCHAR" property="autoModelName" />
    <result column="APPLY_DATE" jdbcType="VARCHAR" property="applyDate" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="EFT_DATE" jdbcType="VARCHAR" property="eftDate" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
  </resultMap>
  
  <resultMap id="ExtendResultMap" type="com.ls.ner.billing.config.bo.BillingConfigFunc">
    <result column="BILLING_NO" jdbcType="VARCHAR" property="billingNo" />
    <result column="IS_ADJUSTED" jdbcType="VARCHAR" property="isAdjusted" />
    <result column="BILLING_CONFIG_NAME" jdbcType="VARCHAR" property="billingConfigName" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="CHARGE_WAY" jdbcType="VARCHAR" property="chargeWay" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="AUTO_MODEL_NO" jdbcType="VARCHAR" property="autoModelNo" />
    <result column="AUTO_MODEL_NAME" jdbcType="VARCHAR" property="autoModelName" />
    <result column="APPLY_DATE" jdbcType="VARCHAR" property="applyDate" />
    <result column="EFT_DATE" jdbcType="VARCHAR" property="eftDate" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
  </resultMap>
 
  <sql id="Base_Column_List">
    SYSTEM_ID, BILLING_NO, VERSION, IS_LATEST_VERSION, IS_ADJUSTED, BILLING_CONFIG_NAME, 
    UNIFORM_PRICE, PE_BE, SUB_BE, CHARGE_WAY, CHARGE_MODE, MIN_PRICE_UNIT_DESC, MAX_PRICE_UNIT_DESC, 
    DATA_OPER_TIME, DATA_OPER_TYPE, ORG_AUTO_MODEL_KEY, OPER_ORG_CODE, RT_NO, AUTO_MODEL_NO, 
    AUTO_MODEL_NAME, APPLY_DATE, REMARK
  </sql>
  <select id="selectByOrderRela" parameterType="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo" resultMap="BaseResultMap">
	 <!-- select
    <include refid="Base_Column_List" />
    from ( -->
    select
    <include refid="Base_Column_List" />
    from e_billing_config a
   	where a.SUB_BE = #{subBe,jdbcType=VARCHAR}
		AND a.org_auto_model_key = #{orgAutoModelKey,jdbcType=VARCHAR}
		AND (
			(a.apply_date &gt;=left(#{versionLimit,jdbcType=VARCHAR},8)
			AND a.apply_date &lt;= date_format(now(),'%Y%m%d')
			) 
			OR a.APPLY_DATE = '99999999'
		)
		AND a.version &lt;= #{versionLimit,jdbcType=VARCHAR}
		and a.eft_date &lt;= sysdate()
      order by a.version desc limit 0,1
   <!-- ) m group by m.apply_date -->
  </select>
  
  <select id="countBillingConfig" resultType="int" parameterType="com.ls.ner.billing.config.bo.BillingConfigQueryCondition">
    select count(1) from e_billing_config a,e_tariff_plan b
    <where>
      a.plan_no=b.plan_no
      <if test="autoModelNo != null and autoModelNo != ''">
        AND a.AUTO_MODEL_NO = #{autoModelNo}
      </if>
      <if test="isAdjusted != null and isAdjusted != ''">
        AND a.IS_ADJUSTED = #{isAdjusted}
      </if>
      <if test="subBe != null and subBe != ''">
        AND a.SUB_BE = #{subBe}
      </if>
      <if test="orgAutoModelKey != null and orgAutoModelKey != ''">
        AND a.ORG_AUTO_MODEL_KEY = #{orgAutoModelKey}
      </if>
      <if test="orgCode != null and orgCode != ''">
        AND a.OPER_ORG_CODE =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.OPER_ORG_CODE in 
       <foreach item="item" index="index" collection="orgCodes"
         open="(" separator="," close=")">
           #{item}
       </foreach>
      </if>
    </where>
  </select>
  
  <select id="selectBillingConfig" resultMap="BaseResultMap" parameterType="com.ls.ner.billing.config.bo.BillingConfigQueryCondition">
    select 
    DATE_FORMAT(a.EFT_DATE,"%Y-%m-%d %H:%i") EFT_DATE,
    a.*,b.plan_name from e_billing_config a,e_tariff_plan b
    <where>
      a.plan_no=b.plan_no
      <if test="autoModelNo != null and autoModelNo != ''">
        AND a.AUTO_MODEL_NO = #{autoModelNo}
      </if>
      <if test="isAdjusted != null and isAdjusted != ''">
        AND a.IS_ADJUSTED = #{isAdjusted}
      </if>
      <if test="subBe != null and subBe != ''">
        AND a.SUB_BE = #{subBe}
      </if>
      <if test="orgAutoModelKey != null and orgAutoModelKey != ''">
        AND a.ORG_AUTO_MODEL_KEY = #{orgAutoModelKey}
      </if>
      <if test="orgCode != null and orgCode != ''">
        AND a.OPER_ORG_CODE =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.OPER_ORG_CODE in 
       <foreach item="item" index="index" collection="orgCodes"
         open="(" separator="," close=")">
           #{item}
       </foreach>
      </if>
    </where>
    order by a.EFT_DATE desc
    <if test="pageEnd!=null and pageEnd!=0">
      limit #{pageBegin} ,#{pageEnd}
    </if>
  </select>
  <!-- 暂无用 -->
  <select id="getBillingAdjustListCount" resultType="int" parameterType="com.ls.ner.billing.config.bo.BillingConfigQueryCondition">
    select count(1) 
    from e_billing_config a,e_billing_func b
    <where>
      a.BILLING_NO = b.BILLING_NO
      AND a.IS_LATEST_VERSION = '1'
      AND a.EFT_DATE &lt;= SYSDATE()
      <if test="autoModelNo != null and autoModelNo != ''">
        AND a.AUTO_MODEL_NO = #{autoModelNo}
      </if>
      <if test="subBe != null and subBe != ''">
        AND a.sub_be = #{subBe}
      </if>
      <if test="applyDate != null and applyDate != ''">
        AND (
          (
            a.APPLY_DATE &lt;= DATE_FORMAT(
              <!-- last_day(#{applyDate}), -->
              #{applyDate},
              "%Y%m%d"
            )
            AND a.IS_ADJUSTED = '1'
          )
          OR a.APPLY_DATE = '99999999'
        )
      </if>
      <if test="orgCode != null and orgCode != ''">
        AND a.OPER_ORG_CODE =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.OPER_ORG_CODE in 
       <foreach item="item" index="index" collection="orgCodes"
         open="(" separator="," close=")">
           #{item}
       </foreach>
      </if>
    </where>
  </select>
  
  <select id="getBillingAdjustList" resultMap="ExtendResultMap" parameterType="com.ls.ner.billing.config.bo.BillingConfigQueryCondition">
    select 
    DATE_FORMAT(a.VERSION,"%Y-%m-%d %H:%i:%s") CREATE_TIME,
    IFNULL(DATE_FORMAT(a.APPLY_DATE,"%Y-%m-%d"),"99999999") APPLY_DATE,
    a.*,b.CONTENT 
    from e_billing_config a,e_billing_func b
    <where>
      a.BILLING_NO = b.BILLING_NO
			AND a.IS_LATEST_VERSION = '1'
			AND a.EFT_DATE &lt;= SYSDATE()
      <if test="autoModelNo != null and autoModelNo != ''">
        AND a.AUTO_MODEL_NO = #{autoModelNo}
      </if>
      <if test="subBe != null and subBe != ''">
        AND a.sub_be = #{subBe}
      </if>
      <if test="applyDate != null and applyDate != ''">
        AND (
				  (
				    a.APPLY_DATE &lt;= DATE_FORMAT(
				      <!-- last_day(#{applyDate}), -->
				      #{applyDate},
				      "%Y%m%d"
				    )
				    AND a.IS_ADJUSTED = '1'
				  )
				  OR a.APPLY_DATE = '99999999'
				)
      </if>
      <if test="orgCode != null and orgCode != ''">
        AND a.OPER_ORG_CODE =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.OPER_ORG_CODE in 
       <foreach item="item" index="index" collection="orgCodes"
         open="(" separator="," close=")">
           #{item}
       </foreach>
      </if>
    </where>
  </select>
  
  <update id="updateAutoModelRental" parameterType="java.util.Map">
    update e_auto_model_rental a
    set a.BILLCONF_FLAG='1'
    where a.ORG_CODE=#{orgCode} and a.AUTO_MODEL_NO=#{autoModelNo} and a.SUB_BE=#{subBe}
  </update>
  
  <!-- 备份 <select id="selectPrepayByRela" parameterType="java.util.Map" resultMap="BaseResultMap">
   select
    <include refid="Base_Column_List" />
    from (
    select
    <include refid="Base_Column_List" />
    from e_billing_config a
    where a.SUB_BE = #{rentType,jdbcType=VARCHAR}
    AND a.org_auto_model_key = #{orgAutoModelKey,jdbcType=VARCHAR}
    AND (
      (a.apply_date &gt;=left(#{versionLimit,jdbcType=VARCHAR},8)
      AND a.apply_date &lt;= date_format(#{maxRentTime},'%Y%m%d')
      ) 
      OR a.APPLY_DATE = '99999999'
    )
    and a.IS_LATEST_VERSION = '1'
    AND a.version &lt;= #{versionLimit,jdbcType=VARCHAR}
    and a.eft_date &lt;= sysdate()
      order by a.version desc
   ) m group by m.apply_date
  </select> -->
  <select id="selectPrepayByRela" parameterType="java.util.Map" resultMap="BaseResultMap">
   select
    <include refid="Base_Column_List" />
    from  e_billing_config  where VERSION in (
    select
    max(a.VERSION)
    from e_billing_config a
    where a.SUB_BE = #{rentType,jdbcType=VARCHAR}
    AND a.org_auto_model_key = #{orgAutoModelKey,jdbcType=VARCHAR}
    AND (
      (a.apply_date &gt;=left(#{versionLimit,jdbcType=VARCHAR},8)
      AND a.apply_date &lt;= date_format(#{maxRentTime},'%Y%m%d')
      ) 
      OR a.APPLY_DATE = '99999999'
    )
    and a.IS_LATEST_VERSION = '1'
    AND a.version &lt;= #{versionLimit,jdbcType=VARCHAR}
    and a.eft_date &lt;= sysdate() GROUP BY
	a.apply_date order by a.version desc
   )
  </select>
  
  <select id="getAutoBillingList" resultMap="BaseResultMap" parameterType="com.ls.ner.billing.config.bo.BillingConfigQueryCondition">
    select a.AUTO_MODEL_NO,a.SUB_BE from e_auto_model_rental a
    <where>
    <if test="orgCode !=null and orgCode != ''">
    and a.ORG_CODE = #{orgCode}
    </if>
    <if test="subBe !=null and subBe != ''">
    and a.SUB_BE = #{subBe}
    </if>
    </where> 
  </select>
</mapper>