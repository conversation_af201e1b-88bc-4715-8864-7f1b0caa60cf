package com.ls.ner.billing.charge.service;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
/**
 * 充电资费管理---服务项目维护
 * <AUTHOR>
 */
public interface IChargeSerItemService {
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public List<ChargeSerItemBo> queryChargeSerItems(ChargeSerItemQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询刷卡费用条数C_CAR_PAY
	 */
	public int queryChargeSerItemsNum(ChargeSerItemQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目最大的sn值 E_APPEND_CHARGE_ITEM 
	 */
	public int queryChargeSerMaxSn(ChargeSerItemQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 根据itemName查询追加收费项目条数E_APPEND_CHARGE_ITEM
	 */
	public int queryChargeSerItemsNumByName(ChargeSerItemQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增追加收费项目E_APPEND_CHARGE_ITEM 
	 */
	public void insertChargeSerItem(ChargeSerItemBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public void updateChargeSerItem(ChargeSerItemBo bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目及费用
	 * @param itemNo 多个编号    如：1,2,3,4,5
	 * @param price  多个编号价格  如：1.21,2.11,3.11
	 */
	public List<ChargeSerItemBo> querySerItemsByNo(ChargeSerItemQueryCondition bo) throws Exception;
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目及费用
	 * @param itemNo 多个编号    如：1,2,3,4,5
	 */
	public String queryUnitsByNo(String itemNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目名称
	 * @param attachItemNos 多个编号    如：1,2,3,4,5
	 * @param attachItemPrices  服务项目单价  如：1.21,2.11,3.11
	 * @param attachItemUnits  服务项目单位  如：1.21,2.11,3.11
	 * @param attachItemNums  服务项目数量  如：1.21,2.11,3.11
	 * @param attachItemAmts  服务项目金额  如：1.21,2.11,3.11
	 */
	public List<Map<String,Object>> querySerItemsByNo(ChargeBillingRltBo cbrBo) throws Exception;
	
}
