<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IActCondDao" >

	<resultMap id="marketTypeBo" type="com.ls.ner.billing.mktact.bo.MarketTypeBo" >
		<result column="ACT_ID" property="actId" jdbcType="VARCHAR" />
		<result column="ACT_TYPE" property="actType" jdbcType="VARCHAR" />
		<result column="DCT_COND_FLAG" property="dctCondFlag" jdbcType="VARCHAR" />
		<result column="DCT_LVL" property="dctLvl" jdbcType="VARCHAR" />
		<result column="ACT_COND_ID" property="actCondId" jdbcType="VARCHAR" />
		<result column="DCT_COND_TYPE" property="dctCondType" jdbcType="VARCHAR" />
		<result column="PROD_ID" property="prodId" jdbcType="VARCHAR" />
		<result column="DCT_COND_UNIT" property="dctCondUnit" jdbcType="VARCHAR" />
		<result column="ACT_COND_DET_ID" property="actCondDetId" jdbcType="VARCHAR" />
		<result column="DCT_COND_VALUE" property="dctCondValue" jdbcType="VARCHAR" />
		<result column="DCT_TYPE" property="dctType" jdbcType="VARCHAR" />
		<result column="DCT_CALC_METHOD" property="dctCalcMethod" jdbcType="VARCHAR" />
		<result column="ACT_DCT_ID" property="actDctId" jdbcType="VARCHAR" />
		<result column="ACT_DCT_TYPE" property="actDctType" jdbcType="VARCHAR" />
		<result column="ACT_DCT_PROD_ID" property="actDctProdId" jdbcType="VARCHAR" />
		<result column="ALL_FREE" property="allFree" jdbcType="VARCHAR" />
		<result column="DCT_VALUE" property="dctValue" jdbcType="VARCHAR" />
		<result column="ACT_DCT_COND_UNIT" property="actDctCondUnit" jdbcType="VARCHAR" />
		<result column="ALL_STATION_FLAG" property="allStationFlag" jdbcType="VARCHAR" />
		<result column="P_BILL_FLAG" property="pBillFlag" jdbcType="VARCHAR" />
		<result column="APP_USER" property="appUser" jdbcType="VARCHAR" />
		<result column="STATION_ID" property="stationId" jdbcType="VARCHAR" />
		<result column="STATION_NAME" property="stationName" jdbcType="VARCHAR" />
		<result column="ACT_STATION_ID" property="actStationId" jdbcType="VARCHAR" />
	</resultMap>


<!-- 描述:保存优惠条件主表  创建人:biaoxiangd  创建时间: 2017/6/29 21:11 -->
	<insert id="saveActCond" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" keyProperty="actCondId" useGeneratedKeys="true">
		INSERT INTO e_mkt_act_cond(act_id,dct_cond_flag,dct_lvl,dct_cond_type,prod_id,dct_cond_unit,ALL_STATION_FLAG,P_BILL_FLAG,APP_USER)
		VALUES (#{actId},#{dctCondFlag},#{dctLvl},#{dctCondType},#{prodId},#{dctCondUnit},#{allStationFlag},#{pBillFlag},#{appUser})
	</insert>

<!-- 描述:更新优惠条件主表  创建人:biaoxiangd  创建时间: 2017/6/29 21:18 -->
	<update id="updateActCond" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo">
		UPDATE e_mkt_act_cond
		<set>
			<if test="dctLvl != null and dctLvl != ''">
				dct_lvl = #{dctLvl},
			</if>
			<if test="dctCondType != null and dctCondType != ''">
				dct_Cond_Type = #{dctCondType},
			</if>
			<if test="prodId != null and prodId != ''">
				prod_id = #{prodId},
			</if>
			<if test="dctCondUnit != null and dctCondUnit != ''">
				dct_cond_unit = #{dctCondUnit},
			</if>
			<if test="allStationFlag != null and allStationFlag != ''">
				ALL_STATION_FLAG = #{allStationFlag},
			</if>
			<if test="pBillFlag != null and pBillFlag != ''">
				P_BILL_FLAG = #{pBillFlag},
			</if>
			<if test="appUser != null and appUser != ''">
				APP_USER = #{appUser},
			</if>
		</set>
		WHERE act_cond_id = #{actCondId}
		and act_id = #{actId}
	</update>

<!-- 描述: 新增优惠条件明细 创建人:biaoxiangd  创建时间: 2017/6/29 21:20 -->
	<insert id="saveActCondDet" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" keyProperty="actCondDetId" useGeneratedKeys="true">
		INSERT INTO e_mkt_act_cond_det(act_cond_id,dct_cond_value,dct_type)
		VALUES (#{actCondId},#{dctCondValue},#{dctType})
	</insert>
<!-- 描述:更新优惠条件明细   创建人:biaoxiangd  创建时间: 2017/6/29 21:31 -->
	<update id="updateActCondDet" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo">
		UPDATE e_mkt_act_cond_det
		<set>
			<if test="actCondId != null and actCondId = ''">
				act_cond_id = #{actCondId},
			</if>
			<if test="dctCondValue != null and dctCondValue != ''">
				dct_cond_value = #{dctCondValue},
			</if>
			<if test="dctType != null and dctType != ''">
				dct_type = #{dctType},
			</if>
			<if test="dctCalcMethod != null and dctCalcMethod != ''">
				dct_calc_method = #{dctCalcMethod},
			</if>
		</set>
		WHERE act_cond_id = #{actCondId}
		and act_id = #{actId}
	</update>
<!-- 描述:删除优惠条件明细  创建人:biaoxiangd  创建时间: 2017/6/29 21:41 -->
	<delete id="delActCondDet" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo">
		DELETE FROM e_mkt_act_cond_det WHERE ACT_COND_DET_ID = #{actCondDetId}
	</delete>

<!-- 描述:新增优惠内容  创建人:biaoxiangd  创建时间: 2017/6/29 21:33 -->
	<insert id="saveMketActDct" parameterType="java.util.List">
		INSERT INTO e_mkt_act_dct(act_cond_det_id,dct_type,dct_calc_method,prod_id,all_free,dct_value,dct_cond_unit)
		VALUES
		<foreach collection="list" item="itm" index="index" separator=",">
			(#{itm.actCondDetId}, #{itm.dctType},#{itm.dctCalcMethod}, #{itm.prodId},#{itm.allFree}, #{itm.dctValue},#{itm.dctCondUnit} )
		</foreach>
	</insert>

<!-- 描述:删除优惠内容  创建人:biaoxiangd  创建时间: 2017/6/29 21:40 -->
	<delete id="delMketActDct" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo">
		DELETE FROM e_mkt_act_dct
		WHERE ACT_COND_DET_ID = #{actCondDetId}
		<if test="actDctId != null and actDctId != ''">
			AND ACT_DCT_ID = #{actDctId}
		</if>
	</delete>

<!-- 描述:优惠条件明细中是否还存在优惠内容  创建人:biaoxiangd  创建时间: 2017/7/6 10:26 -->
	<select id="queryMketActDctCount" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultType="int">
		SELECT COUNT(1) FROM e_mkt_act_dct WHERE ACT_COND_DET_ID = #{actCondDetId}
	</select>


	<!-- 描述:有条件查询  创建人:biaoxiangd  创建时间: 2017/6/30 10:22 -->
	<select id="queryActDctAll" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultMap="marketTypeBo">
		SELECT
			a.dct_cond_value,
			a.act_cond_id,
			a.act_cond_det_id,
			act_dct_id,
			b.act_cond_det_id,
			b.dct_type,
			b.dct_calc_method,
			IF (all_free = '1', '是', '否') AS all_free,
			IFNULL(dct_value,'0') AS dct_value,
			dct_cond_unit AS act_dct_cond_unit,
			prod_id AS act_dct_prod_id
		FROM
			e_mkt_act_cond_det a
		LEFT JOIN
			e_mkt_act_dct b
		ON
			a.act_cond_det_id  = b.act_cond_det_id
		WHERE act_cond_id = #{actCondId}
	</select>

	<!-- 描述:限时打折  有条件查询  创建人:biaoxiangd  创建时间: 2017/6/30 10:22 -->
	<select id="queryActAll" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultMap="marketTypeBo">
		SELECT
			a.dct_cond_value,
			a.act_cond_id,
			a.act_cond_det_id,
			b.act_dct_id,
			b.act_cond_det_id,
			b.dct_type,
			IF (b.all_free = '1', '是', '否') AS all_free,
			IFNULL(b.dct_value,'0') AS dct_value,
			b.dct_cond_unit AS act_dct_cond_unit,
			b.prod_id AS act_dct_prod_id
		FROM
			e_mkt_act_cond_det a,e_mkt_act_dct b,e_mkt_act_cond c
		WHERE
			a.act_cond_det_id  = b.act_cond_det_id
		AND c.ACT_COND_ID = a.ACT_COND_ID
		AND c.act_id = #{actId}
	</select>

<!-- 描述:查询活动优惠条件  创建人:biaoxiangd  创建时间: 2017/6/30 15:40 -->
	<select id="queryActCond" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultMap="marketTypeBo">
		SELECT
			a.act_cond_id,a.act_id,a.dct_cond_flag,a.dct_lvl,a.dct_cond_type,
			a.prod_id,a.dct_cond_unit,b.ACT_COND_DET_ID,a.ALL_STATION_FLAG,a.P_BILL_FLAG,a.APP_USER
		FROM e_mkt_act_cond a left join e_mkt_act_cond_det b on  a.ACT_COND_ID = b.ACT_COND_ID
		WHERE
			a.act_id = #{actId}
		ORDER BY  a.act_cond_id
	</select>

	<!-- 描述:查询活动优惠条件  创建人:biaoxiangd  创建时间: 2017/6/30 15:40 -->
	<select id="delActCond" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo">
		DELETE  FROM e_mkt_act_cond
		WHERE act_id = #{actId}
		<if test="actCondId != null and actCondId != ''">
			AND ACT_COND_ID = #{actCondId}
		</if>
	</select>

<!-- 描述:查询优惠条件明细  创建人:biaoxiangd  创建时间: 2017/7/5 15:23 -->
	<select id="queryMktActCondDet" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultMap="marketTypeBo">
		SELECT act_cond_det_id,act_cond_id,dct_cond_value,dct_type FROM e_mkt_act_cond_det
		WHERE act_cond_id = #{actCondId}
		<if test="dctCondValue != null and dctCondValue != ''">
			AND DCT_COND_VALUE &lt;= #{dctCondValue}
		</if>
		<if test="dctCondValue != null and dctCondValue != '' and dctCondValue =='null'">
			AND DCT_COND_VALUE is null
		</if>
		ORDER BY DCT_COND_VALUE desc
	</select>

<!-- 描述：查询优惠内容  创建人:biaoxiangd  创建时间: 2017/7/5 15:32 -->
	<select id="queryMktActDct" parameterType="com.ls.ner.billing.mktact.bo.MarketTypeBo" resultMap="marketTypeBo">
		SELECT ACT_DCT_ID,ACT_COND_DET_ID,DCT_TYPE,PROD_ID,ALL_FREE,DCT_VALUE,DCT_COND_UNIT FROM E_MKT_ACT_DCT FROM ACT_COND_DET_ID = #{actCondDetId}
	</select>

	<!-- 描述:新增赠送内容E_MKT_ACT_GIVE  创建人:biaoxiangd  创建时间: 2017/6/29 21:33 -->
	<insert id="saveMktActGive" parameterType="java.util.List">
		INSERT INTO e_mkt_act_give(ACT_ID,ACT_COND_DET_ID,DCT_GIVE_TYPE,CPN_ID)
		VALUES
		<foreach collection="list" item="itm" index="index" separator=",">
			(#{itm.actId}, #{itm.actCondDetId}, '04',#{itm.cpnId})
		</foreach>
	</insert>
	<!-- 描述:删除优惠条件明细  创建人:biaoxiangd  创建时间: 2017/6/29 21:41 -->
	<delete id="delMktActGive" parameterType="com.ls.ner.billing.mktact.bo.MarketActGiveBo">
		DELETE FROM
			e_mkt_act_give
		WHERE
			ACT_ID = #{actId}
			<if test="cpnId != null and cpnId != ''">
				AND CPN_ID = #{cpnId}
			</if>
	</delete>

	<select id="qryActStationList" parameterType="java.util.Map" resultMap="marketTypeBo">
			SELECT
				a.ACT_ID,
				a.STATION_ID,
				a.STATION_NAME,
				a.ACT_STATION_ID
			FROM
				e_act_station a
			WHERE
				a.ACT_ID = #{actId}
		<if test="stationIds != null and stationIds != ''">
			AND a.STATION_ID in
			<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<insert id="saveActStation" parameterType="java.util.List">
		INSERT INTO e_act_station(ACT_ID,STATION_ID,STATION_NAME)
		VALUES
		<foreach collection="list" item="itm" index="index" separator=",">
			(#{itm.actId}, #{itm.stationId},#{itm.stationName})
		</foreach>
	</insert>


	<select id="qryActStationCount" parameterType="java.util.Map" resultType="int">
		SELECT
		count(1)
		FROM
		e_act_station a
		WHERE
		a.ACT_ID = #{actId}
		<if test="stationIds != null and stationIds != ''">
			AND a.STATION_ID in
			<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<delete id="delActStation">
		DELETE from e_act_station WHERE ACT_STATION_ID = #{actStationId}
	</delete>

</mapper>