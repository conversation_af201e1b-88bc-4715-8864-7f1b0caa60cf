<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IPreSectionDao" >
  	<resultMap id="BaseResultMap" type="com.ls.ner.billing.mktact.bo.PreSectionBo" >
	    <id column="PRESENT_SECTION_ID" property="presentSectionId" jdbcType="BIGINT" />
	    <result column="ACT_ID" property="actId" jdbcType="BIGINT" />
	    <result column="SECTION_NAME" property="sectionName" jdbcType="VARCHAR" />
	    <result column="SECTION_RELA_TYPE" property="sectionRelaType" jdbcType="VARCHAR" />
	    <result column="CALC_PRIORITY" property="calcPriority" jdbcType="DECIMAL" />
	    <result column="SECTION_TYPE" property="sectionType" jdbcType="VARCHAR" />
	    <result column="PAY_BAL_TYPE" property="payBalType" jdbcType="VARCHAR" />
	    <result column="PRESENT_BAL_TYPE" property="presentBalType" jdbcType="VARCHAR" />
	    <result column="PRESENT_RULE_DESC" property="presentRuleDesc" jdbcType="VARCHAR" />
	    <result column="CALC_PRECISION" property="calcPrecision" jdbcType="VARCHAR" />
	    <result column="MAX_VALUE" property="maxValue" jdbcType="DECIMAL" />
	    <result column="PRESENT_CYCLE_TYPE" property="presentCycleType" jdbcType="VARCHAR" />
	    <result column="CYCLE_TYPE_ID" property="cycleTypeId" jdbcType="BIGINT" />
	    <result column="CYCLE_UPPER" property="cycleUpper" jdbcType="DECIMAL" />
	    <result column="CYCLE_LOWER" property="cycleLower" jdbcType="DECIMAL" />
	    <result column="TIME_LIMIT_ID" property="timeLimitId" jdbcType="BIGINT" />
  	</resultMap>

  	<sql id="qryPreSectionWhere">
		<if test="actId>=0">
		    AND act_id = #{actId}
		</if>
		<if test="presentSectionId !=null and presentSectionId !=''">
		    AND present_section_id = #{presentSectionId}
		</if>
		<if test="presentBalType !=null and presentBalType !=''">
			AND PRESENT_BAL_TYPE = #{presentBalType}
		</if>
	</sql>

	<!-- 1、获取段落列表 by condition -->
  	<select id="qryPreSections" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo" resultMap="BaseResultMap" >
  	SELECT
  		present_section_id, act_id, section_name, section_rela_type, calc_priority,
  		section_type, pay_bal_type, present_bal_type, present_rule_desc, calc_precision,
  		max_value,present_cycle_type, cycle_type_id, cycle_upper, cycle_lower, time_limit_id
    FROM
    	e_bal_present_section
    <where>
    	<include refid="qryPreSectionWhere"></include>
    </where>
  	</select>

  	<!-- 2、新增时间规则，一次性赠送时 -->
  	<insert id="insertTimeLimitType" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo">
  	INSERT INTO e_time_limit_type
  	<trim prefix="(" suffix=")">
  		time_limit_id, begin_time_type,
		<if test="beginTimeType==1">begin_calc_object,</if>
		<if test="beginTimeType==2">begin_time_unit,begin_time_duration,</if>
		<if test="endTimeType==1">end_calc_object,</if>
		<if test="endTimeType==2">end_time_unit,end_time_duration,</if>
		end_time_type
	</trim>
	VALUES
	<trim prefix="(" suffix=")">
  		#{timeLimitId}, #{beginTimeType},
		<if test="beginTimeType==1">#{beginCalcObject},</if>
		<if test="beginTimeType==2">#{beginTimeUnit},#{beginTimeDuration},</if>
		<if test="endTimeType==1">#{endCalcObject},</if>
		<if test="endTimeType==2">#{endTimeUnit},#{endTimeDuration},</if>
		#{endTimeType}
	</trim>
  	</insert>

	<update id="updateTimeLimitType" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo">
		UPDATE e_time_limit_type
		<set>
			begin_time_type = #{beginTimeType},
			<if test="beginTimeType==1">begin_calc_object = #{beginCalcObject},</if>
			<if test="beginTimeType==2">begin_time_unit=#{beginTimeUnit},begin_time_duration=#{beginTimeDuration},</if>
			end_time_type,
			<if test="endTimeType==1">end_calc_object = #{endCalcObject}</if>
			<if test="endTimeType==2">end_time_unit = #{endTimeUnit},end_time_duration = #{endTimeDuration}</if>
		</set>
		WHERE time_limit_id = #{timeLimitId}
	</update>

  	<!-- 3、新增周期赠送规则 -->
  	<insert id="insertCycleType" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo">
  	INSERT INTO e_cycle_type
  		(cycle_type_id, cycle_unit, cycle_unit_count, cycle_duration, cycle_duration_days)
	VALUES
		(#{cycleTypeId}, #{cycleUnit},#{cycleUnitCount}, #{cycleDuration},#{cycleDurationDays})
  	</insert>

  		<!-- 4、新增段落明细 -->
	<insert id="insertPreSectionDet" parameterType="java.util.List">
	INSERT INTO e_bal_present_section_det(
		section_det_id,
		present_section_id,
		ref_ceil,
		ref_floor,
		base_value,
		integral_num,
		section_det_sn,
		cpn_id,
		data_oper_time,
		data_oper_type
	  )VALUES
		<foreach collection="list" item="itm" index="index" separator=",">
		(
			#{itm.sectionDetId},
			#{itm.presentSectionId},
			#{itm.refCeil},
			#{itm.refFloor},
			#{itm.baseValue},
			#{itm.integralNum},
			#{itm.sectionDetSn},
			<choose><when test="itm.cpnId !=null and itm.cpnId !=''">#{itm.cpnId},</when><otherwise>null,</otherwise></choose>
			now(), 'I' )
		</foreach>
	</insert>

	<!-- 5、新增段落信息 -->
	<insert id="insertPreSection" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo" >
	INSERT INTO e_bal_present_section
  	<trim prefix="(" suffix=")">
  		present_section_id, act_id, section_name, section_rela_type, section_type,
  		pay_bal_type, present_bal_type, present_rule_desc, calc_precision, max_value,
  		present_cycle_type,
		<if test="presentCycleType ==1">time_limit_id,</if>
		<if test="presentCycleType ==2">cycle_type_id, cycle_upper, cycle_lower,</if>
		data_oper_time, data_oper_type
	</trim>
	VALUES
	<trim prefix="(" suffix=")">
  		#{presentSectionId}, #{actId}, #{sectionName}, #{sectionRelaType}, #{sectionType},
  		#{payBalType}, #{presentBalType}, #{presentRuleDesc}, #{calcPrecision}, #{maxValue},
  		#{presentCycleType},
		<if test="presentCycleType ==1">#{timeLimitId},</if>
		<if test="presentCycleType ==2">#{cycleTypeId}, #{cycleUpper}, #{cycleLower},</if>
		now(), 'I'
	</trim>
	</insert>

	<!-- 6、删除 周期规则 -->
	<delete id="deleteCycleType" parameterType="java.lang.Long" >
	DELETE FROM e_cycle_type WHERE cycle_type_id IN
		(SELECT cycle_type_id FROM e_bal_present_section
		  WHERE present_section_id = #{presentSectionId})
	</delete>

	<!-- 7、删除 时间限制规则 -->
	<delete id="deleteTimeLimitType" parameterType="java.lang.Long" >
	DELETE FROM e_time_limit_type WHERE time_limit_id IN
		(SELECT time_limit_id FROM e_bal_present_section
		  WHERE present_section_id = #{presentSectionId})
	</delete>

	<!-- 8、删除 段落明细 -->
	<delete id="deletePreSectionDet" parameterType="java.lang.Long" >
	DELETE FROM e_bal_present_section_det WHERE present_section_id = #{presentSectionId}
	</delete>

	<!-- 9、删除 段落规则 -->
	<delete id="deletePreSection" parameterType="java.lang.Long" >
	DELETE FROM e_bal_present_section WHERE present_section_id = #{presentSectionId}
	</delete>

	<!-- 10、查询段落规则，List<Map<String, Object>>合集 -->
  	<select id="qryPreSectionsMap" parameterType="java.util.Map" resultType="HashMap" >
  	SELECT
  		present_bal_type presentBalanceType, present_rule_desc presentRuleDesc
    FROM
    	e_bal_present_section
    WHERE
    	act_id = #{actId}
  	</select>

  	<select id="qryPreSectionValue" parameterType="java.util.Map" resultType="HashMap" >
  	SELECT
  		a.present_cycle_type presentCycleType,
  		a.max_value presentMaxValue,
  		b.base_value baseValue,
		b.cpn_id cpnId,
		b.integral_num integralNum,
  		a.cycle_type_id cycleTypeId,
  		a.time_limit_id timeLimitId,
  		a.cycle_upper cycleUpper,
  		a.cycle_lower cycleLower
  	FROM
  		e_bal_present_section a, e_bal_present_section_det b
  	WHERE
  		a.present_section_id = b.present_section_id
		AND a.act_id = #{actId}
		AND <![CDATA[b.ref_ceil <= #{payBalanceValue}]]>
		AND (b.ref_floor = -1 or b.ref_floor > #{payBalanceValue})
		<if test="presentBalType != null and presentBalType != ''">
			AND a.PRESENT_BAL_TYPE = #{presentBalType}
		</if>
  	</select>

  	<select id="qryTimeLimitById" parameterType="java.lang.Long" resultType="HashMap" >
  	SELECT
  		begin_time_type beginTimeType, begin_calc_object beginCalcObject,
  		begin_time_unit beginTimeUnit, begin_time_duration beginTimeDuration,
  		end_time_type endTimeType, end_calc_object endCalcObject,
  		end_time_unit endTimeUnit, end_time_duration endTimeDuration
  	FROM
  		e_time_limit_type
  	WHERE
  		time_limit_id = #{timeLimitId}
  	</select>

  	<select id="qryCycleById" parameterType="java.lang.Long" resultType="HashMap" >
  	SELECT
  		cycle_unit cycleUnit, cycle_unit_count cycleUnitCount,
  		cycle_duration cycleDuration, cycle_duration_days cycleDurationDays
  	FROM
  		e_cycle_type
  	WHERE
  		cycle_type_id = #{cycleTypeId}
  	</select>

	<select id="qryPreSectionDetInfo" parameterType="java.util.Map" resultType="HashMap" >
		SELECT
			b.SECTION_DET_ID sectionDetId,
			b.PRESENT_SECTION_ID presentSectionId,
			b.REF_CEIL	refCeil,
			b.REF_FLOOR	refFloor,
			b.BASE_VALUE baseValue,
			b.INTEGRAL_NUM integralNum,
			b.SECTION_DET_SN sectionDetSn,
			b.CPN_ID cpnId,
			(SELECT GROUP_CONCAT(c.cpn_name) from e_coupon c where FIND_IN_SET(c.cpn_id, b.CPN_ID)) cpnName
		FROM
			e_bal_present_section a, e_bal_present_section_det b
		WHERE
			a.present_section_id = b.present_section_id
		AND a.act_id = #{actId}
		AND a.PRESENT_SECTION_ID = #{presentSectionId}
		AND a.PRESENT_BAL_TYPE = #{presentBalType}
	</select>

	<update id="updatePreSection" parameterType="com.ls.ner.billing.mktact.bo.PreSectionBo">
		UPDATE e_bal_present_section
		<set>
			<if test="sectionName != null and sectionName != ''">SECTION_NAME = #{sectionName},</if>
			<if test="sectionRelaType != null and sectionRelaType != ''">SECTION_RELA_TYPE = #{sectionRelaType},</if>
			<if test="payBalType != null and payBalType != ''">PAY_BAL_TYPE = #{payBalType},</if>
			<if test="presentBalType != null and presentBalType != ''">PRESENT_BAL_TYPE = #{presentBalType},</if>
			<if test="maxValue != null and maxValue != ''">MAX_VALUE = #{maxValue},</if>
			<if test="presentCycleType != null and presentCycleType != ''">PRESENT_CYCLE_TYPE = #{presentCycleType},</if>
			<if test="presentRuleDesc != null and presentRuleDesc != ''">PRESENT_RULE_DESC = #{presentRuleDesc},</if>
			DATA_OPER_TIME = now(),
			DATA_OPER_TYPE = 'U'
		</set>
		WHERE PRESENT_SECTION_ID = #{presentSectionId}
	</update>

	<update id="updatePreSectionDet" parameterType="java.util.List">
		update e_bal_present_section_det
		<set>
			<if test="refCeil != null and refCeil != ''">REF_CEIL = #{refCeil},</if>
			<if test="refFloor != null and refFloor != ''">REF_FLOOR = #{refFloor},</if>
			<if test="baseValue != null and baseValue != ''">BASE_VALUE = #{baseValue},</if>
			<if test="integralNum != null and integralNum != ''">INTEGRAL_NUM = #{integralNum},</if>
			<if test="sectionDetSn != null and sectionDetSn != ''">SECTION_DET_SN = #{sectionDetSn},</if>
			<if test="cpnId != null and cpnId != ''">CPN_ID = #{cpnId},</if>
			DATA_OPER_TIME = now(),
			DATA_OPER_TYPE = 'U'
		</set>
		where
			SECTION_DET_ID = #{sectionDetId}
		AND PRESENT_SECTION_ID = #{presentSectionId}
	</update>

	<delete id="preSectionDetDel" parameterType="java.util.Map">
		DELETE FROM e_bal_present_section_det WHERE SECTION_DET_ID = #{sectionDetId}
	</delete>
</mapper>
