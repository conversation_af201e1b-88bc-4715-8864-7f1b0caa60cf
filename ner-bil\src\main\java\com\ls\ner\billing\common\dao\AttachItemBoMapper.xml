<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.AttachItemBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.AttachItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="ATTACH_NO" jdbcType="VARCHAR" property="attachNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="CHARGE_TYPE" jdbcType="VARCHAR" property="chargeType" />
    <result column="BUY_IDENTITY" jdbcType="VARCHAR" property="buyIdentity" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, PLAN_NO, ATTACH_NO, ITEM_NAME, ITEM_NO, CHARGE_TYPE, BUY_IDENTITY, REMARK
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.AttachItemBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_attach_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_attach_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_attach_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.AttachItemBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_attach_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.AttachItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_attach_item (SYSTEM_ID, PLAN_NO, ATTACH_NO, 
      ITEM_NAME, ITEM_NO, CHARGE_TYPE, 
      BUY_IDENTITY, REMARK)
    values (#{systemId,jdbcType=BIGINT}, #{planNo,jdbcType=VARCHAR}, #{attachNo,jdbcType=VARCHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{itemNo,jdbcType=VARCHAR}, #{chargeType,jdbcType=VARCHAR}, 
      #{buyIdentity,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.AttachItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_attach_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="planNo != null">
        PLAN_NO,
      </if>
      <if test="attachNo != null">
        ATTACH_NO,
      </if>
      <if test="itemName != null">
        ITEM_NAME,
      </if>
      <if test="itemNo != null">
        ITEM_NO,
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE,
      </if>
      <if test="buyIdentity != null">
        BUY_IDENTITY,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="planNo != null">
        #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="attachNo != null">
        #{attachNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="buyIdentity != null">
        #{buyIdentity,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.AttachItemBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_attach_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_attach_item
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.planNo != null">
        PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      </if>
      <if test="record.attachNo != null">
        ATTACH_NO = #{record.attachNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemNo != null">
        ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeType != null">
        CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.buyIdentity != null">
        BUY_IDENTITY = #{record.buyIdentity,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_attach_item
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      ATTACH_NO = #{record.attachNo,jdbcType=VARCHAR},
      ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      BUY_IDENTITY = #{record.buyIdentity,jdbcType=VARCHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.AttachItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_attach_item
    <set>
      <if test="planNo != null">
        PLAN_NO = #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="attachNo != null">
        ATTACH_NO = #{attachNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="buyIdentity != null">
        BUY_IDENTITY = #{buyIdentity,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.AttachItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_attach_item
    set PLAN_NO = #{planNo,jdbcType=VARCHAR},
      ATTACH_NO = #{attachNo,jdbcType=VARCHAR},
      ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      BUY_IDENTITY = #{buyIdentity,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>