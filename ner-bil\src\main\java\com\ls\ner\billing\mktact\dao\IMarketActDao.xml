<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IMarketActDao" >
  	<resultMap id="BaseResultMap" type="com.ls.ner.billing.mktact.bo.MarketActBo" >
	    <id column="ACT_ID" property="actId" jdbcType="BIGINT" />
	    <result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
	    <result column="ACT_NO" property="actNo" jdbcType="VARCHAR" />
	    <result column="ACT_TYPE" property="actType" jdbcType="VARCHAR" />
	    <result column="ACT_SUB_TYPE" property="actSubType" jdbcType="VARCHAR" />
	    <result column="ACT_NAME" property="actName" jdbcType="VARCHAR" />
	    <result column="ACT_CHANNEL" property="actChannel" jdbcType="VARCHAR" />
	    <result column="ACT_LBL" property="actLbl" jdbcType="VARCHAR" />
	    <result column="ACT_MARKS" property="actMarks" jdbcType="VARCHAR" />
		<result column="ACT_DETAIL_MARKS" property="actDetailMarks" jdbcType="VARCHAR" />
	    <result column="EFF_TIME" property="effTime" jdbcType="VARCHAR" />
	    <result column="EXP_TIME" property="expTime" jdbcType="VARCHAR" />
	    <result column="ACT_STATE" property="actState" jdbcType="VARCHAR" />
	    <result column="STATE_TIME" property="stateTime" jdbcType="VARCHAR" />
	    <result column="CRE_TIME" property="creTime" jdbcType="VARCHAR" />
	    <result column="CRE_EMP" property="creEmp" jdbcType="VARCHAR" />
		<result column="PROD_BUSI_TYPE" property="prodBusiType" jdbcType="VARCHAR" />
		<result column="NEED_POINT_NUM" property="needPointNum" />
		<result column="ACT_SUB_TYPE" property="actSubType" />
		<result column="IS_IMMEDIATE" property="isImmediate" jdbcType="VARCHAR" />
		<result column="CUST_TYPE" property="custType" jdbcType="VARCHAR" />
		<result column="CLOSE_TIME" property="closeTime" jdbcType="VARCHAR" />
		<result column="ALLOW_WITHDRAW" property="allowWithdraw" jdbcType="VARCHAR" />
		<result column="IS_LINK" property="isLink" jdbcType="VARCHAR" />
  	</resultMap>

  	<sql id="qryMarketActiveWhere">
		<if test="actId>0">
		    AND act_id =#{actId}
		</if>
		<if test="actNo !=null and actNo !=''">
			AND ACT_NO =#{actNo}
		</if>
		<if test="orgCode !=null and orgCode !=''">
		    AND org_code =#{orgCode}
		</if>
		<if test="orgCodes != null">
        	AND org_code in
       		<foreach item="item" index="index" collection="orgCodes" open="(" separator="," close=")">
           	#{item}
       		</foreach>
      	</if>
		<if test="actName !=null and actName !=''">
		    AND act_name like concat('%',#{actName},'%')
		</if>
		<if test="actType !=null and actType !=''">
		    AND act_type =#{actType}
		</if>
		<if test="actState !=null and actState !=''">
		    AND act_state =#{actState}
		</if>
		<if test="actQryFlag !=null and actQryFlag !=''">
		    AND act_state !=#{actQryFlag}
		</if>
		<if test="effTime !=null and expTime ==null">
		    AND <![CDATA[ eff_time <= #{effTime} ]]>
		    AND <![CDATA[ #{effTime} <= exp_time ]]>
		</if>
		<if test="effTime ==null and expTime !=null">
		    AND <![CDATA[ eff_time <= #{expTime} ]]>
		    AND <![CDATA[ #{expTime} <= exp_time ]]>
		</if>
		<if test="effTime !=null and expTime !=null">
		    AND <![CDATA[((#{effTime} <= exp_time and exp_time <= #{expTime})
		    		or (eff_time <= #{expTime} and #{expTime} <= exp_time))
		    	]]>
		</if>
		<if test="prodBusiType != null and prodBusiType != ''">
			AND prod_busi_type = #{prodBusiType}
		</if>
	</sql>

	<!-- 1、获取活动列表 by condition -->
  	<select id="qryMarketActives" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo" resultMap="BaseResultMap" >
  	SELECT
  		act_id, org_code, act_no, act_type,act_sub_type,act_sub_type, act_name, act_channel, act_lbl, act_marks,act_detail_marks,
  		date_format(eff_time,'%Y-%m-%d %H:%i') eff_time,
    	date_format(exp_time,'%Y-%m-%d %H:%i') exp_time,
    	act_state,
    	date_format(state_time,'%Y-%m-%d %H:%i') state_time,
    	date_format(cre_time,'%Y-%m-%d %H:%i:%s') cre_time,
    	cre_emp,
		prod_busi_type,NEED_POINT_NUM,
        IS_IMMEDIATE isImmediate,
        CUST_TYPE custType,
		ALLOW_WITHDRAW,
		IS_LINK
    FROM
    	e_mkt_act
    <where>
    	<include refid="qryMarketActiveWhere"></include>
    </where>
		order by CRE_TIME desc
    	<if test="end>0">
			limit #{begin},#{end}
		</if>
  	</select>

  	<!-- 2、获取活动列表数量 by condition -->
  	<select id="qryMarketActivesNums" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo" resultType="int" >
  	SELECT
  		count(1)
    FROM
    	e_mkt_act
    <where>
    	<include refid="qryMarketActiveWhere"></include>
    </where>
  	</select>

  	<!-- 3、新增/创建活动 -->
  	<insert id="insertMarketActive" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo">
		INSERT INTO e_mkt_act
		<trim prefix="(" suffix=")">
			<if test="actId !=null and actId !=''">act_id,</if>
			<if test="actNo !=null and actNo !=''">act_no,</if>
			<if test="orgCode !=null and orgCode !=''">org_code,</if>
			<if test="actType !=null and actType !=''">act_type,</if>
			<if test="actSubType !=null and actSubType !=''">act_sub_type,</if>
			<if test="actName !=null and actName !=''">act_name,</if>
			<if test="actChannel !=null and actChannel !=''">act_channel,</if>
			<if test="actLbl !=null and actLbl !=''">act_lbl,</if>
			<if test="actMarks !=null and actMarks !=''">act_marks,</if>
			<if test="actDetailMarks !=null and actDetailMarks !=''">act_detail_marks,</if>
			<if test="effTime !=null and effTime !=''">eff_time,</if>
			<if test="expTime !=null and expTime !=''">exp_time,</if>
			<if test="actState !=null and actState !=''">act_state,</if>
			<if test="creEmp !=null and creEmp !=''">cre_emp,</if>
			<if test="prodBusiType !=null and prodBusiType !=''">prod_busi_type,</if>
			<if test="needPointNum !=null and needPointNum !=''">NEED_POINT_NUM,</if>
			<if test="isImmediate !=null and isImmediate !=''">IS_IMMEDIATE,</if>
			<if test="custType !=null and custType !=''">CUST_TYPE,</if>
			<if test="allowWithdraw !=null and allowWithdraw !=''">ALLOW_WITHDRAW,</if>
			cre_time, data_oper_time, data_oper_type
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="actId !=null and actId !=''">#{actId},</if>
			<if test="actNo !=null and actNo !=''">#{actNo},</if>
			<if test="orgCode !=null and orgCode !=''">#{orgCode},</if>
			<if test="actType !=null and actType !=''">#{actType},</if>
			<if test="actSubType !=null and actSubType !=''">#{actSubType},</if>
			<if test="actName !=null and actName !=''">#{actName},</if>
			<if test="actChannel !=null and actChannel !=''">#{actChannel},</if>
			<if test="actLbl !=null and actLbl !=''">#{actLbl},</if>
			<if test="actMarks !=null and actMarks !=''">#{actMarks},</if>
			<if test="actDetailMarks !=null and actDetailMarks !=''">#{actDetailMarks},</if>
			<if test="effTime !=null and effTime !=''">str_to_date(#{effTime},'%Y-%m-%d %H:%i'),</if>
			<if test="expTime !=null and expTime !=''">str_to_date(#{expTime},'%Y-%m-%d %H:%i'),</if>
			<if test="actState !=null and actState !=''">#{actState},</if>
			<if test="creEmp !=null and creEmp !=''">#{creEmp},</if>
			<if test="prodBusiType !=null and prodBusiType !=''">#{prodBusiType},</if>
			<if test="needPointNum !=null and needPointNum !=''">#{needPointNum},</if>

			<if test="isImmediate !=null and isImmediate !=''">#{isImmediate},</if>
			<if test="custType !=null and custType !=''">#{custType},</if>
			<if test="allowWithdraw !=null and allowWithdraw !=''">#{allowWithdraw},</if>
	      	now(), now(), 'I'
	    </trim>
  	</insert>

  	<!-- 4、更新活动 -->
  	<update id="updateMarketActive" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo">
  		UPDATE e_mkt_act
		<set>
			<if test="actType !=null and actType !=''">act_type =#{actType},</if>
			<if test="actSubType !=null and actSubType !=''">act_sub_type =#{actSubType},</if>
			<if test="actName !=null and actName !=''">act_name =#{actName},</if>
			<if test="actChannel !=null and actChannel !=''">act_channel =#{actChannel},</if>
			<if test="actLbl !=null and actLbl !=''">act_lbl=#{actLbl},</if>
			<if test="actMarks !=null and actMarks !=''">act_marks =#{actMarks},</if>
			<if test="actDetailMarks !=null and actDetailMarks !=''">act_detail_marks =#{actDetailMarks},</if>
			<if test="effTime !=null and effTime !=''">eff_time = str_to_date(#{effTime},'%Y-%m-%d %H:%i'),</if>
			<if test="expTime !=null and expTime !=''">exp_time = str_to_date(#{expTime},'%Y-%m-%d %H:%i'),</if>
			<if test="actState !=null and actState !=''">act_state =#{actState},</if>
			<if test="prodBusiType !=null and prodBusiType !=''">prod_busi_type =#{prodBusiType},</if>
			<if test="needPointNum !=null and needPointNum !=''">NEED_POINT_NUM =#{needPointNum},</if>
			<if test="closeTime !=null and closeTime !=''">CLOSE_TIME =#{closeTime},</if>
			<if test="isImmediate !=null and isImmediate !=''">IS_IMMEDIATE =#{isImmediate},</if>
			<if test="custType !=null and custType !=''">CUST_TYPE =#{custType},</if>
			<if test="allowWithdraw !=null and allowWithdraw !=''">ALLOW_WITHDRAW =#{allowWithdraw},</if>
			<if test="isLink !=null and isLink !=''">IS_LINK =#{isLink},</if>
			data_oper_time = now(),
			data_oper_type = 'U'
		</set>
		<where>
			act_id = #{actId}
		</where>
  	</update>

  	<!-- 5、删除活动 -->
  	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
  	DELETE FROM e_mkt_act WHERE act_id = #{actId}
  	</delete>

  	<!-- 6、查询营销活动记录，List<Map<String, Object>>合集 -->
  	<select id="qryMarketActivesMap" parameterType="java.util.Map" resultType="HashMap" >
  	SELECT
  		act_id actId, act_name actName, act_lbl actLbl, act_marks actMarks, act_sub_type actSubType,
		date_format(IFNULL(cre_time,now()),'%Y-%m-%d %H:%i:%s') putTime,
  		date_format(eff_time,'%Y-%m-%d %H:%i') effTime,
    	date_format(exp_time,'%Y-%m-%d %H:%i') expTime
    FROM
    	e_mkt_act
    WHERE
    	act_type = '07' AND (ACT_STATE='1' OR ACT_STATE='2')
    	<choose>
			<when test="payBalanceTime !=null and payBalanceTime !=''">
			<!--AND <![CDATA[eff_time <= #{payBalanceTime} <= exp_time]]>-->
				AND <![CDATA[eff_time <= #{payBalanceTime}]]>
				AND <![CDATA[#{payBalanceTime} <= exp_time]]>
			</when>
			<otherwise>
			<!--AND <![CDATA[eff_time <= sysdate() <= exp_time]]>-->
				AND <![CDATA[eff_time <= sysdate()]]>
				AND <![CDATA[sysdate()<= exp_time]]>
			</otherwise>
		</choose>
		<if test="channel !=null and channel !=''">
		AND <![CDATA[ position(#{channel} in act_channel) >= 1 ]]>
		</if>
		<if test="actId !=null and actId !=''">
		AND act_id = #{actId}
		</if>
        <if test="custType !=null and custType !=''">
			AND (<![CDATA[ position(#{custType} in CUST_TYPE) >= 1 ]]> OR CUST_TYPE IS NULL)
        </if>
		ORDER BY cre_time DESC
  	</select>


	<update id="updateMarketActiveState">
		UPDATE e_mkt_act
		<set>
			act_state = #{actState},
			data_oper_time = now(),
			data_oper_type = 'U'
		</set>
		<where>
			<if test="actState !=null and actState ==3 ">
		    	<![CDATA[ exp_time < now() and act_state = '2' ]]>
			</if>
			<if test="actState !=null and actState ==2 ">
			 	<![CDATA[ eff_time <= now() and exp_time >= now() and act_state = '1']]>
			</if>
		</where>
	</update>
	<select id="queryActCoupon" parameterType="java.util.Map" resultType="HashMap" >
		SELECT
			a.NEED_POINT_NUM needPointNum,
			b.CPN_ID cpnId
		FROM
			e_mkt_act a,
			e_coupon_push b
		WHERE
			a.ACT_ID = b.ACT_ID
			AND a.ACT_ID = #{actId}
	</select>

	<select id="queryActScope" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.ACT_ID actId,
			a.ACT_NAME actName,
            DATE_FORMAT(a.EFF_TIME,'%Y-%m-%d %H:%i:%S') effTime,
            DATE_FORMAT(a.EXP_TIME,'%Y-%m-%d %H:%i:%S') expTime
		FROM
			e_mkt_act a
		LEFT JOIN e_mkt_act_scope s ON a.ACT_ID = s.ACT_ID
		 where a.ACT_STATE != '0' AND a.ACT_STATE !='4'
			<if test="custType1 !=null and custType2 ==null">
				AND a.CUST_TYPE like concat('%', #{custType1},'%')
			</if>
			<if test="custType1 !=null and custType2 !=null">
				AND (a.CUST_TYPE like concat('%', #{custType1},'%') or a.CUST_TYPE like concat('%', #{custType2},'%'))
			</if>
          <if test="actType!=null and actType!=''">
              AND a.ACT_TYPE = #{actType}
          </if>
			<if test="scopeType!=null and scopeType!=''">
				AND s.SCOPE_TYPE = #{scopeType}
			</if>
			<if test="buildId !=null and buildId !=''">
				AND s.BUILD_ID =#{buildId}
			</if>
			<if test="cityList!=null">
				AND s.CITY in
				<foreach collection="cityList" open="(" close=")" item="item" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="stationList!=null">
				AND s.STATION_ID in
				<foreach collection="stationList" open="(" close=")" item="item" separator=",">
					#{item}
				</foreach>
			</if>
	</select>

	<select id="qryMarketActivesNew" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo" resultType="java.util.Map">
		SELECT
			a.ACT_ID actId,
			a.ACT_NAME actName,
			a.ACT_TYPE actType,
			b.SCOPE_TYPE scopeType,
			b.BUILD_ID buildId,
			GROUP_CONCAT(b.CITY) cityCodes,
			GROUP_CONCAT(b.STATION_ID) stationIds,
			DATE_FORMAT(a.EFF_TIME,'%Y-%m-%d %H:%i:%S') effTime,
			DATE_FORMAT(a.EXP_TIME,'%Y-%m-%d %H:%i:%S') expTime,
			DATE_FORMAT(a.DATA_OPER_TIME,'%Y-%m-%d %H:%i:%S') dataOperTime,
			a.ACT_STATE actState
		FROM
			e_mkt_act a
		LEFT JOIN e_mkt_act_scope b ON a.ACT_ID = b.ACT_ID
		<where>
			<if test="actName !=null and actName !=''">
				AND a.ACT_NAME like concat('%',#{actName},'%')
			</if>
			<if test="orgCode !=null and orgCode !=''">
				AND a.org_code =#{orgCode}
			</if>
			<if test="orgCodes != null">
				AND a.org_code in
				<foreach item="item" index="index" collection="orgCodes" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="buildId !=null and buildId !=''">
				AND b.build_id  = #{buildId}
			</if>
			<if test="actType !=null and actType !=''">
				AND a.ACT_TYPE =#{actType}
			</if>
			<if test="actState !=null and actState !='' and actState=='0'.toString()">
				AND a.ACT_STATE = '0'
			</if>
			<if test="actState !=null and actState !='' and actState=='1'.toString()">
				AND a.ACT_STATE = '1' and  <![CDATA[ a.EFF_TIME > now() ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='2'.toString()">
				AND (a.ACT_STATE = '1' or a.ACT_STATE = '2')
				AND <![CDATA[ a.EFF_TIME <= now() ]]>
				AND <![CDATA[ now() <= a.EXP_TIME ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='3'.toString()">
				AND (a.ACT_STATE = '1' or a.ACT_STATE = '2' or a.ACT_STATE = '3')
				AND <![CDATA[ now() > a.EXP_TIME ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='4'.toString()">
				AND a.ACT_STATE = '4'
			</if>

			<if test="effTime !=null and expTime ==null">
				AND <![CDATA[ a.EFF_TIME <= #{effTime} ]]>
				AND <![CDATA[ #{effTime} <= a.EXP_TIME ]]>
			</if>
			<if test="effTime ==null and expTime !=null">
				AND <![CDATA[ a.EFF_TIME <= #{expTime} ]]>
				AND <![CDATA[ #{expTime} <= a.EXP_TIME ]]>
			</if>
			<if test="effTime !=null and expTime !=null">
				AND <![CDATA[((#{effTime} <= a.EXP_TIME and a.EXP_TIME <= #{expTime})
		    		or (a.EFF_TIME <= #{expTime} and #{expTime} <= a.EXP_TIME))
		    	]]>
			</if>
		</where>
		GROUP BY
			a.ACT_ID
		ORDER BY
			a.EFF_TIME IS NULL DESC,
			a.ACT_STATE,
			a.EFF_TIME
		<if test="end>0">
			limit #{begin},#{end}
		</if>
	</select>

	<select id="qryMarketActivesNumNew" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo"  resultType="int">
		select count(1) from (
        SELECT
			a.ACT_ID
		FROM
		e_mkt_act a
		LEFT JOIN e_mkt_act_scope b ON a.ACT_ID = b.ACT_ID
		<where>
			<if test="actName !=null and actName !=''">
				AND a.ACT_NAME like concat('%',#{actName},'%')
			</if>
			<if test="orgCode !=null and orgCode !=''">
				AND a.org_code =#{orgCode}
			</if>
			<if test="orgCodes != null">
				AND a.org_code in
				<foreach item="item" index="index" collection="orgCodes" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="buildId !=null and buildId !=''">
				AND b.build_id  = #{buildId}
			</if>
			<if test="actType !=null and actType !=''">
				AND a.ACT_TYPE =#{actType}
			</if>
			<if test="actState !=null and actState !='' and actState=='0'.toString()">
				AND a.ACT_STATE = '0'
			</if>
			<if test="actState !=null and actState !='' and actState=='1'.toString()">
				AND a.ACT_STATE = '1' and  <![CDATA[ a.EFF_TIME > now() ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='2'.toString()">
				AND (a.ACT_STATE = '1' or a.ACT_STATE = '2')
				AND <![CDATA[ a.EFF_TIME <= now() ]]>
				AND <![CDATA[ now() <= a.EXP_TIME ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='3'.toString()">
				AND (a.ACT_STATE = '1' or a.ACT_STATE = '2' or a.ACT_STATE = '3')
				AND <![CDATA[ now() > a.EXP_TIME ]]>
			</if>
			<if test="actState !=null and actState !='' and actState=='4'.toString()">
				AND a.ACT_STATE = '4'
			</if>

			<if test="effTime !=null and expTime ==null">
				AND <![CDATA[ a.EFF_TIME <= #{effTime} ]]>
				AND <![CDATA[ #{effTime} <= a.EXP_TIME ]]>
			</if>
			<if test="effTime ==null and expTime !=null">
				AND <![CDATA[ a.EFF_TIME <= #{expTime} ]]>
				AND <![CDATA[ #{expTime} <= a.EXP_TIME ]]>
			</if>
			<if test="effTime !=null and expTime !=null">
				AND <![CDATA[((#{effTime} <= a.EXP_TIME and a.EXP_TIME <= #{expTime})
		    		or (a.EFF_TIME <= #{expTime} and #{expTime} <= a.EXP_TIME))
		    	]]>
			</if>
		</where>
		GROUP BY
			a.ACT_ID) a
	</select>

	<select id="qryJoinActNumAndOrder" resultType="java.util.Map">
			SELECT
				t.ACT_ID actId,
				GROUP_CONCAT(j.ORDER_NO) orderNoStr,
				COUNT(1) joinNum
			FROM
				e_mkt_act t
			LEFT JOIN e_act_cust_join j ON t.ACT_ID = j.ACT_ID
             where 1=1
				<if test="actIdList!=null">
					AND j.ACT_ID in
					<foreach collection="actIdList" open="(" close=")" item="item" separator=",">
						#{item}
					</foreach>
				</if>
			GROUP BY
				t.ACT_ID
	</select>
	<select id="qryGiftBalanceValue" resultType="java.util.Map" parameterType="java.util.Map">
	SELECT
		sum( a.BASE_VALUE) baseValue,
  		MAX_VALUE	presentMaxValue
	FROM
		e_bal_present_section_det a,
		e_bal_present_section b
	WHERE
		a.PRESENT_SECTION_ID = b.PRESENT_SECTION_ID
		AND b.ACT_ID = #{actId}</select>

	<select id="queryActById" resultType="java.util.Map">
		select act_name actName,
			   ACT_LBL actLbl,
			   EFF_TIME effTime,
			   EXP_TIME expTime,
			   ACT_STATE actState,
			   ACT_MARKS actMark
		from e_mkt_act
		where act_id = #{actId}
	</select>

	<select id="queryActInProgress" resultType="java.util.Map" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo">
		select act_name actName,
			   ACT_LBL actLbl,
			   ACT_ID actId,
			   EFF_TIME effTime,
			   EXP_TIME expTime,
			   ACT_STATE actState,
			   ACT_MARKS actMark,
			   act_detail_marks actDetailMarks
		from e_mkt_act
		where ACT_STATE = #{actState} and ACT_TYPE = #{actType}
		ORDER BY DATA_OPER_TIME DESC LIMIT 1
	</select>

</mapper>
