<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="桩电价下发明细" />
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="充电计费配置信息"></ls:title>
		<table class="tab_search">
			<tr>
				<td><ls:label text="充电计费编号" ref="chcNo" /></td>
				<td colspan="2"><ls:text name="chcNo" property="chcNo" readOnly="true"/></td>
				<td><ls:label text="充电计费名称" ref="chcName" /></td>
				<td colspan="2"><ls:text name="chcName" property="chcName" readOnly="true"/></td>
			</tr>
			<tr>
				<td><ls:label text="计费配置状态" ref="chcStatus" /></td>
				<td colspan="2"><ls:select name="chcStatus" property="chcStatus" readOnly="true">
					<ls:options property="validFlagList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
				<td><ls:label text="下发状态" ref="pileSendStatus"/></td>
				<td colspan="2"><ls:select name="pileSendStatus" onchanged="changePileSendStatus">
					<ls:options property="issuedPileStatusList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
			</tr>
		</table>
	</ls:form>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="detailGrid" height="200px;" width="100%" showCheckBox="true" singleSelect="true" caption="桩电价下发明细">
					<ls:gridToolbar name="detailGridBar">
						<ls:gridToolbarItem imageKey="refresh" onclick="doIssuredPile" text="重发"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doLog" text="下发历史"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="back" onclick="doReturn" text="返回"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="充电桩编号" name="pileNo" />
					<ls:column caption="充电桩名称" name="pileName" />
					<ls:column caption="最后一次下发时间" name="sendTime" format="{sendTime,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="下发失败原因" name="failReason" />
					<ls:column caption="下发状态" name="pileSendStatusName" formatFunc="caseSendStatus"/>
					<ls:column caption="内网IP" name="intranetIp" />
					<ls:column caption="外网IP" name="networkIp" />
					<ls:column caption="账户" name="operator" />
					<ls:pager pageSize="15,20,25" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	<ls:script>
	window.detailGrid = detailGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		detailGrid.query('~/billing/chcbillSend/queryChcbillSendDetail',params,function(){});
	}
	
 	function doIssuredPile(){
 		var item = detailGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要重新下发的充电桩");
    		return;
	   }
	   if(item.pileSendStatusName == '001'){//已下发成功，无需下发
	   		LS.message("error","已成功下发，无需重发下发");
    		return;
	   }
	   LS.confirm('确定要重新下发?',function(result){
			if(result){
				LS.ajax("~/billing/chcbillSend/issuredPile?chcNo="+item.chcNo+"&pileNo="+item.pileNo,null,function(data){
					if(data){
						LS.message("info","下发成功");
					}else{
						LS.message("info","下发失败");
					}
						doSearch();
				});
			}
		});
 	}
 	
 	function doLog(){
 		var item = detailGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要查看明细的记录");
    		return;
	   }
		LS.dialog("~/billing/chcbillSend/log?chcNo="+item.chcNo+"&pileNo="+item.pileNo,"桩电价下发历史",1000, 450,true, null);
 	}
 	
 	function doReturn(){
 		LS.window.close();
 	}
 	
 	function changePileSendStatus(){
 		doSearch();
 	}

	var sendTimeOut = ${chargingPriingSendTimeOut};
	function caseSendStatus(item){
		var date1 = item.sendTime;  //下发时间
		var date2 = new Date();    //当前时间
		var date3 = date2.getTime() - new Date(date1).getTime();   //时间差的毫秒数
		var days=Math.floor(date3/(24*3600*1000))
		var leave1=date3%(24*3600*1000)    //计算天数后剩余的毫秒数
		var hours=Math.floor(leave1/(3600*1000))

		//计算相差分钟数
		var leave2=leave1%(3600*1000)        //计算小时数后剩余的毫秒数
		var minutes=Math.floor(leave2/(60*1000))+Math.floor(days*24*60)+Math.floor(hours*60)
		if(minutes > sendTimeOut && item.pileSendStatusName == '下发中'){
			return '下发超时'
		}else
			return item.pileSendStatusName;
		}
    </ls:script>
</ls:body>
</html>