package com.ls.ner.billing.xpcharge.service;

import java.util.List;
import java.util.Map;

/**
 * 充电计费下发
 * <AUTHOR>
 */
public interface IXpStationSendService {

    /**
     * @description 查询最新计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheNewChc(Map m);

    /**
     * @description 查询当前计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheCurrentChc(Map m);

    /**
     * @description 充电计费配置下发到充电站
     * @return String 下发完成状态  02下发失败   03部分成功  04下发成功
     * */
    public void issuredStation(String chcNo, String stationId, Map<String, Object> map) throws Exception;

    /**
     * @Description 下发计费给单桩
     * <AUTHOR>
     * @Date 2022/7/16 15:32
     * @param chcNo
     * @param stationId
     * @param pileNo
     */
    void issuredStationBillToPile(String chcNo, String stationId, String pileNo);

    /**
     * 查询桩是否有单独计费
     * @param sercher
     * @return
     */
    int queryPileBillCount(Map sercher);

    /**
     * 桩计费下发
     * @param chcNo
     * @param pileNo
     * @param stationId
     */
    void issuredPile(String chcNo, String pileNo, String stationId, Map<String, Object> map)throws Exception;
}
