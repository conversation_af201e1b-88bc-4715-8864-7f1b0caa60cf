package com.ls.ner.billing.common.bo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BillingConfigBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public BillingConfigBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNull() {
            addCriterion("BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNotNull() {
            addCriterion("BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBillingNoEqualTo(String value) {
            addCriterion("BILLING_NO =", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotEqualTo(String value) {
            addCriterion("BILLING_NO <>", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThan(String value) {
            addCriterion("BILLING_NO >", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThanOrEqualTo(String value) {
            addCriterion("BILLING_NO >=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThan(String value) {
            addCriterion("BILLING_NO <", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThanOrEqualTo(String value) {
            addCriterion("BILLING_NO <=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLike(String value) {
            addCriterion("BILLING_NO like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotLike(String value) {
            addCriterion("BILLING_NO not like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIn(List<String> values) {
            addCriterion("BILLING_NO in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotIn(List<String> values) {
            addCriterion("BILLING_NO not in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoBetween(String value1, String value2) {
            addCriterion("BILLING_NO between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotBetween(String value1, String value2) {
            addCriterion("BILLING_NO not between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("VERSION is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("VERSION is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("VERSION =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("VERSION <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("VERSION >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("VERSION >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("VERSION <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("VERSION <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("VERSION like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("VERSION not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("VERSION in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("VERSION not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("VERSION between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("VERSION not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionIsNull() {
            addCriterion("IS_LATEST_VERSION is null");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionIsNotNull() {
            addCriterion("IS_LATEST_VERSION is not null");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionEqualTo(String value) {
            addCriterion("IS_LATEST_VERSION =", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionNotEqualTo(String value) {
            addCriterion("IS_LATEST_VERSION <>", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionGreaterThan(String value) {
            addCriterion("IS_LATEST_VERSION >", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionGreaterThanOrEqualTo(String value) {
            addCriterion("IS_LATEST_VERSION >=", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionLessThan(String value) {
            addCriterion("IS_LATEST_VERSION <", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionLessThanOrEqualTo(String value) {
            addCriterion("IS_LATEST_VERSION <=", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionLike(String value) {
            addCriterion("IS_LATEST_VERSION like", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionNotLike(String value) {
            addCriterion("IS_LATEST_VERSION not like", value, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionIn(List<String> values) {
            addCriterion("IS_LATEST_VERSION in", values, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionNotIn(List<String> values) {
            addCriterion("IS_LATEST_VERSION not in", values, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionBetween(String value1, String value2) {
            addCriterion("IS_LATEST_VERSION between", value1, value2, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsLatestVersionNotBetween(String value1, String value2) {
            addCriterion("IS_LATEST_VERSION not between", value1, value2, "isLatestVersion");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedIsNull() {
            addCriterion("IS_ADJUSTED is null");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedIsNotNull() {
            addCriterion("IS_ADJUSTED is not null");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedEqualTo(String value) {
            addCriterion("IS_ADJUSTED =", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedNotEqualTo(String value) {
            addCriterion("IS_ADJUSTED <>", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedGreaterThan(String value) {
            addCriterion("IS_ADJUSTED >", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedGreaterThanOrEqualTo(String value) {
            addCriterion("IS_ADJUSTED >=", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedLessThan(String value) {
            addCriterion("IS_ADJUSTED <", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedLessThanOrEqualTo(String value) {
            addCriterion("IS_ADJUSTED <=", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedLike(String value) {
            addCriterion("IS_ADJUSTED like", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedNotLike(String value) {
            addCriterion("IS_ADJUSTED not like", value, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedIn(List<String> values) {
            addCriterion("IS_ADJUSTED in", values, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedNotIn(List<String> values) {
            addCriterion("IS_ADJUSTED not in", values, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedBetween(String value1, String value2) {
            addCriterion("IS_ADJUSTED between", value1, value2, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andIsAdjustedNotBetween(String value1, String value2) {
            addCriterion("IS_ADJUSTED not between", value1, value2, "isAdjusted");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameIsNull() {
            addCriterion("BILLING_CONFIG_NAME is null");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameIsNotNull() {
            addCriterion("BILLING_CONFIG_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameEqualTo(String value) {
            addCriterion("BILLING_CONFIG_NAME =", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameNotEqualTo(String value) {
            addCriterion("BILLING_CONFIG_NAME <>", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameGreaterThan(String value) {
            addCriterion("BILLING_CONFIG_NAME >", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameGreaterThanOrEqualTo(String value) {
            addCriterion("BILLING_CONFIG_NAME >=", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameLessThan(String value) {
            addCriterion("BILLING_CONFIG_NAME <", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameLessThanOrEqualTo(String value) {
            addCriterion("BILLING_CONFIG_NAME <=", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameLike(String value) {
            addCriterion("BILLING_CONFIG_NAME like", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameNotLike(String value) {
            addCriterion("BILLING_CONFIG_NAME not like", value, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameIn(List<String> values) {
            addCriterion("BILLING_CONFIG_NAME in", values, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameNotIn(List<String> values) {
            addCriterion("BILLING_CONFIG_NAME not in", values, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameBetween(String value1, String value2) {
            addCriterion("BILLING_CONFIG_NAME between", value1, value2, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andBillingConfigNameNotBetween(String value1, String value2) {
            addCriterion("BILLING_CONFIG_NAME not between", value1, value2, "billingConfigName");
            return (Criteria) this;
        }

        public Criteria andUniformPriceIsNull() {
            addCriterion("UNIFORM_PRICE is null");
            return (Criteria) this;
        }

        public Criteria andUniformPriceIsNotNull() {
            addCriterion("UNIFORM_PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andUniformPriceEqualTo(String value) {
            addCriterion("UNIFORM_PRICE =", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceNotEqualTo(String value) {
            addCriterion("UNIFORM_PRICE <>", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceGreaterThan(String value) {
            addCriterion("UNIFORM_PRICE >", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceGreaterThanOrEqualTo(String value) {
            addCriterion("UNIFORM_PRICE >=", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceLessThan(String value) {
            addCriterion("UNIFORM_PRICE <", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceLessThanOrEqualTo(String value) {
            addCriterion("UNIFORM_PRICE <=", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceLike(String value) {
            addCriterion("UNIFORM_PRICE like", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceNotLike(String value) {
            addCriterion("UNIFORM_PRICE not like", value, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceIn(List<String> values) {
            addCriterion("UNIFORM_PRICE in", values, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceNotIn(List<String> values) {
            addCriterion("UNIFORM_PRICE not in", values, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceBetween(String value1, String value2) {
            addCriterion("UNIFORM_PRICE between", value1, value2, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andUniformPriceNotBetween(String value1, String value2) {
            addCriterion("UNIFORM_PRICE not between", value1, value2, "uniformPrice");
            return (Criteria) this;
        }

        public Criteria andPeBeIsNull() {
            addCriterion("PE_BE is null");
            return (Criteria) this;
        }

        public Criteria andPeBeIsNotNull() {
            addCriterion("PE_BE is not null");
            return (Criteria) this;
        }

        public Criteria andPeBeEqualTo(String value) {
            addCriterion("PE_BE =", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotEqualTo(String value) {
            addCriterion("PE_BE <>", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeGreaterThan(String value) {
            addCriterion("PE_BE >", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeGreaterThanOrEqualTo(String value) {
            addCriterion("PE_BE >=", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLessThan(String value) {
            addCriterion("PE_BE <", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLessThanOrEqualTo(String value) {
            addCriterion("PE_BE <=", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLike(String value) {
            addCriterion("PE_BE like", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotLike(String value) {
            addCriterion("PE_BE not like", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeIn(List<String> values) {
            addCriterion("PE_BE in", values, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotIn(List<String> values) {
            addCriterion("PE_BE not in", values, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeBetween(String value1, String value2) {
            addCriterion("PE_BE between", value1, value2, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotBetween(String value1, String value2) {
            addCriterion("PE_BE not between", value1, value2, "peBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNull() {
            addCriterion("SUB_BE is null");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNotNull() {
            addCriterion("SUB_BE is not null");
            return (Criteria) this;
        }

        public Criteria andSubBeEqualTo(String value) {
            addCriterion("SUB_BE =", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotEqualTo(String value) {
            addCriterion("SUB_BE <>", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThan(String value) {
            addCriterion("SUB_BE >", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThanOrEqualTo(String value) {
            addCriterion("SUB_BE >=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThan(String value) {
            addCriterion("SUB_BE <", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThanOrEqualTo(String value) {
            addCriterion("SUB_BE <=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLike(String value) {
            addCriterion("SUB_BE like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotLike(String value) {
            addCriterion("SUB_BE not like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIn(List<String> values) {
            addCriterion("SUB_BE in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotIn(List<String> values) {
            addCriterion("SUB_BE not in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeBetween(String value1, String value2) {
            addCriterion("SUB_BE between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotBetween(String value1, String value2) {
            addCriterion("SUB_BE not between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andChargeWayIsNull() {
            addCriterion("CHARGE_WAY is null");
            return (Criteria) this;
        }

        public Criteria andChargeWayIsNotNull() {
            addCriterion("CHARGE_WAY is not null");
            return (Criteria) this;
        }

        public Criteria andChargeWayEqualTo(String value) {
            addCriterion("CHARGE_WAY =", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotEqualTo(String value) {
            addCriterion("CHARGE_WAY <>", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayGreaterThan(String value) {
            addCriterion("CHARGE_WAY >", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_WAY >=", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLessThan(String value) {
            addCriterion("CHARGE_WAY <", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_WAY <=", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLike(String value) {
            addCriterion("CHARGE_WAY like", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotLike(String value) {
            addCriterion("CHARGE_WAY not like", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayIn(List<String> values) {
            addCriterion("CHARGE_WAY in", values, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotIn(List<String> values) {
            addCriterion("CHARGE_WAY not in", values, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayBetween(String value1, String value2) {
            addCriterion("CHARGE_WAY between", value1, value2, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotBetween(String value1, String value2) {
            addCriterion("CHARGE_WAY not between", value1, value2, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeModeIsNull() {
            addCriterion("CHARGE_MODE is null");
            return (Criteria) this;
        }

        public Criteria andChargeModeIsNotNull() {
            addCriterion("CHARGE_MODE is not null");
            return (Criteria) this;
        }

        public Criteria andChargeModeEqualTo(String value) {
            addCriterion("CHARGE_MODE =", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotEqualTo(String value) {
            addCriterion("CHARGE_MODE <>", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeGreaterThan(String value) {
            addCriterion("CHARGE_MODE >", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_MODE >=", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLessThan(String value) {
            addCriterion("CHARGE_MODE <", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_MODE <=", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLike(String value) {
            addCriterion("CHARGE_MODE like", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotLike(String value) {
            addCriterion("CHARGE_MODE not like", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeIn(List<String> values) {
            addCriterion("CHARGE_MODE in", values, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotIn(List<String> values) {
            addCriterion("CHARGE_MODE not in", values, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeBetween(String value1, String value2) {
            addCriterion("CHARGE_MODE between", value1, value2, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotBetween(String value1, String value2) {
            addCriterion("CHARGE_MODE not between", value1, value2, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescIsNull() {
            addCriterion("MIN_PRICE_UNIT_DESC is null");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescIsNotNull() {
            addCriterion("MIN_PRICE_UNIT_DESC is not null");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescEqualTo(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC =", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescNotEqualTo(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC <>", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescGreaterThan(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC >", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescGreaterThanOrEqualTo(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC >=", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescLessThan(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC <", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescLessThanOrEqualTo(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC <=", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescLike(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC like", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescNotLike(String value) {
            addCriterion("MIN_PRICE_UNIT_DESC not like", value, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescIn(List<String> values) {
            addCriterion("MIN_PRICE_UNIT_DESC in", values, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescNotIn(List<String> values) {
            addCriterion("MIN_PRICE_UNIT_DESC not in", values, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescBetween(String value1, String value2) {
            addCriterion("MIN_PRICE_UNIT_DESC between", value1, value2, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMinPriceUnitDescNotBetween(String value1, String value2) {
            addCriterion("MIN_PRICE_UNIT_DESC not between", value1, value2, "minPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescIsNull() {
            addCriterion("MAX_PRICE_UNIT_DESC is null");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescIsNotNull() {
            addCriterion("MAX_PRICE_UNIT_DESC is not null");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescEqualTo(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC =", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescNotEqualTo(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC <>", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescGreaterThan(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC >", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescGreaterThanOrEqualTo(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC >=", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescLessThan(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC <", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescLessThanOrEqualTo(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC <=", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescLike(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC like", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescNotLike(String value) {
            addCriterion("MAX_PRICE_UNIT_DESC not like", value, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescIn(List<String> values) {
            addCriterion("MAX_PRICE_UNIT_DESC in", values, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescNotIn(List<String> values) {
            addCriterion("MAX_PRICE_UNIT_DESC not in", values, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescBetween(String value1, String value2) {
            addCriterion("MAX_PRICE_UNIT_DESC between", value1, value2, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andMaxPriceUnitDescNotBetween(String value1, String value2) {
            addCriterion("MAX_PRICE_UNIT_DESC not between", value1, value2, "maxPriceUnitDesc");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNull() {
            addCriterion("DATA_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNotNull() {
            addCriterion("DATA_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE =", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <>", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThan(String value) {
            addCriterion("DATA_OPER_TYPE >", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE >=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThan(String value) {
            addCriterion("DATA_OPER_TYPE <", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLike(String value) {
            addCriterion("DATA_OPER_TYPE like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotLike(String value) {
            addCriterion("DATA_OPER_TYPE not like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE not in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE not between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNotNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is not null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY =", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <>", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY not like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY not in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY not between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeIsNull() {
            addCriterion("OPER_ORG_CODE is null");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeIsNotNull() {
            addCriterion("OPER_ORG_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeEqualTo(String value) {
            addCriterion("OPER_ORG_CODE =", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeNotEqualTo(String value) {
            addCriterion("OPER_ORG_CODE <>", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeGreaterThan(String value) {
            addCriterion("OPER_ORG_CODE >", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("OPER_ORG_CODE >=", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeLessThan(String value) {
            addCriterion("OPER_ORG_CODE <", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeLessThanOrEqualTo(String value) {
            addCriterion("OPER_ORG_CODE <=", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeLike(String value) {
            addCriterion("OPER_ORG_CODE like", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeNotLike(String value) {
            addCriterion("OPER_ORG_CODE not like", value, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeIn(List<String> values) {
            addCriterion("OPER_ORG_CODE in", values, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeNotIn(List<String> values) {
            addCriterion("OPER_ORG_CODE not in", values, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeBetween(String value1, String value2) {
            addCriterion("OPER_ORG_CODE between", value1, value2, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andOperOrgCodeNotBetween(String value1, String value2) {
            addCriterion("OPER_ORG_CODE not between", value1, value2, "operOrgCode");
            return (Criteria) this;
        }

        public Criteria andRtNoIsNull() {
            addCriterion("RT_NO is null");
            return (Criteria) this;
        }

        public Criteria andRtNoIsNotNull() {
            addCriterion("RT_NO is not null");
            return (Criteria) this;
        }

        public Criteria andRtNoEqualTo(String value) {
            addCriterion("RT_NO =", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotEqualTo(String value) {
            addCriterion("RT_NO <>", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoGreaterThan(String value) {
            addCriterion("RT_NO >", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoGreaterThanOrEqualTo(String value) {
            addCriterion("RT_NO >=", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLessThan(String value) {
            addCriterion("RT_NO <", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLessThanOrEqualTo(String value) {
            addCriterion("RT_NO <=", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoLike(String value) {
            addCriterion("RT_NO like", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotLike(String value) {
            addCriterion("RT_NO not like", value, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoIn(List<String> values) {
            addCriterion("RT_NO in", values, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotIn(List<String> values) {
            addCriterion("RT_NO not in", values, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoBetween(String value1, String value2) {
            addCriterion("RT_NO between", value1, value2, "rtNo");
            return (Criteria) this;
        }

        public Criteria andRtNoNotBetween(String value1, String value2) {
            addCriterion("RT_NO not between", value1, value2, "rtNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIsNull() {
            addCriterion("AUTO_MODEL_NO is null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIsNotNull() {
            addCriterion("AUTO_MODEL_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO =", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO <>", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoGreaterThan(String value) {
            addCriterion("AUTO_MODEL_NO >", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoGreaterThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO >=", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLessThan(String value) {
            addCriterion("AUTO_MODEL_NO <", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLessThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NO <=", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoLike(String value) {
            addCriterion("AUTO_MODEL_NO like", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotLike(String value) {
            addCriterion("AUTO_MODEL_NO not like", value, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoIn(List<String> values) {
            addCriterion("AUTO_MODEL_NO in", values, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotIn(List<String> values) {
            addCriterion("AUTO_MODEL_NO not in", values, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NO between", value1, value2, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNoNotBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NO not between", value1, value2, "autoModelNo");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameIsNull() {
            addCriterion("AUTO_MODEL_NAME is null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameIsNotNull() {
            addCriterion("AUTO_MODEL_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameEqualTo(String value) {
            addCriterion("AUTO_MODEL_NAME =", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameNotEqualTo(String value) {
            addCriterion("AUTO_MODEL_NAME <>", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameGreaterThan(String value) {
            addCriterion("AUTO_MODEL_NAME >", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NAME >=", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameLessThan(String value) {
            addCriterion("AUTO_MODEL_NAME <", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameLessThanOrEqualTo(String value) {
            addCriterion("AUTO_MODEL_NAME <=", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameLike(String value) {
            addCriterion("AUTO_MODEL_NAME like", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameNotLike(String value) {
            addCriterion("AUTO_MODEL_NAME not like", value, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameIn(List<String> values) {
            addCriterion("AUTO_MODEL_NAME in", values, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameNotIn(List<String> values) {
            addCriterion("AUTO_MODEL_NAME not in", values, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NAME between", value1, value2, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andAutoModelNameNotBetween(String value1, String value2) {
            addCriterion("AUTO_MODEL_NAME not between", value1, value2, "autoModelName");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("APPLY_DATE is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("APPLY_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(String value) {
            addCriterion("APPLY_DATE =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(String value) {
            addCriterion("APPLY_DATE <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(String value) {
            addCriterion("APPLY_DATE >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(String value) {
            addCriterion("APPLY_DATE >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(String value) {
            addCriterion("APPLY_DATE <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(String value) {
            addCriterion("APPLY_DATE <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLike(String value) {
            addCriterion("APPLY_DATE like", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotLike(String value) {
            addCriterion("APPLY_DATE not like", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<String> values) {
            addCriterion("APPLY_DATE in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<String> values) {
            addCriterion("APPLY_DATE not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(String value1, String value2) {
            addCriterion("APPLY_DATE between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(String value1, String value2) {
            addCriterion("APPLY_DATE not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("REMARK is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("REMARK =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("REMARK <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("REMARK >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("REMARK >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("REMARK <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("REMARK <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("REMARK like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("REMARK not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("REMARK in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("REMARK not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("REMARK between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("REMARK not between", value1, value2, "remark");
            return (Criteria) this;
        }
        
        public Criteria andEftDateIsNull() {
            addCriterion("EFT_DATE is null");
            return (Criteria) this;
        }

        public Criteria andEftDateIsNotNull() {
            addCriterion("EFT_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andEftDateEqualTo(Date value) {
            addCriterion("EFT_DATE =", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateNotEqualTo(Date value) {
            addCriterion("EFT_DATE <>", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateGreaterThan(Date value) {
            addCriterion("EFT_DATE >", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateGreaterThanOrEqualTo(Date value) {
            addCriterion("EFT_DATE >=", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateLessThan(Date value) {
            addCriterion("EFT_DATE <", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateLessThanOrEqualTo(Date value) {
            addCriterion("EFT_DATE <=", value, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateIn(List<Date> values) {
            addCriterion("EFT_DATE in", values, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateNotIn(List<Date> values) {
            addCriterion("EFT_DATE not in", values, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateBetween(Date value1, Date value2) {
            addCriterion("EFT_DATE between", value1, value2, "eftDate");
            return (Criteria) this;
        }

        public Criteria andEftDateNotBetween(Date value1, Date value2) {
            addCriterion("EFT_DATE not between", value1, value2, "eftDate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_config
     *
     * @mbggenerated do_not_delete_during_merge Tue Mar 15 14:42:10 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}