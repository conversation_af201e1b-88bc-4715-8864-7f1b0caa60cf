<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
	http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<bean id="bilSessionFactory"
		class="com.pt.poseidon.common.persistence.mybatis.DBSessionFactoryBean">
		<property name="defaultDataSource" value="ner_billing" />
	</bean>
	
	<bean
		class="com.pt.poseidon.common.persistence.mybatis.MapperScannerConfigurer">
		<property name="basePackage" value="com.ls.ner.billing" />
		<property name="sqlSessionFactory" ref="bilSessionFactory" />
	</bean>
</beans>