/**
 *
 * @(#) IChargeItemService.java
 * @Package com.ls.ner.billing.item.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.item.service;

import java.util.List;

import com.ls.ner.billing.item.bo.ChargeItemBo;

/**
 * Description : 费用项 service接口
 * 
 * @author: lipf
 * 
 *          History: 2015年7月7日 下午7:19:28 lipf Created.
 * 
 */
public interface IChargeItemService {

	/**
	 * 
	 * Method description : 费用项 查询
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:22:29 History: 2015年7月7日
	 * 下午7:22:29 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	List<ChargeItemBo> getChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用项 查询记录数
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:22:41 History: 2015年7月7日
	 * 下午7:22:41 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	int getChargeItemCount(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 保存
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:22:53 History: 2015年7月7日
	 * 下午7:22:53 lipf Created.
	 * 
	 * @param bo
	 * 
	 */
	void saveChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 更新
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:23:01 History: 2015年7月7日
	 * 下午7:23:01 lipf Created.
	 * 
	 * @param bo
	 * 
	 */
	void updateChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 删除
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:23:12 History: 2015年7月7日
	 * 下午7:23:12 lipf Created.
	 * 
	 * @param id
	 * 
	 */
	void deleteChargeItem(String id);

	/**
	 * 
	 * Method description : 费用明细查询
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午8:59:17 History: 2015年7月22日
	 * 下午8:59:17 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	List<ChargeItemBo> getItemDetail(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用明细查询记录数
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午8:59:30 History: 2015年7月22日
	 * 下午8:59:30 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	int getItemDetailCount(ChargeItemBo bo);

}
