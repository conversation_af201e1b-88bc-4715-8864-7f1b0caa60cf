package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.AttachConfigBo;
import com.ls.ner.billing.common.bo.AttachConfigBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AttachConfigBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(AttachConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(AttachConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(AttachConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(AttachConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<AttachConfigBo> selectByExample(AttachConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    AttachConfigBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") AttachConfigBo record, @Param("example") AttachConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") AttachConfigBo record, @Param("example") AttachConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(AttachConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(AttachConfigBo record);
}