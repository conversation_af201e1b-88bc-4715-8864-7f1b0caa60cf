<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="企业充电计费配置" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="企业名称" /></td>
				<td><ls:text type="hidden" name="custId"></ls:text>
                   	<ls:text imageKey="search" onClickImage="getCust" name="custName" enabled="false" onchanged="changeCustName"></ls:text>
               	</td>
				<td><ls:label text="计费编号" /></td>
				<td><ls:text name="chcNo" property="chcNo"/></td>
				<td>
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" width="100%" showCheckBox="false" singleSelect="true"
						 caption="充电计费配置列表" primaryKey="chcNo">
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doAdd" text="新增"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doCopy" text="复制"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="search" onclick="doShow" text="详情"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doCancell" text="注销" name="canCellBar"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="企业名称" name="custName" />
					<ls:column caption="充电计费编号" name="chcNo" />
					<ls:column caption="充电计费名称" name="chcName" align="left"/>
					<ls:column caption="费控模型" name="billCtlModeName" />
					<ls:column caption="计费模式" name="chargeModeName" />
					<ls:column caption="采集数据类型" name="gatherDataTypeName" />
					<ls:column caption="生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="状态" name="chcStatusName" />
					<ls:column caption="操作" name="opr"  formatFunc="opr"/>
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	var astPath = '${astPath }';
	var cstPath = '${cstPath }';
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		params.unChcStatus = '0';
		chargeGrid.query('~/billing/chargeUserbilconf/queryChargeBillingConfs',params,function(){});
	}
	
    function doAdd(){
		LS.dialog("~/billing/chargeUserbilconf/addChargeBillingConf?chcNo=","新增充电计费配置",1000, 600,true, null);
    }
 	
 	function doShow(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要查看详情的记录");
    		return;
	   }
		LS.dialog("~/billing/chargeUserbilconf/showChargeBillingConf?chcNo="+item.chcNo,"查看充电费用配置",1000, 600,true, null);
 	}
 	
 	function doCopy(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要复制的记录");
    		return;
	   }
	   LS.confirm('确定要复制该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargebillingconf/copyChargeBillingConf?chcNo="+item.chcNo,null,function(data){
					if(data!=null){
						LS.message("info","复制成功，新的充电计费编号为"+data.chcNo);
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
 	}
 	window.doModify =doModify;
 	function doModify(chcNo){
		LS.dialog("~/billing/chargeUserbilconf/addChargeBillingConf?chcNo="+chcNo,"修改充电计费配置",1000, 600,true, null);
 	}
    window.doDel =doDel;
	function doDel(chcNo){
	   LS.confirm('确定要删除该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargebillingconf/delChargeBillingConf?chcNo="+chcNo,null,function(data){
					if(data==true){
						LS.message("info","删除成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});	
	}
    window.doRelease =doRelease;
	function doRelease(chcNo){
	   var preEndTime = chargeGrid.getCell(chcNo, "eftDate");
	   var endTime = preEndTime.split(":");
	   preEndTime = endTime[0]+":"+endTime[1];
	   var current = (new Date()).Format("yyyy-MM-dd hh:mm");
	   if(current >= preEndTime){
		  LS.message("error","生效时间小于当前时间，不允许发布");
	   	  return;
	   }
	   LS.confirm('确定要发布该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargebillingconf/releaseChargeBillingConf?chcNo="+chcNo,null,function(data){
					if(data==true){
						LS.message("info","发布成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});	
	}
	
    function doClear(){
      custId.setValue();
      custName.setValue();
      searchForm.clear();
    }
    

	function opr(rowdata){
		if(rowdata.chcStatus=='2'){//草稿
			return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>"
				+"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>删除</a>"
				+"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doRelease(\"" + rowdata.chcNo + "\")'>发布</a>";
		}else if(rowdata.chcStatus=='0'){//无效
			return "";
		}else{//有效
		   var endTime = rowdata.eftDate.split(":");
		   var rowDate = endTime[0]+":"+endTime[1];
		   var current = (new Date()).Format("yyyy-MM-dd hh:mm");
			if(rowDate > current){
				return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>";
			}else{
				return "";	
			}
		}
	}
	
	//====================================企业用户查询=============================================================
    function getCust(){
		LS.dialog(cstPath + "/ner/cust/custselect?custType=02", "查询企业信息", 800, 500, true);
		}
    
    function changeCustName(){
    	if(custName.getValue()==null || custName.getValue()==''){
    		custId.setValue();
    	}
    }

	window.setCust = setCust;
	function setCust(item) {
		custId.setValue(item.custId);
		custName.setValue(item.custName);
	}

		window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
		}

		function doCancell(){
		var item = chargeGrid.getSelectedItem();
		if(LS.isEmpty(item)){
		LS.message("error","请选择要注销的记录");
		return;
		}
		LS.confirm('确定要注销该记录?',function(result){
		if(result){
		LS.ajax("~/billing/chargeUserbilconf/cancellChargeBillingConf?chcNo="+item.chcNo,null,function(data){
		if(data==true){
		LS.message("info","注销成功");
		doSearch();
		return;
		}else{
		LS.message("info","操作失败或网络异常，请重试！");
		return;
		}
		});
		}
		});
		}


		chargeGrid.onitemclick = function () {
		var item = chargeGrid.getSelectedItem();
		if (!item) {
		LS.message("info", "请选择一条记录！");
		return;
		}
		var _chcStatus = item.chcStatus;

		if ( _chcStatus == '1') {//生效
		chargeGrid.toolbarItems.canCellBar.setEnabled(true);
		}else{
		chargeGrid.toolbarItems.canCellBar.setEnabled(false);
		}
		}
    </ls:script>
</ls:body>
</html>