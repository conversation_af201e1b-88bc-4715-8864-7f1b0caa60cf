/**
 *
 * @(#) VehicleBillingBo.java
 * @Package com.ls.ner.billing.api.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.bo;

import java.io.Serializable;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: VehicleBillingBo.java,v 1.1 2016/02/26 02:35:24 34544 Exp $
 * 
 *          History: 2016年2月25日 下午9:47:19 lipf Created.
 * 
 */
public class VehicleBillingBo implements Serializable {

	private static final long serialVersionUID = 1683355072784997047L;
	private String billingConfigName;
	private String billingNo;
	private String minChargeMode;
	private String maxChargeMode;
	private String chargeUnit;
	private String peBe;
	private String subBe;
	private String chargeMode;
	private String remark;
	private String batch;
	private String rtNo;
	private String autoModelNo;
	private String effectTime;
	private String invalidTime;
	private String isCover;
	private String priority;
	private String version;
	private String orgCode;
	private String status;
	private String unifiedPrice;

	public String getBillingConfigName() {
		return billingConfigName;
	}

	public void setBillingConfigName(String billingConfigName) {
		this.billingConfigName = billingConfigName;
	}

	public String getBillingNo() {
		return billingNo;
	}

	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}

	public String getMinChargeMode() {
		return minChargeMode;
	}

	public void setMinChargeMode(String minChargeMode) {
		this.minChargeMode = minChargeMode;
	}

	public String getMaxChargeMode() {
		return maxChargeMode;
	}

	public void setMaxChargeMode(String maxChargeMode) {
		this.maxChargeMode = maxChargeMode;
	}

	public String getChargeUnit() {
		return chargeUnit;
	}

	public void setChargeUnit(String chargeUnit) {
		this.chargeUnit = chargeUnit;
	}

	public String getPeBe() {
		return peBe;
	}

	public void setPeBe(String peBe) {
		this.peBe = peBe;
	}

	public String getSubBe() {
		return subBe;
	}

	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}

	public String getChargeMode() {
		return chargeMode;
	}

	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getBatch() {
		return batch;
	}

	public void setBatch(String batch) {
		this.batch = batch;
	}

	public String getRtNo() {
		return rtNo;
	}

	public void setRtNo(String rtNo) {
		this.rtNo = rtNo;
	}

	public String getAutoModelNo() {
		return autoModelNo;
	}

	public void setAutoModelNo(String autoModelNo) {
		this.autoModelNo = autoModelNo;
	}

	public String getEffectTime() {
		return effectTime;
	}

	public void setEffectTime(String effectTime) {
		this.effectTime = effectTime;
	}

	public String getInvalidTime() {
		return invalidTime;
	}

	public void setInvalidTime(String invalidTime) {
		this.invalidTime = invalidTime;
	}

	public String getIsCover() {
		return isCover;
	}

	public void setIsCover(String isCover) {
		this.isCover = isCover;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getUnifiedPrice() {
		return unifiedPrice;
	}

	public void setUnifiedPrice(String unifiedPrice) {
		this.unifiedPrice = unifiedPrice;
	}
}
