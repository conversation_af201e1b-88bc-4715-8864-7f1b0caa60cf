<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.BillingConfigBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="BILLING_NO" jdbcType="VARCHAR" property="billingNo" />
    <result column="VERSION" jdbcType="VARCHAR" property="version" />
    <result column="IS_LATEST_VERSION" jdbcType="VARCHAR" property="isLatestVersion" />
    <result column="IS_ADJUSTED" jdbcType="VARCHAR" property="isAdjusted" />
    <result column="BILLING_CONFIG_NAME" jdbcType="VARCHAR" property="billingConfigName" />
    <result column="UNIFORM_PRICE" jdbcType="VARCHAR" property="uniformPrice" />
    <result column="PE_BE" jdbcType="VARCHAR" property="peBe" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="CHARGE_WAY" jdbcType="VARCHAR" property="chargeWay" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="MIN_PRICE_UNIT_DESC" jdbcType="VARCHAR" property="minPriceUnitDesc" />
    <result column="MAX_PRICE_UNIT_DESC" jdbcType="VARCHAR" property="maxPriceUnitDesc" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ORG_AUTO_MODEL_KEY" jdbcType="VARCHAR" property="orgAutoModelKey" />
    <result column="OPER_ORG_CODE" jdbcType="VARCHAR" property="operOrgCode" />
    <result column="RT_NO" jdbcType="VARCHAR" property="rtNo" />
    <result column="AUTO_MODEL_NO" jdbcType="VARCHAR" property="autoModelNo" />
    <result column="AUTO_MODEL_NAME" jdbcType="VARCHAR" property="autoModelName" />
    <result column="APPLY_DATE" jdbcType="VARCHAR" property="applyDate" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="EFT_DATE" jdbcType="VARCHAR" property="eftDate" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    SYSTEM_ID, BILLING_NO, VERSION, IS_LATEST_VERSION, IS_ADJUSTED, BILLING_CONFIG_NAME, 
    UNIFORM_PRICE, PE_BE, SUB_BE, CHARGE_WAY, CHARGE_MODE, MIN_PRICE_UNIT_DESC, MAX_PRICE_UNIT_DESC, 
    DATA_OPER_TIME, DATA_OPER_TYPE, ORG_AUTO_MODEL_KEY, OPER_ORG_CODE, RT_NO, AUTO_MODEL_NO, 
    AUTO_MODEL_NAME, APPLY_DATE, REMARK,EFT_DATE,PLAN_NO
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.BillingConfigBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    SELECT (select a.plan_name from e_tariff_plan a where a.plan_no = b.PLAN_NO) plan_name,
		DATE_FORMAT(b.EFT_DATE,"%Y-%m-%d %H:%i") EFT_DATE,b.*
		FROM(
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_billing_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    ) b
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_billing_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    delete from e_billing_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.BillingConfigBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    delete from e_billing_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    insert into e_billing_config (SYSTEM_ID, BILLING_NO, VERSION, 
      IS_LATEST_VERSION, IS_ADJUSTED, BILLING_CONFIG_NAME, 
      UNIFORM_PRICE, PE_BE, SUB_BE, 
      CHARGE_WAY, CHARGE_MODE, MIN_PRICE_UNIT_DESC, 
      MAX_PRICE_UNIT_DESC, DATA_OPER_TIME, DATA_OPER_TYPE, 
      ORG_AUTO_MODEL_KEY, OPER_ORG_CODE, RT_NO, 
      AUTO_MODEL_NO, AUTO_MODEL_NAME, APPLY_DATE, 
      REMARK)
    values (#{systemId,jdbcType=BIGINT}, #{billingNo,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{isLatestVersion,jdbcType=VARCHAR}, #{isAdjusted,jdbcType=VARCHAR}, #{billingConfigName,jdbcType=VARCHAR}, 
      #{uniformPrice,jdbcType=VARCHAR}, #{peBe,jdbcType=VARCHAR}, #{subBe,jdbcType=VARCHAR}, 
      #{chargeWay,jdbcType=VARCHAR}, #{chargeMode,jdbcType=VARCHAR}, #{minPriceUnitDesc,jdbcType=VARCHAR}, 
      #{maxPriceUnitDesc,jdbcType=VARCHAR}, #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}, 
      #{orgAutoModelKey,jdbcType=VARCHAR}, #{operOrgCode,jdbcType=VARCHAR}, #{rtNo,jdbcType=VARCHAR}, 
      #{autoModelNo,jdbcType=VARCHAR}, #{autoModelName,jdbcType=VARCHAR}, #{applyDate,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    insert into e_billing_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="billingNo != null">
        BILLING_NO,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="isLatestVersion != null">
        IS_LATEST_VERSION,
      </if>
      <if test="isAdjusted != null">
        IS_ADJUSTED,
      </if>
      <if test="billingConfigName != null">
        BILLING_CONFIG_NAME,
      </if>
      <if test="uniformPrice != null">
        UNIFORM_PRICE,
      </if>
      <if test="peBe != null">
        PE_BE,
      </if>
      <if test="subBe != null">
        SUB_BE,
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY,
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE,
      </if>
      <if test="minPriceUnitDesc != null">
        MIN_PRICE_UNIT_DESC,
      </if>
      <if test="maxPriceUnitDesc != null">
        MAX_PRICE_UNIT_DESC,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY,
      </if>
      <if test="operOrgCode != null">
        OPER_ORG_CODE,
      </if>
      <if test="rtNo != null">
        RT_NO,
      </if>
      <if test="autoModelNo != null">
        AUTO_MODEL_NO,
      </if>
      <if test="autoModelName != null">
        AUTO_MODEL_NAME,
      </if>
      <if test="applyDate != null">
        APPLY_DATE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="planNo != null">
        PLAN_NO,
      </if>
      <if test="eftDate != null">
        EFT_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="billingNo != null">
        #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="isLatestVersion != null">
        #{isLatestVersion,jdbcType=VARCHAR},
      </if>
      <if test="isAdjusted != null">
        #{isAdjusted,jdbcType=VARCHAR},
      </if>
      <if test="billingConfigName != null">
        #{billingConfigName,jdbcType=VARCHAR},
      </if>
      <if test="uniformPrice != null">
        #{uniformPrice,jdbcType=VARCHAR},
      </if>
      <if test="peBe != null">
        #{peBe,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="chargeWay != null">
        #{chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="minPriceUnitDesc != null">
        #{minPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="maxPriceUnitDesc != null">
        #{maxPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="operOrgCode != null">
        #{operOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="rtNo != null">
        #{rtNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelNo != null">
        #{autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelName != null">
        #{autoModelName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="planNo != null">
        #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="eftDate != null">
        #{eftDate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.BillingConfigBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    select count(*) from e_billing_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    update e_billing_config
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.billingNo != null">
        BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        VERSION = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.isLatestVersion != null">
        IS_LATEST_VERSION = #{record.isLatestVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.isAdjusted != null">
        IS_ADJUSTED = #{record.isAdjusted,jdbcType=VARCHAR},
      </if>
      <if test="record.billingConfigName != null">
        BILLING_CONFIG_NAME = #{record.billingConfigName,jdbcType=VARCHAR},
      </if>
      <if test="record.uniformPrice != null">
        UNIFORM_PRICE = #{record.uniformPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.peBe != null">
        PE_BE = #{record.peBe,jdbcType=VARCHAR},
      </if>
      <if test="record.subBe != null">
        SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeWay != null">
        CHARGE_WAY = #{record.chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeMode != null">
        CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="record.minPriceUnitDesc != null">
        MIN_PRICE_UNIT_DESC = #{record.minPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.maxPriceUnitDesc != null">
        MAX_PRICE_UNIT_DESC = #{record.maxPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="record.operOrgCode != null">
        OPER_ORG_CODE = #{record.operOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.rtNo != null">
        RT_NO = #{record.rtNo,jdbcType=VARCHAR},
      </if>
      <if test="record.autoModelNo != null">
        AUTO_MODEL_NO = #{record.autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="record.autoModelName != null">
        AUTO_MODEL_NAME = #{record.autoModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDate != null">
        APPLY_DATE = #{record.applyDate,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        REMARK = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.eftDate != null">
        EFT_DATE = #{record.eftDate,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    update e_billing_config
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      VERSION = #{record.version,jdbcType=VARCHAR},
      IS_LATEST_VERSION = #{record.isLatestVersion,jdbcType=VARCHAR},
      IS_ADJUSTED = #{record.isAdjusted,jdbcType=VARCHAR},
      BILLING_CONFIG_NAME = #{record.billingConfigName,jdbcType=VARCHAR},
      UNIFORM_PRICE = #{record.uniformPrice,jdbcType=VARCHAR},
      PE_BE = #{record.peBe,jdbcType=VARCHAR},
      SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      CHARGE_WAY = #{record.chargeWay,jdbcType=VARCHAR},
      CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      MIN_PRICE_UNIT_DESC = #{record.minPriceUnitDesc,jdbcType=VARCHAR},
      MAX_PRICE_UNIT_DESC = #{record.maxPriceUnitDesc,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      OPER_ORG_CODE = #{record.operOrgCode,jdbcType=VARCHAR},
      RT_NO = #{record.rtNo,jdbcType=VARCHAR},
      AUTO_MODEL_NO = #{record.autoModelNo,jdbcType=VARCHAR},
      AUTO_MODEL_NAME = #{record.autoModelName,jdbcType=VARCHAR},
      APPLY_DATE = #{record.applyDate,jdbcType=VARCHAR},
      REMARK = #{record.remark,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    update e_billing_config
    <set>
      <if test="billingNo != null">
        BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=VARCHAR},
      </if>
      <if test="isLatestVersion != null">
        IS_LATEST_VERSION = #{isLatestVersion,jdbcType=VARCHAR},
      </if>
      <if test="isAdjusted != null">
        IS_ADJUSTED = #{isAdjusted,jdbcType=VARCHAR},
      </if>
      <if test="billingConfigName != null">
        BILLING_CONFIG_NAME = #{billingConfigName,jdbcType=VARCHAR},
      </if>
      <if test="uniformPrice != null">
        UNIFORM_PRICE = #{uniformPrice,jdbcType=VARCHAR},
      </if>
      <if test="peBe != null">
        PE_BE = #{peBe,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        SUB_BE = #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY = #{chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="minPriceUnitDesc != null">
        MIN_PRICE_UNIT_DESC = #{minPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="maxPriceUnitDesc != null">
        MAX_PRICE_UNIT_DESC = #{maxPriceUnitDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="operOrgCode != null">
        OPER_ORG_CODE = #{operOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="rtNo != null">
        RT_NO = #{rtNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelNo != null">
        AUTO_MODEL_NO = #{autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelName != null">
        AUTO_MODEL_NAME = #{autoModelName,jdbcType=VARCHAR},
      </if>
      <if test="applyDate != null">
        APPLY_DATE = #{applyDate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.api.common.bo.BillingConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Mar 15 14:42:10 CST 2016.
    -->
    update e_billing_config
    set BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      VERSION = #{version,jdbcType=VARCHAR},
      IS_LATEST_VERSION = #{isLatestVersion,jdbcType=VARCHAR},
      IS_ADJUSTED = #{isAdjusted,jdbcType=VARCHAR},
      BILLING_CONFIG_NAME = #{billingConfigName,jdbcType=VARCHAR},
      UNIFORM_PRICE = #{uniformPrice,jdbcType=VARCHAR},
      PE_BE = #{peBe,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      CHARGE_WAY = #{chargeWay,jdbcType=VARCHAR},
      CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      MIN_PRICE_UNIT_DESC = #{minPriceUnitDesc,jdbcType=VARCHAR},
      MAX_PRICE_UNIT_DESC = #{maxPriceUnitDesc,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      OPER_ORG_CODE = #{operOrgCode,jdbcType=VARCHAR},
      RT_NO = #{rtNo,jdbcType=VARCHAR},
      AUTO_MODEL_NO = #{autoModelNo,jdbcType=VARCHAR},
      AUTO_MODEL_NAME = #{autoModelName,jdbcType=VARCHAR},
      APPLY_DATE = #{applyDate,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>