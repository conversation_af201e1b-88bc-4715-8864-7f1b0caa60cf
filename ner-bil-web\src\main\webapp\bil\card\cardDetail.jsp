<%@page import="com.ls.ner.def.api.constants.Constants.HandleStatus"%>
<%@page import="com.ls.ner.def.api.constants.Constants.FileStatus"%>
<%@page import="com.ls.ner.def.api.constants.Constants.InvoiceStatus"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="卡库">
</ls:head>
<ls:body >
    <ls:form name="cardDetailForm" id="cardDetailForm">
        <ls:text name="cardId" type="hidden" value="${cardId}"></ls:text>
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="手机号"/></td>
                <td><ls:text name="mobile" property="mobile"/></td>
                <td><ls:label text="卡号"/></td>
                <td><ls:text name="cardNo" property="cardNo"/></td>
                <td><ls:label text="卡密"/></td>
                <td><ls:text name="cardSecret" property="cardSecret"/></td>
            </tr>

            <tr>
                <td>
                    <ls:label text="状态" ref="cardDetailStatus"></ls:label>
                </td>
                <td>
                    <ls:select name="cardDetailStatus">
                        <ls:options property="rechargeCardStatusList" scope="request" text="codeName" value="codeValue"></ls:options>
                    </ls:select>
                </td>
                <td>
                    <ls:label text="使用时间"></ls:label>
                </td>
                <td>
                    <table>
                        <tr>
                            <td>
                                <ls:date name="settleTimeBegin" format="yyyy-MM-dd"/>
                            </td>
                            <td>
                                <span>至</span>
                            </td>
                            <td>
                                <ls:date name="settleTimeEnd" format="yyyy-MM-dd"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="6">
                    <div class="pull-right">
                        <ls:button text="查询" onclick="do_query" />
                        <ls:button text="清空" onclick="do_clear" />
                    </div>
                </td>
            </tr>
        </table>

        <ls:title text="卡号列表"></ls:title>
        <table class="tab_search" id="gridContent">
            <tr>
                <td>
                    <ls:grid url="" showRowNumber="false" name="cardDetailGrid"   showCheckBox="false">
                                <ls:gridToolbar name="export_Data">
                                    <ls:gridToolbarItem name="freeze" text="冻结" imageKey="start" onclick="freeze" ></ls:gridToolbarItem>
                                    <ls:gridToolbarItem name="unFreeze" text="解冻" imageKey="stop" onclick="unFreeze"></ls:gridToolbarItem>
                                    <ls:gridToolbarItem name="refund" text="退款" imageKey="cancel" onclick="refund" ></ls:gridToolbarItem>
                                    <ls:gridToolbarItem name="unRefund" text="取消退款" imageKey="reset" onclick="unRefund" ></ls:gridToolbarItem>
                                    <ls:gridToolbarItem name="detailCardOrder" text="详情" imageKey="look" onclick="detailCardOrder" />

                                    <%--                                    <ls:gridToolbarItem name="export_data" text="导出" imageKey="add" onclick="exportData"></ls:gridToolbarItem>--%>
                                </ls:gridToolbar>
                        <ls:column caption="充值卡明细Id" name="cardDetailId"  align="center" hidden="true"></ls:column>
                        <ls:column caption="充值卡Id" name="cardId"  align="center" hidden="true"></ls:column>
                        <ls:column caption="卡号" name="cardNo" align="center" />
                        <ls:column caption="卡密" name="cardSecret" align="center" />
                        <ls:column caption="手机号" name="mobile" align="center"/>
                        <ls:column caption="总面值" name="totalAmt" align="center" />
                        <ls:column caption="实际面值" name="actualAmt" align="center" />
                        <ls:column caption="赠送部分面值" name="giftAmt" align="center"  />
                        <ls:column caption="总核销金额" name="totalClearCount" align="center"  />
                        <ls:column caption="实际核销金额" name="realClearCount" align="center"  />
                        <ls:column caption="赠送部分核销金额" name="giftClearCount" align="center"  />
                        <ls:column caption="总余额" name="totalBalance" align="center"  />
                        <ls:column caption="实际余额" name="actualBalance" align="center"  />
                        <ls:column caption="赠送部分余额" name="giftBalance" align="center"  />
                        <ls:column caption="激活时间" name="activateTime" align="center"  />
                        <ls:column caption="状态" name="cardDetailStatusName" align="center"  />
                        <ls:column caption="创建时间" name="createTime" align="center"  />
                        <ls:column caption="创建人" name="createUser" align="center"  />
                        <ls:pager pageSize="15"></ls:pager>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        var cardId = '${cardId}';

        $(function() {
            do_query();
        });

        function do_query() {
            var data = cardDetailForm.getFormData();

                cardDetailGrid.query('~/bil/rechargeCard/qryDetail',data,function(result) {

            });
        }


        function do_clear() {
            cardDetailForm.clear();
        }

        function freeze() {
            var item = cardDetailGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要冻结的记录");
                return;
            }
            if(item.cardDetailStatus=='1' || item.cardDetailStatus=='0'){
            }else{
                LS.message("error","已激活/待激活状态才可以冻结");
                return;
            }

            LS.confirm('确定要冻结该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/updateCardDetailStatus?cardDetailId="+item.cardDetailId +"&detailStatus=2" ,null,function(data){
                        if(data==true){
                            LS.message("info","冻结成功");
                            do_query();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function unFreeze() {
            var item = cardDetailGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择解冻的记录");
                return;
            }
            if(item.cardDetailStatus!='2'){
                LS.message("error","冻结状态才可以解冻");
                return;
            }

            LS.confirm('确定要解冻该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/updateCardDetailStatus?cardDetailId="+item.cardDetailId +"&detailStatus=unFreeze" ,null,function(data){
                        if(data==true){
                            LS.message("info","解冻成功");
                            do_query();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function refund() {
            var item = cardDetailGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择退款的记录");
                return;
            }

            if(item.cardDetailStatus!='2'){
                LS.message("error","冻结状态才可以退款");
                return;
            }

            LS.confirm('确定要退款该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/updateCardDetailStatus?cardDetailId="+item.cardDetailId +"&detailStatus=3" ,null,function(data){
                        if(data==true){
                            LS.message("info","退款成功");
                            do_query();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function unRefund() {
            var item = cardDetailGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择取消退款的记录");
                return;
            }

            if(item.cardDetailStatus!='3'){
                LS.message("error","已退款状态才可以取消退款");
                return;
            }
            LS.confirm('确定要取消退款该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/updateCardDetailStatus?cardDetailId="+item.cardDetailId +"&detailStatus=unRefund" ,null,function(data){
                        if(data==true){
                            LS.message("info","取消退款成功");
                            do_query();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function detailCardOrder(){
            var item = cardDetailGrid.getSelectedItem();
                if(LS.isEmpty(item)){
                LS.message("error","请选择要查看明细");
                return;
            }
            LS.dialog("~/bil/rechargeCard/cardOrderDetails/" + item.cardDetailId , "充值卡订单明细", 800, 500, true, null);
        }

    </ls:script>
</ls:body>
</html>