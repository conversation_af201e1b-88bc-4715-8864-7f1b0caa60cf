package com.ls.ner.billing.charge.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.constants.PublicConstants;
import com.pt.poseidon.code.api.bo.CodeBO;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.util.FormatUtil;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
/**
 * 充电资费管理---服务项目维护
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/chargeServiceItem")
public class ChargeSerItemController extends QueryController<ChargeSerItemQueryCondition> {
	
	@Autowired
	private ControllerSupport controllerSupport;

	@ServiceAutowired("chargeSerItemService")
	private IChargeSerItemService chargeSerItemService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;
	
	@Override
	protected ChargeSerItemQueryCondition initCondition() {
		return new ChargeSerItemQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 初始化服务项目维护
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		List<CodeBO> itemStatus = codeService.getStandardCodes("itemStatus",null);
		m.addAttribute("itemStatusList", itemStatus);

		ChargeSerItemQueryCondition condition=new ChargeSerItemQueryCondition();
		condition.setItemStatus("1");
		m.addAttribute("searchForm", condition );
		return "/bil/charge/chargeSerItemIndex";
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目
	 */
	@RequestMapping(value = "/queryChargeSerItems")
	public @ItemResponseBody QueryResultObject queryChargeSerItems(
			@QueryRequestParam("params") RequestCondition params) {
		ChargeSerItemQueryCondition condition = this.rCondition2QCondition(params);
		condition.setpBe("02");//业务大类：01租车 02 充电
		condition.setItemType("01");//项目类型：01服务02押金
		List<ChargeSerItemBo> list =chargeSerItemService.queryChargeSerItems(condition);
		int recordCount = chargeSerItemService.queryChargeSerItemsNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增/修改服务项目
	 */
	@RequestMapping(value = "/addChargeSerItem", method = RequestMethod.GET)
	public String addCardPay(Model m, @RequestParam("itemNo") String itemNo) {
		if (StringUtils.nullOrBlank(itemNo)) {
			ChargeSerItemQueryCondition condition = new ChargeSerItemQueryCondition();
			condition.setpBe("02");//业务大类：01租车 02 充电
			condition.setItemType("01");//项目类型：01服务02押金
			int recordCount = chargeSerItemService.queryChargeSerMaxSn(condition);
			ChargeSerItemBo csi = new ChargeSerItemBo();
			csi.setSn(String.valueOf(recordCount+1));
			m.addAttribute("chargeForm", csi);
		} else {
			ChargeSerItemQueryCondition condition = new ChargeSerItemQueryCondition();
			condition.setItemNo(itemNo);
			List<ChargeSerItemBo> list =chargeSerItemService.queryChargeSerItems(condition);
			if(list != null && list.size() > 0){
				ChargeSerItemBo bo =list.get(0);
				m.addAttribute("chargeForm", bo);
			}else
				m.addAttribute("chargeForm", new ChargeSerItemBo());
		}
		addCodes(m, BizConstants.CodeType.BILLING_APPEND_ITEM_BUY_TYPE,BizConstants.CodeType.CHC_BILLING_PRICE_UNIT);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/charge/addChargeSerItem";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 保存服务项目
	 */
	@RequestMapping(value = "/saveChargeSerItem", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveCardPay(@RequestBody Map<String, Object> dataParams) throws IllegalAccessException, InvocationTargetException{
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeSerItemBo bo = new ChargeSerItemBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try {
			String itemNo = bo.getItemNo();
			String systemId = bo.getSystemId();
			System.out.print("--------systemId"+systemId);
			ChargeSerItemQueryCondition search = new ChargeSerItemQueryCondition();
			search.setItemName(bo.getItemName());
			search.setpBe("02");//业务大类：01租车 02 充电
			search.setItemType("01");//项目类型：01服务02押金
			search.setItemStatus("1");//启用
			search.setItemNo(itemNo);
			if (systemId != null && !"".equals(systemId)) {
				chargeSerItemService.updateChargeSerItem(bo);
			} else {
				int count = chargeSerItemService.queryChargeSerItemsNumByName(search);
				if (count > 0)
					return WrappedResult.failedWrappedResult("已存在相同费用项");
				bo.setBuyType("02");//必购
				bo.setpBe("02");//业务大类：01租车 02 充电
				bo.setItemType("01");//项目类型：01服务02押金
				bo.setItemStatus("1");//启用 1
				chargeSerItemService.insertChargeSerItem(bo);
			}
		}catch(Exception e){
			return WrappedResult.failedWrappedResult("系统错误！");
		}
		return WrappedResult.successWrapedResult(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 启用服务费用项目
	 */
	@RequestMapping(value = "/enableChargeSerItem", method = RequestMethod.POST)
	public @ResponseBody WrappedResult enableChargeSerItem(Model m, @RequestParam("itemNo") String itemNo) {
		ChargeSerItemBo bo = new ChargeSerItemBo();
		bo.setItemNo(itemNo);
		bo.setItemStatus("1");//启用
		chargeSerItemService.updateChargeSerItem(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 停用服务费用项目
	 */
	@RequestMapping(value = "/disableChargeSerItem")
	public @ResponseBody WrappedResult disableChargeSerItem(Model m, @RequestParam("itemNo") String itemNo) {
		ChargeSerItemBo bo = new ChargeSerItemBo();
		bo.setItemNo(itemNo);
		bo.setItemStatus("0");//停用
		chargeSerItemService.updateChargeSerItem(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-25
	 * @description 查询服务项目及费用
	 * @param itemNo 多个编号    如：1,2,3,4,5
	 * @param price  多个编号价格  如：1.21,2.11,3.11
	 * @throws Exception 
	 */
	@RequestMapping(value = "/querySerItemsByNo")
	public @ItemResponseBody QueryResultObject querySerItemsByNo(
			@QueryRequestParam("params") RequestCondition params) throws Exception {
		ChargeSerItemQueryCondition condition = this.rCondition2QCondition(params);
//		condition.setPageEnd(0);
		condition.setpBe("02");//业务大类：01租车 02 充电
		condition.setItemType("01");//项目类型：01服务02押金
		List<ChargeSerItemBo> list =chargeSerItemService.querySerItemsByNo(condition);
	/*	if(list != null)
			for(ChargeSerItemBo csBo:list){
				csBo.setServicePrice(FormatUtil.formatNumber(csBo.getServicePrice(),4));
			}*/
		return RestUtils.wrappQueryResult(list);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 选择化服务项目
	 */
	@RequestMapping(value = "/select", method = RequestMethod.GET)
	public String select(Model m,ChargeSerItemQueryCondition condition) {
		m.addAttribute("searchForm", condition);
		addCodes(m, BizConstants.CodeType.ENABLE_FLAG);
		return "/bil/charge/chargeSerItemSelect";
	}
	
	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}
}
