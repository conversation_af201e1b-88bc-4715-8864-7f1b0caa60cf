<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型调价" />
<ls:body>
		<ls:form id="billingAdjustForm" name="billingAdjustForm">
		    <ls:text name="isAdjusted" value="1" type="hidden"></ls:text>
				<ls:title text="基本信息 "></ls:title>
				<table class="tab_search">
						<tr>
								<td>
										<ls:label text="调价日期" ref="applyDate" />
								</td>
								<td>
										<ls:text name="applyDate" property="applyDate" readOnly="true"></ls:text>
								</td>
								<td>
										<ls:label text="车型" ref="autoModelName" />
								</td>
								<td>
										<ls:text name="autoModelNo" property="autoModelNo" type="hidden"></ls:text>
										<ls:text imageKey="search" required="true" name="autoModelName" property="autoModelName" readOnly="true"></ls:text>
								</td>
								<td>
										<ls:label text="租赁方式" ref="subBeName" />
								</td>
								<td>
										<ls:text name="subBe" property="subBe" type="hidden"></ls:text>
										<ls:text name="subBeName" property="subBeName" readOnly="true" required="true"></ls:text>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="资费模版" ref="planName" />
								</td>
								<td>
										<ls:text name="planNo" property="planNo" type="hidden"></ls:text>
										<ls:text imageKey="search" required="true" name="planName" property="planName" readOnly="true"></ls:text>
								</td>
								<td>
										<ls:label text="计费方式" ref="chargeWayName" />
								</td>
								<td>
										<ls:text name="chargeWay" property="chargeWay" type="hidden"></ls:text>
										<ls:text name="chargeWayName" property="chargeWayName" required="true" readOnly="true"></ls:text>
								</td>
								<td>
										<ls:label text="计费模式" ref="chargeModeName" />
								</td>
								<td>
										<ls:text name="chargeMode" property="chargeMode" type="hidden"></ls:text>
										<ls:text name="chargeModeName" property="chargeModeName" required="true" readOnly="true"></ls:text>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="调价方案名称" ref="billingConfigName" />
								</td>
								<td colspan="3">
										<ls:text name="billingConfigName" property="billingConfigName" required="true"></ls:text>
								</td>
								<%-- <td>
										<ls:label text="生效日期" ref="eftDate" />
								</td>
								<td>
										<ls:date name="eftDate" format="yyyy-MM-dd HH:mm" property="eftDate" required="true"></ls:date>
								</td> --%>
						</tr>
				</table>
				<ls:title text="租赁费用"></ls:title>
				<table class="tab_search">
						<tr style="display: none;">
								<td width="200px">
										<ls:label text="付费时点" ref="billingFunc_mainChargeItem_chargeTimePoint" />
								</td>
								<td>
										<ls:select name="billingFunc_mainChargeItem_chargeTimePoint" property="billingFunc.mainChargeItem.chargeTimePoint" required="true" visible="false" value="00">
												<ls:option value="01" text="预付费"></ls:option>
												<ls:option value="02" text="后付费"></ls:option>
										</ls:select>
								</td>
						</tr>
						<%--按时间 --%>
						<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeWay eq '0101'||billingAdjustForm.billingFunc.mainChargeItem.chargeWay eq '0103' }">
								<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '01'}">
										<tr>
												<td width="200px">
														<ls:label text="时间单价" ref="billingFunc_mainChargeItem_timeChargeItem_price" />
												</td>
												<td colspan="2">
														<ls:text name="billingFunc_mainChargeItem_timeChargeItem_price" property="billingFunc.mainChargeItem.timeChargeItem.price" required="true"></ls:text>
												</td>
												<td>
														元/
														<c:out value="${billingAdjustForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc}" />
												</td>
										</tr>
								</c:if>
								<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '02'|| billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
										<tr>
												<td width="200px">
														<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
																<ls:label text="时间分时价格" />
														</c:if>
														<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
																<ls:label text="时间阶梯价格" />
														</c:if>
												</td>
										</tr>
										<c:forEach var="rci" items="${billingAdjustForm.billingFunc.mainChargeItem.timeChargeItem.rangeChargeItems}" varStatus="status">
												<tr>
														<td style="text-align: right">
																<ls:label text="${rci.range}" ref="billingFunc_mainChargeItem_timeChargeItem_${status.index}_price"></ls:label>
														</td>
														<td>
																<ls:text name="billingFunc_mainChargeItem_timeChargeItem_${status.index}_price" id="billingFunc_mainChargeItem_timeChargeItem_${status.index}_price" property="billingFunc.mainChargeItem.timeChargeItem.rangeChargeItems[${status.index}].price" required="true"></ls:text>
														</td>
														<td>
																元/
																<c:out value="${billingAdjustForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc}" />
														</td>
												</tr>
										</c:forEach>
								</c:if>
						</c:if>
						<%--按里程 --%>
						<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeWay=='0102'||billingAdjustForm.billingFunc.mainChargeItem.chargeWay=='0103' }">
								<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '01'}">
										<tr>
												<td width="200px">
														<ls:label text="里程单价" ref="billingFunc_mainChargeItem_millChargeItem_price" />
												</td>
												<td colspan="2">
														<ls:text name="billingFunc_mainChargeItem_millChargeItem_price" property="billingFunc.mainChargeItem.millChargeItem.price" required="true"></ls:text>
												</td>
												<td>
														元/
														<c:out value="${billingAdjustForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.desc}" />
												</td>
										</tr>
								</c:if>
								<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '02'|| billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
										<tr>
												<td width="200px">
														<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
																<ls:label text="里程分时价格" />
														</c:if>
														<c:if test="${billingAdjustForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
																<ls:label text="里程阶梯价格" />
														</c:if>
												</td>
										</tr>
										<c:forEach var="rci" items="${billingAdjustForm.billingFunc.mainChargeItem.millChargeItem.rangeChargeItems}" varStatus="status">
												<tr>
														<td style="text-align: right">
                                <ls:label text="${rci.range}" ref="billingFunc_mainChargeItem_millChargeItem_${status.index}_price"/>
														</td>
														<td>
																<ls:text name="billingFunc_mainChargeItem_millChargeItem_${status.index}_price" id="billingFunc_mainChargeItem_millChargeItem_${status.index}_price" property="billingFunc.mainChargeItem.millChargeItem.rangeChargeItems[${status.index}].price" required="true"></ls:text>
														</td>
														<td>
																元/
																<c:out value="${billingAdjustForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.desc}" />
														</td>
												</tr>
										</c:forEach>
								</c:if>
						</c:if>
						<tr>
								<td width="200px">
										<ls:label text="最低消费" ref="billingFunc_mainChargeItem_min_limitQuantity" />
								</td>
								<td>
										<ls:select name="billingFunc_mainChargeItem_min_limitType" property="billingFunc.mainChargeItem.min.limitType">
												<ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
								<td>
										<ls:text name="billingFunc_mainChargeItem_min_limitQuantity" property="billingFunc.mainChargeItem.min.limitQuantity" required="true"></ls:text>
								</td>
								<td>元</td>
						</tr>
						<tr>
								<td width="200px">
										<ls:label text="最高消费" ref="billingFunc_mainChargeItem_max_limitQuantity" />
								</td>
								<td>
										<ls:select name="billingFunc_mainChargeItem_max_limitType" property="billingFunc.mainChargeItem.max.limitType">
												<ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
								<td>
										<ls:text name="billingFunc_mainChargeItem_max_limitQuantity" property="billingFunc.mainChargeItem.max.limitQuantity" required="true"></ls:text>
								</td>
								<td>元</td>
						</tr>
				</table>
				<ls:title text="押金项目"></ls:title>
				<table align="center" class="tab_search">
						<c:forEach var="aci" items="${billingAdjustForm.billingFunc.depositChargeItems}" varStatus="status">
								<tr>
										<td width="200px">
												 <ls:label text="${aci.itemName}" ref="depositItem_price_${status.index}"></ls:label>
										</td>
										<td>
												<ls:select name="depositItem_chargeTimePoint_${status.index}" id="depositItem_chargeTimePoint_${status.index}" property="billingFunc.depositChargeItems[${status.index}].chargeTimePoint" visible="false" value="00">
														<ls:option value="01" text="预付费"></ls:option>
														<ls:option value="02" text="后付费"></ls:option>
												</ls:select>
										</td>
										<td>
												<ls:text name="depositItem_price_${status.index}" id="depositItem_price_${status.index}" property="billingFunc.depositChargeItems[${status.index}].price" required="true"></ls:text>
										</td>
										<td>
												元/
												<c:out value="${aci.priceUnit.desc}" />
										</td>
								</tr>
						</c:forEach>
				</table>
				<ls:title text="其他服务项目"></ls:title>
				<table align="center" class="tab_search">
						<c:forEach var="aci" items="${billingAdjustForm.billingFunc.attachChargeItems}" varStatus="status">
								<tr>
										<td width="200px">
												<ls:label text="${aci.itemName}(${aci.buyTypeName})" ref="attachItem_price_${status.index}"></ls:label>
										</td>
										<td>
												<ls:select name="attachItem_chargeTimePoint_${status.index}" id="attachItem_chargeTimePoint_${status.index}" property="billingFunc.attachChargeItems[${status.index}].chargeTimePoint" visible="false" value="00">
														<ls:option value="01" text="预付费"></ls:option>
														<ls:option value="02" text="后付费"></ls:option>
												</ls:select>
										</td>
										<td>
												<ls:text name="attachItem_price_${status.index}" id="attachItem_price_${status.index}" property="billingFunc.attachChargeItems[${status.index}].price" required="true"></ls:text>
										</td>
										<td>
												元/
												<c:out value="${aci.priceUnit.desc}" />
										</td>
								</tr>
						</c:forEach>
				</table>
		</ls:form>
		<c:if test="${operFlag=='add'|| operFlag=='edit'|| operFlag=='adjust' }">
				<table>
						<tr>
								<td>
										<ls:button text="保存" onclick="saveBillingConfigs"></ls:button>
								</td>
						</tr>
				</table>
		</c:if>
		<ls:script>
	var baseUrl = "~/billing/configs";
	<%-- var billingNo = '<c:out value="${requestScope.billingNo}" />'; --%>
	var billingConfigFormObj = <c:out value="${requestScope.billingConfigFormJson}" escapeXml="false" />;
	

    
    function saveBillingConfigs(){
//    if(eftDate.getValue()< nowDateFormat("yyyy-MM-dd hh:mm")){
//      LS.message("info","生效日期必须大于当前时间");
//      return;
//    }
    var url = rootUrl+'billing/configs';
    url += '/post';
    	
    	<%-- 收集需要提交的数据 --%>
    	var formData = billingAdjustForm.getFormData();
    	billingConfigFormObj.autoModelNo = autoModelNo.getValue();
    	billingConfigFormObj.isAdjusted = isAdjusted.getValue();
//    	billingConfigFormObj.eftDate = eftDate.getValue();
    	billingConfigFormObj.billingConfigName = billingConfigName.getValue();
    	//billingConfigFormObj.unionPrice = unionPrice.getValue();
    	var billingFunc = billingConfigFormObj.billingFunc;
    	var mainChargeItem = billingFunc.mainChargeItem;
    	var chargeWay = mainChargeItem.chargeWay;
    	var chargeMode = mainChargeItem.chargeMode;
    	mainChargeItem.chargeTimePoint = billingFunc_mainChargeItem_chargeTimePoint.getValue();

    	if(chargeWay == '0101' || chargeWay == '0103'){
    		var timeChargeItem = mainChargeItem.timeChargeItem;
    		if(chargeMode == '01'){
    			timeChargeItem.price = billingFunc_mainChargeItem_timeChargeItem_price.getValue();
    		}
    		else if(chargeMode == '02' || chargeMode == '03'){
    			var rangeChargeItems = timeChargeItem.rangeChargeItems;
    			if(rangeChargeItems){
	    			for(var i=0;i< rangeChargeItems.length ;i++){
	    				var rci=rangeChargeItems[i]; rci.price=formData[ 'billingFunc_mainChargeItem_timeChargeItem_'+i+'_price'];
	    			}
    			}    		
    		}
    	}

    	if(chargeWay== '0102' || chargeWay== '0103'){
    		var millChargeItem=mainChargeItem.millChargeItem; if(chargeMode== '01'){
    			millChargeItem.price=billingFunc_mainChargeItem_millChargeItem_price.getValue(); }
    		else if(chargeMode== '02' || chargeMode== '03'){
    			var rangeChargeItems=millChargeItem.rangeChargeItems; if(rangeChargeItems){
	    			for(var i=0;i < rangeChargeItems.length;i++){
	    				var rci = rangeChargeItems[i];
	    				rci.price = formData['billingFunc_mainChargeItem_millChargeItem_'+i+'_price'];
	    			}
    			}
    		}
    	}
    	

    	var min = mainChargeItem.min;
    	min.limitType = billingFunc_mainChargeItem_min_limitType.getValue();
    	min.limitQuantity = billingFunc_mainChargeItem_min_limitQuantity.getValue();
    	var max = mainChargeItem.max;
    	max.limitType = billingFunc_mainChargeItem_max_limitType.getValue();
    	max.limitQuantity = billingFunc_mainChargeItem_max_limitQuantity.getValue();       	
    	var depositChargeItems = billingFunc.depositChargeItems;
    	if(depositChargeItems){
	    	for(var i=0;i< billingFunc.depositChargeItems.length;i++){
	    		depositChargeItems[i].chargeTimePoint = formData['depositItem_chargeTimePoint_'+i];
	    		depositChargeItems[i].price = formData['depositItem_price_'+i];
	    	}
    	}
		var attachChargeItems = billingFunc.attachChargeItems;
		if(attachChargeItems){
	    	for(var i=0;i< billingFunc.attachChargeItems.length;i++){
	    		attachChargeItems[i].chargeTimePoint = formData['attachItem_chargeTimePoint_'+i];
	    		attachChargeItems[i].price = formData['attachItem_price_'+i];
	    	}
    	}
    	
      	$.ajax({
      	method:'POST',
      	url:url, 
      	contentType:'application/json',
      	data:JSON.stringify(billingConfigFormObj),
      	dataType:'json'
      	}).done(function(result){
     		
       		LS.message("info","调整定价成功");
       		LS.parent().doSearch();
          LS.window.close();
       	
      });
      
    }

    function nowDateFormat(fmt){
      var d = new Date();
      var o = {
          "M+": d.getMonth() + 1, //月份 
          "d+": d.getDate(), //日 
          "h+": d.getHours(), //小时 
          "m+": d.getMinutes(), //分 
          "s+": d.getSeconds(), //秒 
          "q+": Math.floor((d.getMonth() + 3) / 3), //季度 
          "S": d.getMilliseconds() //毫秒 
      };
      if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
       if (new RegExp("(" + k + ")").test(fmt)) 
       fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    }
    </ls:script>
</ls:body>
</html>