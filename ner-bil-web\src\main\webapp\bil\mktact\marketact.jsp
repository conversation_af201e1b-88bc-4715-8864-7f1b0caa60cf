<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="活动管理" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<table class="tab_search">
			<colgroup>
				<col width="10%"/>
				<col width="25%"/>
				<col width="10%"/>
				<col width="20%"/>
				<col width="10%"/>
				<col width="20%"/>
			</colgroup>
			<tr>
				<td><ls:label text="产品业务类" /></td>
				<td>
					<ls:select name="prodBusiType" property="prodBusiType">
<%--						<ls:options property="orderTypeList" scope="request" text="codeName" value="codeValue" />--%>
						<ls:option text="充电" value="02"></ls:option>
					</ls:select>
				</td>

				<td><ls:label text="活动名称" /></td>
				<td><ls:text name="actName" /></td>

				<td><ls:label text="活动类型" /></td>
				<td>
					<ls:select name="actType" property="actType">
						<ls:options property="actTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
			</tr>
			<tr>
				<td><ls:label text="活动时间" /></td>
				<td>
					<table>
						<tr>
							<td width="40%"><ls:date name="effTime" format="yyyy-MM-dd HH:mm" /></td>
							<td width="20%"><ls:label text="至" /></td>
							<td width="40%"><ls:date name="expTime" format="yyyy-MM-dd HH:mm" /></td>
						</tr>
					</table>
				</td>

				<td><ls:label text="活动状态" /></td>
				<td>
					<ls:select name="actState" property="actState" >
						<ls:options property="actStateList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>

				<td colspan="2">
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="mktactGrid" height="330px;" width="100%"
					showCheckBox="false" singleSelect="true" caption="活动列表" primaryKey="actId">
					<ls:gridToolbar name="mktactGridBar">
						<ls:gridToolbarItem name="addBtn" imageKey="add" onclick="doAdd" text="创建" />
						<ls:gridToolbarItem name="editBtn" imageKey="edit" onclick="doEdit" text="修改" />
						<ls:gridToolbarItem name="closeBtn" imageKey="stop" onclick="doClose" text="关闭" />
						<ls:gridToolbarItem name="deleteBtn" imageKey="delete" onclick="doDel" text="删除" />
						<%--<ls:gridToolbarItem name="editActBtn" imageKey="add" onclick="doEditInfo" text="编辑资讯" />--%>
					</ls:gridToolbar>
					<ls:column caption="" name="actId" hidden="true" />
					<ls:column caption="" name="actState" hidden="true" />
					<ls:column caption="" name="actType" hidden="true" />
					<ls:column caption="活动名称" name="actName" formatFunc="operation"/>
					<ls:column caption="活动类型" name="actTypeName" />
					<ls:column caption="活动标签" name="actLbl" />
					<ls:column caption="所需积分数量" name="needPointNum" hidden="${actType!='08'}" />
					<ls:column caption="生效时间" name="effTime" />
					<ls:column caption="失效时间" name="expTime" />
					<ls:column caption="活动状态" name="actStateName" />
					<ls:column caption="创建时间" name="creTime" />
					<ls:pager pageSize="15" ></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	window.mktactGrid = mktactGrid;
	window.getMarketDetail=getMarketDetail;
	function operation(rowdata){
		const safeActName = escapeHtml(rowdata.actName);
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='getMarketDetail(\"" + rowdata.actId +"\")'>"+safeActName+"</a>";
	}
	function escapeHtml(unsafe) {
		if (typeof unsafe !== 'string') {
		return unsafe;
		}
		const map = {
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;',
			'"': '&quot;',
			"'": '&#039;'
		};
		const reg = /[&<>"']/g;
		return unsafe.replace(reg, match => map[match]);
	}
	function getMarketDetail(actId){
		LS.dialog("~/marketqry/"+actId, "活动详情", 800, 450, true, null);
	}


<%-- 描述:做为菜单项，活动类型不可编辑  创建人:biaoxiangd  创建时间:2017/7/8 2:26 --%>
	window.onload = function(){
		var _actType = actType.getValue();
		if(!LS.isEmpty(_actType)&&_actType!="null"){
			actType.setReadOnly(true);
		}
	}

	mktactGrid.itemclick = function(){
    	var item = mktactGrid.getSelectedItem();
        if(!item){
          	return;
        }
        var actStateValue = item.actState;
        if(actStateValue=="0"){
        	mktactGrid.toolbarItems.editBtn.setEnabled(true);
          	mktactGrid.toolbarItems.closeBtn.setEnabled(true);
          	mktactGrid.toolbarItems.deleteBtn.setEnabled(true);
			<%--mktactGrid.toolbarItems.editActBtn.setEnabled(true);--%>
        }
        else if(actStateValue=="1"){
          	mktactGrid.toolbarItems.editBtn.setEnabled(true);
          	mktactGrid.toolbarItems.closeBtn.setEnabled(true);
          	mktactGrid.toolbarItems.deleteBtn.setEnabled(true);
			<%--mktactGrid.toolbarItems.editActBtn.setEnabled(true);--%>
        }
        else if(actStateValue=="2"){
          	mktactGrid.toolbarItems.editBtn.setEnabled(false);
          	mktactGrid.toolbarItems.closeBtn.setEnabled(true);
          	mktactGrid.toolbarItems.deleteBtn.setEnabled(false);
			<%--mktactGrid.toolbarItems.editActBtn.setEnabled(true);--%>
        }
        else {
          	mktactGrid.toolbarItems.editBtn.setEnabled(false);
          	mktactGrid.toolbarItems.closeBtn.setEnabled(false);
          	mktactGrid.toolbarItems.deleteBtn.setEnabled(false);
			<%--mktactGrid.toolbarItems.editActBtn.setEnabled(false);--%>
        }
    }

	window.doSearch=doSearch;
    doSearch();
	function doSearch(){
		var eff = effTime.getValue();
		var exp = expTime.getValue();
		if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff>exp) {
			LS.message("info", "开始时间不能大于截止时间。");
      		return;
		}
		var params = searchForm.getFormData();
		mktactGrid.query('~/marketacts', params, function(){});
	}
	function doClear(){
		var _actType = actType.getValue();
		searchForm.clear();
		if(actType.isReadOnly()){
		actType.setValue(_actType);
		}
	}

    function doAdd(){
		LS.dialog("~/marketacts/edit?actId=&actType="+actType.getValue(),"创建活动", 800, 535, true, null);
    }
    function doEdit(){
		var item = mktactGrid.getSelectedItem();
    	if(LS.isEmpty(item)){
	   		LS.message("error","请选择一条记录！");
    		return;
	   	}
		LS.dialog("~/marketacts/edit?actId="+item.actId,"修改活动", 800, 535, true, null);
    }
    function doClose(){
		var item = mktactGrid.getSelectedItem();
    	if(LS.isEmpty(item)){
	   		LS.message("error","请选择一条记录！");
    		return;
	   	}
		LS.confirm('该活动关闭后，将无法重新开启，确定关闭该活动吗？',function(result){
			if(result){
				LS.ajax("~/marketacts/close?actId="+item.actId, null, function(e){
					if(e.items[0]=="T"){
						LS.message("info","关闭成功！");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
    }
 	function doDel(){
		var item = mktactGrid.getSelectedItem();
    	if(LS.isEmpty(item)){
	   		LS.message("error","请选择一条记录！");
    		return;
	   	}
		LS.confirm('确定要删除该活动？',function(result){
			if(result){
				LS.ajax("~/marketacts/delete?actId="+item.actId, null, function(e){
					if(e.items[0]=="T"){
						LS.message("info","删除成功！");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
    }
<%-- 描述:编辑资讯按钮  创建人:biaoxiangd  创建时间:2017/6/26 14:37 --%>
    function doEditInfo() {
		var item = mktactGrid.getSelectedItem();
		if(LS.isEmpty(item)){
			LS.message("error","请选择一条记录！");
			return;
		}
		var _actId = item.actId;
		var _actType = item.actType;
		if(LS.isEmpty(_actId)){
			LS.message("error","选中的记录不存在活动！");
			return;
		}
		if(LS.isEmpty(_actType)){
			LS.message("error","选中的记录不存在活动类型！");
			return;
		}

		LS.dialog("~/marketacts/actInfo?actType="+_actType+"&actId="+_actId,"活动资讯发布",850,530,true, null);
    }
		window.onload = function() {
		initGridHeight('searchForm','mktactGrid');
		}
    </ls:script>
</ls:body>
</html>
