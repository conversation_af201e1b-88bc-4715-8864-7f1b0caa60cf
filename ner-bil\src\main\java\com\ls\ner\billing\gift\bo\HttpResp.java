package com.ls.ner.billing.gift.bo;


import org.apache.commons.httpclient.Header;

/**
 * <p>http响应对象</p>
 * <p>Created by qrf on 2016/8/12.</p>
 */
public class HttpResp {

    private int status;
    private Header[] headers;
    private byte[] bodyByte;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Header[] getHeaders() {
        return headers;
    }

    public void setHeaders(Header[] headers) {
        this.headers = headers;
    }

    public byte[] getBodyByte() {
        return bodyByte;
    }

    public void setBodyByte(byte[] bodyByte) {
        this.bodyByte = bodyByte;
    }

    public String getBodyString() {
        return new String(this.bodyByte);
    }

    public boolean isSuccess(){
        return this.status == 200;
    }
}
