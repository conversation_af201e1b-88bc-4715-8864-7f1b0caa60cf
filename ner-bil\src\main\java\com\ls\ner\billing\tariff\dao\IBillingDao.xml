<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.tariff.dao.IBillingDao">
	<resultMap type="com.ls.ner.billing.bo.VehicleBillingBo" id="billingBo">
		<result column="BILLING_CONFIG_NAME" property="billingConfigName" />
		<result column="BILLING_NO" property="billingNo" />
		<result column="MIN_CHARGE_MODE" property="minChargeMode" />
		<result column="MAX_CHARGE_MODE" property="maxChargeMode" />
		<result column="CHARGE_UNIT" property="chargeUnit" />
		<result column="PE_BE" property="peBe" />
		<result column="SUB_BE" property="subBe" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="REMARK" property="remark" />
		<result column="BATCH" property="batch" />
		<result column="RT_NO" property="rtNo" />
		<result column="AUTO_MODEL_NO" property="autoModelNo" />
		<result column="EFFECT_TIME" property="effectTime" />
		<result column="INVALID_TIME" property="invalidTime" />
		<result column="IS_COVER" property="isCover" />
		<result column="PRIORITY" property="priority" />
		<result column="VERSION" property="version" />
		<result column="ORG_CODE" property="orgCode" />
		<result column="STATUS" property="status" />
		<result column="UNIFIED_PRICE" property="unifiedPrice" />
	</resultMap>

	<resultMap type="com.ls.ner.billing.bo.MainChargeConfigBo" id="mainBo">
		<result column="MAIN_NO" property="mainNo" />
		<result column="BILLING_NO" property="billingNo" />
		<result column="PRICE" property="price" />
		<result column="MIN_COST" property="minCost" />
		<result column="MAX_COST" property="maxCost" />
		<result column="CHARG_MODE" property="chargMode" />
		<result column="CHARGE_TYPE" property="chargeType" />
		<result column="CHARGE_UNIT" property="chargeUnit" />
		<result column="CHARGE_NUM" property="chargeNum" />
	</resultMap>

	<resultMap type="com.ls.ner.billing.bo.AttachConfigBo" id="attachBo">
		<result column="BILLING_NO" property="billingNo" />
		<result column="PRICE" property="price" />
		<result column="ITEM_NAME" property="itemName" />
		<result column="ITEM_NO" property="itemNo" />
		<result column="CHARGE_TYPE" property="chargeType" />
		<result column="BUY_IDENTITY" property="buyIdentity" />
		<result column="REMARK" property="remark" />
	</resultMap>

	<resultMap type="com.ls.ner.billing.bo.ItemPeriods" id="periodsBo">
		<result column="MAIN_NO" property="mainNo" />
		<result column="PRICE" property="price" />
		<result column="SN" property="sn" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
	</resultMap>

	<resultMap type="com.ls.ner.billing.bo.StepConfig" id="stepBo">
		<result column="MAIN_NO" property="mainNo" />
		<result column="PRICE" property="price" />
		<result column="SN" property="sn" />
		<result column="START_POINT" property="startPoint" />
		<result column="END_POINT" property="endPoint" />
	</resultMap>

	<select id="getVehicleBilling" resultMap="billingBo" parameterType="java.util.Map">
		SELECT
		b.BILLING_CONFIG_NAME,
		b.BILLING_NO,
		b.MIN_CHARGE_MODE,
		b.MAX_CHARGE_MODE,
		b.CHARGE_UNIT,
		b.PE_BE,
		b.SUB_BE,
		b.CHARGE_MODE,
		b.REMARK,
		a.BATCH,
		a.RT_NO,
		a.AUTO_MODEL_NO,
		a.EFFECT_TIME,
		a.INVALID_TIME,
		a.IS_COVER,
		a.PRIORITY,
		a.VERSION
		FROM
		e_vehicle_price a,
		e_billing_config b
		<where>
			a.BILLING_NO = b.BILLING_NO
			<if test="orgCode != null and orgCode != ''">
				AND a.ORG_CODE =#{orgCode}
			</if>
			<if test="rtNo != null and rtNo != ''">
				AND a.RT_NO =#{rtNo}
			</if>
			<if test="autoModelNo != null and autoModelNo != ''">
				AND a.AUTO_MODEL_NO =#{autoModelNo}
			</if>
			<if test="peBe != null and peBe != ''">
				AND b.PE_BE =#{peBe}
			</if>
			<if test="subBe != null and subBe != ''">
				AND b.SUB_BE =#{subBe}
			</if>
			<if test="effectTime != null and effectTime != ''">
				AND a.EFFECT_TIME &gt;= #{effectTime}
			</if>
			<if test="invalidTime != null and invalidTime != ''">
				AND a.INVALID_TIME &lt;= #{invalidTime}
			</if>
		</where>
	</select>

	<select id="getMainChargeConfig" resultMap="mainBo" parameterType="java.util.Map">
		SELECT
		a.MAIN_NO,
		a.BILLING_NO,
		a.PRICE,
		a.MIN_COST,
		a.MAX_COST,
		a.CHARG_MODE,
		a.CHARGE_TYPE,
		a.CHARGE_UNIT,
		a.CHARGE_NUM
		FROM
		e_main_charge_config a
		<where>
			<if test="billingNo != null and billingNo != ''">
				AND a.BILLING_NO = #{billingNo}
			</if>
		</where>
	</select>

	<select id="getAttachConfig" resultMap="attachBo" parameterType="java.util.Map">
		select
		a.BILLING_NO,
		a.PRICE,
		a.ITEM_NAME,
		a.ITEM_NO,
		a.CHARGE_TYPE,
		a.BUY_IDENTITY,
		a.REMARK
		from e_attach_config a
		<where>
			<if test="billingNo != null and billingNo != ''">
				AND a.BILLING_NO = #{billingNo}
			</if>
		</where>
	</select>

	<select id="getItemPeriods" resultMap="periodsBo" parameterType="java.util.Map">
		select
		a.MAIN_NO,
		a.PRICE,
		a.SN,
		a.BEGIN_TIME,
		a.END_TIME
		from e_tiem_periods_config a
		<where>
			<if test="mainNo != null and mainNo != ''">
				AND a.MAIN_NO = #{mainNo}
			</if>
		</where>
	</select>

  <select id="getStepConfig" resultMap="stepBo" parameterType="java.util.Map">
    select
    a.MAIN_NO,
    a.PRICE,
    a.SN,
    a.START_POINT,
    a.END_POINT
    from E_STEP_CONFIG a
    <where>
      <if test="mainNo != null and mainNo != ''">
        AND a.MAIN_NO = #{mainNo}
      </if>
    </where>
  </select>
</mapper>