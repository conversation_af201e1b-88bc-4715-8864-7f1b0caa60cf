package com.ls.ner.billing.gift.constant;


public enum IntegralEnum {

    /**
     * 通用枚举值
     */
    REGISTRATION("01", "注册"),
    BIND_VEHICLE("02", "绑定车辆"),
    INVITE_NEW_USERS("03", "邀新"),
    CHARGING("04", "充电消费"),
    CHECK_IN("05", "签到"),
    PRODUCT_REDEMPTION("06", "商品兑换"),
    POINTS_EXPIRATION("07", "积分过期"),
    PAID_MEMBERSHIP_UPGRADE_PACKAGE("08", "会员升级礼包");

    private String code;

    private String name;

    IntegralEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IntegralEnum getByCode(String code) {
        for (IntegralEnum value : IntegralEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("SexEnum missing code = " + code);
    }

}
