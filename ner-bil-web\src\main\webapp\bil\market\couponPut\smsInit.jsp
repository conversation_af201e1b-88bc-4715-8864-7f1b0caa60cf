<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="短信通知">
</ls:head>
<ls:body>
	<ls:form name="smsForm" id="smsForm">
		<table align="center" class="tab_search">
			<tr>
				<td><ls:label text="通知方式"  /></td>
				<td><ls:checklist name="noticeType" type="radio" property="noticeType" required="true" columns="2" onchanged="noticeTypeOnchange">
							<ls:checkitems property="noticeTypeList" scope="request" text="text" value="value"></ls:checkitems>
					</ls:checklist></td>
			</tr>
			<tr id="tr1">
				<td><ls:label text="通知内容"  /></td>
				<td><ls:text name="noticeCont" property="noticeCont" type="textarea" required="true" maxlength="256"></ls:text></td>
			</tr>
	        <tr>
	            <td colspan="2">
	                <div class="pull-right">
	                    <ls:button text="确定" onclick="save" name="save"/>
	                    <ls:button text="取消" onclick="close" />
	                </div>
	            </td>
	        </tr>
		</table>
	</ls:form>

	<ls:script>
	<%-- 描述:通知方式改变  创建人:biaoxiangd  创建时间:2017-06-04 15:51 --%>
	noticeTypeOnchange();
	function noticeTypeOnchange(){
		var type = noticeType.getValue();
		if(type == '01'){//无短信
			$('#tr1').hide();
			noticeCont.setValue('');
		}else{
			$('#tr1').show();
		}
	}
	
	<%-- 描述:保存  创建人:biaoxiangd  创建时间:2017-06-04 15:52 --%>
	function save(){
		LS.parent().setPutMsg(smsForm.getFormData());
		LS.window.close();
	}
	
	<%-- 描述:返回  创建人:biaoxiangd  创建时间:2017-06-04 15:52 --%>
    function close(){
      LS.window.close();
    }
    
	</ls:script>

</ls:body>
</html>