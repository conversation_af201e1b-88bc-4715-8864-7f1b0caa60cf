package com.ls.ner.billing.mktact.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ls.ner.ast.api.station.service.IStationRpcService;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.billing.mktact.bo.*;
import com.ls.ner.billing.mktact.condition.MarketActFormCondition;
import com.ls.ner.billing.mktact.dao.IMarketActScopeDao;
import com.ls.ner.billing.mktact.service.IActCondService;
import com.ls.ner.billing.mktact.service.IActInfoService;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.ls.ner.def.api.operators.service.IDefrayOperRpcService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.util.*;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @description 新版活动（目前针对浙江）
 * @create 2019/5/22 15:01
 *
 * @return
 */
@Controller
@RequestMapping("/markertAct")
public class NewMarketActController extends QueryController<MarketActBo> {
    private static final Logger logger = LoggerFactory.getLogger(NewMarketActController.class);

    @Autowired
    private ControllerSupport controllerSupport;

    @Autowired
    private IMarketActScopeDao marketActScopeDao;

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "orgRpcService", serviceTypes = ServiceType.RPC)
    private IOrgRpcService orgRpcService;

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    @ServiceAutowired("preSectionService")
    private IPreSectionService preSectionService;

    @ServiceAutowired(value = "actInfoService")
    private IActInfoService actInfoService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="attachService")
    private IAttachRpcService attachService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defrayOperRpcService")
    private IDefrayOperRpcService defrayOperRpcService;

    @ServiceAutowired(value = "actCondService", serviceTypes = ServiceType.LOCAL)
    private IActCondService actCondService;

    @ServiceAutowired(value = "stationRpcService", serviceTypes = ServiceType.RPC)
    private IStationRpcService stationRpcService;

    @ServiceAutowired(value = "areaRpcService", serviceTypes = ServiceType.RPC)
    private IAreaRpcService areaRpcService;

    @ServiceAutowired("chargeSerItemService")
    private IChargeSerItemService chargeSerItemService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC, value="accountService")
    private IAccountService accountService;
    /**
     * <AUTHOR>
     * @description 活动列表
     * @create 2019/5/22 15:24
     *
     * @return
     */
    @RequestMapping(value = "/init/{actType}", method = RequestMethod.GET)
    public String init(Model m, @PathVariable("actType") String actType) {
        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE, ActStatus.CODE_TYPE, "orderType");
        MarketActBo bo = new MarketActBo();
        bo.setActType(actType);
        m.addAttribute("actType",actType);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        Map<String,Object> paramMap = new HashMap<>();
        List<String> orgList = new ArrayList<>();
        /*try {
            orgList = orgRpcService.getSubOrgCodes();
        }catch (Exception e){
            e.printStackTrace();
        }
        paramMap.put("orgCodeList",orgList);*/
        //paramMap.put("statusCode",01);
        List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
        m.addAttribute("buildList",operInfoList);
        m.addAttribute("searchForm", bo);
        if("07".equals(actType)){
            return "/bil/mktact/prepaidgift/prepaidGift";
        }else{
            //获取当前账号
            String accountName = authentication.getCurrentAccount().getAccountName();
            //判断当前账号是否是系统账号
            if (!accountService.isAdmin(accountName)) {
                //不是系统账号，获取当前系统账号的管理单位
                OrgBo orgBoT = orgService.getOrgByAccountName(accountName);
                if (orgBoT != null) {
                    String orgCode = orgBoT.getOrgCode();
                    if (StringUtil.isNotBlank(orgCode)) {
                        //通过管理单位code查找运营商id
                        Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                        if (buildMap != null) {
                            String operId = StringUtil.nullToString(buildMap.get("operId"));
                            bo.setBuildId(operId);
                        }
                    }
                }
            }else{
                m.addAttribute("currentUser","SYSADMIN");
            }
            return "/bil/mktact/timeLimit/marketact";
        }
    }



    /**
     * 获取营销活动列表
     *
     * @param params
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts")
    public @ItemResponseBody
    QueryResultObject queryMarketActives(@QueryRequestParam("params") RequestCondition params) {

        MarketActBo condition = this.rCondition2QCondition(params);
        List<Map<String,Object>> list = new ArrayList<>();
        try {
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if (orgBo != null) {
                condition.setOrgCodes(OrgCodeUtil.getSubOrg(orgBo.getOrgCode()));
            }
            Map<String,Object> map = marketActService.qryMarketActivesNew(condition);
            list = (List<Map<String,Object>>) map.get("dataList");
            condition.setNums(Integer.valueOf(StringUtil.trimToEmpty(map.get("num"))));
        }catch (Exception e){
            e.printStackTrace();
        }


        return RestUtils.wrappQueryResult(list, condition.getNums());
    }

    /**
     * @param
     * @description 活动编辑
     * <AUTHOR>
     * @create 2019/5/30 23:58
     */
    @RequestMapping(value = "/edit", method = RequestMethod.GET)
    public String editMarketactForm(Model m, @RequestParam("actId") String actId, @RequestParam("actType") String  actType)throws Exception {
        logger.info("actId----------------"+actId);
        Map<String,Object> paramMap = new HashMap<>();
        //初始化页面
        MarketActBo marketActBo = new MarketActBo();
        if (!StringUtils.nullOrBlank(actId)) {
            marketActBo = marketActService.queryByPrimaryKey(Long.valueOf(actId));
            if(StringUtils.nullOrBlank(marketActBo.getActSubType())){
                marketActBo.setActSubType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
            }
        }
        List<String> orgList = new ArrayList<>();
        AccountBo currentAccount = authentication.getCurrentAccount();
        OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
        if (orgBo != null) {
            orgList = OrgCodeUtil.getSubOrg(orgBo.getOrgCode());
        }
        paramMap.put("orgCodeList",orgList);
//        paramMap.put("statusCode","01");
        logger.info("编辑页面运营商--------orgList"+orgList);
        List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);

        MarketActFormCondition marketActFormCondition = new MarketActFormCondition();
        BeanUtil.copyProperties(marketActFormCondition,marketActBo);
        marketActFormCondition.setBuildType("0");
        marketActFormCondition.setCity("0");
        marketActFormCondition.setStationId("0");
        String cityCodes="";
        String stationIds="";
        //查找试用范围
        if (StringUtil.isNotBlank(actId) && "02".equals(actType)){
            paramMap.put("actId",actId);
            List<MarketActScopeBo> marketActScopeBoList = marketActScopeDao.queryScopeList(paramMap);
            for (MarketActScopeBo scopeBo : marketActScopeBoList){
                if ("1".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("0");
                    marketActFormCondition.setCity("0");
                    marketActFormCondition.setStationId("0");
                }
                if ("2".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("0");
                    marketActFormCondition.setStationId("0");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                }
                if ("3".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("1");
                    marketActFormCondition.setStationId("0");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                    cityCodes += scopeBo.getCity()+",";
                }
                if ("4".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("1");
                    marketActFormCondition.setStationId("1");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                    cityCodes += scopeBo.getCity()+",";
                    stationIds += scopeBo.getStationId()+",";
                }
            }
        }

        marketActFormCondition.setCityCodes(cityCodes);
        marketActFormCondition.setStationIds(stationIds);
        m.addAttribute("marketactForm", marketActFormCondition);
        m.addAttribute("buildList",operInfoList);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        //初始化优惠对象
        if ("02".equals(actType)){
            MarketTypeBo marketTypeBo = new MarketTypeBo();
            if (StringUtil.isNotBlank(actId)){
                marketTypeBo.setActId(actId);
                MarketTypeBo typeBo = actCondService.queryActCond(marketTypeBo);
                if (typeBo != null && StringUtil.isNotBlank(typeBo.getActDctId())) {
                    marketTypeBo = typeBo;
                }
            }
            marketTypeBo.setDctType("0201");
            // 优惠内容 - 优惠对象
            ChargeSerItemQueryCondition chargeSerItemQueryCondition = new ChargeSerItemQueryCondition();
            chargeSerItemQueryCondition.setpBe("02");//业务大类：01租车 02 充电
            chargeSerItemQueryCondition.setItemType("01");//项目类型：01服务02押金
            chargeSerItemQueryCondition.setItemStatus("1");
            List<ChargeSerItemBo> list =chargeSerItemService.queryChargeSerItems(chargeSerItemQueryCondition);
            /*ChargeSerItemBo chargeSerItemBo = new ChargeSerItemBo();
            chargeSerItemBo.setItemNo(BizConstants.PriceCode.CHARGE_PRICE_CODE);
            chargeSerItemBo.setItemName("电费");
            list.add(chargeSerItemBo);*/
            m.addAttribute("actDctProdIdList", list);
            m.addAttribute("limDisCountForm", marketTypeBo);
        }


        // 初始化资讯内容
        List<Map<String, String>> infoTypeList = new ArrayList<Map<String, String>>();
        Map<String, String> actTypeMap = new HashMap<String, String>();
        actTypeMap.put("text", "图文");
        actTypeMap.put("value", "1");
        infoTypeList.add(actTypeMap);
        Map<String, String> typeMap = new HashMap<String, String>();
        typeMap.put("text", "外部链接");
        typeMap.put("value", "2");
        infoTypeList.add(typeMap);
        m.addAttribute("infoTypeList", infoTypeList);
        ActInfoBo actInfoBo = new ActInfoBo();
        actInfoBo.setActId(actId);
        actInfoBo.setActType(actType);
        actInfoBo.setInfoType("1");
        ActInfoBo InfoBo = actInfoService.queryActInfo(actInfoBo);
        logger.debug(">>>>>>>>>>资讯发布controller出参:{}",IJsonUtil.obj2Json(InfoBo));
        if (InfoBo != null) {
            InfoBo.setActId(actId);
            InfoBo.setActType(actType);
            if (StringUtil.isBlank(InfoBo.getCoberPic())) {
                InfoBo.setPicFlag("1");
            } else {
                InfoBo.setPicFlag("0");
            }
        } else {
            InfoBo = actInfoBo;
        }
        m.addAttribute("actinfoForm", InfoBo);
        if("07".equals(actType)){
            //初始化段落明细
            PreSectionBo model = new PreSectionBo();
            PreSectionBo condition = new PreSectionBo();
            if (StringUtil.isNotEmpty(actId)){
                condition.setActId(Long.parseLong(actId));
                condition.setPresentBalType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
                List<PreSectionBo> list2 = preSectionService.qryPreSectionsNew(condition);
                if(CollectionUtils.isNotEmpty(list2)){
                    model = list2.get(0);
                    model.setRelease("false");
                }else{
                    model.setActId(Long.parseLong(actId));
                    model.setPresentBalType(actType);
                    model.setRelease("false");
                    model.setSectionRelaType("2");//1依赖 2互斥 3并存
                    model.setSectionType("5");//5单向单段 6循环
                }
            }
            model.setBeginCalcObject(DateTools.dateToStr(new Date(), DateTools.YMD));
            m.addAttribute("preSectionForm", model);
            //普通下拉框
            controllerSupport.addStCodes(m,
                    BillConstants.CALC_PRECISION,
                    BillConstants.PresentCycleType.CODE_TYPE,
                    BillConstants.CYCLE_UNIT,
                    BillConstants.TIME_UNIT);
            //单选按钮组
            controllerSupport.addCheckListMapsCodes(m,
                    BillConstants.SECTION_RELA_TYPE,
                    BillConstants.SECTION_TYPE,
                    BillConstants.DATE_TYPE);
            return "/bil/mktact/prepaidgift/prepaidGiftEdit";
        }else{

            //获取当前账号
            String accountName = authentication.getCurrentAccount().getAccountName();
            //判断当前账号是否是系统账号
            if (!accountService.isAdmin(accountName)) {
                //不是系统账号，获取当前系统账号的管理单位
                OrgBo orgBoT = orgService.getOrgByAccountName(accountName);
                if (orgBoT != null) {
                    String orgCode = orgBoT.getOrgCode();
                    if (StringUtil.isNotBlank(orgCode)) {
                        //通过管理单位code查找运营商id
                        Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                        if (buildMap != null) {
                            String operId = StringUtil.nullToString(buildMap.get("operId"));
                            marketActFormCondition.setBuildId(operId);
                            marketActFormCondition.setBuildType("1");
                        }
                    }
                }
            }else{
                m.addAttribute("currentUser","SYSADMIN");
            }


            return "/bil/mktact/timeLimit/marketactEdit";
        }
    }

    @RequestMapping(value = "/getCityByBuild")
    public @ItemResponseBody
    QueryResultObject getCityByBuild(@RequestParam(value = "buildNo")String buildNo) {
        List<Map> resultList = stationRpcService.queryStationCityByBuild(buildNo);
        return RestUtils.wrappQueryResult(resultList);
    }


    @RequestMapping(value = "/getStationList")
    public @ItemResponseBody
    QueryResultObject getStationList(@QueryRequestParam("params") RequestCondition params) {
        List<Map<String,Object>> resultList = new ArrayList<>();
        Map<String,Object> inMap = this.filterToMap(params);
        logger.info("inMap================="+inMap);
        if (inMap.containsKey("cityCodes")){
            String cityCodes = StringUtil.trimToEmpty(inMap.get("cityCodes"));
            if (StringUtil.isNotBlank(cityCodes)){
                List<String> cityList = new ArrayList<>();
                if (cityCodes.contains(",")){
                    String[] cityStr = cityCodes.split(",");
                    cityList = Arrays.asList(cityStr);
                }
                Map<String,Object> stationMap = new HashMap<>();
                stationMap.put("buildId",inMap.get("buildId"));
                stationMap.put("cityList",cityList);
                resultList = stationRpcService.queryStationByBuildAndCity(stationMap);
                if (resultList!=null && resultList.size()>0){
                    for (Map map : resultList){
                        if ("1".equals(String.valueOf(map.get("stationType")))){
                            map.put("stationTypeName","公共");
                        }
                        if ("20".equals(String.valueOf(map.get("stationType")))){
                            map.put("stationTypeName","单位内部专用");
                        }
                        if ("50".equals(String.valueOf(map.get("stationType")))){
                            map.put("stationTypeName","个人");
                        }
                    }
                }
            }

        }
        return RestUtils.wrappQueryResult(resultList, resultList.size());
    }
    /**
     * 关闭/删除活动
     *
     * @param oprType
     * @param actId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/closeDel/{oprType}")
    public @ItemResponseBody
    QueryResultObject operateMarketAct(
            @PathVariable String oprType, @RequestParam Long actId) {
        String mark = "T";
        try {
            marketActService.operateMarketActNew(oprType, actId);
        } catch (Exception e) {
            logger.error("", e);
            mark = "F";
        }
        return RestUtils.wrappQueryResult(mark);
    }

    /**
     * 保存 -- 营销活动
     *
     * @param marketActFormCondition
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/saving", method = RequestMethod.POST)
    public @ItemResponseBody QueryResultObject saveMarketAct(MarketActFormCondition marketActFormCondition, MultipartHttpServletRequest request) {
        String mark = "T";
        String url = "";
        try {
            logger.info(">>>>>MarketActFormCondition:================="+ marketActFormCondition);
            String youhuiStr ="";
            String presentSectionDets="";
            if("02".equals(marketActFormCondition.getActType())){
                youhuiStr=String.valueOf(marketActFormCondition.getYouhui());
            }else if("07".equals(marketActFormCondition.getActType())){
                presentSectionDets=String.valueOf(marketActFormCondition.getPresentSection());
            }

            String infoStr =   String.valueOf(marketActFormCondition.getInfo());
            logger.info("infoStr================="+infoStr);
            MarketActBo marketActBo = new MarketActBo();
            if (StringUtil.isNotBlank(marketActFormCondition.getActId())){
                marketActBo.setActId(Long.valueOf(marketActFormCondition.getActId()));
            }
            logger.info("controller----------"+JsonUtil.obj2Json(marketActFormCondition));
            marketActBo.setActName(marketActFormCondition.getActName());
            marketActBo.setIsImmediate(marketActFormCondition.getIsImmediate());
            marketActBo.setEffTime(marketActFormCondition.getEffTime());
            marketActBo.setExpTime(marketActFormCondition.getExpTime());
            marketActBo.setCustType(marketActFormCondition.getCustType());
            marketActBo.setRelease(marketActFormCondition.getRelease());
            marketActBo.setActMarks(marketActFormCondition.getActMarks());
            marketActBo.setActChannel("01,02,03,04,05,07,08,09");
            marketActBo.setActType(marketActFormCondition.getActType());
            marketActBo.setAllowWithdraw(marketActFormCondition.getAllowWithdraw());
            if ("07".equals(marketActBo.getActType())){
                marketActBo.setActSubType(marketActFormCondition.getActSubType());
                marketActFormCondition.setBuildId("0");
                marketActFormCondition.setBuildType("0");
                marketActFormCondition.setCity("0");
                marketActFormCondition.setStationId("0");
            }
            //检验活动时间是否交叉
            if ("true".equals(marketActBo.getRelease())){
                JSONObject crossJson = marketActService.checkActIsCross(marketActFormCondition);
                logger.info("查询活动是否交叉结果----------"+crossJson);
                if (crossJson!=null && crossJson.getInteger("resCode")==-1){
                    mark = "与"+crossJson.getString("oldActName")+"活动存在时间冲突，无法新建！";
                    return RestUtils.wrappQueryResult(mark);
                }
            }

            //新增时，设置创建人员为当前登陆人员，单位为当前登陆人员所属单位
            if (marketActBo.getActId() == 0){
                AccountBo currentAccount = authentication.getCurrentAccount();
                marketActBo.setCreEmp(currentAccount.getAccountId());
                OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
                if (orgBo != null) {
                    logger.info("保存管理单位----------"+orgBo.getOrgCode());
                    marketActBo.setOrgCode(orgBo.getOrgCode());
                }
            }
            //充电
            marketActBo.setProdBusiType("02");
            long actId = marketActService.saveMarketActNew(marketActBo);
            logger.info("actId==================="+actId);
            //保存scope
            marketActService.saveActScope(marketActFormCondition,String.valueOf(actId),marketActBo.getActType());

            try {
                if (StringUtil.isNotBlank(youhuiStr)){
                    MarketTypeBo marketTypeBo = new MarketTypeBo();
                    marketTypeBo.setActId(String.valueOf(actId));
                    MarketTypeBo typeBo = actCondService.queryActCond(marketTypeBo);
                    Map<String,Object> youhuiMap =(Map)JSON.parse(youhuiStr);
                    logger.info("优惠 map======================="+youhuiMap);
                    logger.info("ActCondDetId======================="+typeBo.getActCondDetId());
                    youhuiMap.put("actId",actId);
                    youhuiMap.put("actType","02");
                    youhuiMap.put("dctType","0201");
                    youhuiMap.put("actCondId",typeBo.getActCondId());
                    youhuiMap.put("actCondDetId",typeBo.getActCondDetId());
                    if (youhuiMap != null && youhuiMap.size() > 0) {
                        int flag = actCondService.saveAllNew(youhuiMap);
                        if (flag <= 0) {
                            mark = "保存优惠内容失败";
                        }
                    }
                }
                if (StringUtil.isNotBlank(presentSectionDets)){
                    Map<String,Object> presentSectionMap =(Map)JSON.parse(presentSectionDets);
                    logger.info("预存赠送 map======================="+presentSectionMap);
                    if (presentSectionMap != null && presentSectionMap.size() > 0) {
                        //保存段落明细
                        PreSectionBo model = new PreSectionBo();
                        model.setActId(actId);
                        model.setPresentSectionId(Long.parseLong(StringUtil.nullToString(presentSectionMap.get("presentSectionId"))));
                        model.setPresentRuleDesc(StringUtil.nullToString(presentSectionMap.get("presentRuleDesc")));
                        model.setPresentSectionDets(StringUtil.nullToString(presentSectionMap.get("presentSectionDets")));
                        model.setMaxValue(Long.parseLong(StringUtil.nullToString(presentSectionMap.get("maxValue"))));
                        model.setEndTimeUnit(StringUtil.nullToString(presentSectionMap.get("endTimeUnit")));
                        model.setEndTimeDuration(Integer.parseInt(StringUtil.nullToString(presentSectionMap.get("endTimeDuration"))));
                        model.setBeginCalcObject(StringUtil.nullToString(presentSectionMap.get("beginCalcObject")));

                        String endCalcObject = StringUtil.nullToString(presentSectionMap.get("endCalcObject"));
                        endCalcObject = StringUtil.isBlank(endCalcObject)?endCalcObject: (endCalcObject.length() >10?
                                endCalcObject:endCalcObject+" 23:59:59");
                        model.setEndCalcObject(endCalcObject);
                        model.setPresentCycleType("1");
                        if("1".equals(presentSectionMap.get("beginTimeType"))){
                            model.setEndTimeType("1");
                        }else if("2".equals(presentSectionMap.get("beginTimeType"))){
                            model.setBeginTimeUnit(StringUtil.nullToString(presentSectionMap.get("endTimeUnit")));
                            model.setBeginTimeDuration(0);
                            model.setEndTimeType("2");
                        }
                        model.setBeginTimeType(presentSectionMap.get("beginTimeType").toString());
                        model.setPresentBalType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
                        model.setSectionRelaType("2");//1依赖 2互斥 3并存
                        model.setSectionType("5");//5单向单段 6循环
                        logger.info("开始保存段落=======================");
                        long flag = preSectionService.savePreSection(model);
                        if (flag <= 0) {
                            mark = "保存预存赠送失败";
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //资讯
            if (StringUtil.isNotBlank(infoStr)){
                logger.info("infoStr================="+infoStr);
                Map<String,Object> infoMap =(Map)JSON.parse(infoStr);
                // 去除content的特殊字符
                String content = ESAPI.encoder().encodeForHTML(MapUtils.getValue(infoMap,"content" ));

                ActInfoBo actInfoBo = new ActInfoBo();
                actInfoBo.setActId(String.valueOf(actId));
                actInfoBo.setActType(marketActFormCondition.getActType());
                actInfoBo.setActInfoId(StringUtil.trimToEmpty(infoMap.get("actInfoId")));
                actInfoBo.setAttachId(StringUtil.trimToEmpty(infoMap.get("attachId")));
                actInfoBo.setContentUrl(StringUtil.trimToEmpty(infoMap.get("contentUrl")));
                actInfoBo.setPicFlag(StringUtil.trimToEmpty(infoMap.get("picFlag")));
                actInfoBo.setInfoType(StringUtil.trimToEmpty(infoMap.get("infoType")));
                actInfoBo.setContent(content);
                actInfoBo.setLinkUrl(content);
                Map<String, Object> retMap = new HashMap<String, Object>();
                int flag = 0;
                try {
                    if (StringUtil.isBlank(actInfoBo.getActInfoId())) {
                        flag = actInfoService.saveActInfoNew(actInfoBo, request);
                    } else {
                        flag = actInfoService.updateActInfoNew(actInfoBo, request);
                    }
                    if (flag <= 0) {
                        mark = "活动资讯发布失败";
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    mark = e.getMessage();
                }
            }

        } catch (Exception e) {
            logger.error("", e);
            mark = "F";
        }

        return RestUtils.wrappQueryResult(mark);
    }


    protected MarketActBo getBean(Map<String, Object> dataParams) {
        MarketActBo model = new MarketActBo();
        controllerSupport.populateSubmitParams(dataParams, model);
        return model;
    }


    /**
     * 活动详情--页面初始化
     *
     * @param m
     * @param actId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/detail/{actId}/{actType}")
    public String marketDetail(Model m, @PathVariable String actId ,@PathVariable String actType) {
        logger.info("actId----------------"+actId);
        Map<String,Object> paramMap = new HashMap<>();
        //初始化页面
        MarketActBo marketActBo = new MarketActBo();
        if (!StringUtils.nullOrBlank(actId)) {
            marketActBo = marketActService.queryByPrimaryKey(Long.valueOf(actId));
            if(StringUtils.nullOrBlank(marketActBo.getActSubType())){
                marketActBo.setActSubType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
            }
        }

        MarketActFormCondition marketActFormCondition = new MarketActFormCondition();
        BeanUtil.copyProperties(marketActFormCondition,marketActBo);
        marketActFormCondition.setBuildType("0");
        marketActFormCondition.setCity("0");
        marketActFormCondition.setStationId("0");
        if ("1".equals(marketActFormCondition.getIsImmediate()) && StringUtil.isEmpty(marketActFormCondition.getEffTime())){
            marketActFormCondition.setEffTime("立即生效");
        }
        String buildId = "";
        String cityCodes="";
        String stationIds="";
        List<Map> cityList = new ArrayList<>();
        int stationNum = 0;
        //查找试用范围
        if (StringUtil.isNotBlank(actId)){
            paramMap.put("actId",actId);
            List<MarketActScopeBo> marketActScopeBoList = marketActScopeDao.queryScopeList(paramMap);
            for (MarketActScopeBo scopeBo : marketActScopeBoList){
                Map<String,Object> paramsMap = new HashMap<>();
                if ("1".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("0");
                    marketActFormCondition.setCity("0");
                    marketActFormCondition.setStationId("0");
                }
                if ("2".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("0");
                    marketActFormCondition.setStationId("0");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                    buildId = scopeBo.getBuildId();
                }
                if ("3".equals(scopeBo.getScopeType())){
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("1");
                    marketActFormCondition.setStationId("0");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                    buildId = scopeBo.getBuildId();
                    cityCodes += scopeBo.getCity()+",";
                }
                if ("4".equals(scopeBo.getScopeType())){
                    stationNum++;
                    marketActFormCondition.setBuildType("1");
                    marketActFormCondition.setCity("1");
                    marketActFormCondition.setStationId("1");
                    marketActFormCondition.setBuildId(scopeBo.getBuildId());
                    buildId = scopeBo.getBuildId();
                    cityCodes += scopeBo.getCity()+",";
                    stationIds += scopeBo.getStationId()+",";
                }
            }
            m.addAttribute("stationNum",stationNum);
            //初始化运营商
            paramMap.put("operNo",buildId);
            List<Map<String,Object>> buildList = defrayOperRpcService.qryOperInfoList(paramMap);
            if (CollectionUtils.isNotEmpty(buildList)){
                marketActFormCondition.setBuildName(StringUtil.trimToEmpty(buildList.get(0).get("operName")));
            }

            //初始化城市
            logger.info("活动详情cityCodes-----------"+cityCodes);
            if (cityCodes.length()>1){
                AreaCondition cBo = new AreaCondition();
                cBo.setAreaCode(cityCodes.substring(0,cityCodes.length()-1));
                cityList = areaRpcService.queryCityByCodes(cBo);
            }
        }

        marketActFormCondition.setCityCodes(cityCodes);
        marketActFormCondition.setStationIds(stationIds);
        List<Map<String,Object>> resulCitytList = new ArrayList<>();
        for (Map<String, Object> map : cityList) {
            Map<String, Object> reMap = new HashMap<>();
            reMap.put("text", StringUtil.nullToString(map.get("AREA_NAME")));
            reMap.put("value", StringUtil.nullToString(map.get("AREA_CODE")));
            resulCitytList.add(reMap);
        }

        logger.info("resulCitytList-------------"+resulCitytList);
        m.addAttribute("cityList",resulCitytList);
        m.addAttribute("marketactForm", marketActFormCondition);

        //初始化优惠对象
        MarketTypeBo marketTypeBo = new MarketTypeBo();
        m.addAttribute("limDisCountForm", marketTypeBo);

        // 初始化资讯内容
        ActInfoBo actInfoBo = new ActInfoBo();
        actInfoBo.setActId(actId);
        actInfoBo.setActType(actType);
        ActInfoBo InfoBo = actInfoService.queryActInfo(actInfoBo);
        String pubPath = PublicConstants.ApplicationPath.getPubPath();
        String infoUrl ="";
        String relaId = "";
        String actInfoId = "";
        if (InfoBo != null) {
            relaId = InfoBo.getRelaId();
            actInfoId = InfoBo.getActInfoId();
            if("1".equals(InfoBo.getInfoType())){//图文
                infoUrl = pubPath + "/api/v0.1/attachs/" + InfoBo.getContentUrl();
            }else{//外部链接
                infoUrl = InfoBo.getLinkUrl();
            }
        } else {
            InfoBo = actInfoBo;
        }
        logger.info("infoObj---------"+JsonUtil.obj2Json(InfoBo));
        m.addAttribute("infoUrl", infoUrl);
        m.addAttribute("relaId", relaId);
        m.addAttribute("actInfoId", actInfoId);
        m.addAttribute("pubPath", pubPath);
        if("07".equals(actType)){
            //初始化段落明细
            PreSectionBo model = new PreSectionBo();
            PreSectionBo condition = new PreSectionBo();
            if (StringUtil.isNotEmpty(actId)){
                condition.setActId(Long.parseLong(actId));
                condition.setPresentBalType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
                List<PreSectionBo> list = preSectionService.qryPreSectionsNew(condition);
                if(CollectionUtils.isNotEmpty(list)){
                    model = list.get(0);
                    model.setRelease("false");
                }else{
                    model.setActId(Long.parseLong(actId));
                    model.setPresentBalType(actType);
                    model.setRelease("false");
                    model.setSectionRelaType("2");//1依赖 2互斥 3并存
                    model.setSectionType("5");//5单向单段 6循环
                }
            }
            m.addAttribute("preSectionForm", model);
            //普通下拉框
            controllerSupport.addStCodes(m,
                    BillConstants.CALC_PRECISION,
                    BillConstants.PresentCycleType.CODE_TYPE,
                    BillConstants.CYCLE_UNIT,
                    BillConstants.TIME_UNIT);
            //单选按钮组
            controllerSupport.addCheckListMapsCodes(m,
                    BillConstants.SECTION_RELA_TYPE,
                    BillConstants.SECTION_TYPE,
                    BillConstants.DATE_TYPE);
            return "/bil/mktact/prepaidgift/prepaidGiftDetail";
        }else{
            return "/bil/mktact/timeLimit/marketactDetail";
        }
    }

    @RequestMapping(value = "/getActStationList")
    public @ItemResponseBody
    QueryResultObject getActStationList(@QueryRequestParam("params") RequestCondition params) {
        Map<String,Object> inMap = this.filterToMap(params);
        logger.info("inMap================="+inMap);
        String stationIds = StringUtil.trimToEmpty(inMap.get("stationIds"));
        List<String> stationList = new ArrayList<>();
        if (StringUtil.isNotBlank(stationIds)){
            if (stationIds.contains(",")){
                String[] stationArray = stationIds.split(",");
                stationList = Arrays.asList(stationArray);
            }else {
                stationList.add(stationIds);
            }
        }
        inMap.put("stationList",stationList);
        List<Map<String,Object>> resultList = stationRpcService.qryStationByStationId(inMap);
        if (resultList!=null && resultList.size()>0){
            for (Map<String,Object> map : resultList){
                if ("1".equals(String.valueOf(map.get("stationType")))){
                    map.put("stationTypeName","公共");
                }
                if ("20".equals(String.valueOf(map.get("stationType")))){
                    map.put("stationTypeName","单位内部专用");
                }
                if ("50".equals(String.valueOf(map.get("stationType")))){
                    map.put("stationTypeName","个人");
                }
            }
        }
        return RestUtils.wrappQueryResult(resultList, resultList.size());
    }
    @Override
    protected MarketActBo initCondition() {
        return new MarketActBo();
    }
}
