package com.ls.ner.billing.charge.bo;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @dateTime 2016-03-28
 * @description 充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
 */
public class ChargeBillingRltPeriodsBo implements Serializable {
	private static final long serialVersionUID = 1L;
	private String systemId;//主键
	private String appNo;//订单编号
	private String chcNo;//充电计费编号
	private String sn;//排序号
	private String beginTime;//开始时段 00:00
	private String endTime;//结束时段 00:00
	private String price;//计费单价
	private String pq;//时段电量
	private String amt;//时段金额
	private String timeFlag;//时段标识：1：尖；2：峰；3：平；4：谷
	private String relBeginTime;//实际开始时段 00:00
	private String relEndTime;//实际结束时段 00:00
	private String itemNo;//费用项编号

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getRelBeginTime() {
		return relBeginTime;
	}

	public void setRelBeginTime(String relBeginTime) {
		this.relBeginTime = relBeginTime;
	}

	public String getRelEndTime() {
		return relEndTime;
	}

	public void setRelEndTime(String relEndTime) {
		this.relEndTime = relEndTime;
	}

	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getAppNo() {
		return appNo;
	}
	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getPq() {
		return pq;
	}
	public void setPq(String pq) {
		this.pq = pq;
	}
	public String getAmt() {
		return amt;
	}
	public void setAmt(String amt) {
		this.amt = amt;
	}
	public String getTimeFlag() {
		return timeFlag;
	}
	public void setTimeFlag(String timeFlag) {
		this.timeFlag = timeFlag;
	}

}