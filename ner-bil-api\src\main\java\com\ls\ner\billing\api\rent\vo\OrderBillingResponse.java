package com.ls.ner.billing.api.rent.vo;

import java.util.List;

import com.ls.ner.billing.api.rent.model.OrderBillingRelaResult;
import com.ls.ner.billing.api.rent.model.AppendChargeItem;
import com.ls.ner.billing.api.rent.model.DepositChargeItem;
import com.ls.ner.billing.api.rent.model.MainChargeItem;

/**
 * app api 查询订单定价响应信息
 * <AUTHOR>
 *
 */
public class OrderBillingResponse extends OrderBillingRelaResult{
	/**
	 * 粗略的定价信息
	 */
//	private String price;
////	private PriceUnit priceUnit;
//	private String priceUnitName;
	private String priceUnitDesc;
	/**
	 * 定价描述
	 */
	private String remark;
	
	private MainChargeItem mainChargeItem;
	
	private List<DepositChargeItem> depositChargeItems;
	
	private List<AppendChargeItem> attachChargeItems;
	
	/**
	 * 里程数 按米记录
	 */
	private long meters;
	/**
	 * 使用时长 按分钟记录
	 */
	private long minutes;

//	public String getPrice() {
//		return price;
//	}
//
//	public void setPrice(String price) {
//		this.price = price;
//	}


//	public PriceUnit getPriceUnit() {
//		return priceUnit;
//	}
//
//	public void setPriceUnit(PriceUnit priceUnit) {
//		this.priceUnit = priceUnit;
//	}
	
	

	public List<DepositChargeItem> getDepositChargeItems() {
		return depositChargeItems;
	}

//	public String getPriceUnitName() {
//		return priceUnitName;
//	}
//
//	public void setPriceUnitName(String priceUnitName) {
//		this.priceUnitName = priceUnitName;
//	}

	public void setDepositChargeItems(List<DepositChargeItem> depositChargeItems) {
		this.depositChargeItems = depositChargeItems;
	}

	public List<AppendChargeItem> getAttachChargeItems() {
		return attachChargeItems;
	}

	public void setAttachChargeItems(List<AppendChargeItem> attachChargeItems) {
		this.attachChargeItems = attachChargeItems;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getPriceUnitDesc() {
		return priceUnitDesc;
	}

	public void setPriceUnitDesc(String priceUnitDesc) {
		this.priceUnitDesc = priceUnitDesc;
	}

	public MainChargeItem getMainChargeItem() {
		return mainChargeItem;
	}

	public void setMainChargeItem(MainChargeItem mainChargeItem) {
		this.mainChargeItem = mainChargeItem;
	}

	public long getMeters() {
		return meters;
	}

	public void setMeters(long meters) {
		this.meters = meters;
	}

	public long getMinutes() {
		return minutes;
	}

	public void setMinutes(long minutes) {
		this.minutes = minutes;
	}
	
	
}
