package com.ls.ner.billing.xpcharge.controller;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.constants.AstConstants;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.service.IChargeBillRpcService;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.billing.xpcharge.service.IXpPileSendService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.util.*;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

/**
 * 充电桩电价下发
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/pile-bill-send")
public class XpPileBillSendController extends QueryController<ChcbillSendQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(XpPileBillSendController.class);

	@ServiceAutowired("xpPileSendService")
	private IXpPileSendService xpPileSendService;

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired
	Authentication authentication;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="areaRpcService")
	private IAreaRpcService areaRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "chargeBillRpcService")
	private IChargeBillRpcService chargeBillRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
	private ISysParamService sysParamService;

	@Override
	protected ChcbillSendQueryCondition initCondition() {
		return new ChcbillSendQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-4-02
	 * @description 初始化充电桩电价下发
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.CHC_BILL_SEND_STATUS);
		ChcbillSendQueryCondition condition = new ChcbillSendQueryCondition();
		AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
		if (orgBo != null) {
			condition.setOrgCode(orgBo.getOrgCode());
			condition.setOrgCodeName(orgBo.getOrgShortName());
		}

		//计费下拉列表
		Map inMap =new HashMap();
		inMap.put("chcStatus", "1");
		inMap.put("endEftDate", DateTools.dateToStr(new Date(),DateTools.YMDHM));
		List<Map> mapList = chargeBillRpcService.queryChargeBillingList(inMap);
		List<Map> chargeList =new ArrayList<>();
		if(CollectionUtils.isNotEmpty(mapList)){
			for(Map map1:mapList){
				Map map =new HashMap();
				map.put("chcName",map1.get("CHC_NAME"));
				map.put("chcNo",map1.get("CHC_NO"));
				chargeList.add(map);
			}
		}
		List<CodeBO> sendStatusList = codeService.getStandardCodes("chcBillSendStatus", null);
		m.addAttribute("chargeList", chargeList);
		m.addAttribute("chcBillSendStatusList", sendStatusList);
		m.addAttribute("searchForm", condition);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		
		String currentEnvironment = sysParamService.getSysParamsValues("currentEnvironment");
		if ("ndjt".equals(currentEnvironment)) {
			//设备子分类
			//充电桩分类
			List<CodeBO> pileSubTypeList = codeService.getStandardCodes("pileSubType", null);
			m.addAttribute("subTypeList", pileSubTypeList);
			return "/bil/xpchargeConf/pileSend/pileSendIndexNd";
		}
		return "/bil/xpchargeConf/pileSend/pileSendIndex";
	}
	

	/**
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SecurityException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 查询充电电价下发
	 */
	@RequestMapping(value = "/queryChcbillSends")
	public @ItemResponseBody
	QueryResultObject queryChcbillSends(@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		if (StringUtils.nullOrBlank(condition.getOrgCode())) {
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if (orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}

		// 管理单位
		List<OrgBo> org = orgService.getAllOrg();
		Map<String,String> orgMap = new HashMap<>();
		for (OrgBo orgBo : org) {
			orgMap.put(orgBo.getOrgCode(),orgBo.getOrgShortName());
		}

		// 下发状态处理
		Map<String,String> sendStatusMap = new HashMap<>();
		List<CodeBO> sendStatusList = codeService.getStandardCodes("chcBillSendStatus", null);
		if (CollectionUtils.isNotEmpty(sendStatusList)){
			for (CodeBO codeBO : sendStatusList) {
				sendStatusMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}

		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				condition.setOrgCodeList(orgCodeList);
				condition.setOrgCode(null);
			}
		}

		// 查询计费
		List<Map> chcConfigList = new ArrayList<>();
		int recordCount = xpPileSendService.queryPileBillInfoCount(condition);
		if (recordCount > 0){
			List<String> pileList = new ArrayList<>();// 桩ID集合
			chcConfigList = xpPileSendService.queryPileBillInfo(condition);
			String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
			if (StringUtil.isBlank(sendTimeOut)){
				sendTimeOut = "20";
			}
			for (Map map : chcConfigList) {
				map.put("orgCodeName",orgMap.get(map.get("orgCode")));
				map.put("sendStatusName",sendStatusMap.get(map.get("sendStatus")));
				pileList.add(MapUtils.getValue(map,"pileId"));
				if ("05".equals(map.get("sendStatus")) && StringUtil.isNotBlank(map.get("sendTime"))){
					int min =  DateTools.getMinutesTonow(MapUtils.getValue(map,"sendTime"));
					if (MathUtils.compareTo(sendTimeOut,String.valueOf(min)) < 0){
						map.put("sendStatusName","下发超时");
					}
				}
			}

			Map<String,Object> searchMap = new HashedMap();
			searchMap.put("pileIdList",pileList);
			List<Map> list = archivesRpcService.queryPilesByMap(searchMap);
			MergeUtil.mergeList(chcConfigList, list, "pileId",  new String[]{"stationName","pileName","pileNo","runStatus"},  new String[]{"stationName","pileName","pileNo","runStatus"});

			for(Map pile:chcConfigList){
				// 状态处理
				String runStatus = StringUtil.nullForString(pile.get("runStatus"));
				if (StringUtil.isNotBlank(runStatus) && runStatus.indexOf(AssetConstants.GunCouplerRunStatus.Gun_Coupler_Run_Status_5) < 0){
					pile.put("runStatusName","在线");
				} else {
					pile.put("runStatusName","离线");
				}
			}
		}
		return RestUtils.wrappQueryResult(chcConfigList, recordCount);
	}
	
	/**
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SecurityException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 查询充电电价下发
	 */
	@RequestMapping(value = "/queryChcbillSendsNd")
	public @ItemResponseBody
	QueryResultObject queryChcbillSendsNd(@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		if (StringUtils.nullOrBlank(condition.getOrgCode())) {
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if (orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}

		// 管理单位
		List<OrgBo> org = orgService.getAllOrg();
		Map<String,String> orgMap = new HashMap<>();
		for (OrgBo orgBo : org) {
			orgMap.put(orgBo.getOrgCode(),orgBo.getOrgShortName());
		}

		// 下发状态处理
		Map<String,String> sendStatusMap = new HashMap<>();
		List<CodeBO> sendStatusList = codeService.getStandardCodes("chcBillSendStatus", null);
		if (CollectionUtils.isNotEmpty(sendStatusList)){
			for (CodeBO codeBO : sendStatusList) {
				sendStatusMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		
		Map<String,String> subTypeMap = new HashMap<>();
		List<CodeBO> pileSubTypeList = codeService.getStandardCodes("pileSubType", null);
		if (CollectionUtils.isNotEmpty(pileSubTypeList)){
			for (CodeBO codeBO : pileSubTypeList) {
				subTypeMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}

		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				condition.setOrgCodeList(orgCodeList);
				condition.setOrgCode(null);
			}
		}

		// 查询计费
		List<Map> chcConfigList = new ArrayList<>();
		int recordCount = xpPileSendService.queryPileBillInfoCount(condition);
		if (recordCount > 0){
			List<String> pileList = new ArrayList<>();// 桩ID集合
			chcConfigList = xpPileSendService.queryPileBillInfo(condition);
			String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
			if (StringUtil.isBlank(sendTimeOut)){
				sendTimeOut = "20";
			}
			for (Map map : chcConfigList) {
				map.put("orgCodeName",orgMap.get(map.get("orgCode")));
				map.put("sendStatusName",sendStatusMap.get(map.get("sendStatus")));
				map.put("subTypeName",subTypeMap.get(map.get("subType")));
				pileList.add(MapUtils.getValue(map,"pileId"));
				if ("05".equals(map.get("sendStatus")) && StringUtil.isNotBlank(map.get("sendTime"))){
					int min =  DateTools.getMinutesTonow(MapUtils.getValue(map,"sendTime"));
					if (MathUtils.compareTo(sendTimeOut,String.valueOf(min)) < 0){
						map.put("sendStatusName","下发超时");
					}
				}
			}

			Map<String,Object> searchMap = new HashedMap();
			searchMap.put("pileIdList",pileList);
			List<Map> list = archivesRpcService.queryPilesByMap(searchMap);
			MergeUtil.mergeList(chcConfigList, list, "pileId",  new String[]{"stationName","pileName","pileNo","runStatus"},  new String[]{"stationName","pileName","pileNo","runStatus"});

			for(Map pile:chcConfigList){
				// 状态处理
				String runStatus = StringUtil.nullForString(pile.get("runStatus"));
				if (StringUtil.isNotBlank(runStatus) && runStatus.indexOf(AssetConstants.GunCouplerRunStatus.Gun_Coupler_Run_Status_5) < 0){
					pile.put("runStatusName","在线");
				} else {
					pile.put("runStatusName","离线");
				}
			}
		}
		return RestUtils.wrappQueryResult(chcConfigList, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-30
	 * @description 下发充电电价
	 */
	@RequestMapping(value = "/issured", method = RequestMethod.POST)
	public
	@ResponseBody
	WrappedResult issured(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo, @RequestParam("stationId") String stationId) {
		try {
			xpPileSendService.issuredPile(chcNo, pileNo, stationId);
			return WrappedResult.successWrapedResult("正在下发");
		} catch (Exception e) {
			logger.error("", e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发明细
	 */
	@RequestMapping(value = "/detail", method = RequestMethod.GET)
	public String detail(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo, @RequestParam("stationId") String stationId) {
		addCodes(m, BizConstants.CodeType.SUCCESS_FLAG, BizConstants.CodeType.VALID_FLAG);
		List<CodeBO> pileSendStatusList = codeService.getAllStandardCodes(BizConstants.CodeType.CHC_BILL_SEND_STATUS, null);
		m.addAttribute("issuedPileStatusList", pileSendStatusList);
		if (!StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list = chargeBillingConfService.queryChargeBillingMap(condition);
			if (list != null && list.size() > 0) {
				ChargeBillingConfBo bo = list.get(0);
				bo.setStationId(stationId);
				//查询下发状态
				List<String> pileNoList = new LinkedList<>();
				pileNoList.add(pileNo);
				Map<String, Object> searcurChcMap = new HashedMap();
				searcurChcMap.put("pileNoList", pileNoList);
				List<Map> curentChcs = xpPileSendService.queryTheCurrentChc(searcurChcMap);
				if (curentChcs!=null && curentChcs.size()>0){
					if (bo.getChcNo().equals(StringUtil.nullForString(curentChcs.get(0).get("chcNo")))){
						bo.setSendStatus(StringUtil.nullForString(curentChcs.get(0).get("sendStatus")));
						bo.setSendTime(StringUtil.nullForString(curentChcs.get(0).get("sendTime")));
					}else{
						bo.setSendStatus(ChargeConstant.chcBillSendStatus.NOT);
					}
				}
				Map<String, Object> searchMap = new HashMap<String, Object>();
				searchMap.put("pileNo", pileNo);
				Map pileMap = archivesRpcService.queryPileMap(searchMap);
				if (pileMap != null) {
					bo.setStationName(String.valueOf(pileMap.get("stationName")));
					bo.setStationId(String.valueOf(pileMap.get("stationId")));
					bo.setPileNo(String.valueOf(pileMap.get("equipNo")));
					bo.setPileName(String.valueOf(pileMap.get("equipName")));
				}
				OrgBo org = orgService.getOrgByNo(bo != null ? bo.getOrgCode() : "");
				if (org != null)
					bo.setOrgCodeName(org.getOrgShortName());
				m.addAttribute("searchForm", bo);
			} else
				m.addAttribute("searchForm", new ChargeBillingConfBo());
		}else{
			m.addAttribute("searchForm", new ChargeBillingConfBo());
		}
		String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
		m.addAttribute("chargingPriingSendTimeOut", StringUtil.isEmpty(sendTimeOut)?"20":sendTimeOut);
		return "/bil/xpchargeConf/pileSend/chcbillSendDetail";
	}

	/**
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws SecurityException
	 * @throws NoSuchFieldException
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 查询桩电价下发明细
	 */
	@RequestMapping(value = "/queryChcbillSendDetail")
	public
	@ItemResponseBody
	QueryResultObject queryChcbillSendDetail(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendBo> list = null;
		int recordCount = 0;
		if (!StringUtils.nullOrBlank(condition.getChcNo())) {

			List pileNoList = new ArrayList();
			pileNoList.add(condition.getPileNo());
			condition.setPileNos(pileNoList);
			list = chcbillSendService.queryChcbillPileSends(condition);
			if (list != null && list.size() > 0) {
				StringBuilder pileNoSb = new StringBuilder();
				for (ChcbillPileSendBo cbcBo : list) {
					if (!StringUtils.nullOrBlank(cbcBo.getPileNo())) {
						pileNoSb.append(cbcBo.getPileNo()).append(",");
					}
				}
				String pileNos = pileNoSb.toString();
				if (!StringUtils.nullOrBlank(pileNos)) {
					pileNos = pileNos.substring(0, pileNos.length() - 1);
					ChargeBillingConfQueryCondition cbQCondition = new ChargeBillingConfQueryCondition();
					cbQCondition.setChcNo(condition.getChcNo());
					List<ChargeBillingConfBo> cbcs = chargeBillingConfService.queryChargeBillingMap(cbQCondition);
					String stationId = null;
					if (cbcs.size() > 0)
						stationId = condition.getStationId();
					List<Map<String, Object>> stationList = archivesRpcService.queryEquipByStationIdAndEquipNos(stationId, pileNos);
					MergeUtil.mergeList(list, stationList, "pileNo", "equipNo", new String[]{"pileName"}, new String[]{"equipName"});
				}
			}
			recordCount = chcbillSendService.queryChcbillPileSendsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-10
	 * @description 重新下发桩电价
	 */
	@RequestMapping(value = "/issuredPile", method = RequestMethod.POST)
	public
	@ResponseBody
	WrappedResult issuredPile(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo, @RequestParam("stationId") String stationId) {
		try {
			xpPileSendService.issuredPile(chcNo, pileNo, stationId);
			return WrappedResult.successWrapedResult(true);
		} catch (Exception e) {
			logger.error("", e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发历史
	 */
	@RequestMapping(value = "/log", method = RequestMethod.GET)
	public String log(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		if (!StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list = chargeBillingConfService.queryChargeBillingMap(condition);
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			if (list != null && list.size() > 0)
				bo = list.get(0);
			bo.setPileId(pileNo);
			m.addAttribute("searchForm", bo);
		}
		return "/bil/xpchargeConf/stationSend/chcbillSendLog";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-04-09
	 * @description 查询桩电价下发历史
	 */
	@RequestMapping(value = "/queryChcbillSendLog")
	public
	@ItemResponseBody
	QueryResultObject queryChcbillSendLog(@QueryRequestParam("params") RequestCondition params) {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendLogBo> list = null;
		int recordCount = 0;
		if (!StringUtils.nullOrBlank(condition.getChcNo())) {
			list = chcbillSendService.queryChcbillPileSendLogs(condition);
			recordCount = chcbillSendService.queryChcbillPileSendLogsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	private void addCodes(Model m, String... strings) {
		for (String str : strings) {
			m.addAttribute(str + "List", codeService.getStandardCodes(str, null));
		}
	}

	/**
	 * @Description:获取下级
	 * @Author: tianyoupeng
	 * @Time: 2017/6/19 10:13
	 */
	private List<String> getSubOrg(String orgCode) {
		List<String> orgList = new ArrayList<String>();
		if (StringUtil.isNotBlank(orgCode)) {
			String[] orgArr = orgCode.split(",");
			for (String orgCodeTemp : orgArr) {
				List<OrgBo> orgBoList = orgService.getSubOrg(orgCodeTemp, null, null);
				if (!CollectionUtils.isEmpty(orgBoList)) {
					for (OrgBo bo : orgBoList) {
						orgList.add(bo.getOrgCode());
					}
				}
			}
		} else {
			return null;
		}
		orgList.add(orgCode);
		return orgList;
	}
}
