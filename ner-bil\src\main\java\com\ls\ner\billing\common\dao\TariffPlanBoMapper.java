package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.common.bo.TariffPlanBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TariffPlanBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int countByExample(TariffPlanBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int deleteByExample(TariffPlanBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int insert(TariffPlanBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int insertSelective(TariffPlanBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    List<TariffPlanBo> selectByExample(TariffPlanBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    TariffPlanBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int updateByExampleSelective(@Param("record") TariffPlanBo record, @Param("example") TariffPlanBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int updateByExample(@Param("record") TariffPlanBo record, @Param("example") TariffPlanBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int updateByPrimaryKeySelective(TariffPlanBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    int updateByPrimaryKey(TariffPlanBo record);
}