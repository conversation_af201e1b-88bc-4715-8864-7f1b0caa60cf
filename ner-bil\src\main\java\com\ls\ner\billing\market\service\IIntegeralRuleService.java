package com.ls.ner.billing.market.service;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.ls.ner.billing.market.bo.IntegeralRuleBo;

public interface IIntegeralRuleService {
	
	/**
	 * 获取积分规则列表
	 * @param integeralRuleBo
	 * @return
	 */
	List<IntegeralRuleBo> queryRuleList(IntegeralRuleBo integeralRuleBo);
	
	/**
	 * 获取积分规则列表总数
	 * @param integeralRuleBo
	 * @return
	 */
	int queryRuleListCount(IntegeralRuleBo integeralRuleBo);
	
	/**
	 * 创建积分规则
	 * @param integeralRuleBo
	 */
	void addRule(IntegeralRuleBo integeralRuleBo) throws Exception ;
	
	/**
	 * 修改积分规则
	 * @param integeralRuleBo
	 */
	void editRule(IntegeralRuleBo integeralRuleBo);
	
	/**
	 * 规则状态变更（启用/停用）
	 * @param integeralRuleBo
	 */
	void updateRuleStatus(IntegeralRuleBo integeralRuleBo);
	
	/**
	 * 获取规则详情
	 * @return
	 */
	IntegeralRuleBo getRuleInfo(@Param("ruleId") String ruleId);

}
