<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充电计费配置" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<ls:text name="chcStatus" property="chcStatus" type="hidden" />
				<ls:text name="isMain" property="isMain" type="hidden" />
				<ls:text name="orgCode" property="orgCode" type="hidden" />
				<ls:text name="endEftDate" property="endEftDate" type="hidden" />
				<td><ls:label text="计费编号" /></td>
				<td><ls:text name="chcNo"/></td>
				<td><ls:label text="计费名称" /></td>
				<td><ls:text name="chcName"/></td>
			</tr>
			<tr>
				<td><ls:label text="上个版本" /></td>
				<td><ls:text name="upChcNo"/></td>
				<td colspan="2">
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" width="100%" showCheckBox="true" singleSelect="true" caption="充电计费配置列表" primaryKey="chcNo">
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doSure" text="确认"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="search" onclick="doShow" text="详情"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="管理单位" name="orgCode" hidden="true"/>
					<ls:column caption="计费编号" name="chcNo" />
					<ls:column caption="计费名称" name="chcName" />
					<ls:column caption="采集数据类型" name="gatherDataTypeName" />
					<ls:column caption="计费模式" name="chargeModeName" />
					<ls:column caption="费控模式" name="billCtlMode" hidden="true"/>
					<ls:column caption="状态" name="chcStatusName" />
					<ls:column caption="桩生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="上个版本" name="upChcName" formatFunc="showDetail"/>
					<ls:column caption="上个版本"  name="upChcNo" hidden="true"/>
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</ls:form>
	<ls:script>
	var astPath = '${astPath }';
	var isFrom = '${isFrom}';
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		params.isFrom=isFrom;
		chargeGrid.query('~/billing/xpcbc/queryChargeBillingConfs',params,function(){});
	}

 	function doShow(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要查看详情的记录");
    		return;
	   }
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+item.chcNo,"查看充电费用配置",1000, 600,true, null);
 	}

	function doSure(){
		var item = chargeGrid.getSelectedItem();
		if(LS.isEmpty(item)){
		LS.message("error","请选择计费配置记录");
		return;
		}
		LS.parent().setChcConfig(item);
		LS.window.close();
		}

    function doClear(){
	  chcNo.setValue();
	  chcName.setValue();
	  upChcNo.setValue();
    }
    
	function getOrgs(){
		var initValue = [];
		js_util.selectOrgTree(false, null, true, initValue, false, function(node){
			orgCodeName.setValue(node.text);
			orgCode.setValue(node.id);
		});
	}
	
	function changeOrgName(){
		if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
		 orgCode.setValue();
		}
	}
	
	function opr(rowdata){
		if(rowdata.chcStatus=='2'){//草稿
			return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>"
				+"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>删除</a>"
				+"&nbsp;&nbsp;<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doRelease(\"" + rowdata.chcNo + "\")'>发布</a>";
		}else if(rowdata.chcStatus=='0'){//无效
			return "";
		}else{//有效
		   //var endTime = rowdata.eftDate.split(":");
		   //var rowDate = endTime[0]+":"+endTime[1];
		   //var current = (new Date()).Format("yyyy-MM-dd hh:mm");
			//if(rowDate > current){
			//	return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doModify(\"" + rowdata.chcNo + "\")'>修改</a>";
			//}else{
				return "";
			//}
		}
	}
	
	//====================================充电站查询=============================================================
    function getPile(){
       LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
    }
    
    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }
    
    window.setStation = setStation;
    function setStation(item) {
       stationId.setValue(item.stationId);  
       stationName.setValue(item.stationName);
    }
		window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
		}


		function showDetail(rowdata){
		if(rowdata.upChcNo==null){
		return '--'
		}else{
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowDetail(\"" + rowdata.upChcNo +"\")'>"+rowdata.upChcName+"</a>";
		}
		}

		window.doShowDetail=doShowDetail;
		function doShowDetail(upChcNo){
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+upChcNo,"查看充电费用配置",1000, 600,true, null);
		}
    </ls:script>
</ls:body>
</html>