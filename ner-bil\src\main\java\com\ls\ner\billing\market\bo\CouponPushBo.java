package com.ls.ner.billing.market.bo;

import com.pt.poseidon.api.framework.DicAttribute;

import java.io.Serializable;

/**
 * 描述:优惠券推广日志
 * CouponPushBo.java
 * 作者：biaoxiangd
 * 创建日期：2017/7/6 14:36
 **/
public class CouponPushBo implements Serializable {


	private static final long serialVersionUID = -7644820444613242474L;

	private String pushId;
	private String actId;
	private String actType;
	private String pushEmp;
	private String orgCode;
	private String CrtOrgName;
	private String cpnId;
	private String cpnName;
	private String cpnType;
	@DicAttribute(dicName = "codeDict", key = "cpnType", subType = "cpnType")
	private String cpnTypeName;
	private String busiType;
	@DicAttribute(dicName = "codeDict", key = "busiType", subType = "orderType")
	private String busiTypeName;
	private String cpnDctType;
	private String cpnAmt;
	private String eftDate;
	private String invDate;
	private String timeDuration;
	private String timeUnit;
	private String cpnNum;
	private String limGetNum;
	private String alrdyGetNum;
	private String cpnMarks;
	private String cpnStatus;
	@DicAttribute(dicName = "codeDict", key = "cpnStatus", subType = "cpnState")
	private String cpnStatusName;
	private String creTime;
	private String creEmp;
	private String effectTime;
	private String cpnTimeType;

	private String buildId;

	public String getActType() {
		return actType;
	}

	public void setActType(String actType) {
		this.actType = actType;
	}

	public String getCpnTypeName() {
		return cpnTypeName;
	}

	public void setCpnTypeName(String cpnTypeName) {
		this.cpnTypeName = cpnTypeName;
	}

	public String getBusiTypeName() {
		return busiTypeName;
	}

	public void setBusiTypeName(String busiTypeName) {
		this.busiTypeName = busiTypeName;
	}

	public String getCpnStatusName() {
		return cpnStatusName;
	}

	public void setCpnStatusName(String cpnStatusName) {
		this.cpnStatusName = cpnStatusName;
	}

	public String getCrtOrgName() {
		return CrtOrgName;
	}

	public void setCrtOrgName(String crtOrgName) {
		CrtOrgName = crtOrgName;
	}

	public String getCpnTimeType() {
		return cpnTimeType;
	}

	public void setCpnTimeType(String cpnTimeType) {
		this.cpnTimeType = cpnTimeType;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getCpnName() {
		return cpnName;
	}

	public void setCpnName(String cpnName) {
		this.cpnName = cpnName;
	}

	public String getCpnType() {
		return cpnType;
	}

	public void setCpnType(String cpnType) {
		this.cpnType = cpnType;
	}

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public String getCpnDctType() {
		return cpnDctType;
	}

	public void setCpnDctType(String cpnDctType) {
		this.cpnDctType = cpnDctType;
	}

	public String getCpnAmt() {
		return cpnAmt;
	}

	public void setCpnAmt(String cpnAmt) {
		this.cpnAmt = cpnAmt;
	}

	public String getEftDate() {
		return eftDate;
	}

	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}

	public String getInvDate() {
		return invDate;
	}

	public void setInvDate(String invDate) {
		this.invDate = invDate;
	}

	public String getTimeDuration() {
		return timeDuration;
	}

	public void setTimeDuration(String timeDuration) {
		this.timeDuration = timeDuration;
	}

	public String getTimeUnit() {
		return timeUnit;
	}

	public void setTimeUnit(String timeUnit) {
		this.timeUnit = timeUnit;
	}

	public String getCpnNum() {
		return cpnNum;
	}

	public void setCpnNum(String cpnNum) {
		this.cpnNum = cpnNum;
	}

	public String getLimGetNum() {
		return limGetNum;
	}

	public void setLimGetNum(String limGetNum) {
		this.limGetNum = limGetNum;
	}

	public String getAlrdyGetNum() {
		return alrdyGetNum;
	}

	public void setAlrdyGetNum(String alrdyGetNum) {
		this.alrdyGetNum = alrdyGetNum;
	}

	public String getCpnMarks() {
		return cpnMarks;
	}

	public void setCpnMarks(String cpnMarks) {
		this.cpnMarks = cpnMarks;
	}

	public String getCpnStatus() {
		return cpnStatus;
	}

	public void setCpnStatus(String cpnStatus) {
		this.cpnStatus = cpnStatus;
	}

	public String getCreTime() {
		return creTime;
	}

	public void setCreTime(String creTime) {
		this.creTime = creTime;
	}

	public String getCreEmp() {
		return creEmp;
	}

	public void setCreEmp(String creEmp) {
		this.creEmp = creEmp;
	}

	public String getEffectTime() {
		return effectTime;
	}

	public void setEffectTime(String effectTime) {
		this.effectTime = effectTime;
	}

	public String getPushEmp() {
		return pushEmp;
	}

	public void setPushEmp(String pushEmp) {
		this.pushEmp = pushEmp;
	}

	public String getPushId() {
		return pushId;
	}

	public void setPushId(String pushId) {
		this.pushId = pushId;
	}

	public String getActId() {
		return actId;
	}

	public void setActId(String actId) {
		this.actId = actId;
	}

	public String getCpnId() {
		return cpnId;
	}

	public void setCpnId(String cpnId) {
		this.cpnId = cpnId;
	}

	public String getBuildId() {
		return buildId;
	}

	public void setBuildId(String buildId) {
		this.buildId = buildId;
	}
}
