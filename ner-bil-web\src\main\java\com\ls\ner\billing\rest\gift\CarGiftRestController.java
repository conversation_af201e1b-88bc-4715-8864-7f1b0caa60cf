package com.ls.ner.billing.rest.gift;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.base.service.ITokenService;
import com.ls.ner.billing.gift.bo.AccessTokenResult;
import com.ls.ner.billing.gift.bo.GetAccessTokenRequest;
import com.ls.ner.billing.gift.service.ICarGiftService;
import com.ls.ner.billing.gift.service.impl.UserOAuthServiceImpl;
import com.ls.ner.billing.gift.util.BusinessException;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.http.HttpClientUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.ls.ner.util.sign.ThreeDES;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description CarGiftRestController
 * @create 2020-05-03 15:31
 */
@Controller
@RequestMapping("/api")
public class CarGiftRestController {

    private static final String ThreeDesKey = "+7+hkq4l97VMgGHTufKDEHzfH8FzQ0aw";

    private static final Logger LOGGER = LoggerFactory.getLogger(CarGiftRestController.class);

    //30秒
    private static final long millionDuring = 30 * 1000;

    @ServiceAutowired(value="carGiftService")
    private ICarGiftService iCarGiftService;

    @ServiceAutowired(value = "tokenService")
    private ITokenService tokenService;


    @RequestMapping(value = "/open/v0.1/ndinfo", method = RequestMethod.POST)
    public @ResponseBody
    Object queryUserInfo(HttpServletRequest request) throws UnsupportedEncodingException {

        Map<String,Object> resultMap = new HashedMap();

        Map<String, Object> inMap = HttpClientUtil.getReqParams(request, false);

        LOGGER.debug("======inMap:{}",IJsonUtil.obj2Json(inMap));

        String userStr = String.valueOf(inMap.get("userStr"));
        if (StringUtil.isBlank(userStr)){
            LOGGER.debug("userStr:{}",userStr);
            resultMap.put("ret","400");
            resultMap.put("msg","用户信息不能为空");
            return resultMap;
        }

        try {
            String decrypt = ThreeDES.decrypt(userStr, ThreeDesKey);
            LOGGER.debug("解密后的shareStr:{}", decrypt);
            Map<String,String> map = IJsonUtil.json2Obj(decrypt, Map.class);

            String code = String.valueOf(inMap.get("code"));
            String timeStamp = String.valueOf(map.get("timestamp"));

            if (StringUtil.isBlank(code) || StringUtil.isBlank(timeStamp)){
                resultMap.put("ret","400");
                resultMap.put("msg","验证码或时间错不能为空");
                return resultMap;
            }

             int during = DateTools.getSecondTonow(Long.valueOf(timeStamp));

            if(during > 30){
                resultMap.put("ret","400");
                resultMap.put("msg","请求超时");
                return resultMap;
            }

            LOGGER.debug("调试用：{}",IJsonUtil.obj2Json(map));
            Map params = new HashMap();

            params.put("code",map.get("code"));

            Map userInfo = iCarGiftService.getUserInfo(params);

            LOGGER.debug("====userInfo:{}",userInfo);

            return userInfo;
        } catch (Exception e) {
            LOGGER.debug("报错====={}",e.getMessage());
            resultMap.put("ret",400);
            resultMap.put("msg","验证信息已失效，请重新进入");
            e.printStackTrace();
        }


        return resultMap;
    }


    @RequestMapping(value = "/open/v0.1/ndapply", method = RequestMethod.POST)
    public @ResponseBody
    Object submitApply(HttpServletRequest request) {

        Map applyMap = new HashedMap();

        Map<String, Object> inMap = HttpClientUtil.getReqParams(request, false);

        LOGGER.debug("提交购车信息入参：{}",IJsonUtil.obj2Json(inMap));

        Map<String,Object> result = new HashedMap();
        if (StringUtil.isBlank(StringUtil.nullForString(inMap.get("vin")))){
            result.put("ret",400);
            result.put("msg","车架号不能为空");
            return result;
        }
        if (StringUtil.isBlank(StringUtil.nullForString(inMap.get("licenseNo")))){
            result.put("ret",400);
            result.put("msg","车牌号不能为空");
            return result;
        }
        if (StringUtil.isBlank(StringUtil.nullForString(inMap.get("alipayNo")))){
            result.put("ret",400);
            result.put("msg","支付宝账号不能为空");
            return result;
        }
        if (StringUtil.isBlank(StringUtil.nullForString(inMap.get("buyAddr")))){
            result.put("ret",400);
            result.put("msg","购买4s店不能为空");
            return result;
        }

        String userStr = String.valueOf(inMap.get("userStr"));
        if (StringUtil.isBlank(userStr)){
            result.put("ret","400");
            result.put("msg","用户信息不能为空");
            return result;
        }

        try {
            String decrypt = ThreeDES.decrypt(userStr, ThreeDesKey);
            LOGGER.debug("解密后的shareStr:{}", decrypt);
            Map<String,String> map = IJsonUtil.json2Obj(decrypt, Map.class);

            String timeStamp = String.valueOf(map.get("timestamp"));
            int during = DateTools.getSecondTonow(Long.valueOf(timeStamp));
            LOGGER.debug("during====={}",during);

            if(during > 30){
                result.put("ret","400");
                result.put("msg","请求超时");
                return result;
            }

            inMap.put("code",map.get("code"));
            applyMap = iCarGiftService.submitApply(inMap);

        }catch (Exception e){

            applyMap.put("ret",400);
            applyMap.put("msg","提交信息不正确");
        }

        return applyMap;
    }

    @RequestMapping(value = "/v0.1/ndgift", method = RequestMethod.GET)
    public @ResponseBody
    Object queryUserGift(){
        try {
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String mobile = tokenMsg.get("mobile").toString();
            Map params = new HashedMap();
            params.put("mobile",mobile);
            Map giftInfo = iCarGiftService.queryRecentGiftInfo(params);
            return new ResponseEntity<Map>(giftInfo, HttpStatus.OK);
        }catch (Exception e) {
            LOGGER.debug("查询用户购车优惠券报错:{}",e.getMessage());
            Map resultMap = MapUtils.createFailResult( e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
