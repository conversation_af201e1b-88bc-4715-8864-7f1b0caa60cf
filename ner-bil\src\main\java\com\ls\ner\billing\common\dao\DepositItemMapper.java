package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.DepositItem;
import com.ls.ner.billing.common.bo.DepositItemExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface DepositItemMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(DepositItemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(DepositItemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(DepositItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(DepositItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<DepositItem> selectByExample(DepositItemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    DepositItem selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") DepositItem record, @Param("example") DepositItemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") DepositItem record, @Param("example") DepositItemExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(DepositItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_deposit_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(DepositItem record);
}