<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="限时折扣">
    <style>
        .file-img {
            display: block;
            width: 200px;
            height: 120px;
            position: relative;
        }

        .file-img img {
            display: block;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: gray;
        }

        .file-img:hover span {
            display: block;
        }

        .file-img input {
            position: absolute;
            font-size: 100px;
            right: 0;
            top: 0;
            opacity: 0;
        }

    </style>
</ls:head>
<ls:body>
    <ls:form id="marketactForm" name="marketactForm">
        <ls:text name="stationIds" property="stationIds" type="hidden"/>
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden"/>
        <table class="tab_search">
            <tr>
                <td width="10%"><ls:label text="活动名称" ref="actName"/></td>
                <td><ls:text name="actName" property="actName" required="true" readOnly="true"/></td>
                <td rowspan="4">
                    <div>
                        <a class="file-img" href="javascript:void(0);" style="left: 290px;top: 10px;">
                            <img src="" id="headImg"></img>
                        </a>
                    </div>
                </td>

            </tr>
            <tr>
                <td><ls:label text="生效时间" ref="effTime"/></td>
                <td>
                    <ls:text name="effTime" property="effTime" required="true" readOnly="true"/></td>
                </td>

            </tr>
            <tr>
                <td><ls:label text="失效时间" ref="expTime"/></td>
                <td>
                    <ls:text name="expTime" property="expTime" required="true" readOnly="true"/>
                </td>
            </tr>
            <tr>
                <td><ls:label text="适用对象" ref="custType"/></td>
                <td>
                    <ls:checklist name="custType" property="custType" type="checkbox" required="true" readOnly="true">
                        <ls:checkitem text="个人" value="01"/>
                        <ls:checkitem text="企业" value="02"/>
                    </ls:checklist>
                </td>

            </tr>
            <tr>
                <td><ls:label text="适用运营商" ref="buildType"/></td>
                <td>
                    <ls:checklist name="buildType" type="radio" property="buildType" required="true" readOnly="true">
                        <ls:checkitem text="全部" value="0"/>
                        <ls:checkitem text="部分" value="1"/>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="buildListTr" style="display: none">
                <td><ls:label text=""/></td>
                <td>
                    <ls:text name="buildName" property="buildName" required="true" readOnly="true"/>
                </td>

            </tr>
            <tr>
                <td><ls:label text="适用城市" ref="city"/></td>
                <td>
                    <ls:checklist name="city" id="city" property="city" type="radio" required="true" readOnly="true">
                        <ls:checkitem text="全部" value="0"/>
                        <ls:checkitem text="部分" value="1"/>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="cityListTr" style="display: none">
                <td><ls:label text=""/></td>
                <td>
                    <ls:checklist name="cityCodes" enabled="true" type="checkbox" property="cityCodes" readOnly="true">
                        <ls:checkitems property="cityList" scope="request" text="text"
                                       value="value"></ls:checkitems>
                    </ls:checklist>
                </td>
            </tr>
            <tr>
                <td><ls:label text="适用站点" ref="stationId"/></td>
                <td>
                    <ls:checklist name="stationId" property="stationId" id="stationId" type="radio" required="true"
                                  value="0"
                                  onchanged="doSearchStation"  readOnly="true">
                        <ls:checkitem text="全部" value="0"/>
                        <ls:checkitem text="部分" value="1"/>
                    </ls:checklist>
                </td>
            </tr>

            <tr id="stationNumTr" style="display: none">
                <td><ls:label text=""/></td>
                <td><span id="stationSelectNum" style="color: red">共${stationNum}个充电站</span></td>
            </tr>
            <tr id="stationListTr" style="display: none">
                <td><ls:label text=""/></td>
                <td colspan="3">
                    <ls:grid height="auto" width="80%" url="" name="station_grid" showCheckBox="false"
                             primaryKey="stationId"
                             allowSorting="true" singleSelect="false">
                        <ls:column caption="站点类型" name="stationTypeName"></ls:column>
                        <ls:column caption="站点名称" name="stationName"></ls:column>
                    </ls:grid>
                </td>
            </tr>
            <tr>
                <td><ls:label text="优惠内容" ref="actCondDetId"/><ls:text type="hidden" name="actCondDetId" required="true"/></td>
                <td colspan="3">
                    <ls:grid height="auto" width="80%"  url="" name="actDctGrid" showRowNumber="true" primaryKey="id"
                             showCheckBox="false" allowSorting="true" singleSelect="true">
                        <ls:column caption="费用项" name="actDctProdName" editableEdit="false" width="50%"></ls:column>
                        <ls:column name="dctValue" caption="折扣" editableEdit="false" width="25%"></ls:column>
                    </ls:grid>
                </td>
            </tr>
            <tr>
                <td><ls:label text="活动描述" ref="actMarks"/></td>
                <td colspan="3">
                    <ls:text name="actMarks" property="actMarks" type="textarea" readOnly="true" required="true"/>
                </td>
            </tr>
            <tr>
                <td><ls:label text="资讯内容" ref="actInfo"/><ls:text type="hidden" name="actInfo" required="true"/></td>
                <td>
                    <a href="javascript:void(0)" onclick="toActInfo()">查看</a>
                </td>
            </tr>
        </table>
    </ls:form>

    <ls:script>
        var infoUrlValue = '${infoUrl}'
        var pubPath = '${pubPath }';
        var _actInfoId = '${actInfoId }';
        var _relaId = '${relaId }';

        window.onload = function () {
            if (buildType.getValue() == 1) {
                $("#buildListTr").show();
            }

            if (city.getValue() == 1) {
                $("#cityListTr").show();
            }
            if (stationId.getValue() == 1) {
                doSearchStation();
            }

            queryGrid();
            //$('#infoUrl').attr('href',infoUrlValue);

            //资讯

            headImg.src = pubPath + '/pub/image/js/add.png';
            if (!LS.isEmpty(_relaId) && !LS.isEmpty(_actInfoId)) {
                headImg.src = pubPath + '/attach/attachs/mkt_act_info/02/' + _relaId;
            }
        }
        window.actDctGrid = actDctGrid;
        window.queryGrid = queryGrid;

        function queryGrid() {
            actDctGrid.query("~/marketAct/limDisCountManage/queryActAllNew", {actId:actId.getValue(),actType:actType.getValue(),isFromNewDetail:1})
        }

        window.toActInfo = function(){
            LS.dialog(infoUrlValue, "活动资讯", 1000, 600, true, null);
        }


        function doSearchStation() {
            var stationIdsValue = stationIds.getValue();
            if (!LS.isEmpty(stationIdsValue)) {
                stationIdsValue = stationIdsValue.substring(0,stationIdsValue.length-1)
                $("#stationListTr").show();
                $("#stationNumTr").show();
                station_grid.query("~/markertAct/getActStationList", {stationIds:stationIdsValue});
            }
        }

    </ls:script>
</ls:body>
</html>