<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IMarketActScopeDao" >
  	<resultMap id="BaseResultMap" type="com.ls.ner.billing.mktact.bo.MarketActScopeBo" >
		<id column="SCOPE_ID" property="scopeId" jdbcType="BIGINT" />
	    <result column="ACT_ID" property="actId" jdbcType="BIGINT" />
	    <result column="ACT_TYPE" property="actType" jdbcType="VARCHAR" />
	    <result column="BUILD_ID" property="buildId" jdbcType="VARCHAR" />
	    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
	    <result column="CITY" property="city" jdbcType="VARCHAR" />
	    <result column="STATION_ID" property="stationId" jdbcType="VARCHAR" />
	    <result column="SCOPE_TYPE" property="scopeType" jdbcType="VARCHAR" />
  	</resultMap>
	

  	<insert id="insertMarketActScopeBatch" parameterType="java.util.List">
		insert into e_mkt_act_scope(
		ACT_ID,
		ACT_TYPE,
		BUILD_ID,
		PROVINCE,
		CITY,
		STATION_ID,
		SCOPE_TYPE,
		DATA_OPER_TIME
		)values
		<foreach collection="list" item="item" index="index" separator=",">
			(
			<choose>
				<when test="item.actId !=null and item.actId !=''">#{item.actId},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.actType !=null and item.actType !=''">#{item.actType},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.buildId !=null and item.buildId !=''">#{item.buildId},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.province !=null and item.province !=''">#{item.province},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.city !=null and item.city !=''">#{item.city},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.stationId != null and item.stationId !=''">#{item.stationId},</when>
				<otherwise>null,</otherwise>
			</choose>
			<choose>
				<when test="item.scopeType != null and item.scopeType !=''">#{item.scopeType},</when>
				<otherwise>null,</otherwise>
			</choose>
			now()
			)
		</foreach>
  	</insert>

	<select id="queryScopeList" parameterType="java.util.Map" resultMap="BaseResultMap">
		select SCOPE_ID,ACT_ID,ACT_TYPE,BUILD_ID,PROVINCE,CITY,STATION_ID,SCOPE_TYPE from e_mkt_act_scope
		<where>
			<if test="actId!='' and actId!=null">
				and ACT_ID = #{actId}
			</if>
		</where>
	</select>

	<delete id="deleteByActId" parameterType="java.lang.String">
		delete from e_mkt_act_scope
		<where>
			ACT_ID =#{actId}
		</where>
	</delete>
</mapper>