package com.ls.ner.billing.gift.service;

import com.ls.ner.billing.gift.bo.CarGiftBo;
import com.ls.ner.billing.gift.condition.CarGiftCondition;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description CarGiftService
 * @create 2020-04-29 18:03
 */
public interface ICarGiftService {

    /**
     * @param params
     * @description 查询领取购车券
     * <AUTHOR>
     * @create 2020-04-29 18:04:24
     */
    List<CarGiftBo> listCarGift(CarGiftCondition carGiftCondition);

    /**
     * @param params
     * @description  数量查询
     * <AUTHOR>
     * @create 2020-04-29 18:32:27
     */
    int countCarGift(CarGiftCondition carGiftCondition);

    /**
     * @param inMap
     * @description  获取用户信息
     * <AUTHOR>
     * @create 2020-05-03 15:50:26
     */
    Map getUserInfo(Map inMap) throws Exception;


    /**
     * @param inMap
     * @description 提交购车申请
     * <AUTHOR>
     * @create 2020-05-04 01:10:08
     */
    Map submitApply(Map inMap) throws Exception;

    /**
     * @param inMap
     * @description
     * <AUTHOR>
     * @create 2020-05-04 15:11:14
     */
    Map queryRecentGiftInfo(Map inMap);

    /**
     * @param carGiftBo
     * @description 更新购车信息
     * <AUTHOR>
     * @create 2020-05-04 13:51:54
     */
    int updateCarGift(CarGiftBo carGiftBo);

    /**
     * @param request
     * @description 导入购车信息
     * <AUTHOR>
     * @create 2020-05-04 13:52:10
     */
    Map saveImportData(MultipartHttpServletRequest request) throws Exception;

    /**
     * @param request
     * @description 审核通过直充
     * <AUTHOR>
     * @create 2020-05-04 13:52:10
     */
    Map<String,Object> doCharge(CarGiftBo carGiftBo) throws Exception;
}
