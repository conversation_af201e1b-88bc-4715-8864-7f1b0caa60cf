package com.ls.ner.billing.market.service;

import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.vo.MarketCondition;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;


public interface ICouponService {

    /**
     *描述:新增优惠券
     * @param: [bo, request]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:06
     */
    int saveCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception;

    /**
     *描述:获取条件参数
     * @param: [map]
     *@return: java.util.List<java.util.Map>
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:06
     */
    public List<Map> queryBProd(Map map);

    /**
     *描述:新增优惠券关联关系
     * @param: [bo]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:06
     */
    public int insertDctCond(CouponBo bo);
    /**
     *描述:更新优惠券表和关系表
     * @param: [bo, request]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:06
     */
    int updateCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception;
    /**
     *描述:获取优惠券详情
     * @param: [condition]
     *@return: com.ls.ner.billing.market.bo.CouponBo
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:07
     */
    CouponBo getCouponDetail(MarketCondition condition);

    /**
     *描述:优惠券管理-查询
     * @param: [condition]
     *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:07
     */
    List<CouponBo> getCoupons(MarketCondition condition);
    int getCouponsCount(MarketCondition condition);

    /**
     * 获取条件列表
     *
     * @param inMap
     * @return
     */
    public List<CouponBo> queryDctCond(Map<String, String> inMap);

    /**
     *描述:更新优惠券状态
     * @param: [bo]
     *@return: void
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:07
     */
    void updateCouponStatus(CouponBo bo);

    /**
     *描述:提供给发放功能调用，更新已领属性
     * @param: [map]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 12:05
     */
    int rpcUpdateCoupon(Map<String, String> map);

    /**
     *描述:优惠券使用信息
     * @param: []
     *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 20:28
     */
    List<CouponBo> getAccountCoupons(MarketCondition condition);

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-01 可领用的优惠券
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    public List<Map<String, Object>> couponsRestGET(Map<String, String> map) throws Exception;

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-02 优惠券领取
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    public Map<String, Object> couponsRestPOST(Map<String, String> map) throws Exception;
    /**
     * @param
     * @description 优惠券失效任务调度
     * <AUTHOR>
     * @create 2018-03-07 14:59:21
     */
    void excuteCouponTask();

    /**
     * @param inMap
     * @description 积分兑换优惠券
     * <AUTHOR> 
     * @create 2018-04-24 15:36:33
     */
    public Map<String, Object> pointCoupon(Map<String, Object> inMap) throws Exception;



    /**
     * @param condition
     * @description  新优惠券管理-查询
     * <AUTHOR>
     * @create 2019-05-27 11:26:51
     */
    List<CouponBo> newGetCoupons(MarketCondition condition);
    int newGetCouponsCount(MarketCondition condition);



    /**
     * @param cpnId
     * @description  删除优惠券
     * <AUTHOR>
     * @create 2019-05-28 15:10:37
     */
    int deleteByCpnId(String cpnId);


   /**
    * @param bo
    * @param request
    * @description
    * <AUTHOR>
    * @create 2019-05-28 15:54:00
    */
   int newSaveCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception;


    /**
     * @param bo
     * @param request
     * @description  更新优惠券表和关系表
     * <AUTHOR>
     * @create 2019-05-29 12:36:08
     */
    int newUpdateCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception;


   /**
    * @param inMap
    * @description
    * <AUTHOR>
    * @create 2019-05-29 13:32:05
    */
   public List<CouponBo> newQueryDctCond(Map<String, String> inMap);

    public CouponBo getCouponCpnPurposeDetail(MarketCondition condition);
}
