package com.ls.ner.billing.mktact.dao;

import com.ls.ner.billing.mktact.bo.MarketActGiveBo;
import com.ls.ner.billing.mktact.bo.MarketTypeBo;

import java.util.List;
import java.util.Map;

/**
 * 描述:活动类型处理 DAO
 * IActInfoService.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 11:47
 **/
public interface IActCondDao {

    /**
     * 描述: 保存优惠条件主表
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int saveActCond(MarketTypeBo bo) throws Exception;

    /**
     * 描述: 更新优惠条件主表
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int updateActCond(MarketTypeBo bo) throws  Exception;

    /**
     * 描述: 新增优惠条件明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int saveActCondDet(MarketTypeBo bo) throws  Exception;

    /**
     * 描述: 更新优惠条件明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int updateActCondDet(MarketTypeBo bo) throws  Exception;

    /**
     * 描述: 删除优惠条件明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int delActCondDet(MarketTypeBo bo)throws  Exception;

    /**
     * 描述: :新增优惠内容
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int saveMketActDct(List list)throws  Exception;

    /**
     * 描述: :删除优惠内容
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int delMketActDct(MarketTypeBo bo)throws  Exception;

    /**
     * 描述: :优惠条件明细中是否还存在优惠内容
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int queryMketActDctCount(MarketTypeBo bo)throws  Exception;;

    /**
     * 描述: :有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    List<MarketTypeBo> queryActDctAll(MarketTypeBo bo)throws  Exception;

    /**
     * 描述: :限时打折 -- 有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    List<MarketTypeBo> queryActAll(MarketTypeBo bo)throws  Exception;


    /**
     * 描述: :获取活动优惠条件
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    List<MarketTypeBo> queryActCond(MarketTypeBo bo)throws  Exception;

    /**
     * 描述: :删除活动优惠条件
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    void delActCond(MarketTypeBo bo) throws  Exception;

    /**
     * 描述:查询优惠条件明细
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    List<MarketTypeBo> queryMktActCondDet(MarketTypeBo bo) throws Exception;

    /**
     * 描述:查询优惠内容
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    List<MarketTypeBo> queryMktActDct(MarketTypeBo bo) throws Exception;

    /**
     * 描述: :新增赠送内容
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    int saveMktActGive(List list) throws  Exception;

    /**
     * @param bo
     * @description 删除赠送内容
     * <AUTHOR>
     * @create 2017-08-09 11:06:11
     */
    int delMktActGive(MarketActGiveBo bo) throws  Exception;

    /**
     * @param actMap
     * @description 查询活动和站点关联记录
     * <AUTHOR>
     * @create 2019-05-09 16:50:33
     */
  List<MarketTypeBo> qryActStationList(Map<String, Object> actMap);

  /**
   * @param actMap
   * @description 保存活动和站点关联
   * <AUTHOR>
   * @create 2019-05-09 16:58:11
   */
    void saveActStation(List<Map<String, String>> actMap);

    /**
     * @param serchMap
     * @description 查询活动关联的站点条数
     * <AUTHOR>
     * @create 2019-05-09 17:33:47
     */
    int qryActStationCount(Map<String, Object> serchMap);

    /**
     * @param map
     * @description 删除关联站点
     * <AUTHOR>
     * @create 2019-05-09 17:49:54
     */
    void delActStation(Map<String, Object> map);
}
