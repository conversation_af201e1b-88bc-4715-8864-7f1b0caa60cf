package com.ls.ner.billing.gift.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.mktact.service.IMarketActRpcService;
import com.ls.ner.billing.gift.bo.RechargeCardBo;
import com.ls.ner.billing.gift.bo.RechargeCardDetail;
import com.ls.ner.billing.gift.bo.RechargeCardOrder;
import com.ls.ner.billing.gift.condition.RechargeCardCondition;
import com.ls.ner.billing.gift.dao.IRechargeCardDao;
import com.ls.ner.billing.gift.service.IRechargeCardService;
import com.ls.ner.def.api.account.service.IDefRechargeCardRpcService;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.service.impl
 * @ClassName: RechargeCardServiceImpl
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/4/27 14:28
 * @Version: 1.0
 */
@Service(target = { ServiceType.LOCAL }, value = "rechargeCardService")
public class RechargeCardServiceImpl implements IRechargeCardService {

    @Autowired
    private IRechargeCardDao iRechargeCardDao;

    @ServiceAutowired
    Authentication authentication;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="defRechargeCardDefRpcService")
    private IDefRechargeCardRpcService iDefRechargeCardRpcService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="defRechargeCardDefRpcService")
    private IMarketActRpcService marketActRpcService;

    // 纯数字
    private static final String SYMBOLS = "0123456789";

    // 字符集中去掉0，用于首位和末位
    private static final String SYMBOLS_WITHOUT_ZERO = "123456789";

    private static final String BASE = "wqetryuioplkjhgfdsazxcvbmnABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    private static final Random RANDOM = new SecureRandom();

    private static final Logger logger = LoggerFactory.getLogger(RechargeCardServiceImpl.class);


    @Override
    public List<RechargeCardBo> listRechargeCard(RechargeCardCondition rechargeCardCondition) {
        List<RechargeCardBo> list = iRechargeCardDao.listRechargeCard(rechargeCardCondition);
        List<RechargeCardOrder> cardOrders = new ArrayList<>();
        Map<Long, RechargeCardOrder> cardOrderMaps = new HashMap<>();
        if(StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeBegin()) && StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeEnd())){
            RechargeCardOrder rechargeCardOrder = new RechargeCardOrder();
            rechargeCardOrder.setSettleTimeBegin(rechargeCardCondition.getSettleTimeBegin());
            rechargeCardOrder.setSettleTimeEnd(rechargeCardCondition.getSettleTimeEnd());
            cardOrders = iRechargeCardDao.queryCardOrderGroupByCardId(rechargeCardOrder);
            if(CollectionUtils.isNotEmpty(cardOrders)){
                cardOrderMaps = cardOrders.stream().collect(Collectors.toMap(RechargeCardOrder::getCardId, Function.identity()));
            }
        }

        // 使用时间即为订单结算时间（但是订单结算那边update order settleTime的时候 sql用的是now() 直接更新的 所以这里偷懒直接b_recharge_card_order表里面的updateTime 最多比settleTime快1-2s 无伤大雅）
        // 使用时间只能影响核销金额 其他不影响
        for(RechargeCardBo bo : list){
            if(StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeBegin()) && StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeEnd())){
                if(cardOrderMaps !=null && cardOrderMaps.containsKey(bo.getCardId())){
                    bo.setTotalClearCount(cardOrderMaps.get(bo.getCardId()).getActualAmt().add(cardOrderMaps.get(bo.getCardId()).getGiftAmt()));
                    bo.setGiftClearCount(cardOrderMaps.get(bo.getCardId()).getGiftAmt());
                    bo.setRealClearCount(cardOrderMaps.get(bo.getCardId()).getActualAmt());
                }
            }
        }
        return list;
    }

    @Override
    public int countRechargeCard(RechargeCardCondition rechargeCardCondition) {
        return iRechargeCardDao.countRechargeCard(rechargeCardCondition);
    }

    @Override
    public List<RechargeCardDetail> queryDetails(RechargeCardCondition rechargeCardCondition) {
        List<RechargeCardDetail> list = iRechargeCardDao.queryDetails(rechargeCardCondition);

        List<RechargeCardOrder> cardOrders = new ArrayList<>();
        Map<Long, RechargeCardOrder> cardOrderMaps = new HashMap<>();
        if(StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeBegin()) && StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeEnd())){
            RechargeCardOrder rechargeCardOrder = new RechargeCardOrder();
            rechargeCardOrder.setSettleTimeBegin(rechargeCardCondition.getSettleTimeBegin());
            rechargeCardOrder.setSettleTimeEnd(rechargeCardCondition.getSettleTimeEnd());
            cardOrders = iRechargeCardDao.queryCardOrderGroupByCardDetailId(rechargeCardOrder);
            if(CollectionUtils.isNotEmpty(cardOrders)){
                cardOrderMaps = cardOrders.stream().collect(Collectors.toMap(RechargeCardOrder::getCardDetailId, Function.identity()));
            }
        }

        for(RechargeCardDetail bo : list){
            if(StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeBegin()) && StringUtils.isNotEmpty(rechargeCardCondition.getSettleTimeEnd())){
                if(cardOrderMaps !=null && cardOrderMaps.containsKey(bo.getCardDetailId())){
                    bo.setTotalClearCount(cardOrderMaps.get(bo.getCardDetailId()).getActualAmt().add(cardOrderMaps.get(bo.getCardDetailId()).getGiftAmt()));
                    bo.setGiftClearCount(cardOrderMaps.get(bo.getCardDetailId()).getGiftAmt());
                    bo.setRealClearCount(cardOrderMaps.get(bo.getCardDetailId()).getActualAmt());
                }
            }
        }

        return list;
    }

    @Override
    public int queryDetailsCount(RechargeCardCondition rechargeCardCondition) {
        return iRechargeCardDao.queryDetailsCount(rechargeCardCondition);
    }

    @Override
    public List<RechargeCardOrder> qryCardOrderDetail(RechargeCardCondition rechargeCardCondition) {
        return iRechargeCardDao.qryCardOrderDetail(rechargeCardCondition);
    }

    @Override
    public int queryCardOrderDetailCount(RechargeCardCondition rechargeCardCondition) {
        return iRechargeCardDao.queryCardOrderDetailCount(rechargeCardCondition);
    }

    @Override
    public void addRechargeCard(RechargeCardBo rechargeCardBo) {
        rechargeCardBo.setCardBatchNo(createPrepaidCardBatchNo());
        if (rechargeCardBo == null || StringUtils.isEmpty(rechargeCardBo.getCardBatchNo()) ) {
            throw new RuntimeException("批次号非有效字段");
        }

        if(rechargeCardBo.getCardCount() == null || rechargeCardBo.getCardCount() <= 0){
            throw new RuntimeException("制卡数量不应为空");
        }

        if("2".equals(rechargeCardBo.getCardTimeType())){
            if(StringUtils.isEmpty(rechargeCardBo.getEftDate()) || StringUtils.isEmpty(rechargeCardBo.getInvDate())){
                throw new RuntimeException("兑换有效期不应为空");
            }
        }

        RechargeCardBo queryOne = new RechargeCardBo();
        queryOne.setCardName(rechargeCardBo.getCardName());
        queryOne.setCardStatus("1");
        RechargeCardBo queryRechargeCard = iRechargeCardDao.queryRechargeCard(queryOne);
        if(queryRechargeCard != null){
            throw new RuntimeException("储值卡:"+rechargeCardBo.getCardName()+"已经存在");
        }

        AccountBo currentAccount = authentication.getCurrentAccount();
        rechargeCardBo.setCreateUser(currentAccount.getAccountName());

        //插入主表
        rechargeCardBo.setCardStatus("1");
        iRechargeCardDao.addRechargeCard(rechargeCardBo);
        //卡密长度 默认12
        String codeNum = sysParamService.getSysParamsValues("rechargeCardCodeNum");
        if(StringUtils.isEmpty(codeNum)){
            codeNum = "12";
        }

        List<RechargeCardDetail> rechargeCardDetails = generateBatchCard(rechargeCardBo.getCardBatchNo(),rechargeCardBo.getCardCount(),Integer.valueOf(codeNum));
        if (CollectionUtils.isEmpty(rechargeCardDetails)) {
            throw new RuntimeException("批量生成储值卡卡号卡密失败");
        }

        for (RechargeCardDetail detail : rechargeCardDetails) {
            buildAccountPrepaidCard(detail,rechargeCardBo);
        }

        iRechargeCardDao.batchAddRechargeCardDetail(rechargeCardDetails);

    }

    @Override
    public void updateRechargeCard(RechargeCardBo rechargeCardBo) {
        AccountBo currentAccount = authentication.getCurrentAccount();
        RechargeCardBo queryOne = new RechargeCardBo();
        queryOne.setCardName(rechargeCardBo.getCardName());
        queryOne.setCardStatus("1");
        queryOne.setUpCardId(rechargeCardBo.getCardId());
        RechargeCardBo queryRechargeCard = iRechargeCardDao.queryRechargeCard(queryOne);
        if(queryRechargeCard != null){
            throw new RuntimeException("储值卡:"+rechargeCardBo.getCardName()+"已经存在");
        }
        rechargeCardBo.setUpdateUser(currentAccount.getAccountName());
        iRechargeCardDao.updateRechargeCard(rechargeCardBo);
    }

    @Override
    public void addBatchNoCard(RechargeCardBo rechargeCardBo) {
        RechargeCardBo queryOne = new RechargeCardBo();
        queryOne.setCardId(rechargeCardBo.getCardId());
        RechargeCardBo queryRechargeCard = iRechargeCardDao.queryRechargeCard(queryOne);


        String codeNum = sysParamService.getSysParamsValues("rechargeCardCodeNum");
        if(StringUtils.isEmpty(codeNum)){
            codeNum = "12";
        }

        List<RechargeCardDetail> rechargeCardDetails = generateBatchCard(queryRechargeCard.getCardBatchNo(),rechargeCardBo.getCardCount(),Integer.valueOf(codeNum));
        if (CollectionUtils.isEmpty(rechargeCardDetails)) {
            throw new RuntimeException("批量生成储值卡卡号卡密失败");
        }

        for (RechargeCardDetail detail : rechargeCardDetails) {
            buildAccountPrepaidCard(detail,queryRechargeCard);
        }
        iRechargeCardDao.batchAddRechargeCardDetail(rechargeCardDetails);

    }

    @Override
    public RechargeCardBo queryDetail(RechargeCardBo rechargeCardBo) {
        RechargeCardBo queryOne = new RechargeCardBo();
        queryOne.setCardId(rechargeCardBo.getCardId());
        RechargeCardBo queryRechargeCard = iRechargeCardDao.queryRechargeCard(queryOne);
        return queryRechargeCard;
    }

    @Override
    public void updateCardStatus(RechargeCardBo rechargeCardBo) {
        iRechargeCardDao.updateRechargeCard(rechargeCardBo);
    }

    @Override
    public void updateCardDetailStatus(RechargeCardDetail rechargeCardDetail) {
        RechargeCardDetail queryOne = iRechargeCardDao.queryCardDetailsByOne(rechargeCardDetail);
        /** 实际余额 */
        BigDecimal actualBalance = queryOne.getActualBalance();
        /** 赠送部分余额 */
        BigDecimal giftBalance = queryOne.getGiftBalance();
        //解冻
        if("unFreeze".equals(rechargeCardDetail.getCardDetailStatus())){
            if(StringUtils.isNotEmpty(queryOne.getMobile())){
                //如果有手机号说明解冻之前已经绑定则 更新为已激活状态 反之则待激活状态
                rechargeCardDetail.setCardDetailStatus("1");
            }else{
                rechargeCardDetail.setCardDetailStatus("0");
            }
            try {
                //且对用户充值卡实际 进行解冻
                if(!BigDecimal.ZERO.equals(actualBalance)){
                    Map<String, Object> actualAmtMap = new HashMap<>();
                    actualAmtMap.put("mobile",queryOne.getMobile());
                    actualAmtMap.put("payAmount",actualBalance);
                    actualAmtMap.put("accType","10");//标准代码accType 10	充值卡-实际
                    iDefRechargeCardRpcService.doThaw(actualAmtMap);
                }


                //且对用户充值卡赠送 进行解冻
                if(!BigDecimal.ZERO.equals(giftBalance)){
                    Map<String, Object> giftAmtMap = new HashMap<>();
                    giftAmtMap.put("mobile",queryOne.getMobile());
                    giftAmtMap.put("payAmount",giftBalance);
                    giftAmtMap.put("accType","11");//标准代码accType 11	充值卡-赠送
                    iDefRechargeCardRpcService.doThaw(giftAmtMap);
                }
            }catch (Exception e){
                logger.error("充值卡解冻失败：{}",e);
            }
        }
        //2已冻结
        if("2".equals(rechargeCardDetail.getCardDetailStatus())){
            try {
                //且对用户充值卡实际 进行冻结
                if(!BigDecimal.ZERO.equals(actualBalance)){
                    Map<String, Object> actualAmtMap = new HashMap<>();
                    actualAmtMap.put("mobile",queryOne.getMobile());
                    actualAmtMap.put("payAmount",actualBalance);
                    actualAmtMap.put("accType","10");//标准代码accType 10	充值卡-实际
                    iDefRechargeCardRpcService.doFrozen(actualAmtMap);
                }

                //且对用户充值卡赠送 进行冻结
                if(!BigDecimal.ZERO.equals(giftBalance)){
                    Map<String, Object> giftAmtMap = new HashMap<>();
                    giftAmtMap.put("mobile",queryOne.getMobile());
                    giftAmtMap.put("payAmount",giftBalance);
                    giftAmtMap.put("accType","11");//标准代码accType 11	充值卡-赠送
                    iDefRechargeCardRpcService.doFrozen(giftAmtMap);
                }
            }catch (Exception e){
                logger.error("充值卡冻结失败：{}",e);
            }
        }
        //取消退款
        if("unRefund".equals(rechargeCardDetail.getCardDetailStatus())){
            rechargeCardDetail.setCardDetailStatus("2");
        }
        iRechargeCardDao.updateCardDetailStatus(rechargeCardDetail);
    }

    @Override
    public Map<String, Object> rechargeCardCode(Map<String, String> map) {
        logger.info("充值卡兑换卡密入参：{}", JSON.toJSONString(map));
        Map<String, Object> result = new HashMap<>();
        String custId = StringUtil.nullForString(map.get("custId"));
        String cardSecret = StringUtil.nullForString(map.get("cardSecret"));
        String mobile = StringUtil.nullForString(map.get("mobile"));
        if(StringUtils.isEmpty(custId) || StringUtils.isEmpty(mobile)){
            result.put("ret",400);
            result.put("msg","用户信息不能为空");
            return result;
        }

        if(StringUtils.isEmpty(cardSecret)){
            result.put("ret",400);
            result.put("msg","卡密不能为空");
            return result;
        }

        String codeNum = sysParamService.getSysParamsValues("rechargeCardCodeNum");
        if(StringUtils.isEmpty(codeNum)){
            codeNum = "12";
        }

        String CARD_REGEX = "^[wqetryuioplkjhgfdsazxcvbmnABCDEFGHIJKLMNOPQRSTUVWXYZ0-9]{"+codeNum+"}$";


        if(!cardSecret.matches(CARD_REGEX)){
            result.put("ret",400);
            result.put("msg","您的输入有误");
            return result;
        }
        RechargeCardDetail queryOne = new RechargeCardDetail();
        queryOne.setCardSecret(cardSecret);
        RechargeCardDetail rechargeCardDetail = iRechargeCardDao.queryRechargeCardCodeByOne(queryOne);
        if(rechargeCardDetail == null){
            result.put("ret",400);
            result.put("msg","您的输入有误");
            return result;
        }else{
            if("2".equals(rechargeCardDetail.getCardDetailStatus())){
                result.put("ret",400);
                result.put("msg","您的卡已被冻结，请联系客服处理");
                return result;
            }

            if("2".equals(rechargeCardDetail.getCardTimeType())){
                // 定义时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                // 定义起始和结束时间
                LocalDateTime start = LocalDateTime.parse(rechargeCardDetail.getEftDate(), formatter);
                LocalDateTime end = LocalDateTime.parse(rechargeCardDetail.getInvDate(), formatter);
                // 获取当前时间
                LocalDateTime now = LocalDateTime.now();

                // 判断当前时间是否在时间范围内
                if (now.isBefore(start) || now.isAfter(end)) {
                    result.put("ret",400);
                    result.put("msg","批次卡到有效期无法兑换");
                    return result;
                }
            }

            //绑定对应卡密和用户
            rechargeCardDetail.setCustId(custId);
            rechargeCardDetail.setMobile(mobile);
            rechargeCardDetail.setCardDetailStatus("1");
            iRechargeCardDao.updateCardDetailCustId(rechargeCardDetail);

            try {
                //且对用户余额进行添加   充值卡实际金额
                Map<String, Object> actualAmtMap = new HashMap<>();
                actualAmtMap.put("mobile",mobile);
                actualAmtMap.put("reqTyp",StringUtil.nullForString(map.get("reqTyp")));
                actualAmtMap.put("payAmount",rechargeCardDetail.getActualAmt());
                actualAmtMap.put("accType","10");//标准代码accType 10	充值卡-实际
                iDefRechargeCardRpcService.doCharge(actualAmtMap);

                //且对用户余额进行添加   充值卡赠送金额
                Map<String, Object> giftAmtMap = new HashMap<>();
                giftAmtMap.put("mobile",mobile);
                giftAmtMap.put("reqTyp",StringUtil.nullForString(map.get("reqTyp")));
                giftAmtMap.put("payAmount",rechargeCardDetail.getGiftAmt());
                giftAmtMap.put("accType","11");//标准代码accType 11	充值卡-赠送
                iDefRechargeCardRpcService.doCharge(giftAmtMap);

                // 新增：赠送积分逻辑
                RechargeCardBo bo = new RechargeCardBo();
                bo.setCardId(rechargeCardDetail.getCardId());
                RechargeCardBo cardBo = iRechargeCardDao.queryRechargeCard(bo);
                if (cardBo != null && StringUtils.isNotBlank(cardBo.getIntegralNum())) {
                    int integralNum = 0;
                    try {
                        integralNum = Integer.parseInt(cardBo.getIntegralNum());
                    } catch (Exception e) {
                        logger.error("赠送积分数量转换异常：", e);
                    }
                    if (integralNum > 0) {
                        Map<String, Object> integralMap = new HashMap<>();
                        integralMap.put("custId", custId);
                        integralMap.put("mobile", mobile);
                        integralMap.put("integralNum", String.valueOf(integralNum));
                        integralMap.put("eventName", "充值卡赠送积分");
                        integralMap.put("eventType", "09");
                        marketActRpcService.aysncCustIntegral(integralMap);
                    }
                }
            }catch (Exception e){
                result.put("ret", PublicConstants.returnCode.RET_400);
                result.put("msg",e.getMessage());
                return result;
            }
        }

        result.put("msg", "兑换成功");
        result.put("ret", 200);
        return result;
    }

    @Override
    public Map<String, Object> rechargeCardCodeByIntegral(Map<String, String> map) {
        logger.info("积分兑换充值卡入参：{}", JSON.toJSONString(map));
        Map<String, Object> result = new HashMap<>();
        String custId = StringUtil.nullForString(map.get("custId"));
        String cardId = StringUtil.nullForString(map.get("cardId"));
        String mobile = StringUtil.nullForString(map.get("mobile"));

        RechargeCardBo condition = new RechargeCardBo();
        if(StringUtils.isEmpty(cardId)){
            result.put("ret",400);
            result.put("msg","卡ID不能为空");
            return result;
        }
        condition.setCardId(Long.valueOf(cardId));
        condition.setCardStatus("1");
        RechargeCardBo bo = iRechargeCardDao.queryRechargeCard(condition);
        logger.info("充值卡详情：{}", JSON.toJSONString(bo));
        if(bo==null){
            result.put("ret",400);
            result.put("msg","此充值卡无效");
            return result;
        }
        if("2".equals(bo.getCardTimeType())){
            // 定义时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 定义起始和结束时间
            LocalDateTime start = LocalDateTime.parse(bo.getEftDate(), formatter);
            LocalDateTime end = LocalDateTime.parse(bo.getInvDate(), formatter);
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 判断当前时间是否在时间范围内
            if (now.isBefore(start) || now.isAfter(end)) {
                result.put("ret",400);
                result.put("msg","批次卡到有效期无法兑换");
                return result;
            }
        }

        //积分兑换的要新增一条自己的卡号卡密  不能用 后管批量生成的（这是属于线下的）
        String codeNum = sysParamService.getSysParamsValues("rechargeCardCodeNum");
        if(StringUtils.isEmpty(codeNum)){
            codeNum = "12";
        }

        List<RechargeCardDetail> rechargeCardDetails = generateBatchCard(bo.getCardBatchNo(),1,Integer.valueOf(codeNum));
        if (CollectionUtils.isEmpty(rechargeCardDetails)) {
            result.put("ret",400);
            result.put("msg","积分兑换生成储值卡卡号卡密失败");
            return result;
        }

        for (RechargeCardDetail detail : rechargeCardDetails) {
            buildAccountIntegralCard(detail,bo,custId,mobile);
        }
        iRechargeCardDao.batchAddRechargeCardDetailIntegral(rechargeCardDetails);

        try {
            //且对用户余额进行添加   充值卡实际金额
            Map<String, Object> actualAmtMap = new HashMap<>();
            actualAmtMap.put("mobile",mobile);
            actualAmtMap.put("reqTyp",StringUtil.nullForString(map.get("reqTyp")));
            actualAmtMap.put("payAmount",bo.getActualAmt());
            actualAmtMap.put("accType","10");//标准代码accType 10	充值卡-实际
            iDefRechargeCardRpcService.doCharge(actualAmtMap);

            //且对用户余额进行添加   充值卡赠送金额
            Map<String, Object> giftAmtMap = new HashMap<>();
            giftAmtMap.put("mobile",mobile);
            giftAmtMap.put("reqTyp",StringUtil.nullForString(map.get("reqTyp")));
            giftAmtMap.put("payAmount",bo.getGiftAmt());
            giftAmtMap.put("accType","11");//标准代码accType 11	充值卡-赠送
            iDefRechargeCardRpcService.doCharge(giftAmtMap);
            // 新增：赠送积分逻辑
            if (StringUtils.isNotBlank(bo.getIntegralNum())) {
                int integralNum = 0;
                try {
                    integralNum = Integer.parseInt(bo.getIntegralNum());
                    String num = StringUtil.nullForString(map.get("num"));
                    if (StringUtil.isNotBlank(num) && Integer.parseInt(num) > 0) {
                        integralNum = integralNum * Integer.parseInt(num);
                    }
                } catch (Exception e) {
                    logger.error("赠送积分数量转换异常：", e);
                }
                if (integralNum > 0) {
                    Map<String, Object> integralMap = new HashMap<>();
                    integralMap.put("custId", custId);
                    integralMap.put("mobile", mobile);
                    integralMap.put("integralNum", String.valueOf(integralNum));
                    integralMap.put("eventName", "充值卡赠送积分");
                    integralMap.put("eventType", "09");
                    marketActRpcService.aysncCustIntegral(integralMap);
                    logger.info("充值卡积分赠送成功");
                    // 返回赠送的积分数量给调用方
                    result.put("integralNum", integralNum);
                }
            }
        }catch (Exception e){
            result.put("ret", PublicConstants.returnCode.RET_400);
            result.put("msg",e.getMessage());
            return result;
        }

        result.put("msg", "兑换成功");
        result.put("ret", 200);
        return result;
    }



    @Override
    public Map<String, Object> myRechargeCardInfos(Map<String, String> map) {
        logger.info("我的充值卡入参：{}", JSON.toJSONString(map));
        Map<String, Object> result = new HashMap<>();
        String custId = StringUtil.nullForString(map.get("custId"));
        if(StringUtils.isEmpty(custId)){
            result.put("ret",400);
            result.put("msg","用户信息不能为空");
            return result;
        }
        RechargeCardDetail condition = new RechargeCardDetail();
        condition.setCustId(custId);
        List<RechargeCardDetail> myRechargeCardInfos = iRechargeCardDao.myRechargeCardInfos(condition);
        BigDecimal amt = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(myRechargeCardInfos)){
            amt = myRechargeCardInfos.stream().filter(entity ->
                   "1".equals( entity.getCardDetailStatus())
            ) .map(entity ->
                            Optional.ofNullable(entity.getTotalBalance()).orElse(BigDecimal.ZERO)
                    )
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        result.put("ret",200);
        result.put("amt",amt);
        result.put("myCards",myRechargeCardInfos);
        return result;
    }

    @Override
    public List<Map<String, String>> getRechargeCardList() {
        List<Map<String, String>> list = iRechargeCardDao.getRechargeCardList();
        if(CollectionUtils.isNotEmpty(list)){
            for (Iterator<Map<String, String>> iterator = list.iterator(); iterator.hasNext();) {
                Map<String, String> cardList = iterator.next();


                if ("2".equals(cardList.get("cardTimeType"))) {
                    // 定义时间格式
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    // 定义起始和结束时间
                    LocalDateTime start = LocalDateTime.parse(cardList.get("eftDate"), formatter);
                    LocalDateTime end = LocalDateTime.parse(cardList.get("invDate"), formatter);
                    // 获取当前时间
                    LocalDateTime now = LocalDateTime.now();
                    if (now.isBefore(start) || now.isAfter(end)) {
                        iterator.remove(); // 通过迭代器的安全移除方法
                    }
                }

            }
        }
        return list;
    }

    @Override
    public void updateRechargeCardByOrder(Map<String, String> map) throws Exception {
        //比如 一个充电订单结算过来  这个时候此用户有a,b两张充值卡 赠送金额a不够扣 就继续扣b的赠送金额  然后实际金额就正常扣  不够扣就继续下一张 （理论上传过来的充值卡消费金额均不会超过充值卡的余额 因为在def里面已经计算完成了）
        logger.info("订单结算返回更新充值卡入参：{}", JSON.toJSONString(map));
        String custId = StringUtil.nullForString(map.get("custId"));
        //充电订单扣的充值卡赠送
        String cardGiftAmtG = StringUtil.nullForString(map.get("cardGiftAmtG"));
        //充电订单扣的充值卡实际
        String cardGiftAmtR = StringUtil.nullForString(map.get("cardGiftAmtR"));
        BigDecimal cardGiftAmtG_B = BigDecimal.ZERO;
        BigDecimal cardGiftAmtR_B = BigDecimal.ZERO;
        if(StringUtils.isNotEmpty(cardGiftAmtG)){
            cardGiftAmtG_B = new BigDecimal(cardGiftAmtG);
        }
        if(StringUtils.isNotEmpty(cardGiftAmtR)){
            cardGiftAmtR_B = new BigDecimal(cardGiftAmtR);
        }

        String orderNo = StringUtil.nullForString(map.get("orderNo"));
        if(StringUtils.isEmpty(custId)){
            throw new RuntimeException("用户信息custId不能为空");
        }
        RechargeCardCondition condition = new RechargeCardCondition();
        condition.setCustId(custId);
        condition.setCardDetailStatus("1");
        List<RechargeCardDetail> queryDetails = iRechargeCardDao.queryDetailsOrderByActualBalance(condition);

        if(CollectionUtils.isNotEmpty(queryDetails)){
            for(RechargeCardDetail detail : queryDetails){
                BigDecimal giftBalance = detail.getGiftBalance();
                BigDecimal actualBalance = detail.getActualBalance();

                RechargeCardOrder rechargeCardOrder = new RechargeCardOrder();
                rechargeCardOrder.setOrderNo(orderNo);
                rechargeCardOrder.setCardDetailId(detail.getCardDetailId());
                rechargeCardOrder.setCardId(detail.getCardId());
                rechargeCardOrder.setCardSource(detail.getCardSource());

                if(cardGiftAmtG_B.compareTo(BigDecimal.ZERO) > 0){
                    if(cardGiftAmtG_B.compareTo(giftBalance) <= 0){
                        //余额够扣
                        giftBalance = giftBalance.subtract(cardGiftAmtG_B);
                        rechargeCardOrder.setGiftAmt(cardGiftAmtG_B);
                        cardGiftAmtG_B = BigDecimal.ZERO;
                    }else{
                        //余额不够扣
                        rechargeCardOrder.setGiftAmt(giftBalance);
                        cardGiftAmtG_B = cardGiftAmtG_B.subtract(giftBalance);
                        giftBalance = BigDecimal.ZERO;

                    }
                }
                if(cardGiftAmtR_B.compareTo(BigDecimal.ZERO) > 0){
                    if(cardGiftAmtR_B.compareTo(actualBalance) <= 0){
                        //余额够扣
                        actualBalance = actualBalance.subtract(cardGiftAmtR_B);
                        rechargeCardOrder.setActualAmt(cardGiftAmtR_B);
                        cardGiftAmtR_B = BigDecimal.ZERO;
                    }else{
                        //余额不够扣
                        rechargeCardOrder.setActualAmt(actualBalance);
                        cardGiftAmtR_B = cardGiftAmtR_B.subtract(actualBalance);
                        actualBalance = BigDecimal.ZERO;

                    }
                }
                //更新充值卡余额
                RechargeCardDetail updateDetail = new RechargeCardDetail();
                updateDetail.setCardDetailId(detail.getCardDetailId());
                updateDetail.setGiftBalance(giftBalance);
                updateDetail.setActualBalance(actualBalance);
                if(BigDecimal.ZERO.compareTo(giftBalance) == 0 && BigDecimal.ZERO.compareTo(actualBalance) == 0){
                    updateDetail.setCardDetailStatus("4");
                }
                iRechargeCardDao.updateCardDetailAmt(updateDetail);

                //插入订单消费了充值卡明细表
                iRechargeCardDao.insertRechargeCardOrder(rechargeCardOrder);
            }

        }else{
            throw new RuntimeException( custId + "此用户没有查询到有效的充值卡信息");
        }
    }

    @Override
    public List<String> cardOrderNoList(String cardId, String cardDetailId) {
        return iRechargeCardDao.cardOrderNoList(cardId, cardDetailId);
    }

    @Override
    public List<Map<String, Object>> queryCardOrderByOrderNo(Map<String, Object> map) {
        return iRechargeCardDao.queryCardOrderByOrderNo(map);
    }

    @Override
    public List<Map<String, Object>> queryCardInfosByChargeOrderList(Map<String, Object> map) {
        return iRechargeCardDao.queryCardInfosByChargeOrderList(map);
    }

    @Override
    public List<String> getOrderListByCardNo(Map<String, Object> map) {
        return iRechargeCardDao.getOrderListByCardNo(map);
    }


    private RechargeCardDetail buildAccountPrepaidCard(RechargeCardDetail detail, RechargeCardBo rechargeCardBo) {
        detail.setActualAmt(rechargeCardBo.getActualAmt());
        detail.setCardId(rechargeCardBo.getCardId());
        detail.setGiftAmt(rechargeCardBo.getGiftAmt());
        detail.setCardDetailStatus("0");
        detail.setCreateUser(rechargeCardBo.getCreateUser());
        detail.setCardSource("0");
        detail.setActualBalance(rechargeCardBo.getActualAmt());
        detail.setGiftBalance(rechargeCardBo.getGiftAmt());
        detail.setGiftAmt(rechargeCardBo.getGiftAmt());
        return detail;
    }

    private RechargeCardDetail buildAccountIntegralCard(RechargeCardDetail detail, RechargeCardBo rechargeCardBo,String custId,String mobile) {
        detail.setActualAmt(rechargeCardBo.getActualAmt());
        detail.setCardId(rechargeCardBo.getCardId());
        detail.setGiftAmt(rechargeCardBo.getGiftAmt());
        detail.setCardDetailStatus("1");
        detail.setCreateUser(rechargeCardBo.getCreateUser());
        detail.setCardSource("1");
        detail.setActualBalance(rechargeCardBo.getActualAmt());
        detail.setGiftBalance(rechargeCardBo.getGiftAmt());
        detail.setGiftAmt(rechargeCardBo.getGiftAmt());
        detail.setCustId(custId);
        detail.setMobile(mobile);
        return detail;
    }

        /**
         * 生成6位储值卡批次号
         *
         * @return
         */
    public static String createPrepaidCardBatchNo() {
        char[] nonceChars = new char[6];

        // 首位确保不为0
        nonceChars[0] = SYMBOLS_WITHOUT_ZERO.charAt(RANDOM.nextInt(SYMBOLS_WITHOUT_ZERO.length()));

        // 中间四位可以是0-9中的任意数字
        for (int index = 1; index < 5; ++index) {
            nonceChars[index] = SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length()));
        }

        // 末位确保不为0
        nonceChars[5] = SYMBOLS_WITHOUT_ZERO.charAt(RANDOM.nextInt(SYMBOLS_WITHOUT_ZERO.length()));

        return new String(nonceChars);
    }


    /**
     * 按批次号批量生成卡号、卡密
     *
     * @param batchNo 批次号
     * @param num     数量
     * @return  卡号,卡密
     */
    public static List<RechargeCardDetail> generateBatchCard(String batchNo, Integer num ,Integer codeNum) {
        if (batchNo == null || num == null) {
            return null;
        }

        if (num <= 0 || batchNo.length() != 6) {
            return null;
        }

        char[] nonceChars = new char[5];
        StringBuffer key;
        StringBuffer password;

        List<RechargeCardDetail> res = Lists.newArrayList();
        for (Integer i = 0; i < num; i++) {
            RechargeCardDetail cardBO = new RechargeCardDetail();
            key = new StringBuffer(batchNo);
            key = key.append(getTimeStr());
            password = new StringBuffer();

            for (int index = 0; index < nonceChars.length; ++index) {
                nonceChars[index] = SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length()));
            }
            key = key.append(nonceChars);



            for (int j = 0; j < codeNum; j++) {
                password.append(BASE.charAt(RANDOM.nextInt(BASE.length())));
            }

            cardBO.setCardNo(key.toString());
            cardBO.setCardSecret(password.toString());
            cardBO.setCardBatchNo(batchNo);

            res.add(cardBO);
        }

        return res;
    }

    private static String getTimeStr() {
        LocalDateTime now = LocalDateTime.now();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmmss");
        // 格式化当前时间
        return now.format(formatter);
    }
}
