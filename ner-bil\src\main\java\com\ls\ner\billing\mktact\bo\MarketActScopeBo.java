package com.ls.ner.billing.mktact.bo;

import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.util.List;

public class MarketActScopeBo extends QueryCondition {
	/**
 	* 主键id 自增
 	*/
	private String scopeId;
	/**
	  * 活动ID
	  */
	private String actId;

	private String actType;
	/**
	  * 运营商id
	  */
	private String buildId;
	/**
	  * 省份code
	  */
	private String province;
	/**
	  * 城市code
	  */
	private String city;
	/**
	  * 站点id
	  */
	private String stationId;
	/**
	  * 适用类型：标准代码---1所有全部  2运营商部分(城市站点全部)  3城市部分(运营商部分站点全部)  4站点部分(运营商城市部分)
	  */
	private String scopeType;

	public String getScopeId() {
		return scopeId;
	}

	public void setScopeId(String scopeId) {
		this.scopeId = scopeId;
	}

	public String getActId() {
		return actId;
	}

	public void setActId(String actId) {
		this.actId = actId;
	}

	public String getActType() {
		return actType;
	}

	public void setActType(String actType) {
		this.actType = actType;
	}

	public String getBuildId() {
		return buildId;
	}

	public void setBuildId(String buildId) {
		this.buildId = buildId;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getScopeType() {
		return scopeType;
	}

	public void setScopeType(String scopeType) {
		this.scopeType = scopeType;
	}
}
