<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充值卡配置">
    <script type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
    <ls:form id="searchForm" name="searchForm">
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="充值卡名称"/></td>
                <td><ls:text name="cardName"></ls:text></td>
                <td>
                    <ls:label text="充值卡批次号"/>
                </td>
                <td>
                    <ls:text name="cardBatchNo"></ls:text>
                </td>
                <td>
                    <ls:label text="领取状态" ></ls:label>
                </td>
                <td>
                    <ls:select name="cardStatus">
                        <ls:option value="1" text="启用"></ls:option>
                        <ls:option value="0" text="禁用"></ls:option>
                    </ls:select>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="创建时间"></ls:label>
                </td>
                <td>
                    <table>
                        <tr>
                            <td>
                                <ls:date name="createTimeBegin" format="yyyy-MM-dd"/>
                            </td>
                            <td>
                                <span>至</span>
                            </td>
                            <td>
                                <ls:date name="createTimeEnd" format="yyyy-MM-dd"/>
                            </td>
                        </tr>
                    </table>
                </td>
                <td>
                    <ls:label text="使用时间"></ls:label>
                </td>
                <td>
                    <table>
                        <tr>
                            <td>
                                <ls:date name="settleTimeBegin" format="yyyy-MM-dd"/>
                            </td>
                            <td>
                                <span>至</span>
                            </td>
                            <td>
                                <ls:date name="settleTimeEnd" format="yyyy-MM-dd"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="6">
                    <div class="pull-right">
                        <ls:button text="查询" onclick="doSearch"/>
                        <ls:button text="清空" onclick="doClear"/>
                    </div>
                </td>
            </tr>
        </table>
        <table  align="center" class="tab_search">
            <tr>
                <td>
                    <ls:grid url="" caption="充值卡列表" showRowNumber="false" name="carGiftGrid" height="400px" showCheckBox="false"
                             allowScroll="true" exportFilename="领取列表记录">
                        <ls:gridToolbar name="cardGiftBar">
                            <ls:gridToolbarItem name="add" text="新增" imageKey="add" onclick="addCard" ></ls:gridToolbarItem>
                            <ls:gridToolbarItem name="modify" text="修改" imageKey="edit" onclick="modifyCard"></ls:gridToolbarItem>
                            <ls:gridToolbarItem name="addCards" text="增加卡量" imageKey="add" onclick="addCards" ></ls:gridToolbarItem>
                            <ls:gridToolbarItem name="detailCard" text="详情" imageKey="search" onclick="detail" ></ls:gridToolbarItem>
                            <ls:gridToolbarItem imageKey="start" onclick="doEnable" text="启用"></ls:gridToolbarItem>
                            <ls:gridToolbarItem imageKey="stop" onclick="doDisable" text="停用"></ls:gridToolbarItem>
<%--                            <ls:gridToolbarItem name="export_data" text="导出" imageKey="add" onclick="exportData"></ls:gridToolbarItem>--%>
                        </ls:gridToolbar>
                        <ls:column caption="充值卡Id" name="cardId"  align="center" hidden="true"></ls:column>
                        <ls:column caption="充值卡名称" name="cardName"  align="center"  formatFunc="linkFunc" ></ls:column>
                        <ls:column caption="充值卡批次号" name="cardBatchNo" align="center"></ls:column>
                        <ls:column caption="总面值" name="totalAmt" align="center"></ls:column>
                        <ls:column caption="实际面值" name="actualAmt" align="center"></ls:column>
                        <ls:column caption="赠送部分面值" name="giftAmt" align="center"></ls:column>
                        <ls:column caption="总卡量" name="totalCardCount" align="center"></ls:column>
                        <ls:column caption="有余额卡量" name="remainingCardCount" align="center"></ls:column>
                        <ls:column caption="已用完卡量" name="usedUpCardCount" align="center"></ls:column>
                        <ls:column caption="总核销金额" name="totalClearCount" align="center"></ls:column>
                        <ls:column caption="实际核销金额" name="realClearCount" align="center"></ls:column>
                        <ls:column caption="赠送部分核销金额" name="giftClearCount" align="center"></ls:column>
                        <ls:column caption="总余额" name="totalBalance" align="center"></ls:column>
                        <ls:column caption="实际余额" name="actualBalance" align="center"></ls:column>
                        <ls:column caption="赠送部分余额" name="giftBalance" align="center"></ls:column>
                        <ls:column caption="状态" name="cardStatusName" align="center"></ls:column>
                        <ls:column caption="创建人" name="createUser" align="center"></ls:column>
                        <ls:column caption="创建时间" name="createTime" align="center"></ls:column>
                        <ls:pager pageSize="15" pageIndex="1"></ls:pager>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        function doClear(){
            searchForm.clear();
        }
        //查询
        window.doSearch = doSearch;
        doSearch();
        function doSearch() {
        if(!LS.isEmpty(createTimeBegin.getValue()) && !LS.isEmpty(createTimeEnd.getValue())){
            if(createTimeBegin.getValue()>createTimeEnd.getValue()){
                LS.message("info","创建开始时间不能大于结束时间");
                return;
            }
        }

        if(!LS.isEmpty(settleTimeBegin.getValue()) && !LS.isEmpty(settleTimeEnd.getValue())){
            if(settleTimeBegin.getValue()>settleTimeEnd.getValue()){
                LS.message("info","使用时间不能大于结束时间");
                return;
            }
        }

        var params = searchForm.getFormData();
        if(!LS.isEmpty(createTimeBegin.getValue())){
            params.createTimeBegin = params.createTimeBegin+" 00:00:00"
        }
        if(!LS.isEmpty(createTimeEnd.getValue())){
            params.createTimeEnd = params.createTimeEnd+" 23:59:59"
        }
            carGiftGrid.query('~/bil/rechargeCard/querys', params, function (obj) {});
        }


        function addCard(){
            LS.dialog("~/bil/rechargeCard/addCard?cardId=","新增",1000, 260,true, null);
        }

        function modifyCard(){
            var obj = carGiftGrid.getSelectedItem();
            if (!obj) {
                LS.message("info", "请选择一条记录！");
                return;
            }
            var cardId = obj.cardId;
            LS.dialog("~/bil/rechargeCard/updateCard?cardId="+cardId,"修改",1000, 260,true, null);
        }

        function addCards(){
            var obj = carGiftGrid.getSelectedItem();
            if (!obj) {
                LS.message("info", "请选择一条记录！");
                return;
            }
            var cardId = obj.cardId;
            LS.dialog("~/bil/rechargeCard/addBatchCardInit?cardId="+cardId,"增加卡量",1000, 130,true, null);
        }

        function detail(){
            var item = carGiftGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要查看明细");
                return;
            }

            LS.dialog("~/bil/rechargeCard/cardDetails/" + item.cardId , "卡库", 1300, 600, true, null);
        }

        function doEnable(){
            var item = carGiftGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要启用的记录");
                return;
            }
            if(item.cardStatus=='1'){
                LS.message("error","已启用，无需重复启用");
                return;
            }
            LS.confirm('确定要启用该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/enableCard?cardId="+item.cardId,null,function(data){
                        if(data==true){
                            LS.message("info","启用成功");
                            doSearch();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function doDisable(){
            var item = carGiftGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要禁用的记录");
                return;
            }
            if(item.cardStatus=='0'){
                LS.message("error","已禁用，无需重复禁用");
                return;
            }
            LS.confirm('确定要禁用该记录?',function(result){
                if(result){
                    LS.ajax("~/bil/rechargeCard/disableCard?cardId="+item.cardId,null,function(data){
                        if(data==true){
                            LS.message("info","禁用成功");
                            doSearch();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function exportData(){

        }


        function linkFunc(rowdata){
            var cardName = rowdata.cardName;
            var cardId = rowdata.cardId;
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='getCardDetail(\"" + cardId + "\" )'>"+cardName+"</a></u>";
        }

        window.getCardDetail = function (cardId) {
         LS.dialog("~/bil/rechargeCard/cardDetails/" + cardId , "卡库", 1300, 600, true, null);
        }

    </ls:script>
</ls:body>
</html>