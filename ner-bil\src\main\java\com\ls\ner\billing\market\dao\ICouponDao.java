package com.ls.ner.billing.market.dao;

import com.ls.ner.billing.market.bo.*;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.bo.MarketActBo;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;


/**
*描述:优惠券dao层实现
*ICouponDao.java
*作者：biaoxiangd
*创建日期：2017-06-12 11:55
**/
public interface ICouponDao {

    /**
     *描述:新增优惠券
     * @param: [bo]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 11:55
     */
    public int insertCoupon(CouponBo bo);

    /**
     *描述:新增优惠券关联关系
     * @param: [bo]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 11:56
     */
    public int insertDctCond(CouponBo bo);

    /**
     *描述:更新优惠券条件
     * @param: [bo]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 11:56
     */
    public int updateDctCond(CouponBo bo);

    /**
     *描述:优惠券管理-查询功能
     * @param: [condition]
     *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 11:56
     */
    public List<CouponBo> getCoupons(MarketCondition condition);
    public int getCouponsCount(MarketCondition condition);

    /**
     *描述:获取条件参数
     * @param: [map]
     *@return: java.util.List<java.util.Map>
     *创建人:biaoxiangd
     *创建时间: 2017-06-07 18:19
     */
    public List<Map> queryBProd(Map map);

    /**
     *描述:获取条件列表
     * @param: [inMap]
     *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     *创建人:biaoxiangd
     *创建时间: 2017-06-07 18:17
     */
    public List<CouponBo> queryDctCond(Map<String, String> inMap);
    /**
     *描述:更新优惠券
     * @param: [bo]
     *@return: int
     *创建人:biaoxiangd
     *创建时间: 2017-06-12 11:56
     */
    public int updateCoupon(CouponBo bo);

    /**
     * @param bo
     * @description 查询注册送/邀请送，送的优惠券
     * <AUTHOR>
     * @create 2017-08-09 10:32:43
     */
    public List<CouponBo> queryActCpn(MarketActBo bo);
    /**
     * @param condition
     * @description 可领取的优惠券
     * <AUTHOR>
     * @create 2017-10-11 23:47:36
     */
    List<CouponBo> getRestCoupons(MarketCondition condition);
    /**
     * @param
     * @description 查询所有过期的优惠券
     * <AUTHOR>
     * @create 2018-03-07 14:59:21
     */
    List<Map<String,Object>> queryInvCoupon();
    /**
     * @param inMap
     * @description 更新所有失效的优惠券状态为作废
     * <AUTHOR>
     * @create 2018-03-07 16:52:27
     */
    void updateCpnInv(Map<String, Object> inMap);

    /**
     * @param inMap
     * @description 查询活动
     * <AUTHOR>
     * @create 2018-11-06 14:53:03
     */
    List<Map<String,Object>> queryAct(Map<String, Object> inMap);

    /**
     * @param inMap
     * @description 查询充值区间列表与每个区间赠送优惠券数量
     * <AUTHOR>
     * @create 2018-11-06 15:10:47
     */
    List<Map<String,Object>> querySectionList(Map<String, Object> inMap);

    /**
     * @param cpnIds
     * @description 查询优惠券
     * <AUTHOR>
     * @create 2018-11-13 17:15:38
     */
    List<Map<String,Object>> queryCpnById(String[] cpnIds);

    List<Map> qryCouponList(@Param("cpnIds") String[] cpnIds);


    /**
     * @param condition
     * @description  站点可领取优惠券
     * <AUTHOR>
     * @create 2019-05-26 21:11:27
     */
    List<CouponBo> getAllowCoupons(MarketCondition condition);


    /**
     * @param condition
     * @description  优惠券管理查询功能
     * <AUTHOR>
     * @create 2019-05-27 11:11:02
     */
    public List<CouponBo> newGetCoupons(MarketCondition condition);
    public int newGetCouponsCount(MarketCondition condition);



    /**
     * @param cpnId
     * @description  删除优惠券
     * <AUTHOR>
     * @create 2019-05-28 15:10:37
     */
    int deleteByCpnId(String cpnId);



    /**
     * @param bo
     * @description  新增加优惠券关联关系
     * <AUTHOR>
     * @create 2019-05-29 12:16:33
     */
    public int insertDctCondNew(CouponBo bo);

    /**
     * @param bo
     * @description  新更新优惠券关联关系
     * <AUTHOR>
     * @create 2019-05-29 12:15:51
     */
    public int updateDctCondNew(CouponBo bo);


    /**
     * @param inMap
     * @description  获取条件列表
     * <AUTHOR>
     * @create 2019-05-29 13:28:34
     */
    public List<CouponBo> queryDctCondNew(Map<String, String> inMap);
    /**
     * @param map
     * @description 获取优惠券详情（新）
     * <AUTHOR>
     * @create 2019-06-10 10:53:56
     */
    List<Map<String, Object>> couponsDetailInfo(Map<String, Object> map);


    List<Map<String, Object>> getGoodsVrCoupon();

    List<Long> selectCouponId(@Param("actId") Long actId);

    List<Map<String, String>> queryCpnNum(Map<String, Object> map);

    int queryGetCpnNum(@Param("cpnId") String cpnId, @Param("custId") String custId);

    public Map<String, Object> getMyCarCoupon();

    public CouponBo getCouponCpnPurposeDetail(MarketCondition condition);

    List<Map<String, String>> qryCustCouponList(@Param("cpnIdList") List<String> cpnIdList);
}
