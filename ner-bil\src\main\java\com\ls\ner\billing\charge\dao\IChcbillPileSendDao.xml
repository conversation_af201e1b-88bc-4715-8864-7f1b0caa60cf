<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChcbillPileSendDao">

	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChcbillPileSendBo">
		<result column="SYSTEM_ID" property="systemId" />
		<result column="CHC_NO" property="chcNo" />
		<result column="PILE_NO" property="pileNo" />
		<result column="PILE_ID" property="pileId" />
		<result column="PILE_SEND_ID" property="pileSendId" />
		<result column="SEND_TIME" property="sendTime" />
		<result column="FAIL_REASON" property="failReason" />
		<result column="PILE_SEND_STATUS" property="pileSendStatus" />
		<result column="PILE_NAME" property="pileName" />
		 <result column="OPERATOR" property="operator" />
		 <result column="INTRANET_IP" property="intranetIp" />
		 <result column="NETWORK_IP" property="networkIp" />
	 </resultMap>

	 <sql id="chcbillPileSendItem">
		a.SYSTEM_ID,a.CHC_NO,a.PILE_NO,a.PILE_ID,a.PILE_SEND_ID
	</sql>
	<sql id="chcbillPileSendLogItem">
		b.SEND_TIME,b.FAIL_REASON,b.PILE_SEND_STATUS,b.OPERATOR,b.INTRANET_IP,b.NETWORK_IP
	</sql>
	<sql id="queryChcbillPileSendWhere">
		<if test="chcNo !=null and chcNo !=''">
		    and a.CHC_NO =#{chcNo}
		</if>
		<if test="pileNos !=null and pileNo !='' ">
			and a.PILE_NO in
			<foreach item="item" index="index" collection="pileNos" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="pileNo !=null and pileNo !=''">
		    and a.PILE_NO =#{pileNo}
		</if>
	</sql>
	<sql id="queryChcbillPileSendLogWhere">
		<if test="pileSendStatus !=null and pileSendStatus !=''">
		    and b.PILE_SEND_STATUS =#{pileSendStatus}
		</if>
		<if test="pileSendStatussSplit !=null">
		    and b.PILE_SEND_STATUS in
			<foreach item="item" index="index" collection="pileSendStatussSplit" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</sql>

	<!-- 查询桩电价下发信息  E_CHCBILL_PILESEND-->
	<select id="queryChcbillPileSends" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultMap="BaseResultMap">
		select
			<include refid="chcbillPileSendItem"></include>,
			<include refid="chcbillPileSendLogItem"></include>
		from E_CHCBILL_PILESEND a
			 LEFT JOIN E_CHCBILL_PILESEND_LOG b ON a.PILE_SEND_ID = b.PILE_SEND_ID
		<where>
			<include refid="queryChcbillPileSendWhere"></include>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="queryChcbillStationPileSendsLogLast"  resultMap="BaseResultMap" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition">
		select
		<include refid="chcbillPileSendLogItem"></include>
		from E_CHCBILL_PILESEND_LOG b
		<where>
			<if test="pileNos !=null and pileNos.size()>0 ">
				and b.PILE_NO in
				<foreach item="item" index="index" collection="pileNos" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="pileNo !=null and pileNo !=''">
				and b.PILE_NO =#{pileNo}
			</if>
			<if test="sendTime !=null ">
				and #{sendTime}>=b.SEND_TIME
			</if>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
		 order by b.SEND_TIME desc limit 1
	</select>

	<!-- 查询桩电价下发信息条数  E_CHCBILL_PILESEND-->
	<select id="queryChcbillPileSendsNumId" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="String">
		select
		a.SYSTEM_ID
		from E_CHCBILL_PILESEND a
		LEFT JOIN E_CHCBILL_PILESEND_LOG b ON a.PILE_SEND_ID = b.PILE_SEND_ID
		<where>
			<include refid="queryChcbillPileSendWhere"></include>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
		GROUP BY a.SYSTEM_ID
	</select>

	<!-- 查询桩电价下发信息条数  E_CHCBILL_PILESEND-->
	<select id="queryChcbillPileSendsNum" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="int">
		select
			count(1)
		from E_CHCBILL_PILESEND a
			 LEFT JOIN E_CHCBILL_PILESEND_LOG b ON a.PILE_SEND_ID = b.PILE_SEND_ID
		<where>
			<include refid="queryChcbillPileSendWhere"></include>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
	</select>

	<!-- 新增桩电价下发信息   E_CHCBILL_PILESEND -->
	<insert id="insertChcbillPileSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillPileSendBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHCBILL_PILESEND
		<trim prefix="(" suffix=")">
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="pileNo !=null and pileNo !=''">PILE_NO,</if>
			<if test="pileId !=null and pileId !=''">PILE_ID,</if>
			<if test="pileSendId !=null and pileSendId !=''">PILE_SEND_ID,</if>
	      	DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="pileNo !=null and pileNo !=''">#{pileNo},</if>
			<if test="pileId !=null and pileId !=''">#{pileId},</if>
			<if test="pileSendId !=null and pileSendId !=''">#{pileSendId},</if>
	      	now(),'I'
	    </trim>
	</insert>

	<!-- 更新桩电价下发信息   E_CHCBILL_PILESEND -->
	<update id="updateChcbillPileSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillPileSendBo">
		update E_CHCBILL_PILESEND
		<set>
			<if test="pileSendId !=null and pileSendId !=''">PILE_SEND_ID =#{pileSendId},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			CHC_NO =#{chcNo}
			and PILE_NO =#{pileNo}
		</where>
	</update>

	<update id="updateChcbillPileSendById" parameterType="com.ls.ner.billing.charge.bo.ChcbillPileSendBo">
		update E_CHCBILL_PILESEND
		<set>
			<if test="pileSendId !=null and pileSendId !=''">PILE_SEND_ID =#{pileSendId},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			SYSTEM_ID =#{systemId}
		</where>
	</update>

	<!-- 删除桩电价下发信息   E_CHCBILL_PILESEND -->
	<delete id="deleteChcbillSend" parameterType="string">
		delete from E_CHCBILL_PILESEND where CHC_NO = #{chcNo}
	</delete>

	<!-- 查询充电下发成功数 -->
	<select id="queryPileSendSuccessCount" resultType="java.util.Map">
		SELECT
			sum(if(b.PILE_SEND_STATUS='001',1,0)) success,count(1) count
		FROM
			E_CHCBILL_PILESEND a
				LEFT JOIN E_CHCBILL_PILESEND_LOG b on a.PILE_SEND_ID = b.PILE_SEND_ID
		WHERE a.CHC_NO = #{chcNo}
		<if test="pileNo !=null and pileNo !=''">
			and a.PILE_NO =#{pileNo}
		</if>
	</select>

	<!-- 查询站点充电下发成功数 -->
	<select id="queryXpPileSendSuccessCount" resultType="java.util.Map">
		SELECT
		sum(if(b.PILE_SEND_STATUS='001',1,0)) success,count(1) count
		FROM
		E_CHCBILL_PILESEND a
		LEFT JOIN E_CHCBILL_PILESEND_LOG b on a.PILE_SEND_ID = b.PILE_SEND_ID
		WHERE a.CHC_NO = #{chcNo}
		<if test="pileNos !=null">
			and a.PILE_NO in
			<foreach item="item" index="index" collection="pileNos" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="pileNo !=null and pileNo !=''">
			and a.PILE_NO =#{pileNo}
		</if>
	</select>


	<!--判断今天是否有成功下发记录-->
	<select id="querySendSucceedCount"  resultType="int">
		SELECT
			count(1)
		FROM
			e_chcbill_pilesend_log a
		WHERE
			a.PILE_NO = #{pileNo}
			AND date_format(a.SEND_TIME, '%Y-%m-%d') = CURRENT_DATE
			AND a.PILE_SEND_STATUS = '001'
	</select>

	<!--查询需要重新下发的记录-->
	<select id="queryReSendRecord" resultType="java.util.Map">
		SELECT
			a.CHC_NO chcNo,
			a.PILE_NO pileNo,
			a.PILE_SEND_STATUS pileSendStatus
		FROM
			e_chcbill_pilesend_log a
		WHERE
			a.PILE_NO = #{pileNo}
		ORDER BY
			a.SEND_TIME DESC
		LIMIT 1
	</select>

	<!--查询最新计费模型-->
	<select id="qyeruBilConfLast" resultType="java.util.Map">
		SELECT
			a.ORG_CODE orgCode,
			a.STATION_ID stationId,
			a.CHC_NO chcNo,
			a.CHC_NAME chcName,
			a.BILL_CTL_MODE billCtlMode,
		  a.CHARGE_MODE chargeMode
		FROM
			e_charge_billing_conf a
		WHERE
			a.STATION_ID = #{stationId}
		AND a.CHC_STATUS = '1'
		ORDER BY
			a.EFT_DATE DESC
		LIMIT 1
	</select>

	<!--查询最新新版计费模型-->
	<select id="qyeruBilConfLastNew" resultType="java.util.Map">
		SELECT
		a.ORG_CODE orgCode,
		a.STATION_ID stationId,
		a.CHC_NO chcNo,
		a.CHC_NAME chcName,
		a.BILL_CTL_MODE billCtlMode,
		a.CHARGE_MODE chargeMode
		FROM
		e_charge_billing_conf a,e_station_billing b
		WHERE
		a.CHC_NO=b.CHC_NO
		AND B.STATION_ID = #{stationId}
		AND a.CHC_STATUS = '1'
		ORDER BY
		a.EFT_DATE DESC
		LIMIT 1
	</select>

	<select id="queryChcbillPileSendsByNo" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="java.util.Map">
		select
			a.CHC_NO chcNo,
			a.STATION_ID stationId,
			a.SEND_TIME sendTime,
			a.PILE_NO pileNo,
			a.PILE_ID pileId
		from E_CHCBILL_SEND a,(
			select
				b.STATION_ID,
				b.PILE_NO,
				MAX(b.SEND_TIME) SEND_TIME
			from E_CHCBILL_SEND b
			<where>
				b.STATION_ID is not null
				<if test="chcNo !=null and chcNo !=''">
					and b.CHC_NO =#{chcNo}
				</if>
				<if test="sendStatus != null and sendStatus != '' and sendStatus == 'fail'.toString()">
					and b.SEND_STATUS in ('01','02','03','05')
				</if>
				<if test="stationId !=null and stationId !=''">
					and b.STATION_ID =#{stationId}
				</if>
				<if test="pileNo !=null and pileNo !=''">
					and b.PILE_NO =#{pileNo}
				</if>
				<if test="groupType != null and groupType != '' and groupType == 'station'.toString()">
					and b.PILE_NO is null
				</if>
				<if test="groupType != null and groupType != '' and groupType == 'pile'.toString()">
					and b.PILE_NO is not null
				</if>
			</where>
			<if test="groupType != null and groupType != '' and groupType == 'station'.toString()">
				group by b.STATION_ID
			</if>
			<if test="groupType != null and groupType != '' and groupType == 'pile'.toString()">
				group by b.PILE_NO
			</if>
		) c
		<where>
			a.STATION_ID is not null
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO =#{chcNo}
			</if>
			<if test="stationId !=null and stationId !=''">
				and a.STATION_ID =#{stationId}
			</if>
			<if test="pileNo !=null and pileNo !=''">
				and a.PILE_NO =#{pileNo}
			</if>
			<if test="sendStatus != null and sendStatus != '' and sendStatus == 'fail'.toString()">
				and a.SEND_STATUS in ('01','02','03','05')
			</if>
			<if test="groupType != null and groupType != '' and groupType == 'station'.toString()">
				and a.PILE_NO is null
				and a.STATION_ID = c.STATION_ID
				and a.SEND_TIME = c.SEND_TIME
			</if>
			<if test="groupType != null and groupType != '' and groupType == 'pile'.toString()">
				and a.PILE_NO is not null
				and a.STATION_ID = c.STATION_ID
				and a.PILE_NO = c.PILE_NO
				and a.SEND_TIME = c.SEND_TIME
			</if>
			and EXISTS (
				SELECT 1
				from E_STATION_BILLING
				WHERE station_id = a.STATION_ID
				and CHC_NO = a.CHC_NO
			)
		</where>
	</select>

	<select id="queryChcbillPileSendByNo" parameterType="java.util.Map" resultType="java.util.Map">
		select
			a.CHC_NO chcNo,
			a.STATION_ID stationId,
			a.SEND_TIME sendTime,
			a.PILE_NO pileNo,
			a.PILE_ID pileId
		from E_CHCBILL_SEND a
		<where>
			<if test="stationId !=null and stationId !=''">
				and a.STATION_ID =#{stationId}
			</if>
			<if test="pileNo != null and pileNo != ''">
				and a.PILE_NO = #{pileNo}
			</if>
		</where>
		order by a.SEND_TIME desc
		limit 1
	</select>

	<select id="queryStationBillConfs" resultType="java.util.Map">
		SELECT
			a.CHC_NO chcNo,
			a.STATION_ID stationId
		FROM
			e_station_billing a
				join ner_asset.a_station b on a.STATION_ID = b.STATION_ID and b.billing_config = '01'
				LEFT JOIN (
				SELECT
					station_id,
					send_status
				FROM
					e_chcbill_send
				WHERE
						send_time = (
						SELECT MAX(send_time)
						FROM e_chcbill_send s2
						WHERE s2.station_id = e_chcbill_send.station_id
					)
			) c ON a.station_id = c.station_id
		WHERE
			c.send_status != '04' OR c.station_id IS NULL
	</select>

	<select id="queryPileBillConfs" resultType="java.util.Map">
		SELECT
			a.CHC_NO chcNo,
			a.station_id stationId,
			d.equip_id pileId,
		    d.equip_no pileNo
		FROM
			e_station_billing a
				join ner_asset.a_station b on a.STATION_ID = b.STATION_ID and b.billing_config = '02'
			    join ner_asset.a_equip d on d.station_id = b.station_id and d.class_sort_code = '0201'
				LEFT JOIN (
				SELECT
					station_id,
					send_status
				FROM
					e_chcbill_send
				WHERE
						send_time = (
						SELECT MAX(send_time)
						FROM e_chcbill_send s2
						WHERE s2.station_id = e_chcbill_send.station_id
					)
			) c ON a.station_id = c.station_id
		WHERE
			c.send_status != '04' OR c.station_id IS NULL
	</select>

</mapper>
