/**
 *
 * @(#) MainChargeConfigBo.java
 * @Package com.ls.ner.billing.api.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.bo;

import java.io.Serializable;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: MainChargeConfigBo.java,v 1.1 2016/02/26 02:35:24 34544 Exp $ 
 *
 *  History:  2016年2月25日 下午9:52:41   lipf   Created.
 *           
 */
public class MainChargeConfigBo implements Serializable {

	private static final long serialVersionUID = 3426260748070548181L;
	private String mainNo;
	private String billingNo;
	private String price;
	private String minCost;
	private String maxCost;
	private String chargMode;
	private String chargeType;
	private String chargeUnit;
	private String chargeNum;

	public String getMainNo() {
		return mainNo;
	}

	public void setMainNo(String mainNo) {
		this.mainNo = mainNo;
	}

	public String getBillingNo() {
		return billingNo;
	}

	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getMinCost() {
		return minCost;
	}

	public void setMinCost(String minCost) {
		this.minCost = minCost;
	}

	public String getMaxCost() {
		return maxCost;
	}

	public void setMaxCost(String maxCost) {
		this.maxCost = maxCost;
	}

	public String getChargMode() {
		return chargMode;
	}

	public void setChargMode(String chargMode) {
		this.chargMode = chargMode;
	}

	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}

	public String getChargeUnit() {
		return chargeUnit;
	}

	public void setChargeUnit(String chargeUnit) {
		this.chargeUnit = chargeUnit;
	}

	public String getChargeNum() {
		return chargeNum;
	}

	public void setChargeNum(String chargeNum) {
		this.chargeNum = chargeNum;
	}
}
