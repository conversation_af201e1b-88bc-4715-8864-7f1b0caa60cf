<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑段落"/>
<ls:body>
    <ls:form id="preSectionForm" name="preSectionForm">
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="presentSectionId" property="presentSectionId" type="hidden"/>
        <ls:text name="release" property="release" type="hidden"/>
        <ls:text name="presentBalType" property="presentBalType" type="hidden"/>
        <table>
            <tr>
                <td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
            </tr>
        </table>
        <table class="tab_search">
            <tr style="display: none">
                <td>
                    <ls:label text="段落关系" ref="sectionRelaType"/>
                </td>
                <td colspan="6">
                    <ls:checklist name="sectionRelaType" property="sectionRelaType" type="radio" required="true">
                        <ls:checkitems property="presentSectRelaTypeCheckList" scope="request" text="codeName"
                                       value="codeValue"/>
                    </ls:checklist>
                </td>
            </tr>
            <tr style="display: none;">
                <td>
                    <ls:label text="段落名称"/>
                </td>
                <td colspan="4">
                    <ls:text name="sectionName" property="sectionName"/>
                </td>
                <td colspan="2"></td>
            </tr>
            <tr style="display: none;">
                <td>
                    <ls:label text="段落类型" ref="sectionType"/>
                </td>
                <td>
                    <ls:checklist name="sectionType" property="sectionType" type="radio" required="true">
                        <ls:checkitems property="presentSectTypeCheckList" scope="request" text="codeName"
                                       value="codeValue"/>
                    </ls:checklist>
                </td>
                <td colspan="5"></td>
            </tr>
            <tr id="sectionDetTr">
                <td width="100px">
                    <ls:label text="段落明细" ref="presentSectionDets"/>
                </td>
                <td colspan="6">
                    <ls:grid url="" name="sectionDetGrid" primaryKey="sectionDetSn" cellEdit="true" treeGrid="true"
                             expandColumn="value" expand="true" treeGridModel="sectionDet" height="100px" width="100%">
                        <ls:gridToolbar name="sectionDetGridBar">
                            <ls:gridToolbarItem imageKey="add" onclick="doAddSectionDet" text="新增"/>
                            <ls:gridToolbarItem imageKey="delete" onclick="doDelSectionDet" text="删除"/>
                        </ls:gridToolbar>
                        <ls:column caption="预存段落ID" name="sectionDetId" editableEdit="false" hidden="true"/>
                        <ls:column caption="排序号" name="sectionDetSn" editableEdit="false"/>
                        <ls:column caption="起始值(>=)" name="refCeil" editableEdit="true">
                            <ls:textEditor required="true" type="number" onValidate="endValidate()"/>
                        </ls:column>
                        <ls:column caption="终止值(<)" name="refFloor" editableEdit="true">
                            <ls:textEditor required="true" type="number" onValidate="endValidate()"/>
                        </ls:column>
                        <ls:column caption="优惠券" name="cpnName" editableEdit="true">
                            <ls:textEditor required="true" imageKey="search" onClickImage="choiceCoupon"/>
                        </ls:column>
                        <ls:column caption="优惠券" name="cpnId" editableEdit="false" hidden="true"></ls:column>
                        <ls:column caption="赠送数量" name="baseValue" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="赠送积分" name="integralNum" editableEdit="true">
                            <ls:textEditor required="false" type="number"/>
                        </ls:column>
                    </ls:grid>
                    <ls:text name="presentSectionDets" property="presentSectionDets" required="true" type="hidden"/>
                </td>
            </tr>
            <tr style="display: none;">
                <td>
                    <ls:label text="赠送周期类型" ref="presentCycleType"/>
                </td>
                <td>
                    <ls:select name="presentCycleType" property="presentCycleType" required="true">
                        <ls:options property="cycleTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
                <td colspan="5"></td>
            </tr>
            <tr>
                <td>
                    <ls:label text="参与活动" ref="maxValue"/>
                </td>
                <td>
                    <ls:text name="maxValue" property="maxValue" required="true"/>
                </td>
                <td align="right" colspan="5">次(-1为无上限)</td>
            </tr>
            <tr>
                <td>
                    <ls:label text="规则描述" ref="presentRuleDesc"/>
                </td>
                <td colspan="6">
                    <ls:text name="presentRuleDesc" property="presentRuleDesc" type="textarea" required="true"/>
                </td>
            </tr>
        </table>
    </ls:form>
    <table>
        <tr>
            <td>
                <div class="pull-right">
                    <ls:button text="上一步" onclick="doUpStep"/>
                    <ls:button text="保存" onclick="doSave" />
                    <ls:button text="发布" onclick="doRelease"/>
                </div>
            </td>
        </tr>
    </table>
    <ls:script>
        doSearch();
        function doSearch() {
            var actIdValue = actId.getValue();
            var balTypeValue = presentBalType.getValue();
            var sectionIdValue = presentSectionId.getValue();
            if (!LS.isEmpty(actIdValue)) {
                sectionDetGrid.query('~/cpn/sections?actId=' + actIdValue + "&presentSectionId=" + sectionIdValue + "&presentBalType=" + balTypeValue, null);
            }
        }

        function endValidate(value,grid) {
            var isValid = true;
            if (!LS.isEmpty(value)) {
                isValid = /^\d{1,}$/.test(value);
            }
            return {isValid: isValid, message: "请输入整数"}
        }

        //新增
        function doAddSectionDet() {
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                var data = {sectionDetSn: '1', refCeil: '', refFloor: '', integralNum: 0};
                sectionDetGrid.appendItem(data);
            } else {
                if (LS.isEmpty(items[items.length - 1].refFloor)) {
                    LS.message("error", "上一条终止值不能为空！");
                    return;
                }
                var data = {
                    sectionDetSn: items.length + 1,
                    refCeil: parseInt(items[items.length - 1].refFloor),
                    refFloor: '',
                    integralNum: 0
                };
                sectionDetGrid.appendItem(data);
            }
        }

        //删除
        function doDelSectionDet() {
            var item = sectionDetGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择要删除的记录");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 1 || item.sectionDetSn == items[0].sectionDetSn) {
                LS.message("error", "第一条记录不能删除");
                return;
            }
            var newItems = [];
            sectionDetGrid.setCell(parseInt(item.sectionDetSn) + 1, "refCeil", items[parseInt(item.sectionDetSn) - 2].refFloor);
            items = sectionDetGrid.getItems();
            for (var i = 0; i < items.length; i++) {
                if (item.sectionDetSn != items[i].sectionDetSn) {
                    newItems.push(items[i]);
                }else{
                    if(!LS.isEmpty(item.sectionDetId)){
                        LS.ajax("~/sections/cpn/delete", {sectionDetId : item.sectionDetId}, function(e){});
                    }
                }
            }
            sectionDetGrid.removeAllItems();
            for (var i = 0; i < newItems.length; i++) {
                newItems[i].sectionDetSn = i + 1;
            }
            sectionDetGrid.appendItem(newItems);
        }

        //保存
        function doSave() {
            if (!preSectionForm.valid()) {
                return;
            }
            if (!sectionDetGrid) {
                LS.message("error", "出错了，请联系管理员");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                LS.message("info", "段落明细请至少设置一条记录！");
                return;
            }
            var newItems = [];
            for (var m = 0; m < items.length; m++) {
                newItems.push(JSON.stringify(items[m]));
            }
            presentSectionDets.setValue(newItems.join(";"));

            preSectionForm.submit("~/sections/saving", function (e) {
                if (e.items[0][0] == "T") {
                    presentSectionId.setValue(e.items[0][1]);
                    LS.message("info", "保存成功");
                } else {
                    LS.message("info", "操作失败或网络异常，请重试！");
                    return;
                }
            });
        }

        //上一步
        function doUpStep() {
            var actIdValue = actId.getValue();
            window.location.href = "<%=request.getContextPath() %>/marketacts/edit?actId=" + actIdValue;
        }

        //下一步
        function doRelease() {
            var items = sectionDetGrid.getItems();
            for(var i=0;i< items.length;i++){
                if(parseInt(items[i].refFloor) < parseInt(items[i].refCeil)){
                    LS.message("info", "排序号"+items[i].sectionDetSn+"终止值不能小于起始值！");
                    return;
                }
                for(var j=0;j< items.length;j++){
                    if((parseInt(items[i].refCeil) < parseInt(items[j].refCeil)) && (parseInt(items[i].sectionDetSn) !=parseInt(items[j].sectionDetSn))){
                        if(parseInt(items[j].refCeil) < parseInt(items[i].refFloor)){
                            LS.message("info", "排序号"+items[j].sectionDetSn+"起始值必须大于等于"+"排序号"+items[i].sectionDetSn+"的终止值！");
                            return;
                        }
                    }else if((parseInt(items[i].refCeil) == parseInt(items[j].refCeil)) && (parseInt(items[i].sectionDetSn) !=parseInt(items[j].sectionDetSn))){
                        LS.message("info", "排序号"+items[i].sectionDetSn+"和"+items[j].sectionDetSn+"起始值不能一样！");
                        return;
                    }
                }
            }
            doSave();
            if (items.length == 0) {
                LS.message("info", "请先完成段落规则维护！");
                return;
            }

            var params = preSectionForm.getFormData();
            LS.ajax("~/marketacts/releaseAct",params,function(data){
                if(data.successful){
                    LS.message("info",data.resultHint);
                    LS.parent().doSearch();
                    LS.window.close();
                }else{
                    LS.message("info",data.resultHint);
                }
            });
        }

        var rowId, rowData;
        window.setCoupon = function (item) {
            if(item.length == undefined){
                sectionDetGrid.setCell(rowId, "cpnName", item.cpnName);
                sectionDetGrid.setCell(rowId, "cpnId", item.cpnId);
            }else{
                var tempCpnName = '';
                var tempcpnId = '';
                for(var i=0;i<item.length;i++){
                    if(i == item.length-1){
                        tempCpnName += item[i].cpnName;
                        tempcpnId += item[i].cpnId;
                    }else{
                        tempCpnName += item[i].cpnName+',';
                        tempcpnId += item[i].cpnId+',';
                    }
                }
                sectionDetGrid.setCell(rowId, "cpnName", tempCpnName);
                sectionDetGrid.setCell(rowId, "cpnId", tempcpnId);
            }
        }

        function choiceCoupon(rowid, rowdata, rowEditor) {
            rowId = rowid;
            rowData = rowdata;
            LS.dialog("~/billing/coupon/couponManage/06?singleSelect=false", "选择优惠券", 950, 600, true);
        }
    </ls:script>
</ls:body>
</html>