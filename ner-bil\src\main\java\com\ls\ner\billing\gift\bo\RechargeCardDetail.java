package com.ls.ner.billing.gift.bo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.bo
 * @ClassName: RrechargeCardDetail
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/4/28 14:34
 * @Version: 1.0
 */
public class RechargeCardDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 充值卡明细ID
     */
    private Long cardDetailId;

    /**
     * 充值卡批次号
     */
    private String cardBatchNo;

    /**
     * 充值卡名称
     */
    private String cardName;
    /**
     * 充值id
     */
    private Long cardId;

    /**
     * 充值卡号
     */
    private String cardNo;

    /**
     * 充值卡密
     */
    private String cardSecret;

    /**
     * 用户唯一标识
     */
    private String custId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 总面值
     */
    private BigDecimal totalAmt;

    /**
     * 实际面值
     */
    private BigDecimal actualAmt;

    /**
     * 赠送部分面值
     */
    private BigDecimal giftAmt;

    /**
     * 激活时间
     */
    private String activateTime;

    /**
     * 卡号状态：0 待激活 1 已激活 2已冻结 3已退款 4使用完毕
     */
    private String cardDetailStatus;

    private String cardDetailStatusName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人员
     */
    private String createUser;

    /**
     * 操作时间
     */
    private String updateTime;

    /**
     * 操作人员
     */
    private String updateUser;

    /**
     * 卡号来源：0 后管 1 小程序 or APP积分
     */
    private String cardSource;

    /** 总核销金额 */
    private BigDecimal totalClearCount;

    /** 实际核销金额 */
    private BigDecimal realClearCount;

    /** 赠送部分核销金额 */
    private BigDecimal giftClearCount;


    /** 总余额 */
    private BigDecimal totalBalance;

    /** 实际余额 */
    private BigDecimal actualBalance;


    /** 赠送部分余额 */
    private BigDecimal giftBalance;


    /** 充值卡有效期时间类型，1永久有效 2限时有效 */
    private String cardTimeType;

    /** 生效日期 */
    private String eftDate;

    /** 失效日期 */
    private String invDate;

    public String getCardTimeType() {
        return cardTimeType;
    }

    public void setCardTimeType(String cardTimeType) {
        this.cardTimeType = cardTimeType;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getCardDetailStatusName() {
        return cardDetailStatusName;
    }

    public void setCardDetailStatusName(String cardDetailStatusName) {
        this.cardDetailStatusName = cardDetailStatusName;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getTotalClearCount() {
        return totalClearCount;
    }

    public void setTotalClearCount(BigDecimal totalClearCount) {
        this.totalClearCount = totalClearCount;
    }

    public BigDecimal getRealClearCount() {
        return realClearCount;
    }

    public void setRealClearCount(BigDecimal realClearCount) {
        this.realClearCount = realClearCount;
    }

    public BigDecimal getGiftClearCount() {
        return giftClearCount;
    }

    public void setGiftClearCount(BigDecimal giftClearCount) {
        this.giftClearCount = giftClearCount;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getActualBalance() {
        return actualBalance;
    }

    public void setActualBalance(BigDecimal actualBalance) {
        this.actualBalance = actualBalance;
    }

    public BigDecimal getGiftBalance() {
        return giftBalance;
    }

    public void setGiftBalance(BigDecimal giftBalance) {
        this.giftBalance = giftBalance;
    }

    public String getCardSource() {
        return cardSource;
    }

    public void setCardSource(String cardSource) {
        this.cardSource = cardSource;
    }

    public Long getCardDetailId() {
        return cardDetailId;
    }

    public void setCardDetailId(Long cardDetailId) {
        this.cardDetailId = cardDetailId;
    }

    public String getCardBatchNo() {
        return cardBatchNo;
    }

    public void setCardBatchNo(String cardBatchNo) {
        this.cardBatchNo = cardBatchNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardSecret() {
        return cardSecret;
    }

    public void setCardSecret(String cardSecret) {
        this.cardSecret = cardSecret;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getActualAmt() {
        return actualAmt;
    }

    public void setActualAmt(BigDecimal actualAmt) {
        this.actualAmt = actualAmt;
    }

    public BigDecimal getGiftAmt() {
        return giftAmt;
    }

    public void setGiftAmt(BigDecimal giftAmt) {
        this.giftAmt = giftAmt;
    }

    public String getActivateTime() {
        return activateTime;
    }

    public void setActivateTime(String activateTime) {
        this.activateTime = activateTime;
    }

    public String getCardDetailStatus() {
        return cardDetailStatus;
    }

    public void setCardDetailStatus(String cardDetailStatus) {
        this.cardDetailStatus = cardDetailStatus;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }
}
