/**
 *
 * @(#) ChargeItemController.java
 * @Package com.ls.ner.billing.item.controller
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.item.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.BeanUtils;
import org.owasp.esapi.ESAPI;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.item.bo.ChargeItemBo;
import com.ls.ner.billing.item.service.IChargeItemService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * Description :费用项
 * 
 * @author: lipf
 * 
 *          History: 2015年7月7日 下午5:22:13 lipf Created.
 * 
 */
@Controller
@RequestMapping("/billing/chargeItem")
public class ChargeItemController extends QueryController<ChargeItemBo> {
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired("chargeItemService")
	private IChargeItemService chargeItemService;

	/**
	 * 
	 * Method description : 费用项页面 初始化
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:20:59 History: 2015年7月7日
	 * 下午7:20:59 lipf Created.
	 * 
	 * @param m
	 * @return
	 * 
	 */
	@RequestMapping("/init")
	public String init(Model m, HttpServletRequest request) {
		List<CodeBO> ynJudgeFlagList = codeService.getStandardCodes(BillConstants.JUDGE_FLAG, null);
		List<CodeBO> chargeTypeList = codeService.getStandardCodes(BillConstants.CHARGE_TYPE, null);
		List<CodeBO> timeLineList = codeService.getStandardCodes("timeLine", null);
		m.addAttribute("chargeTypeList", chargeTypeList);
		m.addAttribute("ynJudgeFlagList", ynJudgeFlagList);
		m.addAttribute("timeLineList", timeLineList);

		String flag = ESAPI.encoder().encodeForJavaScript(request.getParameter("flag"));
		if (!StringUtils.nullOrBlank(flag)) {
			m.addAttribute("flag", "1");
		}
		m.addAttribute("chargeItemForm", new ChargeItemBo());
		return "/bil/chargeItem";
	}

	/**
	 * 
	 * Method description : 费用项 表格数据查询
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:21:11 History: 2015年7月7日
	 * 下午7:21:11 lipf Created.
	 * 
	 * @param params
	 * @return
	 * 
	 */
	@RequestMapping("/getChargeItemQuery")
	public @ItemResponseBody
	QueryResultObject getChargeItemQuery(@QueryRequestParam("params")
	RequestCondition params) {
		ChargeItemBo bo = this.rCondition2QCondition(params);
		List<ChargeItemBo> dataList = chargeItemService.getChargeItem(bo);
		int count = chargeItemService.getChargeItemCount(bo);
		return RestUtils.wrappQueryResult(dataList, count);
	}

	/**
	 * 
	 * Method description : 保存 / 更新
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:21:24 History: 2015年7月7日
	 * 下午7:21:24 lipf Created.
	 * 
	 * @param dataParams
	 * @param m
	 * @return
	 * 
	 */
	@RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
	public @ItemResponseBody
	QueryResultObject saveOrUpdate(@RequestBody
	Map<String, Object> dataParams, Model m) {
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeItemBo bo = new ChargeItemBo();
		try {
			for (Map<String, Object> map : saveList) {
				BeanUtils.populate(bo, map);
			}
			if (StringUtils.nullOrBlank(bo.getItemId())) {
				chargeItemService.saveChargeItem(bo);
				bo.setMsg("saveSucc");
			} else {
				chargeItemService.updateChargeItem(bo);
				bo.setMsg("updateSucc");
			}

		} catch (Exception e) {
			e.printStackTrace();
			bo.setMsg(e.getMessage());
		}

		return RestUtils.wrappQueryResult(bo);
	}

	/**
	 * 
	 * Method description : 删除
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:21:47 History: 2015年7月7日
	 * 下午7:21:47 lipf Created.
	 * 
	 * @param id
	 * @return
	 * 
	 */
	@RequestMapping({ "/deleteChargeItem" })
	public @ItemResponseBody
	QueryResultObject deleteChargeItem(@RequestParam
	String id) {
		String mark = "N";
		try {
			chargeItemService.deleteChargeItem(id);
			mark = "Y";
		} catch (Exception e) {
			e.printStackTrace();
			mark = e.getMessage();
		}

		return RestUtils.wrappQueryResult(mark);
	}

	/**
	 * 
	 * Method description : 预收费用明细查询
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午8:39:26 History: 2015年7月22日
	 * 下午8:39:26 lipf Created.
	 * 
	 * @param params
	 * @return
	 * 
	 */
	@RequestMapping("/queryPreItemDetail/{appNo}")
	public @ItemResponseBody
	QueryResultObject queryPreItemDetail(@PathVariable("appNo")
	String appNo) {
		ChargeItemBo bo = new ChargeItemBo();
		bo.setAppNo(appNo);
		bo.setBillingType("01");
		List<ChargeItemBo> dataList = chargeItemService.getItemDetail(bo);
		int count = chargeItemService.getItemDetailCount(bo);
		return RestUtils.wrappQueryResult(dataList, count);
	}

	/**
	 * 
	 * Method description : 结算费用明细
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午8:58:44 History: 2015年7月22日
	 * 下午8:58:44 lipf Created.
	 * 
	 * @param appNo
	 * @return
	 * 
	 */
	@RequestMapping("/querySetItemDetail/{appNo}")
	public @ItemResponseBody
	QueryResultObject querySetItemDetail(@PathVariable("appNo")
	String appNo) {
		ChargeItemBo bo = new ChargeItemBo();
		bo.setAppNo(appNo);
		bo.setBillingType("02");
		List<ChargeItemBo> dataList = chargeItemService.getItemDetail(bo);
		int count = chargeItemService.getItemDetailCount(bo);
		return RestUtils.wrappQueryResult(dataList, count);
	}

	@Override
	protected ChargeItemBo initCondition() {
		return new ChargeItemBo();
	}

}
