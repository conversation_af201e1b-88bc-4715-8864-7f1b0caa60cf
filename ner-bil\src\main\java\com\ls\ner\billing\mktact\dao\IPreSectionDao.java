package com.ls.ner.billing.mktact.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.mktact.bo.PreSectionBo;

public interface IPreSectionDao {
	
	/**
	 * 获取段落列表
	 * 
	 * @param condition
	 * @return
	 * <AUTHOR>
	 */
	public List<PreSectionBo> qryPreSections(PreSectionBo condition);

	/**
	 * 获取段落listMap
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryPreSectionsMap(Map<String, Object> map);
	
	/**
	 * 新增 -- 时间限制规则
	 * @param model
	 * <AUTHOR>
	 */
	public void insertTimeLimitType(PreSectionBo model);

	/**
	 * 更新 -- 时间限制规则
	 * @param model
	 * <AUTHOR>
	 */
	public void updateTimeLimitType(PreSectionBo model);

	/**
	 * 新增  -- 周期规则
	 * 
	 * @param model
	 * <AUTHOR>
	 */
	public void insertCycleType(PreSectionBo model);

	/**
	 * 新增 -- 赠送段落明细
	 * 
	 * @param preSectionList
	 * <AUTHOR>
	 */
	public void insertPreSectionDet(List<Map<String, Object>> preSectionList);
	
	/**
	 * 新增 -- 段落规则
	 * @param model
	 * @return
	 * <AUTHOR>
	 */
	public void insertPreSection(PreSectionBo model);
	
	/**
	 * 删除 时间限制规则
	 * @param presentSectionId
	 * <AUTHOR>
	 */
	public void deleteTimeLimitType(Long presentSectionId);

	/**
	 * 删除 周期规则
	 * 
	 * @param presentSectionId
	 * <AUTHOR>
	 */
	public void deleteCycleType(Long presentSectionId);

	/**
	 * 删除 赠送段落明细
	 * 
	 * @param presentSectionId
	 * <AUTHOR>
	 */
	public void deletePreSectionDet(Long presentSectionId);

	/**
	 * 删除 段落规则
	 * 
	 * @param presentSectionId
	 * @return
	 * <AUTHOR>
	 */
	public void deletePreSection(Long presentSectionId);

	/**
	 * 根据活动ID、预存金额 获取赠送金额
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public Map<String, Object> qryPreSectionValue(Map<String, Object> map);

	public Map<String, Object> qryTimeLimitById(long timeLimitId);

	public Map<String, Object> qryCycleById(long cycleTypeId);

	/**
	 * 根据活动ID、预存金额 获取赠送金额
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String,Object>> qryPreSectionDetInfo(Map<String, Object> map);
	/**
	 * 更新 -- 段落规则
	 * @param model
	 * @return
	 * <AUTHOR>
	 */
	public void updatePreSection(PreSectionBo model);
	/**
	 * 更新 -- 段落明细
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public void updatePreSectionDet(Map<String, Object> map);
	/**
	 * @param inMap
	 * @description 删除充值段落明细
	 * <AUTHOR>
	 * @create 2018-07-03 17:53:02
	 */
	void preSectionDetDel(Map<String, Object> inMap);

}
