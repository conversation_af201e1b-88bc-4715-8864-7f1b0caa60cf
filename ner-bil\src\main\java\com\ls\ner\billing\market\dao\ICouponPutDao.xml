<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.ICouponPutDao">

	<resultMap id="putLogBo" type="com.ls.ner.billing.market.bo.CouponPutBo">
		<result column="GET_NO" property="getNo" jdbcType="VARCHAR" />
		<result column="CPN_ID" property="cpnId" jdbcType="VARCHAR" />
		<result column="PUT_EMP_TYPE" property="putEmpType" jdbcType="VARCHAR" />
		<result column="PUT_EMP" property="putEmp" jdbcType="VARCHAR" />
		<result column="CUST_ID" property="custId" jdbcType="VARCHAR" />
		<result column="PUT_TIME" property="putTime" jdbcType="VARCHAR" />
		<result column="PUT_NUM" property="putNum" jdbcType="VARCHAR" />
		<result column="NOTICE_TYPE" property="noticeType" jdbcType="VARCHAR" />
		<result column="NOTICE_CONT" property="noticeCont" jdbcType="VARCHAR" />
		<result column="PUT_CHANNEL" property="putChannel" jdbcType="VARCHAR" />
		<result column="PUT_ID" property="putId" jdbcType="VARCHAR" />
		<result column="PUT_FLAG" property="putFlag" jdbcType="VARCHAR" />
		<result column="FAIL_REASON" property="failReason" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="putDetBo" type="com.ls.ner.billing.market.bo.CouponPutDetBo">
		<result column="GET_NO" property="getNo" jdbcType="VARCHAR" />
		<result column="CUST_ID" property="custId" jdbcType="VARCHAR" />
		<result column="PUT_TIME" property="putTime" jdbcType="VARCHAR" />
		<result column="mobile" property="mobile" jdbcType="VARCHAR" />
		<result column="cust_name" property="custName" jdbcType="VARCHAR" />
		<result column="cust_sort_code" property="custSortCode" jdbcType="VARCHAR" />
		<result column="GET_FLAG" property="getFlag" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="couponBo" type="com.ls.ner.billing.market.bo.CouponBo">
		<result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
		<result column="CPN_ID" property="cpnId" jdbcType="VARCHAR" />
		<result column="CPN_NAME" property="cpnName" jdbcType="VARCHAR" />
		<result column="CPN_TYPE" property="cpnType" jdbcType="VARCHAR" />
		<result column="PROD_BUSI_TYPE" property="busiType" jdbcType="VARCHAR" />
		<result column="CPN_AMT" property="cpnAmt" jdbcType="VARCHAR" />
		<result column="EFT_DATE" property="eftDate" jdbcType="VARCHAR" />
		<result column="INV_DATE" property="invDate" jdbcType="VARCHAR" />
		<result column="CPN_NUM" property="cpnNum" jdbcType="VARCHAR" />
		<result column="LIM_GET_NUM" property="limGetNum" jdbcType="VARCHAR" />
		<result column="ALRDY_GET_NUM" property="alrdyGetNum" jdbcType="VARCHAR" />
		<result column="CPN_MARKS" property="cpnMarks" jdbcType="VARCHAR" />
		<result column="CPN_STATUS" property="cpnStatus" jdbcType="VARCHAR" />
		<result column="CRE_TIME" property="creTime" jdbcType="VARCHAR" />
		<result column="CRE_EMP" property="creEmp" jdbcType="VARCHAR" />
		<result column="EFFECT_TIME" property="effectTime" jdbcType="VARCHAR" />
		<result column="PUSH_EMP" property="pushEmp" jdbcType="VARCHAR" />
		<result column="PUT_NUM" property="putNum" jdbcType="VARCHAR" />
		<result column="CPN_TIME_TYPE" property="cpnTimeType" jdbcType="VARCHAR" />
		<result column="TIME_UNIT" property="timeUnit" jdbcType="VARCHAR" />
		<result column="IS_DELAY" property="isDelay" jdbcType="INTEGER" />
	</resultMap>

	<!-- 描述:优惠券查询条件  创建人:biaoxiangd  创建时间:2017-06-03 11:35 -->
	<sql id="getCouponWhere">
		<if test="cpnId != null and cpnId != ''">
			and c.CPN_ID = #{cpnId}
		</if>
		<if test="cpnType != null and cpnType != ''">
			and c.cpn_type = #{cpnType}
		</if>
		<if test="cpnName != null and cpnName != ''">
			and c.CPN_NAME like concat('%',#{cpnName},'%')
		</if>
		<if test="cpnAmt != null and cpnAmt != ''">
			and c.CPN_AMT = #{cpnAmt}
		</if>
		<if test="cpnStatus != null and cpnStatus != ''">
			and c.CPN_STATE = #{cpnStatus}
		</if>
		<if test="orgCode != null and orgCode != ''">
			and c.ORG_CODE in(#{orgCode})
		</if>
		<if test="alrdyGetNum != null and alrdyGetNum != ''">
			and ifnull(c.ALRDY_GET_NUM,0) &lt;c.CPN_NUM
		</if>
		<if test="invDate != null and invDate != ''">
			and now() &lt;= c.INV_DATE
		</if>
		<if test="busiType != null and busiType != ''">
			and c.prod_busi_type = #{busiType}
		</if>
	</sql>

<!-- 描述:新增发放  创建人:biaoxiangd  创建时间:2017-06-04 15:15 -->
	<insert id="insertCouponPut" parameterType="com.ls.ner.billing.market.bo.CouponPutBo" useGeneratedKeys="true" keyProperty="putId">
		insert into e_coupon_put(CPN_ID,PUT_EMP_TYPE,PUT_EMP,CUST_ID,PUT_TIME,PUT_NUM,NOTICE_TYPE,NOTICE_CONT,PUT_CHANNEL,IS_DELAY,IS_ALL_CUST)
		values(#{cpnId},#{putEmpType},#{putEmp},#{custId},#{putTime},#{putNum},#{noticeType},#{noticeCont},#{putChannel},#{isDelay},#{isAllCust})
	</insert>
<!-- 描述:新增发放详细  创建人:biaoxiangd  创建时间:2017-06-04 15:19 -->
	<insert id="insertCouponPutDet" parameterType="com.ls.ner.billing.market.bo.CouponPutDetBo">
		insert into e_coupon_put_det(PUT_ID,PUT_TIME,CUST_ID,MOBILE,PUT_FLAG,FAIL_REASON)
		values(#{putId},#{putTime},#{custId},#{mobile},#{putFlag},#{failReason})
	</insert>
<!-- 描述:调用RPC04-02-003优惠券发放接口后更新发放主表  创建人:biaoxiangd  创建时间:2017-06-05 11:35 -->
	<update id="updateCouponPut" parameterType="com.ls.ner.billing.market.bo.CouponPutBo">
		UPDATE e_coupon_put
		SET put_num = #{putNum}
		WHERE put_id = #{putId}
	</update>
<!-- 描述:调用RPC04-02-003优惠券发放接口后更新发放详细表  创建人:biaoxiangd  创建时间:2017-06-05 11:37 -->
	<update id="updateCouponPutDet" parameterType="com.ls.ner.billing.market.bo.CouponPutDetBo">
		UPDATE e_coupon_put_det
		SET
			PUT_FLAG = #{putFlag},FAIL_REASON = #{failReason}
		WHERE
			PUT_ID = #{putId}
			<if test="custId != null and custId != ''">
				AND CUST_ID = #{custId}
			</if>
			<if test="mobile != null and mobile != ''">
				AND MOBILE = #{mobile}
			</if>
	</update>
	<update id="updateDelayByPutId">
		update e_coupon_put set IS_DELAY = #{delayValue} where PUT_ID = #{putId} and IS_DELAY = #{expectDelay}
	</update>
	<update id="updateCouponPutNum">
		UPDATE e_coupon_put SET PUT_NUM = #{custCount} WHERE PUT_ID = #{putId}
	</update>
	<update id="cumulaAddPutNum">
		UPDATE e_coupon_put SET PUT_NUM = PUT_NUM+#{addPutNum} WHERE PUT_ID = #{putId}
	</update>

	<!-- 描述:获取优惠券发放记录  创建人:biaoxiangd  创建时间:2017-06-09 14:40 -->
	<select id="getCouponPutLog" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="putLogBo">
		SELECT
			if(put_emp = 'SYSADMIN','系统人员',cust_id) put_emp,
			put_id,l.put_emp_type,date_format(l.put_time, '%Y-%m-%d %H:%i:%s') put_time,l.put_num,l.notice_type,l.notice_cont,l.cust_id
	    FROM e_coupon_put l
		<where>
			<if test="cpnId != null and cpnId != ''">
				AND l.cpn_id = #{cpnId}
			</if>
			<if test="putEmpType != null and putEmpType != ''">
				AND l.put_emp_type = #{putEmpType}
			</if>
		</where>
		order by l.put_time desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="getCouponPutLogCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		SELECT COUNT(1)
	    FROM e_coupon_put l
		<where>
			<if test="cpnId != null and cpnId != ''">
				and l.cpn_id = #{cpnId}
			</if>
			<if test="putEmpType != null">
				and l.put_emp_type = #{putEmpType}
			</if>
		</where>
	</select>
<!-- 描述:获取优惠券发放明细  创建人:biaoxiangd  创建时间:2017-06-09 16:39 -->
	<select id="getCouponPutDetLog" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="putLogBo">
		SELECT
		'系统人员' as put_emp, a.put_id,date_format(b.put_time, '%Y-%m-%d %H:%i:%s') put_time,b.cust_id,b.mobile,
		if(b.put_flag = '1','成功','失败') as put_flag,
		if(b.put_flag = '1','',b.fail_reason) as fail_reason
		FROM e_coupon_put a, e_coupon_put_det b
		<where>
			a.put_id = b.put_id
			and a.put_emp_type = '01'
			<if test="cpnId != null and cpnId != ''">
				AND a.cpn_id = #{cpnId}
			</if>
		</where>
		order by b.put_time desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="getCouponPutLogDetCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		SELECT COUNT(1) FROM e_coupon_put a, e_coupon_put_det b
		<where>
			a.put_id = b.put_id
			<if test="cpnId != null and cpnId != ''">
				AND a.cpn_id = #{cpnId}
			</if>
		</where>
		order by b.put_time desc
	</select>
	<!-- 描述:获取优惠券发放  创建人:biaoxiangd  创建时间:2017-06-03 11:12 -->
	<select id="getCouponPut" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT DISTINCT c.cpn_id,c.org_code,c.cpn_name,c.cpn_type,c.prod_busi_type,c.cpn_amt,
			ifnull(cpn_num,'0')as cpn_num, ifnull(lim_get_num,'0') as lim_get_num, ifnull(alrdy_get_num,'0')  as alrdy_get_num,
			date_format(c.cre_time, '%Y-%m-%d %H:%i:%s') cre_time,
			c.cpn_state as cpn_status,
			if ( cpn_time_type = '1',
				concat( date_format(eft_date, '%Y-%m-%d'), '~', date_format(inv_date, '%Y-%m-%d') ),
				concat( '领用或发放后', ifnull(time_duration,'0'))) effect_time,
			date_format(eft_date, '%Y-%m-%d') eft_date, date_format(inv_date, '%Y-%m-%d') inv_date,
			c.cpn_marks,
			IFNULL((SELECT sum(put_num) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'0') put_num,
			cpn_time_type,time_unit,
			CASE
			WHEN EXISTS (SELECT 1 FROM e_coupon_put ep WHERE ep.CPN_ID = c.CPN_ID AND ep.IS_DELAY = 1) THEN 1
			ELSE 0
			END AS IS_DELAY
		FROM e_coupon c
		<where>
			<include refid="getCouponWhere"></include>
			AND ( ( c.EFT_DATE &lt;= now() AND c.INV_DATE &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d') ) OR c.EFT_DATE IS NULL )
		</where>
		ORDER BY cre_time DESC
		<if test="end!=null and end!=0">
			LIMIT #{begin} ,#{end}
		</if>
	</select>
	<!-- 描述::获取优惠券发放数量  创建人:biaoxiangd  创建时间:2017-06-03 11:36 -->
	<select id="getCouponPutCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		SELECT
			COUNT(1)
		FROM e_coupon c
		<where>
			<include refid="getCouponWhere"></include>
			AND ( ( c.EFT_DATE &lt;= now() AND c.INV_DATE &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d') ) OR c.EFT_DATE IS NULL )
		</where>
	</select>
	<select id="queryCustByPutId" resultType="com.ls.ner.billing.market.bo.CouponPutDetBo">
		SELECT
			c.PUT_ID AS putId,
			b.MOBILE AS mobile,
			b.CUST_ID AS custId
		FROM
			e_coupon_put c
				JOIN e_coupon_put_det b ON b.PUT_ID = c.PUT_ID
		WHERE
			c.PUT_ID = #{putId}


	</select>
	<select id="queryCouponPutByDelay" resultType="com.ls.ner.billing.api.market.vo.CouponPutRpcBo">
		SELECT
		e.PUT_ID AS putId,
		e.CPN_ID AS cpnId,
		e.IS_ALL_CUST AS isAllCust,
		e.IS_DELAY AS isDelay,
		e.PUT_TIME AS putTime
		FROM e_coupon_put e
		WHERE e.IS_DELAY=1
		AND #{delayTime} >= e.PUT_TIME
	</select>
	<select id="queryPutDelayByCpnId" resultType="com.ls.ner.billing.market.bo.DelayInfo">
		SELECT
			p.PUT_ID AS putId,
			p.PUT_TIME AS delayTime
		FROM
			e_coupon_put p
		WHERE
			p.CPN_ID = #{cpnId}
		  AND p.IS_DELAY = 1
			LIMIT 1 LOCK IN SHARE MODE
	</select>

	<select id="getSuperpositionFlag" resultType="java.util.Map">
		SELECT SUPERPOSITION_FLAG AS superpositionFlag
		FROM e_dct_cond l
		<where>
			<if test="cpnId != null and cpnId != ''">
				and l.cpn_id = #{cpnId}
			</if>
		</where>
	</select>

</mapper>