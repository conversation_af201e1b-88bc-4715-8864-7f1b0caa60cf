package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.Date;

public class StepConfigBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String mainNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal price;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.SN
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Integer sn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.START_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal startPoint;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.END_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal endPoint;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_step_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String dataOperType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.SYSTEM_ID
     *
     * @return the value of e_step_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.SYSTEM_ID
     *
     * @param systemId the value for e_step_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.MAIN_NO
     *
     * @return the value of e_step_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getMainNo() {
        return mainNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.MAIN_NO
     *
     * @param mainNo the value for e_step_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setMainNo(String mainNo) {
        this.mainNo = mainNo == null ? null : mainNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.PRICE
     *
     * @return the value of e_step_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.PRICE
     *
     * @param price the value for e_step_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.SN
     *
     * @return the value of e_step_config.SN
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Integer getSn() {
        return sn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.SN
     *
     * @param sn the value for e_step_config.SN
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSn(Integer sn) {
        this.sn = sn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.START_POINT
     *
     * @return the value of e_step_config.START_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getStartPoint() {
        return startPoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.START_POINT
     *
     * @param startPoint the value for e_step_config.START_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setStartPoint(BigDecimal startPoint) {
        this.startPoint = startPoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.END_POINT
     *
     * @return the value of e_step_config.END_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getEndPoint() {
        return endPoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.END_POINT
     *
     * @param endPoint the value for e_step_config.END_POINT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setEndPoint(BigDecimal endPoint) {
        this.endPoint = endPoint;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.DATA_OPER_TIME
     *
     * @return the value of e_step_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_step_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_step_config.DATA_OPER_TYPE
     *
     * @return the value of e_step_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_step_config.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_step_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }
}