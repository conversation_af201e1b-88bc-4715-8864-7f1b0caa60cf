<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="会员购买记录"/>
<script type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js"></script>
<ls:body>
    <ls:form name="recordForm" id="recordForm">
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td>
                    <ls:label text="订单编号" ref="recordId"/>
                </td>
                <td colspan="3">
                    <ls:text name="recordId" property="recordId"></ls:text>
                </td>
                <td>
                    <ls:label text="手机号" ref="mobile"/>
                </td>
                <td colspan="3">
                    <ls:text name="mobile" property="mobile"></ls:text>
                </td>
                <td><ls:label text="套餐类型"></ls:label></td>
                <td>
                    <ls:select name="vipType">
                        <ls:option value="all" text="全部"></ls:option>
                        <ls:options property="vipTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
                <td><ls:label text="付费类型"></ls:label></td>
                <td>
                    <ls:select name="payType">
                        <ls:option value="all" text="全部"></ls:option>
                        <ls:options property="payTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
                <td><ls:label text="购买渠道"></ls:label></td>
                <td>
                    <ls:select name="payChannel">
                        <ls:option value="all" text="全部"></ls:option>
                        <ls:option value="01" text="APP"></ls:option>
                        <ls:option value="02" text="小程序"></ls:option>
                    </ls:select>
                </td>
                <td>
                    <div class="pull-right">
                        <ls:button text="查询" onclick="query"/>
                        <ls:button text="清空" onclick="clearAll"/>
                    </div>
                </td>
            </tr>
        </table>
        <table align="center" class="tab_search">
            <tr>
                <td>
                    <ls:grid url="" name="recordGrid" caption="会员购买记录" showRowNumber="false"
                             height="350px" showCheckBox="false">
                        <ls:column caption="订单编号" name="recordId" align="center"/>
                        <ls:column caption="手机号" name="mobile" align="center"/>
                        <ls:column caption="套餐类型" name="vipType" align="center" hidden="true"/>
                        <ls:column caption="套餐类型" name="vipTypeName" align="center"/>
                        <ls:column caption="付费类型" name="payType" align="center" hidden="true"/>
                        <ls:column caption="付费类型" name="payTypeName" align="center"/>
                        <ls:column caption="付款金额" name="amount" align="center"/>
                        <ls:column caption="购买渠道" name="payChannel" align="center" hidden="true"/>
                        <ls:column caption="购买渠道" name="payChannelName" align="center"/>
                        <ls:column caption="购买时间" name="payTimeStr" align="center"/>
                        <ls:column caption="生效时间" name="effectTimeStr" align="center"/>
                        <ls:column caption="到期时间" name="expireTimeStr" align="center"/>
                        <ls:pager pageSize="15,20"></ls:pager>
                    </ls:grid></td>
            </tr>
        </table>
    </ls:form>

    <ls:script>

        query();
        window.query=query;

        function query(){
        var datas={};
        datas = recordForm.getFormData();
        recordGrid.query("~/vip/record/getVipPayRecordList",datas);
        }

        function clearAll(){
        recordForm.clear();
        query();
        };

    </ls:script>
</ls:body>
</html>