<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.xpcharge.dao.IXpPileSendDao">

	<!--查询最新计费版本-->
	<select id="queryTheNewChc" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" resultType="java.util.Map">
		SELECT
		a.ORG_CODE 		orgCode,
		a.STATION_ID 	stationId,
		a.PILE_ID  		pileId,
		a.CHC_NO     	newChcNo,
		a.STATION_ID 	stationId,
		b.CHC_NAME 		newChcName,
		b.EFT_DATE 		eftDate
		FROM
		E_PILE_BILLING a,
		e_charge_billing_conf b
		WHERE
		a.CHC_NO = b.CHC_NO
		<if test="pileId !=null and pileId !=''">
			and a.PILE_ID= #{pileId}
		</if>
		<if test="pileIds !=null and pileIds.size() > 0">
			and a.PILE_ID in
            <foreach item="item" index="index" collection="pileIds" open="(" separator="," close=")">
                #{item}
            </foreach>
		</if>
		<if test="stationId !=null and stationId !=''">
			and a.STATION_ID= #{stationId}
		</if>
		<if test="orgCode !=null and orgCode !=''">
			and a.ORG_CODE= #{orgCode}
		</if>
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<!--查询最新计费版本-->
	<select id="queryTheNewChcNum" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" resultType="int">
		SELECT
		count(1)
		FROM
		E_PILE_BILLING a,
		e_charge_billing_conf b
		WHERE
		a.CHC_NO = b.CHC_NO
		<if test="stationId !=null and stationId !=''">
			and a.STATION_ID= #{stationId}
		</if>
		<if test="pileId !=null and pileId !=''">
			and a.PILE_ID= #{pileId}
		</if>
		<if test="orgCode !=null and orgCode !=''">
			and a.ORG_CODE= #{orgCode}
		</if>

	</select>

	<!--查询当前计费版本-->
	<select id="queryTheCurrentChc" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT b.PILE_ID pileId,b.PILE_NO pileNo,b.CHC_NO chcNo,c.CHC_NAME chcName,b.SEND_TIME sendTime,b.SEND_STATUS sendStatus
		from(
			SELECT a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
			FROM(
				SELECT a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
				FROM E_CHCBILL_SEND a
				<where>
					<if test="pileNoList !=null">
						and a.PILE_NO in
						<foreach item="item" index="index" collection="pileNoList" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
					<if test="pileIdList !=null">
						and a.PILE_ID in
						<foreach item="item" index="index" collection="pileIdList" open="(" separator="," close=")">
							#{item}
						</foreach>
					</if>
				</where>
				ORDER BY a.SEND_TIME desc
			) a GROUP BY a.PILE_NO
		) b LEFT JOIN E_CHARGE_BILLING_CONF c on b.CHC_NO = c.CHC_NO
	</select>

	<!-- 新增充电计费配置下发信息 E_CHCBILL_SEND -->
	<insert id="insertChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHCBILL_SEND
		<trim prefix="(" suffix=")">
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sendTime !=null and sendTime !=''">SEND_TIME,</if>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS,</if>
			<if test="stationId !=null and stationId !=''">STATION_ID,</if>
			<if test="pileNo !=null and pileNo !=''">PILE_NO,</if>
			<if test="pileId !=null and pileId !=''">PILE_ID,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
		</trim>
		<trim prefix="values (" suffix=")">
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sendTime !=null and sendTime !=''">#{sendTime},</if>
			<if test="sendStatus !=null and sendStatus !=''">#{sendStatus},</if>
			<if test="stationId !=null and stationId !=''">#{stationId},</if>
			<if test="pileNo !=null and pileNo !=''">#{pileNo},</if>
			<if test="pileId !=null and pileId !=''">#{pileId},</if>
			now(),'I'
		</trim>
	</insert>

	<!-- 更新充电计费配置下发信息 E_CHCBILL_SEND -->
	<update id="updateChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo">
		update E_CHCBILL_SEND
		<set>
			<choose>
				<when test="sendTime == 'clear'">
					SEND_TIME = null,
				</when>
				<when test="sendTime != null and sendTime != ''">
					SEND_TIME =#{sendTime},
				</when>
			</choose>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS =#{sendStatus},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			CHC_NO = #{chcNo}
		</where>
	</update>

	<select id="queryPileBillCount" parameterType="java.util.Map" resultType="int">
		SELECT
		count(1)
		FROM
			e_pile_billing a
		<where>
			<if test="pileId !=null and  pileId != '' ">
				a.PILE_ID=#{pileId}
			</if>
		</where>
	</select>

	<!--查询最新计费版本-->
	<select id="queryPileBillInfo" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="java.util.Map">
		select
			aa.orgCode,
			aa.stationId,
			aa.pileId,
			(select aem.sub_type from ner_asset.a_equip_model aem,ner_asset.a_equip ae  where ae.equip_id = aa.pileId
			and ae.model_id = aem.model_id) subType,
			aa.newChcNo,
			aa.newChcName,
			aa.eftDate,
			bb.chcName curChcName,
			bb.chcNo curChcNo,
			bb.sendStatus,
			DATE_FORMAT(bb.sendTime,'%Y-%m-%d %H:%i:%s') sendTime
		from (
			SELECT
				a.ORG_CODE orgCode,
				a.STATION_ID stationId,
				a.PILE_ID pileId,
				a.CHC_NO newChcNo,
				b.CHC_NAME newChcName,
				b.EFT_DATE eftDate
			FROM E_PILE_BILLING a, e_charge_billing_conf b
			WHERE a.CHC_NO = b.CHC_NO
			<if test="pileId !=null and pileId !=''">
				and a.PILE_ID= #{pileId}
			</if>
			<if test="stationId !=null and stationId !=''">
				and a.STATION_ID= #{stationId}
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE= #{orgCode}
			</if>
			<if test="orgCodeList !=null">
				and a.ORG_CODE in (
				<foreach collection="orgCodeList" item="item" index="index" separator=",">
					#{item}
				</foreach>
				)
			</if>
			<if test="newChcNo !=null and newChcNo !=''">
				and a.CHC_NO= #{newChcNo}
			</if>
			<if test="subType !=null and subType !=''">
				and a.PILE_ID in (select ae.equip_id from ner_asset.a_equip ae,ner_asset.a_equip_model aem
				where ae.model_id = aem.model_id and aem.SUB_TYPE=#{subType})
			</if>
		) aa left join (
			SELECT
				b.PILE_ID pileId,b.PILE_NO pileNo,b.CHC_NO chcNo,c.CHC_NAME chcName,b.SEND_TIME sendTime,b.SEND_STATUS sendStatus
			from(
				SELECT
					a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
				FROM(
					SELECT a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
					FROM E_CHCBILL_SEND a
					WHERE a.PILE_NO is not null
					<if test="curChcNo !=null and curChcNo !=''">
						and a.CHC_NO= #{curChcNo}
					</if>
					ORDER BY a.SEND_TIME desc
				) a GROUP BY a.PILE_NO
			) b LEFT JOIN E_CHARGE_BILLING_CONF c on b.CHC_NO = c.CHC_NO
			<where>
				<if test="curChcNo !=null and curChcNo !=''">
					and b.CHC_NO= #{curChcNo}
				</if>
			</where>
		) bb on bb.pileId = aa.pileId
		<where>
			<if test="newChcNo !=null and newChcNo !=''">
				and aa.newChcNo= #{newChcNo}
			</if>
			<if test="curChcNo !=null and curChcNo !=''">
				and bb.chcNo= #{curChcNo}
			</if>
			<if test="sendStatus != null and sendStatus != '' and sendStatus == '01'.toString()">
				and (bb.sendStatus is null or aa.newChcNo != bb.chcNo)
			</if>
			<if test="sendStatus != null and sendStatus != '' and (sendStatus == '02' or sendStatus == '03' or sendStatus == '04')">
				and bb.sendStatus= #{sendStatus}
			</if>
			<if test="sendStatus != null and sendStatus != '' and sendStatus == '05'">
				and bb.sendStatus= #{sendStatus} and aa.newChcNo = bb.chcNo
			</if>
		</where>
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="queryPileBillInfoCount" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="int">
		select
			count(*)
		from (
			SELECT
				a.ORG_CODE orgCode,
				a.STATION_ID stationId,
				a.PILE_ID pileId,
				a.CHC_NO newChcNo,
				b.CHC_NAME newChcName,
				b.EFT_DATE eftDate
			FROM E_PILE_BILLING a, e_charge_billing_conf b
			WHERE a.CHC_NO = b.CHC_NO
			<if test="pileId !=null and pileId !=''">
				and a.PILE_ID= #{pileId}
			</if>
			<if test="stationId !=null and stationId !=''">
				and a.STATION_ID= #{stationId}
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE= #{orgCode}
			</if>
			<if test="orgCodeList !=null">
				and a.ORG_CODE in (
				<foreach collection="orgCodeList" item="item" index="index" separator=",">
					#{item}
				</foreach>
				)
			</if>
			<if test="newChcNo !=null and newChcNo !=''">
				and a.CHC_NO= #{newChcNo}
			</if>
			<if test="subType !=null and subType !=''">
				and a.PILE_ID in (select ae.equip_id from ner_asset.a_equip ae,ner_asset.a_equip_model aem
				where ae.model_id = aem.model_id and aem.SUB_TYPE=#{subType})
			</if>
		) aa left join (
			SELECT
				b.PILE_ID pileId,b.PILE_NO pileNo,b.CHC_NO chcNo,c.CHC_NAME chcName,b.SEND_TIME sendTime,b.SEND_STATUS sendStatus
			from(
				SELECT
					a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
				FROM(
					SELECT a.PILE_ID,a.PILE_NO,a.CHC_NO,a.SYSTEM_ID,a.SEND_TIME,a.SEND_STATUS
					FROM E_CHCBILL_SEND a
					WHERE a.PILE_NO is not null
					<if test="curChcNo !=null and curChcNo !=''">
						and a.CHC_NO= #{curChcNo}
					</if>
					ORDER BY a.SEND_TIME desc
				) a GROUP BY a.PILE_NO
			) b LEFT JOIN E_CHARGE_BILLING_CONF c on b.CHC_NO = c.CHC_NO
			<where>
				<if test="curChcNo !=null and curChcNo !=''">
					and b.CHC_NO= #{curChcNo}
				</if>
			</where>
		) bb on bb.pileId = aa.pileId
		<where>
			<if test="newChcNo !=null and newChcNo !=''">
				and aa.newChcNo= #{newChcNo}
			</if>
			<if test="curChcNo !=null and curChcNo !=''">
				and bb.chcNo= #{curChcNo}
			</if>
			<if test="sendStatus != null and sendStatus != '' and sendStatus == '01'.toString()">
				and (bb.sendStatus is null or aa.newChcNo != bb.chcNo)
			</if>
			<if test="sendStatus != null and sendStatus != '' and (sendStatus == '02' or sendStatus == '03' or sendStatus == '04')">
				and bb.sendStatus= #{sendStatus}
			</if>
			<if test="sendStatus != null and sendStatus != '' and sendStatus == '05'">
				and bb.sendStatus= #{sendStatus} and aa.newChcNo = bb.chcNo
			</if>
		</where>
	</select>
</mapper>