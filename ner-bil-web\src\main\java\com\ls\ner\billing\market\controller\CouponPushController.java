package com.ls.ner.billing.market.controller;


import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPushBo;
import com.ls.ner.billing.market.service.ICouponPushService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.def.api.operators.service.IDefrayOperRpcService;
import com.ls.ner.util.OrgCodeUtil;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:优惠券推广管理
 * 创建人:biaoxiangd
 * 创建时间: 2017-06-03 12:08
 */
@Controller
@RequestMapping("/billing/couponPush")
public class CouponPushController extends QueryController<MarketCondition> {

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(value = "couponPushService", serviceTypes = ServiceType.LOCAL)
    private ICouponPushService couponPushService;

    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defrayOperRpcService")
    private IDefrayOperRpcService defrayOperRpcService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC, value="accountService")
    private IAccountService accountService;
    /**
     * 优惠券推广页面
     * description
     * 创建时间 2016年4月11日上午9:31:33
     * 创建人 lise
     */
    @RequestMapping("/couponPushMain")
    public String couponPushMain(Model m, @RequestParam("actId") String actId) {

        CouponPushBo couponPushBo = new CouponPushBo();
        String chargeType = "01";//租车优惠券
        List<CodeBO> cpnStatusList = codeService.getStandardCodes("cpnState", null);
        m.addAttribute("cpnStatusList", cpnStatusList);

        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        couponPushBo.setBusiType("01");

        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);

        couponPushBo.setActId(actId);
        MarketActBo marketActBo = marketActService.queryByPrimaryKey(Long.valueOf(actId));
        couponPushBo.setActType(marketActBo.getActType());

        m.addAttribute("couponPushForm", couponPushBo);


//        List<String> orgList = new ArrayList<>();
//        AccountBo currentAccount = authentication.getCurrentAccount();
//        OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
//        if (orgBo != null) {
//            orgList = OrgCodeUtil.getSubOrg(orgBo.getOrgCode());
//        }
//        Map<String,Object> paramMap = new HashMap<>();
//        paramMap.put("orgCodeList",orgList);
//        paramMap.put("statusCode","01");
//        List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
//        m.addAttribute("buildList",operInfoList);
//
//
//        //获取当前账号
//        String accountName = authentication.getCurrentAccount().getAccountName();
//        //判断当前账号是否是系统账号
//        if (!accountService.isAdmin(accountName)) {
//            //不是系统账号，获取当前系统账号的管理单位
//            OrgBo orgBoT = orgService.getOrgByAccountName(accountName);
//            if (orgBoT != null) {
//                String orgCode = orgBoT.getOrgCode();
//                if (StringUtil.isNotBlank(orgCode)) {
//                    //通过管理单位code查找运营商id
//                    Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
//                    if (buildMap != null) {
//                        String operId = StringUtil.nullToString(buildMap.get("operId"));
//                        m.addAttribute("buildId", operId);
//                    }
//                }
//            }
//        }else{
//            m.addAttribute("currentUser","SYSADMIN");
//        }


        return "/bil/market/coupon/couponPushMain";
    }
    /**
     * 待推广
     * description
     * 创建时间 2016年4月11日上午9:10:36
     * 创建人 lise
     */
    @RequestMapping("/waitPush")
    public String waitPush(Model m, HttpServletRequest request) {
        return "/market/coupon/waitPush";
    }

    /**
     * 推广方式编辑
     * description
     * 创建时间 2016年4月11日上午9:32:37
     * 创建人 lise
     */
    @RequestMapping("/pushCoupon")
    public String pushCoupon(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponPushBo bo = new CouponPushBo();
        bo.setCpnId(cpnId);
        m.addAttribute("pushForm", bo);
        List<CodeBO> pushWayList = new ArrayList<CodeBO>();
        m.addAttribute("pushWayList", pushWayList);
        return "/market/coupon/editPushCoupon";
    }

    /**
     * 已推广
     * description
     * 创建时间 2016年4月11日上午9:10:36
     * 创建人 lise
     */
    @RequestMapping("/donePush")
    public String donePush(Model m, HttpServletRequest request) {
        return "/market/coupon/donePush";
    }

    /**
     * 查询推广信息
     * description
     * 创建时间 2016年4月11日上午10:56:03
     * 创建人 lise
     */
    @RequestMapping("/getCouponsPush")
    public @ItemResponseBody
    QueryResultObject getCouponsPush(@QueryRequestParam("params")
                                             RequestCondition params) throws Exception {
        MarketCondition condition = this.rCondition2QCondition(params);

//        if (StringUtils.isNotBlank(condition.getBuildId())){
//            Map<String,Object> orgCodeMap  = defrayOperRpcService.qryOrgCodeByOperId(condition.getBuildId());
//            if (orgCodeMap != null){
//                String orgCode = StringUtil.nullToString(orgCodeMap.get("orgCode"));
//                if (StringUtils.isNotBlank(orgCode)){
//                    condition.setOrgCode(orgCode);
//                }
//            }
//        }
        if (StringUtils.isBlank(condition.getOrgCode())) {
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if (orgBo != null)
                condition.setOrgCode(orgBo.getOrgCode());
        }
        List<CouponPushBo> dataList = couponPushService.getCouponsPush(condition);
        int count = couponPushService.getCouponsPushCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 删除优惠券推广信息
     * description
     * 创建时间 2016年4月11日上午9:49:49
     * 创建人 lise
     */
    @RequestMapping(value = "/delCouponPush", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult delCouponPush(@RequestBody Map<String,Object> dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean successful = true;
        String resultHint = "", resultValue = "";
        try {
            if (dataParams != null && dataParams.size() > 0) {
                String actId = (String) dataParams.get("actId");
                String pushId = (String) dataParams.get("pushId");
                if (StringUtils.isNotBlank(actId) && StringUtil.isNotBlank(pushId)) {
                    MarketCondition condition = new MarketCondition();
                    condition.setActId((actId));
                    condition.setPushId(pushId);
                    int flag = couponPushService.delCouponPush(condition);
                    if (flag > 0) {
                        successful = true;
                    } else {
                        successful = false;
                        resultHint = "删除优惠券推广信息失败";
                    }
                } else {
                    successful = false;
                    resultHint = "当前选择的优惠券推广信息数据异常";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            successful = false;
            resultHint = e.getMessage();
        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);
        return WrappedResult.successWrapedResult(retMap);
    }

    /**
     * 保存优惠券推广信息
     * description
     * 创建时间 2016年4月11日上午9:49:49
     * 创建人 lise
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/saveCouponPush", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveCouponPush(
            @RequestBody Map<String, Object> dataParams)
            throws Exception {
        boolean successful = true;
        String actCondId = "", resultHint = "", resultValue = "";
        Map<String, Object> retMap = new HashMap<String, Object>();

        CouponPushBo bo = new CouponPushBo();
        BeanUtils.populate(bo, dataParams);

//        AccountBo userBo = authentication.getCurrentAccount();
//        bo.setPushEmp(userBo != null ? userBo.getAccountName() : "SYSADMIN");

        int flag = couponPushService.saveCouponPush(bo);
        if (flag > 0) {
            successful = true;
        } else {
            successful = false;
            resultHint = "添加优惠券失败";

        }
        retMap.put("successful", successful);
        retMap.put("resultHint", resultHint);
        retMap.put("resultValue", resultValue);

        return WrappedResult.successWrapedResult(retMap);
    }


    /**
     * 优惠券推广详细信息
     * description
     * 创建时间 2016年4月11日下午4:21:16
     * 创建人 biaoxiangd
     */
    @RequestMapping("/pushInfo")
    public String pushInfo(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponBo bo = new CouponBo();
        bo.setCpnId(cpnId);
        m.addAttribute("couponPushForm", bo);
        return "/bil/market/coupon/pushInfo";
    }


    @Override
    protected MarketCondition initCondition() {
        return new MarketCondition();
    }

}
