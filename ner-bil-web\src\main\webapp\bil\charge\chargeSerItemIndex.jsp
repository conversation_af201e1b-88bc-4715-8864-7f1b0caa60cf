<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="服务项目维护" >
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<table class="tab_search">
			<tr>
				<td><ls:label text="费用项编号" ref="itemNo" /></td>
				<td><ls:text name="itemNo"></ls:text></td>
				<td><ls:label text="费用项名称" ref="itemName" /></td>
				<td><ls:text name="itemName"></ls:text></td>
				<td><ls:label text="状态" ref="itemStatus"/></td>
				<td><ls:select name="itemStatus" property="itemStatus">
					<ls:options property="itemStatusList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
				<td colspan="3">
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" allowSorting="true" showCheckBox="true" singleSelect="true" caption="服务项目列表">
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doAdd" text="新增"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doModify" text="修改"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="search" onclick="doEnable" text="启用"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="delete" onclick="doDisable" text="停用"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="排序号" name="sn" />
					<ls:column caption="费用项编号" name="itemNo"/>
					<ls:column caption="费用项名称" name="itemName" align="left"/>
					<ls:column caption="费用项单位" name="itemUnitName" />
					<ls:column caption="费用项描述" name="remarks"  align="left"/>
					<ls:column caption="状态" name="itemStatusName" />
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		chargeGrid.query('~/billing/chargeServiceItem/queryChargeSerItems',params,function(){});
	}
	
    function doAdd(){
		LS.dialog("~/billing/chargeServiceItem/addChargeSerItem?itemNo=","新增服务项目",800, 240,true, null);
    }
 	
 	function doModify(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要修改的记录");
    		return;
	   }
		LS.dialog("~/billing/chargeServiceItem/addChargeSerItem?itemNo="+item.itemNo,"修改服务项目",800, 240,true, null);
 	}
    
	function doEnable(){
	   var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要启用的记录");
    		return;
	   }
	   if(item.itemStatus=='1'){
	   		LS.message("error","该服务项已启用，无需重复启用");
	   		return;
	   }
	   LS.confirm('确定要启用该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargeServiceItem/enableChargeSerItem?itemNo="+item.itemNo,null,function(data){
					if(data==true){
						LS.message("info","启用成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});	
	}
    
	function doDisable(){
	   var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要停用的记录");
    		return;
	   }
	   if(item.itemStatus=='0'){
	   		LS.message("error","该服务项已停用，无需重复停用");
	   		return;
	   }
	   LS.confirm('确定要停用该记录?',function(result){
			if(result){
				LS.ajax("~/billing/chargeServiceItem/disableChargeSerItem?itemNo="+item.itemNo,null,function(data){
					if(data==true){
						LS.message("info","停用成功");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});	
	}
	
    function doClear(){
      searchForm.clear();
    }

	window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
	}
    
    </ls:script>
</ls:body>
</html>