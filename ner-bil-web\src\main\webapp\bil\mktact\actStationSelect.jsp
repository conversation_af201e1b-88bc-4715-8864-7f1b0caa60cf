<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券管理"></ls:head>
<ls:body>
    <ls:form name="stationForm" id="stationForm">
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <ls:text visible="false" name="busiType" property="busiType"></ls:text>
            <tr>
            <tr>
                <td>
                    <ls:label text="管理单位" />
                </td>
                <td class="orgCodeClass">
                    <ls:text type="hidden" name="orgCode"/>
                    <ls:text imageKey="search"  onClickImage="getOrg" name="orgCodeName" readOnly="true" onchanged="changeOrgName" />
                </td>
                <td>
                    <ls:label text="站点名称" />
                </td>
                <td>
                    <ls:text name="stationName"></ls:text>
                </td>
            <td>
                <div class="pull-right">
                    <ls:button text="查询" onclick="stationQuery"/>
                </div>
            </td>
            </tr>
            </tr>
            <tr>
                <td colspan="7">
                    <ls:grid url="" name="stationGrid" caption="站点列表" height="230px" showCheckBox="true" singleSelect="false">
                        <ls:gridToolbar name="operation">
                            <ls:gridToolbarItem name="add" text="选择" imageKey="add" onclick="addStation" ></ls:gridToolbarItem>
                        </ls:gridToolbar>
                        <ls:column caption="站点编号" name="stationId" hidden="true"/>
                        <ls:column caption="站点编号" name="stationNo"/>
                        <ls:column caption="站点名称" name="stationName"/>
                        <ls:column caption="管理单位" name="orgCodeName"></ls:column>
                        <ls:column caption="城市" name="cityName"></ls:column>
                        <ls:column caption="详细地址" name="stationAddr" ></ls:column>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        function changeOrgName(){
            if(LS.isEmpty(orgCodeName.getValue())){
                 orgCode.setValue();
            }
        }

        function getOrg(){
            var initValue=null;
            if(!LS.isEmpty(orgCode.getValue())){
             initValue=orgCode.getValue().split(",");
            }
            js_util.selectOrgTree(false, null, true, initValue, false, setOrg);
        }

        function setOrg(node) {
            if (node == null) {
                 return;
            }
            orgCode.setValue(node.id);
            orgCodeName.setValue(node.text);
        }
        <%--stationQuery();--%>
        function stationQuery(){
            if((orgCode.getValue() == null || orgCode.getValue() == '' )&& (stationName.getValue() == null || stationName.getValue() =='' )){
            LS.message("error", "管理单位和站点名称不能同时为空");
            return;
        }
            var data = stationForm.getFormData();
            stationGrid.query("~/billing/coupon/queryStationInfo", data);
        }
        function addStation(){
            var items = stationGrid.getCheckedItems();
            LS.parent().selectCpnStation(items);
            LS.window.close();
        }
    </ls:script>
</ls:body>
</html>