package com.ls.ner.billing.xpcharge.service.impl;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.dao.IChargeBillRpcDao;
import com.ls.ner.billing.charge.dao.IChargeBillingRltPeriodsDao;
import com.ls.ner.billing.charge.dao.IChargePeriodsDao;
import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import com.ls.ner.billing.xpcharge.bo.PeakRelBo;
import com.ls.ner.billing.xpcharge.bo.PeakTimeBo;
import com.ls.ner.billing.xpcharge.dao.IXpPeakDao;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.ls.ner.billing.xpcharge.util.XpConstant;
import com.ls.ner.order.api.constant.OrderRpcConstants;
import com.ls.ner.order.api.dto.OrderExtendDTO;
import com.ls.ner.order.api.service.IChargeOrderRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.param.api.ISysParamService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 尖峰计费
 * @date 2022/6/21 14:53
 */
@Service(target = {ServiceType.APPLICATION}, value = "peakService")
public class XpPeakServiceImpl implements IXpPeakService {

    private static final Logger logger = LoggerFactory.getLogger(XpPeakServiceImpl.class);

    @Autowired
    private IXpPeakDao peakDao;
    @Autowired
    private IChargePeriodsDao periodsDao;
    @ServiceAutowired(serviceTypes= ServiceType.RPC)
    private ISysParamService sysParamService;
    @Autowired
    private ITimeShareServicePriceService servicePriceService;
    @Autowired
    private IChargePeriodsDao chargePeriodsDao;
    @Autowired
    private IChargeBillRpcDao chargeBillRpcDao;
    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;
    @ServiceAutowired(value = "chargeOrderRpcService", serviceTypes = ServiceType.RPC)
    private IChargeOrderRpcService chargeOrderRpcService;
    @ServiceAutowired("xpStationSendService")
    private IXpStationSendService xpStationSendService;
    @Autowired
    private IChargeBillingRltPeriodsDao chargeBillingRltPeriodsDao;

    @Override
    public Boolean peakBillingSwitch() {
        String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
        if(PublicConstants.YN.TRUE.equals(peakBillingSwitch)){
            return true;
        }
        return false;
    }

    @Override
    public void savePeakTime(PeakTimeBo peakTimeBo) {
        PeakTimeBo last = peakDao.selectPeakTimeEffective();
        //插入
        peakDao.insertTimePeak(peakTimeBo);

        List<PeakRelBo> peakRelBos = peakDao.selectPeakRelAll();
        //如果是当天开始或者当天结束，需下发计费
        String currentDate = DateTools.getStringDateShort();
        if (peakTimeBo != null && (currentDate.compareTo(peakTimeBo.getEftDate()) <= 0
                || currentDate.compareTo(peakTimeBo.getInvDate()) >= 0)){
            for (PeakRelBo relBo:peakRelBos) {
                peakDao.updateSendLogSendStatus(relBo.getChcNo());
            }
        }

        if (last != null){
            //插入变更日志
            for (PeakRelBo relBo : peakRelBos){
                relBo.setLastState(XpConstant.BILLING_STATE_ON);
                relBo.setCurrentState(XpConstant.BILLING_STATE_ON);
                relBo.setInvDate(last.getInvDate());
                relBo.setEftDate(last.getEftDate().compareTo(relBo.getCreateTime()) > 0 ? last.getEftDate():relBo.getCreateTime());
            }
            if (!CollectionUtils.isEmpty(peakRelBos)){
                peakDao.insertTimePeakRelLogs(peakRelBos);
            }
        }
    }

    @Override
    public PeakTimeBo queryPeakTimeEffective() {
        PeakTimeBo peakTimeBo = peakDao.selectPeakTimeEffective();
        return peakTimeBo == null ? new PeakTimeBo() : peakTimeBo;
    }

    @Override
    public String addPeakRels(String chcNos,Map<String,Object> map) {
        if (StringUtils.isEmpty(chcNos)){
            return "";
        }

        String unRelChcNos = "";

        String[] chcNoArr = chcNos.split(",");
        List<PeakRelBo> peakRelBos = new ArrayList<>();

        //尖峰时间段,如：11:00-12:00,15:00-17:00
        String peakBillingTimeSlot = sysParamService.getSysParamsValues("peakBillingTimeSlot");
        //倍率
        String peakBillingRate = sysParamService.getSysParamsValues("peakBillingRate");
        logger.debug("peakBillingTimeSlot:{}--------peakBillingRate:{}",peakBillingTimeSlot,peakBillingRate);

        String[] timeSlot = peakBillingTimeSlot.split(",");

        //将原先计费的分时明细添加尖峰计费后拷贝到尖峰分时计费表
        for (String chcNo : chcNoArr) {
            List<ChargePeriodsBo> chargePeriodsBos = periodsDao.selectChargePeriodsByChcNo(chcNo);
            logger.debug("chargePeriodsBos:{}",IJsonUtil.obj2Json(chargePeriodsBos));

            List<ChargePeriodsBo> periodPeaks = new ArrayList<>();
            //过滤无法生成尖峰计费的数据（峰所在的时间对不上）----尖峰电费只针对电费，与服务费无关
            int index = 0;
            int snAdd = 0;
            boolean flag = true;
            for (ChargePeriodsBo period : chargePeriodsBos) {
                logger.debug("period:{}", IJsonUtil.obj2Json(period));

                int sn = Integer.parseInt(period.getSn());

                //尖峰电价每天的执行时段为11-12时、15-17时共三个小时，新增2个尖时段。
                if (XpConstant.TIMEFLAG_F.equals(period.getTimeFlag())){
                    if (index > timeSlot.length){
                        flag = false;
                        break;
                    }
                    String[] solt = timeSlot[index].split("-");
                    //开始时间
                    String bgnTime = solt[0];
                    //结束时间
                    String endTime = solt[1];
                    logger.debug("bgnTime:{}------------endTime:{}",bgnTime,endTime);
                    if (bgnTime.compareTo(period.getBeginTime()) < 0 || endTime.compareTo(period.getEndTime()) > 0){
                        flag = false;
                        break;
                    }
                    ChargePeriodsBo fPeriodOne = new ChargePeriodsBo();
                    ChargePeriodsBo jPeriod = new ChargePeriodsBo();
                    ChargePeriodsBo fPeriodTwo = new ChargePeriodsBo();
                    BeanUtils.copyProperties(period,fPeriodOne);
                    BeanUtils.copyProperties(period,jPeriod);
                    BeanUtils.copyProperties(period,fPeriodTwo);
                    if (!XpConstant.SERVICE.equals(period.getItemNo())){
                        //电费,尖电价在峰时电价基础上上浮25%
                        jPeriod.setPrice(MathUtils.multiply(jPeriod.getPrice(),peakBillingRate));
                    }
                    //重新生成尖峰配置（一个尖，一个峰或者2个峰，一个尖或者全是尖）
                    if (bgnTime.compareTo(period.getBeginTime()) == 0 && endTime.compareTo(period.getEndTime()) < 0){
                        //开始时间等于峰起始时间，结束时间小于峰结束时间-----------一尖一峰
                        jPeriod.setTimeFlag(XpConstant.TIMEFLAG_J);
                        jPeriod.setBeginTime(bgnTime);
                        jPeriod.setEndTime(endTime);
                        jPeriod.setSn(String.valueOf(sn + snAdd));
                        periodPeaks.add(jPeriod);

                        fPeriodOne.setBeginTime(endTime);
                        fPeriodOne.setEndTime(period.getEndTime());
                        fPeriodOne.setSn(String.valueOf(sn + snAdd + 1));
                        periodPeaks.add(fPeriodOne);
                    }else if(bgnTime.compareTo(period.getBeginTime()) > 0 && endTime.compareTo(period.getEndTime()) == 0){
                        //开始时间大于峰起始时间，结束时间等于峰结束时间-----------一尖一峰
                        fPeriodOne.setBeginTime(period.getBeginTime());
                        fPeriodOne.setEndTime(bgnTime);
                        fPeriodOne.setSn(String.valueOf(sn + snAdd));
                        periodPeaks.add(fPeriodOne);

                        jPeriod.setTimeFlag(XpConstant.TIMEFLAG_J);
                        jPeriod.setBeginTime(bgnTime);
                        jPeriod.setEndTime(endTime);
                        jPeriod.setSn(String.valueOf(sn + snAdd + 1));
                        periodPeaks.add(jPeriod);
                    }else if(bgnTime.compareTo(period.getBeginTime()) > 0 && endTime.compareTo(period.getEndTime()) < 0){
                        //开始时间大于峰起始时间，结束时间小于于峰结束时间-----------一尖两峰
                        fPeriodOne.setBeginTime(period.getBeginTime());
                        fPeriodOne.setEndTime(bgnTime);
                        fPeriodOne.setSn(String.valueOf(sn + snAdd));
                        periodPeaks.add(fPeriodOne);

                        jPeriod.setTimeFlag(XpConstant.TIMEFLAG_J);
                        jPeriod.setBeginTime(bgnTime);
                        jPeriod.setEndTime(endTime);
                        jPeriod.setSn(String.valueOf(sn + snAdd + 1));
                        periodPeaks.add(jPeriod);

                        fPeriodTwo.setBeginTime(endTime);
                        fPeriodTwo.setEndTime(period.getEndTime());
                        fPeriodTwo.setSn(String.valueOf(sn + snAdd + 2));
                        periodPeaks.add(fPeriodTwo);
                    }else if(bgnTime.compareTo(period.getBeginTime()) == 0 && endTime.compareTo(period.getEndTime()) == 0){
                        //开始时间等于峰起始时间，结束时间等于峰结束时间-----------一尖
                        jPeriod.setTimeFlag(XpConstant.TIMEFLAG_J);
                        jPeriod.setSn(String.valueOf(sn + snAdd));
                        periodPeaks.add(jPeriod);
                    }

                    if (!XpConstant.SERVICE.equals(period.getItemNo())){
                        snAdd = Integer.parseInt(periodPeaks.get(periodPeaks.size() - 1).getSn()) - sn;
                        index ++;
                    }
                }else {
                    period.setSn(String.valueOf(sn + snAdd));
                    periodPeaks.add(period);
                }
            }
            logger.debug("flag:{}", flag);
            logger.debug("periodPeaks:{}", IJsonUtil.obj2Json(periodPeaks));
            if (!flag){
                unRelChcNos += "," + chcNo;
                continue;
            }
            //先删除，后插入
            peakDao.deletePeriodPeakByChcNo(chcNo);
            peakDao.insertPeriodPeaks(periodPeaks);

            PeakRelBo peakRelBo = new PeakRelBo();
            peakRelBo.setChcNo(chcNo);
            peakRelBos.add(peakRelBo);
        }

        //插入关联关系
        if (!CollectionUtils.isEmpty(peakRelBos)){
            peakDao.insertTimePeakRels(peakRelBos);

            //如果在生效时间里面,将原先计费的下发成功状态修改
            if (checkIsPeak()){
                try {
                    for (PeakRelBo relBo: peakRelBos) {
                        peakDao.updateSendLogSendStatus(relBo.getChcNo());
                        List<String> stationIds = peakDao.selectStationByChcNo(relBo.getChcNo());
                        if(CollectionUtils.isEmpty(stationIds)){
                            continue;
                        }
                        for (String stationId : stationIds) {
                            xpStationSendService.issuredStation(relBo.getChcNo(),stationId,map);
                        }
                    }
                }catch (Exception e){
                    logger.error("尖峰计费新增关联时下发失败" + e.getMessage());
                }
            }
        }

        return unRelChcNos;
    }

    @Override
    public List<PeakRelBo> pagePeakRelBoList(XpChargeBillingConfQueryCondition condition) {
        List<PeakRelBo> relBos = peakDao.pagePeakRelBoList(condition);
        Map<String,String> orgMap = new HashMap<>();
        for (PeakRelBo relBo : relBos ) {
            OrgBo org = orgService.getOrgByNo(relBo!=null?relBo.getOrgCode():"");
            if(org != null && !orgMap.containsKey(relBo.getOrgCode())){
                orgMap.put(relBo.getOrgCode(),org.getOrgShortName());
            }
            relBo.setOrgCodeName(orgMap.get(relBo.getOrgCode()));
        }
        return relBos;
    }

    @Override
    public int countPagePeakRel(XpChargeBillingConfQueryCondition condition) {
        return peakDao.countPageRelBoList(condition);
    }

    @Override
    public List<PeakRelBo> pageUnPeakRelBoList(XpChargeBillingConfQueryCondition condition) {
        List<PeakRelBo> relBos = peakDao.pageUnPeakRelBoList(condition);
        Map<String,String> orgMap = new HashMap<>();
        for (PeakRelBo relBo : relBos ) {
            OrgBo org = orgService.getOrgByNo(relBo!=null?relBo.getOrgCode():"");
            if(org != null && !orgMap.containsKey(relBo.getOrgCode())){
                orgMap.put(relBo.getOrgCode(),org.getOrgShortName());
            }
            relBo.setOrgCodeName(orgMap.get(relBo.getOrgCode()));
        }
        return relBos;
    }

    @Override
    public int countPageUnPeakRel(XpChargeBillingConfQueryCondition condition) {
        return peakDao.countPageUnRelBoList(condition);
    }

    @Override
    public void delPeakRel(String chcNo,Map<String,Object> requestMap) {
        PeakTimeBo timeBo = peakDao.selectPeakTimeEffective();
        PeakRelBo relBo = peakDao.selectPeakRelByChcNo(chcNo);
        //删除分时设置
        //分时设置不删除，保存可做回查
//        peakDao.deletePeriodPeakByChcNo(chcNo);
        //删除关联
        peakDao.deleteTimePeakRelByChcNo(chcNo);
        //插入关联日志

        if (relBo != null && timeBo != null){
            PeakRelBo log = new PeakRelBo();
            log.setChcNo(relBo.getChcNo());
            log.setCurrentState(XpConstant.BILLING_STATE_DEL);
            log.setLastState(XpConstant.BILLING_STATE_ON);
            log.setInvDate(DateTools.getStringDateShort());
            log.setEftDate(timeBo.getEftDate().compareTo(relBo.getCreateTime()) > 0 ? timeBo.getEftDate():relBo.getCreateTime());

            List<PeakRelBo> logs = new ArrayList<>();
            logs.add(log);
            peakDao.insertTimePeakRelLogs(logs);

            //重新下发计费
            peakDao.updateSendLogSendStatus(relBo.getChcNo());

            try {
                List<String> stationIds = peakDao.selectStationByChcNo(chcNo);
                if(!CollectionUtils.isEmpty(stationIds)){
                    for (String stationId : stationIds) {
                        xpStationSendService.issuredStation(chcNo,stationId,requestMap);
                    }
                }
            }catch (Exception e){
                logger.error("尖峰计费移除时下发失败" + e.getMessage());
            }
        }
    }

    @Override
    public void addPeakRelLogs(PeakTimeBo peakTimeBo) {

    }

    @Override
    public List<ChargePeriodsBo> queryPeriodListCheckPeak(ChargeBillingConfQueryCondition condition) {
        if (this.checkIsPeak(condition.getChcNo())){
            return peakDao.selectPeriodPeakListByChcNo(condition);
        }
        return chargePeriodsDao.queryChargePeriods(condition);
    }

    @Override
    public List<ChargePeriodsBo> queryPeriodListPeak(ChargeBillingConfQueryCondition condition) {
        return peakDao.selectPeriodPeakListByChcNo(condition);
    }

    @Override
    public List<Map> queryChargePeriods(String chcNo, String[] chcNos) {
        String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
        String peakChcNoStr = "";
        String normalChcNoStr = "";
        if (PublicConstants.YN.TRUE.equals(peakBillingSwitch)){
            //判断尖峰计费是否启用,且在有效期内
            PeakTimeBo timeBo = peakDao.selectPeakTimeEffective();
            String currentDate = DateTools.getStringDateShort();
            if (timeBo != null && currentDate.compareTo(timeBo.getEftDate()) >= 0
                    && currentDate.compareTo(timeBo.getInvDate()) < 0){
                //判断是否有关联尖峰计费，且在生效期内
                List<PeakRelBo> relBos = peakDao.selectPeakRelByChcNos(chcNos);
                for (PeakRelBo relBo : relBos) {
                    /*if (relBo.getCreateTime().compareTo(currentDate) < 0){
                        peakChcNoStr+= "," + relBo.getChcNo();
                    }*/
                    peakChcNoStr+= "," + relBo.getChcNo();
                }
            }
        }

        if (StringUtil.isNotEmpty(peakChcNoStr)){
            String[] peakChcNos = peakChcNoStr.substring(1).split(",");

            for (String peakChcNo:peakChcNos) {
                for (String normal : chcNos) {
                    if (!peakChcNo.equals(normal)){
                        normalChcNoStr += "," + normal;
                    }
                }
            }
            List<Map> peakPeriods = peakDao.queryChargePeriods(chcNo, peakChcNos);
            if (StringUtil.isNotEmpty(normalChcNoStr)){
                List<Map> normalPeriods = chargeBillRpcDao.queryChargePeriods(chcNo,normalChcNoStr.substring(1).split(","));
                peakPeriods.addAll(normalPeriods);
            }
            return peakPeriods;
        }else {
            return chargeBillRpcDao.queryChargePeriods(chcNo,chcNos);
        }
    }

    @Override
    public List<Map<String, Object>> getStationChargePeriods(Map map) {
        return chargeBillRpcDao.getStationChargePeriods(map);
    }

    @Override
    public List<Map<String, Object>> getNewStationChargePeriods(Map map) {
        String chcNo = peakDao.selectChcNoByStation(map);
        if (StringUtil.isNotEmpty(chcNo) && this.checkIsPeak(chcNo)){
            return peakDao.getNewStationChargePeriods(map);
        }
        return chargeBillRpcDao.getNewStationChargePeriods(map);
    }

    @Override
    public List<Map<String, Object>> getPileStationChargePeriods(Map map) {
        return chargeBillRpcDao.getPileStationChargePeriods(map);
    }

    @Override
    public List<Map> getChargeBillRltModel(Map map) {
        //判断是否有关联尖峰计费
        if (this.checkOrderIsPeak(String.valueOf(map.get("orderNo")))){
            return peakDao.getChargeBillRltModel(map);
        }
        return chargeBillingRltPeriodsDao.getChargeBillRltModel(map);
    }

    @Override
    public void peakEffTast() {
        String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
        if (PublicConstants.YN.TRUE.equals(peakBillingSwitch)){
            //判断尖峰计费是否启用,且生效日期那天天或者结束日期那天，重新下发计费
            PeakTimeBo timeBo = peakDao.selectPeakTimeEffective();
            String currentDate = DateTools.getStringDateShort();
            if (timeBo != null && (currentDate.compareTo(timeBo.getEftDate()) == 0
                    || currentDate.compareTo(timeBo.getInvDate()) == 0)){
                List<PeakRelBo> peakRelBos = peakDao.selectPeakRelAll();
                for (PeakRelBo relBo:peakRelBos) {
                    peakDao.updateSendLogSendStatus(relBo.getChcNo());
                }
            }
        }
    }

    @Override
    public List<PeakRelBo> selectPeakRelAll() {
        return peakDao.selectPeakRelAll();
    }

    @Override
    public Boolean checkOrderIsPeak(String orderNo) {
        OrderExtendDTO extend = chargeOrderRpcService
                .getOrderExtendByOrderNoAndRelType(orderNo, OrderRpcConstants.RelType.REL_TYPE_PEAK);
        if (extend != null){
            return true;
        }
        return false;
    }

    @Override
    public void saveOrderExtend(String ordNo, String chcNo) {
        if (StringUtil.isEmpty(ordNo) || StringUtil.isEmpty(chcNo)){
            return;
        }
        if (this.checkIsPeak(chcNo)){
            logger.debug("添加订单与尖峰计费关联orderNo=" + ordNo + "-----chcNo=" + chcNo);
            chargeOrderRpcService.addOrderExtend(ordNo, OrderRpcConstants.RelType.REL_TYPE_PEAK);
        }
    }

    /**
     * @Description 校验计费是否属于尖峰计费
     * <AUTHOR>
     * @Date 2022/6/27 20:25
     * @param chcNo
     */
    private boolean checkIsPeak(String chcNo){
        String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
        if (PublicConstants.YN.TRUE.equals(peakBillingSwitch)){
            //判断尖峰计费是否启用,且在有效期内
            PeakTimeBo timeBo = peakDao.selectPeakTimeEffective();
            String currentDate = DateTools.getStringDateShort();
            if (timeBo != null && currentDate.compareTo(timeBo.getEftDate()) >= 0
                    && currentDate.compareTo(timeBo.getInvDate()) < 0){
                //判断是否有关联尖峰计费，且在生效期内
                PeakRelBo relBo = peakDao.selectPeakRelByChcNo(chcNo);
                /*if (relBo != null && relBo.getCreateTime().compareTo(currentDate) < 0){
                    return true;
                }*/
                if (relBo != null){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @Description 校验计费是否属于尖峰计费
     * <AUTHOR>
     * @Date 2022/6/27 20:26
     */
    private boolean checkIsPeak(){
        String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
        if (PublicConstants.YN.TRUE.equals(peakBillingSwitch)){
            //判断尖峰计费是否启用,且在有效期内
            PeakTimeBo timeBo = peakDao.selectPeakTimeEffective();
            String currentDate = DateTools.getStringDateShort();
            if (timeBo != null && currentDate.compareTo(timeBo.getEftDate()) >= 0
                    && currentDate.compareTo(timeBo.getInvDate()) < 0){
                return true;
            }
        }
        return false;
    }
}
