<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券管理">
    <script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
    <ls:form name="couponForm" id="couponForm">

        <ls:text name="handleType" property="handleType" visible="false"></ls:text>
        <ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
        <ls:text name="actId" property="actId" visible="false"></ls:text>


        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="优惠券名称"></ls:label></td>
                <td><ls:text name="cpnName"></ls:text></td>

                <td><ls:label text="优惠券内容"></ls:label></td>
                <td>
                    <ls:select name="busiType">
<%--                        <ls:options property="busiTypeList" scope="request" text="codeName" value="codeValue"/>--%>
                        <ls:option text="充电" value="02"></ls:option>
                    </ls:select>
                </td>

                <td><ls:label text="优惠券状态" id="lab_cpnStatus"></ls:label></td>
                <td>
                    <ls:select name="cpnStatus" id="sel_cpnStatus">
                        <ls:options property="cpnStatusList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>

                <td><ls:label text="优惠类型" id="lab_cpnType"></ls:label></td>
                <td>
                    <ls:select name="cpnType" property="cpnType" id="sel_cpnType">
                        <ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
            </tr>
            <tr>
               <%-- <c:choose>
                    <c:when test="${currentUser == 'SYSADMIN' }">
                        <td><ls:label text="运营商"/></td>
                        <td>
                            <ls:select name="buildId" property="buildId" >
                                <ls:options property="buildList" scope="request" text="operName" value="operNo"/>
                            </ls:select>
                        </td>
                    </c:when>
                    <c:otherwise>
                        <td><ls:label text="运营商"/></td>
                        <td>
                            <ls:select name="buildId" property="buildId"  readOnly="true" >
                                <ls:options property="buildList" scope="request" text="operName" value="operNo"/>
                            </ls:select>
                        </td>
                    </c:otherwise>
                </c:choose>--%>
                <td colspan="8">
                    <div class="pull-right">
                        <ls:button text="查询" onclick="query"/>
                        <ls:button text="清空" onclick="clearAll"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="8"><ls:grid url="" name="couponGrid" caption="优惠券列表" showRowNumber="false"
                                         height="350px" showCheckBox="false" singleSelect="${(singleSelect=='' || singleSelect == null)?'true':singleSelect}">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="add" text="创建" imageKey="add"
                                            onclick="addCoupon" visible="${readOnly}"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="modify" text="修改" imageKey="edit"
                                            onclick="modifyCoupon" visible="${readOnly}"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="start" text="启用" imageKey="add"
                                            onclick="enableCoupon" visible="${readOnly}"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="stop" text="停用" imageKey="stop"
                                            onclick="stopCoupon" visible="${readOnly}"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="cancel" text="作废" imageKey="cancel"
                                            onclick="cancelCoupon" visible="${readOnly}"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="select" text="选择" imageKey="add"
                                            onclick="selectCoupon"></ls:gridToolbarItem>
                    </ls:gridToolbar>
                    <ls:column caption="优惠券编号" name="cpnNo" hidden="true"/>
                    <ls:column caption="主键" name="cpnId" hidden="true"/>
                    <ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="20%"/>
                    <ls:column caption="优惠券类型" name="cpnType" align="center" hidden="true"/>
                    <ls:column caption="优惠券类型" name="cpnTypeName" align="center" width="8%"/>
                    <ls:column caption="优惠券内容" name="busiType" align="center" hidden="true"/>
                    <ls:column caption="优惠券内容" name="busiTypeName" align="center" width="8%"/>
                    <ls:column caption="面额/折扣(元/折)" name="cpnAmt" align="right" width="6%"/>
                    <ls:column caption="发行数量" name="cpnNumOrigin" align="center" width="6%"/>
                    <ls:column caption="发放数量" name="cpnNum" align="center" width="5%"/>
                    <ls:column caption="每人限领" name="limGetNum" align="center" width="5%"/>
                    <ls:column caption="已领数量" name="alrdyGetNum" align="center" width="5%"/>
                    <ls:column caption="有效时间范围" name="effectTime" align="center" width="20%"/>
                    <ls:column caption="状态" name="cpnStatusName" align="center" width="5%"/>
                    <ls:column caption="状态" name="cpnStatus" hidden="true"/>
                    <ls:column caption="创建时间" name="creTime" align="center" width="15%"/>
                    <ls:column caption="生效时间" name="eftDate" hidden="true"/>
                    <ls:column caption="失效时间" name="invDate" hidden="true"/>
                    <ls:column caption="每人限领数量" name="limGetNum" hidden="true"/>
                    <ls:column caption="优惠券生效时间类型" name="cpnTimeType" hidden="true"/>
                    <ls:column caption="生效时间偏移量" name="timeDuration" hidden="true"/>
                    <ls:column caption="生效时间偏移单位" name="timeUnit" hidden="true"/>
                    <ls:pager pageSize="15,20"></ls:pager>
                </ls:grid></td>
            </tr>
        </table>
    </ls:form>

    <ls:script>
        initShow();
        window.onload = function(){
            initShow();
        }
        function initShow(){
            var _handleType = handleType.getValue();
            <%-- 描述:03展现管理界面；04展现查询界面；05发放功能中触发选择功能  创建人:biaoxiangd  创建时间:2017-06-07 20:35 --%>
            if("03" === _handleType){
                $("#t__id_couponGrid").show();
                $("#couponGridselect").hide();
                cpnType.setValue("");
                $("#lab_cpnType").hide();
                $("#sel_cpnType").hide();
            }else if("04" === _handleType){
                $("#t__id_couponGrid").hide();
                $("#couponGridselect").hide();
            }else if("05" === _handleType || "06" === _handleType){
                $("#couponGridadd").hide();
                $("#couponGridmodify").hide();
                $("#couponGridstart").hide();
                $("#couponGridstop").hide();
                $("#couponGridcancel").hide();
                $("#couponGridselect").show();
                cpnStatus.setValue("1");
                couponGrid.setHideCol(['cpnStatusName']);
                cpnStatus.setValue("");
                $("#lab_cpnStatus").hide();
                $("#sel_cpnStatus").hide();
            }else{
                $("#t__id_couponGrid").show();
            }
        }
        init();
        query();
        function init(){
            cpnStatus.setValue("1");
        }

        <%-- 描述:点击数据行事件  创建人:biaoxiangd  创建时间:2017-06-07 20:35 --%>
        couponGrid.onitemclick=function(item, event, rowid){
            if(item.cpnStatus == "1" ){ //在用
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.start.setEnabled(false);
                couponGrid.toolbarItems.stop.setEnabled(true);
                couponGrid.toolbarItems.cancel.setEnabled(true);
            }else if(item.cpnStatus == "0"){ // 草稿
                couponGrid.toolbarItems.modify.setEnabled(true);
                couponGrid.toolbarItems.start.setEnabled(true);
                couponGrid.toolbarItems.stop.setEnabled(false);
                couponGrid.toolbarItems.cancel.setEnabled(true);
            }else if(item.cpnStatus == "2"){ // 失效
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.start.setEnabled(true);
                couponGrid.toolbarItems.stop.setEnabled(false);
                couponGrid.toolbarItems.cancel.setEnabled(true);
            }else if(item.cpnStatus == "3"){ // 作废
                couponGrid.toolbarItems.modify.setEnabled(false);
                couponGrid.toolbarItems.start.setEnabled(false);
                couponGrid.toolbarItems.stop.setEnabled(false);
                couponGrid.toolbarItems.cancel.setEnabled(false);
            }
        }

        function addCoupon(){
            LS.dialog("~/billing/coupon/editCoupon?handleType=01","创建优惠券",950,500,true, null);
        }

        function modifyCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            if("1"==item.cpnStatus){
                LS.message("info","启用状态不可修改!");
                return;
            }
            LS.dialog("~/billing/coupon/editCoupon?handleType=02&cpnId="+item.cpnId,"修改优惠券",950,450,true, null);
        }


        function enableCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'1'
            };
            LS.ajax("~/billing/coupon/updateStatus",params,function(data){
                if(data){
                    LS.message("info","启用成功!");
                    query();
                }else{
                    LS.message("info",data.msg);
                }
            });
        }
        function stopCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'2'
            };
            LS.ajax("~/billing/coupon/updateStatus",params,function(data){
                if(data){
                    LS.message("info","停用成功!");
                    query();
                }else{
                    LS.message("info",data.msg);
                }
            });
        }

        window.query=query;
        function query(){
            var datas={};
            datas = couponForm.getFormData();
            if(${readOnly } != null && ${readOnly }==true){
                datas.allCoupon = "01";
            }
            couponGrid.query("~/billing/coupon/getCoupons",datas);
        }

        function clearAll(){
            couponForm.clear();
        };

        function linkFunc(rowdata){
        var _cpnId = rowdata.cpnId;
        var _cpnName = escapeHtml(rowdata.cpnName);
        return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnId + "\")'>"+_cpnName+"</a></u>";
        }
        function escapeHtml(unsafe) {
            if (typeof unsafe !== 'string') {
                return unsafe;
            }
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            const reg = /[&<>"']/g;
            return unsafe.replace(reg, match => map[match]);
        }

        window.showCouponDetail = function(_cpnId){
            <%--LS.dialog("~/billing/coupon/initCouponDetail?cpnId="+_cpnId,"优惠券详细信息",950, 550,true);--%>
            LS.dialog("~/billing/coupon/newInitCouponDetail?cpnId="+_cpnId,"优惠券详细信息",950, 550,true);
        }

        function cancelCoupon(){
            var item = couponGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                cpnNo:item.cpnNo,
                cpnId:item.cpnId,
                cpnStatus:'3'
            };
            LS.ajax("~/billing/coupon/updateStatus",params,function(data){
                if(data){
                    LS.message("info","作废成功!");
                    query();
                }else{
                    LS.message("info",data.msg);
                }
            });
        }
        <%-- 描述:选择按钮  创建人:biaoxiangd  创建时间:2017-06-07 20:34 --%>
        function selectCoupon(){
            var item;
            if("${singleSelect}" == 'true' || "${singleSelect}"== null || "${singleSelect}"==''){
                var item = couponGrid.getSelectedItem();
            }else{
                var item = couponGrid.getCheckedItems();
            }
            if(item==null){
                LS.message("info","请选择一条记录");
                return;
            }
            LS.parent().setCoupon(item);
            LS.window.close();
        }

        window.onload = function() {
            initGridHeight('couponForm','couponGrid');
        }
    </ls:script>
</ls:body>
</html>
