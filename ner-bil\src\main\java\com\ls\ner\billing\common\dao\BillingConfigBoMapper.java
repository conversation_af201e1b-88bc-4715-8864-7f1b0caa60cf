package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.common.bo.BillingConfigBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface BillingConfigBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int countByExample(BillingConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int deleteByExample(BillingConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int insert(BillingConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int insertSelective(BillingConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    List<BillingConfigBo> selectByExample(BillingConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    BillingConfigBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByExampleSelective(@Param("record") BillingConfigBo record, @Param("example") BillingConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByExample(@Param("record") BillingConfigBo record, @Param("example") BillingConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByPrimaryKeySelective(BillingConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_config
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByPrimaryKey(BillingConfigBo record);
}