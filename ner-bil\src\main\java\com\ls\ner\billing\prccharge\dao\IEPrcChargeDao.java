/**
 *
 * @(#) IEPrcChargeDao.java
 * @Package com.ls.ner.billing.prccharge.dao
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.prccharge.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.prccharge.bo.EPrcChargeBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年5月1日 上午10:34:20   lipf   Created.
 *           
 */
public interface IEPrcChargeDao {

	/**
	 * 
	 * 方法说明：批量插入计费结果
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 上午10:34:32
	 * History:  2016年5月1日 上午10:34:32   lipf   Created.
	 *
	 * @param list
	 *
	 */
	void insertEPrcCharge(List<EPrcChargeBo> list);

	/**
	 * 
	 * 方法说明：批量插入计费结果明细
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 上午10:34:40
	 * History:  2016年5月1日 上午10:34:40   lipf   Created.
	 *
	 * @param list
	 *
	 */
	void insertEPrcChargeDet(List<EPrcChargeDetBo> list);

	List<EPrcChargeBo> getPrcCharge(Map<String, String> paraMap);

	List<EPrcChargeDetBo> getPrcChargeDet(Map<String, String> paraMap);

	/**
	 * 通过app_no和计费类型billing_type更新订单总费用
	 * @param tMap
	 */
	void updateEprcCharge(Map<String, Object> tMap);

}
