<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="首单免" >
	<script type="text/javascript" charset="utf-8" src="<%=pubPath %>/pub/validate/validation.js"></script>
	<style>

	</style>
</ls:head>

<ls:body>
	<ls:layout-use>
	<ls:form id="firstFreeForm" name="firstFreeForm">
		<ls:text name="actId" property="actId" type="hidden" />
		<ls:text name="actType" property="actType" type="hidden" />
		<ls:text name="actCondId" property="actCondId" type="hidden" />
		<ls:text name="busiType" property="busiType" type="hidden" />
		<ls:layout-put into="container">
			<table>
				<tr>
					<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
				</tr>
			</table>
			<ls:title text="优惠条件" expand="false"></ls:title>
			<table align="center" class="tab_search">
				<colgroup>
					<col width="15%"/>
					<col width="35%"/>
					<col width="18%"/>
					<col width="32%"/>
				</colgroup>
				<tr>
					<td><ls:label text="优惠方式" ref="dctLvl" /></td>
					<td>
						<ls:checklist type="radio" name="dctLvl" value="1" property="dctLvl" required="true" >
							<ls:checkitem value="0" text="普通" ></ls:checkitem>
							<ls:checkitem value="1" text="多级(5级)" ></ls:checkitem>
						</ls:checklist>
					</td>

					<td><ls:label text="优惠条件类别"  ref="dctCondType"/></td>
					<td>
						<ls:select  name="dctCondType" property="dctCondType" required="true" onchanged="doDctCondType">
							<ls:options property="dctCondTypeList" scope="request" text="codeName" value="codeValue"></ls:options>
						</ls:select>
					</td>
				</tr>

				<tr id="billId">
					<td><ls:label text="适用用户" ref="appUser" /></td>
					<td>
						<ls:checklist type="radio" name="appUser" value="03" property="appUser" required="true" >
							<ls:checkitem value="03" text="全部" ></ls:checkitem>
							<ls:checkitem value="01" text="个人" ></ls:checkitem>
							<ls:checkitem value="02" text="企业" ></ls:checkitem>
						</ls:checklist>
					</td>

					<td><ls:label text="是否同时享受企业计费" ref="pBillFlag" /></td>
					<td>
						<ls:checklist type="radio" name="pBillFlag" value="0" property="pBillFlag" required="true" >
							<ls:checkitem value="0" text="否" ></ls:checkitem>
							<ls:checkitem value="1" text="是" ></ls:checkitem>
						</ls:checklist>
					</td>
				</tr>
				<tr id="stationFlagId">
					<td><ls:label text="适用站点" ref="allStationFlag" /></td>
					<td>
						<ls:checklist type="radio" name="allStationFlag" value="1" property="allStationFlag" required="true" onchanged="allStationFlagChan">
							<ls:checkitem value="1" text="全部" ></ls:checkitem>
							<ls:checkitem value="0" text="部分" ></ls:checkitem>
						</ls:checklist>
					</td>
				</tr>

				<tr>
					<td id="td_lab_prodId"><ls:label text="优惠条件对象" /></td>
					<td id="td_sel_prodId">
						<ls:select  name="prodId" property="prodId" >
							<ls:options property="prodIdList" scope="request" text="codeName" value="codeValue"></ls:options>
						</ls:select>
					</td>
					<td id="td_lab_unit"><ls:label text="数值单位" /></td>
					<td id="td_sel_unit">
						<ls:select  name="dctCondUnit" property="dctCondUnit" >
							<ls:options property="dctCondUnitList" scope="request" text="codeName" value="codeValue"></ls:options>
						</ls:select>
					</td>
				</tr>
				<tr>
					<td>&nbsp;</td>
					<td>&nbsp;</td>
					<td>&nbsp;</td>
					<td class="pull-right">
						<ls:button text="保存" onclick="saveDctDet" />
					</td>
				</tr>

				<tr id="stationGId">
					<td colspan="4">
						<ls:grid url="" name="cpnStationGrid" height="150px" showCheckBox="true" singleSelect="false" caption="站点列表" primaryKey="actStationId">
							<ls:gridToolbar name="cityGridBar">
								<ls:gridToolbarItem name="selectStationBtn" text="添加" onclick="stationSelect" imageKey="add"/>
								<ls:gridToolbarItem name="deleteStationBtn" text="删除" onclick="stationDelete" imageKey="delete"/>
							</ls:gridToolbar>
							<ls:column name="actStationId" caption="" hidden="true" />
							<ls:column name="stationId" caption="" hidden="true" />
							<ls:column name="stationName" caption="站点"/>
							<ls:pager pageSize="10"></ls:pager>
						</ls:grid>
					</td>
				</tr>

				<tr>
					<td colspan="4">
						<ls:grid url="" name="actDctGrid" caption="优惠条件内容" showRowNumber="true"
								 height="240px" showCheckBox="false" singleSelect="true" treeGrid="true" treeGridModel="adjacency">
							<ls:gridToolbar name="operation">
								<ls:gridToolbarItem name="addBtn" text="新增" imageKey="add" onclick="addAct"></ls:gridToolbarItem>
								<ls:gridToolbarItem name="delBtn" text="删除" imageKey="delete" onclick="delAct"></ls:gridToolbarItem>
							</ls:gridToolbar>
							<ls:column caption="优惠条件明细ID" name=" actCondDetId" hidden="true"></ls:column>
							<ls:column caption="优惠条件ID" name="actCondId" hidden="true"></ls:column>
							<ls:column caption="优惠内容ID" name="actDctId" hidden="true"></ls:column>
							<ls:column caption="优惠类别" name="dctType" hidden="true"></ls:column>
							<ls:column caption="优惠类别" name="dctTypeName"></ls:column>
							<ls:column caption="优惠条件" name="dctCondValue" ></ls:column>
							<ls:column caption="优惠对象" name="actDctProdId" hidden="true"></ls:column>
							<ls:column caption="优惠对象" name="actDctProdName" hidden="true"></ls:column>
							<ls:column caption="是否全免" name="allFree" ></ls:column>
							<ls:column caption="优惠数值" name="dctValue" ></ls:column>
						</ls:grid>
					</td>
				</tr>
			</table>
		</ls:layout-put>
		<ls:layout-put into="footer">
			<div class="pull-right">
				<ls:button text="上一步" onclick="doUp" />
				<%--<ls:button text="下一步" onclick="doPut" />--%>
				<ls:button text="保存" onclick="doSave" />
				<ls:button text="发布" onclick="doRelease" />
			</div>
		</ls:layout-put>
	</ls:form>
	</ls:layout-use>

	<ls:script>
	window.actDctGrid = actDctGrid;
	window.queryGrid = queryGrid;
	window.queryActStation=queryActStation;

	window.onload = function(){
		doDctCondType();
		queryGrid();
	}

	function queryGrid(){
		var params =  firstFreeForm.getFormData();
		actDctGrid.query("~/marketacts/firstFreeManage/queryActDctAll",params);
	}

<%-- 描述:保存优惠条件  创建人:biaoxiangd  创建时间:2017/6/29 15:24 --%>
	function saveDctDet(){
		if(!firstFreeForm.valid()){
			return;
		}
		var params = firstFreeForm.getFormData();
		LS.ajax("~/marketacts/firstFreeManage/saveActCond",params,function(data){
			if(data.successful){
				LS.message("info","保存成功!");
				actCondId.setValue(data.resultValue);
			}else{
				LS.message("info",data.resultHint);
			}
		});
	}
<%-- 描述:选择优惠条件类别  00 整单金额;01	计价数量;02	计价金额 创建人:biaoxiangd  创建时间:2017/6/29 15:17 --%>
	function doDctCondType(){
		var _dctCondType = dctCondType.getValue();
		if('00' == _dctCondType){
			$('#td_lab_prodId').hide();
			$('#td_sel_prodId').hide();
			$('#td_lab_unit').hide();
			$('#td_sel_unit').hide();
		}else if('01' == _dctCondType){
			$('#td_lab_prodId').show();
			$('#td_sel_prodId').show();
			$('#td_lab_unit').hide();
			$('#td_sel_unit').hide();
		}else if('02' == _dctCondType){
			$('#td_sel_prodId').show();
			$('#td_sel_prodId').show();
			$('#td_lab_unit').show();
			$('#td_sel_unit').show();
		}

		var _actType = actType.getValue();
		if(_actType == '03'){
		$('#billId').show();
		$('#stationFlagId').show();
		$('#stationGId').show();
		queryActStation();
		allStationFlagChan();
		}else{
		$('#billId').hide();
		$('#stationFlagId').hide();
		$('#stationGId').hide();
		}


	}
<%-- 描述:上一步  创建人:biaoxiangd  创建时间:2017/6/29 15:19 --%>
		function doUp(){
			var actIdValue = actId.getValue();
			window.location.href="<%=request.getContextPath() %>/marketacts/edit?actId="+actIdValue+"&actType="+actType.getValue();
		}
<%-- 描述:发布  创建人:biaoxiangd  创建时间:2017/6/29 17:59 --%>
		function doPut(){
			<%--LS.window.close();--%>
			var actIdValue = actId.getValue();
			window.location.href="<%=request.getContextPath() %>/marketacts/actInfo?actId="+actIdValue+"&actType=";
		}


		function doSave(){
		    LS.window.close();
		}

		function doRelease(){
			var params = firstFreeForm.getFormData();
			LS.ajax("~/marketacts/releaseAct",params,function(data){

				if(data.successful){
					LS.message("info",data.resultHint);
					LS.parent().doSearch();
					LS.window.close();
				}else{
					LS.message("info",data.resultHint);
				}
			});
		}

		function saveActBil(){
		if(!firstFreeForm.valid()){
			return;
		}
		var params = firstFreeForm.getFormData();
		LS.ajax("~/marketacts/firstFreeManage/saveActCond",params,function(data){
		if(data.successful){

		}else{
		LS.message("info",data.resultHint);
		}
		});
		}


<%-- 描述:优惠内容-表格-新增  创建人:biaoxiangd  创建时间:2017/6/29 15:42 --%>
	function addAct(){
		var _dctLvl = dctLvl.getValue();
		var items = actDctGrid.getItems();
		if('1' == _dctLvl && items.length > 5){
			LS.message("info","优惠条件内容最多只支持5级");
			return false;
		}

		var _actCondId = actCondId.getValue();
		if(LS.isEmpty(_actCondId)){
			LS.message("info","请点击[保存]按钮后，再新增优惠条件内容");
			return false;
		}
		LS.dialog("~/marketacts/firstFreeManage/actDet?actCondId="+actCondId.getValue()+"&dctLvl="+_dctLvl,"优惠条件明细",650,360,true, null);
	}
<%-- 描述:优惠内容-表格-删除  创建人:biaoxiangd  创建时间:2017/6/29 15:42 --%>
	function delAct(){
		var checkedItems = actDctGrid.getCheckedItems();
		if(checkedItems==null || checkedItems.length!=1){
			LS.message("info","请选择一条记录!");
			return;
		}
		var dataParams = checkedItems[0];
		LS.ajax("~/marketacts/firstFreeManage/delActCondDet",dataParams,function(data) {
			if(data.successful){
				LS.message("info","删除成功");
				queryGrid();
			}else{
				LS.message("error",data.resultHint);
			}
		});
	}

		//删除
		function stationDelete() {
		var items = cpnStationGrid.getCheckedItems();
		if (items == null || items == '') {
		LS.message('info', '请选择一条记录');
		return;
		}
		LS.confirm('确认删除?', function (data) {
		if (data) {
		var params = {};
		params.items = items;
		LS.ajax("~/marketacts/firstFreeManage/del", params, function (e) {
		if (e.items[0].actStationId != null && e.items[0].actStationId != '') {
		LS.message("info", "删除成功！");
		queryActStation();
		} else {
		LS.message("error", "删除失败");
		}
		});
		}
		});
		}

		function queryActStation() {
		cpnStationGrid.query("~/marketacts/firstFreeManage/qryActStation", firstFreeForm.getFormData());
		}


		//站点选择按钮
		function stationSelect() {
		var params={'info':'',busiType:busiType.getValue()};
		LS.dialog("~/marketacts/firstFreeManage/actStationSelect","站点选择",800,450,true, params);
		}

		window.selectCpnStation = selectCpnStation;
		function selectCpnStation(items) {
		if(items.length<1){
		LS.message("info", "请选择要添加的站点");
		return;
		}
		var params = {};
		params.firstFreeForm = firstFreeForm.getFormData();
		params.items = items;
		LS.ajax("~/marketacts/firstFreeManage/saveActStation", params, function (e) {
		var data = e.items[0];
		if (data.successful == true) {
		if(data.resultValue != 'true'){
		LS.message("error", data.resultValue);
		return;
		}
		LS.message("info", "添加成功");
		queryActStation();
		}else{
		LS.message("error", "添加失败");
		}
		})
		}

		function allStationFlagChan() {
		var _allStationFlag = allStationFlag.getValue();
		if(_allStationFlag == '0'){
		$('#stationGId').show();
		}else{
		$('#stationGId').hide();
		}
		}
	</ls:script>
</ls:body>
</html>