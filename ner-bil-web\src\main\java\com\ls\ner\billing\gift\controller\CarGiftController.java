package com.ls.ner.billing.gift.controller;

import com.ls.ner.base.constants.CustConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.gift.bo.CarGiftBo;
import com.ls.ner.billing.gift.condition.CarGiftCondition;
import com.ls.ner.billing.gift.constant.CarGiftConstants;
import com.ls.ner.billing.gift.service.ICarGiftService;
import com.ls.ner.pub.api.msg.service.IMsgRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import com.sun.org.apache.bcel.internal.generic.I2F;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description CarGiftController 购车送礼金管理
 * @create 2020-04-29 17:17
 */
@Controller
@RequestMapping("/bil/carGift")
public class CarGiftController extends QueryController<CarGiftCondition> {

    private static final Logger logger = LoggerFactory.getLogger(CarGiftController.class);

    @ServiceAutowired(value="carGiftService")
    private ICarGiftService ICarGiftService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;


    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "msgRpcService")
    private IMsgRpcService msgRpcService;
    /**
     * @param m
     * @description 购车送礼金管理页面
     * <AUTHOR>
     * @create 2020-05-04 11:23:53
     */
    @RequestMapping(value = "/init")
    public String init(Model m) {
        m.addAttribute("searchForm",new CarGiftCondition());
        List<CodeBO> carGiftGetStatusList = codeService.getStandardCodes(CarGiftConstants.carGiftGetStatus.NAME, null);
        m.addAttribute("carGiftGetStatusList", carGiftGetStatusList);
        return "/bil/car/carGiftInit";
    }

    /**
     * @param params
     * @description 购车送礼金列表数据查询
     * <AUTHOR>
     * @create 2020-05-04 11:24:48
     */
    @RequestMapping(value = "/querys")
    public @ItemResponseBody
    QueryResultObject querys(@QueryRequestParam("params") RequestCondition params){
        CarGiftCondition condition = this.rCondition2QCondition(params);
        logger.debug("购车券查询入参{}", JsonUtil.obj2Json(condition));
        List<CarGiftBo> dataList = ICarGiftService.listCarGift(condition);
        int count = ICarGiftService.countCarGift(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * @param m
     * @param condition
     * @description 发放页面
     * <AUTHOR>
     * @create 2020-05-04 12:58:52
     */
    @RequestMapping("/confirm")
    public String confirm(Model m, CarGiftCondition condition){
        logger.info("发放页面inMap:{}", com.alibaba.fastjson.JSON.toJSONString(condition));
        List<CarGiftBo> carGiftBoList = ICarGiftService.listCarGift(condition);
        CarGiftBo bo = new CarGiftBo();
        if(CollectionUtils.isNotEmpty(carGiftBoList)){
            bo = carGiftBoList.get(0);
        }
        List<CodeBO> carGiftGetStatusList = codeService.getStandardCodes(CarGiftConstants.carGiftGetStatus.NAME, null);
        m.addAttribute("carGiftGetStatusList", carGiftGetStatusList);
        String carGiftDefaultAmt = sysParamService.getSysParamsValues("carGiftDefaultAmt");
        m.addAttribute("carGiftDefaultAmt",carGiftDefaultAmt);
        m.addAttribute("searchForm",bo);
        return "/bil/car/carGiftPushConfirm";
    }

    /**
     * @param inMap
     * @description 发放成功处理
     * <AUTHOR>
     * @create 2020-05-04 12:59:11
     */
    @RequestMapping("/doSucceed")
    public @ResponseBody WrappedResult doSucceed(@RequestBody Map<String, Object> inMap) throws Exception{
        WrappedResult result = new WrappedResult();
        result.setSuccessful(true);
        try {
            CarGiftBo carGiftBo = new CarGiftBo();
            if(StringUtil.isNotBlank(inMap.get("giftId"))){
                carGiftBo.setGiftId(inMap.get("giftId").toString());
                carGiftBo.setGiftStatus(CarGiftConstants.carGiftGetStatus.ISSUED);
                carGiftBo.setGetTime(DateTools.getStringDateShort(DateTools.YMDHMS));
                ICarGiftService.updateCarGift(carGiftBo);
                doSendMessage(inMap, CarGiftConstants.templateCode.PUSH_SUCCEED_NOTICE);
            }
        }catch (Exception e){
            logger.info("发放成功操作失败:{}",e.getMessage());
            e.printStackTrace();
            result.setSuccessful(false);
            result.setResultValue("操作失败");
        }
        return WrappedResult.successWrapedResult(result);
    }

    /**
     * @param inMap
     * @description 发放失败处理
     * <AUTHOR>
     * @create 2020-05-04 12:59:26
     */
    @RequestMapping("/doFail")
    public @ResponseBody WrappedResult doFail(@RequestBody Map<String, Object> inMap) throws Exception{
        WrappedResult result = new WrappedResult();
        result.setSuccessful(true);
        try {
            CarGiftBo carGiftBo = new CarGiftBo();
            if(StringUtil.isNotBlank(inMap.get("giftId"))){
                carGiftBo.setGiftId(inMap.get("giftId").toString());
                carGiftBo.setGiftStatus(CarGiftConstants.carGiftGetStatus.ISSUANCE_FAILED);
                carGiftBo.setFailReason("支付宝帐号填写错误");
                ICarGiftService.updateCarGift(carGiftBo);
                doSendMessage(inMap, CarGiftConstants.templateCode.PUSH_FAIL_NOTICE);
            }
        }catch (Exception e){
            logger.info("发放失败操作失败:{}",e.getMessage());
            e.printStackTrace();
            result.setSuccessful(false);
            result.setResultValue("操作失败");
        }
        return WrappedResult.successWrapedResult(result);
    }

    private void doSendMessage(Map<String, Object> inMap, String templateCode){
        Map<String, String> msgmap = new HashMap<String, String>();
        CarGiftCondition condition = new CarGiftCondition();
        condition.setGiftId(inMap.get("giftId").toString());
        if(CarGiftConstants.templateCode.PUSH_SUCCEED_NOTICE.equals(templateCode)){
            condition.setGiftStatus(CarGiftConstants.carGiftGetStatus.ISSUED);
        }else if(CarGiftConstants.templateCode.PUSH_FAIL_NOTICE.equals(templateCode)){
            condition.setGiftStatus(CarGiftConstants.carGiftGetStatus.ISSUANCE_FAILED);
        }
        if (!CarGiftConstants.templateCode.PLATFORM_PUSH_SUCCEED_NOTICE.equals(templateCode)){
            List<CarGiftBo> carGiftBoList = ICarGiftService.listCarGift(condition);
            if(CollectionUtils.isNotEmpty(carGiftBoList)){
                CarGiftBo carGiftBo = carGiftBoList.get(0);
                msgmap.put("mobile", carGiftBo.getMobile());
                msgmap.put("templateType", CustConstants.TemplateType.MSG);
                msgmap.put("templateCode", templateCode);
                if(CarGiftConstants.templateCode.PUSH_FAIL_NOTICE.equals(templateCode)){
                    msgmap.put("failReason",carGiftBo.getFailReason());
                }
                logger.info("购车送礼金发放通知短信入参:{}", JsonUtil.obj2Json(msgmap));
                msgRpcService.doSendWithTemplate(msgmap);
            }
        }else{
            msgmap.put("mobile", MapUtils.getValue(inMap,"mobile"));
            msgmap.put("templateType", CustConstants.TemplateType.MSG);
            msgmap.put("templateCode", templateCode);
            msgmap.put("giftAmt", MapUtils.getValue(inMap,"giftAmt"));
            logger.info("购车直充送礼金发放通知短信入参:{}", JsonUtil.obj2Json(msgmap));
            msgRpcService.doSendWithTemplate(msgmap);
        }
    }

    @RequestMapping(value="/importFilePage")
    public String importFile() {
        return "/bil/car/importPage";
    }

    /**
     * @param request
     * @description 导入购车信息Excel
     * <AUTHOR>
     * @create 2020-05-04 13:51:23
     */
    @RequestMapping(value = "/saveImportData", method = RequestMethod.POST)
    public @ItemResponseBody WrappedResult saveImportData(MultipartHttpServletRequest request) {
        try {
            Map resultMap = ICarGiftService.saveImportData(request);
            return WrappedResult.successWrapedResult(resultMap);
        } catch (RuntimeException e) {
            return WrappedResult.failedWrappedResult("导入失败:"+e.getMessage());
        }catch (Exception e) {
            logger.error("",e);
            return WrappedResult.failedWrappedResult("系统错误！");
        }

    }
    /**
     * @param inMap
     * @description 审核通过直充
     * <AUTHOR>
     * @create 2020-05-04 12:59:26
     */
    @RequestMapping("/doCharge")
    public @ResponseBody WrappedResult doCharge(@RequestBody Map<String, Object> inMap) throws Exception{
        WrappedResult result = new WrappedResult();
        result.setSuccessful(true);
        try {
            CarGiftBo carGiftBo = new CarGiftBo();
            if(StringUtil.isNotBlank(inMap.get("giftId"))){
                carGiftBo.setGiftId(inMap.get("giftId").toString());
                if (StringUtil.isBlank(MapUtils.getValue(inMap,"mobile"))){
                    result.setSuccessful(false);
                    result.setResultValue("用户手机号不能为空");
                    return WrappedResult.successWrapedResult(result);
                }
                if (StringUtil.isBlank(MapUtils.getValue(inMap,"giftAmt"))){
                    result.setSuccessful(false);
                    result.setResultValue("赠送馈赠金不能为空");
                    return WrappedResult.successWrapedResult(result);
                }
                carGiftBo.setMobile(MapUtils.getValue(inMap,"mobile"));
                carGiftBo.setGiftAmt(MapUtils.getValue(inMap,"giftAmt"));
                Map<String,Object> resultMap = ICarGiftService.doCharge(carGiftBo);
                if (resultMap != null && "200".equals(StringUtil.getString(resultMap.get("ret")))){
                    // 发送短信
                    doSendMessage(inMap, CarGiftConstants.templateCode.PLATFORM_PUSH_SUCCEED_NOTICE);
                }else{
                    result.setSuccessful(false);
                    result.setResultValue(StringUtil.getString(resultMap.get("msg")));
                    return WrappedResult.successWrapedResult(result);
                }
            }
        }catch (Exception e){
            logger.info("发放失败操作失败:{}",e.getMessage());
            result.setSuccessful(false);
            result.setResultValue("操作失败");
        }
        return WrappedResult.successWrapedResult(result);
    }

    @Override
    protected CarGiftCondition initCondition() {
        return new CarGiftCondition();
    }
}
