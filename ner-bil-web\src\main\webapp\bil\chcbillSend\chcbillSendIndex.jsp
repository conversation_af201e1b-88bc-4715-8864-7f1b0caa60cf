<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib prefix="ner" uri="http://www.longshine.com/taglib/ner" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充点电价下发" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ner:head/>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="管理单位" /></td>
				<td><ls:text imageKey="search" onClickImage="getOrgs" name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" /></td>
				<td><ls:label text="充电站" ref="stationName"/></td>
				<td width="20%">
					<ls:select name="stationId" style="width:100%;" selectbox="true" property="stationId" required="true" allowEditing="true">
						<ls:options property="stationList" scope="request" text="text"
									value="value"></ls:options>
					</ls:select>
				</td>
				<td><ls:label text="下发状态" ref="sendStatus"/></td>
				<td><ls:select name="sendStatus">
					<ls:options property="chcBillSendStatusList" scope="request" text="codeName" value="codeValue" />
				</ls:select></td>
				<td>
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" allowSorting="true" showCheckBox="true" singleSelect="true" caption="充电计费配置列表">
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doIssured" text="下发"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doDetail" text="桩下发明细"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="充电站" name="stationName" />
					<ls:column caption="充电计费编号" name="chcNo" formatFunc="showDetail"/>
					<ls:column caption="充电计费名称" name="chcName" />
					<ls:column caption="生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="计费配置状态" name="chcStatusName" />
					<ls:column caption="最后一次下发时间" name="sendTime"  format="{sendTime,date,yyyy-MM-dd HH:mm}"/>
					<ls:column caption="下发状态" name="sendStatusName" />
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	var astPath = '${astPath }';
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		chargeGrid.query('~/billing/chcbillSend/queryChcbillSends',params,function(){});
	}
	queryStation();
 	function doDetail(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要查看明细的记录");
    		return;
	   }
		LS.dialog("~/billing/chcbillSend/detail?chcNo="+item.chcNo,"桩电价下发明细",1000, 500,true, null);
 	}
 	
 	function doIssured(){
 		var item = chargeGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要下发的充电计费配置");
    		return;
	   }
	   var message = '确定要下发该充电计费配置?';
	   if(item.sendStatus == '04'){//已下发成功，无需下发
	   //	LS.message("error","已成功下发，无需重发下发");
       //	return;
       		message = "该计费配置已下发成功，确定需要重新下发？";
	   }
	   LS.confirm(message,function(result){
			if(result){
				LS.ajax("~/billing/chcbillSend/issured?chcNo="+item.chcNo,null,function(data){
					if(data!=null){
						LS.message("info",data);
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
 	}
 	
    function doClear(){
      orgCodeName.setValue();
      orgCode.setValue();
      stationId.setValue();
      stationName.setValue();
      searchForm.clear();
    }
    
	function getOrgs(){
		var initValue = [];
		js_util.selectOrgTree(false, null, true, initValue, false, function(node){
			orgCodeName.setValue(node.text);
			orgCode.setValue(node.id);
			queryStation();
		});
	}

	function queryStation(){
		var _orgCode = orgCode.getValue();
		if(LS.isEmpty(_orgCode)){
			return;
		}
		stationId.clearItems();
		LS.ajax(astUrl+"/ast/host/queryStationList/" + orgCode.getValue(), '', function (e) {
			var items = new Array();
			for (var i = 0; i < e.items[0].resultValue.length; i++) {
				var item = {};
				item.text = e.items[0].resultValue[i].stationName;
				item.value = e.items[0].resultValue[i].stationId;
				items.push(item);
			}
			stationId.appendItems(items);
		});
	}

	function changeOrgName(){
		if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
			orgCode.setValue();
		}else{
			queryStation();
		}
	}
	
	//====================================充电站查询=============================================================
    function getPile(){
       LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
    }
    
    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }
    
    window.setStation = setStation;
    function setStation(item) {
       stationId.setValue(item.stationId);  
       stationName.setValue(item.stationName);
    }
		window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
		}


		function showDetail(rowdata){
		if(rowdata.chcNo==null){
		return '--'
		}else{
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowDetail(\"" + rowdata.chcNo +"\")'>"+rowdata.chcNo+"</a>";
		}
		}

		window.doShowDetail=doShowDetail;
		function doShowDetail(chcNo){
		LS.dialog("~/billing/chargebillingconf/showChargeBillingConf?chcNo="+chcNo,"查看充电费用配置",1000, 600,true, null);
		}

	</ls:script>
</ls:body>
</html>