<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.tariff2.dao.ITariffPlanDao">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.TariffPlanBo">

    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
    <result column="OPER_NO" jdbcType="VARCHAR" property="operNo" />
    <result column="OPER_NAME" jdbcType="VARCHAR" property="operName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="PE_BE" jdbcType="VARCHAR" property="peBe" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="CHARGE_WAY" jdbcType="VARCHAR" property="chargeWay" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="TIME_PER_PRICE_VALUE" jdbcType="INTEGER" property="timePerPriceValue" />
    <result column="TIME_PER_PRICE_UNIT" jdbcType="VARCHAR" property="timePerPriceUnit" />
    <result column="MILL_PER_PRICE_VALUE" jdbcType="INTEGER" property="millPerPriceValue" />
    <result column="MILL_PER_PRICE_UNIT" jdbcType="VARCHAR" property="millPerPriceUnit" />
    <result column="PERIOD_SPLIT_POINTS" jdbcType="VARCHAR" property="periodSplitPoints" />
    <result column="MILL_STEP_SPLIT_POINTS" jdbcType="VARCHAR" property="millStepSplitPoints" />
    <result column="TIME_STEP_SPLIT_POINTS" jdbcType="VARCHAR" property="timeStepSplitPoints" />
    <result column="DEPOSIT_ITEM_NOS" jdbcType="VARCHAR" property="depositItemNos" />
    <result column="ATTACH_ITEM_NOS" jdbcType="VARCHAR" property="attachItemNos" />
    <result column="PLAN_REMARK" jdbcType="VARCHAR" property="planRemark" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="VALID_FLAG" jdbcType="VARCHAR" property="validFlag" />
  </resultMap>



	<select id="pagiTariff" resultMap="BaseResultMap" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
		select
		a.plan_name,
		a.plan_no,
		a.oper_name,
		a.create_time,
		a.pe_be,
		a.sub_be,
		a.CHARGE_WAY,
		a.CHARGE_MODE,
		a.ORG_CODE,
		a.VALID_FLAG,
		a.plan_remark
		from e_tariff_plan a
		<where>
			<if test="subBe != null and subBe != ''">
				AND a.SUB_BE like concat(#{subBe},'%')
			</if>
			<if test="validFlag != null and validFlag != ''">
        AND a.VALID_FLAG =#{validFlag}
      </if>
			<if test="orgCode != null and orgCode != ''">
        AND a.org_code =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.org_code in 
        <foreach item="item" index="index" collection="orgCodes"
          open="(" separator="," close=")">
            #{item}
        </foreach>
      </if>
		</where>
		<if test="pageEnd!=null and pageEnd!=0">
			limit #{pageBegin} ,#{pageEnd}
		</if>
	</select>

	<select id="countTariff" resultType="int" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
		select count(1) from e_tariff_plan a
		<where>
			<if test="subBe != null and subBe != ''">
       AND a.SUB_BE like concat(#{subBe},'%')
      </if>
      <if test="validFlag != null and validFlag != ''">
        AND a.VALID_FLAG =#{validFlag}
      </if>
      <if test="orgCode != null and orgCode != ''">
        AND a.org_code =#{orgCode}
      </if>
      <if test="orgCodes != null">
        AND a.org_code in 
        <foreach item="item" index="index" collection="orgCodes"
          open="(" separator="," close=")">
            #{item}
        </foreach>
      </if>
		</where>
	</select>

  <update id="updateByPlanNo" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
    
    update e_tariff_plan
    set 
      PLAN_NAME = #{planName,jdbcType=VARCHAR},
      OPER_NO = #{operNo,jdbcType=VARCHAR},
      OPER_NAME = #{operName,jdbcType=VARCHAR},
     <!--  CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}, -->
      PE_BE = #{peBe,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      CHARGE_WAY = #{chargeWay,jdbcType=VARCHAR},
      CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      TIME_PER_PRICE_VALUE = #{timePerPriceValue,jdbcType=INTEGER},
      TIME_PER_PRICE_UNIT = #{timePerPriceUnit,jdbcType=VARCHAR},
      MILL_PER_PRICE_VALUE = #{millPerPriceValue,jdbcType=INTEGER},
      MILL_PER_PRICE_UNIT = #{millPerPriceUnit,jdbcType=VARCHAR},
      PERIOD_SPLIT_POINTS = #{periodSplitPoints,jdbcType=VARCHAR},
      TIME_STEP_SPLIT_POINTS = #{timeStepSplitPoints,jdbcType=VARCHAR},
      MILL_STEP_SPLIT_POINTS = #{millStepSplitPoints,jdbcType=VARCHAR},
      DEPOSIT_ITEM_NOS = #{depositItemNos,jdbcType=VARCHAR},
      ATTACH_ITEM_NOS = #{attachItemNos,jdbcType=VARCHAR},
      PLAN_REMARK = #{planRemark,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
  	  ORG_CODE = #{orgCode,jdbcType=VARCHAR}
	where PLAN_NO = #{planNo,jdbcType=VARCHAR}
  </update>

</mapper>