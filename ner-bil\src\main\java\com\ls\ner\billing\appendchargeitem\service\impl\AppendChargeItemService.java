package com.ls.ner.billing.appendchargeitem.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.appendchargeitem.service.IAppendChargeItemService;
import com.ls.ner.billing.common.bo.AppendChargeItemBo;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

@Service(target = { ServiceType.APPLICATION }, value = "appendChargeItemService")
public class AppendChargeItemService implements IAppendChargeItemService {

	@Autowired
	AppendChargeItemServiceImpl appendChargeItemServiceImpl;

	public String createAttachChargeItem(AppendChargeItemBo appendChargeItemBo) {
		return appendChargeItemServiceImpl.createAttachChargeItem(appendChargeItemBo);
	}

	public String createDepositChargeItem(AppendChargeItemBo appendChargeItemBo) {
		return appendChargeItemServiceImpl.createDepositChargeItem(appendChargeItemBo);
	}

	public void updateAttachChargeItem(AppendChargeItemBo appendChargeItemBo) {
		appendChargeItemServiceImpl.updateAttachChargeItem(appendChargeItemBo);
	}

	public void updateDepositChargeItem(AppendChargeItemBo appendChargeItemBo) {
		appendChargeItemServiceImpl.updateDepositChargeItem(appendChargeItemBo);
	}

	public void update(AppendChargeItemBo appendChargeItemBo) {
		appendChargeItemServiceImpl.update(appendChargeItemBo);
	}

	public void deleteAttachChargeItem(String itemNo) {
		appendChargeItemServiceImpl.deleteAttachChargeItem(itemNo);
	}

	public void deleteDepositChargeItem(String itemNo) {
		appendChargeItemServiceImpl.deleteDepositChargeItem(itemNo);
	}

	public void delete(String itemType, String itemNo) {
		appendChargeItemServiceImpl.delete(itemType, itemNo);
	}

	public void delete(AppendChargeItemBo appendChargeItemBo) {
		appendChargeItemServiceImpl.delete(appendChargeItemBo);
	}

	public List<AppendChargeItemBo> listAttachChargeItem(AppendChargeItemBo bo) {
		return appendChargeItemServiceImpl.listAttachChargeItem(bo);
	}

	public List<AppendChargeItemBo> listDepositChargeItem(AppendChargeItemBo bo) {
		return appendChargeItemServiceImpl.listDepositChargeItem(bo);
	}

	public List<AppendChargeItemBo> list(AppendChargeItemBo bo) {
		return appendChargeItemServiceImpl.list(bo);
	}

	@Override
	public void updateItems(List<AppendChargeItemBo> list) {
		appendChargeItemServiceImpl.updateItems(list);
	}

}
