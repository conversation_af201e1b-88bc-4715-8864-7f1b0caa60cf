package com.ls.ner.billing.xpcharge.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.dao.IChargeBillingConfDao;
import com.ls.ner.billing.charge.dao.IChargePeriodsDao;
import com.ls.ner.billing.xpcharge.bo.PeakRelBo;
import com.ls.ner.billing.xpcharge.bo.PeakTimeBo;
import com.ls.ner.billing.xpcharge.dao.IXpChargeBillingConfDao;
import com.ls.ner.billing.xpcharge.service.IXpChargeBillingConfService;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.billing.xpcharge.service.IXpPileSendService;
import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.MergeUtil;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 充电计费配置
 * <AUTHOR>
 */
@Service(target = {ServiceType.APPLICATION}, value = "xpChargeBillingConfService")
public class XpChargeBillingConfServiceImpl implements IXpChargeBillingConfService {

    private static final Logger logger = LoggerFactory.getLogger(XpChargeBillingConfServiceImpl.class);

    @Autowired
    private IXpChargeBillingConfDao xpChargeBillingConfDao;

    @ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
    private ISeqRpcService seqRpcService;

    @Autowired
    private IChargeBillingConfDao chargeBillingConfDao;

    @Autowired
    private IChargePeriodsDao chargePeriodsDao;

    @ServiceAutowired
    Authentication authentication;

    @ServiceAutowired("xpPileSendService")
    private IXpPileSendService xpPileSendService;

    @ServiceAutowired("xpStationSendService")
    private IXpStationSendService xpStationSendService;

    @ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
    private IArchivesRpcService archivesRpcService;

    @ServiceAutowired("peakService")
    private IXpPeakService peakService;

    /**
     * <AUTHOR>
     * @dateTime 2018-03-16
     * @description 查询充电计费配置E_CHARGE_BILLING_CONF
     */
    public List<ChargeBillingConfBo> queryChargeBillingConfs(XpChargeBillingConfQueryCondition bo){
        List<ChargeBillingConfBo> chargeBillingConfBos = xpChargeBillingConfDao.queryXpChargeBillingConfs(bo);
        if (peakService.peakBillingSwitch()){
            List<PeakRelBo> peakRelBos = peakService.selectPeakRelAll();
            Map<String,String> map = new HashMap<>();
            for (PeakRelBo relBo : peakRelBos) {
                map.put(relBo.getChcNo(),relBo.getChcNo());
            }
            PeakTimeBo peakTimeBo = peakService.queryPeakTimeEffective();
            String peakTime = "";
            if (peakTime != null){
                peakTime = peakTimeBo.getEftDate() + "\r\n" + peakTimeBo.getInvDate();
            }
            for (ChargeBillingConfBo  confBo : chargeBillingConfBos) {
                if (map.containsKey(confBo.getChcNo())){
                    confBo.setPeakTime(peakTime);
                    confBo.setIsRelPeak("已关联");
                    continue;
                }
                confBo.setIsRelPeak("未关联");
            }
        }
        return chargeBillingConfBos;
    }

    /**
     * <AUTHOR>
     * @dateTime 2018-03-16
     * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
     */
    public int queryChargeBillingConfsNum(XpChargeBillingConfQueryCondition bo){
        return xpChargeBillingConfDao.queryXpChargeBillingConfsNum(bo);
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-03-24
     * @description 新增充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void insertChargeBillingConf(ChargeBillingConfBo bo){
        bo.setChcNo(seqRpcService.getDefNo());
        //如果“费控模式=本地”，gatherDataType=03量价费
        if(ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode())){
            bo.setGatherDataType(ChargeConstant.gatherDataType.PRICE);
        }
        if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(bo.getChargeMode())){//如果分时
            String chargePeriodsStr = bo.getChargePeriodsStr();//分时数据
            Gson gson=new Gson();
            List<ChargePeriodsBo> cpBos =gson.fromJson(chargePeriodsStr, new TypeToken<List<ChargePeriodsBo>>(){}.getType());
            bo.setPeriodSplitNum(String.valueOf(cpBos.size()));//分时阶段数
            for(int i = 0;i < cpBos.size(); i++){
                ChargePeriodsBo cpBo = cpBos.get(i);
                cpBo.setSn(i+1+"");
                cpBo.setChcNo(bo.getChcNo());
                chargePeriodsDao.insertChargePeriods(cpBo);
            }
            bo.setChargePrice(null);
        }else{
            bo.setPeriodSplitNum("0");//分时阶段数
        }
        bo.setChargeNum("1");//最小计费数
        bo.setChargeUnit(ChargeConstant.chcBillingPriceUnit.KWH);//计费单位：01kWh
        bo.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
        chargeBillingConfDao.insertChargeBillingConf(bo);
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-03-24
     * @description 更新充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void updateChargeBillingConf(ChargeBillingConfBo bo){
//如果“费控模式=本地”，gatherDataType=03量价费
        if(ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode())){
            bo.setGatherDataType(ChargeConstant.gatherDataType.PRICE);
        }
        //如果对“状态=草稿”进行修改，则直接更新原记录；

        //1、先删除分时表数据
        ChargePeriodsBo delCpBo = new ChargePeriodsBo();
        delCpBo.setChcNo(bo.getChcNo());
        chargePeriodsDao.deleteChargePeriods(delCpBo);
        //2、如果分时，插入分时数据
        if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(bo.getChargeMode())){
            String chargePeriodsStr = bo.getChargePeriodsStr();//分时数据
            Gson gson=new Gson();
            List<ChargePeriodsBo> cpBos =gson.fromJson(chargePeriodsStr, new TypeToken<List<ChargePeriodsBo>>(){}.getType());
            bo.setPeriodSplitNum(String.valueOf(cpBos.size()));//分时阶段数
            for(int i = 0;i < cpBos.size(); i++){
                ChargePeriodsBo cpBo = cpBos.get(i);
                cpBo.setSn(i+1+"");
                cpBo.setChcNo(bo.getChcNo());
                chargePeriodsDao.insertChargePeriods(cpBo);
            }
            bo.setChargePrice(null);
        }else{
            bo.setPeriodSplitNum("0");//分时阶段数
        }
//		bo.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
        chargeBillingConfDao.updateChargeBillingConf(bo);
    }

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 发布充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void releaseChargeBillingConf(ChargeBillingConfBo bo){
        XpChargeBillingConfQueryCondition searchBo = new XpChargeBillingConfQueryCondition();
        searchBo.setChcNo(bo.getChcNo());
        List<ChargeBillingConfBo> cbList = xpChargeBillingConfDao.queryXpChargeBillingConfs(searchBo);
        if(cbList!=null && cbList.size() == 1){
            bo = cbList.get(0);
            //1、先将上一个计费版本改为失效
            ChargeBillingConfBo updateBo = new ChargeBillingConfBo();
            AccountBo currentAccount = authentication.getCurrentAccount();
            updateBo.setChcStatusName(null);
            updateBo.setChargePrice("");
            updateBo.setOperNo(currentAccount.getAccountName());
            updateBo.setCreateTime("true");
            if(bo.getUpChcNo() != null){
                //updateBo.setChcStatus(ChargeConstant.validFlag.UNENABLE);
                //updateBo.setChcNo(bo.getUpChcNo());
                //chargeBillingConfDao.updateChargeBillingConf(updateBo);

                //调价时需要计费关联站点一致修改
                //Map map = new HashMap();
                //map.put("newChcNo", bo.getChcNo());//新的计费编号
                //map.put("upChcNo", bo.getUpChcNo());//旧的计费编号
                //chargeBillingConfDao.updateStationBilling(map);
                //chargeBillingConfDao.updatePileBilling(map);

            }
            //2、把当前充电计费配置设为有效
            updateBo.setChcStatus(ChargeConstant.validFlag.WAIT_EFFECTIVE);
            updateBo.setChcNo(bo.getChcNo());
            chargeBillingConfDao.updateChargeBillingConf(updateBo);

        }
    }

    public List<ChargeBillingConfBo> queryXpChargeBillingConfInfo(XpChargeBillingConfQueryCondition xpCondition) {
        return xpChargeBillingConfDao.queryXpChargeBillingConfInfo(xpCondition);
    }

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 计费生效任务调度
     */
    @Override
    public void excuteBillingTask() throws Exception{
        logger.info("开始执行计费生效任务调度 excuteBillingTask ---- start");
        // 查询所有待生效且生效时间已到的计费信息
        XpChargeBillingConfQueryCondition xpCondition = new XpChargeBillingConfQueryCondition();
        xpCondition.setChcStatus(ChargeConstant.validFlag.WAIT_EFFECTIVE);
        xpCondition.setEndEftDate(DateTools.dateToStr(new Date(),DateTools.YMDHMS));
        List<ChargeBillingConfBo> billList = xpChargeBillingConfDao.queryEffectChargeBillingConfs(xpCondition);
        if (CollectionUtils.isNotEmpty(billList)){
            for (ChargeBillingConfBo chargeBillingConfBo : billList) {
                ChargeBillingConfBo updateBo = new ChargeBillingConfBo();
                updateBo.setChcStatusName(null);
                updateBo.setChargePrice("");
                if (StringUtil.isNotBlank(chargeBillingConfBo.getUpChcNo())){
                    // 将上一个计费版本改为失效
                    updateBo.setChcStatus(ChargeConstant.validFlag.UNENABLE);
                    updateBo.setChcNo(chargeBillingConfBo.getUpChcNo());
                    chargeBillingConfDao.updateChargeBillingConf(updateBo);

                    //调价时需要计费关联站点一致修改
                    Map map = new HashMap();
                    map.put("newChcNo", chargeBillingConfBo.getChcNo());//新的计费编号
                    map.put("upChcNo", chargeBillingConfBo.getUpChcNo());//旧的计费编号
                    chargeBillingConfDao.updateStationBilling(map);
                    chargeBillingConfDao.updatePileBilling(map);

                    //删除尖峰计费关联
                    if (peakService.peakBillingSwitch()){
                        //自动作业没有操作者 默认传入SYSADMIN
                        Map<String,Object> mapTask=new HashMap<String,Object>();
                        mapTask.put("operator","SYSADMIN");
                        mapTask.put("networkIp","127.0.0.1");
                        mapTask.put("intranetIp","127.0.0.1");
                        peakService.delPeakRel(chargeBillingConfBo.getUpChcNo(),mapTask);
                    }
                }

                //把当前充电计费配置设为有效
                updateBo.setChcStatus(ChargeConstant.validFlag.ENABLE);
                updateBo.setChcNo(chargeBillingConfBo.getChcNo());
                chargeBillingConfDao.updateChargeBillingConf(updateBo);

                // 计费主动下发
                if (StringUtil.isNotBlank(chargeBillingConfBo.getUpChcNo())){
                    ChcbillSendQueryCondition bo = new ChcbillSendQueryCondition();
                    bo.setChcNo(chargeBillingConfBo.getUpChcNo());
                    autoBillingSend(bo,chargeBillingConfBo.getChcNo());
                }
            }
        }
    }

    @Override
    public void executeBillSendTask() {
        // 1. 查找未下发成功或从未下发过计费的站点计费配置
        List<Map<String, Object>> sendList = xpPileSendService.queryStationBillConfs();
        if (CollectionUtils.isNotEmpty(sendList)) {
            sendList.forEach(sendMap -> {
                String chcNo = MapUtils.getValue(sendMap, "chcNo");
                String stationId = MapUtils.getValue(sendMap, "stationId");
                try {
                    //自动作业没有操作者 默认传入SYSADMIN
                    Map<String,Object> map=new HashMap<String,Object>();
                    map.put("operator","SYSADMIN");
                    map.put("networkIp","127.0.0.1");
                    map.put("intranetIp","127.0.0.1");
                    xpStationSendService.issuredStation(chcNo, stationId, map);
                } catch (Exception e) {
                    logger.error("==========站点计费下发失败:", e);
                }
            });
        }
        // 2. 查找未下发成功或从未下发过计费的桩计费配置
        List<Map<String, Object>> pileSendList = xpPileSendService.queryPileBillConfs();
        if (CollectionUtils.isNotEmpty(pileSendList)) {
            pileSendList.forEach(sendMap -> {
                try {
                    String chcNo = MapUtils.getValue(sendMap, "chcNo");
                    String stationId = MapUtils.getValue(sendMap, "stationId");
                    String pileId = MapUtils.getValue(sendMap, "pileId");
                    String pileNo = MapUtils.getValue(sendMap, "pileNo");
                    logger.debug("==========计费:{},站:{},桩:{}", chcNo, stationId, pileNo);
                    Map<String, Object> searchMap = new HashedMap();
                    searchMap.put("pileIdList", Collections.singletonList(pileId));
                    List<Map> pileInfoList = archivesRpcService.queryPilesByMap(searchMap);
                    if (CollectionUtils.isNotEmpty(pileInfoList)) {
                        String runStatus = StringUtil.nullForString(pileInfoList.get(0).get("runStatus"));
                        if (StringUtil.isNotBlank(runStatus)
                                && !AssetConstants.GunCouplerRunStatus.Gun_Coupler_Run_Status_5.equals(runStatus)) {
                            xpPileSendService.issuredPile(chcNo, pileNo, stationId);
                        }
                    }
                } catch (Exception e) {
                    logger.error("==========桩计费下发失败:", e);
                }
            });
        }
    }

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 计费下发任务调度
     */
    @Override
    public void excuteBillingSendTask(String staitonId,String pileNo) throws Exception{
        ChcbillSendQueryCondition bo = new ChcbillSendQueryCondition();
        bo.setSendStatus("fail");
        if (StringUtil.isNotBlank(staitonId)){
            bo.setStationId(staitonId);
        }
        if (StringUtil.isNotBlank(pileNo)){
            bo.setPileNo(pileNo);
        }
        autoBillingSend(bo,null);
    }

    private void autoBillingSend(ChcbillSendQueryCondition bo,String chcNo) throws Exception{
        bo.setGroupType("station");
        List<Map<String,Object>> sendList = xpPileSendService.queryChcbillPileSendsByNo(bo);
        if (CollectionUtils.isNotEmpty(sendList)){
            List<String> stationIdList = new ArrayList<>();
            for (Map<String, Object> sendMap : sendList) {
                if (StringUtil.isNotBlank(MapUtils.getValue(sendMap,"stationId"))){
                    stationIdList.add(MapUtils.getValue(sendMap,"stationId"));
                }
            }
            Map<String, Object> m = new HashMap<>();
            m.put("stationIdList",stationIdList);
            List<Map<String,Object>> list = archivesRpcService.queryStationsList(m);
            MergeUtil.mergeList(sendList, list, "stationId",  new String[]{"billingConfig"},  new String[]{"billingConfig"});
            for (Map<String, Object> sendMap : sendList) {
                if (!AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(sendMap, "billingConfig"))){
                    String newChcNo = "";
                    if (StringUtil.isNotBlank(chcNo)){
                        newChcNo = chcNo;
                    }else{
                        newChcNo = MapUtils.getValue(sendMap,"chcNo");
                    }
                    try {
                        if (StringUtil.isNotBlank(newChcNo)){
                            logger.debug("==========计费:{},站:{}", newChcNo,MapUtils.getValue(sendMap,"stationId"));
                            //自动作业没有操作者 默认传入SYSADMIN
                            Map<String,Object> map=new HashMap<String,Object>();
                            map.put("operator","SYSADMIN");
                            map.put("networkIp","127.0.0.1");
                            map.put("intranetIp","127.0.0.1");
                            xpStationSendService.issuredStation(newChcNo, MapUtils.getValue(sendMap, "stationId"), map);
                        }
                    }catch (Exception e){
                        logger.debug("==========站点计费下发失败:", e);
                    }
                }
            }
        }
        bo.setGroupType("pile");
        List<Map<String,Object>> pileSendList = xpPileSendService.queryChcbillPileSendsByNo(bo);
        if (CollectionUtils.isNotEmpty(pileSendList)){
            List<String> stationIdList = new ArrayList<>();
            for (Map<String, Object> sendMap : pileSendList) {
                if (StringUtil.isNotBlank(MapUtils.getValue(sendMap,"stationId"))){
                    stationIdList.add(MapUtils.getValue(sendMap,"stationId"));
                }
            }
            Map<String, Object> m = new HashMap<>();
            m.put("stationIdList",stationIdList);
            List<Map<String,Object>> list = archivesRpcService.queryStationsList(m);
            MergeUtil.mergeList(pileSendList, list, "stationId",  new String[]{"billingConfig"},  new String[]{"billingConfig"});

            for (Map<String, Object> sendMap : pileSendList) {
                if (AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(sendMap, "billingConfig"))){
                    String newChcNo = "";
                    if (StringUtil.isNotBlank(chcNo)){
                        newChcNo = chcNo;
                    }else{
                        newChcNo = MapUtils.getValue(sendMap,"chcNo");
                    }
                    try {
                        if (StringUtil.isNotBlank(newChcNo)){
                            logger.debug("==========计费:{},站:{},桩:{}", newChcNo,MapUtils.getValue(sendMap,"stationId"),MapUtils.getValue(sendMap,"pileNo"));
                            List<String> pileList = new ArrayList<>();
                            pileList.add(MapUtils.getValue(sendMap, "pileId"));
                            Map<String,Object> searchMap = new HashedMap();
                            searchMap.put("pileIdList",pileList);
                            List<Map> pileInfoList = archivesRpcService.queryPilesByMap(searchMap);
                            if (CollectionUtils.isNotEmpty(pileInfoList)){
                                String runStatus = StringUtil.nullForString(pileInfoList.get(0).get("runStatus"));
                                if (StringUtil.isNotBlank(runStatus) && runStatus.indexOf(AssetConstants.GunCouplerRunStatus.Gun_Coupler_Run_Status_5) < 0){
                                    xpPileSendService.issuredPile(newChcNo, MapUtils.getValue(sendMap, "pileNo"), MapUtils.getValue(sendMap, "stationId"));
                                }
                            }
                        }
                    }catch (Exception e){
                        logger.debug("==========桩计费下发失败:", e);
                    }
                }
            }
        }
        logger.info("开始执行计费生效任务调度 autoBillingSend ---- end");
    }
}
