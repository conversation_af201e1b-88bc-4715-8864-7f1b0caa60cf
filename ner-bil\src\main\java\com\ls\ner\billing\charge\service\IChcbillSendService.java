package com.ls.ner.billing.charge.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;

/**
 * 充电配置下发
 * <AUTHOR>
 */
public interface IChcbillSendService {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询充电计费配置下发信息  E_CHCBILL_SEND
	 */
	public List<ChcbillSendBo> queryChcbillSends(ChcbillSendQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description  查询充电计费配置下发信息条数  E_CHCBILL_SEND
	 */
	public int queryChcbillSendsNum(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息  E_CHCBILL_PILESEND
	 */
	public List<ChcbillPileSendBo> queryChcbillPileSends(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息条数  E_CHCBILL_PILESEND
	 */
	public int queryChcbillPileSendsNum(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志   E_CHCBILL_PILESEND_LOG
	 */
	public List<ChcbillPileSendLogBo> queryChcbillPileSendLogs(ChcbillSendQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志条数   E_CHCBILL_PILESEND_LOG
	 */
	public int queryChcbillPileSendLogsNum(ChcbillSendQueryCondition bo);

	/**
	 * @description 充电计费配置下发到充电站
	 * @param chcNo 要下发的充电计费配置编号
	 * @return String 下发完成状态  02下发失败   03部分成功  04下发成功
	 * */
	public void issuredStation(String chcNo, Map<String, Object> map) throws Exception;

	/**
	 * @description 充电计费配置下发到充电桩
	 * @param chcNo 要下发的充电计费配置编号
	 * @param pileNo 要下发的充电桩编号
	 * @return boolean true 下发成功   false 下发失败
	 * */
	public void issuredPile(String chcNo, String pileNo, Map<String, Object> map) throws Exception;

	/**
	  *@Description: 充电桩登录时，采集通过指定topic把桩号传到业务boss，bil自动识别并判断是否有计费规则需要下发。
	  * 			 判断规则：桩是否投运、所属站点是否配置有效的计费规则，当天是否已经下发成功过，一天只下发成功一次即可。
	  *@Author: qianghuang
	  *@Time: 2017/11/16 15:55
	  */
    void autoIssuredPile(Map map) throws Exception;

    /**
     * @param lmap
     * @description 充电桩登录时，采集通过指定topic把桩号传到业务boss，bil自动识别并判断是否有计费规则需要下发。
	 * 判断规则：桩是否投运、所属站点是否配置有效的计费规则，当天是否已经下发成功过，一天只下发成功一次即可。
     * <AUTHOR>
     * @create 2018-04-27 14:34:56
     */
	void newAutoIssuredPile(Map lmap) throws Exception;

	/**
	 * @Description 通过桩编号查询最新的下发成功记录
	 * <AUTHOR>
	 * @Date 2023/7/27 15:22
	 * @param: pileIds
	 */
	ChcbillSendBo querySendLongByStationIdAndChcNo(String stationId, String chcNo);
}
