<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillingPeriodsDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo">
		<result column="SYSTEM_ID" property="systemId" />
		<result column="APP_NO" property="appNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="SN" property="sn" />
		<result column="BEGIN_TIME" property="beginTime" />
		<result column="END_TIME" property="endTime" />
		<result column="PRICE" property="price" />
		<result column="PQ" property="pq" />
		<result column="AMT" property="amt" />
		<result column="REL_BEGIN_TIME" property="relBeginTime" />
		<result column="REL_END_TIME" property="relEndTime" />
		<result column="ITEM_NO" property="itemNo"  />
	 </resultMap>
	 
	 <sql id="chargeBillingPeriodItems">
	 	a.APP_NO,a.CHC_NO,a.SN,a.BEGIN_TIME,a.END_TIME,a.PRICE,a.PQ,a.AMT,a.REL_BEGIN_TIME,a.REL_END_TIME,a.ITEM_NO
	 </sql>
	 
	<!-- 查询充电实时计费分时明细 E_CHARGE_BILLING_PERIODS by appNo-->
	<select id="queryChargeBillingPeriod" parameterType="java.util.Map" resultMap="BaseResultMap">
		select  
			<include refid="chargeBillingPeriodItems"></include>
		from E_CHARGE_BILLING_PERIODS a
		<where>
			a.APP_NO = #{appNo}
			<if test="itemNo !=null and itemNo !=''">
				and a.ITEM_NO = #{itemNo}
			</if>
		</where>
		order by a.SN
	</select>
	 
	<!-- 新增充电实时计费分时明细 E_CHARGE_BILLING_PERIODS-->
	<insert id="insertChargeBillingPeriods" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_BILLING_PERIODS
		<trim prefix="(" suffix=")">
			<if test="appNo !=null and appNo !=''">APP_NO,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sn !=null and sn !=''">SN,</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME,</if>
			<if test="endTime !=null and endTime !=''">END_TIME,</if>
			<if test="price !=null and price !=''">PRICE,</if>
			<if test="pq !=null and pq !=''">PQ,</if>
			<if test="amt !=null and amt !=''">AMT,</if>
			<if test="relBeginTime !=null and relBeginTime !=''">REL_BEGIN_TIME,</if>
			<if test="relEndTime !=null and relEndTime !=''">REL_END_TIME,</if>
			<if test="itemNo !=null and itemNo !=''">ITEM_NO,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="appNo !=null and appNo !=''">#{appNo},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sn !=null and sn !=''">#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">#{endTime},</if>
			<if test="price !=null and price !=''">#{price},</if>
			<if test="pq !=null and pq !=''">#{pq},</if>
			<if test="amt !=null and amt !=''">#{amt},</if>
			<if test="relBeginTime !=null and relBeginTime !=''">#{relBeginTime},</if>
			<if test="relEndTime !=null and relEndTime !=''">#{relEndTime},</if>
			<if test="itemNo !=null and itemNo !=''">#{itemNo},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电实时计费分时明细 E_CHARGE_BILLING_PERIODS -->
	<update id="updateChargeBillingPeriods" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo">
		update E_CHARGE_BILLING_PERIODS
		<set>
			<if test="appNo !=null and appNo !=''">APP_NO =#{appNo},</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO =#{chcNo},</if>
			<if test="sn !=null and sn !=''">SN =#{sn},</if>
			<if test="beginTime !=null and beginTime !=''">BEGIN_TIME =#{beginTime},</if>
			<if test="endTime !=null and endTime !=''">END_TIME =#{endTime},</if>
			<if test="price !=null and price !=''">PRICE =#{price},</if>
			<if test="pq !=null and pq !=''">PQ =#{pq},</if>
			<if test="amt !=null and amt !=''">AMT =#{amt},</if>
			<if test="relBeginTime !=null and relBeginTime !=''">REL_BEGIN_TIME = #{relBeginTime},</if>
			<if test="relEndTime !=null and relEndTime !=''">REL_END_TIME = #{relEndTime},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			SYSTEM_ID =#{systemId}
		</where>
	</update>
	
	<delete id="deleteChargeBillingPeriod" parameterType="string">
		delete from E_CHARGE_BILLING_PERIODS where APP_NO =#{appNo}
	</delete>
</mapper>