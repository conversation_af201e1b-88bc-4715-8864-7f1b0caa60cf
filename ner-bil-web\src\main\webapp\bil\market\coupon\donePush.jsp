<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券已推广">
<style type="text/css">
.hiddenFlow{
overflow-x:hidden; 
}
</style>
</ls:head>
<ls:body>
	<ls:form name="donePush" cssClass="hiddenFlow">
		<ls:text name="cpnStatus" value="1" visible="false"></ls:text>
		<ls:text name="pushStatus" value="1" visible="false"></ls:text>
		<ls:text name="invDate" value="true" visible="false"></ls:text>
		<ls:text name="chargeType" value="${chargeType}" visible="false"></ls:text>
		<ls:title text="查询条件"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td width="120px"><ls:label text="优惠券名称"></ls:label></td>
				<td width="250px"><ls:text name="cpnName"></ls:text></td>
				<td><ls:label text="优惠券类型"></ls:label></td>
				<td>
					<ls:select name="cpnType">
						<ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td width="150px"><ls:label text="面额/折扣"></ls:label></td>
				<td width="200px"><ls:text name="cpnAmt"></ls:text></td>
				<td >
					<div class="pull-right">
						<ls:button text="清空" onclick="clearAll" />
						<ls:button text="查询" onclick="query" />
					</div>
				</td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="donePushGrid" caption="优惠券列表" showRowNumber="false"
						height="410px" width="100%" showCheckBox="false" singleSelect="true" >
						<ls:gridToolbar name="operation" >
                    		<ls:gridToolbarItem name="closeCoupon" text="关闭" imageKey="set" onclick="closeCoupon"></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column name="cpnNo" caption="优惠券编号" hidden="true" />
						<ls:column name="pushNo" caption="推广编号" hidden="true" />
						<ls:column caption="推广渠道" name="pushChannelName" align="center" />
						<ls:column caption="推广方式" name="pushWayName" align="center" />
						<ls:column caption="推广时间" name="pushTime" align="center" />
						<ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="150px"/>
						<ls:column caption="优惠券类型" name="cpnTypeName" align="center" />
						<ls:column caption="面额/折扣" name="cpnAmt" align="right" />
						<ls:column caption="发行数量" name="cpnNum" align="right" width="100px"/>
						<ls:column caption="每人限领" name="limGetNum" align="right" width="80px"/>
						<ls:column caption="有效时间" name="effectTime" align="center" />
						<ls:pager pageSize="15,20"></ls:pager>
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:form name="doneForm">
		<ls:text name="pushOperType" value="02" visible="false"></ls:text>
		<ls:text name="pushStatus"  value="0" visible="false"></ls:text>
		<ls:text name="cpnNo"  visible="false"></ls:text>
	</ls:form>
	<ls:script>
	function closeCoupon(){
		var item = donePushGrid.getSelectedItem();
    	if(item==null){
    		 LS.message("info","请选择一条记录!");
    		 return;
    	}
    	cpnNo.setValue(item.cpnNo);
    	doneForm.submit("~/market/coupon/savePushCoupon",function(data){
  		 	if(data){
   		 		LS.message("info","关闭成功!");
   		 		query();
   		 	}else{
   		 		LS.message("error","系统异常");
   		 	}	
	   });
	}
	
    function query(){
      var datas={};
      datas = donePush.getFormData();
      donePushGrid.query("~/market/coupon/getCouponsPushLog",datas);
    }
    function clearAll(){
      donePush.clear();
    };
    
 	function linkFunc(rowdata){
    	var _cpnNo = rowdata.cpnNo;
    	var _cpnName = rowdata.cpnName;
    	return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnNo + "\")'>"+_cpnName+"</a></u>";
    }
    
    window.showCouponDetail = function(_cpnNo){
    	LS.dialog("~/market/coupon/initCouponDetail?cpnNo="+_cpnNo,"优惠券详细信息",1000, 600,true, null);
    }
    
    </ls:script>
</ls:body>
</html>