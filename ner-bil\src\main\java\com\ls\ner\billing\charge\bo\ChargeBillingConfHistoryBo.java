package com.ls.ner.billing.charge.bo;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.charge.bo
 * @ClassName: ChargeBillingConfHistoryBo
 * @Author: bdBoWenYang
 * @Description: 充电计费配置_历史修改
 * @Date: 2025/3/31 15:46
 * @Version: 1.0
 */
public class ChargeBillingConfHistoryBo {

    /**
     * 主键id
     */
    private String historyId;

    /**
     * 计费编号
     */
    private String chcNo;

    /**
     * 操作人
     */
    private String operNo;

    private String dataOperType;

    private String dataOperTime;

    public String getDataOperTime() {
        return dataOperTime;
    }

    public void setDataOperTime(String dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    public String getDataOperType() {
        return dataOperType;
    }

    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType;
    }

    public String getHistoryId() {
        return historyId;
    }

    public void setHistoryId(String historyId) {
        this.historyId = historyId;
    }

    public String getChcNo() {
        return chcNo;
    }

    public void setChcNo(String chcNo) {
        this.chcNo = chcNo;
    }

    public String getOperNo() {
        return operNo;
    }

    public void setOperNo(String operNo) {
        this.operNo = operNo;
    }
}
