package com.ls.ner.billing.market.bo;

import java.io.Serializable;

/**
 * 积分规则用户关联
 * <AUTHOR>
 *
 */
public class IntegeralRuleUserBo  implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5986990022908933518L;

	private String ruleUserId;

	private String ruleId;//积分规则Id
	
	private String ruleName;//积分规则名称
	
	private String eventType;//触发事件
	
	private String eventTypeName;
	
	private String money;//发放金额
	
	private String userType;//用户类型
	
	private String userTypeName;
	
	private String maxTimes;//上限次数
	
	private String chargingPq;//发放  1  积分需充电?kWh

	private String extendJson;//扩展字段，与integralEventType相关\r\n若integralEventType == 5 含maxMoney,minMoney

	public String getExtendJson() {
		return extendJson;
	}

	public void setExtendJson(String extendJson) {
		this.extendJson = extendJson;
	}

	public String getRuleUserId() {
		return ruleUserId;
	}

	public void setRuleUserId(String ruleUserId) {
		this.ruleUserId = ruleUserId;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getEventTypeName() {
		return eventTypeName;
	}

	public void setEventTypeName(String eventTypeName) {
		this.eventTypeName = eventTypeName;
	}

	public String getMoney() {
		return money;
	}

	public void setMoney(String money) {
		this.money = money;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}

	public String getMaxTimes() {
		return maxTimes;
	}

	public void setMaxTimes(String maxTimes) {
		this.maxTimes = maxTimes;
	}

	public String getChargingPq() {
		return chargingPq;
	}

	public void setChargingPq(String chargingPq) {
		this.chargingPq = chargingPq;
	}

	
	
	
}
