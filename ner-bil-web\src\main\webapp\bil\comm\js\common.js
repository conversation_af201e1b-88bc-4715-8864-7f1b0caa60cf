var commonUtils = {
	copyObj : function(obj) {
		var objTemp = {};
		if(obj) {
			for(var key in obj) {
				objTemp[key] = obj[key];
			}
		}
		return objTemp;
	},
    concatStr : function(arr, key) {
	    var item = '';
	    if(arr && arr.length>0) {
	        for(var i=0; i< arr.length; i++) {
	            if(item == '') {
                    item = arr[i][key];
                }else {
	                item = item+','+arr[i][key];
                }
            }
        }
        return item;
    }
};



function initGridHeight(bodyId,girdName) {
	$(window).resize(function() {
		  setGridHeight(bodyId,girdName);
		});
	
	setTimeout(function() {
		$(window).trigger('resize');
	},300);
	//setGridHeight(bodyId,girdName);
}
//是否在可视化范围内
function isVisible($item) {
	return !($(window).scrollTop()>($item.offset().top+$item.outerHeight()) || $(window).scrollTop()<($item.offset().top-$(window).height()))
}


function setGridHeight(bodyId,girdName) {
	$('#'+bodyId).css('height','100%');
	var grid = $('#gbox__id_'+girdName);
	var rowContent = grid.find('.ui-jqgrid-bdiv');
	rowContent.css('height',0);
	var bodyHeight = $('#'+bodyId).outerHeight();
	var domArr = $('#'+bodyId).children();
	var height = 0;
	for(var i=0; i< domArr.length; i++) {
		if(!$(domArr[i]).is(":hidden") && isVisible($(domArr[i]))) {
			height+=$(domArr[i]).outerHeight();
		}
	}
	bodyHeight-=height;
	if(bodyHeight<0) {
		bodyHeight+=17;
	}
	rowContent.css('height',rowContent.outerHeight()+bodyHeight-5);
}