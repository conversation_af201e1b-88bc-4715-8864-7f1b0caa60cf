package com.ls.ner.billing.charge.service.impl;

import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertThat;

public class ChargeBillingRltAdapterTest {

	ChargeBillingRltAdapter adapter;
	
	ChargeBillingRltCalculator calculator;
	
	@Before
	public void setUp() throws Exception {
		adapter = new ChargeBillingRltAdapter();
		calculator = new ChargeBillingRltCalculator();
	}

	@After
	public void tearDown() throws Exception {
		adapter = null;
		calculator = null;
	}

	//测试用例1：DATA_TYPE = 1 , BOSS计费，推送数据为示数
	//		chargeMode = 01 ,计费模型为标准计费
	@Test
	public void testCaseOne(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "1");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 20:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_TIME", "12");//充电时间
		dataMap.put("MR_NUM", "250.876");//示数
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChargeMode("0201");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");//1.22+351.99+3.44+687.24+5.67
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		adapter.adapter(dataMap, cbrBo);
		assertThat("250.876", equalTo(cbrBo.getEndMrNum()));
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("16747.24", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和
		assertThat("17796.80", equalTo(cbrBo.getAmt()));//总金额
	}
	
	//测试用例2：DATA_TYPE = 1 , BOSS计费，推送数据为示数
	//		chargeMode = 02 ,计费模型为分时计费
	@Test
	public void testCaseTwo(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-31 10:02");//数据时间
		dataMap.put("DATA_TYPE", "1");//数据类型
		dataMap.put("MR_NUM", "250.876");//示数
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setBgnTime("2016-03-30 18:45");
		cbrBo.setChargeMode("0202");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		List<ChargeBillingRltPeriodsBo> rltPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
		ChargeBillingRltPeriodsBo b1 =new ChargeBillingRltPeriodsBo();
		b1.setSn("1");
		b1.setBeginTime("00:00");
		b1.setEndTime("12:00");
		b1.setPrice("10.23");
		rltPeriods.add(b1);
		ChargeBillingRltPeriodsBo b2 =new ChargeBillingRltPeriodsBo();
		b2.setSn("2");
		b2.setBeginTime("12:00");
		b2.setEndTime("18:45");
		b2.setPrice("20.23");
		rltPeriods.add(b2);
		ChargeBillingRltPeriodsBo b3 =new ChargeBillingRltPeriodsBo();
		b3.setSn("3");
		b3.setBeginTime("18:45");
		b3.setEndTime("24:00");
		b3.setPrice("30.23");
		rltPeriods.add(b3);
		cbrBo.setRltPeriods(rltPeriods);
		List<ChargeBillingRltPeriodsBo> newPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
		ChargeBillingRltPeriodsBo b4 =new ChargeBillingRltPeriodsBo();
		b4.setSn("1");
		b4.setBeginTime("18:45");
		b4.setEndTime("24:00");
		b4.setPrice("30.23");
		b4.setPq("100");
		newPeriods.add(b4);
		cbrBo.setNewPeriods(newPeriods);
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		assertThat("50.876", equalTo(cbrBo.getRltPeriods().get(1).getPq()));
		assertThat("10.23", equalTo(cbrBo.getRltPeriods().get(1).getPrice()));
		assertThat("2", equalTo(cbrBo.getRltPeriods().get(1).getSn()));
		assertThat("00:00", equalTo(cbrBo.getRltPeriods().get(1).getBeginTime()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("3543.46", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和
		assertThat("4593.02", equalTo(cbrBo.getAmt()));//总金额
	}
	
	//测试用例3：DATA_TYPE = 2 , BOSS计费，推送数据为电量
	//		chargeMode = 01 ,计费模型为标准计费
	@Test
	public void testCaseThree(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "2");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_TIME", "12");//充电时间
		dataMap.put("T_PQ", "150.876");//电量
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChargeMode("0201");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("16747.24", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和
		assertThat("17796.80", equalTo(cbrBo.getAmt()));//总金额
	}

	//测试用例4：DATA_TYPE = 2 , BOSS计费，推送数据为电量
	//		chargeMode = 02 ,计费模型为分时计费
	@Test
	public void testCaseFour(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "2");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_PQ", "150.876");//电量
		dataMap.put("TIME_SHAR_FLAG", "1");//分时
		List<Map<String,Object>> attachList = new ArrayList<Map<String,Object>>();//分时计费数据
		Map<String,Object> m1 = new HashMap<String,Object> ();
		m1.put("SN", "1");//排序号
		m1.put("BEGIN_TIME", "10:00");//开始时间
		m1.put("END_TIME", "12:00");//结束时间
		m1.put("PQ", "10.001");//时段电量
		attachList.add(m1);
		Map<String,Object> m2 = new HashMap<String,Object> ();
		m2.put("SN", "2");//排序号
		m2.put("BEGIN_TIME", "12:00");//开始时间
		m2.put("END_TIME", "24:00");//结束时间
		m2.put("PQ", "10.001");//时段电量
		attachList.add(m2);
		Map<String,Object> m3 = new HashMap<String,Object> ();
		m3.put("SN", "3");//排序号
		m3.put("BEGIN_TIME", "00:00");//开始时间
		m3.put("END_TIME", "08:00");//结束时间
		m3.put("PQ", "10.001");//时段电量
		attachList.add(m3);
		dataMap.put("PERIODSLIST", attachList);
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setAppNo("2222");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChcNo("11111");
		cbrBo.setChargeMode("0202");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		List<ChargeBillingRltPeriodsBo>  periods = new ArrayList<ChargeBillingRltPeriodsBo>();
		ChargeBillingRltPeriodsBo b1 =new ChargeBillingRltPeriodsBo();
		b1.setSn("1");
		b1.setBeginTime("00:00");
		b1.setEndTime("12:00");
		b1.setPrice("10.23");
		periods.add(b1);
		ChargeBillingRltPeriodsBo b2 =new ChargeBillingRltPeriodsBo();
		b2.setSn("2");
		b2.setBeginTime("12:00");
		b2.setEndTime("24:00");
		b2.setPrice("20.23");
		periods.add(b2);
		cbrBo.setRltPeriods(periods);
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		assertThat("10.23", equalTo(cbrBo.getRltPeriods().get(0).getPrice()));
		assertThat("20.23", equalTo(cbrBo.getRltPeriods().get(1).getPrice()));
		assertThat("10.23", equalTo(cbrBo.getRltPeriods().get(2).getPrice()));
		assertThat("11111", equalTo(cbrBo.getRltPeriods().get(2).getChcNo()));
		assertThat("2222", equalTo(cbrBo.getRltPeriods().get(2).getAppNo()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("406.94", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和 102.31 + 202.32 + 102.31
		assertThat("1456.50", equalTo(cbrBo.getAmt()));//总金额
	}
	
	//测试用例5：DATA_TYPE = 3 , 桩计费，推送数据为电量
	//		chargeMode = 01 ,计费模型为标准计费
	@Test
	public void testCaseFive(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "3");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_TIME", "12");//充电时间
		dataMap.put("T_PQ", "150.876");//电量
		dataMap.put("T_PRICE", "150.876");//充电电价
		dataMap.put("T_AMT", "150.876");//充电金额
		dataMap.put("ITEM_T_AMT", "150.876");//服务费用项总金额
		dataMap.put("AMT", "150.876");//总金额
		List<Map<String,Object>> serList = new ArrayList<Map<String,Object>>();//服务项
		Map<String,Object> s1 = new HashMap<String,Object> ();//服务项
		s1.put("ITEM_NO", "1");//服务费用项编码
		s1.put("ITEM_PRICE", "1.111");//服务费用项单价
		s1.put("ITEM_UNIT", "0200");//单位
		s1.put("ITEM_NUM", "1");//数量
		s1.put("ITEM_AMT", "1.11");//服务费用项金额
		serList.add(s1);
		Map<String,Object> s2 = new HashMap<String,Object> ();//服务项
		s2.put("ITEM_NO", "2");//服务费用项编码
		s2.put("ITEM_PRICE", "2.222");//服务费用项单价
		s2.put("ITEM_UNIT", "0201");//单位
		s2.put("ITEM_NUM", "150.876");//数量
		s2.put("ITEM_AMT", "335.25");//服务费用项金额
		serList.add(s2);
		Map<String,Object> s3 = new HashMap<String,Object> ();//服务项
		s3.put("ITEM_NO", "3");//服务费用项编码
		s3.put("ITEM_PRICE", "3.333");//服务费用项单价
		s3.put("ITEM_UNIT", "0200");//单位
		s3.put("ITEM_NUM", "1");//数量
		s3.put("ITEM_AMT", "3.33");//服务费用项金额
		serList.add(s3);
		Map<String,Object> s4 = new HashMap<String,Object> ();//服务项
		s4.put("ITEM_NO", "4");//服务费用项编码
		s4.put("ITEM_PRICE", "4.444");//服务费用项单价
		s4.put("ITEM_UNIT", "0201");//单位
		s4.put("ITEM_NUM", "150.876");//数量
		s4.put("ITEM_AMT", "670.49");//服务费用项金额
		serList.add(s4);
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setChargeMode("0201");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.getAmt()));
	}
	
	//测试用例6：DATA_TYPE = 3 , 桩计费，推送数据为电量
	//		chargeMode = 02 ,计费模型为分时计费
	@Test
	public void testCaseSix(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "3");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_PQ", "150.876");//电量
		dataMap.put("T_PRICE", "150.876");//充电电价
		dataMap.put("T_AMT", "150.876");//充电金额
		dataMap.put("ITEM_T_AMT", "150.876");//服务费用项总金额
		dataMap.put("AMT", "150.876");//总金额
		dataMap.put("TIME_SHAR_FLAG", "1");//分时
		List<Map<String,Object>> serList = new ArrayList<Map<String,Object>>();//服务项
		Map<String,Object> s1 = new HashMap<String,Object> ();//服务项
		s1.put("ITEM_NO", "1");//服务费用项编码
		s1.put("ITEM_PRICE", "1.111");//服务费用项单价
		s1.put("ITEM_UNIT", "0200");//单位
		s1.put("ITEM_NUM", "1");//数量
		s1.put("ITEM_AMT", "1.11");//服务费用项金额
		serList.add(s1);
		Map<String,Object> s2 = new HashMap<String,Object> ();//服务项
		s2.put("ITEM_NO", "2");//服务费用项编码
		s2.put("ITEM_PRICE", "2.222");//服务费用项单价
		s2.put("ITEM_UNIT", "0201");//单位
		s2.put("ITEM_NUM", "150.876");//数量
		s2.put("ITEM_AMT", "335.25");//服务费用项金额
		serList.add(s2);
		Map<String,Object> s3 = new HashMap<String,Object> ();//服务项
		s3.put("ITEM_NO", "3");//服务费用项编码
		s3.put("ITEM_PRICE", "3.333");//服务费用项单价
		s3.put("ITEM_UNIT", "0200");//单位
		s3.put("ITEM_NUM", "1");//数量
		s3.put("ITEM_AMT", "3.33");//服务费用项金额
		serList.add(s3);
		dataMap.put("ATTACHLIST", serList);
		Map<String,Object> s4 = new HashMap<String,Object> ();//服务项
		s4.put("ITEM_NO", "4");//服务费用项编码
		s4.put("ITEM_PRICE", "4.444");//服务费用项单价
		s4.put("ITEM_UNIT", "0201");//单位
		s4.put("ITEM_NUM", "150.876");//数量
		s4.put("ITEM_AMT", "670.49");//服务费用项金额
		serList.add(s4);
		List<Map<String,Object>> attachList = new ArrayList<Map<String,Object>>();//分时计费数据
		Map<String,Object> m1 = new HashMap<String,Object> ();
		m1.put("SN", "1");//排序号
		m1.put("BEGIN_TIME", "10:00");//开始时间
		m1.put("END_TIME", "12:00");//结束时间
		m1.put("PQ", "10.001");//时段电量
		m1.put("PRICE", "1");//时段电价
		m1.put("AMT", "10.001");//时段金额
		attachList.add(m1);
		Map<String,Object> m2 = new HashMap<String,Object> ();
		m2.put("SN", "2");//排序号
		m2.put("BEGIN_TIME", "12:00");//开始时间
		m2.put("END_TIME", "24:00");//结束时间
		m2.put("PQ", "10.001");//时段电量
		m2.put("PRICE", "1");//时段电价
		m2.put("AMT", "10.001");//时段金额
		attachList.add(m2);
		Map<String,Object> m3 = new HashMap<String,Object> ();
		m3.put("SN", "3");//排序号
		m3.put("BEGIN_TIME", "00:00");//开始时间
		m3.put("END_TIME", "08:00");//结束时间
		m3.put("PQ", "10.001");//时段电量
		m3.put("PRICE", "1");//时段电价
		m3.put("AMT", "10.001");//时段金额
		attachList.add(m3);
		dataMap.put("PERIODSLIST", attachList);
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChargeMode("0202");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.getAmt()));
		assertThat("1,2,3,4", equalTo(cbrBo.getAttachItemNos()));
		assertThat("1.111,2.222,3.333,4.444", equalTo(cbrBo.getAttachItemPrices()));
		assertThat("1,150.876,1,150.876", equalTo(cbrBo.getAttachItemNums()));
		assertThat("0200,0201,0200,0201", equalTo(cbrBo.getAttachItemUnits()));
		assertThat("1", equalTo(cbrBo.getRltPeriods().get(0).getPrice()));
		assertThat("1", equalTo(cbrBo.getRltPeriods().get(1).getPrice()));
		assertThat("1", equalTo(cbrBo.getRltPeriods().get(2).getPrice()));
		assertThat("11111", equalTo(cbrBo.getRltPeriods().get(2).getChcNo()));
	}
	
	
	//========================================================容错测试================================================
	//测试用例7：DATA_TYPE = 2 , BOSS计费，推送数据为电量       TIME_SHAR_FLAG 为0，不分时
		//		chargeMode = 02 ,计费模型为分时计费
	@Test
	public void testCaseSeven(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "2");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_TIME", "11");//充电时间
		dataMap.put("T_PQ", "150.876");//电量
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setBgnTime("2016-03-30 12:02");
		cbrBo.setAppNo("2222");
		cbrBo.setChcNo("11111");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChargeMode("0202");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		List<ChargeBillingRltPeriodsBo>  periods = new ArrayList<ChargeBillingRltPeriodsBo>();
		ChargeBillingRltPeriodsBo b1 =new ChargeBillingRltPeriodsBo();
		b1.setSn("1");
		b1.setBeginTime("00:00");
		b1.setEndTime("12:00");
		b1.setPrice("10.23");
		periods.add(b1);
		ChargeBillingRltPeriodsBo b2 =new ChargeBillingRltPeriodsBo();
		b2.setSn("2");
		b2.setBeginTime("12:00");
		b2.setEndTime("24:00");
		b2.setPrice("20.23");
		periods.add(b2);
		cbrBo.setRltPeriods(periods);
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		assertThat("20.23", equalTo(cbrBo.getChargePrice()));
		assertThat(null, equalTo(cbrBo.getRltPeriods()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("3052.22", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和 102.31 + 202.32 + 102.31
		assertThat("4101.78", equalTo(cbrBo.getAmt()));//总金额
	}
	
	//测试用例8：DATA_TYPE = 2 , BOSS计费，推送数据为电量   TIME_SHAR_FLAG 为1，分时
		//		chargeMode = 01 ,计费模型为标准计费
	@Test
	public void testCaseEight(){
		Map<String,Object> dataMap = new HashMap<String, Object>();
		dataMap.put("DATA_TIME", "2016-03-30 20:02");//数据时间
		dataMap.put("DATA_TYPE", "2");//数据类型
		dataMap.put("BGN_TIME", "2016-03-30 09:01");//结束时间
		dataMap.put("END_TIME", "2016-03-30 20:02");//结束时间
		dataMap.put("T_PQ", "150.876");//电量
		dataMap.put("TIME_SHAR_FLAG", "1");//分时
		List<Map<String,Object>> attachList = new ArrayList<Map<String,Object>>();//分时计费数据
		Map<String,Object> m1 = new HashMap<String,Object> ();
		m1.put("SN", "1");//排序号
		m1.put("BEGIN_TIME", "10:00");//开始时间
		m1.put("END_TIME", "12:00");//结束时间
		m1.put("PQ", "10.001");//时段电量
		attachList.add(m1);
		Map<String,Object> m2 = new HashMap<String,Object> ();
		m2.put("SN", "2");//排序号
		m2.put("BEGIN_TIME", "12:00");//开始时间
		m2.put("END_TIME", "24:00");//结束时间
		m2.put("PQ", "10.001");//时段电量
		attachList.add(m2);
		Map<String,Object> m3 = new HashMap<String,Object> ();
		m3.put("SN", "3");//排序号
		m3.put("BEGIN_TIME", "00:00");//开始时间
		m3.put("END_TIME", "08:00");//结束时间
		m3.put("PQ", "10.001");//时段电量
		attachList.add(m3);
		dataMap.put("PERIODSLIST", attachList);
		ChargeBillingRltBo cbrBo = new ChargeBillingRltBo();
		cbrBo.setChcNo("11111");
		cbrBo.setAppNo("2222");
		cbrBo.setBgnTime("2016-03-30 20:01");
		cbrBo.setChargeMode("0201");
		cbrBo.setAttachItemNos("01,02,03,04,05");
		cbrBo.setAttachItemPrices("1.222,2.333,3.444,4.555,5.666");
		cbrBo.setAttachItemUnits("0200,0201,0200,0201,0200");
		cbrBo.setChargePrice("111");
		cbrBo.setBgnMrNum("100");
		adapter.adapter(dataMap, cbrBo);
		assertThat("150.876", equalTo(cbrBo.gettPq()));
		assertThat("111", equalTo(cbrBo.getRltPeriods().get(0).getPrice()));
		assertThat("10.001", equalTo(cbrBo.getRltPeriods().get(1).getPq()));
		assertThat("111", equalTo(cbrBo.getRltPeriods().get(1).getPrice()));
		assertThat("111", equalTo(cbrBo.getRltPeriods().get(2).getPrice()));
		assertThat("11111", equalTo(cbrBo.getRltPeriods().get(2).getChcNo()));
		assertThat("2222", equalTo(cbrBo.getRltPeriods().get(2).getAppNo()));
		calculator.calculator(cbrBo);
		assertThat("1049.56", equalTo(cbrBo.getItemTAmt()));//服务项目总金额
		assertThat("3330.33", equalTo(cbrBo.gettAmt()));//充电金额。如果是分时，则为分时金额总和 102.31 + 202.32 + 102.31
		assertThat("4379.89", equalTo(cbrBo.getAmt()));//总金额
	}
}
