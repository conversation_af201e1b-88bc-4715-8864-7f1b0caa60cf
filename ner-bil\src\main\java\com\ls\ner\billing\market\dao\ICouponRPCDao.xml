<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.ICouponRPCDao">

	<resultMap id="billBo" type="com.ls.ner.billing.market.bo.BillBo">
		<result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
		<result column="PROD_BUSI_TYPE" property="prodBusiType" jdbcType="VARCHAR" />
		<result column="CUST_ID" property="custId" jdbcType="VARCHAR" />
		<result column="BILL_AMT" property="billAmt" jdbcType="VARCHAR" />
		<result column="SP_T_AMT" property="spTAmt" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="couponBo" type="com.ls.ner.billing.market.bo.CouponContentBo">
		<result column="CPN_ID" property="cpnId" jdbcType="VARCHAR" />
		<result column="CPN_NAME" property="cpnName" jdbcType="VARCHAR" />
		<result column="CPN_TYPE" property="cpnType" jdbcType="VARCHAR" />
		<result column="PROD_BUSI_TYPE" property="prodBusiType" jdbcType="VARCHAR" />
		<result column="CPN_AMT" property="cpnAmt" jdbcType="VARCHAR" />
		<result column="CPN_MARKS" property="cpnMarks" jdbcType="VARCHAR" />
		<result column="CPN_DCT_TYPE" property="dctType" jdbcType="VARCHAR" />
		<result column="PROD_ID" property="prodId" jdbcType="VARCHAR" />
		<result column="DCT_CALC_METHOD" property="dctCalcMethod" jdbcType="VARCHAR" />
		<result column="CALC_PRECISION" property="calcPrecision" jdbcType="VARCHAR" />
		<result column="CALC_PRECISION_DIGITS" property="calcPrecisionDigits" jdbcType="VARCHAR" />
		<result column="TIME_COND_FLAG" property="timeCondFlag" jdbcType="VARCHAR" />
		<result column="TIME_COND_BEG" property="timeCondBeg" jdbcType="VARCHAR" />
		<result column="TIME_COND_END" property="timeCondEnd" jdbcType="VARCHAR" />
		<result column="INV_DATE" property="invDate" jdbcType="VARCHAR" />
	</resultMap>

	<!-- 通过过滤条件查询优惠券信息-->
	<select id="queryCouponListByCondition" parameterType="java.util.Map" resultMap="couponBo">
		(
			SELECT a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
				a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
			FROM
				e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
			WHERE
				b.DCT_COND_FLAG = 1
				<if test="timeFlag == '1'.toString()">
					AND (IF (
						b.TIME_COND_FLAG = 2,
						DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
						AND DATE_FORMAT(now(), '%H%i') <![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
						TRUE
					))
				</if>
				and b.DCT_COND_AREA_FLAG = 1
				and a.cpn_id IN
				<foreach collection="list" item="item" open="(" close=")" separator=",">
					#{item.cpnId}
				</foreach>
		)
		UNION
		(
			SELECT
				a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
				a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
			FROM
				e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
			WHERE
				b.DCT_COND_FLAG = 2
                <if test="timeFlag == '1'.toString()">
                    AND (IF (
                    b.TIME_COND_FLAG = 2,
                    DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
                    AND DATE_FORMAT(now(), '%H%i') <![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
                    TRUE
                    ))
                </if>
				and b.DCT_COND_AREA_FLAG = 1
				and a.cpn_id IN
				<foreach collection="list" item="item" open="(" close=")" separator=",">
					#{item.cpnId}
				</foreach>
				and (#{amount} <![CDATA[ >= ]]> IFNULL(b.DCT_COND_VALUE,0) or b.DCT_COND_VALUE=0)
		)
		UNION
		(
			SELECT
				a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
				a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
			FROM
				e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
			WHERE
				b.DCT_COND_FLAG = 1
                <if test="timeFlag == '1'.toString()">
                    AND (IF (
                    b.TIME_COND_FLAG = 2,
                    DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
                    AND DATE_FORMAT(now(), '%H%i') <![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
                    TRUE
                    ))
                </if>
				and b.DCT_COND_AREA_FLAG = 2
				and a.cpn_id IN
				<foreach collection="list" item="item" open="(" close=")" separator=",">
					#{item.cpnId}
				</foreach>
				and (ISNULL(b.DCT_COND_CITY)=1 or b.DCT_COND_CITY = "" or b.DCT_COND_CITY LIKE "%"#{city}"%")
				and (ISNULL(b.DCT_COND_STATION)=1 or b.DCT_COND_STATION = "" or b.DCT_COND_STATION LIKE "%"#{stationId}"%")
		)
		UNION
		(
			SELECT
				a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
				a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
			FROM
				e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
			WHERE
				b.DCT_COND_FLAG = 2
                <if test="timeFlag == '1'.toString()">
                    AND (IF (
                    b.TIME_COND_FLAG = 2,
                    DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
                    AND DATE_FORMAT(now(), '%H%i')<![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
                    TRUE
                    ))
                </if>
				and b.DCT_COND_AREA_FLAG = 2
				and a.cpn_id IN
				<foreach collection="list" item="item" open="(" close=")" separator=",">
					#{item.cpnId}
				</foreach>
				and (#{amount} <![CDATA[ >= ]]> IFNULL(b.DCT_COND_VALUE,0) or b.DCT_COND_VALUE=0)
				and (ISNULL(b.DCT_COND_CITY)=1 or b.DCT_COND_CITY = "" or b.DCT_COND_CITY LIKE "%"#{city}"%")
				and (ISNULL(b.DCT_COND_STATION)=1 or b.DCT_COND_STATION = "" or b.DCT_COND_STATION LIKE "%"#{stationId}"%")
		)
		<if test="totalNum != null and totalNum != 0">
			limit #{startNum} ,#{totalNum}
		</if>
	</select>

<!-- 描述: 获取RPC05-11-01 优惠券详情 创建人:biaoxiangd  创建时间:2017-06-08 14:08 -->
	<select id="queryCouponList" parameterType="java.util.List" resultMap="couponBo">
		SELECT
		a.cpn_id,
		a.cpn_name,
		a.cpn_type,
		a.prod_busi_type,
		a.cpn_amt,
		a.cpn_marks,
		a.cpn_dct_type,
		a.prod_id,
		a.dct_calc_method,
		a.calc_precision,
		a.calc_precision_digits,
		b.TIME_COND_FLAG,
		b.TIME_COND_BEG,
		b.TIME_COND_END
		FROM
		e_coupon a LEFT JOIN e_dct_cond b ON a.CPN_ID = b.CPN_ID
		WHERE 
			a.cpn_id in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.cpnId}
		</foreach>
		/*AND cpn_state = '1'*/
	</select>

<!-- 描述:验证订单是否存在并获取费用总金额  创建人:biaoxiangd  创建时间:2017-06-06 18:04 -->
	<select id="queryBillInfo" parameterType="string" resultMap="billBo">
		SELECT ORDER_NO,PROD_BUSI_TYPE,CUST_ID,BILL_AMT,SP_T_AMT  FROM B_BILL WHERE ORDER_NO = #{orderNo} limit 1
	</select>

<!-- 描述:查询优惠券信息  创建人:biaoxiangd  创建时间:2017-06-06 20:03 -->
	<select id="queryCouponInfo" parameterType="string" resultType="com.ls.ner.billing.market.bo.CouponBo">
	SELECT CPN_ID cpnId,CPN_NAME cpnName,CPN_TYPE cpnType,BUSI_TYPE busiType,DCT_TYPE dctType,PROD_ID prodId,CPN_AMT cpnAmt,
		DCT_CALC_METHOD dctCalcMethod,CPN_TIME_TYPE cpnTimeType,CPN_NUM cpnNum,LIM_GET_NUM limGetNum,
		ALRDY_GET_NUM alrdyGetNum,CPN_STATUS cpnStatus,CALC_PRECISION calcPrecision,CALC_PRECISION_DIGITS calcPrecisionDigits,
		CPN_COND_ID cpnCondId,DCT_COND_FLAG dctCondFlag,DCT_TYPE dctCondType,DCT_COND_VALUE dctCondValue,
		DCT_COND_AREA_FLAG  dctCondAreaFlag,
		DCT_COND_CITY dctCondCity,
		DCT_COND_STATION dctCondStation,DCT_BUSI_TYPE dctBusiType,DCT_PROD_ID dctProdId,FULL_REDUCTION_AMT fullReductionAmt,
		MAXIMUM_DISCOUNT_AMT maximumDiscountAmt
	 FROM (
		SELECT
			c.CPN_ID,CPN_NAME,CPN_TYPE,PROD_BUSI_TYPE AS BUSI_TYPE,CPN_DCT_TYPE as DCT_TYPE,C.PROD_ID,
				CPN_AMT,DCT_CALC_METHOD,CPN_TIME_TYPE,IFNULL(CPN_NUM, '0') AS CPN_NUM,IFNULL(LIM_GET_NUM, '0') AS LIM_GET_NUM,
				IFNULL(ALRDY_GET_NUM, '0') AS ALRDY_GET_NUM,CPN_STATE AS CPN_STATUS,CALC_PRECISION,CALC_PRECISION_DIGITS,
			CPN_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,
			DCT_COND_AREA_FLAG,
			DCT_COND_CITY,
			DCT_COND_STATION,DCT_BUSI_TYPE,DCT_PROD_ID,FULL_REDUCTION_AMT,MAXIMUM_DISCOUNT_AMT
		FROM
			e_coupon c
		LEFT JOIN
			(SELECT
				CPN_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,CPN_ID,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,
				LEFT (STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') - 1) DCT_PROD_ID,
				SUBSTRING(STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') + 1) DCT_BUSI_TYPE
			FROM
				(SELECT DCT_COND_ID AS CPN_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,CPN_ID,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,
					(SELECT CONCAT(PROD_ID, '_', PROD_BUSI_TYPE)FROM B_PROD p WHERE p.PROD_ID = PROD_ID LIMIT 1) STR_BUSI_TYPE
					FROM E_DCT_COND b) ee
			) dd
		ON dd.cpn_id = c.cpn_id
		WHERE c.cpn_id = #{cpnId}
		<if test="prodBusiType != null and prodBusiType != ''">
			AND PROD_BUSI_TYPE = #{prodBusiType}
		</if>
	) aa limit 1
	</select>
<!-- 描述:新增订单优惠券流水  创建人:biaoxiangd  创建时间:2017-06-07 9:17 -->
	<insert id="insertBillCoupon" parameterType="com.ls.ner.billing.market.bo.BillBo">
		REPLACE INTO B_BILL_COUPON(ORDER_NO,CPN_ID,SP_AMT,CALC_PROCESS)
        SELECT #{orderNo},#{cpnId},#{spAmt},#{calcProcess} FROM dual

	</insert>
<!-- 描述:更新订单计费结果中的订单优惠总金额  创建人:biaoxiangd  创建时间:2017-06-07 9:29 -->
	<update id="updateBill" parameterType="string">
		UPDATE B_BILL
		SET SP_T_AMT = IFNULL(SP_T_AMT,0) + IFNULL(#{spTAmt},0)
		WHERE
			ORDER_NO = #{orderNo}
	</update>
	<!--查询客户订单数量-->
	<select id="queryBillNum" parameterType="Map" resultType="int">
		select count(1) from B_BILL
		<where>
			<if test="orderState != null and orderState !=''">
				AND ORDER_STATE=#{orderState}
			</if>
			<if test="custId != null and custId !=''">
				AND CUST_ID = #{custId}
			</if>
		</where>
	</select>


	<!-- 通过过滤条件查询可用优惠券信息-->
	<select id="queryMyCouponListByCondition" parameterType="java.util.Map" resultMap="couponBo">
		(
		SELECT a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
		a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
		FROM
		e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
		WHERE
		b.DCT_COND_FLAG = 1
		AND (IF (
		b.TIME_COND_FLAG = 2,
		DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
		AND DATE_FORMAT(now(), '%H%i') <![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
		TRUE
		))
		and a.cpn_id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.cpnId}
		</foreach>
		and (IFNULL(b.DCT_COND_BUILD,'')=1 or b.DCT_COND_BUILD LIKE  CONCAT('%',#{buildId},'%'))
		and (IFNULL(b.DCT_COND_CITY,'')=1 or b.DCT_COND_CITY LIKE  CONCAT('%',#{city},'%'))
		and (IFNULL(b.DCT_COND_STATION,'')=1 or b.DCT_COND_STATION LIKE   CONCAT('%',#{stationId},'%'))
		)
		UNION
		(
		SELECT
		a.cpn_id,a.cpn_name,a.cpn_type,a.prod_busi_type,a.cpn_amt,a.cpn_marks,a.cpn_dct_type,a.prod_id,a.dct_calc_method,
		a.calc_precision,a.calc_precision_digits,b.TIME_COND_FLAG,b.TIME_COND_BEG,b.TIME_COND_END
		FROM
		e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
		WHERE
		b.DCT_COND_FLAG = 2
		AND (IF (
		b.TIME_COND_FLAG = 2,
		DATE_FORMAT(now(), '%H%i') <![CDATA[ >= ]]> REPLACE (b.TIME_COND_BEG, ':', '')
		AND DATE_FORMAT(now(), '%H%i') <![CDATA[ <= ]]> REPLACE (b.TIME_COND_END, ':', ''),
		TRUE
		))
		and a.cpn_id IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.cpnId}
		</foreach>
		and (#{amount} <![CDATA[ >= ]]> IFNULL(b.DCT_COND_VALUE,0) or b.DCT_COND_VALUE=0)
		and (IFNULL(b.DCT_COND_BUILD,'')=1 or b.DCT_COND_BUILD LIKE  CONCAT('%',#{buildId},'%'))
		and (IFNULL(b.DCT_COND_CITY,'')=1 or b.DCT_COND_CITY LIKE  CONCAT('%',#{city},'%'))
		and (IFNULL(b.DCT_COND_STATION,'')=1 or b.DCT_COND_STATION LIKE   CONCAT('%',#{stationId},'%'))
		)
		<if test="totalNum != null and totalNum != 0">
			limit #{startNum} ,#{totalNum}
		</if>
	</select>

	<!-- 通过过滤条件查询可用优惠券信息-->
	<select id="queryMyCouponInfo" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT b.DCT_COND_STATION dicCondStation,b.DCT_COND_CITY dctCondCity,b.DCT_COND_BUILD dctCondBuild
		FROM e_coupon a LEFT JOIN e_dct_cond b on a.CPN_ID=b.CPN_ID
		AND a.cpn_id = #{cpnId}
	</select>
</mapper>