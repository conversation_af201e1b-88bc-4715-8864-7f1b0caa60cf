package com.ls.ner.billing.vip.bo;

import com.pt.poseidon.api.framework.DicAttribute;

/**
 * @description 用户会员等级
 */
public class VipLevelBo {
    /** 主键id **/
    private String id;
    // 用户id
    private String custId;
    /** 等级 **/
    private String vipLevel;
    private String chargeTime;//变更时间
    private String mobile;
    private String levelFlag; //升级标识

    public String getLevelFlag() {
        return levelFlag;
    }

    public void setLevelFlag(String levelFlag) {
        this.levelFlag = levelFlag;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(String vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getChargeTime() {
        return chargeTime;
    }

    public void setChargeTime(String chargeTime) {
        this.chargeTime = chargeTime;
    }
}
