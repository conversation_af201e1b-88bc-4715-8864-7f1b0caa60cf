<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.item.dao.IChargeItemDao">
	<resultMap type="com.ls.ner.billing.item.bo.ChargeItemBo" id="itemBo">
		<result column="ITEM_ID" property="itemId" />
		<result column="CHARGE_ITEM_CODE" property="chargeItemCode" />
		<result column="ITEM_NAME" property="itemName" />
		<result column="CHARGE_TYPE" property="chargeType" />
		<result column="NEED_FLAG" property="needFlag" />
		<result column="ITEM_DESC" property="itemDesc" />
		<result column="EFFECT_FLAG" property="effectFlag" />
		<result column="BUILD_DATE" property="buildDate" />
		<result column="PRICING_AMT" property="pricingAmt" />
		<result column="TIME_LINE" property="timeLine" />
	</resultMap>

	<insert id="saveChargeItem" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		insert into e_charge_item(
		item_id,charge_item_code,item_name,charge_type,
		need_flag,item_desc,effect_flag,build_date,time_line)
		values (
		#{itemId},#{chargeItemCode},#{itemName},#{chargeType},
		#{needFlag},#{itemDesc},#{effectFlag},#{buildDate},#{timeLine});
	</insert>

	<update id="updateChargeItem" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		update e_charge_item set
		charge_item_code=#{chargeItemCode},
		item_name=#{itemName},
		charge_type=#{chargeType},
		need_flag=#{needFlag},
		item_desc=#{itemDesc},
		effect_flag=#{effectFlag},
		effect_flag=#{effectFlag},
		time_line=#{timeLine}
		where item_id=#{itemId}
	</update>

	<select id="getChargeItem" resultMap="itemBo" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		select a.item_id,a.charge_item_code,a.item_name,
		a.charge_type,a.need_flag,a.item_desc,a.effect_flag,a.build_date,a.time_line
		from e_charge_item a
		<where>
			<if test="itemId != null">
				AND a.item_id =#{itemId}
			</if>
			<if test="chargeItemCode != null">
				AND a.charge_item_code =#{chargeItemCode}
			</if>
			<if test="itemName != null">
				AND a.item_name =#{itemName}
			</if>
			<if test="chargeType != null">
				AND a.charge_type =#{chargeType}
			</if>
		</where>
		order by a.charge_type
		<if test="pageEnd!=null and pageEnd!=0">
			limit #{pageBegin} ,#{pageEnd}
		</if>
	</select>

	<select id="getChargeItemCount" resultType="int" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		select count(1) from e_charge_item a
		<where>
			<if test="itemId != null">
				AND a.item_id =#{itemId}
			</if>
			<if test="chargeItemCode != null">
				AND a.charge_item_code =#{chargeItemCode}
			</if>
			<if test="itemName != null">
				AND a.item_name =#{itemName}
			</if>
			<if test="chargeType != null">
				AND a.charge_type =#{chargeType}
			</if>
		</where>
	</select>

	<delete id="deleteChargeItem" parameterType="java.lang.String">
		delete from e_charge_item where item_id=#{itemId}
	</delete>

	<select id="getItemDetail" resultMap="itemBo" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		select a.billing_type,a.app_no,b.item_name,b.pricing_amt
		from e_prc_charge a,e_prc_charge_det b
		<where>
			a.prc_id=b.prc_id
			<if test="appNo != null">
				AND a.app_no =#{appNo}
			</if>
			<if test="billingType != null">
				AND a.billing_type =#{billingType}
			</if>
		</where>
		<if test="pageEnd!=null and pageEnd!=0">
			limit #{pageBegin} ,#{pageEnd}
		</if>
	</select>

	<select id="getItemDetailCount" resultType="int" parameterType="com.ls.ner.billing.item.bo.ChargeItemBo">
		select count(1) from e_prc_charge a,e_prc_charge_det b
		<where>
			a.prc_id=b.prc_id
			<if test="appNo != null">
				AND a.app_no =#{appNo}
			</if>
			<if test="billingType != null">
				AND a.billing_type =#{billingType}
			</if>
		</where>
	</select>
</mapper>