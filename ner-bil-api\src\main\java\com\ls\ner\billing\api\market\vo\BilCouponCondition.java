package com.ls.ner.billing.api.market.vo;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;

/**
 * Created by Administrator on 2017-06-13.
 */
public class BilCouponCondition extends QueryCondition implements Serializable {
    private static final long serialVersionUID = 1762624099030079698L;

    private String getChannel;
    private String useStatus;
    private String pageNum;
    private String totalNum;
    private String cpnId;
    private String prodBusiType;
    private String mobile;
    private String actType;


    public String getActType() {
        return actType;
    }

    public void setActType(String actType) {
        this.actType = actType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getProdBusiType() {
        return prodBusiType;
    }

    public void setProdBusiType(String prodBusiType) {
        this.prodBusiType = prodBusiType;
    }

    public String getGetChannel() {
        return getChannel;
    }

    public void setGetChannel(String getChannel) {
        this.getChannel = getChannel;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getPageNum() {
        return pageNum;
    }

    public void setPageNum(String pageNum) {
        this.pageNum = pageNum;
    }

    public String getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(String totalNum) {
        this.totalNum = totalNum;
    }

    public String getCpnId() {
        return cpnId;
    }

    public void setCpnId(String cpnId) {
        this.cpnId = cpnId;
    }
}
