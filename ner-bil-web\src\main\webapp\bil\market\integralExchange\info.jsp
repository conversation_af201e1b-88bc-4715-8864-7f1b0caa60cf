<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="商品管理" />
<ls:body>
		<ls:form name="infoForm" id="infoForm">
            <table class="tab_search">
                <tr>
                    <td  width="5%">
                        <ls:label text="订单编号" />
                    </td>
                    <td  width="28%">
                    	 <ls:text name="integralId"></ls:text>
                      
                    </td>
                    <td width="5%">
                        <ls:label text="商品名称" />
                    </td>
                    <td width="28%">
                        <ls:text name="goodsName"></ls:text>
                    </td>
                    <td width="5%">
                        <ls:label text="商品类型" />
                    </td>
                    <td width="28%">
                        <ls:select name="goodsType" >
                            <ls:options property="goodsTypeList" scope="request" text="name" value="value" />
                        </ls:select>
                    </td>
                </tr>
                <tr>
                      <td>
                        <ls:label text="手机号" />
                    </td>
                    <td>
                       <ls:text name="custMobile"></ls:text>
                    </td>
                      <td>
                        <ls:label text="下单时间" />
                    </td>
                
				<td>
					<table>
						<tr>
							<td width="45%"><ls:date name="startTime" format="yyyy-MM-dd HH:mm:ss" /></td>
							<td width="10%"><ls:label text="至" /></td>
							<td width="45%"><ls:date name="endTime" format="yyyy-MM-dd HH:mm:ss" /></td>
						</tr>
					</table>
				</td>
                     <td>
                        <ls:label text="状态" />
                    </td>
                    <td>
                        <ls:select name="status" >
                            <ls:options property="exchangeStatusList" scope="request" text="name" value="value" />
                        </ls:select>
                    </td>
                   
                </tr>
              <tr>
               <td colspan="6">
                        <div class="pull-right">
                           
                            <ls:button text="重置" onclick="doClear" />
                            <ls:button text="查询" onclick="doSearch" />
                        </div>
                    </td></tr>
            </table>
            <table align="center" class="tab_search">
                <tr>
                    <td>
                        <ls:grid url="" name="infoGrid" height="420" allowSorting="true" primaryKey="integralId"  >
                            <ls:gridToolbar name="myButton">                            
                                <ls:gridToolbarItem text="详情" imageKey="search" onclick="doPreview" name="search"></ls:gridToolbarItem>
                                <ls:gridToolbarItem text="录入单号" imageKey="edit" onclick="doEdit" name="edit"></ls:gridToolbarItem>
                            </ls:gridToolbar>
                            <ls:column name="integralId" caption="订单编号"  />
                            <ls:column name="goodsName"  caption="商品名称"    />
                            <ls:column name="goodsTypeName"  caption="商品类型"  />
                            <ls:column name="goodsType"   hidden="true"  />
                            <ls:column name="custMobile" caption="用户手机号" />
                            <ls:column name="exchangeTime" caption="下单时间" />
                            <ls:column name="statusName" caption="状态" />
                              <ls:column name="status"   hidden="true"/>     
<%--                            <ls:column caption="操作" name="oper" formatFunc="operFormat"/>                 --%>
                            <ls:pager pageSize="5"></ls:pager>
                        </ls:grid>
                    </td>
                </tr>
            </table>
        </ls:form>
 
    <ls:script>
   
	  window.infoGrid=infoGrid;
	  <%--window.edit=edit;--%>
	   window.doSearch=doSearch;
	   	   infoGrid.itemclick =function() {
 		var item = infoGrid.getSelectedItem();
        if(!item){
          	return;
        }
        var goodsTypeValue = item.goodsType;
        if(goodsTypeValue=="01"){
        	infoGrid.toolbarItems.edit.setEnabled(false);
          
        }else{
        	infoGrid.toolbarItems.edit.setEnabled(true);
        }
  }
    window.onload = function () {
       
          infoForm.clear();
        doSearch();
    }

  	function operFormat(item) {
<%--		if(item.status=='01' && item.goodsType=='02') {--%>
<%--		return '<a href="javascript:void(0);" class="clickLink" " style="color: blue;" onclick="edit('+item.integralId+')">录入单号</a>';--%>
<%--		}else{--%>
<%--		return '--';--%>
<%--		}--%>
		}
  
    function doSearch(){
    var start = startTime.getValue();
		var end = endTime.getValue();
		if (!LS.isEmpty(start) && !LS.isEmpty(end) && start>end) {
			LS.message("info", "开始时间不能大于截止时间!");
      		return;
		}
    
      infoGrid.query("~/billing/integralExchange/queryInfo",infoForm.getFormData());
    }
    function doClear(){
      infoForm.clear();
    }

 
		//修改    
		function doEdit(){
		  var item = infoGrid.getCheckedIDs();
		  if(item.length>0){
		    if(item.length>1){
		      LS.message("info","只能选择一条记录");
		      return;
		    }else{
		       var items=infoGrid.getCheckedItems();
		    if(items[0].goodsType=="01"){
		     LS.message("info","虚拟商品无法录入");
		      return;
		    
		    }
		      LS.dialog(rootUrl+"/billing/integralExchange/initEdit?id="+item[0], "录入单号",800, 400, true, null);
		    }
		  }else{
		    LS.message("info","请选择一条记录");
		    return;
		  }
		  
		}
		function doPreview(){
		  var item = infoGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.dialog(rootUrl+"/billing/integralExchange/initInfo?id="+item[0],"详情", 800, 400, true, null);
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
		}
   
    
   
    </ls:script>
</ls:body>
</html>