package com.ls.ner.billing.gift.service.impl;

import com.ls.ner.billing.api.gift.service.ICarGiftRPCService;
import com.ls.ner.billing.gift.bo.CarGiftBo;
import com.ls.ner.billing.gift.constant.CarGiftConstants;
import com.ls.ner.billing.gift.dao.ICarGiftDao;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description CarGiftRPCServiceImpl
 * @create 2020-05-05 10:34
 */
@Service(target = { ServiceType.RPC }, value = "carGiftRPCService")
public class CarGiftRPCServiceImpl implements ICarGiftRPCService {

    @Autowired
    private ICarGiftDao carGiftDao;

    /**
     * @param mobile
     * @description 礼金核销
     * <AUTHOR>
     * @create 2020-05-05 10:33:03
     */
    @Override
    public void updateCargiftStatus(String mobile) {
        CarGiftBo carGiftBo = new CarGiftBo();
        carGiftBo.setGiftStatus(CarGiftConstants.carGiftGetStatus.ISSUED);
        carGiftBo.setMobile(mobile);
        List<CarGiftBo> carGiftBoList = carGiftDao.listCarGiftByCondition(carGiftBo);
        if(CollectionUtils.isNotEmpty(carGiftBoList)){
            CarGiftBo bo = carGiftBoList.get(0);
            bo.setGiftStatus(CarGiftConstants.carGiftGetStatus.WRITTEN_OFF);
            carGiftDao.updateCarGift(bo);
        }
    }
}
