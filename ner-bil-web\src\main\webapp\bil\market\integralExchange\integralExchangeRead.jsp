<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="" >
</ls:head>
<ner:head/>
<ls:body>
	<ls:form name="infoForm" id="infoForm"  >
		 <ls:title text="订单信息" expand="true">
		<table class="tab_search">
			<tr >
					<ls:text name="goodsType" property="goodsType" visible="false"></ls:text>
				<td >
					<ls:label text="订单编号" ref="integralId" />
				</td>
				<td>
					<ls:text name="integralId" property="integralId" readOnly="true"></ls:text>
				</td>

				<td>
					<ls:label text="商品类型" ref="goodsTypeName" />
				</td>
				<td>
					<ls:text name="goodsTypeName" property="goodsTypeName" readOnly="true"></ls:text>
				</td>
			</tr>

			<tr>
				<td>
					<ls:label text="商品名称" ref="goodsName" />
				</td>
				<td>
					<ls:text name="goodsName" property="goodsName" readOnly="true"></ls:text>
				</td>

				<td>
					<ls:label text="购买数量" ref="number" />
				</td>
				<td>
					<ls:text name="number" property="number" readOnly="true"></ls:text>
				</td>
			</tr>

			<tr>

				<td>
					<ls:label text="实付积分" ref="integral"/>
				</td>
				<td>
					<ls:text name="integral" property="integral" readOnly="true" ></ls:text>
				</td>

				<td>
					<ls:label text="实付金额" ref="integral"/>
				</td>
				<td>
					<ls:text name="goodsAmt" property="goodsAmt" readOnly="true" ></ls:text>
				</td>

		  	</tr>

				<tr class="freight">

				<td >
					<ls:label text="收货人" ref="consignee" />
				</td>
				<td>
					<ls:text name="consignee" property="consignee" readOnly="true" ></ls:text>
				</td>


				<td >
					<ls:label text="收货人手机号" ref="mobile" />
				</td>
				<td>
					<ls:text name="mobile" property="mobile" readOnly="true"></ls:text>
				</td>
			</tr>

			<tr class="freight">

				<td class="freight">
					<ls:label text="发货类型" ref="goodsFreightName"  />
				</td>
				<td class="freight">
					<ls:text name="goodsFreightName" property="goodsFreightName" readOnly="true" ></ls:text>
				</td>

				<td>
					<ls:label text="收获地址" ref="address" />
				</td>
				<td colspan="3">

					<ls:text name="address" property="address" readOnly="true"></ls:text>


				</td>
			</tr>



		</table>
	</ls:title>
	<div id="logistics" >
	 <ls:title text="物流信息" expand="true">
		<table class="tab_search">
			<tr >

				<td width="11%">
					<ls:label text="物流公司" ref="logisticsName" />
				</td>
				<td width="36%">
					<ls:select name="logisticsName" property="logisticsName"  readOnly="true" >
						<ls:options property="logisticsList" scope="request" text="name" value="value"/>
					</ls:select>
				</td>
				<td width="14%">

				</td>
				<td width="49%">

				</td>

		  	</tr>
				<tr>


				<td>
					<ls:label text="快递号" ref="logisticsNo" />
				</td>
				<td>
					<ls:text name="logisticsNo" property="logisticsNo"  readOnly="true"></ls:text>
				</td>
				<td >

				</td>
				<td>

				</td>
			</tr>

			</table>
	</ls:title>
	</div>


	</ls:form>

	<ls:script>
	 	window.onload=function(){


	 		var type=goodsType.getValue();

				if(type=='01'){
					$('.freight').hide();

				$('#logistics').hide();

				}




		}








	</ls:script>
</ls:body>
</html>
