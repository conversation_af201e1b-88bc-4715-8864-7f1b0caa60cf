package com.ls.ner.billing.vip.dao;

import com.ls.ner.billing.api.vip.bo.VipSaveRecordRPCBo;
import com.ls.ner.billing.vip.bo.*;

import java.util.List;

/**
 * @description IMemberBenefitsDao
 */
public interface IVipSaveRecordDao {
    List<VipSaveRecordRPCBo> getSaveRecordByMobile(VipSaveRecordVo vo);

    VipSaveRecordRPCBo getUserSaveRecord(VipSaveRecordVo vo);

    void insertVipSaveRecord(VipSaveRecordBo bo);

    void updateByOrder(VipSaveRecordBo bo);

}
