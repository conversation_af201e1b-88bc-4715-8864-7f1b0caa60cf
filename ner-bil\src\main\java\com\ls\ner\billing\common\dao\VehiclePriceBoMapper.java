package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.VehiclePriceBo;
import com.ls.ner.billing.common.bo.VehiclePriceBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface VehiclePriceBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(VehiclePriceBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(VehiclePriceBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(VehiclePriceBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(VehiclePriceBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<VehiclePriceBo> selectByExample(VehiclePriceBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    VehiclePriceBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") VehiclePriceBo record, @Param("example") VehiclePriceBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") VehiclePriceBo record, @Param("example") VehiclePriceBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(VehiclePriceBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_vehicle_price
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(VehiclePriceBo record);
}