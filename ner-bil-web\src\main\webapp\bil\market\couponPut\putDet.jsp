<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券发放明细">
</ls:head>
<ls:body>
	<ls:form name="couponPutForm" id="couponPutForm">
		<ls:title text="优惠券信息"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td width="100px"><ls:label text="优惠券名称"  /></td>
				<td width="200px">
					<ls:text name="cpnName"  enabled="false"  property="cpnName"></ls:text>
					<ls:text name="cpnId"  visible="false" property="cpnId"></ls:text></td>

				<td><ls:label text="优惠内容" /></td>
				<td>
					<ls:text name="busiTypeName" property="busiTypeName" enabled="false"></ls:text>
					<ls:text name="busiType"  property="busiType"  visible="false"></ls:text>
				</td>

				<td><ls:label text="优惠券类型" /></td>
				<td>
					<ls:text name="cpnTypeName" property="cpnTypeName" enabled="false"></ls:text>
					<ls:text name="cpnType"  property="cpnType"  visible="false"></ls:text>
				</td>
			</tr>

			<tr>
				<td width="100px"><ls:label text="面额/折扣" /></td>
				<td width="200px">
					<ls:text name="cpnAmt" property="cpnAmt" enabled="false"></ls:text>
				</td>

				<td width="100px"><ls:label text="有效时间" /></td>
				<td width="200px"><ls:text name="effectTime" enabled="false" property="effectTime"/></td>

				<td width="100px"><ls:label text="优惠券状态" /></td>
				<td width="200px">
					<ls:text name="cpnStatusName" enabled="false" property="cpnStatusName"></ls:text>
					<ls:text name="cpnStatus" visible="false" property="cpnStatus" ></ls:text>
				</td>
			</tr>

			<tr>
				<td width="100px"><ls:label text="发放数量" /></td>
				<td width="200px"><ls:text name="cpnNum" enabled="false" property="cpnNum"></ls:text>
				</td>

				<td width="100px"><ls:label text="已领数量" /></td>
				<td width="200px">
					<ls:text name="alrdyGetNum" enabled="false" property="alrdyGetNum"></ls:text>
				</td>
			</tr>
		</table>
		<ls:title text="发放信息"></ls:title>
		   <ls:tab name="coupon" >
		      <ls:tabpage text="历史发放记录" name="putLog">
		      	 <table align="center" class="tab_search">
					<ls:grid url="" name="putLogGrid" caption="" primaryKey="getId"
						height="180px" width="100%" singleSelect="false"
						showRowNumber="false" showCheckBox="false" cellEdit="false" >
						<ls:column caption="发放主键" name="putId" hidden="true" />
						<ls:column caption="发放人员" name="putEmp" align="center"/>
						<ls:column caption="发放时间" name="putTime" align="center" />
						<ls:column caption="发放数量" name="putNum" align="center"  />
						<ls:column caption="通知方式" name="noticeType" hidden="true" />
						<ls:column caption="通知方式" name="noticeTypeName" align="center" />
						<ls:column caption="通知内容" name="noticeCont"/>
		
					</ls:grid>
				</table>
		      </ls:tabpage>
		      <ls:tabpage text="发放客户列表" name="putCustList">
		      	 <table align="center" class="tab_search">
					<ls:grid url="" name="custGrid" caption="" primaryKey="custId"
						height="180px" width="100%" singleSelect="false"
						showRowNumber="false" showCheckBox="false" cellEdit="false" >
						<ls:column caption="客户主键" name="custId" hidden="true" />
						<ls:column caption="发放人员" name="putEmp" align="center"/>
						<ls:column caption="发放时间" name="putTime" align="center" />
						<ls:column caption="发放成功标志" name="putFlag" align="center" />
						<ls:column caption="失败原因" name="failReason" align="center" />
						<ls:column caption="客户名称" name="custName" align="center" />
						<ls:column caption="手机号码" name="mobile" align="center"  />
						<ls:column caption="客户分类" name="custSortCode" hidden="true" />
						<ls:column caption="客户分类" name="custSortCodeName" />
					</ls:grid>
				</table>
		      </ls:tabpage>
		    
		  </ls:tab>
		
	</ls:form>

	<ls:script>
		query();
		function query(){
	      var datas={};
	      datas = couponPutForm.getFormData();
	      putLogGrid.query("~/billing/couponPut/getCouponPutLog",datas);
	      custGrid.query("~/billing/couponPut/getPutCustList",datas);
	    }

	
	</ls:script>

</ls:body>
</html>