<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="积分规则配置" />
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
<ls:body>
	<ls:form name="ruleForm" id="ruleForm" >
		 <ls:title text="查询条件"></ls:title>
      		  <table align="center" class="tab_search">
      		  	<tr>
      		  		<td><ls:label text="积分规则名称"></ls:label></td>
      		  		<td><ls:text name="ruleName"></ls:text></td>
      		  		
      		  		<td><ls:label text="触发事件"></ls:label></td>
	                <td>
	                    <ls:select name="eventType">
	                    	<ls:option value="all" text="全部" ></ls:option>
	                        <ls:options property="eventTypeList" scope="request" text="codeName" value="codeValue"/>
	                    </ls:select>
	                </td>
	                
	                <td><ls:label text="状态"></ls:label></td>
	                <td>
	                    <ls:select name="ruleStatus">
	                    	<ls:option value="all" text="全部" ></ls:option>
	                        <ls:options property="ruleStatusList" scope="request" text="codeName" value="codeValue"/>
	                    </ls:select>
	                </td>
      		  	</tr>
      		  	<tr>
	      		  <td colspan="6">
	                    <div class="pull-right">
	                        <ls:button text="查询" onclick="query"/>
	                        <ls:button text="清空" onclick="clearAll"/>
	                    </div>
	                </td>
	          	</tr>
	          	<tr>
	                <td colspan="6">
	                	<ls:grid url="" name="ruleGrid" caption="积分规则列表" showRowNumber="false"
	                             height="350px" showCheckBox="false" singleSelect="${(singleSelect=='' || singleSelect == null)?'true':singleSelect}">
	                    <ls:gridToolbar name="operation">
	                        <ls:gridToolbarItem name="add" text="创建" imageKey="add"
	                                            onclick="addRule" ></ls:gridToolbarItem>
	                        <ls:gridToolbarItem name="modify" text="修改" imageKey="edit"
	                                            onclick="modifyRule"></ls:gridToolbarItem>
	                        <ls:gridToolbarItem name="getInfo" text="详情" imageKey="get"
	                                            onclick="getRuleInfo" ></ls:gridToolbarItem>             
	                        <ls:gridToolbarItem name="start" text="启用" imageKey="start"
	                                            onclick="enableRule" ></ls:gridToolbarItem>
	                        <ls:gridToolbarItem name="stop" text="停用" imageKey="stop"
	                                            onclick="stopRule" ></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="clearTime" text="积分清零时间" imageKey="time"
                                            onclick="setClearTime" ></ls:gridToolbarItem>
	                    </ls:gridToolbar>
	                    <ls:column caption="积分规则id" name="ruleId" hidden="true"/>
	                    <ls:column caption="积分规则名称" name="ruleName" align="center" width="25%"/>
	                    <ls:column caption="触发事件" name="eventType" align="center" hidden="true"/>
	                    <ls:column caption="触发事件" name="eventTypeName" align="center" width="25%"/>
	                    <ls:column caption="状态" name="ruleStatus" align="center" hidden="true"/>
	                    <ls:column caption="状态" name="ruleStatusName" align="center" width="25%"/>
	                    <ls:column caption="创建时间" name="createTime" align="center" width="25%"/>
	                    <ls:column caption="修改时间" name="updateDate" hidden="true"/>
	                    <ls:pager pageSize="15,20"></ls:pager>
	                </ls:grid></td>
	            </tr>
	        </table>
	</ls:form>
	
	<ls:script>
		initShow();
		window.onload = function(){
            initShow();
        }
        
		function initShow(){
			ruleStatus.setValue("all");
			eventType.setValue("all");
			query();
		}
		window.query=query;
		
		function query(){
			var datas={};
            datas = ruleForm.getFormData();
            ruleGrid.query("~/billing/integralRule/getRuleList",datas);
		}
		
		<%-- 描述:点击数据行事件   --%>
        ruleGrid.onitemclick=function(item, event, rowid){
            if(item.ruleStatus == "02" ){ //生效
                ruleGrid.toolbarItems.modify.setEnabled(true);
                ruleGrid.toolbarItems.start.setEnabled(false);
                ruleGrid.toolbarItems.stop.setEnabled(true);
                ruleGrid.toolbarItems.getInfo.setEnabled(true);
            }else if(item.ruleStatus == "01"){ // 草稿
                ruleGrid.toolbarItems.modify.setEnabled(true);
                ruleGrid.toolbarItems.start.setEnabled(true);
                ruleGrid.toolbarItems.stop.setEnabled(false);
                ruleGrid.toolbarItems.getInfo.setEnabled(true);
            }else if(item.ruleStatus == "03"){ // 停用
                ruleGrid.toolbarItems.modify.setEnabled(true);
                ruleGrid.toolbarItems.start.setEnabled(true);
                ruleGrid.toolbarItems.stop.setEnabled(false);
                ruleGrid.toolbarItems.getInfo.setEnabled(true);
            }
        }
        
        function addRule(){
            LS.dialog("~/billing/integralRule/addRule","创建积分规则",950,500,true, null);
        }

        function modifyRule(){
            var item = ruleGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            LS.dialog("~/billing/integralRule/editRule?ruleId="+item.ruleId,"修改积分规则",950,450,true, null);
        }


        function enableRule(){
            var item = ruleGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = {
                ruleId:item.ruleId,
                ruleStatus:'02'
            };
            LS.ajax("~/billing/integralRule/updateStatus",params,function(data){
                if(data){
                    LS.message("info","启用成功!");
                    query();
                }else{
                    LS.message("info",data.msg);
                }
            });
        }
        function stopRule(){
            var item = ruleGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
             var params = {
                ruleId:item.ruleId,
                ruleStatus:'03'
            };
            LS.ajax("~/billing/integralRule/updateStatus",params,function(data){
                if(data){
                    LS.message("info","停用成功!");
                    query();
                }else{
                    LS.message("info",data.msg);
                }
            });
        }
        
        function getRuleInfo(){
        	var item = ruleGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
             LS.dialog("~/billing/integralRule/getRuleInfo?ruleId="+item.ruleId,"积分规则详情",950, 550,true);
        }

        function setClearTime(){
            LS.dialog("~/billing/integralRule/setClearTime","设置积分清零时间",500,300,true, null);
        }
        
         function clearAll(){
            ruleForm.clear();
        };
        
        window.onload = function() {
            initGridHeight('ruleForm','ruleGrid');
        }
	</ls:script>
</ls:body>
</html>