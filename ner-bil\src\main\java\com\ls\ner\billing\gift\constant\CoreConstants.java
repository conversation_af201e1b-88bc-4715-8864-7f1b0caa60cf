package com.ls.ner.billing.gift.constant;

import com.ls.ner.billing.gift.util.CodeMessage;

public class CoreConstants {
    public static final CodeMessage RETURN_CODE_SUCCESS = new CodeMessage("0", "private ???");
    public static final CodeMessage RETURN_CODE_UNKNOWN_ERROR = new CodeMessage("SYS59999", "δ???");

    public CoreConstants() {
    }

    /**
     * ??????null
     */
    public static final String REQUEST_ERROR_CODE = "-100";
    /**
     * code?????
     */
    public static final String CODE_ERROR  = "-200";
    /**
     * token?null
     */
    public  static final String ACCESSTONKEN_ERROR ="-300";
    /**
     * ??????
     */
    public static final String REQUEST_ERROR_PARAMS = "-1";

}

