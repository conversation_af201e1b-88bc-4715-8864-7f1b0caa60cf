package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.ItemPeriodsConfigBo;
import com.ls.ner.billing.common.bo.ItemPeriodsConfigBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ItemPeriodsConfigBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(ItemPeriodsConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(ItemPeriodsConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(ItemPeriodsConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(ItemPeriodsConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<ItemPeriodsConfigBo> selectByExample(ItemPeriodsConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    ItemPeriodsConfigBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") ItemPeriodsConfigBo record, @Param("example") ItemPeriodsConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") ItemPeriodsConfigBo record, @Param("example") ItemPeriodsConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(ItemPeriodsConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_item_periods_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(ItemPeriodsConfigBo record);
}