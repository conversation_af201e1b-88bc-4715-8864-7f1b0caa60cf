/**
 *
 * @(#) ModelsBillServiceImpl.java
 * @Package com.ls.ner.billing.models.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.models.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ls.ner.billing.models.bo.ModelsBillBo;
import com.ls.ner.billing.models.dao.IModelsBillDao;
import com.ls.ner.billing.models.service.IModelsBillService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;

/**
 *  Description : 
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月28日 下午4:38:11   qixiyu   Created.
 *           
 */
@Component
public class ModelsBillServiceImpl implements IModelsBillService{
	@Autowired(required = true)
	private IModelsBillDao dao;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;
	
	@Override
	public List<ModelsBillBo> getModelRentalList(ModelsBillBo bo) {
		List<ModelsBillBo> models = dao.getModelRentalList(bo); 
		return models;
	}

	@Override
	public int getModelRentalListNum(ModelsBillBo bo) {
		return dao.getModelRentalListNum(bo);
	}

	@Override
	public void saveModelRental(ModelsBillBo bo) {
		String[] modelsId = bo.getModelId().split(",");
		for (String modelId : modelsId) {
			ModelsBillBo newBo = new ModelsBillBo();
			newBo.setProdNo(seqRpcService.getDefNo());
			newBo.setModelId(modelId);
			newBo.setOrgCode(bo.getOrgCode());
			newBo.setSubBe(bo.getSubBe());
			dao.saveModelRental(newBo);
		}
	}

	@Override
	public void delModelRentals(String[] ids) {
		for (String id : ids) {
			this.delModelRental(id);
		}
	}

	private void delModelRental(String id) {
		dao.delModelRental(id);
	}

}
