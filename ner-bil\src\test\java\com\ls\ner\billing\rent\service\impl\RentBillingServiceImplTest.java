package com.ls.ner.billing.rent.service.impl;

import static org.junit.Assert.*;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Test;

public class RentBillingServiceImplTest {

	@AfterClass
	public static void tearDownAfterClass() throws Exception {
	}

	@Before
	public void setUp() throws Exception {
	}

	@After
	public void tearDown() throws Exception {
	}

	@Test
	public void testLocateBillingConfigBo() {
//		fail("Not yet implemented");
	}

	@Test
	public void testLocateBillingConfigBoMap() {
//		System.out.println(new DateTime().toString("yyyyMMdd"));
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String today = sdf.format(new Date());
//		System.out.println(today);
	}

}
