/**
 *
 * @(#) ITariffDao.java
 * @Package com.ls.ner.billing.tariff.dao
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.dao;

import java.util.List;

import com.ls.ner.billing.tariff.bo.AttachItemBo;
import com.ls.ner.billing.tariff.bo.TariffPlanBo;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: ITariffDao.java,v 1.1 2016/02/26 02:34:01 34544 Exp $
 * 
 *          History: 2016年2月23日 下午5:33:48 lipf Created.
 * 
 */
public interface ITariffDao {

	/**
	 * 
	 * 方法说明：
	 * 
	 * Author： lipf Create Date： 2016年2月23日 下午5:33:56 History: 2016年2月23日
	 * 下午5:33:56 lipf Created.
	 * 
	 * @param attachItemBo
	 * 
	 */
	void saveAttachItem(AttachItemBo attachItemBo);

	/**
	 * 
	 * 方法说明：
	 * 
	 * Author： lipf Create Date： 2016年2月23日 下午5:34:03 History: 2016年2月23日
	 * 下午5:34:03 lipf Created.
	 * 
	 * @param formBo
	 * 
	 */
	void saveTariff(TariffPlanBo formBo);

	/**
	 * 
	 * 方法说明：
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月23日 下午5:40:02
	 * History:  2016年2月23日 下午5:40:02   lipf   Created.
	 *
	 * @param formBo
	 *
	 */
	void saveMainItem(TariffPlanBo formBo);

	/**
	 * 
	 * 方法说明：资费查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:58:22
	 * History:  2016年2月24日 上午9:58:22   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	List<TariffPlanBo> getTariff(TariffPlanBo bo);

	/**
	 * 
	 * 方法说明：资费数目查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:58:34
	 * History:  2016年2月24日 上午9:58:34   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	int getTariffCount(TariffPlanBo bo);

}
