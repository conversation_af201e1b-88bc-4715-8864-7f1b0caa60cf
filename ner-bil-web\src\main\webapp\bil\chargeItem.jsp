<%@ page import="org.owasp.esapi.ESAPI" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String flag = String.valueOf(request.getAttribute("flag"));
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="费用项定义" />
<ls:body>
		<table align="center" class="tab_search">
				<tr>
						<td>
								<ls:grid url="" name="chargeItemGrid" height="150" width="100%" showCheckBox="true">
										<ls:column name="itemId" caption="" hidden="true" />
										<ls:column name="chargeItemCode" caption="费用项编码" />
										<ls:column name="itemName" caption="费用项名称" />
										<ls:column name="chargeTypeName" caption="费用类别" />
										<ls:column name="timeLineName" caption="时效类型" />
										<ls:column name="needFlagName" caption="是否必须" />
										<ls:pager pageSize="5,10,15"></ls:pager>
								</ls:grid>
						</td>
				</tr>
		</table>
		<%
			if (flag != null && flag.equals("1")) {
		%>
		<table>
				<tr>
						<td>
								<div class="pull-right">
										<ls:button text="确认" onclick="doSure" />
										<ls:button text="返回" onclick="closeWin" />
								</div>
						</td>
				</tr>
		</table>
		<%
			} else {
		%>
		<ls:form id="chargeItemForm" name="chargeItemForm">
				<table class="tab_search">
						<ls:text name="itemId" property="itemId" type="hidden"></ls:text>
						<tr>
								<td>
										<ls:label text="费用项编码" ref="chargeItemCode" />
								</td>
								<td>
										<ls:text name="chargeItemCode" property="chargeItemCode" required="true"></ls:text>
								</td>
								<td>
										<ls:label text="费用项名称" ref="itemName" />
								</td>
								<td>
										<ls:text name="itemName" property="itemName" required="true"></ls:text>
								</td>
								<td>
										<ls:label text="费用类型" ref="chargeType" />
								</td>
								<td>
										<ls:select name="chargeType" property="chargeType" required="true">
												<ls:options property="chargeTypeList" scope="request" text="codeName" value="codeValue"/>
										</ls:select>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="时效类型" ref="timeLine" />
								</td>
								<td>
										<ls:select name="timeLine" property="timeLine" required="true">
												<ls:options property="timeLineList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
								<td>
										<ls:label text="是否必须" ref="needFlag" />
								</td>
								<td>
										<ls:select name="needFlag" property="needFlag" required="true">
												<ls:options property="ynJudgeFlagList" scope="request" text="codeName" value="codeValue"/>
										</ls:select>
								</td>
						</tr>
						<tr>
								<td>
										<ls:label text="费用项描述" />
								</td>
								<td colspan="7">
										<ls:text type="textarea" name="itemDesc" property="itemDesc"></ls:text>
								</td>
						</tr>
				</table>
				<table>
						<tr>
								<td>
										<div class="pull-right">
												<ls:button text="新增" onclick="doAdd" />
												<ls:button text="清除" onclick="doClear" />
												<ls:button text="保存" onclick="doSave" />
												<ls:button text="删除" onclick="doDel" />
										</div>
								</td>
						</tr>
				</table>
		</ls:form>
		<%
			}
		%>
		<ls:script>
    query();
    //此页面已经没在使用
    function query(){
      var queryParam={
      	chargeType:'<%=ESAPI.encoder().encodeForJavaScript(request.getParameter("chargeType")) %>'
      };
	  if(queryParam.chargeType == 'null') {
			queryParam.chargeType = null;
		}
      chargeItemGrid.query("~/billing/chargeItem/getChargeItemQuery",queryParam);
    };
    function doAdd(){  
      doClear();
      itemId.setValue();
    }
    function doClear(){
      chargeItemForm.clear();
    };
    function doSave(){
      chargeItemForm.submit("~/billing/chargeItem/saveOrUpdate",function(e){
        if(e.items[0].msg=="saveSucc"){
          itemId.setValue(e.items[0].itemId);
          LS.message("info","保存成功");
          query();
        }else if(e.items[0].msg=="updateSucc"){
          LS.message("info","更新成功");  
          query();        
        }else{
          LS.message("info","保存失败:"+e.items[0].msg);
        }
      });
    }
    function doDel(){      
      var item = chargeItemGrid.getSelectedItem();
      if(item!=null){
        LS.confirm('确认删除吗?', function(data) {
        if (data) {
          LS.ajax('~/billing/chargeItem/deleteChargeItem?id='+item.itemId,{},function(e){
             if (e.items[0]=="Y") {                  
                LS.message("info", "删除成功！");
                query();
                doAdd();
             } else {
                LS.message("error", e.items[0]);
             }
            });
          }
        })
      }else{
        LS.message("info","请选择一条记录!");
      }
    }
    chargeItemGrid.itemclick = function(){
      if(<%=flag%>!="1"){
	      var item = chargeItemGrid.getSelectedItem();
	      itemId.setValue(item.getValue("itemId"));
        itemName.setValue(item.getValue("itemName"));
	      chargeType.setValue(item.getValue("chargeType"));
	      needFlag.setValue(item.getValue("needFlag"));
	      itemDesc.setValue(item.getValue("itemDesc"));
	      timeLine.setValue(item.getValue("timeLine"));
	      chargeItemCode.setValue(item.getValue("chargeItemCode"));
      }
    }
    function closeWin(){
      LS.window.close();
    }
    function doSure(){      
      var item = chargeItemGrid.getCheckedItems();
      if(item!=null){
        LS.parent().setItem(item);
        setTimeout("LS.window.close()",300);
      }else{
        LS.message("info","请选择一条记录!");
      }
    }
    </ls:script>
</ls:body>
</html>