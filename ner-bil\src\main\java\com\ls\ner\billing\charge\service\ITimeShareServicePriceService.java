package com.ls.ner.billing.charge.service;

import com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;

import java.util.List;
import java.util.Map;

/**
 * @Description 分时服务处理接口
 * <AUTHOR>
 * @Date 2022/5/17 15:56
 */
public interface ITimeShareServicePriceService {

    /**
     * <AUTHOR>
     * @throws Exception
     * @dateTime 2016-11-30
     * @description *******	RPC05-02-02查询充电定价
     */
    List<Map<String, Object>> getChargePrice(Map<String,Object> inMap) throws Exception;

     /**
      * @Description 判断分时服务费是否开启
      * <AUTHOR>
      * @Date 2022/5/17 16:38
      */
     Boolean getServicePriceSwitch();

     /**
      * @Description 初始化itemchargemode,兼容新旧版本计费
      * <AUTHOR>
      * @Date 2022/5/17 16:58 
      * @param cbcBo 
      */
    void initItemChargeModeConfBo(ChargeBillingConfBo cbcBo);

    /**
     * @Description 初始化itemchargemode,兼容新旧版本计费
     * <AUTHOR>
     * @Date 2022/5/19 10:48
     * @param cbrBo
     */
    void initItemChargeModeRltBo(ChargeBillingRltBo cbrBo);

    /**
     * <AUTHOR>
     * @dateTime 2016-11-23
     * @description *******	RPC05-02-25充电计费 – 预估
     */
    Map<String, Object> estimate(Map<String, Object> inMap)throws Exception;

    /**
     * <AUTHOR>
     * @dateTime 2016-11-25
     * @description *******	RPC05-02-24充电计费 – 预收/计费版本
     */
    Map<String, Object> payInAdvance(Map<String, Object> inMap)throws Exception;

    /**
     * <AUTHOR>
     * @dateTime 2016-11-25
     * @description *******	RPC05-02-26充电计费 – 结算
     */
    Map<String, Object> settlement(Map<String, Object> inMap)throws Exception;

    /**
     * @Description 查询分时费用明细
     * <AUTHOR>
     * @Date 2022/5/19 11:06
     * @param map
     */
    List<BillRltPeriodsBo> getChargeBillRltPeriod(Map map);

    /**
     * 查询计费描述
     * @param inMap
     * @return
     * @throws Exception
     */
    List<Map<String, Object>> getNewElecAmtRemark(Map<String, Object> inMap) throws Exception;

    /**
     * @param inMap
     * @description 查询当前时间 充电费+服务费/充电费分时列表+其他费列表
     * <AUTHOR>
     * @create 2018-05-21 17:35:04
     */
    List<Map<String, Object>> getCurChargePriceRemark(Map<String,Object> inMap) throws Exception;

    /**
     * @param inMap
     * @description 个性化计费查询-小鹏使用
     * <AUTHOR>
     * @create 2018-07-25 17:15:22
     */
    List<Map<String, Object>> getXpChargePriceRemark(Map<String, Object> inMap) throws Exception;

    /**
     * 将数据组合成下发到车联网的格式
     * */
    List<String> combinateFormat(List<Map<String,String>> piles,ChargeBillingConfBo cbcBo,String dataSource);
}
