package com.ls.ner.billing.charge.bo;

import java.util.List;

import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.DicAttribute;


/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 充电计费配置  E_CHARGE_BILLING_CONF
 */
public class ChargeBillingConfBo {
	
	private String systemId;//主键id
	private String orgCode;//管理单位
	private String stationId;//本实体记录的唯一标识，产生规则为流水号
	private String pileId;//本实体记录的唯一标识，产生规则为流水号
	private String chcNo;//充电计费编号
	private String chcName;//充电计费名称
	private String chcRemark;//充电计费说明
	private String chargeMode;//计费模式 01标准 02分时
	private String chargeMethod;//收费方式：01预付费 02后付费
	private String periodSplitNum;//分时阶数
	private String attachItemNos;//可选其他服务项目编号，逗号隔开
	private String attachItemPrices;//服务项目单价，逗号隔开
	private String depositItemNos;//押金项目编号，逗号隔
	private String depositItemPrices;//押金项目单价，逗号隔
	private String chargeNum;//最小计费数
	private String chargeUnit;//计费单位：01kWh
	private String chargePrice;//计费单价
	private String operNo;//发布人员
	private String createTime;//发布时间
	private String chcStatus;//状态，1有效、2无效、3草稿
	private String stationName;//充电站名称
	private String chargePeriodsStr;//充电分时设置数据
	private String billCtlMode;//费控模式，1本地 2远程
	private String gatherDataType;//采集数据类型，1表示数 2电量 3量价费
	private String eftDate;//生效日期
	private String invDate;//失效日期
	private String freeNum;//免费段，逗号隔开，空或是0表示没有免费段
	private String upChcNo;//上个版本
	private String pileName;//桩名称
	private String pileNo;//桩编号
	private String upChcName;
	private String sendStatus;
	private String endEftDate;
	private String custId;//用户id
	private String custName;//用户名称
	private String rangeFlag;//站点范围 1 全部 0 部分
	private String stationIdStr;//站点ID
	private String billingTag;//01通用计费 02直流桩计费 03交流桩计费
	private String sendTime;

	private String serviceMode; //服务费计费模式  01标准，02分时
	private String itemChargeMode;//费用项分时标志

	private String billingConfType;  //计费类型 1.企业计费

	/**
	 * 是否关联尖峰计费
	 */
	private String isRelPeak;

	/**
	 * 尖峰电价生效时间
	 */
	private String peakTime;

	/**
	 * 操作时间
	 */
	private String dataOperTime;

	public String getDataOperTime() {
		return dataOperTime;
	}

	public void setDataOperTime(String dataOperTime) {
		this.dataOperTime = dataOperTime;
	}

	public String getPeakTime() {
		return peakTime;
	}

	public void setPeakTime(String peakTime) {
		this.peakTime = peakTime;
	}

	public String getIsRelPeak() {
		return isRelPeak;
	}

	public void setIsRelPeak(String isRelPeak) {
		this.isRelPeak = isRelPeak;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getEndEftDate() {
		return endEftDate;
	}
	public void setEndEftDate(String endEftDate) {
		this.endEftDate = endEftDate;
	}

	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}


	public String getUpChcName() {
		return upChcName;
	}
	public void setUpChcName(String upChcName) {
		this.upChcName = upChcName;
	}

	public String getPileName() {
		return pileName;
	}
	public void setPileName(String pileName) {
		this.pileName = pileName;
	}
	public String getPileNo() {
		return pileNo;
	}
	public void setPileNo(String pileNo) {
		this.pileNo = pileNo;
	}

	private String orgCodeName;//管理单位名称
	@DicAttribute(dicName = "codeDict", key = "gatherDataType", subType = BizConstants.CodeType.GATHER_DATA_TYPE)
	private String gatherDataTypeName;//采集数据类型名称
	@DicAttribute(dicName = "codeDict", key = "billCtlMode", subType = BizConstants.CodeType.BILL_CTL_MODE)
	private String billCtlModeName;//费控模式名称
	@DicAttribute(dicName = "codeDict", key = "chargeMode", subType = BizConstants.CodeType.CHC_BILLING_CHARGE_MODE)
	private String chargeModeName;//计费模式名称
	@DicAttribute(dicName = "codeDict", key = "chcStatus", subType = BizConstants.CodeType.VALID_FLAG)
	private String chcStatusName;//状态名称，01有效、02无效、03草稿
	
	private List<ChargePeriodsBo> cperiods;//分时记录
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	public String getPileId() {
		return pileId;
	}
	public void setPileId(String pileId) {
		this.pileId = pileId;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getChcName() {
		return chcName;
	}
	public void setChcName(String chcName) {
		this.chcName = chcName;
	}
	public String getChcRemark() {
		return chcRemark;
	}
	public void setChcRemark(String chcRemark) {
		this.chcRemark = chcRemark;
	}
	public String getChargeMode() {
		return chargeMode;
	}
	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}
	public String getChargeMethod() {
		return chargeMethod;
	}
	public void setChargeMethod(String chargeMethod) {
		this.chargeMethod = chargeMethod;
	}
	public String getPeriodSplitNum() {
		return periodSplitNum;
	}
	public void setPeriodSplitNum(String periodSplitNum) {
		this.periodSplitNum = periodSplitNum;
	}
	public String getAttachItemNos() {
		return attachItemNos;
	}
	public void setAttachItemNos(String attachItemNos) {
		this.attachItemNos = attachItemNos;
	}
	public String getAttachItemPrices() {
		return attachItemPrices;
	}
	public void setAttachItemPrices(String attachItemPrices) {
		this.attachItemPrices = attachItemPrices;
	}
	public String getDepositItemNos() {
		return depositItemNos;
	}
	public void setDepositItemNos(String depositItemNos) {
		this.depositItemNos = depositItemNos;
	}
	public String getDepositItemPrices() {
		return depositItemPrices;
	}
	public void setDepositItemPrices(String depositItemPrices) {
		this.depositItemPrices = depositItemPrices;
	}
	public String getChargeNum() {
		return chargeNum;
	}
	public void setChargeNum(String chargeNum) {
		this.chargeNum = chargeNum;
	}
	public String getChargeUnit() {
		return chargeUnit;
	}
	public void setChargeUnit(String chargeUnit) {
		this.chargeUnit = chargeUnit;
	}
	public String getChargePrice() {
		return chargePrice;
	}
	public void setChargePrice(String chargePrice) {
		this.chargePrice = chargePrice;
	}
	public String getOperNo() {
		return operNo;
	}
	public void setOperNo(String operNo) {
		this.operNo = operNo;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getChcStatus() {
		return chcStatus;
	}
	public void setChcStatus(String chcStatus) {
		this.chcStatus = chcStatus;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getChargeModeName() {
		return chargeModeName;
	}
	public void setChargeModeName(String chargeModeName) {
		this.chargeModeName = chargeModeName;
	}
	public String getChcStatusName() {
		return chcStatusName;
	}
	public void setChcStatusName(String chcStatusName) {
		this.chcStatusName = chcStatusName;
	}
	public String getChargePeriodsStr() {
		return chargePeriodsStr;
	}
	public void setChargePeriodsStr(String chargePeriodsStr) {
		this.chargePeriodsStr = chargePeriodsStr;
	}
	public String getBillCtlMode() {
		return billCtlMode;
	}
	public void setBillCtlMode(String billCtlMode) {
		this.billCtlMode = billCtlMode;
	}
	public String getGatherDataType() {
		return gatherDataType;
	}
	public void setGatherDataType(String gatherDataType) {
		this.gatherDataType = gatherDataType;
	}
	public String getEftDate() {
		return eftDate;
	}
	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}
	public String getInvDate() {
		return invDate;
	}
	public void setInvDate(String invDate) {
		this.invDate = invDate;
	}
	public String getGatherDataTypeName() {
		return gatherDataTypeName;
	}
	public void setGatherDataTypeName(String gatherDataTypeName) {
		this.gatherDataTypeName = gatherDataTypeName;
	}
	public String getBillCtlModeName() {
		return billCtlModeName;
	}
	public void setBillCtlModeName(String billCtlModeName) {
		this.billCtlModeName = billCtlModeName;
	}
	public List<ChargePeriodsBo> getCperiods() {
		return cperiods;
	}
	public void setCperiods(List<ChargePeriodsBo> cperiods) {
		this.cperiods = cperiods;
	}
	public String getFreeNum() {
		return freeNum;
	}
	public void setFreeNum(String freeNum) {
		this.freeNum = freeNum;
	}
	public String getUpChcNo() {
		return upChcNo;
	}
	public void setUpChcNo(String upChcNo) {
		this.upChcNo = upChcNo;
	}

	public String getRangeFlag() {
		return rangeFlag;
	}

	public void setRangeFlag(String rangeFlag) {
		this.rangeFlag = rangeFlag;
	}

	public String getStationIdStr() {
		return stationIdStr;
	}

	public void setStationIdStr(String stationIdStr) {
		this.stationIdStr = stationIdStr;
	}

	public String getBillingTag() {
		return billingTag;
	}

	public void setBillingTag(String billingTag) {
		this.billingTag = billingTag;
	}

	public String getSendTime() {
		return sendTime;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}

	public String getServiceMode() {
		return serviceMode;
	}

	public void setServiceMode(String serviceMode) {
		this.serviceMode = serviceMode;
	}

	public String getItemChargeMode() {
		return itemChargeMode;
	}

	public void setItemChargeMode(String itemChargeMode) {
		this.itemChargeMode = itemChargeMode;
	}

	public String getBillingConfType() {
		return billingConfType;
	}

	public void setBillingConfType(String billingConfType) {
		this.billingConfType = billingConfType;
	}
}