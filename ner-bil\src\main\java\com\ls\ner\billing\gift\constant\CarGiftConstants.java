package com.ls.ner.billing.gift.constant;
/**
 * <AUTHOR>
 * @description CarGiftConstants
 * @create 2020-04-30 9:50
 */
public class CarGiftConstants {

    /**
     *  购车送礼金领取状态
     * */
    public static final class carGiftGetStatus{
        public static final String NAME = "carGiftGetStatus";
        /** 未申请 **/
        public static final String NOT_APPLIED = "01";
        /** 待审核 **/
        public static final String PENDING_REVIEW = "02";
        /** 审核通过 **/
        public static final String PASS = "03";
        /** 已发放 **/
        public static final String ISSUED = "04";
        /** 已核销 **/
        public static final String WRITTEN_OFF = "05";
        /** 发放失败 **/
        public static final String ISSUANCE_FAILED = "06";
    }

    /**
     *  是否营运车辆(暂时无用)
     * */
    public static final class giftCarType{
        public static final String NAME = "giftCarType";
        /** 是 **/
        public static final String YN = "1";
        /** 否 **/
        public static final String NO = "0";
    }

    /**
     *  消息模板编码
     * */
    public static final class templateCode{
        public static final String NAME = "templateCode";

        /** 购车送礼金发放成功通知 **/
        public static final String PUSH_SUCCEED_NOTICE = "C0101";

        /** 购车送礼金发放失败通知 **/
        public static final String PUSH_FAIL_NOTICE = "C0102";

        /** 平台直充购车送礼金通知 **/
        public static final String PLATFORM_PUSH_SUCCEED_NOTICE = "C0103";
    }
}
