package com.ls.ner.billing.api.market.service;

import com.ls.ner.billing.api.integral.IntegralRuleDTO;
import com.ls.ner.billing.api.integral.MemberBenefitsDTO;
import com.ls.ner.billing.api.market.vo.IntegralCustLogVo;

import java.util.List;
import java.util.Map;

public interface IIntegeralCustRPCService {

	/**
	 * 更新用户积分并记录积分流水
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> saveIntegralCustInfo(Map<String, Object> map) throws Exception;

	/**
	 * 查询是否为双倍权益
	 * @param
	 * @return
	 * @throws Exception
	 */
	public MemberBenefitsDTO userMemberBenefit(Long custId) ;

	/**
	 * 批量获取用户积分
	 *
	 * @param custIdList
	 * @return
	 * @throws Exception
	 */
	public Map<String, Map<String, String>> getCustIntegralNumber(List<String> custIdList);


	/**
	 * 获取用户积分
	 *
	 * @param custId
	 * @return
	 */
	public String getCustIntegralNumberById(String custId);

	/**
	 * 获取用户积分流水记录列表
	 * @param queryVo
	 * @return
	 */
	public Map getIntegralCustLog(IntegralCustLogVo queryVo);

	/**
	 * 获取积分任务完成情况
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getIntegralTask(String mobile) throws Exception;

	/**
	 * 获取用户会员权益
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getBenefitsUser(String custId);

	/**
	 * 获取积分配置
	 * <AUTHOR>
	 * @date 2025/1/14
	 */
	List<Map<String, Object>> getIntegralRule();


	/**
	 * 获取积分配置
	 * <AUTHOR>
	 * @date 2025/1/14
	 */
	List<IntegralRuleDTO> getIntegralRule2();

}
