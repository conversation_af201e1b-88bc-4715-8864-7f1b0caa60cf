package com.ls.ner.billing.vip.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ls.ner.aly.api.IInvoiceAlyRpcService;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.vip.bo.*;
import com.ls.ner.billing.api.vip.service.IVipRPCService;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.vo.ExclusiveOfferVo;
import com.ls.ner.billing.vip.bo.*;
import com.ls.ner.billing.vip.condition.VipRecordCondition;
import com.ls.ner.billing.vip.condition.VipRecordPageCondition;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.dao.IVipExclusivePutDao;
import com.ls.ner.billing.vip.dao.IVipSaveRecordDao;
import com.ls.ner.billing.vip.service.IVipLevelCalcConfigService;
import com.ls.ner.billing.vip.service.IVipLevelService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Service(target = {ServiceType.RPC}, value = "vipRPCService")
public class VipRPCServiceImpl implements IVipRPCService {

    @Autowired
    private IMemberBenefitsDao memberBenefitsDao;

    @Autowired
    private IVipSaveRecordDao saveRecordDao;

    @Autowired
    private IVipExclusivePutDao exclusivePutDao;

    @ServiceAutowired(value = "vipLevelCalcConfigService", serviceTypes = ServiceType.LOCAL)
    private IVipLevelCalcConfigService configService;

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "vipLevelService")
    private IVipLevelService vipLevelService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="invoiceAlyRpcService")
    private IInvoiceAlyRpcService iInvoiceAlyRpcService;

    private static final Logger logger = LoggerFactory.getLogger(VipRPCServiceImpl.class);

    /**
     * 会员权益说明
     * @return
     */
    @Override
    public Map configDesc(String vipLevel) throws JsonProcessingException {
        Map<String, Object> resultMap = new HashMap<>();
        MemberBenefitsBo bo = new MemberBenefitsBo();
        bo.setVipLevel(vipLevel);
        MemberBenefitsBo memberBenefitsBo = memberBenefitsDao.configDesc(bo);
        resultMap.put("vipConfig",memberBenefitsBo);
        BigDecimal memberStation = memberBenefitsBo.getMemberStation().divide(BigDecimal.TEN);
        ObjectMapper objectMapper = new ObjectMapper();
        List<CouponBo> couponBos = new ArrayList<>();
        List<ExclusiveOfferVo> coupons = new ArrayList<>();
        if (StringUtil.isNotEmpty(memberBenefitsBo.getExclusiveOffer())&&!memberBenefitsBo.getExclusiveOffer().equals("[]")){
            coupons = objectMapper.readValue(memberBenefitsBo.getExclusiveOffer(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
            couponBos = memberBenefitsDao.queryCpnById(coupons);
            Map<String, String> collect = coupons.stream()
                    .collect(Collectors.toMap(
                            ExclusiveOfferVo::getCouponId,
                            ExclusiveOfferVo::getCouponNum
                    ));
            for (CouponBo couponBo : couponBos) {
                couponBo.setLimGetNum(collect.get(couponBo.getCpnId()));
            }
        }
        if (StringUtil.isNotEmpty(memberBenefitsBo.getWelcomeGift())&&!memberBenefitsBo.getWelcomeGift().equals("[]")){
            List<ExclusiveOfferVo> giftCoupons = objectMapper.readValue(memberBenefitsBo.getWelcomeGift(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
            coupons.addAll(giftCoupons);

            List<CouponBo> gifts = memberBenefitsDao.queryCpnById(giftCoupons);
            Map<String, String> collect = giftCoupons.stream()
                    .collect(Collectors.toMap(
                            ExclusiveOfferVo::getCouponId,
                            ExclusiveOfferVo::getCouponNum
                    ));
            for (CouponBo couponBo : gifts) {
                couponBo.setLimGetNum(collect.get(couponBo.getCpnId()));
                couponBos.add(couponBo);
            }
        }
        //默认30度
        String vipDegrees = sysParamService.getSysParamsValues("vipDegrees");
        BigDecimal degrees = new BigDecimal("30");
        if (StringUtil.isNotEmpty(vipDegrees)){
            degrees = new BigDecimal(vipDegrees);
        }
        //会员默认服务费配置
        String vipServiceAmt = sysParamService.getSysParamsValues("vipServiceAmt");
        String vipChargeAmt = sysParamService.getSysParamsValues("vipChargeAmt");
        BigDecimal sysServiceAmt = BigDecimal.ZERO;
        BigDecimal sysChargeAmt = BigDecimal.ZERO;
        if (StringUtil.isEmpty(vipServiceAmt)){
            sysServiceAmt = new BigDecimal("1");
        }else {
            sysServiceAmt = new BigDecimal(vipServiceAmt);
        }
        if (StringUtil.isEmpty(vipChargeAmt)){
            sysChargeAmt = new BigDecimal("1");
        }else {
            sysChargeAmt = new BigDecimal(vipChargeAmt);
        }
        //会员月省度数配置
        String vipCharge = sysParamService.getSysParamsValues("vipCharge");
        BigDecimal sysVipCharge = BigDecimal.ZERO;
        if (StringUtil.isEmpty(vipServiceAmt)){
            sysVipCharge = new BigDecimal("1500");
        }else {
            sysVipCharge = new BigDecimal(vipCharge);
        }
        BigDecimal chargeFrequency = sysVipCharge.divide(degrees, 0, RoundingMode.DOWN);
        //应收服务费
        BigDecimal serviceAmt = sysServiceAmt.multiply(degrees);
        //打折后服务费
        BigDecimal disServiceAmt = serviceAmt.multiply(memberStation);
        Integer vipStationLimit = 9999;
        //会员场站限制
        if(memberBenefitsBo.getDiscountLimit().equals("1")){
            if (memberBenefitsBo.getLimitAmt()!=null&&memberBenefitsBo.getLimitAmt().compareTo(disServiceAmt)<0){
                disServiceAmt = memberBenefitsBo.getLimitAmt();
            }
            if (memberBenefitsBo.getLimitNum()!=null){
                vipStationLimit = memberBenefitsBo.getLimitNum();
            }
        }
        Integer limit = 0;
        BigDecimal chargeAmt = sysChargeAmt.multiply(degrees);
        //发放数量
        BigDecimal count = BigDecimal.ZERO;
        //可省金额
        BigDecimal amt = BigDecimal.ZERO;
        List<BigDecimal> maxAmtList = new ArrayList<>();
        if (coupons.size()>0){
            List<CouponBo> cashCouponList = new ArrayList<>();
            List<CouponBo> discountCouponList = new ArrayList<>();
            for (CouponBo couponBo : couponBos) {
                couponBo.setCpnNum(couponBo.getLimGetNum());
                if (couponBo.getCpnType().equals("01")){
                    cashCouponList.add(couponBo);
                }else {
                    discountCouponList.add(couponBo);
                }
            }
            Map<String, Optional<CouponBo>> maxCpn = couponBos.stream()
                    .collect(Collectors.groupingBy(
                            CouponBo::getCpnType,
                            Collectors.maxBy(Comparator.comparing(CouponBo::getCpnAmt))
                    ));
            // 现金券
            CouponBo cashCoupon = maxCpn.getOrDefault("01", Optional.empty()).orElse(null);
            if(cashCoupon!=null){
                resultMap.put("cashCouponAmt",cashCoupon.getCpnAmt());
            }
            // 折扣券
//        CouponBo discountCoupon = maxCpn.getOrDefault("02", Optional.empty()).orElse(null);
            resultMap.put("cashCoupon",cashCouponList);
            resultMap.put("discountCoupon",discountCouponList);
            for (CouponBo coupon : couponBos) {
                limit ++;
                logger.debug("saveMoneyStrategy-coupon:{}", JSONObject.toJSONString(coupon));
                //金额
                BigDecimal cpnAmt = new BigDecimal(coupon.getCpnAmt());
                //发放数量
                BigDecimal cpnLimit = new BigDecimal(coupon.getLimGetNum());
                //现金券
                if (coupon.getCpnType().equals("01")) {
                    maxAmtList.add(cpnAmt);
                    if (serviceAmt.compareTo(cpnAmt) < 0) {
                        cpnAmt = serviceAmt;
                    }
                    //第一次计算
                    if (count.compareTo(BigDecimal.ZERO) == 0) {
                        if (chargeFrequency.compareTo(cpnLimit) > 0) {
                            amt = cpnLimit.multiply(cpnAmt);
                        } else {
                            amt = chargeFrequency.multiply(cpnAmt);
                        }
                        count = cpnLimit;
                    } else {
                        BigDecimal subtract = chargeFrequency.subtract(count);
                        //剩余次数大于发放数量 按发放数量计算
                        if (subtract.compareTo(cpnLimit) > 0) {
                            amt = amt.add(cpnLimit.multiply(cpnAmt));
                            count = count.add(cpnLimit);
                        } else {
                            //剩余次数大于发放数量 按剩余次数计算
                            amt = amt.add(subtract.multiply(cpnAmt));
                            count = count.add(subtract);
                        }
                    }
                    logger.debug("现金券-count:{},amt:{}", count,amt);
                } else if (coupon.getCpnType().equals("02")) {
                    cpnAmt = cpnAmt.divide(BigDecimal.TEN);
                    //折扣券
                    //用券后金额
                    BigDecimal afterCpn = BigDecimal.ZERO;
                    //折扣金额
                    BigDecimal finaAmt = BigDecimal.ZERO;
                    //0110：服务费、0100：整单金额
                    if ("0110".equals(coupon.getCpnDctType())){
                        if (limit>vipStationLimit){
                            finaAmt = serviceAmt.multiply(cpnAmt);
                        }else {
                            finaAmt = serviceAmt.subtract(disServiceAmt).multiply(cpnAmt);
                        }
                        //最高抵扣金额大于消费金额
                        if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(finaAmt)<0){
                            finaAmt = new BigDecimal(coupon.getMaximumDiscountAmt());
                        }
                    }else if ("0100".equals(coupon.getCpnDctType())){
                        if (limit>vipStationLimit){
                            afterCpn = chargeAmt.add(serviceAmt).multiply(cpnAmt);
                        }else {
                            afterCpn = chargeAmt.add(serviceAmt).subtract(disServiceAmt).multiply(cpnAmt);
                        }
                        if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(afterCpn)<0){
                            afterCpn = new BigDecimal(coupon.getMaximumDiscountAmt());
                        }
                        finaAmt = afterCpn;
                    }
                    maxAmtList.add(finaAmt);
                    if (count.compareTo(BigDecimal.ZERO) == 0) {
                        //第一次计算
                        if (chargeFrequency.compareTo(cpnLimit) > 0) {
                            amt = finaAmt.multiply(cpnLimit);
                        } else {
                            amt = finaAmt.multiply(chargeFrequency).add(amt);
                        }
                        count = cpnLimit;
                    } else {
                        BigDecimal subtract = chargeFrequency.subtract(count);
                        //剩余次数大于发放数量 按发放数量计算
                        if (subtract.compareTo(cpnLimit) > 0) {
                            amt = finaAmt.multiply(cpnLimit).add(amt);
                            count = count.add(cpnLimit);
                        } else {
                            //剩余次数大于发放数量 按剩余次数计算
                            amt = finaAmt.multiply(subtract).add(amt);
                            count = count.add(subtract);
                        }
                    }
                    logger.debug("折扣券-count:{},amt:{}", count,amt);
                }
            }
        }
        //优惠券使用完
        BigDecimal discount = BigDecimal.ZERO;
        if (limit<vipStationLimit){
            discount = serviceAmt.subtract(disServiceAmt);
        }
        amt = chargeFrequency.multiply(discount).add(amt);
        if (maxAmtList.size()>0){
            BigDecimal bigDecimal = maxAmtList.stream()
                    .max(BigDecimal::compareTo).get();
            resultMap.put("maxCoupon",bigDecimal);
        }
        //最高月省
        resultMap.put("maxAmt",amt);

        //会员场站
        if(memberBenefitsBo.getIsMemberStation().equals("1")&&memberBenefitsBo.getMemberStation()!=null){
            resultMap.put("memberStation",memberBenefitsBo.getMemberStation());
        }

        return resultMap;
    }

    @Override
    public List<RegularVipRankRPCBo> getRegularVipConfigList() {
        List<RegularVipRankRPCBo> list = new ArrayList<>();
        List<RegularVipRankBo> regularVipConfigList = memberBenefitsDao.selectRegularVipConfigList();
        for (RegularVipRankBo regularVipRankBo : regularVipConfigList) {
            RegularVipRankRPCBo bo = new RegularVipRankRPCBo();
            bo.setRankId(regularVipRankBo.getRankId());
            bo.setRankNo(regularVipRankBo.getRankNo());
            bo.setRankName(regularVipRankBo.getRankName());
            bo.setRankMax(regularVipRankBo.getRankMax());
            bo.setRankMin(regularVipRankBo.getRankMin());
            bo.setRankAmtMax(regularVipRankBo.getRankAmtMax());
            bo.setRankAmtMin(regularVipRankBo.getRankAmtMin());
            list.add(bo);
        }
        return list;
    }

    @Override
    public List<VipPayRecordRPCBo> getVipPayRecord(Map<String, Object> inMap) {

        int pageNum = (int) inMap.get("pageNum");
        int totalNum = (int) inMap.get("totalNum");
        int start = (pageNum - 1) * totalNum;
        inMap.put("start", start);

        logger.debug("page-inMap:{}", JSONObject.toJSONString(inMap));
        List<VipPayRecordBo> recordList = memberBenefitsDao.getRecordListByMobile(inMap);
        logger.debug("page-result:{}", JSONObject.toJSONString(recordList));

        List<CodeBO> vipTypeList = codeService.getStandardCodes("vipType", null);

        List<VipPayRecordRPCBo> resultList = new ArrayList<>();
        for (VipPayRecordBo vipPayRecordBo : recordList) {
            VipPayRecordRPCBo result = new VipPayRecordRPCBo();
            result.setRecordId(vipPayRecordBo.getRecordId());
            result.setVipType(vipPayRecordBo.getVipType());
            if (vipPayRecordBo.getAmount().compareTo(BigDecimal.ZERO) >= 0) {
                result.setAmount(vipPayRecordBo.getAmount());
            }
            if (vipPayRecordBo.getIntegral().compareTo(BigDecimal.ZERO) >= 0) {
                result.setIntegral(vipPayRecordBo.getIntegral());
            }
            result.setPayTime(vipPayRecordBo.getPayTimeStr());
            if (ObjectUtil.isNotNull(vipPayRecordBo.getEffectTimeStr())) {
                result.setEffectTime(vipPayRecordBo.getEffectTimeStr());
            }
            if (ObjectUtil.isNotNull(vipPayRecordBo.getExpireTimeStr())) {
                result.setExpireTime(vipPayRecordBo.getExpireTimeStr());
            }
            for (CodeBO code : vipTypeList) {
                if (code.getCodeValue().equals(vipPayRecordBo.getVipType())) {
                    result.setVipTypeName(code.getCodeName());
                }
            }
            resultList.add(result);
        }

        return resultList;
    }

    @Override
    public Integer getRecordListByMobileCount(Map<String, Object> inMap) {
        String mobile = StringUtil.trimToEmpty(inMap.get("mobile"));
        VipRecordPageCondition condition = new VipRecordPageCondition();
        condition.setMobile(mobile);
        return memberBenefitsDao.getRecordListByMobileCount(condition);
    }

    @Override
    public List<VipRPCBo> getVipConfig() {
        List<VipBo> vipConfigList = memberBenefitsDao.getVipConfigList();

        List<CodeBO> vipTypeList = codeService.getStandardCodes("vipType", null);

        List<VipRPCBo> list = new ArrayList<>();
        for (VipBo vip : vipConfigList) {
            VipRPCBo result = new VipRPCBo();
            result.setVipId(vip.getVipId());
            result.setVipType(vip.getVipType());
            result.setVipPrice(vip.getVipPrice());
            result.setVipPriceNew(vip.getVipPriceNew());
            result.setVipPriceOld(vip.getVipPriceOld());
            result.setStatus(vip.getStatus());
            for (CodeBO code : vipTypeList) {
                if (code.getCodeValue().equals(vip.getVipType())) {
                    result.setVipTypeName(code.getCodeName());
                }
            }
            list.add(result);
        }

        return list;
    }

    @Override
    public void addRecord(VipPayRecordRPCBo record) {
        memberBenefitsDao.addRecord(record);
    }

    @Override
    public void updateRecord(VipPayRecordRPCBo record) {
        memberBenefitsDao.updateRecord(record);
    }

    @Override
    public VipPayRecordRPCBo getVipPayRecordById(String recordId) {
        VipPayRecordBo vipPayRecordById = memberBenefitsDao.getVipPayRecordById(recordId);
        if (vipPayRecordById == null) {
            return null;
        }
        VipPayRecordRPCBo result = new VipPayRecordRPCBo();
        result.setRecordId(vipPayRecordById.getRecordId());
        result.setMobile(vipPayRecordById.getMobile());
        result.setCustId(vipPayRecordById.getCustId());
        result.setVipType(vipPayRecordById.getVipType());
        result.setPayType(vipPayRecordById.getPayType());
        result.setAmount(vipPayRecordById.getAmount());
        result.setPayChannel(vipPayRecordById.getPayChannel());
        result.setPayTime(vipPayRecordById.getPayTimeStr());
        result.setEffectTime(vipPayRecordById.getEffectTimeStr());
        result.setExpireTime(vipPayRecordById.getExpireTimeStr());

        return result;
    }

    @Override
    public MemberBenefitsRPCBo getMemberBenefitByVip(MemberBenefitsRPCBo bo) {
        MemberBenefitsBo req = new MemberBenefitsBo();
        MemberBenefitsRPCBo res = new MemberBenefitsRPCBo();

        BeanUtils.copyProperties(bo, req);
        MemberBenefitsBo memberBenefits = memberBenefitsDao.getMemberBenefits(req);
        BeanUtils.copyProperties(memberBenefits, res);
        logger.debug("memberBenefits:{}", JSONObject.toJSONString(bo));
        logger.debug("memberBenefits:{}", JSONObject.toJSONString(memberBenefits));

        return res;
    }

    @Override
    public String getCustVipType(String custId) {
        VipPayRecordBo bo = memberBenefitsDao.getCustVipType(custId);
        if (bo != null) {
            return bo.getVipType();
        } else {
            return "";
        }
    }

    @Override
    public String updateVipLevel(VipRPCBo vipRPCBo) {

        return vipLevelService.updateVipLevel(vipRPCBo);
    }

    @Override
    public String getLevelFlag(VipRPCBo vipRPCBo) {
        return vipLevelService.getLevelFlag(vipRPCBo);
    }

    @Override
    public Map updateLevelFlag(VipRPCBo vipRPCBo) {
        return vipLevelService.updateLevelFlag(vipRPCBo);
    }

    @Override
    public void openGift(VipRPCBo vipRPCBo) {
        vipLevelService.openGift(vipRPCBo);
    }

    @Override
    public void exclusiveOffer(VipRPCBo vipRPCBo) {
        vipLevelService.exclusiveOffer(vipRPCBo);
    }

    @Override
    public void upgradeGift(VipRPCBo vipRPCBo) {
        vipLevelService.upgradeGift(vipRPCBo);
    }

    /**
     * 省钱攻略
     *
     * @return
     */
    @Override
    public Map saveMoneyStrategy(String custVipType) throws JsonProcessingException {
        Map<String, Object> resultMap = new HashedMap();
        List<SaveMoneyStrategyRPCBo> list = new ArrayList<>();
        MemberBenefitsRPCBo req = new MemberBenefitsRPCBo();
        req.setVipType("02");
        req.setVipLevel(custVipType);
        MemberBenefitsRPCBo memberBenefit = getMemberBenefitByVip(req);
        ObjectMapper objectMapper = new ObjectMapper();
        List<CouponBo> couponBos = new ArrayList<>();
        List<ExclusiveOfferVo> coupons = new ArrayList<>();
        if (StringUtil.isNotEmpty(memberBenefit.getExclusiveOffer())&&!memberBenefit.getExclusiveOffer().equals("[]")){
            coupons = objectMapper.readValue(memberBenefit.getExclusiveOffer(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
            couponBos = memberBenefitsDao.queryCpnById(coupons);
            Map<String, String> collect = coupons.stream()
                    .collect(Collectors.toMap(
                            ExclusiveOfferVo::getCouponId,
                            ExclusiveOfferVo::getCouponNum
                    ));
            for (CouponBo couponBo : couponBos) {
                couponBo.setLimGetNum(collect.get(couponBo.getCpnId()));
            }
        }
        if (StringUtil.isNotEmpty(memberBenefit.getWelcomeGift())&&!memberBenefit.getWelcomeGift().equals("[]")){
            List<ExclusiveOfferVo> giftCoupons = objectMapper.readValue(memberBenefit.getWelcomeGift(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
            coupons.addAll(giftCoupons);

            List<CouponBo> gifts = memberBenefitsDao.queryCpnById(giftCoupons);
            Map<String, String> collect = giftCoupons.stream()
                    .collect(Collectors.toMap(
                            ExclusiveOfferVo::getCouponId,
                            ExclusiveOfferVo::getCouponNum
                    ));
            for (CouponBo couponBo : gifts) {
                couponBo.setLimGetNum(collect.get(couponBo.getCpnId()));
                couponBos.add(couponBo);
            }
        }

        BigDecimal memberStation = memberBenefit.getMemberStation().divide(BigDecimal.TEN);
        //默认30度
        String vipDegrees = sysParamService.getSysParamsValues("vipDegrees");
        BigDecimal degrees = new BigDecimal("30");
        if (StringUtil.isNotEmpty(vipDegrees)){
            degrees = new BigDecimal(vipDegrees);
        }
        String vipServiceAmt = sysParamService.getSysParamsValues("vipServiceAmt");
        String vipChargeAmt = sysParamService.getSysParamsValues("vipChargeAmt");
        BigDecimal sysServiceAmt = BigDecimal.ZERO;
        BigDecimal sysChargeAmt = BigDecimal.ZERO;
        if (StringUtil.isEmpty(vipServiceAmt)){
            sysServiceAmt = new BigDecimal("1");
        }else {
            sysServiceAmt = new BigDecimal(vipServiceAmt);
        }
        if (StringUtil.isEmpty(vipChargeAmt)){
            sysChargeAmt = new BigDecimal("1");
        }else {
            sysChargeAmt = new BigDecimal(vipChargeAmt);
        }
        //月卡价格
        VipBo monthVipConfig = memberBenefitsDao.getMonthVipConfig();
        //应收服务费
        BigDecimal serviceAmt = sysServiceAmt.multiply(degrees);
        BigDecimal chargeAmt = sysChargeAmt.multiply(degrees);
        //打折后服务费
        BigDecimal disServiceAmt = serviceAmt.multiply(memberStation);
        //会员场站限制
        Integer vipStationLimit = 9999;
        //会员场站限制
        if(memberBenefit.getDiscountLimit().equals("1")){
            if (memberBenefit.getLimitAmt()!=null&&memberBenefit.getLimitAmt().compareTo(disServiceAmt)<0){
                disServiceAmt = memberBenefit.getLimitAmt();
            }
            if (memberBenefit.getLimitNum()!=null){
                vipStationLimit = memberBenefit.getLimitNum();
            }
        }
        if (StringUtil.isNotEmpty(memberBenefit.getChargeDegree1())) {
            saveMoney(list, memberBenefit.getChargeDegree1(), degrees);
        }
        if (StringUtil.isNotEmpty(memberBenefit.getChargeDegree2())) {
            saveMoney(list, memberBenefit.getChargeDegree2(), degrees);
        }
        if (StringUtil.isNotEmpty(memberBenefit.getChargeDegree3())) {
            saveMoney(list, memberBenefit.getChargeDegree3(), degrees);
        }
        if (StringUtil.isNotEmpty(memberBenefit.getChargeDegree4())) {
            saveMoney(list, memberBenefit.getChargeDegree4(), degrees);
        }
        if (StringUtil.isNotEmpty(memberBenefit.getChargeDegree5())) {
            saveMoney(list, memberBenefit.getChargeDegree5(), degrees);
        }
        logger.debug("saveMoneyStrategy-list:{}", JSONObject.toJSONString(list));

        if (list.size()>0){
            for (SaveMoneyStrategyRPCBo bo : list) {
                Integer limit = 0;
                BigDecimal chargeFrequency = new BigDecimal(bo.getChargeFrequency());
                //发放数量
                BigDecimal count = BigDecimal.ZERO;
                //可省金额
                BigDecimal amt = BigDecimal.ZERO;
                if (StringUtil.isNotEmpty(bo.getChargeFrequency()) && new BigDecimal(bo.getChargeFrequency()).compareTo(BigDecimal.ZERO) > 0&&couponBos.size()>0) {
                    for (CouponBo coupon : couponBos) {
                        limit ++;
                        logger.debug("saveMoneyStrategy-coupon:{}", JSONObject.toJSONString(coupon));
                        //折扣金额
                        BigDecimal cpnAmt = new BigDecimal(coupon.getCpnAmt());
                        //发放数量
                        BigDecimal cpnLimit = new BigDecimal(coupon.getLimGetNum());
                        //现金券
                        if (coupon.getCpnType().equals("01")) {
                            if (limit>vipStationLimit&&serviceAmt.compareTo(cpnAmt) < 0){
                                cpnAmt = serviceAmt;
                            }else if (limit<=vipStationLimit&&disServiceAmt.compareTo(cpnAmt) < 0){
                                cpnAmt = disServiceAmt;
                            }
                            //第一次计算
                            if (count.compareTo(BigDecimal.ZERO) == 0) {
                                if (chargeFrequency.compareTo(cpnLimit) > 0) {
                                    amt = cpnLimit.multiply(cpnAmt);
                                } else {
                                    amt = chargeFrequency.multiply(cpnAmt);
                                }
                                count = cpnLimit;
                            } else {
                                BigDecimal subtract = chargeFrequency.subtract(count);
                                //剩余次数大于发放数量 按发放数量计算
                                if (subtract.compareTo(cpnLimit) > 0) {
                                    amt = cpnLimit.multiply(cpnAmt).add(amt);
                                    count = count.add(cpnLimit);
                                } else {
                                    //剩余次数大于发放数量 按剩余次数计算
                                    amt = amt.add(subtract.multiply(cpnAmt));
                                    count = count.add(subtract);
                                }
                            }
                            logger.debug("现金券-发放数量:{},折扣:{},折扣金额:{}", cpnLimit,cpnAmt,amt);
                        } else if (coupon.getCpnType().equals("02")) {
                            cpnAmt = cpnAmt.divide(BigDecimal.TEN);
                            //折扣券
                            //用券后金额
                            BigDecimal afterCpn = BigDecimal.ZERO;
                            //折扣金额
                            BigDecimal finaAmt = BigDecimal.ZERO;
                            //0110：服务费、0100：整单金额
                            if ("0110".equals(coupon.getCpnDctType())){
                                if (limit>vipStationLimit){
                                    finaAmt = serviceAmt.multiply(cpnAmt);
                                }else {
                                    finaAmt = serviceAmt.subtract(disServiceAmt).multiply(cpnAmt);
                                }
                                //最高抵扣金额大于消费金额
                                if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(finaAmt)<0){
                                    finaAmt = new BigDecimal(coupon.getMaximumDiscountAmt());
                                }
                            }else if ("0100".equals(coupon.getCpnDctType())){
                                if (limit>vipStationLimit){
                                    afterCpn = chargeAmt.add(serviceAmt).multiply(cpnAmt);
                                }else {
                                    afterCpn = chargeAmt.add(serviceAmt).subtract(disServiceAmt).multiply(cpnAmt);
                                }
                                if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(afterCpn)<0){
                                    afterCpn = new BigDecimal(coupon.getMaximumDiscountAmt());
                                }
                                finaAmt = afterCpn;
                            }
                            if (count.compareTo(BigDecimal.ZERO) == 0) {
                                //第一次计算
                                if (chargeFrequency.compareTo(cpnLimit) > 0) {
                                    amt = finaAmt.multiply(cpnLimit);
                                } else {
                                    amt = finaAmt.multiply(chargeFrequency).add(amt);
                                }
                                count = cpnLimit;
                            } else {
                                BigDecimal subtract = chargeFrequency.subtract(count);
                                //剩余次数大于发放数量 按发放数量计算
                                if (subtract.compareTo(cpnLimit) > 0) {
                                    amt = finaAmt.multiply(cpnLimit).add(amt);
                                    count = count.add(cpnLimit);
                                } else {
                                    //剩余次数大于发放数量 按剩余次数计算
                                    amt = finaAmt.multiply(subtract).add(amt);
                                    count = count.add(subtract);
                                }
                            }
                            logger.debug("折扣券-发放数量:{},折扣:{},折扣金额:{}", cpnLimit,cpnAmt,amt);
                        }
                    }
                }
                //优惠券使用完
                BigDecimal discount = BigDecimal.ZERO;
                if (limit<vipStationLimit){
                    discount = serviceAmt.subtract(disServiceAmt);
                }
                amt = chargeFrequency.multiply(discount).add(amt).subtract(monthVipConfig.getVipPrice());
                bo.setSaveMoney(amt.toString());
            }
        }

        resultMap.put("saveMoneyStrategy", list);
        String vipCharge = sysParamService.getSysParamsValues("vipCharge");
        //月充电量
        resultMap.put("vipCharge", vipCharge);
        BigDecimal chargeFrequency = new BigDecimal(vipCharge).divide(degrees, 0, RoundingMode.DOWN);
        //充电次数
        resultMap.put("chargeFrequency", chargeFrequency);
        saveMoney(couponBos,resultMap,memberStation,memberBenefit);
        resultMap.put("monthVipPrice",monthVipConfig.getVipPrice());
        resultMap.put("serviceAmt", sysServiceAmt);
        resultMap.put("vipServiceAmt", sysServiceAmt.multiply(memberStation));
        if (memberBenefit.getLimitAmt()!=null){
            resultMap.put("limitAmt",memberBenefit.getLimitAmt());
        }
        if (memberBenefit.getLimitNum()!=null){
            resultMap.put("limitNum",memberBenefit.getLimitNum());
        }
        return resultMap;
    }

    @Override
    public List<VipSaveRecordRPCBo> getSaveRecordByMobile(Map<String, Object> inMap) {
        String mobile = StringUtil.trimToEmpty(inMap.get("mobile"));
        String startTime = StringUtil.trimToEmpty(inMap.get("startTime"));
        String endTime = StringUtil.trimToEmpty(inMap.get("endTime"));

        VipSaveRecordVo vo = new VipSaveRecordVo();
        vo.setMobile(mobile);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        return saveRecordDao.getSaveRecordByMobile(vo);
    }

    @Override
    public Map getUserSaveRecord(Map<String, Object> inMap) {
        String mobile = StringUtil.trimToEmpty(inMap.get("mobile"));
        VipSaveRecordVo vo = new VipSaveRecordVo();
        vo.setMobile(mobile);
        VipSaveRecordRPCBo userSaveRecord = saveRecordDao.getUserSaveRecord(vo);
        BigDecimal vipSaveMoney = userSaveRecord.getVipDiscount().add(userSaveRecord.getVipDiscountUp());
        BigDecimal cardsSaveMoney = userSaveRecord.getCashAmt().add(userSaveRecord.getDiscountAmt());
        BigDecimal saveMoney = userSaveRecord.getVipDiscount().add(userSaveRecord.getVipDiscountUp()).add(userSaveRecord.getCashAmt()).add(userSaveRecord.getDiscountAmt());
        Map<String, Object> resultMap = new HashedMap();
        resultMap.put("saveMoney",saveMoney);
        resultMap.put("vipSaveMoney",vipSaveMoney);
        resultMap.put("cardsSaveMoney",cardsSaveMoney);
        return resultMap;
    }

    @Override
    public void insertVipSaveRecord(VipSaveRecordRPCBo rpcBo) {
        VipSaveRecordBo bo = new VipSaveRecordBo();
        BeanUtils.copyProperties(rpcBo, bo);
        saveRecordDao.insertVipSaveRecord(bo);
    }

    @Override
    public void updateByOrder(VipSaveRecordRPCBo rpcBo) {
        VipSaveRecordBo bo = new VipSaveRecordBo();
        BeanUtils.copyProperties(rpcBo, bo);
        saveRecordDao.updateByOrder(bo);
    }

    @Override
    public Map qryEffectTime(Map<String, Object> inMap) {
        Map<String, Object> resultMap = new HashedMap();
        VipRecordCondition condition = new VipRecordCondition();
        String mobile = StringUtil.trimToEmpty(inMap.get("mobile"));
        condition.setMobile(mobile);
        VipPayRecordBo vipPayRecordBo = memberBenefitsDao.qryEffectTime(condition);
//        List<VipPayRecordBo> vipTimeBo = memberBenefitsDao.qryVipTime(condition);
//        BigDecimal reduce = vipTimeBo.stream()
//                .filter(Objects::nonNull) // 确保对象不是 null
//                .map(VipPayRecordBo::getIntervalDays) // 获取 intervalDays
//                .filter(item -> item != null) // 过滤掉 intervalDays 为 null 的情况
//                .reduce(BigDecimal.ZERO, (sum, current) -> sum.add(current));
//        resultMap.put("countDays",reduce);

        resultMap.put("startTime",vipPayRecordBo.getEffectTimeStr());
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义一个日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate targetDate = LocalDate.parse(vipPayRecordBo.getEffectTimeStr(), formatter);
        long between = ChronoUnit.DAYS.between(currentDate, targetDate)+1;
        resultMap.put("countDays",between);
        // 将当前日期格式化为指定的格式
        String formattedDate = currentDate.format(formatter);
        resultMap.put("endTime",formattedDate);
        return resultMap;
    }

    @Override
    public void insertVipExclusive(VipExclusivePutRPCBo rpcBo) {
        VipExclusivePutBo bo = new VipExclusivePutBo();
        BeanUtils.copyProperties(rpcBo, bo);
        exclusivePutDao.insert(bo);
    }

    @Override
    public void updateVipExclusive(VipExclusivePutRPCBo rpcBo) {
        VipExclusivePutBo bo = new VipExclusivePutBo();
        BeanUtils.copyProperties(rpcBo, bo);
        exclusivePutDao.update(bo);
    }

    @Override
    public VipExclusivePutRPCBo getVipExclusivePut(Map<String, Object> inMap) {
        String custId = StringUtil.nullToString(inMap.get("custId"));
        String mobile = StringUtil.nullToString(inMap.get("mobile"));
        VipExclusivePutBo bo = new VipExclusivePutBo();
        bo.setMobile(mobile);
        bo.setCustId(custId);
        return exclusivePutDao.getExclusivePut(bo);
    }

    @Override
    public List<Map<String,Object>> vipListEnabledInvoices(Map<String, Object> map) {
        return iInvoiceAlyRpcService.vipListEnabledInvoices(map);
    }
    @Override
    public void updateInvoiceFlag(VipInvoiceUpdateBo bo){
        memberBenefitsDao.updateInvoiceFlag(bo);
    }

    @Override
    public String getLevelConfig() {
        VipLevelCalcConfig latestConfig = configService.getLatestConfig();
        if (latestConfig != null) {
            String calcMode = latestConfig.getCalcMode();
            return calcMode;
        }
        return null;
    }

    private void saveMoney(List<SaveMoneyStrategyRPCBo> list, String chargeDegree, BigDecimal degrees) {
        SaveMoneyStrategyRPCBo bo = new SaveMoneyStrategyRPCBo();
        bo.setChargeDegree(chargeDegree);
        BigDecimal chargeFrequency = new BigDecimal(chargeDegree).divide(degrees, 0, RoundingMode.DOWN);
        bo.setChargeFrequency(chargeFrequency.toString());

        list.add(bo);
    }


    private void saveMoney(List<CouponBo> couponBos, Map<String, Object> resultMap, BigDecimal memberStation, MemberBenefitsRPCBo memberBenefit) {
        //默认30度
        String vipDegrees = sysParamService.getSysParamsValues("vipDegrees");
        BigDecimal degrees = new BigDecimal("30");
        if (StringUtil.isNotEmpty(vipDegrees)){
            degrees = new BigDecimal(vipDegrees);
        }
        //会员默认服务费配置
        String vipServiceAmt = sysParamService.getSysParamsValues("vipServiceAmt");
        String vipChargeAmt = sysParamService.getSysParamsValues("vipChargeAmt");
        BigDecimal sysServiceAmt = BigDecimal.ZERO;
        BigDecimal sysChargeAmt = BigDecimal.ZERO;
        if (StringUtil.isEmpty(vipServiceAmt)){
            sysServiceAmt = new BigDecimal("1");
        }else {
            sysServiceAmt = new BigDecimal(vipServiceAmt);
        }
        if (StringUtil.isEmpty(vipChargeAmt)){
            sysChargeAmt = new BigDecimal("1");
        }else {
            sysChargeAmt = new BigDecimal(vipChargeAmt);
        }
        //会员月省度数配置
        String vipCharge = sysParamService.getSysParamsValues("vipCharge");
        BigDecimal sysVipCharge = BigDecimal.ZERO;
        if (StringUtil.isEmpty(vipServiceAmt)){
            sysVipCharge = new BigDecimal("1500");
        }else {
            sysVipCharge = new BigDecimal(vipCharge);
        }
        BigDecimal chargeFrequency = sysVipCharge.divide(degrees, 0, RoundingMode.DOWN);
        //应收服务费
        BigDecimal serviceAmt = sysServiceAmt.multiply(degrees);
        BigDecimal chargeAmt = sysChargeAmt.multiply(degrees);
        //打折后服务费
        BigDecimal disServiceAmt = serviceAmt.multiply(memberStation);
        //会员场站限制
        Integer vipStationLimit = 9999;
        //会员场站限制
        if(memberBenefit.getDiscountLimit().equals("1")){
            if (memberBenefit.getLimitAmt()!=null&&memberBenefit.getLimitAmt().compareTo(disServiceAmt)<0){
                disServiceAmt = memberBenefit.getLimitAmt();
            }
            if (memberBenefit.getLimitNum()!=null){
                vipStationLimit = memberBenefit.getLimitNum();
            }
        }
        Integer limit = 0;
        BigDecimal vipDisAmt = serviceAmt.subtract(disServiceAmt).multiply(chargeFrequency);
        //折扣省钱金额
        resultMap.put("vipDisAmt",vipDisAmt);
        //打折金额
        BigDecimal disAmt = BigDecimal.ZERO;
        //满减金额
        BigDecimal cashAmt = BigDecimal.ZERO;
        //发放数量
        BigDecimal count = BigDecimal.ZERO;
        //可省金额
        BigDecimal amt = BigDecimal.ZERO;
        for (CouponBo coupon : couponBos) {
            limit++;
            logger.debug("saveMoneyStrategy-coupon:{}", JSONObject.toJSONString(coupon));
            //金额
            BigDecimal cpnAmt = new BigDecimal(coupon.getCpnAmt());
            //发放数量
            BigDecimal cpnLimit = new BigDecimal(coupon.getLimGetNum());
            //现金券
            if (coupon.getCpnType().equals("01")) {
                if (disServiceAmt.compareTo(cpnAmt) < 0) {
                    cpnAmt = disServiceAmt;
                }
                //第一次计算
                if (count.compareTo(BigDecimal.ZERO) == 0) {
                    if (chargeFrequency.compareTo(cpnLimit) > 0) {
                        amt = cpnLimit.multiply(cpnAmt);
                        cashAmt = cpnLimit.multiply(cpnAmt);
                    } else {
                        amt = chargeFrequency.multiply(cpnAmt);
                        cashAmt = chargeFrequency.multiply(cpnAmt);
                    }
                    count = cpnLimit;
                } else {
                    BigDecimal subtract = chargeFrequency.subtract(count);
                    //剩余次数大于发放数量 按发放数量计算
                    if (subtract.compareTo(cpnLimit) > 0) {
                        amt = cpnLimit.multiply(cpnAmt).add(amt);
                        cashAmt = cashAmt.add(cpnLimit.multiply(cpnAmt));
                        count = count.add(cpnLimit);
                    } else {
                        //剩余次数大于发放数量 按剩余次数计算
                        amt = amt.add(subtract.multiply(cpnAmt));
                        cashAmt = cashAmt.add(subtract.multiply(cpnAmt));
                        count = count.add(subtract);
                    }
                }
            } else if (coupon.getCpnType().equals("02")) {
                cpnAmt = cpnAmt.divide(BigDecimal.TEN);
                //折扣券
                //用券后金额
                BigDecimal afterCpn = BigDecimal.ZERO;
                //折扣金额
                BigDecimal finaAmt = BigDecimal.ZERO;
                //0110：服务费、0100：整单金额
                if ("0110".equals(coupon.getCpnDctType())){
                    if (limit>vipStationLimit){
                        finaAmt = serviceAmt.multiply(cpnAmt);
                    }else {
                        finaAmt = serviceAmt.subtract(disServiceAmt).multiply(cpnAmt);
                    }
                    //最高抵扣金额大于消费金额
                    if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(finaAmt)<0){
                        finaAmt = new BigDecimal(coupon.getMaximumDiscountAmt());
                    }
                }else if ("0100".equals(coupon.getCpnDctType())){
                    if (limit>vipStationLimit){
                        afterCpn = chargeAmt.add(serviceAmt).multiply(cpnAmt);
                    }else {
                        afterCpn = chargeAmt.add(serviceAmt).subtract(disServiceAmt).multiply(cpnAmt);
                    }
                    if (new BigDecimal(coupon.getMaximumDiscountAmt()).compareTo(afterCpn)<0){
                        afterCpn = new BigDecimal(coupon.getMaximumDiscountAmt());
                    }
                    finaAmt = afterCpn;
                }
                if (count.compareTo(BigDecimal.ZERO) == 0) {
                    //第一次计算
                    if (chargeFrequency.compareTo(cpnLimit) > 0) {
                        amt = finaAmt.multiply(cpnLimit);
                        disAmt = finaAmt.multiply(cpnLimit);
                    } else {
                        amt = finaAmt.multiply(chargeFrequency).add(amt);
                        disAmt = finaAmt.multiply(chargeFrequency);
                    }
                    count = cpnLimit;
                } else {
                    BigDecimal subtract = chargeFrequency.subtract(count);
                    //剩余次数大于发放数量 按发放数量计算
                    if (subtract.compareTo(cpnLimit) > 0) {
                        amt = finaAmt.multiply(cpnLimit).add(amt);
                        disAmt = finaAmt.multiply(cpnLimit).add(disAmt);
                        count = count.add(cpnLimit);
                    } else {
                        //剩余次数大于发放数量 按剩余次数计算
                        amt = finaAmt.multiply(subtract).add(amt);
                        disAmt = finaAmt.multiply(subtract).add(disAmt);
                        count = count.add(subtract);
                    }
                }
            }
        }
        //优惠券使用完
        BigDecimal discount = BigDecimal.ZERO;
        if (limit<vipStationLimit){
            discount = serviceAmt.subtract(disServiceAmt);
        }
        amt = chargeFrequency.multiply(discount).add(amt);
        //PLUS月度省钱金额
        resultMap.put("maxAmt",amt);
        resultMap.put("disAmt",disAmt);
        resultMap.put("cashAmt",cashAmt);

    }

}
