/**
 *
 * @(#) IEPrcChargeService.java
 * @Package com.ls.ner.billing.api.prccharge.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api.prccharge.service;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.prccharge.bo.EPrcChargeBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年5月1日 上午10:30:10   lipf   Created.
 *           
 */
public interface IEPrcChargeService {
	
	/**
	 * 
	 * 方法说明：批量插入计费结果
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 上午10:31:13
	 * History:  2016年5月1日 上午10:31:13   lipf   Created.
	 *
	 * @param list
	 *
	 */
	public void insertEPrcCharge(List<EPrcChargeBo> list);
	/**
	 * 
	 * 方法说明：批量插入计费结果明细
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 上午10:31:09
	 * History:  2016年5月1日 上午10:31:09   lipf   Created.
	 *
	 * @param list
	 *
	 */
	public void insertEPrcChargeDet(List<EPrcChargeDetBo> list);
	/**
	 * 
	 * 方法说明：通过app_no和计费类型billing_type查询订单费用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 下午11:18:54
	 * History:  2016年5月1日 下午11:18:54   lipf   Created.
	 *
	 * @param paraMap
	 * @return
	 *
	 */
	public List<EPrcChargeBo> getPrcCharge(Map<String, String> paraMap);
	/**
	 * 
	 * 方法说明：通过app_no和计费类型billing_type查询订单费用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月1日 下午11:19:00
	 * History:  2016年5月1日 下午11:19:00   lipf   Created.
	 *
	 * @param paraMap
	 * @return
	 *
	 */
	public List<EPrcChargeDetBo> getPrcChargeDet(Map<String, String> paraMap);
	/**
	 * 通过app_no和计费类型billing_type更新订单总费用
	 * @param tMap
	 */
	public void updateEprcCharge(Map<String, Object> tMap);
}
