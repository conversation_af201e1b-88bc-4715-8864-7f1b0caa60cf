<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.ICouponPushDao">

	<resultMap id="couponPushBo" type="com.ls.ner.billing.market.bo.CouponPushBo">
		<result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
		<result column="CPN_ID" property="cpnId" jdbcType="VARCHAR" />
		<result column="CPN_NAME" property="cpnName" jdbcType="VARCHAR" />
		<result column="CPN_TYPE" property="cpnType" jdbcType="VARCHAR" />
		<result column="PROD_BUSI_TYPE" property="busiType" jdbcType="VARCHAR" />
		<result column="CPN_DCT_TYPE" property="cpnDctType" jdbcType="VARCHAR" />
		<result column="CPN_AMT" property="cpnAmt" jdbcType="VARCHAR" />
		<result column="EFT_DATE" property="eftDate" jdbcType="VARCHAR" />
		<result column="INV_DATE" property="invDate" jdbcType="VARCHAR" />
		<result column="TIME_DURATION" property="timeDuration" jdbcType="VARCHAR" />
		<result column="TIME_UNIT" property="timeUnit" jdbcType="VARCHAR" />
		<result column="CPN_TIME_TYPE" property="cpnTimeType" jdbcType="VARCHAR" />
		<result column="CPN_NUM" property="cpnNum" jdbcType="VARCHAR" />
		<result column="LIM_GET_NUM" property="limGetNum" jdbcType="VARCHAR" />
		<result column="ALRDY_GET_NUM" property="alrdyGetNum" jdbcType="VARCHAR" />
		<result column="CPN_MARKS" property="cpnMarks" jdbcType="VARCHAR" />
		<result column="CPN_STATUS" property="cpnStatus" jdbcType="VARCHAR" />
		<result column="CRE_TIME" property="creTime" jdbcType="VARCHAR" />
		<result column="CRE_EMP" property="creEmp" jdbcType="VARCHAR" />
		<result column="EFFECT_TIME" property="effectTime" jdbcType="VARCHAR" />
		<result column="ACT_ID" property="actId" jdbcType="VARCHAR" />
		<result column="PUSH_ID" property="pushId" jdbcType="VARCHAR" />
	</resultMap>

<!-- 描述:获取已推广的优惠券  创建人:biaoxiangd  创建时间: 2017/7/6 15:10 -->
	<select id="getCouponsPush" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponPushBo">
		SELECT
			c.cpn_id,c.cpn_name,cpn_type,prod_busi_type,cpn_dct_type,cpn_amt,
			date_format(eft_date, '%Y-%m-%d') eft_date, date_format(inv_date, '%Y-%m-%d') inv_date,
			time_duration,c.time_unit,cpn_time_type,
			ifnull(cpn_num,'0')as cpn_num, ifnull(lim_get_num,'0') as lim_get_num, ifnull(alrdy_get_num,'0')  as alrdy_get_num,
			cpn_marks,c.cpn_state as cpn_status,
			if ( cpn_time_type = '1',
			concat( date_format(eft_date, '%Y-%m-%d'), '~', date_format(inv_date, '%Y-%m-%d') ),
			concat( '领用或发放后', ifnull(time_duration,'0'))
			) effect_time,
			date_format(c.cre_time, '%Y-%m-%d %H:%i') cre_time,cre_emp,
			p.push_id,p.act_id
		FROM e_coupon c,e_coupon_push p
		WHERE
			c.CPN_ID = p.CPN_ID
		AND ACT_ID = #{actId}
		<if test="cpnType != null and cpnType != ''">
			AND c.CPN_TYPE = #{cpnType}
		</if>
		<if test="cpnName != null and cpnName != ''">
			AND c.cpn_name like concat('%',#{cpnName},'%')
		</if>
		<if test="busiType != null and busiType != ''">
			AND prod_busi_type = #{busiType}
		</if>
		<if test="cpnStatus != null and cpnStatus != ''">
			AND c.CPN_STATE= #{cpnStatus}
		</if>
		<if test="orgCode != null and orgCode != ''">
			AND c.ORG_CODE = #{orgCode}
		</if>
		ORDER BY cre_time DESC
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="getCouponsPushCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		SELECT
			COUNT(1)
		FROM e_coupon c,e_coupon_push p
		WHERE
		c.CPN_ID = p.CPN_ID
		AND ACT_ID = #{actId}
		ORDER BY cre_time DESC
	</select>

	<insert id="saveCouponPush" parameterType="com.ls.ner.billing.market.bo.CouponPushBo">
		INSERT INTO e_coupon_push(ACT_ID,CPN_ID)
		VALUES (#{actId},#{cpnId})
	</insert>

<!-- 描述:删除已推广的优惠券  创建人:biaoxiangd  创建时间: 2017/7/6 15:12 -->
	<delete id="delCouponPush" parameterType="com.ls.ner.billing.market.vo.MarketCondition">
		DELETE FROM e_coupon_push WHERE ACT_ID = #{actId} AND PUSH_ID = #{pushId}
	</delete>

</mapper>