package com.ls.ner.billing.api.xpcharge.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.api.xpcharge.condition
 * @ClassName: ChargeBillingConfHisQueryCondition
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/3/31 22:21
 * @Version: 1.0
 */
public class ChargeBillingConfHisQueryCondition  extends QueryCondition {

    /**
     * 计费编号
     */
    private String chcNo;

    public String getChcNo() {
        return chcNo;
    }

    public void setChcNo(String chcNo) {
        this.chcNo = chcNo;
    }
}
