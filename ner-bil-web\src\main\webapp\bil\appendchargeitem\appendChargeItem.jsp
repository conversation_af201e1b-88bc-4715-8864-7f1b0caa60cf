<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="其他服务项目费用配置" />
<ls:body>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="appendChargeItemGrid" height="350" width="100%"
					showCheckBox="false">
					<ls:column name="itemNo" caption="" hidden="true" />					
					<ls:column name="itemName" caption="费用项名称" />
					<ls:column name="itemUnit" caption="费用项单位" />
					<ls:column name="sn" caption="排序" hidden="false" />

				</ls:grid></td>
		</tr>
	</table>

	<ls:form id="appendChargeItemForm" name="appendChargeItemForm">
		<table class="tab_search">
			<ls:text name="itemNo" property="itemNo" type="hidden"></ls:text>
			<tr>

				<td><ls:label text="费用项名称" ref="itemName" /></td>
				<td><ls:text name="itemName" property="itemName"
						required="true" maxlength="100"></ls:text></td>
				<td><ls:label text="费用项单位" ref="itemUnit" /></td>
				<td>
					<ls:select name="itemUnit" property="itemUnit" required="true">
						<ls:options property="billingPriceUnitList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>				
				</td>
				<td><ls:label text="排序" ref="sn" /></td>
				<td><ls:text  type="number" name="sn" property="sn" min="0" max="9999" required="true"></ls:text></td>

			</tr>

		</table>
		<table>
			<tr>
				<td>
					<div class="pull-right">
						<ls:button text="新增" onclick="doAdd" />
						<ls:button text="清除" onclick="doClear" />
						<ls:button text="保存" onclick="doSave" />
						<ls:button text="删除" onclick="doDel" />
					</div>
				</td>
			</tr>
		</table>
	</ls:form>

	<ls:script>
	var baseUrl = "~/billing/appendchargeitem/attachs";
    query();
    function query(){
    
      appendChargeItemGrid.query(baseUrl);
    }
    function doAdd(){  
      doClear();
      itemNo.setValue();
    }
    function doClear(){
      appendChargeItemForm.clear();
    };
    function doSave(){
	    var url = baseUrl;
	    var itemNoValue = itemNo.getValue();
	    if(!!itemNoValue){
	    	url += "/" + itemNoValue;
	    }
	     url += '/post';
      appendChargeItemForm.submit(url, {valid : true},function(result){
     		query();
       		LS.message("info","保存成功，费用项编号为"+result);
       		
       	
      });
    }
    function doDel(){      
      var item = appendChargeItemGrid.getSelectedItem();
      if(item==null){
      	 LS.message("info","请选择一条记录!");
      	 return;
      }
        LS.confirm('确认删除吗?', function(data) {
        if (data) {
          var url = baseUrl +"/"+item.itemNo+"/delete";
          LS.ajax(url,{},function(result){
          	 query();
          	 doAdd();
             LS.message("info","删除成功,费用项编号为"+result);
            });
          }
        });
      
    }
    
    appendChargeItemGrid.itemclick = function(){
     
	      var item = appendChargeItemGrid.getSelectedItem();
	      itemNo.setValue(item.getValue("itemNo"));
        itemName.setValue(item.getValue("itemName"));
         sn.setValue(item.getValue("sn"));
	    
      };
   

    </ls:script>
</ls:body>
</html>