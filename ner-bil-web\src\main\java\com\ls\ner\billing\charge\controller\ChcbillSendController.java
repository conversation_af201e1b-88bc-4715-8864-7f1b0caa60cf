package com.ls.ner.billing.charge.controller;

import com.ls.ner.util.IPUtil;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.param.api.ISysParamService;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.constants.PublicConstants;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo;
import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.util.MergeUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * 充电电价下发
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/chcbillSend")
public class ChcbillSendController extends QueryController<ChcbillSendQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(ChcbillSendController.class);

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired
	Authentication authentication;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ISysParamService sysParamService;
	
	@Override
	protected ChcbillSendQueryCondition initCondition() {
		return new ChcbillSendQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化充电电价下发
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.CHC_BILL_SEND_STATUS);
		ChcbillSendQueryCondition condition = new ChcbillSendQueryCondition();
		 AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				condition.setOrgCode(orgBo.getOrgCode());
				condition.setOrgCodeName(orgBo.getOrgShortName());			
			}
			m.addAttribute("searchForm", condition);
			m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/chcbillSend/chcbillSendIndex";
	}

	/**
	 * <AUTHOR>
	 * @throws IllegalAccessException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchFieldException 
	 * @dateTime 2016-04-09
	 * @description 查询充电电价下发
	 */
	@RequestMapping(value = "/queryChcbillSends")
	public @ItemResponseBody QueryResultObject queryChcbillSends(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		List<ChcbillSendBo> list =chcbillSendService.queryChcbillSends(condition);
		if(list!=null && list.size()>0){
			StringBuilder stationIdSb = new StringBuilder();
			for(ChcbillSendBo cbcBo:list){
				OrgBo org = orgService.getOrgByNo(cbcBo!=null?cbcBo.getOrgCode():"");
				if(org != null)
					cbcBo.setOrgCodeName(org.getOrgShortName());
				if(!StringUtils.nullOrBlank(cbcBo.getStationId())){
					stationIdSb.append(cbcBo.getStationId()).append(",");
				}
			}
			String stationIds = stationIdSb.toString();
			if(!StringUtils.nullOrBlank(stationIds)){
				stationIds = stationIds.substring(0, stationIds.length() - 1);
				List<Map<String,Object>> stationList = archivesRpcService.queryStations(stationIds);
				MergeUtil.mergeList(list, stationList, "stationId",  new String[]{"stationName"});
			}
		}
		int recordCount = chcbillSendService.queryChcbillSendsNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-10
	 * @description 下发充电电价
	 */
	@RequestMapping(value = "/issured", method = RequestMethod.POST)
	public @ResponseBody WrappedResult issured(Model m, @RequestParam("chcNo") String chcNo, HttpServletRequest request) {
		try {
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			chcbillSendService.issuredStation(chcNo,map);
			return WrappedResult.successWrapedResult("正在下发");
		} catch (Exception e) {
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发明细
	 */
	@RequestMapping(value = "/detail", method = RequestMethod.GET)
	public String detail(Model m, @RequestParam("chcNo") String chcNo) {
		addCodes(m, BizConstants.CodeType.SUCCESS_FLAG,BizConstants.CodeType.VALID_FLAG);
		if (!StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0)
				m.addAttribute("searchForm", list.get(0));
			else
				m.addAttribute("searchForm", new ChargeBillingConfBo());
		}
		String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
		m.addAttribute("chargingPriingSendTimeOut", StringUtil.isEmpty(sendTimeOut)?"20":sendTimeOut);
		return "/bil/chcbillSend/chcbillSendDetail";
	}

	/**
	 * <AUTHOR>
	 * @throws IllegalAccessException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchFieldException 
	 * @dateTime 2016-04-09
	 * @description 查询桩电价下发明细
	 */
	@RequestMapping(value = "/queryChcbillSendDetail")
	public @ItemResponseBody QueryResultObject queryChcbillSendDetail(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendBo> list = null;
		int recordCount = 0;
		if(!StringUtils.nullOrBlank(condition.getChcNo())){
			list =chcbillSendService.queryChcbillPileSends(condition);
			if(list != null && list.size() > 0){
				StringBuilder pileNoSb = new StringBuilder();
				for(ChcbillPileSendBo cbcBo:list){
					if(!StringUtils.nullOrBlank(cbcBo.getPileNo())){
						pileNoSb.append(cbcBo.getPileNo()).append(",");
					}
				}
				String pileNos = pileNoSb.toString();
				if(!StringUtils.nullOrBlank(pileNos)){
					pileNos = pileNos.substring(0, pileNos.length() - 1);
					ChargeBillingConfQueryCondition cbQCondition = new ChargeBillingConfQueryCondition();
					cbQCondition.setChcNo(condition.getChcNo());
					List<ChargeBillingConfBo> cbcs = chargeBillingConfService.queryChargeBillingConfs(cbQCondition);
					String stationId = null;
					if(cbcs.size() > 0)
						stationId = cbcs.get(0).getStationId();
					List<Map<String,Object>> stationList = archivesRpcService.queryEquipByStationIdAndEquipNos(stationId,pileNos);
					MergeUtil.mergeList(list, stationList, "pileNo", "equipNo",  new String[]{"pileName"},  new String[]{"equipName"});
				}
			}
			recordCount = chcbillSendService.queryChcbillPileSendsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-10
	 * @description 重新下发桩电价
	 */
	@RequestMapping(value = "/issuredPile", method = RequestMethod.POST)
	public @ResponseBody WrappedResult issuredPile(Model m, @RequestParam("chcNo") String chcNo,@RequestParam("pileNo") String pileNo,HttpServletRequest request) {
		try {
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			logger.debug("map============{}"+map.toString());
			chcbillSendService.issuredPile(chcNo, pileNo,map);
			return WrappedResult.successWrapedResult(true);
		} catch (Exception e) {
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 初始化桩电价下发历史
	 */
	@RequestMapping(value = "/log", method = RequestMethod.GET)
	public String log(Model m, @RequestParam("chcNo") String chcNo, @RequestParam("pileNo") String pileNo) {
		addCodes(m,BizConstants.CodeType.VALID_FLAG);
		if (!StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			if(list != null && list.size() > 0)
				bo = list.get(0);
			bo.setPileId(pileNo);
			m.addAttribute("searchForm", bo);
		}

		String sendTimeOut = sysParamService.getSysParamsValues("chargingPriingSendTimeOut");
		m.addAttribute("chargingPriingSendTimeOut", StringUtil.isEmpty(sendTimeOut)?"20":sendTimeOut);
		return "/bil/chcbillSend/chcbillSendLog";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-09
	 * @description 查询桩电价下发历史
	 */
	@RequestMapping(value = "/queryChcbillSendLog")
	public @ItemResponseBody QueryResultObject queryChcbillSendLog(@QueryRequestParam("params") RequestCondition params) {
		ChcbillSendQueryCondition condition = this.rCondition2QCondition(params);
		List<ChcbillPileSendLogBo> list = null;
		int recordCount = 0;
		if(!StringUtils.nullOrBlank(condition.getChcNo())){
			list =chcbillSendService.queryChcbillPileSendLogs(condition);
			recordCount = chcbillSendService.queryChcbillPileSendLogsNum(condition);
		}
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}
}
