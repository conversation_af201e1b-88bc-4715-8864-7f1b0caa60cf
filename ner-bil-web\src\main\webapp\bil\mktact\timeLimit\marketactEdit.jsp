<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="限时折扣">
    <style>
        .file-img {
            display: block;
            width: 200px;
            height: 120px;
            position: relative;
        }

        .file-img img {
            display: block;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: gray;
        }

        .file-img .span_add {
            position: absolute;
            left: 0;
            bottom: 0;
            display: none;
            width: 50%;
            height: 30px;
            line-height: 30px;
            background-repeat: no-repeat;
            background-position: center;
            background-color: rgba(0, 0, 0, .5);
            text-align: center;
            background-image: url(<%=request.getContextPath() %>/bil/mktact/img/add.png);
        }

        .file-img .span_del {
            position: absolute;
            right: 0;
            bottom: 0;
            display: none;
            width: 50%;
            height: 30px;
            line-height: 30px;
            background-repeat: no-repeat;
            background-position: center;
            background-color: rgba(0, 0, 0, .5);
            text-align: center;
            background-image: url(<%=request.getContextPath() %>/bil/mktact/img/delete.png);
        }

        .file-img:hover span {
            display: block;
        }

        .file-img input {
            position: absolute;
            font-size: 100px;
            right: 0;
            top: 0;
            opacity: 0;
        }

    </style>

    <link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/themes/default/default.css" />
    <link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.css" />
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/kindeditor-all.js"></script>
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/lang/zh-CN.js"></script>
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.js"></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
    <ls:form id="marketactForm" name="marketactForm" enctype="multipart/form-data">
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden" value="02"/>
        <ls:text name="cityCodes" property="cityCodes" type="hidden"/>
        <ls:text name="stationIds" property="stationIds" type="hidden"/>
        <ls:text name="release" type="hidden"/>
        <ls:text name="youhui" type="hidden"/>
        <ls:text name="info" type="hidden"/>
        <table class="tab_search">
            <tr>
                <td width="10%"><ls:label text="活动名称" ref="actName"/></td>
                <td><ls:text name="actName" property="actName" required="true"/></td>
                <td rowspan="4">
                    <div>
                        <a class="file-img" href="javascript:void(0);" style="left: 200px;top: 10px;">
                            <img src="" id="headImg"></img>
                            <span id="span_add" class="span_add">
                                    <input id="fileData" accept="image/*" class="text" name="fileData"
                                           type="file" onchange="ShowImage(this)" multiple/>
                                </span>
                            <span id="span_del" class="span_del" onclick="doDelPic()"></span>
                        </a>
                    </div>
                </td>
            </tr>
            <tr>
                <td><ls:label text="生效时间" ref="effTime"/></td>
                <td width="50%">
                    <div style="float: left;width: 30%">
                        <ls:checklist name="isImmediate" type="checkbox" property="isImmediate" required="true"
                                      value="1"
                                      onchanged="getImmediate">
                            <ls:checkitem text="立即生效" value="1"/>

                        </ls:checklist>
                    </div>
                    <div style="float: left;width: 70%">
                        <span id="efftimeSpan" style="display: none">
                            <ls:date name="effTime" property="effTime" format="yyyy-MM-dd HH:mm" required="true"/>
                            </span>
                    </div>
                </td>
            </tr>
            <tr>
                <td><ls:label text="失效时间" ref="expTime"/></td>
                <td>
                    <ls:date name="expTime" property="expTime" format="yyyy-MM-dd HH:mm" required="true"/>
                </td>
            </tr>
            <tr style="display: none;">
                <td><ls:label text="适用对象" ref="custType"/></td>
                <td>
                    <ls:checklist name="custType" property="custType" type="checkbox" required="true" value="01">
                        <ls:checkitem text="个人" value="01"/>
                        <ls:checkitem text="企业" value="02"/>
                    </ls:checklist>
                </td>
            </tr>

            <c:choose>
                <c:when test="${currentUser == 'SYSADMIN' }">
                    <tr>
                        <td><ls:label text="适用运营商" ref="buildType"/></td>
                        <td>
                            <ls:checklist name="buildType" type="radio" property="buildType" required="true"
                                          onchanged="getBuildType" value="1">
                                <ls:checkitem text="全部" value="0"/>
                                <ls:checkitem text="部分" value="1"/>
                            </ls:checklist>
                        </td>
                    </tr>
                    <tr id="buildListTr" style="display: none">
                        <td><ls:label text=""/></td>
                        <td>
                            <ls:select name="buildId" property="buildId" required="true" onchanged="getBuild">
                                <ls:options property="buildList" scope="request" text="operName" value="operNo"/>
                            </ls:select>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <tr style="display: none">
                        <td><ls:label text="适用运营商" ref="buildType"/></td>
                        <td>
                            <ls:checklist name="buildType" type="radio" property="buildType" required="true"
                                          onchanged="getBuildType" value="0">
                                <ls:checkitem text="全部" value="0"/>
                                <ls:checkitem text="部分" value="1"/>
                            </ls:checklist>
                        </td>
                    </tr>
                    <tr id="buildListTr" >
                        <td><ls:label text="适用运营商"/></td>
                        <td>
                            <ls:select name="buildId" property="buildId" required="true" readOnly="true" >
                                <ls:options property="buildList" scope="request" text="operName" value="operNo"/>
                            </ls:select>
                        </td>
                    </tr>
                </c:otherwise>
            </c:choose>


            <tr>
                <td><ls:label text="适用城市" ref="city"/></td>
                <td>
                    <ls:checklist name="city" property="city" type="radio" required="true" value="0"
                                  onchanged="getCity">
                        <ls:checkitem text="全部" value="0"/>
                        <ls:checkitem text="部分" value="1"/>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="cityListTr" style="display: none">
                <td><ls:label text=""/></td>
                <td>
                    <span id="cityListSpan">
                    </span>

                </td>
            </tr>
            <tr>
                <td><ls:label text="适用站点" ref="stationId"/></td>
                <td>
                    <ls:checklist name="stationId" property="stationId" type="radio" required="true"
                                  value="0"
                                  onchanged="doSearchStation">
                        <ls:checkitem text="全部" value="0"/>
                        <ls:checkitem text="部分" value="1"/>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="stationNumTr" style="display: none">
                <td><ls:label text=""/></td>
                <td><span id="stationSelectNum" style="color: red"></span></td>
            </tr>
            <tr id="stationListTr" style="display: none">
                <td><ls:label text=""/></td>
                <td colspan="3">
                    <ls:grid height="180px" width="80%" url="" name="station_grid" showCheckBox="true"
                             primaryKey="stationId"
                             allowSorting="true" singleSelect="false">
                        <ls:column caption="站点Id" name="stationId" hidden="true"></ls:column>
                        <ls:column caption="城市code" name="city" hidden="true"></ls:column>
                        <ls:column caption="站点类型" name="stationTypeName"></ls:column>
                        <ls:column caption="站点名称" name="stationName"></ls:column>
                    </ls:grid>
                </td>
            </tr>
            <tr>
                <td><ls:label text="活动描述" ref="actMarks"/></td>
                <td colspan="3">
                    <ls:text name="actMarks" property="actMarks" type="textarea" required="true"/>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:form id="limDisCountForm" name="limDisCountForm">
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden"/>
        <ls:text name="dctType" property="dctType" type="hidden"/>
        <ls:text name="actCondId" property="actCondId" type="hidden"/>
        <ls:text name="actCondDetId" property="actCondDetId" type="hidden"/>
        <ls:text name="actDctProdObj" value="" type="hidden"/>
        <table class="tab_search">
            <colgroup>
                <col width="11.5%"/>
                <col width="88.5%"/>
            </colgroup>
            <tr>
                <td><ls:label text="优惠内容" ref="preferentialContent"/>
                    <ls:text type="hidden" name="preferentialContent" required="true"/></td>
                <td>
                    <ls:grid url="" height="200px" name="actDctGrid" caption="" showRowNumber="true" primaryKey="id"
                             showCheckBox="false" singleSelect="true">
                        <ls:gridToolbar name="operation">
                            <ls:gridToolbarItem name="addBtn" text="添加" imageKey="add"
                                                onclick="addAct"></ls:gridToolbarItem>
                            <ls:gridToolbarItem name="delBtn" text="删除" imageKey="delete"
                                                onclick="delAct"></ls:gridToolbarItem>
                        </ls:gridToolbar>
                        <ls:column name="actCondDetId" caption="优惠条件明细ID" hidden="true"></ls:column>
                        <ls:column caption="优惠对象" name="actDctProdName" editableEdit="false" width="50%">
                            <ls:selectEditor property="actDctProdIdList"
                                             valueMember="itemNo" name="actDctProdId" required="true"
                                             displayMember="itemName">
                            </ls:selectEditor>
                            <%--<ls:selectEditor allowEditing="true" name="actDctProdId">
                                <ls:optionEditor value="1000000000" text="电费"></ls:optionEditor>
                                <ls:optionEditor value="1000001000" text="服务费"></ls:optionEditor>
                            </ls:selectEditor>--%>
                        </ls:column>
                        <ls:column name="actDctAllFree" caption="" hidden="true"></ls:column>
                        <ls:column name="dctValue" caption="优惠数值" editableEdit="false" width="25%">
                            <ls:textEditor type="number"/>
                        </ls:column>
                        <ls:column name="unitZ" caption="" width="15%">
                            <ls:textEditor value="折" readOnly="true"/>
                        </ls:column>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:form id="actinfoForm" name="actinfoForm">
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden"/>
        <ls:text name="actInfoId" property="actInfoId" type="hidden"/>
        <ls:text name="relaId" property="relaId" type="hidden"/>
        <ls:text name="attachId" property="attachId" type="hidden"/>
        <ls:text name="contentUrl" property="contentUrl" type="hidden"/>
        <ls:text name="picFlag" property="picFlag" type="hidden"/>
        <table align="center" class="tab_search">
            <colgroup>
                <col width="10%"/>
                <col width="90%"/>
            </colgroup>
            <tr>
                <td><ls:label text="资讯内容" ref="infoType"/></td>
                <td>
                    <ls:checklist type="radio" name="infoType" property="infoType" required="true"
                                  onchanged="doInfoType" value="1">
                        <ls:checkitems property="infoTypeList" scope="request" text="text"
                                       value="value"></ls:checkitems>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="tr_content">
                <td><ls:label text="活动资讯" ref="actInfoContent"/><ls:text type="hidden" name="actInfoContent" required="true"/></td>
                <td height="180px">
                    <div id="metacontent">
                    </div>
                    <ls:text name="content" property="content" visible="false"></ls:text>
                </td>
            </tr>
            <tr id="tr_linkUrl">
                <td><ls:label text="链接网址"/></td>
                <td><ls:text name="linkUrl" property="linkUrl"/></td>
            </tr>
            <tr>
                <td colspan="2">
                    <div class="pull-right">
                        <ls:button text="保存" name="save" onclick="baocun"/>
                        <ls:button text="发布" name="fabu" onclick="fabu"/>
                    </div>
                </td>
            </tr>
        </table>
    </ls:form>

    <ls:script>

        //定时器 每秒去算选择了多少个站点
        function getStationGridSelectNum() {
            var item = station_grid.getCheckedItems();
            $('#stationSelectNum').empty();
            $("#stationSelectNum").append("已经选择" + item.length + "个站点为")
        }


        var cityNameStr = "";


        var editor;
        var pubPath = '${pubPath}';

        KindEditor.ready(function(K) {
            editor = K.create('#metacontent', {
                cssPath : pubPath + '/pub/kindeditor/plugins/code/prettify.css',
                uploadJson : pubPath + '/pub/picture/upload',
                items : [
                    'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
                    'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
                    'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
                    'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
                    'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
                    'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
                    'table', 'hr', 'emoticons', 'pagebreak',
                    'link', 'unlink'
                ],
                extraFileUploadParams : {
                    relaTable: 'mktActInfo',
                    relaId: actId.getValue()
                },
                afterUpload : function(data) {

                },
                <%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
                allowFileManager : true,
                afterCreate : function() {
                }
            });
            prettyPrint();
        });


        window.onload = function () {
            //活动
            if (!LS.isEmpty(buildId.getValue())) {
                $('#buildListTr').show();
            }
            if (!LS.isEmpty(cityCodes.getValue())) {
                getCity();
            }

            if (!LS.isEmpty(stationIds.getValue())) {
                stationId.setValue("1");
                doSearchStation();
            }

            if (!LS.isEmpty(isImmediate.getValue()) && isImmediate.getValue() == '0') {
                $('#efftimeSpan').show();
            }
            if (buildType.getValue() == 0) {
                $("#buildListTr").hide();
                city.setReadOnly(true);
                city.setEnabled(false);
                stationId.setReadOnly(true);
                stationId.setEnabled(false);
                city.setValue('0');
                stationId.setValue('0');
            }
            if (city.getValue() == 0) {
                stationId.setReadOnly(true);
        stationId.setEnabled(false);
                stationId.setValue('0');
            }

            //优惠
            queryGrid();

            //资讯
            if (!LS.isEmpty(content.getValue())) {
                editor.html(content.getValue());
                contentUrl.setValue(content.getValue())
            }
            var _actInfoId = actInfoId.getValue();
            var _attachId = attachId.getValue();
            var _relaId = relaId.getValue();
            headImg.src = pubPath + '/pub/image/js/add.png';
            if (LS.isEmpty(_actInfoId) || LS.isEmpty(_attachId)) {
                headImg.src = pubPath + '/pub/image/js/add.png';
            } else {
                headImg.src = pubPath + '/attach/attachs/mkt_act_info/02/' + _relaId;
            }
            doInfoType();
        }
        window.actDctGrid = actDctGrid;
        window.queryGrid = queryGrid;

        function queryGrid() {
            var params = limDisCountForm.getFormData();
            actDctGrid.query("~/marketAct/limDisCountManage/queryActAllNew", params);
        }

        function addAct() {
            actDctGrid.addRow();
        }

        // 描述:删除  创建人:biaoxiangd  创建时间:2017/6/30 11:18
        function delAct() {
            var checkedItems = actDctGrid.getCheckedItems();
            if (checkedItems == null || checkedItems.length != 1) {
                LS.message("info", "请选择一条记录!");
                return;
            }
            var allItems = actDctGrid.getItems();
            if (allItems && allItems.length > 0) {
                for (var i = 0; i < allItems.length; i++) {
                    if (checkedItems[0].id == allItems[i].id) {
                        var _actDctProdId = allItems[i].actDctProdId;
                        var _actDctProdObj = actDctProdObj.getValue();
                        if (!LS.isEmpty(_actDctProdId) && !LS.isEmpty(_actDctProdObj)) {
                            var bk = _actDctProdObj.replace(_actDctProdId, '');
                            actDctProdObj.setValue(bk);
                        }
                        actDctGrid.removeItem(allItems[i]);
                    }
                }
            }
        }

        //描述:资讯内容变更  创建人:biaoxiangd  创建时间:2017/6/28 10:52
        function doInfoType() {
            var _infoType = infoType.getValue();
            if ('2' == _infoType) {
                $('#tr_content').hide();
                $('#tr_linkUrl').show();
            } else {
                $('#tr_content').show();
                $('#tr_linkUrl').hide();
            }
        }

        // 描述:添加图片  创建人:biaoxiangd  创建时间:2017/6/28 14:24
        window.ShowImage = function (file) {
            if ($.browser.msie) {
                $("#headImg").attr("src", $(this).val())
            } else {
                if (file.files || file.files[0] != undefined) {
                    var objUrl = getObjectURL(file.files[0]);
                    if (objUrl) {
                        $("#headImg").attr("src", objUrl);
                        picFlag.setValue("0");
                    }
                }
            }
        }

        // 描述:删除图片  创建人:biaoxiangd  创建时间:2017/6/28 14:21
        window.doDelPic = function () {
            var _picFlag = picFlag.getValue();
            if ('0' == _picFlag) {
                headImg.src = pubPath + '/pub/image/js/add.png';
                picFlag.setValue('1');
            }
        }

        function getObjectURL(file) {
            var url = null;
            if (file != undefined) {
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file);
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file);
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file);
                }
            }
            return url;
        }

        function getImmediate() {
            if (isImmediate.getValue() != 1) {
                isImmediate.setValue("0")
                $('#efftimeSpan').show();
            } else {
                $('#efftimeSpan').hide();
            }
        }

        //适用运营商改变
        function getBuildType() {
            if (buildType.getValue() == 0) {
                $("#buildListTr").hide();
                $("#cityListTr").hide();
                $("#cityListSpan").empty();
                station_grid.removeAllItems();
                $("#stationListTr").hide();
                $("#stationNumTr").hide();
                city.setReadOnly(true);
                stationId.setReadOnly(true);
                city.setEnabled(false);
                stationId.setEnabled(false);
                city.setValue('0');
                stationId.setValue('0');
                cityCodes.setValue("");
                stationIds.setValue("");
            }
            if (buildType.getValue() == 1) {
                city.setReadOnly(false);
        city.setEnabled(true);
                $("#buildListTr").show();
            }
        }

        function getBuild() {
            cityNameStr = "";
            city.setValue("0");
            stationId.setValue("0");
            $("#cityListTr").hide();
            $("#cityListSpan").empty();
        }

        function getCity() {
            if(city.getValue() == "0,1"){
                city.setValue(1);
            }

            station_grid.removeAllItems();
            stationId.setValue("0");
            $("#stationListTr").hide();
            $("#stationNumTr").hide();
            if (city.getValue() == 1) {
                stationId.setReadOnly(false);
                stationId.setEnabled(true);
                var buildIdValue = buildId.getValue();
                if (buildIdValue == '' || buildIdValue == null) {
                    LS.message("info", "请选择运营商！");
                    city.setValue("0");
                    return;
                }
                var x1 = document.getElementById("cityListSpan").innerText;
                cityNameStr = cityNameStr.replace(/\s*/g, "");
                x1 = x1.replace(/\s*/g, "");

                var cityCodesValue = cityCodes.getValue();
                var cityArray = new Array(); //定义一数组
                if (!LS.isEmpty(cityCodesValue)) {
                    cityArray = cityCodesValue.split(",");
                }

                if (cityNameStr == "" && x1 == "") {
                    //根据运营商获取城市
                    LS.ajax("~/markertAct/getCityByBuild?buildNo=" + buildIdValue, null, function (e) {
                        if (e.items.length == 0) {
                            LS.message("info", "该运营商下没有站点和城市！");
                            return;
                        } else {
                            $("#cityListSpan").empty();
                            for (var i = 0; i < e.items.length; i++) {
                                cityNameStr += e.items[i].AREA_NAME;
                                var checkNum = 0;
                                for (var j = 0; j < cityArray.length; j++) {
                                    if (cityArray[j] == e.items[i].AREA_CODE) {
                                        checkNum++;
                                    }
                                }
                                if (checkNum > 0) {
                                    $("#cityListSpan").append(
                                        '<input id="checkbox_' + i + '" type="checkbox" checked="checked" name="cityIdBox" onclick="doSearchStation()" value="' + e.items[i].AREA_CODE + '">' +
                                        '<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
                                } else {

                                    $("#cityListSpan").append(
                                        '<input id="checkbox_' + i + '" type="checkbox" name="cityIdBox" onclick="doSearchStation()" value="' + e.items[i].AREA_CODE + '">' +
                                        '<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
                                }
                            }
                            $("#cityListTr").show();
                        }

                    });
                }
                if (cityNameStr != "" && cityNameStr != null && cityNameStr != x1) {
                    //根据运营商获取城市
                    LS.ajax("~/markertAct/getCityByBuild?buildNo=" + buildIdValue, null, function (e) {
                        if (e.items.length == 0) {
                            LS.message("info", "该运营商下没有站点和城市！");
                            return;
                        } else {
                            $("#cityListSpan").empty();
                            for (var i = 0; i < e.items.length; i++) {
                                cityNameStr += e.items[i].AREA_NAME;
                                var checkNum = 0;
                                for (var j = 0; j < cityArray.length; j++) {
                                    if (cityArray[j] == e.items[i].AREA_CODE) {
                                        checkNum++;
                                    }
                                }
                                if (checkNum > 0) {
                                    $("#cityListSpan").append(
                                        '<input id="checkbox_' + i + '" type="checkbox" checked="checked" onclick="doSearchStation()" name="cityIdBox" value="' + e.items[i].AREA_CODE + '">' +
                                        '<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
                                } else {

                                    $("#cityListSpan").append(
                                        '<input id="checkbox_' + i + '" type="checkbox" name="cityIdBox" onclick="doSearchStation()" value="' + e.items[i].AREA_CODE + '">' +
                                        '<label for="checkbox_' + i + '">' + e.items[i].AREA_NAME + '</label>');
                                }
                            }
                            $("#cityListTr").show();
                        }

                    });
                }
                if (cityNameStr != "" && cityNameStr != null && cityNameStr == x1) {
                    $("#cityListTr").show();
                }
            } else {
                $("#cityListTr").hide();
                stationId.setReadOnly(true);

        stationId.setEnabled(false);
                stationId.setValue('0');
            }
        }


        window.doSearchStation = doSearchStation;

        function doSearchStation() {
            station_grid.removeAllItems();
            var cityValue = city.getValue();
            var buildIdValue = buildId.getValue();
            var stationIdValue = stationId.getValue();
            if(stationId.getValue() == "0,1"){
                stationId.setValue(1);
            }
            station_grid.removeAllItems();
            var obj = document.getElementsByName("cityIdBox");
            var cityCodesValue = cityCodes.getValue();
            if (stationIdValue == 1) {
                var t1 = window.setInterval(getStationGridSelectNum, 1000);
            }
            if (cityValue == 0) {
                for (k in obj) {
                    cityCodesValue += obj[k].value + ",";
                }
                if (stationIdValue == 1) {
                    if (cityCodesValue != "" && cityCodesValue != null) {
                        $("#stationListTr").show();
                        $("#stationNumTr").show();
                        station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue}, function () {
                            if (!LS.isEmpty(stationIds.getValue())) {
                                var stationIdsValue = stationIds.getValue();
                                var stationArray = new Array();
                                stationArray = stationIdsValue.split(",");
                                var allItem = station_grid.getItems();
                                for (var i = 0; i < allItem.length; i++) {
                                    for (var j = 0; j < stationArray.length; j++) {
                                        if (allItem[i].stationId == stationArray[j]) {
                                            allItem[i].checked = true;
                                            station_grid.selectItem(allItem[i].stationId);
                                        }
                                    }
                                }
                            }

                        });
                    } else {
                        LS.message("info", "请选择城市！");
                        return;
                    }
                } else {
                    $("#stationListTr").hide();
                    $("#stationNumTr").hide();
                }

            } else {
                if (stationIdValue == 1) {
                    for (k in obj) {
                        if (obj[k].checked)
                            cityCodesValue += obj[k].value + ",";
                    }
                    if (cityCodesValue != "" && cityCodesValue != null) {
                        $("#stationListTr").show();
                        $("#stationNumTr").show();
                        station_grid.query('~/markertAct/getStationList', {cityCodes: cityCodesValue,buildId:buildIdValue}, function () {
                            if (!LS.isEmpty(stationIds.getValue())) {
                                var stationIdsValue = stationIds.getValue();
                                var stationArray = new Array();
                                stationArray = stationIdsValue.split(",");
                                var allItem = station_grid.getItems();
                                for (var i = 0; i < allItem.length; i++) {
                                    for (var j = 0; j < stationArray.length; j++) {
                                        if (allItem[i].stationId == stationArray[j]) {
                                            allItem[i].checked = true;
                                            station_grid.selectItem(allItem[i].stationId);
                                        }
                                    }
                                }
                            }

                        });

                    } else {
                        LS.message("info", "请选择城市！");
                        return;
                    }
                } else {
                    $("#stationListTr").hide();
                    $("#stationNumTr").hide();
                }
            }
        }

        //保存
        function baocun() {
            release.setValue("false");
            doSave(1);
        }

        //发布
        function fabu() {
            release.setValue("true");

            var imme = isImmediate.getValue();
            var eff = effTime.getValue();
            var exp = expTime.getValue();
            var nowDate = new Date();
            var current = (nowDate).Format("yyyy-MM-dd hh:mm");

            var imgSrcValue = document.getElementById("headImg").src;
                //alert(imgSrcValue);
            if(!LS.isEmpty(imgSrcValue) && imgSrcValue.indexOf("/pub/image/js/add.png")>=1){
                LS.message("info", "请上传图片封面！");
                return;
            }

            //1、立即生效：生效时间为当前，失效时间大于当前时间1分钟，状态进行中。
            if (imme == 1) {
                effTime.setValue(current)
            }
            if (imme == 1 && new Date(exp) - nowDate < 60 * 1000) {
                effTime.setValue(current)
                LS.message("info", "立即生效时，失效时间必须大于当前时间1分钟！");
                return;
            }
            //2、无立即生效：生效时间大于当前时间30分钟，失效时间大于生效时间时间1分钟，状态未开始
            if (imme == 0 && LS.isEmpty(eff)) {
                LS.message("info", "请选择生效时间！");
                return;
            }
            if (imme == 0 && new Date(eff) - nowDate < 30 * 60 * 1000) {
                LS.message("info", "生效时间必须大于当前时间30分钟！");
                return;
            }

            if (imme == 0 && new Date(exp) - new Date(eff) < 60 * 1000) {
                LS.message("info", "失效时间必须大于生效时间时1分钟！");
                return;
            }
            if (!LS.isEmpty(exp) && current > exp) {
                LS.message("info", "失效时间不能小于当前时间。");
                return;
            }

            if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff > exp) {
                LS.message("info", "生效时间不能大于失效时间。");
                return;
            }
            doSave(2);
        }

        function doSave(opType) {
            //将富文本内容设置回文本框
            var html = editor.html();
            content.setValue(html);
            contentUrl.setValue(html);

            var imme = isImmediate.getValue();
            var eff = effTime.getValue();
            var exp = expTime.getValue();
            var buildTypeValue = buildType.getValue();
            var buildIdValue = buildId.getValue();
            var cityType = city.getValue();
            var stationType = stationId.getValue();
            var cityCodesValue = "";
            var stationIdsValue = "";
            var actNameValue = actName.getValue();
            var actmarkValue = actMarks.getValue();

            <%--if (!marketactForm.valid()) {
                LS.message("info", "必填项不能为空！");
                return;
            }--%>

            if (LS.isEmpty(actNameValue)) {
                LS.message("info", "请输入活动名称！");
                return;
            }
            if (!LS.isEmpty(actNameValue) && actNameValue.length > 32) {
                LS.message("info", "活动名称过长，请重新输入！");
                return;
            }
            if (isImmediate.getValue() == 0 && LS.isEmpty(eff)) {
                LS.message("info", "请选择生效时间！");
                return;
            }
            if (LS.isEmpty(exp)) {
                LS.message("info", "请选择失效时间！");
                return;
            }
            if (LS.isEmpty(custType.getValue())) {
                LS.message("info", "请选择适用对象！");
                return;
            }
            if (LS.isEmpty(actmarkValue)) {
                LS.message("info", "活动描述不能为空！");
                return;
            }

            if (buildTypeValue == 0) {
                if (cityType != 0 || stationType != 0) {
                    LS.message("info", "运营商选择全部时，城市和站点只能选择全部。");
                    return;
                }
            } else {
                if (buildIdValue == "" && buildIdValue == null) {
                    LS.message("info", "请选择一个运营商");
                    return;
                }
                if (cityType == 1) {
                    var obj = document.getElementsByName("cityIdBox");

                    for (k in obj) {
                        if (obj[k].checked)
                            cityCodesValue += obj[k].value + ",";
                    }
                    if (cityCodesValue == "" || cityCodesValue == null) {
                        LS.message("info", "城市选择部分时，要勾选城市");
                        return;
                    }
                }
                if (stationType == 1) {
                    var item = station_grid.getCheckedItems();
                    if (item.length == 0) {
                        LS.message("info", "站点为部分时，请选择至少一条站点!");
                        return;
                    }
                    for (var i = 0; i < item.length; i++) {
                        stationIdsValue += item[i].stationId + "_" + item[i].city + ",";
                    }
                }
            }

            cityCodes.setValue(cityCodesValue);
            stationIds.setValue(stationIdsValue);
            //优惠内容
            if (!limDisCountForm.valid()) {
                LS.message("info", "必填项不能为空！");
                return false;
            }
            var dataParams = limDisCountForm.getFormData();
            var changeItems = actDctGrid.getItems();
            if (changeItems.length==0) {
                LS.message("info", "最少要添加一个优惠内容");
                return false;
            }
            for (var i = 0; i < changeItems.length; i++) {
                if (LS.isEmpty(changeItems[i].actDctProdName) || LS.isEmpty(changeItems[i].dctValue)) {
                    LS.message("info", "存在未选择的优惠内容或者优惠数值");
                    return false;
                }
                var _dctValue = Number(changeItems[i].dctValue);
                if (_dctValue<=0 || _dctValue>=10) {
                    LS.message("info", "优惠数值必须在0-10之间");
                    return false;
                }

                if (_dctValue%1!=0) {
                    LS.message("info", "优惠数值必须是整数");
                    return false;
                }

                for (var j = i+1; j < changeItems.length; j++) {
                    if(changeItems[i].actDctProdId==changeItems[j].actDctProdId){
                        LS.message("info", "优惠内容不可重复");
                        return false;
                    }
                }
            }
            dataParams.detItems = changeItems;
            youhui.setValue(JSON.stringify(dataParams));

            //资讯内容
            var _infoType = infoType.getValue();
            if ('1' == _infoType) {
                if (LS.isEmpty(content.getValue())) {
                    LS.message("error", "发布活动资讯信息中的内容为空");
                    return;
                }
                linkUrl.setValue("");
            } else {
                if (LS.isEmpty(linkUrl.getValue())) {
                    LS.message("error", "发布活动资讯信息中的外部链接为空");
                    return;
                }
                content.setValue("");
                contentUrl.setValue("");
            }
            var infoDate = actinfoForm.getFormData()
            info.setValue(JSON.stringify(infoDate));
            var marDateParams = marketactForm.getFormData();
            marketactForm.submit("~/markertAct/marketacts/saving", function (e) {
                var result = e.items[0];
                if (result == "T") {
                    if(opType==1){
                        LS.message("info", "保存成功！");
                    }
                    if(opType==2){
                        LS.message("info", "发布成功！");
                    }

                    LS.parent().doSearch();
                    LS.window.close();
                }else if(result.indexOf("时间冲突")>0){
                    LS.message("info", e.items[0]);
                }else {
                    LS.message("info", "操作失败或网络异常，请重试！");
                }
            });
        }
    </ls:script>
</ls:body>
</html>