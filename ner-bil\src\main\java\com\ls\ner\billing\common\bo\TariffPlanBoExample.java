package com.ls.ner.billing.common.bo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TariffPlanBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public TariffPlanBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andPlanNoIsNull() {
            addCriterion("PLAN_NO is null");
            return (Criteria) this;
        }

        public Criteria andPlanNoIsNotNull() {
            addCriterion("PLAN_NO is not null");
            return (Criteria) this;
        }

        public Criteria andPlanNoEqualTo(String value) {
            addCriterion("PLAN_NO =", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoNotEqualTo(String value) {
            addCriterion("PLAN_NO <>", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoGreaterThan(String value) {
            addCriterion("PLAN_NO >", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoGreaterThanOrEqualTo(String value) {
            addCriterion("PLAN_NO >=", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoLessThan(String value) {
            addCriterion("PLAN_NO <", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoLessThanOrEqualTo(String value) {
            addCriterion("PLAN_NO <=", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoLike(String value) {
            addCriterion("PLAN_NO like", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoNotLike(String value) {
            addCriterion("PLAN_NO not like", value, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoIn(List<String> values) {
            addCriterion("PLAN_NO in", values, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoNotIn(List<String> values) {
            addCriterion("PLAN_NO not in", values, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoBetween(String value1, String value2) {
            addCriterion("PLAN_NO between", value1, value2, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNoNotBetween(String value1, String value2) {
            addCriterion("PLAN_NO not between", value1, value2, "planNo");
            return (Criteria) this;
        }

        public Criteria andPlanNameIsNull() {
            addCriterion("PLAN_NAME is null");
            return (Criteria) this;
        }

        public Criteria andPlanNameIsNotNull() {
            addCriterion("PLAN_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andPlanNameEqualTo(String value) {
            addCriterion("PLAN_NAME =", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotEqualTo(String value) {
            addCriterion("PLAN_NAME <>", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameGreaterThan(String value) {
            addCriterion("PLAN_NAME >", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameGreaterThanOrEqualTo(String value) {
            addCriterion("PLAN_NAME >=", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLessThan(String value) {
            addCriterion("PLAN_NAME <", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLessThanOrEqualTo(String value) {
            addCriterion("PLAN_NAME <=", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameLike(String value) {
            addCriterion("PLAN_NAME like", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotLike(String value) {
            addCriterion("PLAN_NAME not like", value, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameIn(List<String> values) {
            addCriterion("PLAN_NAME in", values, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotIn(List<String> values) {
            addCriterion("PLAN_NAME not in", values, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameBetween(String value1, String value2) {
            addCriterion("PLAN_NAME between", value1, value2, "planName");
            return (Criteria) this;
        }

        public Criteria andPlanNameNotBetween(String value1, String value2) {
            addCriterion("PLAN_NAME not between", value1, value2, "planName");
            return (Criteria) this;
        }

        public Criteria andOperNoIsNull() {
            addCriterion("OPER_NO is null");
            return (Criteria) this;
        }

        public Criteria andOperNoIsNotNull() {
            addCriterion("OPER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andOperNoEqualTo(String value) {
            addCriterion("OPER_NO =", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoNotEqualTo(String value) {
            addCriterion("OPER_NO <>", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoGreaterThan(String value) {
            addCriterion("OPER_NO >", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoGreaterThanOrEqualTo(String value) {
            addCriterion("OPER_NO >=", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoLessThan(String value) {
            addCriterion("OPER_NO <", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoLessThanOrEqualTo(String value) {
            addCriterion("OPER_NO <=", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoLike(String value) {
            addCriterion("OPER_NO like", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoNotLike(String value) {
            addCriterion("OPER_NO not like", value, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoIn(List<String> values) {
            addCriterion("OPER_NO in", values, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoNotIn(List<String> values) {
            addCriterion("OPER_NO not in", values, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoBetween(String value1, String value2) {
            addCriterion("OPER_NO between", value1, value2, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNoNotBetween(String value1, String value2) {
            addCriterion("OPER_NO not between", value1, value2, "operNo");
            return (Criteria) this;
        }

        public Criteria andOperNameIsNull() {
            addCriterion("OPER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andOperNameIsNotNull() {
            addCriterion("OPER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andOperNameEqualTo(String value) {
            addCriterion("OPER_NAME =", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameNotEqualTo(String value) {
            addCriterion("OPER_NAME <>", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameGreaterThan(String value) {
            addCriterion("OPER_NAME >", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("OPER_NAME >=", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameLessThan(String value) {
            addCriterion("OPER_NAME <", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameLessThanOrEqualTo(String value) {
            addCriterion("OPER_NAME <=", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameLike(String value) {
            addCriterion("OPER_NAME like", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameNotLike(String value) {
            addCriterion("OPER_NAME not like", value, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameIn(List<String> values) {
            addCriterion("OPER_NAME in", values, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameNotIn(List<String> values) {
            addCriterion("OPER_NAME not in", values, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameBetween(String value1, String value2) {
            addCriterion("OPER_NAME between", value1, value2, "operName");
            return (Criteria) this;
        }

        public Criteria andOperNameNotBetween(String value1, String value2) {
            addCriterion("OPER_NAME not between", value1, value2, "operName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("CREATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("CREATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("CREATE_TIME =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("CREATE_TIME <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("CREATE_TIME >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("CREATE_TIME <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("CREATE_TIME <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("CREATE_TIME in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("CREATE_TIME not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("CREATE_TIME not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andPeBeIsNull() {
            addCriterion("PE_BE is null");
            return (Criteria) this;
        }

        public Criteria andPeBeIsNotNull() {
            addCriterion("PE_BE is not null");
            return (Criteria) this;
        }

        public Criteria andPeBeEqualTo(String value) {
            addCriterion("PE_BE =", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotEqualTo(String value) {
            addCriterion("PE_BE <>", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeGreaterThan(String value) {
            addCriterion("PE_BE >", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeGreaterThanOrEqualTo(String value) {
            addCriterion("PE_BE >=", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLessThan(String value) {
            addCriterion("PE_BE <", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLessThanOrEqualTo(String value) {
            addCriterion("PE_BE <=", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeLike(String value) {
            addCriterion("PE_BE like", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotLike(String value) {
            addCriterion("PE_BE not like", value, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeIn(List<String> values) {
            addCriterion("PE_BE in", values, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotIn(List<String> values) {
            addCriterion("PE_BE not in", values, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeBetween(String value1, String value2) {
            addCriterion("PE_BE between", value1, value2, "peBe");
            return (Criteria) this;
        }

        public Criteria andPeBeNotBetween(String value1, String value2) {
            addCriterion("PE_BE not between", value1, value2, "peBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNull() {
            addCriterion("SUB_BE is null");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNotNull() {
            addCriterion("SUB_BE is not null");
            return (Criteria) this;
        }

        public Criteria andSubBeEqualTo(String value) {
            addCriterion("SUB_BE =", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotEqualTo(String value) {
            addCriterion("SUB_BE <>", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThan(String value) {
            addCriterion("SUB_BE >", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThanOrEqualTo(String value) {
            addCriterion("SUB_BE >=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThan(String value) {
            addCriterion("SUB_BE <", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThanOrEqualTo(String value) {
            addCriterion("SUB_BE <=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLike(String value) {
            addCriterion("SUB_BE like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotLike(String value) {
            addCriterion("SUB_BE not like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIn(List<String> values) {
            addCriterion("SUB_BE in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotIn(List<String> values) {
            addCriterion("SUB_BE not in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeBetween(String value1, String value2) {
            addCriterion("SUB_BE between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotBetween(String value1, String value2) {
            addCriterion("SUB_BE not between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andChargeWayIsNull() {
            addCriterion("CHARGE_WAY is null");
            return (Criteria) this;
        }

        public Criteria andChargeWayIsNotNull() {
            addCriterion("CHARGE_WAY is not null");
            return (Criteria) this;
        }

        public Criteria andChargeWayEqualTo(String value) {
            addCriterion("CHARGE_WAY =", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotEqualTo(String value) {
            addCriterion("CHARGE_WAY <>", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayGreaterThan(String value) {
            addCriterion("CHARGE_WAY >", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_WAY >=", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLessThan(String value) {
            addCriterion("CHARGE_WAY <", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_WAY <=", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayLike(String value) {
            addCriterion("CHARGE_WAY like", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotLike(String value) {
            addCriterion("CHARGE_WAY not like", value, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayIn(List<String> values) {
            addCriterion("CHARGE_WAY in", values, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotIn(List<String> values) {
            addCriterion("CHARGE_WAY not in", values, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayBetween(String value1, String value2) {
            addCriterion("CHARGE_WAY between", value1, value2, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeWayNotBetween(String value1, String value2) {
            addCriterion("CHARGE_WAY not between", value1, value2, "chargeWay");
            return (Criteria) this;
        }

        public Criteria andChargeModeIsNull() {
            addCriterion("CHARGE_MODE is null");
            return (Criteria) this;
        }

        public Criteria andChargeModeIsNotNull() {
            addCriterion("CHARGE_MODE is not null");
            return (Criteria) this;
        }

        public Criteria andChargeModeEqualTo(String value) {
            addCriterion("CHARGE_MODE =", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotEqualTo(String value) {
            addCriterion("CHARGE_MODE <>", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeGreaterThan(String value) {
            addCriterion("CHARGE_MODE >", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_MODE >=", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLessThan(String value) {
            addCriterion("CHARGE_MODE <", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_MODE <=", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeLike(String value) {
            addCriterion("CHARGE_MODE like", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotLike(String value) {
            addCriterion("CHARGE_MODE not like", value, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeIn(List<String> values) {
            addCriterion("CHARGE_MODE in", values, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotIn(List<String> values) {
            addCriterion("CHARGE_MODE not in", values, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeBetween(String value1, String value2) {
            addCriterion("CHARGE_MODE between", value1, value2, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andChargeModeNotBetween(String value1, String value2) {
            addCriterion("CHARGE_MODE not between", value1, value2, "chargeMode");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueIsNull() {
            addCriterion("TIME_PER_PRICE_VALUE is null");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueIsNotNull() {
            addCriterion("TIME_PER_PRICE_VALUE is not null");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueEqualTo(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE =", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueNotEqualTo(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE <>", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueGreaterThan(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE >", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE >=", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueLessThan(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE <", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueLessThanOrEqualTo(Integer value) {
            addCriterion("TIME_PER_PRICE_VALUE <=", value, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueIn(List<Integer> values) {
            addCriterion("TIME_PER_PRICE_VALUE in", values, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueNotIn(List<Integer> values) {
            addCriterion("TIME_PER_PRICE_VALUE not in", values, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueBetween(Integer value1, Integer value2) {
            addCriterion("TIME_PER_PRICE_VALUE between", value1, value2, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceValueNotBetween(Integer value1, Integer value2) {
            addCriterion("TIME_PER_PRICE_VALUE not between", value1, value2, "timePerPriceValue");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitIsNull() {
            addCriterion("TIME_PER_PRICE_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitIsNotNull() {
            addCriterion("TIME_PER_PRICE_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitEqualTo(String value) {
            addCriterion("TIME_PER_PRICE_UNIT =", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitNotEqualTo(String value) {
            addCriterion("TIME_PER_PRICE_UNIT <>", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitGreaterThan(String value) {
            addCriterion("TIME_PER_PRICE_UNIT >", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitGreaterThanOrEqualTo(String value) {
            addCriterion("TIME_PER_PRICE_UNIT >=", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitLessThan(String value) {
            addCriterion("TIME_PER_PRICE_UNIT <", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitLessThanOrEqualTo(String value) {
            addCriterion("TIME_PER_PRICE_UNIT <=", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitLike(String value) {
            addCriterion("TIME_PER_PRICE_UNIT like", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitNotLike(String value) {
            addCriterion("TIME_PER_PRICE_UNIT not like", value, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitIn(List<String> values) {
            addCriterion("TIME_PER_PRICE_UNIT in", values, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitNotIn(List<String> values) {
            addCriterion("TIME_PER_PRICE_UNIT not in", values, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitBetween(String value1, String value2) {
            addCriterion("TIME_PER_PRICE_UNIT between", value1, value2, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andTimePerPriceUnitNotBetween(String value1, String value2) {
            addCriterion("TIME_PER_PRICE_UNIT not between", value1, value2, "timePerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueIsNull() {
            addCriterion("MILL_PER_PRICE_VALUE is null");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueIsNotNull() {
            addCriterion("MILL_PER_PRICE_VALUE is not null");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueEqualTo(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE =", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueNotEqualTo(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE <>", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueGreaterThan(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE >", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE >=", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueLessThan(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE <", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueLessThanOrEqualTo(Integer value) {
            addCriterion("MILL_PER_PRICE_VALUE <=", value, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueIn(List<Integer> values) {
            addCriterion("MILL_PER_PRICE_VALUE in", values, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueNotIn(List<Integer> values) {
            addCriterion("MILL_PER_PRICE_VALUE not in", values, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueBetween(Integer value1, Integer value2) {
            addCriterion("MILL_PER_PRICE_VALUE between", value1, value2, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceValueNotBetween(Integer value1, Integer value2) {
            addCriterion("MILL_PER_PRICE_VALUE not between", value1, value2, "millPerPriceValue");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitIsNull() {
            addCriterion("MILL_PER_PRICE_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitIsNotNull() {
            addCriterion("MILL_PER_PRICE_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitEqualTo(String value) {
            addCriterion("MILL_PER_PRICE_UNIT =", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitNotEqualTo(String value) {
            addCriterion("MILL_PER_PRICE_UNIT <>", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitGreaterThan(String value) {
            addCriterion("MILL_PER_PRICE_UNIT >", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitGreaterThanOrEqualTo(String value) {
            addCriterion("MILL_PER_PRICE_UNIT >=", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitLessThan(String value) {
            addCriterion("MILL_PER_PRICE_UNIT <", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitLessThanOrEqualTo(String value) {
            addCriterion("MILL_PER_PRICE_UNIT <=", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitLike(String value) {
            addCriterion("MILL_PER_PRICE_UNIT like", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitNotLike(String value) {
            addCriterion("MILL_PER_PRICE_UNIT not like", value, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitIn(List<String> values) {
            addCriterion("MILL_PER_PRICE_UNIT in", values, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitNotIn(List<String> values) {
            addCriterion("MILL_PER_PRICE_UNIT not in", values, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitBetween(String value1, String value2) {
            addCriterion("MILL_PER_PRICE_UNIT between", value1, value2, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andMillPerPriceUnitNotBetween(String value1, String value2) {
            addCriterion("MILL_PER_PRICE_UNIT not between", value1, value2, "millPerPriceUnit");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsIsNull() {
            addCriterion("PERIOD_SPLIT_POINTS is null");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsIsNotNull() {
            addCriterion("PERIOD_SPLIT_POINTS is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsEqualTo(String value) {
            addCriterion("PERIOD_SPLIT_POINTS =", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsNotEqualTo(String value) {
            addCriterion("PERIOD_SPLIT_POINTS <>", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsGreaterThan(String value) {
            addCriterion("PERIOD_SPLIT_POINTS >", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsGreaterThanOrEqualTo(String value) {
            addCriterion("PERIOD_SPLIT_POINTS >=", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsLessThan(String value) {
            addCriterion("PERIOD_SPLIT_POINTS <", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsLessThanOrEqualTo(String value) {
            addCriterion("PERIOD_SPLIT_POINTS <=", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsLike(String value) {
            addCriterion("PERIOD_SPLIT_POINTS like", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsNotLike(String value) {
            addCriterion("PERIOD_SPLIT_POINTS not like", value, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsIn(List<String> values) {
            addCriterion("PERIOD_SPLIT_POINTS in", values, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsNotIn(List<String> values) {
            addCriterion("PERIOD_SPLIT_POINTS not in", values, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsBetween(String value1, String value2) {
            addCriterion("PERIOD_SPLIT_POINTS between", value1, value2, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andPeriodSplitPointsNotBetween(String value1, String value2) {
            addCriterion("PERIOD_SPLIT_POINTS not between", value1, value2, "periodSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsIsNull() {
            addCriterion("MILL_STEP_SPLIT_POINTS is null");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsIsNotNull() {
            addCriterion("MILL_STEP_SPLIT_POINTS is not null");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsEqualTo(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS =", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsNotEqualTo(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS <>", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsGreaterThan(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS >", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsGreaterThanOrEqualTo(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS >=", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsLessThan(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS <", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsLessThanOrEqualTo(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS <=", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsLike(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS like", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsNotLike(String value) {
            addCriterion("MILL_STEP_SPLIT_POINTS not like", value, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsIn(List<String> values) {
            addCriterion("MILL_STEP_SPLIT_POINTS in", values, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsNotIn(List<String> values) {
            addCriterion("MILL_STEP_SPLIT_POINTS not in", values, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsBetween(String value1, String value2) {
            addCriterion("MILL_STEP_SPLIT_POINTS between", value1, value2, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andMillStepSplitPointsNotBetween(String value1, String value2) {
            addCriterion("MILL_STEP_SPLIT_POINTS not between", value1, value2, "millStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsIsNull() {
            addCriterion("TIME_STEP_SPLIT_POINTS is null");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsIsNotNull() {
            addCriterion("TIME_STEP_SPLIT_POINTS is not null");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsEqualTo(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS =", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsNotEqualTo(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS <>", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsGreaterThan(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS >", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsGreaterThanOrEqualTo(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS >=", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsLessThan(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS <", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsLessThanOrEqualTo(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS <=", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsLike(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS like", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsNotLike(String value) {
            addCriterion("TIME_STEP_SPLIT_POINTS not like", value, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsIn(List<String> values) {
            addCriterion("TIME_STEP_SPLIT_POINTS in", values, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsNotIn(List<String> values) {
            addCriterion("TIME_STEP_SPLIT_POINTS not in", values, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsBetween(String value1, String value2) {
            addCriterion("TIME_STEP_SPLIT_POINTS between", value1, value2, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andTimeStepSplitPointsNotBetween(String value1, String value2) {
            addCriterion("TIME_STEP_SPLIT_POINTS not between", value1, value2, "timeStepSplitPoints");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosIsNull() {
            addCriterion("DEPOSIT_ITEM_NOS is null");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosIsNotNull() {
            addCriterion("DEPOSIT_ITEM_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosEqualTo(String value) {
            addCriterion("DEPOSIT_ITEM_NOS =", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosNotEqualTo(String value) {
            addCriterion("DEPOSIT_ITEM_NOS <>", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosGreaterThan(String value) {
            addCriterion("DEPOSIT_ITEM_NOS >", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosGreaterThanOrEqualTo(String value) {
            addCriterion("DEPOSIT_ITEM_NOS >=", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosLessThan(String value) {
            addCriterion("DEPOSIT_ITEM_NOS <", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosLessThanOrEqualTo(String value) {
            addCriterion("DEPOSIT_ITEM_NOS <=", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosLike(String value) {
            addCriterion("DEPOSIT_ITEM_NOS like", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosNotLike(String value) {
            addCriterion("DEPOSIT_ITEM_NOS not like", value, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosIn(List<String> values) {
            addCriterion("DEPOSIT_ITEM_NOS in", values, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosNotIn(List<String> values) {
            addCriterion("DEPOSIT_ITEM_NOS not in", values, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosBetween(String value1, String value2) {
            addCriterion("DEPOSIT_ITEM_NOS between", value1, value2, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andDepositItemNosNotBetween(String value1, String value2) {
            addCriterion("DEPOSIT_ITEM_NOS not between", value1, value2, "depositItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosIsNull() {
            addCriterion("ATTACH_ITEM_NOS is null");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosIsNotNull() {
            addCriterion("ATTACH_ITEM_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosEqualTo(String value) {
            addCriterion("ATTACH_ITEM_NOS =", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosNotEqualTo(String value) {
            addCriterion("ATTACH_ITEM_NOS <>", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosGreaterThan(String value) {
            addCriterion("ATTACH_ITEM_NOS >", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosGreaterThanOrEqualTo(String value) {
            addCriterion("ATTACH_ITEM_NOS >=", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosLessThan(String value) {
            addCriterion("ATTACH_ITEM_NOS <", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosLessThanOrEqualTo(String value) {
            addCriterion("ATTACH_ITEM_NOS <=", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosLike(String value) {
            addCriterion("ATTACH_ITEM_NOS like", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosNotLike(String value) {
            addCriterion("ATTACH_ITEM_NOS not like", value, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosIn(List<String> values) {
            addCriterion("ATTACH_ITEM_NOS in", values, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosNotIn(List<String> values) {
            addCriterion("ATTACH_ITEM_NOS not in", values, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosBetween(String value1, String value2) {
            addCriterion("ATTACH_ITEM_NOS between", value1, value2, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andAttachItemNosNotBetween(String value1, String value2) {
            addCriterion("ATTACH_ITEM_NOS not between", value1, value2, "attachItemNos");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkIsNull() {
            addCriterion("PLAN_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkIsNotNull() {
            addCriterion("PLAN_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkEqualTo(String value) {
            addCriterion("PLAN_REMARK =", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkNotEqualTo(String value) {
            addCriterion("PLAN_REMARK <>", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkGreaterThan(String value) {
            addCriterion("PLAN_REMARK >", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("PLAN_REMARK >=", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkLessThan(String value) {
            addCriterion("PLAN_REMARK <", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkLessThanOrEqualTo(String value) {
            addCriterion("PLAN_REMARK <=", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkLike(String value) {
            addCriterion("PLAN_REMARK like", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkNotLike(String value) {
            addCriterion("PLAN_REMARK not like", value, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkIn(List<String> values) {
            addCriterion("PLAN_REMARK in", values, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkNotIn(List<String> values) {
            addCriterion("PLAN_REMARK not in", values, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkBetween(String value1, String value2) {
            addCriterion("PLAN_REMARK between", value1, value2, "planRemark");
            return (Criteria) this;
        }

        public Criteria andPlanRemarkNotBetween(String value1, String value2) {
            addCriterion("PLAN_REMARK not between", value1, value2, "planRemark");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNull() {
            addCriterion("DATA_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNotNull() {
            addCriterion("DATA_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE =", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <>", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThan(String value) {
            addCriterion("DATA_OPER_TYPE >", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE >=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThan(String value) {
            addCriterion("DATA_OPER_TYPE <", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLike(String value) {
            addCriterion("DATA_OPER_TYPE like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotLike(String value) {
            addCriterion("DATA_OPER_TYPE not like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE not in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE not between", value1, value2, "dataOperType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_tariff_plan
     *
     * @mbggenerated do_not_delete_during_merge Thu Mar 10 20:29:22 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_tariff_plan
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}