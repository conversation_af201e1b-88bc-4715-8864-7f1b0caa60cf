package com.ls.ner.billing.common.bo;

import java.util.Date;

public class BillingFuncBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_func.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_func.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_func.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_func.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_func.CONTENT
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_func.SYSTEM_ID
     *
     * @return the value of e_billing_func.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_func.SYSTEM_ID
     *
     * @param systemId the value for e_billing_func.SYSTEM_ID
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_func.BILLING_NO
     *
     * @return the value of e_billing_func.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_func.BILLING_NO
     *
     * @param billingNo the value for e_billing_func.BILLING_NO
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setBillingNo(String billingNo) {
        this.billingNo = billingNo == null ? null : billingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_func.DATA_OPER_TYPE
     *
     * @return the value of e_billing_func.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_func.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_billing_func.DATA_OPER_TYPE
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_func.DATA_OPER_TIME
     *
     * @return the value of e_billing_func.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_func.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_billing_func.DATA_OPER_TIME
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_func.CONTENT
     *
     * @return the value of e_billing_func.CONTENT
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_func.CONTENT
     *
     * @param content the value for e_billing_func.CONTENT
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}