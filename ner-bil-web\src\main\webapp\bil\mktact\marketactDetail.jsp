<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib prefix="ner" uri="http://www.longshine.com/taglib/ner" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑活动">
	<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/themes/default/default.css"/>
	<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.css"/>
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/kindeditor-all.js"></script>
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/lang/zh-CN.js"></script>
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.js"></script>
</ls:head>
<ner:head/>
<style>
	.gridTip{
		color: #0066FF;
		height: 30px;
		text-align: right;
		padding-right: 40px;
		margin-top: 5px;
	}
</style>
<script type="text/javascript" src="../bil/comm/js/validation.js"></script>
<ls:body>
	<ls:form id="marketactForm" name="marketactForm">
		<ls:text name="actId" property="actId" type="hidden"/>
		<ls:text name="fileId" property="fileId" type="hidden"/>
		<ls:text name="actSubType" property="actSubType" type="hidden"/>
		<ls:text name="presentSectionId" property="presentSectionId" type="hidden"/>
		<ls:text name="presentBalType" property="presentBalType" type="hidden"/>
		<ls:text name="actCondId" property="actCondId" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="活动名称" ref="actName"/></td>
				<td><ls:text name="actName" property="actName" readOnly="true"/></td>
				<td><ls:label text="活动标签" ref="actLbl"/></td>
				<td><ls:text name="actLbl" property="actLbl" readOnly="true"/></td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="生效时间" ref="effTime"/></td>
				<td>
					<ls:text name="effTime" property="effTime" readOnly="true"/>
				</td>
				<td><ls:label text="失效时间" ref="expTime"/></td>
				<td>
					<ls:text name="expTime" property="expTime" readOnly="true"/>
				</td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="活动描述" ref="actMarks"/></td>
				<td colspan="3">
					<textarea style="height:255px;" id="actMarks"></textarea>
					<ls:text name="actMarks" property="actMarks" visible="false" type="textarea" readOnly="true"/>
				</td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="活动渠道" ref="actChannel"/></td>
				<td colspan="4">
					<ls:checklist name="actChannel" property="actChannel" type="checkbox" readOnly="true" columns="10">
						<ls:checkitems property="channelCheckList" scope="request" text="text" value="value"/>
					</ls:checklist>
				</td>
			</tr>
			<tr>
				<td><ls:label text="活动类型" ref="actType"/></td>
				<td>
					<ls:select name="actType" property="actType" readOnly="true">
						<ls:options property="actTypeList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td><ls:label text="活动状态" ref="actState"/></td>
				<td>
					<ls:select name="actState" property="actState" readOnly="true">
						<ls:options property="actStateList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td></td>
			</tr>
			<tr >
				<td><ls:label text="活动图片"/></td>
				<td id="image" ></td>
			</tr>
			<tr>
				<td>
					<ls:label text="活动详情描述" ref="actDetailMarks"/>
				</td>
				<td colspan="3">
					<textarea style="height:255px;" id="actDetailMarks" readOnly="true"></textarea>
					<ls:text name="actDetailMarks" visible="false" property="actDetailMarks" type="textarea" readOnly="true" maxlength="1024"
							 ></ls:text>
				</td>
				<td></td>
			</tr>


			<tr class="act-condition-03">
				<td><ls:label text="优惠方式" /></td>
				<td>
					<ls:checklist type="radio" name="dctLvl" value="1" property="dctLvl" readOnly="true">
						<ls:checkitem value="0" text="普通" ></ls:checkitem>
						<ls:checkitem value="1" text="多级(5级)" ></ls:checkitem>
					</ls:checklist>
				</td>

				<td><ls:label text="优惠条件类别"/></td>
				<td>
					<ls:select  name="dctCondType" property="dctCondType" readOnly="true">
						<ls:options property="dctCondTypeList" scope="request" text="codeName" value="codeValue"></ls:options>
					</ls:select>
				</td>
				<td></td>
			</tr>

			<tr class="act-condition-03">
				<td><ls:label text="适用用户"/></td>
				<td>
					<ls:checklist type="radio" name="appUser" value="03" property="appUser" readOnly="true">
						<ls:checkitem value="03" text="全部" ></ls:checkitem>
						<ls:checkitem value="01" text="个人" ></ls:checkitem>
						<ls:checkitem value="02" text="企业" ></ls:checkitem>
					</ls:checklist>
				</td>

				<td><ls:label text="是否同时享受企业计费"/></td>
				<td>
					<ls:checklist type="radio" name="pBillFlag" value="0" property="pBillFlag" readOnly="true">
						<ls:checkitem value="0" text="否" ></ls:checkitem>
						<ls:checkitem value="1" text="是" ></ls:checkitem>
					</ls:checklist>
				</td>
				<td></td>
			</tr>
			<tr class="act-condition-03">
				<td><ls:label text="适用站点"/></td>
				<td>
					<ls:checklist type="radio" name="allStationFlag" value="1" property="allStationFlag" readOnly="true">
						<ls:checkitem value="1" text="全部" ></ls:checkitem>
						<ls:checkitem value="0" text="部分" ></ls:checkitem>
					</ls:checklist>
				</td>
				<td colspan="3"></td>
			</tr>
		</table>
		<div id="bankTip" class="gridTip" style="display: none"></div>
		<table id="couponTab" align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="couponGrid" caption="优惠券列表" showRowNumber="false" width="100%"
							 height="350px" showCheckBox="false" singleSelect="true">
					<ls:column caption="优惠券编号" name="cpnNo" hidden="true"/>
					<ls:column caption="主键" name="cpnId" hidden="true"/>
					<ls:column caption="优惠券名称" name="cpnName" formatFunc="cpnFunc" align="center" width="20%"/>
					<ls:column caption="优惠券类型" name="cpnType" align="center" hidden="true"/>
					<ls:column caption="优惠券类型" name="cpnTypeName" align="center" width="8%"/>
					<ls:column caption="优惠券内容" name="busiType" align="center" hidden="true"/>
					<ls:column caption="优惠券内容" name="busiTypeName" align="center" width="8%"/>
					<ls:column caption="面额/折扣" name="cpnAmt" align="center" width="10%"/>
					<ls:column caption="发行数量" name="cpnNum" align="center" width="6%"/>
					<ls:column caption="已领数量" hidden="true" name="alrdyGetNum" align="center" width="6%"/>
					<ls:column caption="使用数量" hidden="true" name="useCount" align="center" width="6%"/>
					<ls:column caption="使用金额/(元)" hidden="true" name="useMoney" align="center" width="6%"/>
					<ls:pager pageSize="15,20"></ls:pager>
				</ls:grid>
				</td>
			</tr>
		</table>
		<ls:title text="优惠券推广连接"></ls:title>
		<table class="tab_search"  style="height: 40px">
			<tr>
				<td width="20%">
					<ls:label text="是否仅通过链接获取优惠券" ref="isLink"/>
				</td>
				<td width="20%">

					<ls:checklist type="radio" name="isLink" property="isLink">
						<ls:checkitem value="1" text="是"></ls:checkitem>
						<ls:checkitem value="0" text="否"></ls:checkitem>
					</ls:checklist>

				</td>
				<td width="10%"></td>
				<td width="20%"></td>
				<td width="10%"></td>
				<td width="20%"></td>
			</tr>
		</table>
		<table id="stationGrid">
			<tr>
				<td>
					<ls:grid height="120px" caption="适用站点" url="" name="station_grid" showCheckBox="false"
							 primaryKey="stationId" allowSorting="true" singleSelect="false" treeGrid="true" treeGridModel="adjacency">
						<ls:column caption="站点类型" name="stationTypeName" hidden="true"></ls:column>
						<ls:column caption="站点名称" name="stationName"></ls:column>
					</ls:grid>
				</td>
			</tr>
		</table>
		<table id="actDctTab">
			<tr>
				<td>
					<ls:grid url="" name="actDctGrid" caption="优惠条件内容" showRowNumber="true"
							 height="240px" showCheckBox="false" singleSelect="true" treeGrid="true" treeGridModel="adjacency">
						<ls:column caption="优惠条件明细ID" name=" actCondDetId" hidden="true"></ls:column>
						<ls:column caption="优惠条件ID" name="actCondId" hidden="true"></ls:column>
						<ls:column caption="优惠内容ID" name="actDctId" hidden="true"></ls:column>
						<ls:column caption="优惠类别" name="dctType" hidden="true"></ls:column>
						<ls:column caption="优惠类别" name="dctTypeName"></ls:column>
						<ls:column caption="优惠条件" name="dctCondValue" ></ls:column>
						<ls:column caption="优惠对象" name="actDctProdId" hidden="true"></ls:column>
						<ls:column caption="优惠对象" name="actDctProdName" hidden="true"></ls:column>
						<ls:column caption="是否全免" name="allFree" ></ls:column>
						<ls:column caption="优惠数值" name="dctValue" ></ls:column>
					</ls:grid>
				</td>
			</tr>
		</table>

		<table id="limDisTab" class="tab_search">
			<tr>
				<td>
					<ls:grid url="" height="200px" name="limDisGrid" caption="优惠内容" showRowNumber="true" primaryKey="id">
						<ls:column name="actCondDetId" caption="优惠条件明细ID" hidden="true" ></ls:column>
						<ls:column caption="优惠对象" name="actDctProdName"/>
						<ls:column name="actDctAllFree" caption="" hidden="true"></ls:column>
						<ls:column name="dctValue" caption="优惠数值"/>
						<ls:column name="unitZ" caption="" width="15%" formatFunc="unitFunc"/>
					</ls:grid>
				</td>
			</tr>
		</table>
		<table id="sectionTab" style="display:none" align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="sectionGrid" height="80px;" width="100%"
							 showCheckBox="false" singleSelect="true" caption="段落列表" primaryKey="presentSectionId">
					<ls:column caption="" name="presentSectionId" hidden="true"/>
					<ls:column caption="段落名称" name="sectionName"/>
					<ls:column caption="段落关系" name="sectionRelaTypeName"/>
					<ls:column caption="段落类型" name="sectionTypeName"/>
					<ls:column caption="赠送余额类型" name="presentBalTypeName"/>
					<ls:column caption="赠送最大值" name="maxValueStr"/>
					<ls:column caption="赠送周期类型" name="presentCycleTypeName"/>
					<ls:column caption="有效开始时间" name="beginCalcObject" hidden="${showFlag == 'true' ? 'false':'true'}"/>
					<ls:column caption="有效结束时间" name="endCalcObject" hidden="${showFlag == 'true' ? 'false':'true'}"/>
				</ls:grid>
				</td>
			</tr>
		</table>
		<table id="sectionDetTr">
			<tr>
				<td>
					<ls:grid url="" caption="段落明细" name="sectionDetGrid" primaryKey="sectionDetSn" treeGrid="true"
							 expandColumn="value" expand="true" treeGridModel="sectionDet" height="100px" width="100%">
						<ls:column caption="预存段落ID" name="sectionDetId" editableEdit="false" hidden="true"/>
						<ls:column caption="排序号" name="sectionDetSn"/>
						<ls:column caption="起始值(>=)" name="refCeil"/>
						<ls:column caption="终止值(<)" name="refFloor"/>
						<ls:column caption="优惠券" name="cpnName" hidden="${showFlag == 'true' ? 'true':'false'}" formatFunc="cpnFunc"/>
						<ls:column caption="赠送值" name="baseValue"/>
						<ls:column caption="赠送积分" name="integralNum"/>
						<ls:column caption="优惠券" name="cpnId" hidden="true"/>
					</ls:grid>
				</td>
			</tr>
		</table>
		<table class="tab_search" style="display:none">
			<tr>
				<td width="100px"><ls:label text="封面图片:"/></td>
				<td><img id="appImg" alt="" src="" width="320" height="130">
					<ls:text name="infoImgUrl" property="infoImgUrl" visible="false"></ls:text></td>
			</tr>
			<tr>
				<td><ls:label text="资讯内容：" ref="infoType"/></td>
				<td>
					<ls:checklist type="radio" name="infoType" property="infoType" readOnly="true">
						<ls:checkitems property="infoTypeList" scope="request" text="text"
									   value="value"></ls:checkitems>
					</ls:checklist>
				</td>
			</tr>
			<tr id="tr_content">
				<td><ls:label text="活动描述："/></td>
				<td>
					<div id="metacontent">
					</div>
					<ls:text name="content" property="content" visible="false"></ls:text>
				</td>
			</tr>
			<tr id="tr_linkUrl">
				<td><ls:label text="链接网址："/></td>
				<td><ls:text name="linkUrl" property="linkUrl"/></td>
			</tr>
		</table>
		<table>
			<tr>
				<td>
					<div class="pull-right">
						<ls:button text="返回" onclick="doReturn"/>
					</div>
				</td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
		var _showFlag = '${showFlag}'
		var _showFlag1 = '${showFlag1}'
        var headImg = document.getElementById('appImg');
        actTypeChg();
        function actTypeChg() {
            var formData = marketactForm.getFormData();
            var _infoImgUrl = formData.infoImgUrl;
            headImg.src = _infoImgUrl;
			doInfoType();
            var actTypeValue = actType.getValue();
            var actIdValue = actId.getValue();
            var actSubTypeValue = actSubType.getValue();
            if (actTypeValue == "07") {
                sectionGrid.query('~/sections?actId=' + actIdValue + "&presentBalType=" + actSubTypeValue, null, function (e) {
                });
                $('#sectionTab').show();
            } else {
                $('#sectionTab').hide();
            }

			$('#stationGrid').hide();
			$('.act-condition-03').hide();
			if(actTypeValue == '03'){
				$('#stationGrid').show();
				$('.act-condition-03').show();
				doStationQuery();
			}
        }

		function doStationQuery(){
			var params =  {};
			params.actId = actId.getValue();
			station_grid.query("~/marketacts/firstFreeManage/qryActStation",params,function(result){
				if(result.resultValue.itemCount <= 0){
					$('#stationGrid').hide();
				}
			})
		}

		function doInfoType(){
			var _infoType = infoType.getValue();
			if('2' == _infoType){
				$('#tr_content').hide();
				$('#tr_linkUrl').show();
			}else{
				$('#tr_content').show();
				$('#tr_linkUrl').hide();
			}
		}
        function doReturn() {
            setTimeout("LS.window.close()", 200);
        }
        window.query = query;
        query();
        function query() {
            var actTypeValue = actType.getValue();
            if (actTypeValue == "07") {
                querySection();
                $('#sectionDetTr').show();
				$('#actDctTab').hide();
                $('#couponTab').hide();
                $('#limDisTab').hide();
            } else if (actTypeValue == "03") {
				queryActDct();
				$('#sectionDetTr').hide();
				$('#actDctTab').show();
				$('#couponTab').hide();
				$('#limDisTab').hide();
			} else if (actTypeValue == "02") {
				queryLimDis();
				$('#sectionDetTr').hide();
				$('#actDctTab').hide();
				$('#couponTab').hide();
				$('#limDisTab').show();
			} else {
                queryCpn();
                $('#sectionDetTr').hide();
                $('#actDctTab').hide();
                $('#limDisTab').hide();
                $('#couponTab').show();
            }
        }
		function queryLimDis(){
			var params =  marketactForm.getFormData();
			limDisGrid.query("~/marketacts/limDisCountManage/queryActAll",params);
		}
		function queryActDct(){
			var params =  marketactForm.getFormData();
			actDctGrid.query("~/marketacts/firstFreeManage/queryActDctAll",params);
		}
        function queryCpn() {
            var datas = {};
            var cpnList = "";
            var actId = marketactForm.getFormData().actId;
            //根据活动查询优惠卷的数量
            LS.ajax("~/queryCouponByActId/" + actId, '', function (result) {
                for (var i = 0; i < result.length; i++) {
                    cpnList = cpnList + "," + result[i].cpnId;
                }
                datas.cpnList = cpnList;
                if (datas.cpnList != null && datas.cpnList != "") {
                    couponGrid.query("~/billing/coupon/getCoupons", datas, function (obj) {
                        Calculation();
                    });
                }
            });
        }
        function querySection() {
            var actIdValue = actId.getValue();
            var balTypeValue = presentBalType.getValue();
            var sectionIdValue = presentSectionId.getValue();
            if (!LS.isEmpty(actIdValue) && sectionIdValue && balTypeValue) {
                sectionDetGrid.query('~/cpn/sections?actId=' + actIdValue + "&presentSectionId=" + sectionIdValue + "&presentBalType=" + balTypeValue, null);
            }
        }

        function Calculation() {
            var items = couponGrid.getItems();
            var money = 0;
            for (var i = 0; i < items.length; i++) {
                if (items[i].useMoney != null && items[i].useMoney != "") {
                    money = parseInt(money) + parseInt(items[i].useMoney);
                }
            }
            if (items.length != 0 && money != 0) {
				$("#bankTip").show();
                $("#bankTip").html("总金额: " + money + "元");
            }
        }



		var editor;
		var editor2;
		var pref = "<%=pubPath %>/pub/kindeditor";
		var pubPath = '${pubPath }';

		KindEditor.ready(function(K) {
		editor = K.create('#actDetailMarks', {
		cssPath : pref + '/plugins/code/prettify.css',
		uploadJson : pubPath + '/pub/picture/upload',
		items : [
		'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
		'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
		'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
		'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
		'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
		'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
		'table', 'hr', 'emoticons', 'pagebreak',
		'link', 'unlink'
		],
		extraFileUploadParams : {
		relaTable: 'pOpenInfo'
		},
		afterUpload : function(data) {

		},
		<%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
		allowFileManager : true,
		afterCreate : function() {
		}
		});

		editor2 = K.create('#actMarks', {
		cssPath : pref + '/plugins/code/prettify.css',
		uploadJson : pubPath + '/pub/picture/upload',
		items : [
		'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
		'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
		'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
		'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
		'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
		'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
		'table', 'hr', 'emoticons', 'pagebreak',
		'link', 'unlink'
		],
		extraFileUploadParams : {
		relaTable: 'pOpenInfo'
		},
		afterUpload : function(data) {

		},
		<%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
		allowFileManager : true,
		afterCreate : function() {
		}
		});

		prettyPrint();
		});




		var suppPath = pubUrl;

        window.onload = function () {
			if(!LS.isEmpty(fileId.getValue())){
				var strImage='<img src="<%=request.getAttribute("pubPath") %>/attach/'+fileId.getValue()+'" style="max-width: 300px;max-height: 180px;"></img>';
				document.getElementById("image").innerHTML=strImage;
			}
			if(!LS.isEmpty(actDetailMarks.getValue())){
			editor.html(actDetailMarks.getValue());
			editor.readonly(true);
			}
			if(!LS.isEmpty(actMarks.getValue())){
			editor2.html(actMarks.getValue());
			editor2.readonly(true);
			}
        }

		window.unitFunc = unitFunc;
		function unitFunc(rowData) {
			return "<div>" + "折" + "</div>";
		}
		window.cpnFunc = cpnFunc;
		function cpnFunc(rowData) {
			var _cpnId = rowData.cpnId;
			var _cpnName = rowData.cpnName;
			return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCpnDetail(\"" + _cpnId + "\")'>" + _cpnName + "</a></u>";
		}

		window.showCpnDetail = function (_cpnId) {
			LS.dialog("~/billing/coupon/initCouponDetail?cpnId="+_cpnId,"优惠券详细信息",950, 550,true);
		}

	</ls:script>
</ls:body>
</html>
