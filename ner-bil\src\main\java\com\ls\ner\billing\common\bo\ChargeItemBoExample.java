package com.ls.ner.billing.common.bo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ChargeItemBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public ChargeItemBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNull() {
            addCriterion("ITEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andItemIdIsNotNull() {
            addCriterion("ITEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andItemIdEqualTo(Long value) {
            addCriterion("ITEM_ID =", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotEqualTo(Long value) {
            addCriterion("ITEM_ID <>", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThan(Long value) {
            addCriterion("ITEM_ID >", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ITEM_ID >=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThan(Long value) {
            addCriterion("ITEM_ID <", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdLessThanOrEqualTo(Long value) {
            addCriterion("ITEM_ID <=", value, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdIn(List<Long> values) {
            addCriterion("ITEM_ID in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotIn(List<Long> values) {
            addCriterion("ITEM_ID not in", values, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdBetween(Long value1, Long value2) {
            addCriterion("ITEM_ID between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andItemIdNotBetween(Long value1, Long value2) {
            addCriterion("ITEM_ID not between", value1, value2, "itemId");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeIsNull() {
            addCriterion("CHARGE_ITEM_CODE is null");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeIsNotNull() {
            addCriterion("CHARGE_ITEM_CODE is not null");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeEqualTo(String value) {
            addCriterion("CHARGE_ITEM_CODE =", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeNotEqualTo(String value) {
            addCriterion("CHARGE_ITEM_CODE <>", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeGreaterThan(String value) {
            addCriterion("CHARGE_ITEM_CODE >", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_ITEM_CODE >=", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeLessThan(String value) {
            addCriterion("CHARGE_ITEM_CODE <", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_ITEM_CODE <=", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeLike(String value) {
            addCriterion("CHARGE_ITEM_CODE like", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeNotLike(String value) {
            addCriterion("CHARGE_ITEM_CODE not like", value, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeIn(List<String> values) {
            addCriterion("CHARGE_ITEM_CODE in", values, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeNotIn(List<String> values) {
            addCriterion("CHARGE_ITEM_CODE not in", values, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeBetween(String value1, String value2) {
            addCriterion("CHARGE_ITEM_CODE between", value1, value2, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andChargeItemCodeNotBetween(String value1, String value2) {
            addCriterion("CHARGE_ITEM_CODE not between", value1, value2, "chargeItemCode");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("ITEM_NAME is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("ITEM_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("ITEM_NAME =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("ITEM_NAME <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("ITEM_NAME >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("ITEM_NAME >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("ITEM_NAME <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("ITEM_NAME <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("ITEM_NAME like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("ITEM_NAME not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("ITEM_NAME in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("ITEM_NAME not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("ITEM_NAME between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("ITEM_NAME not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNull() {
            addCriterion("CHARGE_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNotNull() {
            addCriterion("CHARGE_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeEqualTo(String value) {
            addCriterion("CHARGE_TYPE =", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotEqualTo(String value) {
            addCriterion("CHARGE_TYPE <>", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThan(String value) {
            addCriterion("CHARGE_TYPE >", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_TYPE >=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThan(String value) {
            addCriterion("CHARGE_TYPE <", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_TYPE <=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLike(String value) {
            addCriterion("CHARGE_TYPE like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotLike(String value) {
            addCriterion("CHARGE_TYPE not like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIn(List<String> values) {
            addCriterion("CHARGE_TYPE in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotIn(List<String> values) {
            addCriterion("CHARGE_TYPE not in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeBetween(String value1, String value2) {
            addCriterion("CHARGE_TYPE between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotBetween(String value1, String value2) {
            addCriterion("CHARGE_TYPE not between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andNeedFlagIsNull() {
            addCriterion("NEED_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andNeedFlagIsNotNull() {
            addCriterion("NEED_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andNeedFlagEqualTo(String value) {
            addCriterion("NEED_FLAG =", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagNotEqualTo(String value) {
            addCriterion("NEED_FLAG <>", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagGreaterThan(String value) {
            addCriterion("NEED_FLAG >", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagGreaterThanOrEqualTo(String value) {
            addCriterion("NEED_FLAG >=", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagLessThan(String value) {
            addCriterion("NEED_FLAG <", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagLessThanOrEqualTo(String value) {
            addCriterion("NEED_FLAG <=", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagLike(String value) {
            addCriterion("NEED_FLAG like", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagNotLike(String value) {
            addCriterion("NEED_FLAG not like", value, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagIn(List<String> values) {
            addCriterion("NEED_FLAG in", values, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagNotIn(List<String> values) {
            addCriterion("NEED_FLAG not in", values, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagBetween(String value1, String value2) {
            addCriterion("NEED_FLAG between", value1, value2, "needFlag");
            return (Criteria) this;
        }

        public Criteria andNeedFlagNotBetween(String value1, String value2) {
            addCriterion("NEED_FLAG not between", value1, value2, "needFlag");
            return (Criteria) this;
        }

        public Criteria andItemDescIsNull() {
            addCriterion("ITEM_DESC is null");
            return (Criteria) this;
        }

        public Criteria andItemDescIsNotNull() {
            addCriterion("ITEM_DESC is not null");
            return (Criteria) this;
        }

        public Criteria andItemDescEqualTo(String value) {
            addCriterion("ITEM_DESC =", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescNotEqualTo(String value) {
            addCriterion("ITEM_DESC <>", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescGreaterThan(String value) {
            addCriterion("ITEM_DESC >", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescGreaterThanOrEqualTo(String value) {
            addCriterion("ITEM_DESC >=", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescLessThan(String value) {
            addCriterion("ITEM_DESC <", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescLessThanOrEqualTo(String value) {
            addCriterion("ITEM_DESC <=", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescLike(String value) {
            addCriterion("ITEM_DESC like", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescNotLike(String value) {
            addCriterion("ITEM_DESC not like", value, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescIn(List<String> values) {
            addCriterion("ITEM_DESC in", values, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescNotIn(List<String> values) {
            addCriterion("ITEM_DESC not in", values, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescBetween(String value1, String value2) {
            addCriterion("ITEM_DESC between", value1, value2, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andItemDescNotBetween(String value1, String value2) {
            addCriterion("ITEM_DESC not between", value1, value2, "itemDesc");
            return (Criteria) this;
        }

        public Criteria andEffectFlagIsNull() {
            addCriterion("EFFECT_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andEffectFlagIsNotNull() {
            addCriterion("EFFECT_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andEffectFlagEqualTo(String value) {
            addCriterion("EFFECT_FLAG =", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagNotEqualTo(String value) {
            addCriterion("EFFECT_FLAG <>", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagGreaterThan(String value) {
            addCriterion("EFFECT_FLAG >", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagGreaterThanOrEqualTo(String value) {
            addCriterion("EFFECT_FLAG >=", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagLessThan(String value) {
            addCriterion("EFFECT_FLAG <", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagLessThanOrEqualTo(String value) {
            addCriterion("EFFECT_FLAG <=", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagLike(String value) {
            addCriterion("EFFECT_FLAG like", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagNotLike(String value) {
            addCriterion("EFFECT_FLAG not like", value, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagIn(List<String> values) {
            addCriterion("EFFECT_FLAG in", values, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagNotIn(List<String> values) {
            addCriterion("EFFECT_FLAG not in", values, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagBetween(String value1, String value2) {
            addCriterion("EFFECT_FLAG between", value1, value2, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andEffectFlagNotBetween(String value1, String value2) {
            addCriterion("EFFECT_FLAG not between", value1, value2, "effectFlag");
            return (Criteria) this;
        }

        public Criteria andTimeLineIsNull() {
            addCriterion("TIME_LINE is null");
            return (Criteria) this;
        }

        public Criteria andTimeLineIsNotNull() {
            addCriterion("TIME_LINE is not null");
            return (Criteria) this;
        }

        public Criteria andTimeLineEqualTo(String value) {
            addCriterion("TIME_LINE =", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineNotEqualTo(String value) {
            addCriterion("TIME_LINE <>", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineGreaterThan(String value) {
            addCriterion("TIME_LINE >", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineGreaterThanOrEqualTo(String value) {
            addCriterion("TIME_LINE >=", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineLessThan(String value) {
            addCriterion("TIME_LINE <", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineLessThanOrEqualTo(String value) {
            addCriterion("TIME_LINE <=", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineLike(String value) {
            addCriterion("TIME_LINE like", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineNotLike(String value) {
            addCriterion("TIME_LINE not like", value, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineIn(List<String> values) {
            addCriterion("TIME_LINE in", values, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineNotIn(List<String> values) {
            addCriterion("TIME_LINE not in", values, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineBetween(String value1, String value2) {
            addCriterion("TIME_LINE between", value1, value2, "timeLine");
            return (Criteria) this;
        }

        public Criteria andTimeLineNotBetween(String value1, String value2) {
            addCriterion("TIME_LINE not between", value1, value2, "timeLine");
            return (Criteria) this;
        }

        public Criteria andBuildDateIsNull() {
            addCriterion("BUILD_DATE is null");
            return (Criteria) this;
        }

        public Criteria andBuildDateIsNotNull() {
            addCriterion("BUILD_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andBuildDateEqualTo(Date value) {
            addCriterion("BUILD_DATE =", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateNotEqualTo(Date value) {
            addCriterion("BUILD_DATE <>", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateGreaterThan(Date value) {
            addCriterion("BUILD_DATE >", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateGreaterThanOrEqualTo(Date value) {
            addCriterion("BUILD_DATE >=", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateLessThan(Date value) {
            addCriterion("BUILD_DATE <", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateLessThanOrEqualTo(Date value) {
            addCriterion("BUILD_DATE <=", value, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateIn(List<Date> values) {
            addCriterion("BUILD_DATE in", values, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateNotIn(List<Date> values) {
            addCriterion("BUILD_DATE not in", values, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateBetween(Date value1, Date value2) {
            addCriterion("BUILD_DATE between", value1, value2, "buildDate");
            return (Criteria) this;
        }

        public Criteria andBuildDateNotBetween(Date value1, Date value2) {
            addCriterion("BUILD_DATE not between", value1, value2, "buildDate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_charge_item
     *
     * @mbggenerated do_not_delete_during_merge Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}