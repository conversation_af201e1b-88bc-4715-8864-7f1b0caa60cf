package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.BillingOrderPricingBo;
import com.ls.ner.billing.common.bo.BillingOrderPricingBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface BillingOrderPricingBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int countByExample(BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int deleteByExample(BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int insert(BillingOrderPricingBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int insertSelective(BillingOrderPricingBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    List<BillingOrderPricingBo> selectByExampleWithBLOBs(BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    List<BillingOrderPricingBo> selectByExample(BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    BillingOrderPricingBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByExampleSelective(@Param("record") BillingOrderPricingBo record, @Param("example") BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByExampleWithBLOBs(@Param("record") BillingOrderPricingBo record, @Param("example") BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByExample(@Param("record") BillingOrderPricingBo record, @Param("example") BillingOrderPricingBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByPrimaryKeySelective(BillingOrderPricingBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByPrimaryKeyWithBLOBs(BillingOrderPricingBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    int updateByPrimaryKey(BillingOrderPricingBo record);
}