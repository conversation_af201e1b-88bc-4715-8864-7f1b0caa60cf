/**
 *
 * @(#) EPrcChargeBo.java
 * @Package com.ls.ner.billing.api.prccharge
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api.prccharge.bo;

import java.io.Serializable;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年5月1日 上午10:16:54   lipf   Created.
 *           
 */
public class EPrcChargeBo implements Serializable{
	private static final long serialVersionUID = 4341693419358000906L;
	private String custId;
	private String custNo;
	private String city;
	private String county;
	private String appNo;
	private String calcId;
	private String billingType;
	private String lease;
	private String tSettlePq;
	private String pricingAmt;
	private String rcvblId;
	private String calcDate;

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getAppNo() {
		return appNo;
	}

	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}

	public String getCalcId() {
		return calcId;
	}

	public void setCalcId(String calcId) {
		this.calcId = calcId;
	}

	public String getBillingType() {
		return billingType;
	}

	public void setBillingType(String billingType) {
		this.billingType = billingType;
	}

	public String getLease() {
		return lease;
	}

	public void setLease(String lease) {
		this.lease = lease;
	}

	public String gettSettlePq() {
		return tSettlePq;
	}

	public void settSettlePq(String tSettlePq) {
		this.tSettlePq = tSettlePq;
	}

	public String getPricingAmt() {
		return pricingAmt;
	}

	public void setPricingAmt(String pricingAmt) {
		this.pricingAmt = pricingAmt;
	}

	public String getRcvblId() {
		return rcvblId;
	}

	public void setRcvblId(String rcvblId) {
		this.rcvblId = rcvblId;
	}

	public String getCalcDate() {
		return calcDate;
	}

	public void setCalcDate(String calcDate) {
		this.calcDate = calcDate;
	}
}
