<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="导入购车信息">
</ls:head>
<ls:body>
    <ls:form name="fileForm" enctype="multipart/form-data">
        <table align="center" class="tab_search">
            <tr>
                <td width="150px"><ls:label text="请选择附件" ref=""></ls:label></td>
                <td width="250px"><input type="file" name="importFile" id="fileOriginPath" class="text">
                </td>
            </tr>
            <tr>
                <td></td>
                <td width="250px">为确保能正常导入，请先<a style="color: red;" href='javascript:void(0);' onclick="downFile()">下载模板</a></td>
            </tr>
            <tr>
                <td colspan="2">
                    <div class="pull-right">
                        <ls:button text="导入" onclick="importData" />
                        <ls:button text="取消" onclick="cancel" />
                    </div>
                </td>
            </tr>

        </table>
    </ls:form>
    <ls:script>
        window.downFile=downFile;
        function downFile(){
            window.location.href = rootUrl+"bil/car/carPurchaseImport.xls";
        }
        function cancel(){
            LS.window.close();
        }
        function importData(){
            var _file=document.getElementById('fileOriginPath').value;
            if(LS.isEmpty(_file)){
                LS.message("warn","不能上传空文件");
                return;
            }
            if(!/\.(xls|xlsx)$/.test(document.getElementById("fileOriginPath").value.toLowerCase())){
                LS.message("warn","文件类型必须是xls");
                return;
            }
            fileForm.submit("~/bil/carGift/saveImportData",function(result){
                console.log(JSON.stringify(result));
                if(result.items[0].successful) {
                    LS.message('info',result.items[0].resultValue.msg,function() {
                        LS.window.close();
                    });
                    LS.parent().doSearch();
                }else {
                    LS.message('info', result.items[0].resultHint);
                }
            });
        }
    </ls:script>
</ls:body>
</html>