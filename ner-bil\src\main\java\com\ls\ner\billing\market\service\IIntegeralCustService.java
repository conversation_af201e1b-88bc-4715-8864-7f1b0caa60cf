package com.ls.ner.billing.market.service;

import java.util.List;

import com.ls.ner.billing.market.bo.IntegeralCustLogBo;

public interface IIntegeralCustService {
	
	/**
	 * 获取用户积分流水记录列表
	 * @param logBo
	 * @return
	 */
	List<IntegeralCustLogBo> getIntegeralCustLogList(IntegeralCustLogBo logBo);
	
	/**
	 * 获取用户积分流水记录列表数
	 * @param logBo
	 * @return
	 */
	int getIntegeralCustLogCount(IntegeralCustLogBo logBo);
	
	/**
     * @param
     * @description 用户积分清零任务调度
     * <AUTHOR>
     * @create 2018-03-07 14:59:21
     */
    void excuteIntegeralCustTask();

	/**
	 * 会员专享优惠 每月发券
	 * @return
	 */
	void excuteExclusiveOfferTask();

	String getCustVipType(String custId);
}
