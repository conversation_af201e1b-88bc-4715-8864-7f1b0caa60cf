package com.ls.ner.billing.market.service.impl;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.dao.ICouponDao;
import com.ls.ner.billing.market.service.ICouponPutService;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.dao.IMarketActDao;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.constants.Constants;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.util.*;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import com.pt.poseidon.ws.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.*;

/**
 * description 创建时间 2016年4月7日下午7:17:29 创建人 lise
 */
@Service(target = {ServiceType.LOCAL}, value = "couponService")
public class CouponServiceImpl implements ICouponService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponServiceImpl.class);

    @Autowired(required = true)
    private ICouponDao dao;

    @Autowired(required = true)
    private IMarketActDao marketActDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "attachRpcService")
    private IAttachRpcService attachService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "couponRpcService")
    private ICouponRpcService couponRpcService; // 支付中心-优惠券接口

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "bilCouponPutService")
    private ICouponPutService couponPutService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IDefrayAccountRpcService defrayAccountRpcService;

    /**
     * 描述:保存优惠券和优惠券条件
     *
     * @param: [bo, request]
     * @return: void
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 18:26
     */
    @Override
    public int saveCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        // 保存优惠券信息
        String cpnNo = "";
        String cpnTimeType = bo.getCpnTimeType();
        // mysql-bug date类型必须设置为null
        if (StringUtil.isNotBlank(cpnTimeType) && !"1".equals(cpnTimeType)) {
            bo.setEftDate(null);
            bo.setInvDate(null);
        }
        String cpnDctType = bo.getDctType();
        if (StringUtil.isNotBlank(cpnDctType) && cpnDctType.length() == 4) {
            bo.setDctCalcMethod(cpnDctType.substring(0, 2));
        }
        String timeDuration = bo.getTimeDuration();
        if (StringUtil.isBlank(timeDuration)) {
            bo.setTimeDuration("0");
        }
        if (StringUtil.isBlank(bo.getProdId())) {
            bo.setProdId("0");
        }
        if (StringUtil.isBlank(bo.getMaximumDiscountAmt())) {
            bo.setMaximumDiscountAmt("0");
        }
        if (StringUtil.isBlank(bo.getDctCondValue())) {
            bo.setDctCondValue("0");
        }

        flag = dao.insertCoupon(bo);
        if (flag > 0) {
            String cpnId = bo.getCpnId();
            if (StringUtil.isNotBlank(cpnId)) {
                // 保存条件
                if (StringUtil.isBlank(bo.getDctCondValue())) {
                    bo.setDctCondValue("0");
                }
                insertDctCond(bo);
            }

            // 图片处理
            String filePath = "", attachId = "";
            Iterator<String> itr = request.getFileNames();
            AttachBo attachBo = null;
            while (itr.hasNext()) {
                MultipartFile file = request.getFile(itr.next());
                if (file != null) {
                    attachBo = new AttachBo();
                    attachBo.setRelaId(bo.getCpnId());
                    attachBo.setRelaTable("coupon");
                    attachBo.setContentType("02");
                    attachBo.setFileSize(file.getSize() + "");
                    attachBo.setFileName(file.getOriginalFilename());
                    attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(file.getInputStream()), attachBo);
                    attachBo = attachService.qryAttachInfo(attachBo);
                    if (attachBo != null) {
                        filePath = attachBo.getFilePath();
                        attachId = attachBo.getAttachId();
                    }
                }
            }
            // 更新优惠券图片
            if (StringUtil.isNotBlank(filePath) && StringUtil.isNotBlank(attachId) && StringUtil.isNotBlank(cpnId)) {
                CouponBo jpgBo = new CouponBo();
                String json = "{\"filePath\":\"" + filePath + "\",\"attachId\":\"" + attachId + "\"}";
                jpgBo.setFilePath(json);
                jpgBo.setCpnId(cpnId);
                dao.updateCoupon(jpgBo);
            }
        }
        return flag;
    }

    /**
     * 描述:新增优惠券关联关系
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-03 18:26
     */
    public int insertDctCond(CouponBo bo) {
        return dao.insertDctCond(bo);
    }

    /**
     * 描述:更新优惠券信息及条件
     *
     * @param: [bo, request]
     * @return: void
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 18:26
     */
    @Override
    public int updateCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        if (bo != null) {
            String cpnId = bo.getCpnId();
            String cpnName = bo.getCpnName();
            if (StringUtil.isNotBlank(cpnId) || StringUtil.isNotBlank(cpnName)) {
                // 更新优惠券信息
                flag = dao.updateCoupon(bo);
                // 更新优惠条件信息
                String dctCondFlag = bo.getDctCondFlag();
                if (StringUtil.isNotBlank(dctCondFlag) && "1".equals(dctCondFlag)) {
                    bo.setDctCondType("");
                    bo.setProdId("0");
                    bo.setDctCondValue("");
                }
                dao.updateDctCond(bo);
                // 图片处理
                String filePath = "", attachId = "";
                Iterator<String> itr = request.getFileNames();
                AttachBo attachBo = null;
                while (itr.hasNext()) {
                    MultipartFile file = request.getFile(itr.next());
                    if (file != null) {
                        attachBo = new AttachBo();
                        attachBo.setRelaId(bo.getCpnId());
                        attachBo.setRelaTable("coupon");
                        attachBo.setContentType("02");
                        attachBo.setFileSize(file.getSize() + "");
                        attachBo.setFileName(file.getOriginalFilename());
                        attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(file.getInputStream()), attachBo);
                        attachBo = attachService.qryAttachInfo(attachBo);
                        if (attachBo != null) {
                            filePath = attachBo.getFilePath();
                            attachId = attachBo.getAttachId();
                        }
                    }
                }
                // 更新优惠券图片
                if (StringUtil.isNotBlank(filePath) && StringUtil.isNotBlank(attachId) && StringUtil.isNotBlank(cpnId)) {
                    CouponBo jpgBo = new CouponBo();
                    String json = "{\"filePath\":\"" + filePath + "\",\"attachId\":\"" + attachId + "\"}";
                    jpgBo.setFilePath(json);
                    jpgBo.setCpnId(cpnId);
                    dao.updateCoupon(jpgBo);
                }
            }
        }
        return flag;
    }

    /**
     * 获取优惠券详细数据
     *
     * @param condition
     * @return
     */
    @Override
    public CouponBo getCouponDetail(MarketCondition condition) {
        List<CouponBo> list = dao.getCoupons(condition);
        CouponBo bo = new CouponBo();
        if (list != null && list.size() > 0) {
            bo = list.get(0);
            if (bo != null) {
                String filePath = bo.getFilePath();
                if (StringUtil.isNotBlank(filePath)) {
                    Map<String, String> jsonMap = (Map<String, String>) JsonUtil.fromJson(filePath, Map.class);
                    if (jsonMap != null && jsonMap.size() > 0) {
                        filePath = jsonMap.get("filePath");
                        bo.setFilePath(jsonMap.get("filePath"));
                        bo.setAttachId(jsonMap.get("attachId"));
                    }
                }
            }
        }
        return bo;
    }

    /**
     * biaoxiangd 获取条件参数
     *
     * @return
     */
    public List<Map> queryBProd(Map map) {
        return dao.queryBProd(map);
    }

    @Override
    public List<CouponBo> getCoupons(MarketCondition condition) {
        List<CouponBo> list = dao.getCoupons(condition);
        for (int i = 0; i < list.size(); i++) {
            CouponBo bo = list.get(i);
            String cpnType = bo.getCpnType();
            CodeBO code = codeService.getStandardCode("cpnStatus",bo.getCpnStatus(), null);
            if (code != null) {
                list.get(i).setCpnStatusName(code.getCodeName());
            }
            bo.setCpnNumOrigin(bo.getCpnNum());
            bo.setCpnNum(MathUtils.add(bo.getPutNum(),bo.getAlrdyGetNum()));
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = bo.getTimeUnit();
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime = bo.getEffectTime();
                                list.get(i).setEffectTime(effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }
            }
            String filePath = bo.getFilePath();
            if (StringUtil.isNotBlank(filePath)) {
                Map<String, String> jsonMap = (Map<String, String>) JsonUtil.fromJson(filePath, Map.class);
                if (jsonMap != null && jsonMap.size() > 0) {
                    filePath = jsonMap.get("filePath");
                    list.get(i).setFilePath(jsonMap.get("filePath"));
                    list.get(i).setAttachId(jsonMap.get("attachId"));
                }
            }

            if (!StringUtils.nullOrBlank(bo.getOrgCode())) {
                OrgBo org = orgService.getOrgByNo(bo.getOrgCode());
                if (org != null) {
                    list.get(i).setCrtOrgName(org.getOrgShortName());
                }
            }

            if (StringUtil.isNotBlank(condition.getCpnId())){
                Map<String, String> useMap = new HashMap<String, String>();
                useMap.put("cpnId", condition.getCpnId());
                List<Map<String, Object>> useList = couponRpcService.queryAccountCoupons(useMap);
                if (CollectionUtils.isNotEmpty(useList)){
                    int unUseNum = 0;
                    int useNum = 0;
                    String totalChargeAmt = "0";
                    String totalServiceAmt = "0";
                    String totalCpnTBal = "0";
                    for (Map<String, Object> map : useList) {
                        if (Constants.UseStatus.NO_USE.equals(MapUtils.getValue(map,"useStatus")) || StringUtil.isBlank(MapUtils.getValue(map,"useStatus"))){
                            unUseNum++;
                        }
                        if (Constants.UseStatus.USE_ALREADY.equals(MapUtils.getValue(map,"useStatus"))){
                            useNum++;
                        }
                        if (StringUtil.isNotBlank(MapUtils.getValue(map,"chargeAmt"))){
                            totalChargeAmt = MathUtils.add(totalChargeAmt,MapUtils.getValue(map,"chargeAmt"));
                        }
                        if (StringUtil.isNotBlank(MapUtils.getValue(map,"serviceAmt"))){
                            totalServiceAmt = MathUtils.add(totalServiceAmt,MapUtils.getValue(map,"serviceAmt"));
                        }
                        if (StringUtil.isNotBlank(MapUtils.getValue(map,"cpnTBal"))){
                            totalCpnTBal = MathUtils.add(totalCpnTBal,MapUtils.getValue(map,"cpnTBal"));
                        }
                    }
                    list.get(i).setUseNum(String.valueOf(useNum));
                    list.get(i).setUnUseNum(String.valueOf(unUseNum));
                    list.get(i).setTotalChargeAmt(totalChargeAmt);
                    list.get(i).setTotalServiceAmt(totalServiceAmt);
                    list.get(i).setTotalCpnTBal(totalCpnTBal);
                } else {
                    list.get(i).setUseNum("0");
                    list.get(i).setUnUseNum("0");
                    list.get(i).setTotalChargeAmt("0");
                    list.get(i).setTotalServiceAmt("0");
                    list.get(i).setTotalCpnTBal("0");
                }
            }
        }
        return list;
    }

    @Override
    public int getCouponsCount(MarketCondition condition) {
        return dao.getCouponsCount(condition);
    }

    /**
     * 获取条件列表
     *
     * @param inMap
     * @return
     */
    @Override
    public List<CouponBo> queryDctCond(Map<String, String> inMap) {
        return dao.queryDctCond(inMap);
    }

    @Override
    public void updateCouponStatus(CouponBo bo) {
        dao.updateCoupon(bo);
    }


    /**
     * 描述:提供给发放功能调用，更新已领属性
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-05 12:07
     */
    public int rpcUpdateCoupon(Map<String, String> map) {
        int falg = -1;
        if (map != null && map.size() > 0) {
            String cpnId = map.get("cpnId");
            String putNum = map.get("putNum");
            if (StringUtil.isNotBlank(cpnId) && StringUtil.isNotBlank(putNum) && org.apache.commons.lang.StringUtils.isNumeric(putNum)) {
                CouponBo bo = new CouponBo();
                bo.setCpnId(cpnId);
                bo.setPutNum(putNum);
                falg = dao.updateCoupon(bo);
            }
        }
        return falg;
    }

    /**
     * 描述:优惠券使用信息
     *
     * @param: []
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 20:28
     */
    public List<CouponBo> getAccountCoupons(MarketCondition condition) {
        List<CouponBo> retList = new ArrayList<CouponBo>();
        if (condition != null) {
            String cpnId = condition.getCpnId();
            if (StringUtil.isNotBlank(cpnId)) {
                Map<String, String> useMap = new HashMap<String, String>();
                useMap.put("cpnId", cpnId);
                List<Map<String, Object>> useList = couponRpcService.queryUseCoupon(useMap);
                if (useList != null && useList.size() > 0) {
                    for (Map<String, Object> map : useList) {
                        CouponBo bo = new CouponBo();
                        BeanUtil.populate(bo, map);
                        retList.add(bo);
                    }
                }
            }
        }
        return retList;
    }

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-01 可领用的优惠券
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    @Override
    public List<Map<String, Object>> couponsRestGET(Map<String, String> map) throws Exception {
        List<Map<String, Object>> retList = new ArrayList<Map<String, Object>>();
        MarketCondition condition = new MarketCondition();
        String prodBusiType = map.get("prodBusiType");
        if (StringUtil.isNotBlank(prodBusiType)) {
            condition.setBusiType(prodBusiType);
        }
        String actType = map.get("actType");
        if(StringUtil.isNotEmpty(actType)){
            condition.setActType(actType);
        }
        String aId = map.get("aId");
        if(StringUtil.isNotEmpty(aId)){
            condition.setaId(aId);
        }
        condition.setCpnStatus(BillConstants.CpnState.IN_USE);
        condition.setInvDate("now");
//        List<CouponBo> couponList = this.getCoupons(condition);
        String sourceApi = map.get("sourceApi");
        if(StringUtil.isNotEmpty(sourceApi)){
            condition.setSourceApi(sourceApi);
        }
        List<CouponBo> couponList = dao.getRestCoupons(condition);
        for (int i = 0; i < couponList.size(); i++) {
            CouponBo bo = couponList.get(i);
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                if ("01".equals(cpnType)) {
                    couponList.get(i).setCpnAmt(bo.getCpnAmt() + "元");
                } else {
                    couponList.get(i).setCpnAmt(bo.getCpnAmt() + "折");
                }
            }
            CodeBO code = codeService.getStandardCode("cpnStatus",
                    bo.getCpnStatus(), null);
            if (code != null) {
                couponList.get(i).setCpnStatusName(code.getCodeName());
            }
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = bo.getTimeUnit();
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime = bo.getEffectTime();
                                couponList.get(i).setEffectTime(effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }
            }
            String filePath = bo.getFilePath();
            if (StringUtil.isNotBlank(filePath)) {
                Map<String, String> jsonMap = (Map<String, String>) JsonUtil.fromJson(filePath, Map.class);
                if (jsonMap != null && jsonMap.size() > 0) {
                    filePath = jsonMap.get("filePath");
                    couponList.get(i).setFilePath(jsonMap.get("filePath"));
                    couponList.get(i).setAttachId(jsonMap.get("attachId"));
                }
            }

            if (!StringUtils.nullOrBlank(bo.getOrgCode())) {
                OrgBo org = orgService.getOrgByNo(bo.getOrgCode());
                if (org != null) {
                    couponList.get(i).setCrtOrgName(org.getOrgShortName());
                }
            }
        }
        if (couponList != null && couponList.size() > 0) {
            // 通过cpnId,custId过滤已领用的优惠券
            ArrayList<Map<String, String>> cpnIdList = new ArrayList<Map<String, String>>();
            for (int i = 0; i < couponList.size(); i++) {
                String cpnId = couponList.get(i).getCpnId();
                if (StringUtil.isNotBlank(cpnId)) {
                    Map<String, String> cMap = new HashMap<String, String>();
                    cMap.put("cpnId", cpnId);
                    cpnIdList.add(cMap);
                }
            }
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("cpnIdList", cpnIdList);
            paramMap.put("custId", String.valueOf(map.get("custId")));
            List<Map<String, Object>> accCouponList = couponRpcService.couponByAcct(paramMap);
            CodeBO codeBO = null;
            if (accCouponList != null && accCouponList.size() > 0) {
                MergeUtil.mergeList(couponList, accCouponList, "cpnId", new String[]{"getNum"});
            }

            for (int i = 0; i < couponList.size(); i++) {
                CouponBo bo = couponList.get(i);
                String limGetNum = bo.getLimGetNum();
                if (StringUtil.isBlank(limGetNum)) limGetNum = "0";
                String getNum = bo.getGetNum();
                if (StringUtil.isBlank(getNum)) getNum = "0";
                // 已领数量和限领数量对比
                if (Integer.parseInt(getNum) >= Integer.parseInt(limGetNum)) {
                    continue;
                }
                // 优惠券发行数量与已领数量+已发放数量做对比
                String cpnNum = bo.getCpnNum();
                if (StringUtil.isBlank(cpnNum)) cpnNum = "0";
                String alrdyGetNum = bo.getAlrdyGetNum();
                if (StringUtil.isBlank(alrdyGetNum)) alrdyGetNum = "0";
                String putNum = bo.getPutNum();
                if (StringUtil.isBlank(putNum)) putNum = "0";
                LOGGER.debug("cpnNum:{},alrdyGetNum:{},putNum:{}",cpnNum,alrdyGetNum,putNum);
                if (Integer.parseInt(alrdyGetNum) + Integer.parseInt(putNum)>= Integer.parseInt(cpnNum)) {
                    continue;
                }

                Map<String, Object> boMap = new HashMap<String, Object>();
                Map<String, String> couponMap = MapUtils.beanToMap(bo);
                if (couponMap != null && couponMap.size() > 0) {
                    boMap.put("cpnId", couponMap.get("cpnId"));
                    boMap.put("cpnName", couponMap.get("cpnName"));
                    String cpnType = couponMap.get("cpnType");
                    if (StringUtil.isNotBlank(cpnType)) {
                        codeBO = codeService.getStandardCode("cpnType", cpnType, null);
                        if (codeBO != null) {
                            boMap.put("cpnType", codeBO.getCodeName());
                        }
                    }
                    String busiType = couponMap.get("busiType");
                    if (StringUtil.isNotBlank(busiType)) {
                        codeBO = codeService.getStandardCode("orderType", busiType, null);
                        if (codeBO != null) {
                            boMap.put("prodBusiType", codeBO.getCodeName());
                        }
                    }
                    boMap.put("cpnAmt", couponMap.get("cpnAmt"));
                    boMap.put("cpnMarks", couponMap.get("cpnMarks"));
                    boMap.put("timeLimit", couponMap.get("effectTime"));
                    boMap.put("needPointNum",couponMap.get("needPointNum"));
                    boMap.put("actId",bo.getActId());
                    retList.add(boMap);
                }
            }
        }
        return retList;
    }

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-02 优惠券领取
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    @Override
    public Map<String, Object> couponsRestPOST(Map<String, String> map) throws Exception{
        Map<String, Object> retMap = new HashMap<String, Object>();
        MarketCondition condition = new MarketCondition();
        String cpnId = map.get("cpnId");
        condition.setCpnId(cpnId);
        List<CouponBo> couponList = this.getCoupons(condition);
        if (couponList != null && couponList.size() > 0) {
            String custInfoList = "[{\"mobile\":\"" + map.get("mobile") + "\",\"sn\":\"1\",\"checked\":true,\"custId\":\"" + map.get("custId") + "\"," +
                    "\"custName\":\"\",\"custSortCodeName\":\"\",\"custSortCode\":\"\"}]";
            for (int i = 0; i < couponList.size(); i++) {
                CouponBo bo = couponList.get(i);
                Map<String, Object> boMap = MapUtils.beanToMap(bo);
                if (boMap != null && boMap.size() > 0) {
                    boMap.put("custInfoList", custInfoList);
                    boMap.put("getChannel", map.get("getChannel"));
                    retMap = couponPutService.couponsRestPOST(boMap);
                }
            }
        }
        return retMap;
    }
    /**
     * @description 优惠券失效任务调度
     * <AUTHOR>
     * @create 2018-03-07 14:59:21
     */
    @Override
    public void excuteCouponTask() {
        //查询所有过期的优惠券
        List<String> cpnIdList = new ArrayList<>();
        List<Map<String,Object>> list = dao.queryInvCoupon();
        if(CollectionUtils.isNotEmpty(list)){
            for (Map<String, Object> map : list) {
                cpnIdList.add(StringUtil.getString(map.get("cpnId")));
            }
            Map<String,Object> iMap = new HashMap<String, Object>();
            iMap.put("cpnIdList",cpnIdList);
            //更新所有失效的优惠券为已过期
            iMap.put("cpnState","3");
            dao.updateCpnInv(iMap);
        }
    }

    /**
     * @param inMap
     * @description 积分兑换优惠券
     * <AUTHOR>
     * @create 2018-04-24 15:36:33
     */
    @Override
    public Map<String, Object> pointCoupon(Map<String, Object> inMap) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        //查询优惠券
        List<Map<String, Object>> actCouponList = marketActDao.queryActCoupon(inMap);
        if (CollectionUtils.isNotEmpty(actCouponList)){
            String cpnId = StringUtil.getString(actCouponList.get(0).get("cpnId"));
            String needPointNum = StringUtil.getString(actCouponList.get(0).get("needPointNum"));
            //查询用户账号积分数
            Map<String, Object> iMap = new HashMap<>();
            iMap.put("custId", inMap.get("custId"));
            Map<String, Object> accMap = defrayAccountRpcService.qryAccount(iMap);
            List<Map<String, Object>> accList = (List<Map<String, Object>>) accMap.get("accList");
            String pointNum = "0";
            for (Map<String, Object> map : accList) {
                if (Constants.AccountType.POINTS.equals(StringUtil.getString(map.get("accType")))){
                    pointNum = StringUtil.getString(map.get("accFree"));
                }
            }
            if (MathUtils.compareTo(pointNum,needPointNum) < 0){
                resultMap.put("ret", 400);
                resultMap.put("msg", "账号积分不足，无法兑换!");
                return resultMap;
            }

            Map<String, String> map = new HashMap<>();
            map.put("mobile", StringUtil.getString(inMap.get("mobile")));
            map.put("getChannel", StringUtil.getString(inMap.get("getChannel")));
            map.put("cpnId", cpnId);
            map.put("custId", StringUtil.getString(inMap.get("custId")));
            Map<String, Object> retMap = couponsRestPOST(map);
            if (retMap != null && retMap.size() > 0) {
                List<Map<String, Object>> custList = ( List<Map<String, Object>>) retMap.get("custList");
                if (custList != null && custList.size() > 0) {
                    Map<String, Object> custMap = (Map<String, Object>)custList.get(0);
                    if (!"1".equals(String.valueOf(custMap.get("putFlag")))) {
                        resultMap.put("ret", 400);
                        resultMap.put("msg", "兑换失败!");
                        return resultMap;
                    }
                }
            }
            //扣取积分，记录流水
            iMap.put("usePoint", needPointNum);
            iMap.put("pointUseId", inMap.get("actId"));
            retMap = defrayAccountRpcService.subtractPoint(iMap);
            if ("true".equals(StringUtil.getString(retMap.get("result")))){
                resultMap.put("ret", 200);
                resultMap.put("msg", "积分兑换成功!");
            }else{
                resultMap.put("ret", 400);
                resultMap.put("msg", "账号积分不足，无法兑换!");
            }
        }else{
            resultMap.put("ret", 400);
            resultMap.put("msg", "查询不到兑换优惠券活动!");
        }
        return resultMap;
    }

    @Override
    public List<CouponBo> newGetCoupons(MarketCondition condition) {
        List<CouponBo> list = dao.newGetCoupons(condition);
        for (int i = 0; i < list.size(); i++) {
            CouponBo bo = list.get(i);
            CodeBO code = codeService.getStandardCode("cpnStatus",
                    bo.getCpnStatus(), null);
            if (code != null) {
                list.get(i).setCpnStatusName(code.getCodeName());
            }
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = bo.getTimeUnit();
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime = bo.getEffectTime();
                                list.get(i).setEffectTime(effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }
            }

            if (!StringUtils.nullOrBlank(bo.getOrgCode())) {
                OrgBo org = orgService.getOrgByNo(bo.getOrgCode());
                if (org != null) {
                    list.get(i).setCrtOrgName(org.getOrgShortName());
                }
            }
        }
        return list;
    }

    @Override
    public int newGetCouponsCount(MarketCondition condition) {
        return dao.newGetCouponsCount(condition);
    }

    @Override
    public int deleteByCpnId(String cpnId) {
        return dao.deleteByCpnId(cpnId);
    }

    @Override
    public int newSaveCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception {
        LOGGER.debug("---------------------CouponBo------------"+ com.pt.poseidon.common.utils.json.JsonUtil.obj2Json(bo));
        int flag = 0;
        // 保存优惠券信息
        String cpnTimeType = bo.getCpnTimeType();
        // mysql-bug date类型必须设置为null
        if (StringUtil.isNotBlank(cpnTimeType) && !"1".equals(cpnTimeType)) {
            bo.setEftDate(null);
            bo.setInvDate(null);
        }
        String timeDuration = bo.getTimeDuration();
        if (StringUtil.isBlank(timeDuration)) {
            bo.setTimeDuration("0");
        }

        if ("1".equals(bo.getBuildType()) && StringUtil.isNotBlank(bo.getBuildId())){
            bo.setDctCondBuild(bo.getBuildId());
        }else{
            bo.setDctCondBuild("1");
        }
        if ("1".equals(bo.getCityType()) && StringUtil.isNotBlank(bo.getCityCodes())){
            bo.setDctCondCity(bo.getCityCodes());
        }else{
            bo.setDctCondCity("1");
        }
        if ("1".equals(bo.getStationType()) && StringUtil.isNotBlank(bo.getStationIds())){
            bo.setDctCondStation(bo.getStationIds());
        }else{
            bo.setDctCondStation("1");
        }

        //浙江默认充电
        bo.setBusiType("02");
        //浙江默认抵用
        bo.setCpnType("01");
        bo.setProdId("0");
        //整单金额
        bo.setDctType("0100");
        bo.setDctCalcMethod("01");
        bo.setCalcPrecision("1");
        bo.setCalcPrecisionDigits("1");


        LOGGER.debug("---------------Insert------CouponBo------------"+ com.pt.poseidon.common.utils.json.JsonUtil.obj2Json(bo));

        flag = dao.insertCoupon(bo);
        if (flag > 0) {
            String cpnId = bo.getCpnId();
            if (StringUtil.isNotBlank(cpnId)) {
                // 保存条件
                if (StringUtil.isBlank(bo.getDctCondValue())) {
                    bo.setDctCondValue("0");
                }
                bo.setDctCondType("00");
                bo.setDctProdId("1");

//                insertDctCond(bo);
                dao.insertDctCondNew(bo);
            }

            // 图片处理
            String filePath = "", attachId = "";
            Iterator<String> itr = request.getFileNames();
            AttachBo attachBo = null;
            while (itr.hasNext()) {
                MultipartFile file = request.getFile(itr.next());
                if (file != null) {
                    attachBo = new AttachBo();
                    attachBo.setRelaId(bo.getCpnId());
                    attachBo.setRelaTable("coupon");
                    attachBo.setContentType("02");
                    attachBo.setFileSize(file.getSize() + "");
                    attachBo.setFileName(file.getOriginalFilename());
                    attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(file.getInputStream()), attachBo);
                    attachBo = attachService.qryAttachInfo(attachBo);
                    if (attachBo != null) {
                        filePath = attachBo.getFilePath();
                        attachId = attachBo.getAttachId();
                    }
                }
            }
            // 更新优惠券图片
            if (StringUtil.isNotBlank(filePath) && StringUtil.isNotBlank(attachId) && StringUtil.isNotBlank(cpnId)) {
                CouponBo jpgBo = new CouponBo();
                String json = "{\"filePath\":\"" + filePath + "\",\"attachId\":\"" + attachId + "\"}";
                jpgBo.setFilePath(json);
                jpgBo.setCpnId(cpnId);
                dao.updateCoupon(jpgBo);
            }
        }
        return flag;
    }

    @Override
    public int newUpdateCoupon(CouponBo bo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        if (bo != null) {
            String cpnId = bo.getCpnId();
            String cpnName = bo.getCpnName();
            if (StringUtil.isNotBlank(cpnId) || StringUtil.isNotBlank(cpnName)) {

                if ("1".equals(bo.getBuildType()) && StringUtil.isNotBlank(bo.getBuildId())){
                    bo.setDctCondBuild(bo.getBuildId());
                }else{
                    bo.setDctCondBuild("1");
                }
                if ("1".equals(bo.getCityType()) && StringUtil.isNotBlank(bo.getCityCodes())){
                    bo.setDctCondCity(bo.getCityCodes());
                }else{
                    bo.setDctCondCity("1");
                }
                if ("1".equals(bo.getStationType()) && StringUtil.isNotBlank(bo.getStationIds())){
                    bo.setDctCondStation(bo.getStationIds());
                }else{
                    bo.setDctCondStation("1");
                }


                // 更新优惠券信息
                LOGGER.debug("bo----------------------更新之前"+ com.pt.poseidon.common.utils.json.JsonUtil.obj2Json(bo));
                bo.setFilePath("");  //图片不在这里更新
                flag = dao.updateCoupon(bo);
                // 更新优惠条件信息
                String dctCondFlag = bo.getDctCondFlag();
                if (StringUtil.isNotBlank(dctCondFlag) && "1".equals(dctCondFlag)) {
                    bo.setDctCondType("");
                    bo.setProdId("0");
                    bo.setDctCondValue("");
                }
                dao.updateDctCondNew(bo);
                // 图片处理
                String filePath = "", attachId = "";
                Iterator<String> itr = request.getFileNames();
                AttachBo attachBo = null;
                while (itr.hasNext()) {
                    MultipartFile file = request.getFile(itr.next());
                    if (file != null) {
                        attachBo = new AttachBo();
                        attachBo.setRelaId(bo.getCpnId());
                        attachBo.setRelaTable("coupon");
                        attachBo.setContentType("02");
                        attachBo.setFileSize(file.getSize() + "");
                        attachBo.setFileName(file.getOriginalFilename());
                        attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(file.getInputStream()), attachBo);
                        attachBo = attachService.qryAttachInfo(attachBo);
                        if (attachBo != null) {
                            filePath = attachBo.getFilePath();
                            attachId = attachBo.getAttachId();
                        }
                    }
                }
                LOGGER.debug("bo----------------------filePath"+filePath+"attachId===="+attachId);
                // 更新优惠券图片
                if (StringUtil.isNotBlank(filePath) && StringUtil.isNotBlank(attachId) && StringUtil.isNotBlank(cpnId)) {
                    CouponBo jpgBo = new CouponBo();
                    String json = "{\"filePath\":\"" + filePath + "\",\"attachId\":\"" + attachId + "\"}";
                    jpgBo.setFilePath(json);
                    jpgBo.setCpnId(cpnId);
                    LOGGER.debug("jpgBo----------------------filePath"+ com.pt.poseidon.common.utils.json.JsonUtil.obj2Json(jpgBo));
                    dao.updateCoupon(jpgBo);
                }
            }
        }
        return flag;
    }

    @Override
    public List<CouponBo> newQueryDctCond(Map<String, String> inMap) {
        return dao.queryDctCondNew(inMap);
    }


    @Override
    public CouponBo getCouponCpnPurposeDetail(MarketCondition condition){
        return dao.getCouponCpnPurposeDetail(condition);
    }
}
