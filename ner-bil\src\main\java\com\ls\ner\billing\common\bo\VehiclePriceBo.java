package com.ls.ner.billing.common.bo;

import java.util.Date;

public class VehiclePriceBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.BATCH
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long batch;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.ORG_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String orgCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.RT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String rtNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.AUTO_MODEL_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String autoModelNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.EFFECT_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date effectTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.INVALID_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date invalidTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.IS_COVER
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String isCover;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.PRIORITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Integer priority;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.VERSION
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.STATUS
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_vehicle_price.UNIFIED_PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String unifiedPrice;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.SYSTEM_ID
     *
     * @return the value of e_vehicle_price.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.SYSTEM_ID
     *
     * @param systemId the value for e_vehicle_price.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.BILLING_NO
     *
     * @return the value of e_vehicle_price.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.BILLING_NO
     *
     * @param billingNo the value for e_vehicle_price.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBillingNo(String billingNo) {
        this.billingNo = billingNo == null ? null : billingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.BATCH
     *
     * @return the value of e_vehicle_price.BATCH
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getBatch() {
        return batch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.BATCH
     *
     * @param batch the value for e_vehicle_price.BATCH
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBatch(Long batch) {
        this.batch = batch;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.ORG_CODE
     *
     * @return the value of e_vehicle_price.ORG_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.ORG_CODE
     *
     * @param orgCode the value for e_vehicle_price.ORG_CODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.RT_NO
     *
     * @return the value of e_vehicle_price.RT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getRtNo() {
        return rtNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.RT_NO
     *
     * @param rtNo the value for e_vehicle_price.RT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setRtNo(String rtNo) {
        this.rtNo = rtNo == null ? null : rtNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.AUTO_MODEL_NO
     *
     * @return the value of e_vehicle_price.AUTO_MODEL_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getAutoModelNo() {
        return autoModelNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.AUTO_MODEL_NO
     *
     * @param autoModelNo the value for e_vehicle_price.AUTO_MODEL_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setAutoModelNo(String autoModelNo) {
        this.autoModelNo = autoModelNo == null ? null : autoModelNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.EFFECT_TIME
     *
     * @return the value of e_vehicle_price.EFFECT_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getEffectTime() {
        return effectTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.EFFECT_TIME
     *
     * @param effectTime the value for e_vehicle_price.EFFECT_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.INVALID_TIME
     *
     * @return the value of e_vehicle_price.INVALID_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getInvalidTime() {
        return invalidTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.INVALID_TIME
     *
     * @param invalidTime the value for e_vehicle_price.INVALID_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setInvalidTime(Date invalidTime) {
        this.invalidTime = invalidTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.IS_COVER
     *
     * @return the value of e_vehicle_price.IS_COVER
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getIsCover() {
        return isCover;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.IS_COVER
     *
     * @param isCover the value for e_vehicle_price.IS_COVER
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setIsCover(String isCover) {
        this.isCover = isCover == null ? null : isCover.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.PRIORITY
     *
     * @return the value of e_vehicle_price.PRIORITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.PRIORITY
     *
     * @param priority the value for e_vehicle_price.PRIORITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.DATA_OPER_TIME
     *
     * @return the value of e_vehicle_price.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_vehicle_price.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.DATA_OPER_TYPE
     *
     * @return the value of e_vehicle_price.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_vehicle_price.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.VERSION
     *
     * @return the value of e_vehicle_price.VERSION
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.VERSION
     *
     * @param version the value for e_vehicle_price.VERSION
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.STATUS
     *
     * @return the value of e_vehicle_price.STATUS
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.STATUS
     *
     * @param status the value for e_vehicle_price.STATUS
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_vehicle_price.UNIFIED_PRICE
     *
     * @return the value of e_vehicle_price.UNIFIED_PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getUnifiedPrice() {
        return unifiedPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_vehicle_price.UNIFIED_PRICE
     *
     * @param unifiedPrice the value for e_vehicle_price.UNIFIED_PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setUnifiedPrice(String unifiedPrice) {
        this.unifiedPrice = unifiedPrice == null ? null : unifiedPrice.trim();
    }
}