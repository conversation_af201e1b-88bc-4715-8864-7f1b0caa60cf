package com.ls.ner.billing.market.bo;

import java.io.Serializable;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

public class IntegeralCustLogBo extends QueryCondition implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 3066134251422186759L;

	private String eventType;//触发事件

	private String eventName;

	private String integralNo;//流水号

	private String integralNumber;// 用户当前积分

	private String chargeNum;//变更值

	private String goodsName;//商品名

	private String chargeType;//变更类型（01：收入；02：支出）
	private String chargeTypeName;

	private String chargeTime;//变更时间

	private String beginTime;

	private String endTime;

	private String phone;

	private String id;


	private int start;
	private int limit;

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getEventName() {
		return eventName;
	}

	public void setEventName(String eventName) {
		this.eventName = eventName;
	}

	public String getIntegralNo() {
		return integralNo;
	}

	public void setIntegralNo(String integralNo) {
		this.integralNo = integralNo;
	}

	public String getChargeNum() {
		return chargeNum;
	}

	public void setChargeNum(String chargeNum) {
		this.chargeNum = chargeNum;
	}

	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}

	public String getChargeTime() {
		return chargeTime;
	}

	public void setChargeTime(String chargeTime) {
		this.chargeTime = chargeTime;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getChargeTypeName() {
		return chargeTypeName;
	}

	public void setChargeTypeName(String chargeTypeName) {
		this.chargeTypeName = chargeTypeName;
	}

	public String getIntegralNumber() {
		return integralNumber;
	}

	public void setIntegralNumber(String integralNumber) {
		this.integralNumber = integralNumber;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
}
