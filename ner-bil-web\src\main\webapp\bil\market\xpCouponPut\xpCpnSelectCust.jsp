<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="ls" uri="http://www.longshine.com/taglib/ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<script type="text/javascript" src="<%=request.getContextPath()%>/cust/common/js/Util.js"></script>
<ls:head title="客户基本信息" />
<style>
</style>
<ls:body>
    <ls:title text="查询条件"></ls:title>
    <ls:form name="xpCustForm" id="xpCustForm">
        <table class="tab_search">
            <tr>
                <td><ls:label text="查询条件" ref="custSearchCondition"/> </td>
                <td>
                    <ls:select name="custSearchCondition" value="01" required="true">
                        <ls:option value="01" text="手机号"></ls:option>
                        <ls:option value="02" text="VIN"></ls:option>
                        <ls:option value="03" text="UID"></ls:option>
                    </ls:select>
                </td>
                <td><ls:label text="查询目标" ref="custSearchResult"/> </td>
                <td><ls:text name="custSearchResult" required="true"></ls:text> </td>
                <td>
                    <div class="pull-right">
                        <ls:button text="清空" onclick="clearAll" />
                        <ls:button text="查询" onclick="query" />
                    </div>
                </td>
            </tr>
        </table>
        <ls:title text="用户列表"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td>
                    <ls:grid url="" name="custGrid" height="180px" width="100%" primaryKey="uid" showCheckBox="true" >
                        <ls:gridToolbar name="cpnCustGridBar">
                            <ls:gridToolbarItem imageKey="add" onclick="select" text="选择"></ls:gridToolbarItem>
                        </ls:gridToolbar>
                        <ls:column name="uid" caption="用户ID"/>
                        <ls:column name="mobile" caption="手机号" />
                        <ls:column name="nickName" caption="昵称" />
                        <ls:column name="custTypeName" caption="用户类型" />
                        <ls:column name="custType" caption="用户类型" hidden="true"/>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        function select(){
            var items = custGrid.getCheckedItems();
            if(items.length == 0) {
                LS.message("info", "请至少选择一条记录！");
                return;
            }
            LS.parent().selectCust(items);
            LS.window.close();
        }

        function query(){
            if(!xpCustForm.valid()){
                return;
            }
            var data = xpCustForm.getFormData();
            custGrid.query("~/billing/couponPut/xp/getCustInfo",data);
        }

        function clearAll(){
            xpCustForm.clear();
        }
    </ls:script>
</ls:body>