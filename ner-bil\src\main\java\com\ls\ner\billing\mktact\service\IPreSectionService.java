package com.ls.ner.billing.mktact.service;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.mktact.bo.PreSectionBo;

public interface IPreSectionService {

	/**
	 * 获取段落列表
	 * @return
	 * <AUTHOR>
	 */
	public List<PreSectionBo> qryPreSections(PreSectionBo condition);

	/**
	 * 获取段落listMap
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryPreSectionsMap(Map<String, Object> map);

	/**
	 * 保存 -- 段落规则
	 * @param model
	 * @return
	 * <AUTHOR>
	 */
	public long savePreSection(PreSectionBo model);
	/**
	 * 删除 段落规则
	 * 
	 * @param presentSectionId
	 * @return
	 * <AUTHOR>
	 */
	public void operatePreSection(Long presentSectionId);

	/**
	 * 根据活动ID、预存金额 获取赠送金额
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public Map<String, Object> qryPreSectionValue(Map<String, Object> map);

	public Map<String, Object> qryTimeLimitById(long timeLimitId);

	public Map<String, Object> qryCycleById(long cycleTypeId);
	/**
	 * @param map
	 * @description 查询充值段落明细
	 * <AUTHOR>
	 * @create 2018-07-03 17:53:02
	 */
	List<Map<String,Object>> qryPreSectionsDetInfo(Map<String, Object> map);
	/**
	 * @param inMap
	 * @description 删除充值段落明细
	 * <AUTHOR>
	 * @create 2018-07-03 17:53:02
	 */
	void preSectionDetDel(Map<String, Object> inMap);

	/**
	 *@param
	 *@description 获取段落列表 ---新
	 *<AUTHOR>
	 *@create 2019/5/30 17:13
	 */
	public List<PreSectionBo> qryPreSectionsNew(PreSectionBo condition);

}
