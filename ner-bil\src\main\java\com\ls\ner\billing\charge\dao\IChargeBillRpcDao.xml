<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillRpcDao">

	<!-- 批量查询充电计费配置 -->
	<select id="queryChargeBillingConfs" parameterType="java.util.Map" resultType="java.util.Map">
		select  a.STATION_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
				a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
				a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
				a.CHARGE_PRICE,a.OPER_NO,date_format(a.CREATE_TIME,'%Y-%m-%d %H:%i:%s'),a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
				a.GATHER_DATA_TYPE,date_format(a.EFT_DATE,'%Y-%m-%d %H:%i:%s') EFT_DATE,a.INV_DATE,a.ORG_CODE,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a
		<where>
			<if test="chcStatus !=null and chcStatus !=''">
			    and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="billCtlMode !=null and billCtlMode !=''">
			    and a.BILL_CTL_MODE =#{billCtlMode}
			</if>
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO =#{chcNo}
			</if>
			<if test="custId !=null and custId !=''">
				and a.CUST_ID =#{custId}
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE LIKE CONCAT(#{orgCode},'%')
			</if>
			<if test="orgCodeList !=null and orgCodeList.size() > 0">
				and a.ORG_CODE in
				<foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
			    and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="stationIds !=null">
			    and a.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="stationList !=null">
				and a.STATION_ID in
				<foreach item="item" index="index" collection="stationList" open="(" separator="," close=")">
					#{item.stationId}
				</foreach>
			</if>
			<!--浙江电动自营 新版-->
			<if test="stationIdIsNull !=null and stationIdIsNull !=''">
				and (a.STATION_ID IS NULL or STATION_ID ='')
			</if>
			<if test="custIdIsNull !=null and custIdIsNull !=''">
				and (a.CUST_ID IS NULL or CUST_ID ='')
			</if>
		</where>
		order by a.STATION_ID,a.EFT_DATE desc
	</select>

	<!-- 批量查询充电分时设置 -->
	<select id="queryChargePeriods" resultType="java.util.Map">
		select
			b.CHC_NO,b.SN,b.BEGIN_TIME,b.END_TIME,b.PRICE,b.TIME_FLAG,b.ITEM_NO
		from
			E_CHARGE_PERIODS b
		<where>
			<if test="chcNo !=null and chcNo !=''">
			    and b.CHC_NO =#{chcNo}
			</if>
			<if test="chcNos !=null">
			    and b.CHC_NO in
				<foreach item="item" index="index" collection="chcNos" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by b.CHC_NO,b.sn asc
	</select>

	<!-- 通过stationId查询下发的计费模型 -->
	<select id="getStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
		a.CHC_NO chcNo,
		a.STATION_ID stationId,
		b.SYSTEM_ID systemId,
		b.SN,
		b.BEGIN_TIME beginTime,
		b.END_TIME endTime,
		FORMAT(b.PRICE, CAST(#{place} AS SIGNED )) PRICE,
		b.TIME_FLAG timeFlag
		FROM
		e_charge_billing_conf AS a
		INNER JOIN e_charge_periods AS b ON a.CHC_NO = b.CHC_NO
		<where>
			a.CHC_STATUS = '1'
			and NOW() >= a.EFT_DATE
			<if test="stationId !=null and stationId !=''">
				and a.STATION_ID =#{stationId}
			</if>
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO =#{chcNo}
			</if>
			<if test="itemNo !=null and itemNo !=''">
				and b.ITEM_NO =#{itemNo}
			</if>
		</where>
		order by b.TIME_FLAG asc
	</select>

	<!-- 通过stationId查询下发的新版计费模型 -->
	<select id="getNewStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
		t.CHC_NO     chcNo,
		t.STATION_ID stationId,
		b.SYSTEM_ID  systemId,
		b.SN,
		b.BEGIN_TIME beginTime,
		b.END_TIME endTime,
		FORMAT(b.PRICE, CAST(#{place} AS SIGNED )) PRICE,
		b.TIME_FLAG timeFlag,
		b.ITEM_NO itemNo
		FROM
		(
		SELECT
		a.CHC_NO,
		c.STATION_ID
		FROM
		e_charge_billing_conf a,
		e_station_billing c
		<where>
			a.CHC_STATUS = '1'
			and a.CHC_NO = c.CHC_NO
			<if test="stationId !=null and stationId !=''">
				and c.STATION_ID =#{stationId}
			</if>
		</where>
		) t
		INNER JOIN e_charge_periods b ON t.CHC_NO = b.CHC_NO
		<where>
			<if test="itemNo !=null and itemNo !=''">
				and b.ITEM_NO =#{itemNo}
			</if>
		</where>
		ORDER BY
		b.TIME_FLAG ASC
	</select>

	<!-- 通过stationId查询下发的新版计费模型 -->
	<select id="getPileStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			t.CHC_NO     chcNo,
			t.STATION_ID stationId,
			b.SYSTEM_ID  systemId,
			b.SN,
			b.BEGIN_TIME beginTime,
			b.END_TIME endTime,
			FORMAT(b.PRICE, 2) PRICE,
			b.TIME_FLAG timeFlag
		FROM
		(
		SELECT
		a.CHC_NO,
		c.STATION_ID
		FROM
		e_charge_billing_conf a,
		e_pile_billing c
		<where>
			a.CHC_STATUS = '1'
			and a.CHC_NO = c.CHC_NO
			<if test="stationId !=null and stationId !=''">
				and c.STATION_ID =#{stationId}
			</if>
			<if test="pileId !=null and pileId !=''">
				and c.PILE_ID =#{pileId}
			</if>
		</where>
		) t
		INNER JOIN e_charge_periods b ON t.CHC_NO = b.CHC_NO
		<where>
			<if test="itemNo !=null and itemNo !=''">
				and b.ITEM_NO =#{itemNo}
			</if>
		</where>
		ORDER BY
		b.TIME_FLAG ASC
	</select>


	<select id="getAppendCharItem" parameterType="java.util.Map" resultType="java.util.Map">
		select
		 SYSTEM_ID itemId, DATE_FORMAT(DATA_OPER_TIME,'%Y-%m-%d %H:%i:%s') dataOperTime, DATA_OPER_TYPE dataOperType, ITEM_NO itemNO, ITEM_NAME itemName, ITEM_TYPE ItemType,
		 ITEM_UNIT itemUnit, SN sn
		from e_append_charge_item
		<where>
			ITEM_STATUS = #{itemStatus}
		<if test="pBe !=null and pBe !=''">
			and P_BE =#{pBe}
		</if>
		<if test="itemType !=null and itemType !=''">
			and ITEM_TYPE =#{itemType}
		</if>
		</where>
	</select>

	<!--新增站点计费配置-->
	<insert id="insertChargeBilling" parameterType="java.util.Map">
		INSERT into e_station_billing  (STATION_ID,CHC_NO,ORG_CODE,DATA_OPER_TIME,DATA_OPER_TYPE)
		VALUES(#{stationId},#{chcNo},IFNULL(#{orgCode},null),now(),'I')
	</insert>

	<!--新增站点计费配置-->
	<insert id="insertPileChargeBilling" parameterType="java.util.Map">
		insert into e_pile_billing(
			ORG_CODE,
			STATION_ID,
			CHC_NO,
			PILE_ID,
			DATA_OPER_TIME,
			DATA_OPER_TYPE
		) values
		<foreach collection="pileBillList" separator="," item="item">
			(
				<choose><when test="item.orgCode !=null and item.orgCode !=''">#{item.orgCode},</when><otherwise>null,</otherwise></choose>
				<choose><when test="item.stationId !=null and item.stationId !=''">#{item.stationId},</when><otherwise>null,</otherwise></choose>
				<choose><when test="item.chcNo !=null and item.chcNo !=''">#{item.chcNo},</when><otherwise>null,</otherwise></choose>
				<choose><when test="item.pileId !=null and item.pileId !=''">#{item.pileId},</when><otherwise>null,</otherwise></choose>
				now(),
				'I'
			)
		</foreach>
	</insert>

	<!--修改站点计费配置-->
	<update id="updateChargeBilling" parameterType="java.util.Map">
		UPDATE e_station_billing
		<set>
		<if test="chcNo !=null and chcNo !=''">CHC_NO=#{chcNo},</if>
		<if test="orgCode !=null and orgCode !=''">ORG_CODE=#{orgCode},</if>
		DATA_OPER_TIME=now(),
		DATA_OPER_TYPE='U'
		</set>
		WHERE  STATION_ID=#{stationId}
	</update>

	<!--删除计费配置-->
	<delete id="delChargeBilling" parameterType="java.util.Map">
		DELETE  from e_station_billing WHERE station_id=#{stationId}
	</delete>

	<!--删除桩计费配置-->
	<delete id="delPileChargeBilling" parameterType="java.util.Map">
		DELETE from e_pile_billing WHERE station_id=#{stationId}
		<if test="pileId != null and pileId != ''">
			and PILE_ID = #{pileId}
		</if>
	</delete>

	<!--查询新版计费-->
	<select id="queryNewChargeBillingConfs" parameterType="java.util.Map" resultType="java.util.Map">
		select  b.STATION_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d %H:%i:%s') CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,a.BILLING_TAG,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_station_billing b
		<where>
			 a.CHC_NO=b.CHC_NO
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
            <if test="chcNo !=null and chcNo !=''">
                and a.CHC_NO =#{chcNo}
            </if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="stationIds !=null">
				and b.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="stationList !=null">
				and b.STATION_ID in
				<foreach item="item" index="index" collection="stationList" open="(" separator="," close=")">
					#{item.stationId}
				</foreach>
			</if>
			<if test="stationId !=null and stationId !=''">
				and b.STATION_ID =#{stationId}
			</if>
		</where>
		order by a.EFT_DATE desc
	</select>

	<!--查询桩计费-->
	<select id="queryNewChargePileBillingConfs" parameterType="java.util.Map" resultType="java.util.Map">
		select  b.STATION_ID,b.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,a.BILLING_TAG,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d %H:%i:%s') CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_pile_billing b
		<where>
			 a.CHC_NO=b.CHC_NO
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="pileId !=null and pileId != ''">
				and b.PILE_ID = #{pileId}
			</if>
			<if test="pileIds !=null">
				and b.PILE_ID in
				<foreach item="item" index="index" collection="pileIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by a.EFT_DATE desc
	</select>

	<!--新增大客户计费配置-->
	<insert id="insertCustGroupChargeBilling" parameterType="java.util.Map">
		INSERT into e_cust_billing  (GROUP_ID,CHC_NO,DATA_OPER_TYPE)
		VALUES(#{groupId},#{chcNo},'I')
	</insert>

	<!--删除大客户计费配置-->
	<delete id="delCustGroupChargeBilling" parameterType="java.util.Map">
		DELETE  from e_cust_billing WHERE GROUP_ID=#{groupId}
	</delete>

	<!--查询大客户计费配置-->
	<select id="queryCustGroupChargeBilling" parameterType="java.util.Map" resultType="java.util.Map">
		select  a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d %H:%i:%s') CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,	b.GROUP_ID,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_cust_billing b
		<where>
			a.CHC_NO=b.CHC_NO
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="groupId !=null and groupId !=''">
				and b.GROUP_ID =#{groupId}
			</if>
		</where>
		order by a.EFT_DATE desc
	</select>

	<select id="queryUserChargeBilling" parameterType="java.util.Map" resultType="java.util.Map">
		select  a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,DATE_FORMAT(a.CREATE_TIME,'%Y-%m-%d %H:%i:%s') CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.RANGE_FLAG as rangeFlag
		from E_CHARGE_BILLING_CONF a
		<where>
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="custId !=null and custId !=''">
				and a.CUST_ID =#{custId}
			</if>
		</where>
	</select>

	<select id="checkIsExistByStationIdAndChcNo" resultType="int">
		select
			count(*)
		from e_charge_billing_station
		where STATION_ID = #{stationId} and CHC_NO = #{chcNo}
	</select>

	<select id="queryPileBillingList" resultType="java.util.Map">
		select b.CHC_NO chcNo,
		       b.CHC_NAME chcName
		from e_pile_billing a
		join e_charge_billing_conf b on b.CHC_NO = a.CHC_NO
		where a.PILE_ID = #{pileId}
		limit 1
	</select>
</mapper>
