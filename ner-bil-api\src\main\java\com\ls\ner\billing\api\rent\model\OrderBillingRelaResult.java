package com.ls.ner.billing.api.rent.model;

import java.math.BigDecimal;
import java.util.Map;

public class OrderBillingRelaResult {
	//押金计费结果
	private BigDecimal depositResult;
	
	//附加费用计费结果
	private BigDecimal attachResult;
	
	//主费用项计费结果
	private BigDecimal mainResult;
	
	//主费用项里程计费结果
	private BigDecimal millResult;
	
	//主费用项时间计费结果
	private BigDecimal timeResult;
	
	//总的计费结果
	private BigDecimal result;
	
	private long minutes;
	
	private long meters;

	private Map<String,Object> prcCharge;
	public BigDecimal getDepositResult() {
		return depositResult;
	}

	public void setDepositResult(BigDecimal depositResult) {
		this.depositResult = depositResult;
	}

	public BigDecimal getAttachResult() {
		return attachResult;
	}

	public void setAttachResult(BigDecimal attachResult) {
		this.attachResult = attachResult;
	}

	public BigDecimal getMainResult() {
		return mainResult;
	}

	public void setMainResult(BigDecimal mainResult) {
		this.mainResult = mainResult;
	}

	public BigDecimal getResult() {
		return result;
	}

	public void setResult(BigDecimal result) {
		this.result = result;
	}

	public BigDecimal getMillResult() {
		return millResult;
	}

	public void setMillResult(BigDecimal millResult) {
		this.millResult = millResult;
	}

	public BigDecimal getTimeResult() {
		return timeResult;
	}

	public void setTimeResult(BigDecimal timeResult) {
		this.timeResult = timeResult;
	}

	public long getMinutes() {
		return minutes;
	}

	public void setMinutes(long minutes) {
		this.minutes = minutes;
	}

	public long getMeters() {
		return meters;
	}

	public void setMeters(long meters) {
		this.meters = meters;
	}

	public Map<String, Object> getPrcCharge() {
		return prcCharge;
	}

	public void setPrcCharge(Map<String, Object> prcCharge) {
		this.prcCharge = prcCharge;
	}
	
}
