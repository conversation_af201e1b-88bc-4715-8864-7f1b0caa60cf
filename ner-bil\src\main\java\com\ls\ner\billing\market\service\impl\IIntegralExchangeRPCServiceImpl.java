package com.ls.ner.billing.market.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.ls.ner.aly.api.IInvoiceAlyRpcService;
import com.ls.ner.billing.api.market.service.IIntegeralCustRPCService;
import com.ls.ner.billing.api.market.service.IIntegralExchangeRPCService;
import com.ls.ner.billing.api.market.vo.EvaVO;
import com.ls.ner.billing.api.market.vo.IntegralExchangeBo;
import com.ls.ner.billing.api.market.vo.IntegralInvoiceUpdateBo;
import com.ls.ner.billing.api.vip.bo.VipRPCBo;
import com.ls.ner.billing.gift.constant.IntegralEnum;
import com.ls.ner.billing.gift.service.IRechargeCardService;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPutBo;
import com.ls.ner.billing.market.bo.CouponPutDetBo;
import com.ls.ner.billing.market.bo.IntegeralCustBo;
import com.ls.ner.billing.market.bo.IntegeralCustLogBo;
import com.ls.ner.billing.market.dao.ICouponPutDao;
import com.ls.ner.billing.market.dao.IIntegeralCustRPCDao;
import com.ls.ner.billing.market.dao.IntegralExchangeDao;
import com.ls.ner.billing.market.vo.ExclusiveOfferVo;
import com.ls.ner.billing.market.vo.IntegralExchangeVo;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.service.IVipLevelService;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.order.api.service.IOrderRpcService;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.pub.api.goods.bo.GoodsBo;
import com.ls.ner.pub.api.goods.service.IGoodsRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service(target = {ServiceType.RPC}, value = "integeralExchangeRPCService")
public class IIntegralExchangeRPCServiceImpl implements IIntegralExchangeRPCService {
    private static final Logger logger = LoggerFactory.getLogger(IIntegralExchangeRPCServiceImpl.class);

    @Autowired
    private IntegralExchangeDao integralExchangeDao;

    @Autowired(required = true)
    private IIntegeralCustRPCDao iIntegeralCustRPCDao;

    @ServiceAutowired(serviceTypes =  ServiceType.RPC,value = "goodsRpcService")
    private IGoodsRpcService goodsRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "seqRpcService")
    private ISeqRpcService seqRpcService;

    @Autowired(required = true)
    private ICouponPutDao putDao; // 发放主表

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "accuCouponRpcService")
    private ICouponRpcService accuCouponRpcService; // 支付中心-优惠券接口

    @Autowired
    private IMemberBenefitsDao memberBenefitsDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
    private IDefrayAccountRpcService defrayAccountRpcService;

    @ServiceAutowired(value = "integeralCustRPCService", serviceTypes = ServiceType.RPC)
    private IIntegeralCustRPCService service;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "orderRpcService")
    private IOrderRpcService orderRpcService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="attachService")
    private IAttachRpcService attachService;

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "vipLevelService")
    private IVipLevelService vipLevelService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="goodsRpcService")
    private IGoodsRpcService iGoodsRpcService;
    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="invoiceAlyRpcService")
    private IInvoiceAlyRpcService iInvoiceAlyRpcService;


    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "rechargeCardService")
    private IRechargeCardService iRechargeCardService;

    @Override
    public Map queryList(IntegralExchangeBo bo) {
        IntegralExchangeVo exchangeVo = new IntegralExchangeVo();
        BeanUtils.copyProperties(bo, exchangeVo);
        int count = integralExchangeDao.getIntegralExchangeListCount(exchangeVo);

        Map returnMap = new HashMap<>();
        List<IntegralExchangeBo> resultList = Lists.newArrayList();
        if (count>0){
            int pageNum = bo.getPageNum();
            int totalNum = bo.getTotalNum();
            // 计算开始位置
            int begin = (pageNum - 1) * totalNum;
            exchangeVo.setStart(begin);
            exchangeVo.setLimit(totalNum);
            logger.info("============exchangeVo:"+ JsonUtil.obj2Json(exchangeVo));
            List<IntegralExchangeVo> exchangeVoList = integralExchangeDao.getIntegralExchangeListByWX(exchangeVo);
            for (IntegralExchangeVo item : exchangeVoList) {
                IntegralExchangeBo res = new IntegralExchangeBo();
                BeanUtils.copyProperties(item, res);
                resultList.add(res);
            }
        }
        returnMap.put("exchangeList",resultList);
        returnMap.put("count",count);

        return returnMap;
    }

    @Override
    public Map<String, Object> queryDetail(IntegralExchangeBo bo) {
        Map<String, Object> map = new HashMap<>();
        IntegralExchangeVo exchangeVo = new IntegralExchangeVo();
        BeanUtils.copyProperties(bo, exchangeVo);
        List<IntegralExchangeVo> exchangeVoList = integralExchangeDao.getIntegralExchangeList(exchangeVo);
        if (CollectionUtils.isEmpty(exchangeVoList)) {
            return null;
        }

        IntegralExchangeBo res = new IntegralExchangeBo();
        BeanUtils.copyProperties(exchangeVoList.get(0), res);
        EvaVO evaVO = integralExchangeDao.queryEva(exchangeVo);
        if (evaVO != null) {
            List<Map<String, String>> list = integralExchangeDao.queryEvaItems(evaVO.getEvaId());
            evaVO.setEvaItems(list);
        }
        AttachBo attach = new AttachBo();
        attach.setRelaTable("orderEva");
        attach.setRelaId(res.getIntegralId());
        attach.setContentType("02");
        List<AttachBo> batchAttach = attachService.getBatchAttach(attach);
        logger.info("评价图片入参：{},返回数据：{}", JsonUtil.obj2Json(attach), JsonUtil.obj2Json(batchAttach));
        if (!CollectionUtils.isEmpty(batchAttach) && evaVO != null) {
            evaVO.setFileIds(batchAttach.stream().map(AttachBo::getAttachId).collect(Collectors.toList()));
        }
        logger.info("商品评价：{}", JsonUtil.obj2Json(evaVO));
        map.put("exchangeBo", res);
        map.put("evaDetail", evaVO);
        return map;
    }

    @Override
    @Transactional
    public Map deal(IntegralExchangeBo bo) {
        logger.debug("deal.IntegralExchangeBo:{}", IJsonUtil.obj2Json(bo));
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //获取用户兑换权限
        Map<String, Object> benefits = service.getBenefitsUser(bo.getCustId());
        if (benefits.isEmpty()||!benefits.get("isExchange").equals("1")){
            resultMap.put("ret", "400");
            resultMap.put("msg", "用户无兑换商品权限");
            return resultMap;
        }
        //消耗积分
        String integral = bo.getIntegral();
        //生成兑换记录
        Map<String, Object> map = goodsRpcService.queryGoodsInfo(bo.getGoodsId());
        GoodsBo goods = (GoodsBo) map.get("detail");
        logger.info("商品详情：{}", JsonUtil.obj2Json(goods));
        //商品限购数量
        int goodsPurchase =  Integer.parseInt(goods.getGoodsPurchase());
        IntegralExchangeVo countVo = new IntegralExchangeVo();
        countVo.setCustId(bo.getCustId());
        countVo.setGoodsId(goods.getGoodsId());
        int exchangeCount = integralExchangeDao.getExchangeCount(countVo);
        int num = Integer.parseInt(bo.getNumber());
        if ((exchangeCount + num) > goodsPurchase){
            resultMap.put("ret", "400");
            resultMap.put("msg", "该商品已超出限购数量");
            return resultMap;
        }
        String orderNo = seqRpcService.getAppNo();
        IntegralExchangeVo exchangeVo = new IntegralExchangeVo();
        exchangeVo.setIntegralId(orderNo);
        exchangeVo.setGoodsId(goods.getGoodsId());
        exchangeVo.setCustId(bo.getCustId());
        exchangeVo.setCustMobile(bo.getCustMobile());
        exchangeVo.setExchangeTime(DateTools.getStringDateShort(DateTools.YMDHMS));
        // 01 虚拟商品 直接发货
        if (goods.getGoodsType().equals("01")){
            exchangeVo.setStatus("02");
            exchangeVo.setGoodsFreight("02");
            int failCount = 0;
            Map<String, Object> accMap = defrayAccountRpcService.qryAccMapByCustId(bo.getCustId());
            if (goods.getGoodsVrType().equals("01")){
                // 获取accId
                String accId = MapUtils.getValue(accMap,"accId");
                //优惠券
                Map<String, String> custMap = new HashMap<>();
                custMap.put("custId", bo.getCustId());
                custMap.put("mobile", bo.getCustMobile());
                List<Map<String, String>> list = new ArrayList<>();
                list.add(custMap);
                String cpnId = goods.getGoodsVrCoupon();
                custMap.put("accId", accId);
                custMap.put("cpnId", cpnId);
                int number = Integer.parseInt(bo.getNumber());
                List<ExclusiveOfferVo> coupons = new ArrayList<>();
                ExclusiveOfferVo offerVo = new ExclusiveOfferVo();
                offerVo.setCouponId(cpnId);
                coupons.add(offerVo);
                CouponBo coupon = memberBenefitsDao.queryCpnById(coupons).get(0);
                // 获取用户已领取该优惠券数量
                int couponNum = defrayAccountRpcService.queryGetCouponNum(custMap);
                // 判断是否超限
                int limitNum = Integer.parseInt(coupon.getLimGetNum());
                if (couponNum >= limitNum) {
                    resultMap.put("ret", "400");
                    resultMap.put("msg", "用户领取该优惠券已达限领数量:【" + limitNum + "】");
                    logger.debug("用户领取该优惠券已达限领数量,resultMap:{}", resultMap);
                    return resultMap;
                } else {
                    if (number > (limitNum - couponNum)) {
                        resultMap.put("ret", "400");
                        resultMap.put("msg", "当前兑换数量:【"+ number +"】超出优惠券剩余限领数量:【"+ (limitNum - couponNum) + "】");
                        logger.debug("当前兑换数量超出优惠券剩余限领数量,resultMap:{}", resultMap);
                        return resultMap;
                    }
                }
                for (int i = 0; i < number; i++) {
                    CouponPutBo putBo = new CouponPutBo();
                    putBo.setCpnId(coupon.getCpnId());
                    putBo.setGetChannel("02");
                    putBo.setBusiType("02");
                    putBo.setLimGetNum(coupon.getLimGetNum());
                    putBo.setInvDate(coupon.getInvDate());
                    putBo.setEftDate(coupon.getEftDate());
                    putBo.setTimeDuration(coupon.getTimeDuration());
                    putBo.setTimeUnit(coupon.getTimeUnit());
                    putBo.setCpnTimeType(coupon.getCpnTimeType());
                    put(putBo,list,failCount,resultMap);
                }
                if (Integer.parseInt(bo.getNumber())==failCount){
                    resultMap.put("ret", "400");
                    resultMap.put("msg", "商品兑换失败");
                    logger.debug("积分兑换优惠券,resultMap:{}", resultMap);
                    return resultMap;
                }
                int succNum = Integer.parseInt(bo.getNumber()) - failCount;
                integral =  String.valueOf(succNum * Integer.parseInt(goods.getGoodsIntegral()));
                bo.setNumber(String.valueOf(succNum));
                resultMap = MapUtils.createSucResult();
            }else if(goods.getGoodsVrType().equals("08")){
                Map<String, String> cardMap = new HashMap<>();
                cardMap.put("custId", bo.getCustId());
                cardMap.put("mobile", bo.getCustMobile());
                String cardId = goods.getGoodsVrCard();
                cardMap.put("cardId", cardId);
                cardMap.put("num", String.valueOf(num));
                resultMap = iRechargeCardService.rechargeCardCodeByIntegral(cardMap);
            }else{
                //PLUS会员兑换
                Map<String, Object> inMap = new HashMap<>();
                inMap.put("custId",bo.getCustId());
                inMap.put("payChannel",bo.getPayChannel());
                inMap.put("vipType",cover(goods.getGoodsVrType()));
                inMap.put("num", num);
                inMap.put("integral",integral);
                inMap.put("amount",BigDecimal.ZERO);
                if (StringUtils.equals(goods.getGoodsMode(), "02")) {
                    inMap.put("amount", goods.getGoodsAmt().multiply(new BigDecimal(bo.getNumber())));
                }
                resultMap = defrayAccountRpcService.pointsRedemptionForMembers(inMap);

                if ("400".equals(StringUtil.nullForString(resultMap.get("ret")))){
                    logger.debug("积分兑换PLUS会员,resultMap:{}", resultMap);
                    return resultMap;
                }
                //兑换成功发券
                String vipExpireTimeStr = MapUtils.getValue(accMap,"vipExpireTimeStr");

                if (StringUtil.isEmpty(vipExpireTimeStr)||LocalDateTime.parse(vipExpireTimeStr,
                        DateTimeFormatter.ofPattern(DateTools.YMDHMS)).isBefore(LocalDateTime.now())){
                    //专享优惠
                    VipRPCBo vipRPCBo = new VipRPCBo();
                    vipRPCBo.setMobile(bo.getCustMobile());
                    vipRPCBo.setCustId(bo.getCustId());
                    for (int i = 0; i < num; i++) {
                        vipLevelService.exclusiveOffer(vipRPCBo);
                    }
                }
                if (StringUtil.isEmpty(vipExpireTimeStr)){
                    //开卡礼
                    VipRPCBo vipRPCBo = new VipRPCBo();
                    vipRPCBo.setMobile(bo.getCustMobile());
                    vipRPCBo.setCustId(bo.getCustId());
                    for (int i = 0; i < num; i++) {
                        vipLevelService.openGift(vipRPCBo);
                    }
                    //升级礼包
                    vipLevelService.upgradeGift(vipRPCBo);
                }

            }
        }else {
            //02 实物 代发货
            exchangeVo.setStatus("01");
            exchangeVo.setConsignee(bo.getConsignee());
            exchangeVo.setAddress(bo.getAddress());
            exchangeVo.setMobile(bo.getMobile());
            resultMap = MapUtils.createSucResult();
        }
        exchangeVo.setNumber(bo.getNumber());
        exchangeVo.setIntegral(integral);
        exchangeVo.setGoodsType(goods.getGoodsType());
        exchangeVo.setGoodsMode(goods.getGoodsMode());
        exchangeVo.setGoodsAmt(goods.getGoodsAmt());
        exchangeVo.setGoodsName(goods.getGoodsName());
        exchangeVo.setFileId(bo.getFileId());
        exchangeVo.setGoodsFreight(goods.getGoodsFreight());
        exchangeVo.setPayChannel(bo.getPayChannel());
        integralExchangeDao.insert(exchangeVo);

        //扣减商品数量
        GoodsBo goodsBo = new GoodsBo();
        goodsBo.setGoodsId(bo.getGoodsId());
        goodsBo.setDeductNum(bo.getNumber());
        goodsRpcService.deductGoodsNum(goodsBo);
        //扣减积分
        IntegeralCustBo integeralCustBo = new IntegeralCustBo();
        integeralCustBo.setCustNo(bo.getCustId());
        //用户现有积分
        String integeralNum = iIntegeralCustRPCDao.getCustIntegeralNum(integeralCustBo);

        // 如果是充值卡兑换，需要考虑充值卡赠送的积分
        if ("08".equals(goods.getGoodsVrType()) && resultMap != null && resultMap.containsKey("integralNum")) {
            // 从充值卡兑换结果中获取赠送的积分数量
            try {
                int giftIntegral = (Integer) resultMap.get("integralNum");
                if (giftIntegral > 0) {
                    // 将赠送的积分加到当前积分中
                    integeralNum = new BigDecimal(integeralNum).add(new BigDecimal(giftIntegral)).toString();
                    logger.info("充值卡兑换，用户原积分加上赠送积分后：{}", integeralNum);
                }
            } catch (Exception e) {
                logger.error("获取充值卡赠送积分失败，使用原积分进行扣减", e);
            }
        }

        // 当前用户积分
        String curNum = new BigDecimal(integeralNum).subtract(new BigDecimal(integral)).toString();
        integeralCustBo.setIntegralNumber(curNum);
        iIntegeralCustRPCDao.updateIntegeralCust(integeralCustBo);
        //记录积分流水
        IntegeralCustLogBo logBo = new IntegeralCustLogBo();
        logBo.setIntegralNo(orderNo);//积分流水号
        // 积分+金额模式
        if (StringUtils.equals(goods.getGoodsMode(), "02")) {
            Map<String, Object> inMap = new HashMap<>();
            inMap.put("custId", bo.getCustId());
            inMap.put("changeAmt", goods.getGoodsAmt().multiply(new BigDecimal(bo.getNumber())));
            inMap.put("relateId", exchangeVo.getIntegralId());
            inMap.put("appNo", logBo.getIntegralNo());
            inMap.put("goodsName", goods.getGoodsName());
            // 扣除余额
            logger.info("兑换商品入参：{}", JsonUtil.obj2Json(inMap));
            defrayAccountRpcService.exchangeGoodsByBalance(inMap);
        }
        logger.info("当前用户剩余积分：{}，生成订单号：{}", curNum, orderNo);
        // 生成商品兑换订单
        orderRpcService.createGoodsOrder(
                ImmutableMap.of("orderNo", orderNo,
                        "custId", bo.getCustId(),
                        "mobile", bo.getCustMobile()));
        logBo.setIntegralNumber(curNum);
        logBo.setPhone(bo.getCustMobile());
        logBo.setEventType(IntegralEnum.PRODUCT_REDEMPTION.getCode());
        logBo.setEventName(IntegralEnum.PRODUCT_REDEMPTION.getName());
        logBo.setChargeType("02");
        logBo.setChargeNum(integral);
        iIntegeralCustRPCDao.addIntegeralCustLog(logBo);
        return resultMap;
    }

    private String cover(String goodsVrType) {
       if (goodsVrType.equals("03")) {
            //季卡
            return "02";
        } else if (goodsVrType.equals("04")) {
            //年卡
            return "03";
        }else {
           //月卡
           return "01";
       }
    }

    public void put(CouponPutBo bo, List<Map<String, String>> custList,int failCount,Map<String, Object> retMap) {
        logger.debug("优惠券兑换入参:{}", JSONObject.toJSONString(bo));
        StringBuffer outMsg = new StringBuffer();
        if (custList != null && custList.size() > 0) {
            try {
                //优惠券id
                String cpnId = bo.getCpnId();
                //每人限领
                String limGetNum = bo.getLimGetNum();
                String putTime = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                bo.setPutTime(putTime);
                // 生效~失效时间计算
                String eftDate = "", invDate = "";
                Map<String, String> timeMap = this.calcTimeRanges(bo);
                if (timeMap != null && timeMap.size() > 0) {
                    String outCode = timeMap.get("outCode");
                    if ("0".equals(outCode)) {
                        eftDate = timeMap.get("eftDate");
                        invDate = timeMap.get("invDate");
                    } else {
                        logger.error("优惠券校验:{}", timeMap.get("outMsg"));
                        return;
                    }
                }
                // 新增发放
                bo.setPutEmpType("01");
                bo.setPutNum(custList.size() + "");
                String getChannel = bo.getGetChannel();
                if (StringUtil.isBlank(getChannel)) {
                    getChannel = "01";
                }
                bo.setPutChannel(getChannel);
                int flag = putDao.insertCouponPut(bo);
                // 调用RPC支付中心 RPC04-02-003优惠券发放
                String putId = bo.getPutId();
                if (StringUtil.isNotBlank(putId)) {
                    //  新增发放明细
                    for (Map<String, String> map : custList) {
                        CouponPutDetBo det = new CouponPutDetBo();
                        det.setPutId(putId);
                        det.setPutTime(putTime);
                        String detCustId = map.get("custId");
                        String detMobile = map.get("mobile");
                        if (StringUtil.isBlank(detCustId) && StringUtil.isBlank(detMobile)) {
                            continue;
                        }
                        if (StringUtil.isBlank(detCustId)) detCustId = null;
                        det.setCustId(detCustId);
                        det.setMobile(detMobile);
                        det.setPutFlag("0");
                        det.setFailReason("未调用【RPC04-02-003优惠券发放】接口");
                        putDao.insertCouponPutDet(det);
                    }
                    Map<String, Object> inMap = new HashMap<String, Object>();
                    inMap.put("cpnId", cpnId);
                    inMap.put("limGetNum", limGetNum);
                    inMap.put("eftDate", eftDate);
                    inMap.put("invDate", invDate);
                    inMap.put("putTime", putTime);
                    inMap.put("custList", custList);
                    inMap.put("getChannel", "02");//02现场，领取渠道channel，存d_account_coupon表的领取来源GET_CHANNEL
                    inMap.put("handFlag", "01");
                    // add biaoxiangd 2017-09-27 添加业务类01租车02充电
                    String prodBusiType = bo.getBusiType();
                    inMap.put("prodBusiType", prodBusiType);
                    inMap.put("getSource", "09");//09优惠券发放，领取来源actType，存d_account_coupon表的领取来源GET_SOURCE
                    logger.error("积分兑换优惠券券入参:{}", inMap);

                    // RPC04-02-003优惠券发放
                    Map<String, Object> prcMap = accuCouponRpcService.couponPut(inMap);
                    if (prcMap != null && prcMap.size() > 0) {
                        String putNum = String.valueOf(prcMap.get("putNum"));
                        if (StringUtil.isNotBlank(putNum) && StringUtils.isNumeric(putNum)) {
                            if (Integer.parseInt(putNum) > 0) {
                                outMsg.append("发送成功笔数【" + putNum + "】;");
                            }
                            // 更新发放主表的发放数量
                            bo.setPutNum(putNum);
                            flag = putDao.updateCouponPut(bo);
                            if (flag > 0) {
                                // 更新发放详细表的成功标志
                                List<Map<String, Object>> prcCustList = (List<Map<String, Object>>) prcMap.get("custList");
                                if (prcCustList != null && prcCustList.size() > 0) {
                                    for (int i = 0; i < prcCustList.size(); i++) {
                                        Map<String, Object> prcCustMap = prcCustList.get(i);
                                        if (prcCustMap != null && prcCustMap.size() > 0) {
                                            String custId = (String) prcCustMap.get("custId");
                                            String mobile = (String) prcCustMap.get("mobile");
                                            if (StringUtil.isBlank(custId) && StringUtil.isBlank(mobile)) {
                                                continue;
                                            } else {
                                                String putFlag = (String) prcCustMap.get("putFlag");
                                                if (StringUtil.isNotBlank(putFlag)) {
                                                    CouponPutDetBo det = new CouponPutDetBo();
                                                    det.setPutId(putId);
                                                    det.setCustId(custId);
                                                    det.setMobile(mobile);
                                                    det.setPutFlag(putFlag);
                                                    det.setFailReason((String) prcCustMap.get("failReason"));
                                                    putDao.updateCouponPutDet(det);
                                                    if ("0".equals(putFlag)) {
                                                        failCount++;
//                                                                outMsg.append("发放失败原因【" + String.valueOf(prcCustMap.get("failReason")) + "】");
                                                    } else {
                                                        //todo 发放成功后 通知接口
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    if (failCount > 0) {
                                        outMsg.append("发送失败笔数【" + failCount + "】");
                                    }
                                } else {
                                    throw new BusinessWarning("RPC04-02-003优惠券发放接口返回用户集为空，请核查", this.getClass());
                                }
                            } else {
                                throw new BusinessWarning("RPC04-02-003优惠券发放接口更新发放信息失败，请核查", this.getClass());
                            }
                        } else {
                            throw new BusinessWarning("RPC04-02-003优惠券发放接口获取已领数量数据失败，请核查", this.getClass());
                        }
                    } else {
                        throw new BusinessWarning("RPC04-02-003优惠券发放接口获取数据失败，请核查", this.getClass());
                    }

                } else {
                    // 抛异常，发放主表生成失败
                    throw new BusinessWarning("未生成发放标识，请核查", this.getClass());
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessWarning(e.getLocalizedMessage(), CouponPutServiceImpl.class);
            }
        } else {
            throw new BusinessWarning("未选择发放的客户，请核查", this.getClass());
        }
        retMap.put("outMsg", outMsg.toString());
        logger.error("积分兑换优惠券:{}", JsonUtil.obj2Json(retMap));
    }

    private Map<String, String> calcTimeRanges(CouponPutBo bo) {
        Map<String, String> retMap = new HashMap<String, String>();
        String outCode = "0", outMsg = "ok";
        // 生效日期,失效日期
        String eftDate = "", invDate = "";
        try {
            if (bo != null) {
                String cpnTimeType = bo.getCpnTimeType();
                if (StringUtil.isNotBlank(cpnTimeType)) {
                    if ("2".equals(cpnTimeType)) {
                        // 日、月、年转换
                        SimpleDateFormat bgnSdf = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat endSdf = new SimpleDateFormat("yyyy-MM-dd");
                        Calendar bgnCale = Calendar.getInstance();
                        Calendar endCale = Calendar.getInstance();
                        // 生效时间计算
                        String bgnDate = bo.getPutTime();
                        if (StringUtil.isBlank(bgnDate)) {
                            bgnDate = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                        }
                        bgnCale.setTime(bgnSdf.parse(bgnDate));
                        eftDate = bgnSdf.format(bgnCale.getTime());
                        int iYear = 0, iMonth = 0, iDay = 0;
                        if (!StringUtils.isBlank(eftDate)) {
                            iYear = Integer.parseInt(eftDate.substring(0, 4));
                            iMonth = Integer.parseInt(eftDate.substring(5, 7)) - 1;
                            iDay = Integer.parseInt(eftDate.substring(8, 10));
                        } else {
                            retMap.put("outCode", "-1");
                            retMap.put("outMsg", "生效时间为空，请核查");
                            return retMap;
                        }
                        // 失效时间计算
                        String timeUnit = bo.getTimeUnit();
                        if (StringUtil.isBlank(timeUnit)) {
                            timeUnit = "1";
                        }
                        String timeDuration = bo.getTimeDuration();
                        if (StringUtil.isNotBlank(timeDuration) && StringUtils.isNumeric(timeDuration)) {
                            int iTimeDuration = Integer.parseInt(timeDuration);
                            switch (timeUnit) {
                                case "1": // 日
                                    iTimeDuration = iTimeDuration + iDay;
                                    endCale.set(Calendar.DATE, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "4": // 月
                                    iTimeDuration = iTimeDuration + iMonth;
                                    endCale.set(Calendar.MONTH, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "5": // 年
                                    iTimeDuration = iTimeDuration + iYear;
                                    endCale.set(Calendar.YEAR, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                            }
                        } else {
                            outCode = "-1";
                            outMsg = "生效时间偏移量数据错误，请核查";
                        }
                    } else {
                        eftDate = bo.getEftDate();
                        invDate = bo.getInvDate();
                    }
                } else {
                    outCode = "-1";
                    outMsg = "优惠券生效时间类型为空，请核查";
                }
            } else {
                outCode = "-1";
                outMsg = "转换日期函数参数为空，请核查";
            }
        } catch (ParseException e) {
            e.printStackTrace();
            outCode = "-1";
            outMsg = "日期转换错误，请核查";
        }
        retMap.put("eftDate", eftDate);
        retMap.put("invDate", invDate);
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        return retMap;
    }



    @Override
    public void insert(IntegralExchangeBo bo) {
        IntegralExchangeVo exchangeVo = new IntegralExchangeVo();
        BeanUtils.copyProperties(bo, exchangeVo);
        integralExchangeDao.insert(exchangeVo);
    }

    @Override
    public void updateStatus(String orderNo) {
        IntegralExchangeVo vo = new IntegralExchangeVo();
        vo.setIntegralId(orderNo);
        vo.setStatus("03");//已评价
        integralExchangeDao.update(vo);
    }



    @Override
    public List<Map<String,Object>> integralListEnabledInvoices(Map<String, Object> map) {
        List<Map<String,Object>> list = iInvoiceAlyRpcService.integralListEnabledInvoices(map);
        for(Map<String,Object> integralMap : list){
            if(StringUtil.isNotBlank(integralMap.get("goodsId"))){
                Map<String, Object> queryGoodsInfos = iGoodsRpcService.queryGoodsInfo(integralMap.get("goodsId").toString());
                logger.info("queryGoodsInfos===={}", JSON.toJSONString(queryGoodsInfos));
                if(queryGoodsInfos!=null && queryGoodsInfos.get("detail")!=null){
                    GoodsBo goodsBo = (GoodsBo) queryGoodsInfos.get("detail");
                    String goodsType = goodsBo.getGoodsType();//01 虚拟商品  02 实物商品
                    String goodsVrType = goodsBo.getGoodsVrType();//01 优惠卷 02-07	PLUS会员  08充值卡
                    String goodsName = goodsBo.getGoodsName();
                    integralMap.put("goodsName",goodsName);
                    integralMap.put("goodsVrType",StringUtil.nullForString(goodsVrType));
                    if("01".equals(goodsType) && "01".equals(goodsVrType)){
                        integralMap.put("goodsType","coupon");
                    } else if ("01".equals(goodsType) && !"01".equals(goodsVrType)) {
                        integralMap.put("goodsType","vip");
                    } else {
                        integralMap.put("goodsType","entity");
                    }
                }

            }
        }
        //充值卡 不显示在商品开票列表中
        List<Map<String, Object>> filteredList = list.stream()
                .filter(mapBo -> !Objects.equals(mapBo.get("goodsVrType"), "08"))
                .collect(Collectors.toList());
        return filteredList;
    }

    @Override
    public void updateInvoiceFlag(IntegralInvoiceUpdateBo bo){
        integralExchangeDao.updateInvoiceFlag(bo);
    }
}
