<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillingConfDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		<result column="SYSTEM_ID" property="systemId" />
		<result column="ORG_CODE" property="orgCode" />
		<result column="STATION_ID" property="stationId" />
		<result column="STATION_NAME" property="stationName" />
		<result column="PILE_ID" property="pileId" />
		<result column="CHC_NO" property="chcNo" />
		<result column="CHC_NAME" property="chcName" />
		<result column="CHC_REMARK" property="chcRemark" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="CHARGE_METHOD" property="chargeMethod" />
		<result column="PERIOD_SPLIT_NUM" property="periodSplitNum" />
		<result column="ATTACH_ITEM_NOS" property="attachItemNos" />
		<result column="ATTACH_ITEM_PRICES" property="attachItemPrices" />
		<result column="DEPOSIT_ITEM_NOS" property="depositItemNos" />
		<result column="DEPOSIT_ITEM_PRICES" property="depositItemPrices" />
		<result column="CHARGE_NUM" property="chargeNum" />
		<result column="CHARGE_UNIT" property="chargeUnit" />
		<result column="CHARGE_PRICE" property="chargePrice" />
		<result column="OPER_NO" property="operNo" />
		<result column="CREATE_TIME" property="createTime" />
		<result column="CHC_STATUS" property="chcStatus" />
		<result column="STATION_NAME" property="stationName" />
		<result column="BILL_CTL_MODE" property="billCtlMode" />
		<result column="GATHER_DATA_TYPE" property="gatherDataType" />
		<result column="EFT_DATE" property="eftDate" />
		<result column="INV_DATE" property="invDate" />
		<result column="FREE_NUM" property="freeNum" />
		<result column="UP_CHC_NO" property="upChcNo" />
		<result column="CUST_ID" property="custId" />
		<result column="RANGE_FLAG" property="rangeFlag" />
		<result column="ITEM_CHARGE_MODE" property="itemChargeMode" />
	 </resultMap>
	 
	 <sql id="queryChargeBillingConfWhere">
		<if test="chcNo !=null and chcNo !=''">
		    and a.CHC_NO =#{chcNo}
		</if>
		<if test="chcStatus !=null and chcStatus !=''">
		    and a.CHC_STATUS =#{chcStatus}
		</if>
		 <if test="stationId !=null and stationId !=''">
			 and a.STATION_ID =#{stationId}
		 </if>
		 <if test="custId !=null and custId !=''">
			 and a.CUST_ID =#{custId}
		 </if>
		 <if test="billType == '1'.toString() ">
			 and a.STATION_ID is not null
		 </if>
		 <if test="billType == '2'.toString() ">
			 and a.CUST_ID is not  null
		 </if>
		<if test="endEftDate !=null and endEftDate !=''">
		    and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
		</if>
		<if test="stationIds !=null">
		    and a.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
		</if>
		 <if test="orgList!=null">
			 and a.ORG_CODE in
			 <foreach collection="orgList" open="(" close=")" item="item" separator=",">
				 #{item}
			 </foreach>
		 </if>
		 <if test="unChcStatus !=null and unChcStatus !=''">
			 and <![CDATA[ a.CHC_STATUS <> #{unChcStatus} ]]>
		 </if>
	 </sql>

	 <sql id="chargeBillingConfItems">
	 	a.STATION_ID,a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
	 	a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
	 	a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
	 	a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
	 	a.BILLING_TAG,
	 	a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,CUST_ID,RANGE_FLAG,ITEM_CHARGE_MODE
	 </sql>

	 
	<!-- 查询充电计费配置E_CHARGE_BILLING_CONF、E_STATION_BILLING-->
	<select id="queryChargeBillingConfs" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select  
			<include refid="chargeBillingConfItems"></include>
		from E_CHARGE_BILLING_CONF a
		<where>
			<include refid="queryChargeBillingConfWhere"></include>
		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	
	<!-- 查询充电计费配置条数E_CHARGE_BILLING_CONF -->
	<select id="queryChargeBillingConfsNum" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultType="int">
		select count(1)
		from E_CHARGE_BILLING_CONF a
		<!--fenge LEFT JOIN C_PILE_STATION b on a.STATION_ID = b.STATION_ID -->
		<where>
			<include refid="queryChargeBillingConfWhere"></include>
			<!--fenge <include refid="queryPileStationWhere"></include> -->
		</where>
	</select>
	
	<!-- 查询同一个生效时间、同一个充电站有多少条记录 -->
	<select id="queryCbcByEftDateAndStationId" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo" resultType="int">
		select count(1)
		from E_CHARGE_BILLING_CONF a
		<where>
			<if test="stationId != null and stationId != ''">
				and  a.STATION_ID=#{stationId}
			</if>
			<if test="custId != null and custId != ''">
				and  a.CUST_ID=#{custId}
			</if>
			and date_format(a.EFT_DATE,'%Y-%m-%d %H:%i') = date_format(#{eftDate},'%Y-%m-%d %H:%i')
			and a.CHC_STATUS = '1'
			<if test="systemId != null and systemId != ''">
				and  <![CDATA[a.SYSTEM_ID <> #{systemId}]]>
			</if>
		</where>
	</select>
	<!-- 新增充电计费配置E_CHARGE_BILLING_CONF-->
	<insert id="insertChargeBillingConf" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_BILLING_CONF
		<trim prefix="(" suffix=")">
			<if test="orgCode !=null and orgCode !=''">ORG_CODE,</if>
			<if test="stationId !=null and stationId !=''">STATION_ID,</if>
			<if test="custId !=null and custId !=''">CUST_ID,</if>
			<if test="pileId !=null and pileId !=''">PILE_ID,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="chcName !=null and chcName !=''">CHC_NAME,</if>
			<if test="chcRemark !=null and chcRemark !=''">CHC_REMARK,</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE,</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE,</if>
			<if test="chargeMethod !=null and chargeMethod !=''">CHARGE_METHOD,</if>
			<if test="periodSplitNum !=null and periodSplitNum !=''">PERIOD_SPLIT_NUM,</if>
			<if test="attachItemNos !=null and attachItemNos !=''">ATTACH_ITEM_NOS,</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">ATTACH_ITEM_PRICES,</if>
			<if test="depositItemNos !=null and depositItemNos !=''">DEPOSIT_ITEM_NOS,</if>
			<if test="depositItemPrices !=null and depositItemPrices !=''">DEPOSIT_ITEM_PRICES,</if>
			<if test="chargeNum !=null and chargeNum !=''">CHARGE_NUM,</if>
			<if test="chargeUnit !=null and chargeUnit !=''">CHARGE_UNIT,</if>
			<if test="chargePrice !=''">CHARGE_PRICE,</if>
			<if test="operNo !=null and operNo !=''">OPER_NO,</if>
			<if test="createTime !=null and createTime !=''">CREATE_TIME,</if>
			<if test="chcStatus !=null and chcStatus !=''">CHC_STATUS,</if>
			<if test="gatherDataType !=null and gatherDataType !=''">GATHER_DATA_TYPE,</if>
			<if test="eftDate !=null and eftDate !=''">EFT_DATE,</if>
			<if test="invDate !=null and invDate !=''">INV_DATE,</if>
			<if test="freeNum !=null and freeNum !=''">FREE_NUM,</if>
			<if test="upChcNo !=null and upChcNo !=''">UP_CHC_NO,</if>
			<if test="rangeFlag !=null and rangeFlag !=''">RANGE_FLAG,</if>
			<if test="billingTag !=null and billingTag !=''">BILLING_TAG,</if>
			<if test="itemChargeMode !=null and itemChargeMode !=''">ITEM_CHARGE_MODE,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="orgCode !=null and orgCode !=''">#{orgCode},</if>
			<if test="stationId !=null and stationId !=''">#{stationId},</if>
			<if test="custId !=null and custId !=''">#{custId},</if>
			<if test="pileId !=null and pileId !=''">#{pileId},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="chcName !=null and chcName !=''">#{chcName},</if>
			<if test="chcRemark !=null and chcRemark !=''">#{chcRemark},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">#{chargeMode},</if>
			<if test="chargeMethod !=null and chargeMethod !=''">#{chargeMethod},</if>
			<if test="periodSplitNum !=null and periodSplitNum !=''">#{periodSplitNum},</if>
			<if test="attachItemNos !=null and attachItemNos !=''">#{attachItemNos},</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">#{attachItemPrices},</if>
			<if test="depositItemNos !=null and depositItemNos !=''">#{depositItemNos},</if>
			<if test="depositItemPrices !=null and depositItemPrices !=''">#{depositItemPrices},</if>
			<if test="chargeNum !=null and chargeNum !=''">#{chargeNum},</if>
			<if test="chargeUnit !=null and chargeUnit !=''">#{chargeUnit},</if>
			<if test="chargePrice !=''">#{chargePrice},</if>
			<if test="operNo !=null and operNo !=''">#{operNo},</if>
			<if test="createTime !=null and createTime !=''">now(),</if>
			<if test="chcStatus !=null and chcStatus !=''">#{chcStatus},</if>
			<if test="gatherDataType !=null and gatherDataType !=''">#{gatherDataType},</if>
			<if test="eftDate !=null and eftDate !=''">#{eftDate},</if>
			<if test="invDate !=null and invDate !=''">#{invDate},</if>
			<if test="freeNum !=null and freeNum !=''">#{freeNum},</if>
			<if test="upChcNo !=null and upChcNo !=''">#{upChcNo},</if>
			<if test="rangeFlag !=null and rangeFlag !=''">#{rangeFlag},</if>
			<if test="billingTag !=null and billingTag !=''">#{billingTag},</if>
			<if test="itemChargeMode !=null and itemChargeMode !=''">#{itemChargeMode},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电计费配置E_CHARGE_BILLING_CONF -->
	<update id="updateChargeBillingConf" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		update E_CHARGE_BILLING_CONF
		<set>
			<if test="orgCode !=null and orgCode !=''">ORG_CODE =#{orgCode},</if>
			<if test="stationId !=null and stationId !=''">STATION_ID =#{stationId},</if>
			<if test="custId !=null and custId !=''">CUST_ID =#{custId},</if>
			<if test="pileId !=null and pileId !=''">PILE_ID =#{pileId},</if>
			<if test="chcName !=null and chcName !=''">CHC_NAME =#{chcName},</if>
			<if test="chcRemark !=null and chcRemark !=''">CHC_REMARK =#{chcRemark},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE=#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE =#{chargeMode},</if>
			<if test="chargeMethod !=null and chargeMethod !=''">CHARGE_METHOD =#{chargeMethod},</if>
			<if test="periodSplitNum !=null and periodSplitNum !=''">PERIOD_SPLIT_NUM =#{periodSplitNum},</if>
			<if test="attachItemNos !=null">ATTACH_ITEM_NOS =#{attachItemNos},</if>
			<if test="attachItemPrices !=null">ATTACH_ITEM_PRICES =#{attachItemPrices},</if>
			<if test="depositItemNos !=null and depositItemNos !='' ">DEPOSIT_ITEM_NOS =#{depositItemNos},</if>
			<if test="depositItemPrices !=null and depositItemPrices !='' ">DEPOSIT_ITEM_PRICES =#{depositItemPrices},</if>
			<if test="chargeNum !=null and chargeNum !=''">CHARGE_NUM =#{chargeNum},</if>
			<if test="chargeUnit !=null and chargeUnit !=''">CHARGE_UNIT =#{chargeUnit},</if>
			<if test="chargePrice !='' and chargePrice !=null and chargePrice !='null' ">CHARGE_PRICE =#{chargePrice},</if>
			<if test="operNo !=null and operNo !=''">OPER_NO =#{operNo},</if>
			<if test="createTime !=null and createTime !=''">CREATE_TIME =now(),</if>
			<if test="chcStatus !=null and chcStatus !=''">CHC_STATUS =#{chcStatus},</if>
			<if test="gatherDataType !=null and gatherDataType !=''">GATHER_DATA_TYPE =#{gatherDataType},</if>
			<if test="eftDate !=null and eftDate !=''">EFT_DATE =#{eftDate},</if>
			<if test="invDate !=null and invDate !=''">INV_DATE =#{invDate},</if>
			<if test="freeNum !=null and freeNum !=''">FREE_NUM =#{freeNum},</if>
			<if test="upChcNo !=null and upChcNo !=''">UP_CHC_NO =#{upChcNo},</if>
			<if test="rangeFlag !=null and rangeFlag !=''">RANGE_FLAG =#{rangeFlag},</if>
			<if test="billingTag !=null and billingTag !=''">BILLING_TAG =#{billingTag},</if>
			<if test="itemChargeMode !=null">ITEM_CHARGE_MODE = #{itemChargeMode},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			CHC_NO =#{chcNo}
		</where>
	</update>
	
	<!-- 删除充电计费配置E_CHARGE_BILLING_CONF -->
	<delete id="deleteChargeBillingConf" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		delete from E_CHARGE_BILLING_CONF where CHC_NO =#{chcNo}
	</delete>


	<!-- 查询充电计费配置E_CHARGE_BILLING_CONF-->
	<select id="queryChargeBillingMap" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select
		a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a
		<where>
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO =#{chcNo}
			</if>
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>

			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>

			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE like concat(#{orgCode},'%')
			</if>
			<if test="unChcStatus !=null and unChcStatus !=''">
				and <![CDATA[ a.CHC_STATUS <> #{unChcStatus} ]]>
			</if>
		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="queryNewChargeBillingConfs" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select
		b.STATION_ID,a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_station_billing b
		<where>
			a.CHC_NO=b.CHC_NO
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO = #{chcNo}
			</if>

			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>

			<if test="stationId !=null and stationId !=''">
				and b.STATION_ID =#{stationId}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="stationIds !=null">
				and b.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE like concat(#{orgCode},'%')
			</if>

		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="queryPileChargeBillingConfs" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		select
		b.STATION_ID,a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_pile_billing b
		<where>
			a.CHC_NO=b.CHC_NO
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO = #{chcNo}
			</if>

			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>

			<if test="stationId !=null and stationId !=''">
				and b.STATION_ID =#{stationId}
			</if>

			<if test="pileId !=null and pileId !=''">
				and b.PILE_ID =#{pileId}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
			<if test="stationIds !=null">
				and b.STATION_ID in
				<foreach item="item" index="index" collection="stationIds" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="orgCode !=null and orgCode !=''">
				and a.ORG_CODE like concat(#{orgCode},'%')
			</if>

		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="queryCustGroupChargeBilling" parameterType="java.util.Map"  resultMap="BaseResultMap">
		select
		a.PILE_ID,a.CHC_NO,a.CHC_NAME,a.CHC_REMARK,a.CHARGE_MODE,
		a.CHARGE_METHOD,a.PERIOD_SPLIT_NUM,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,
		a.DEPOSIT_ITEM_NOS,a.DEPOSIT_ITEM_PRICES,a.CHARGE_NUM,a.CHARGE_UNIT,
		a.CHARGE_PRICE,a.OPER_NO,a.CREATE_TIME,a.CHC_STATUS,a.SYSTEM_ID,a.BILL_CTL_MODE,
		a.GATHER_DATA_TYPE,a.EFT_DATE,a.INV_DATE,a.ORG_CODE,a.FREE_NUM,a.UP_CHC_NO,b.group_id,a.ITEM_CHARGE_MODE
		from E_CHARGE_BILLING_CONF a ,e_cust_billing b
		<where>
			a.CHC_NO=b.CHC_NO
			<if test="chcNo !=null and chcNo !=''">
				and a.CHC_NO = #{chcNo}
			</if>
			<if test="chcStatus !=null and chcStatus !=''">
				and a.CHC_STATUS =#{chcStatus}
			</if>
			<if test="groupId !=null and groupId !=''">
				and b.group_id =#{groupId}
			</if>
			<if test="custId !=null and custId !=''">
				and b.CUST_ID = #{custId}
			</if>
			<if test="endEftDate !=null and endEftDate !=''">
				and  <![CDATA[date_format(a.EFT_DATE,'%Y-%m-%d %H:%i')  <= date_format(#{endEftDate},'%Y-%m-%d %H:%i')]]>
			</if>
		</where>
		order by a.EFT_DATE desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<insert id="insertBillingStation" parameterType="java.util.List">
		INSERT INTO e_charge_billing_station (
			CUST_ID,
			STATION_ID,
			STATION_NAME,
			CHC_NO,
			DATA_OPER_TIME,
			DATA_OPER_TYPE
		)VALUES
		<foreach collection="list" item="item" index="index" separator=",">
			(
			#{item.custId},
			#{item.stationId},
			#{item.stationName},
			#{item.chcNo},
			now(),
			'I'
			)
		</foreach>
	</insert>

	<delete id="delBillingStation" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		delete from e_charge_billing_station where CHC_NO =#{chcNo} AND CUST_ID = #{custId}
	</delete>

	<select id="queryBillingStation" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition" resultMap="BaseResultMap">
		SELECT
			CHC_NO,
			CUST_ID,
			STATION_ID,
			STATION_NAME
		FROM
			e_charge_billing_station
		WHERE
			CHC_NO = #{chcNo}
		<if test="custId != null and custId != ''">
			AND CUST_ID = #{custId}
		</if>
		<if test="stationId != null and stationId != ''">
			AND STATION_ID = #{stationId}
		</if>
	</select>

	<update id="updateStationBilling" parameterType="java.util.Map">
		UPDATE e_station_billing
		SET
			CHC_NO = #{newChcNo},
			DATA_OPER_TIME = now(),
			DATA_OPER_TYPE = 'U'
		WHERE
			CHC_NO = #{upChcNo};
	</update>

	<update id="updatePileBilling" parameterType="java.util.Map">
		UPDATE e_pile_billing
		SET
			CHC_NO = #{newChcNo},
			DATA_OPER_TIME = now(),
			DATA_OPER_TYPE = 'U'
		WHERE
			CHC_NO = #{upChcNo};
	</update>

	<select id="selectBillRels" parameterType="string" resultType="com.ls.ner.billing.charge.bo.ChargeBillingConfBo">
		SELECT
			conf.chc_no chcNo,
			conf.chc_name chcName,
			'' AS pileId,
			relst.station_id stationId
		FROM
			e_station_billing  relst
				LEFT JOIN e_charge_billing_conf conf ON relst.chc_no = conf.chc_no
		WHERE
			relst.chc_no = #{chcNo} UNION ALL
		SELECT
			conf.chc_no chcNo,
			conf.chc_name chcName,
			'' AS stationId,
			relp.pile_id pileId
		FROM
			e_pile_billing relp
				LEFT JOIN e_charge_billing_conf conf  ON relp.chc_no = conf.chc_no
		WHERE
			relp.chc_no = #{chcNo}
	</select>

	<select id="queryServiceMode" parameterType="String" resultType="com.ls.ner.billing.api.charge.bo.ChargeBillConfigServiceModeBO">
		SELECT
		    a.APP_NO AS appNo,
		    a.CHC_NO AS chcNo,
		    b.ATTACH_ITEM_NOS AS attachItemNos,
		    b.ITEM_CHARGE_MODE AS itemChargeMode
		FROM `e_charge_billing_conf` b
		LEFT JOIN e_charge_billing_rlt_periods a on a.CHC_NO = b.CHC_NO
		where a.APP_NO = #{appNo}
		GROUP BY a.APP_NO
	</select>


	<insert id="addChargeBillingConfHistory" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo">
		insert into e_charge_billing_conf_modify_history(CHC_NO, DATA_OPER_TYPE, OPER_NO)
		values (#{chcNo}, #{dataOperType}, #{operNo})
	</insert>


	<select id="queryChargeBillingConfHistory" parameterType="com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition" resultType="com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo">
		SELECT
		HISTORY_ID historyId,
		CHC_NO chcNo,
		DATA_OPER_TIME dataOperTime,
		DATA_OPER_TYPE dataOperType,
		OPER_NO operNo
		FROM
		e_charge_billing_conf_modify_history
		where CHC_NO = #{chcNo}
			order by DATA_OPER_TIME desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<select id="queryChargeBillingConfHistoryCount" parameterType="com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition" resultType="int">
		SELECT count(1) from e_charge_billing_conf_modify_history where CHC_NO = #{chcNo}
	</select>
</mapper>