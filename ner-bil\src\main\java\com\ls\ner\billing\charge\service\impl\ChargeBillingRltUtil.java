package com.ls.ner.billing.charge.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;

/**
 * 充电计费公用方法
 * <AUTHOR>
 */
@Component
public class ChargeBillingRltUtil{
	private static final Logger LOGGER = LoggerFactory.getLogger(ChargeBillingRltUtil.class);

	/**
	 * 验证不为空
	 * */
	public String isNotEmpty(Object str,String message){
		if(StringUtils.isEmpty(str))
			throw new ChargeBillingRltException(message);
		return String.valueOf(str);
	}

	/**
	 * 返回值，如果=null，返回""
	 * */
	public String returnNotNull(Object str){
		if(StringUtils.isEmpty(str))
			return "";
		return String.valueOf(str);
	}

	/**
	 * 将时间格式yyyy-MM-dd HH:mm:ss转化为HH:mm
	 * */
	public String toTime(String str){
		String[] time = str.split(" ")[1].split(":");
		return time[0]+":"+time[1];
	}

	/**
	 * String类型转为BigDecimal
	 * */
	public BigDecimal strToBigDecimal(String str,String message) {
		try{
			return new BigDecimal(str);
		}catch(Exception e){
			throw new ChargeBillingRltException(message);
		}
	}

	/**
	 *BigDecimal转为 String
	 * */
	public String BigDecimalToString(BigDecimal preStr){
		return preStr.toString();
	}

	/**
	 * 加法计算
	 * */
	public BigDecimal addition(BigDecimal preStr,BigDecimal nextStr){
		return preStr.add(nextStr);
	}

	/**
	 * 减法计算
	 * */
	public BigDecimal subtract(BigDecimal preStr,BigDecimal nextStr){
		return preStr.subtract(nextStr);
	}

	/**
	 * 判断是否 bgnStr >= endStr , 是true、否false
	 * */
	public boolean isGt(String bgnStr,String endStr){
		BigDecimal bgn = strToBigDecimal(bgnStr, "");
		BigDecimal end = strToBigDecimal(endStr, "");
		return bgn.compareTo(end) >= 0;
	}

	/**
	 * 判断是否 bgnStr = endStr , 是true、否false
	 * */
	public boolean isEq(String bgnStr,String endStr){
		if(com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(bgnStr))
			bgnStr = "0";
		if(com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(endStr))
			endStr = "0";
		BigDecimal bgn = strToBigDecimal(bgnStr, "");
		BigDecimal end = strToBigDecimal(endStr, "");
		return bgn.compareTo(end) == 0;
	}

	/**
	 * @description 时间跨度
	 * @param startTime 开始时间
	 * @param EndTime 结束时间
	 * @return 返回间隔分钟数
	 * */
	public String startToEndTime(String startTime,String EndTime){
		SimpleDateFormat formatter = new SimpleDateFormat(EndTime!=null&&EndTime.length()==16?"yyyy-MM-dd HH:mm":"yyyy-MM-dd HH:mm:ss");
		long i = 0;
		try {
			Date currentTime = formatter.parse(startTime);
			Date currentTime2 = formatter.parse(EndTime);
			i = (currentTime2.getTime() - currentTime.getTime())/(1000*60);
		} catch (ParseException e) {
			e.printStackTrace();
			LOGGER.error("请检查充电开始时间、充电结束时间，可能格式不正确或没有输入");
			return "0";
		}
		if(i < 0)
			throw new ChargeBillingRltException("充电时间出现负数，请排查是否充电时间或者开始时间与结束时间输入有误");
		return String.valueOf(i);
	}

	/**
	 * 时间格式00:00的比较   ： maxBgnStr <= minBgnStr <= minEndStr < = maxEndStr
	 * */
	public boolean compareTime(String minBgnStr,String maxBgnStr,String maxEndStr){
		return (minBgnStr.compareTo(maxBgnStr)>=0 && maxEndStr.compareTo(minBgnStr)>0);
	}

	/**
	 * @description 分时的时候，将传入的List<Map>记录转为List<ChargeBillingRltPeriodsBo>，
	 * */
	public List<ChargeBillingRltPeriodsBo> rltPeriods(List<Map<String,Object>> rltPeriodList){
		if(rltPeriodList == null || rltPeriodList.size() == 0){
			throw new ChargeBillingRltException("分时的时候，没有分时记录");
		}
		List<ChargeBillingRltPeriodsBo> rltPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
		int i =1;
		for(Map<String,Object> rltMap : rltPeriodList){
			ChargeBillingRltPeriodsBo period =  new ChargeBillingRltPeriodsBo();
			if(rltMap.get("beginTime") == null && rltMap.get("periodFlag") == null)
				throw new ChargeBillingRltException("起始时间与时段标记不能同时为空");
			period.setSn(StringUtils.isEmpty(rltMap.get("sn"))?String.valueOf(i):String.valueOf(rltMap.get("sn")));
			period.setBeginTime(StringUtils.isEmpty(rltMap.get("beginTime"))?null:String.valueOf(rltMap.get("beginTime")));
			period.setEndTime(StringUtils.isEmpty(rltMap.get("endTime"))?null:String.valueOf(rltMap.get("endTime")));
			period.setTimeFlag(StringUtils.isEmpty(rltMap.get("periodFlag"))?null:String.valueOf(rltMap.get("periodFlag")));
			period.setPq(StringUtils.isEmpty(rltMap.get("pq"))?null:String.valueOf(rltMap.get("pq")));
			period.setPrice(StringUtils.isEmpty(rltMap.get("periodRate"))?null:String.valueOf(rltMap.get("periodRate")));
			period.setAmt(StringUtils.isEmpty(rltMap.get("amt"))?null:String.valueOf(rltMap.get("amt")));
			rltPeriods.add(period);
			i++;
		}
		return rltPeriods;
	}

	/**
	 * @description 根据传入的分时信息，回填订单编号appNo,充电计费编号chcNo
	 * */
	public void chcNoForRltPeriods(List<ChargeBillingRltPeriodsBo> periods,String appNo,String chcNo){
		if(periods != null)
			for(ChargeBillingRltPeriodsBo p:periods){
				p.setAppNo(appNo);
				p.setChcNo(chcNo);
			}
	}


	/**
	 * @description 根据传入的分时信息，回填时段单价
	 * @param newRltPeriods 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	public List<ChargeBillingRltPeriodsBo> priceForRltPeriods(List<ChargeBillingRltPeriodsBo> newRltPeriods,List<ChargeBillingRltPeriodsBo> oldRltPeriods){
		for(ChargeBillingRltPeriodsBo newPeriod:newRltPeriods){
			newPeriod.setRelBeginTime(newPeriod.getBeginTime());
			newPeriod.setRelEndTime(newPeriod.getEndTime());
			priceForRltPeriod(newPeriod, oldRltPeriods);
		}
		return newRltPeriods;
	}

	/**
	 * @description 根据传入的分时信息，回填时段单价(分时服务费使用)
	 * @param newRltPeriods 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	public List<ChargeBillingRltPeriodsBo> priceForRltPeriods(List<ChargeBillingRltPeriodsBo> newRltPeriods,List<ChargeBillingRltPeriodsBo> oldRltPeriods,String itemNo){
		for(ChargeBillingRltPeriodsBo newPeriod:newRltPeriods){
			newPeriod.setRelBeginTime(newPeriod.getBeginTime());
			newPeriod.setRelEndTime(newPeriod.getEndTime());
			priceForRltPeriod(newPeriod, oldRltPeriods,itemNo);
		}
		return newRltPeriods;
	}

	/**
	 * @description 根据传入的分时信息，回填时段单价
	 * @param newRltPeriod 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	private void priceForRltPeriod(ChargeBillingRltPeriodsBo newRltPeriod,List<ChargeBillingRltPeriodsBo> oldRltPeriods,String itemNo){
		String TimeType = "02";//sn匹配
		if (newRltPeriod != null && (StringUtil.isNotBlank(newRltPeriod.getBeginTime()) || StringUtil.isNotBlank(newRltPeriod.getTimeFlag()))) {
			TimeType = "01";//开始时间和时段标识匹配
		}

		if ("01".equals(TimeType)) {

			//格式化时间 入参一般都为 年月日 时分秒格式  计费时段为时分秒
			try {
				if (StringUtil.isNotBlank(newRltPeriod.getBeginTime())) {
					newRltPeriod.setBeginTime(DateTools.formatDateStr(newRltPeriod.getBeginTime(), "HH:mm"));
				}
			} catch (Exception e) {
				LOGGER.debug("分时计费设置开始时间异常：", e);
			}

			int i = 1;
			for (ChargeBillingRltPeriodsBo oldRltPeriod : oldRltPeriods) {
				if ((newRltPeriod.getBeginTime() != null
						&& StringUtil.isNotBlank(oldRltPeriod.getBeginTime()) && StringUtil.isNotBlank(oldRltPeriod.getEndTime())
						&& compareTime(newRltPeriod.getBeginTime(), oldRltPeriod.getBeginTime(), oldRltPeriod.getEndTime()))
						|| oldRltPeriod.getTimeFlag().equals(newRltPeriod.getTimeFlag())) {
					newRltPeriod.setPrice(oldRltPeriod.getPrice());
					newRltPeriod.setItemNo(oldRltPeriod.getItemNo());
					if(StringUtil.isBlank(newRltPeriod.getEndTime())){
						newRltPeriod.setEndTime(oldRltPeriod.getEndTime());
					}
					if (!StringUtil.isNotBlank(newRltPeriod.getTimeFlag())) {
						newRltPeriod.setTimeFlag(oldRltPeriod.getTimeFlag());
					}
					return;
				} else {
					newRltPeriod.setPrice("0");
					if (i >= oldRltPeriods.size()) {
						return;
					}
					i++;
				}
			}
			throw new RuntimeException("接口传入的分时记录有错误，无法与本地分时时段对应");
		}

		if ("02".equals(TimeType)) {
			int i = 1;
			for (ChargeBillingRltPeriodsBo oldRltPeriod : oldRltPeriods) {
				if (StringUtil.isNotBlank(newRltPeriod.getSn()) && StringUtil.isNotBlank(oldRltPeriod.getSn())
						&& newRltPeriod.getSn().equals(oldRltPeriod.getSn())) {
					//兼容dmm 分时格式只有sn的情况
					newRltPeriod.setPrice(oldRltPeriod.getPrice());
					newRltPeriod.setItemNo(oldRltPeriod.getItemNo());
					if (!StringUtil.isNotBlank(newRltPeriod.getTimeFlag())) {
						newRltPeriod.setTimeFlag(oldRltPeriod.getTimeFlag());
					}
					return;
				}else {
					newRltPeriod.setPrice("0");
					if (i >= oldRltPeriods.size()) {
						return;
					}
					i++;
				}
			}
			throw new RuntimeException("接口传入的分时记录有错误，无法与本地分时时段对应");
		}

	}

	/**
	 * @description 根据传入的分时信息，回填时段单价
	 * @param newRltPeriod 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	private void priceForRltPeriod(ChargeBillingRltPeriodsBo newRltPeriod,List<ChargeBillingRltPeriodsBo> oldRltPeriods){
		int i =1;
		for(ChargeBillingRltPeriodsBo oldRltPeriod:oldRltPeriods){
			//格式化时间 入参一般都为 年月日 时分秒格式  计费时段为时分秒
			try {
				if(StringUtil.isNotBlank(newRltPeriod.getBeginTime())){
					newRltPeriod.setBeginTime(DateTools.formatDateStr(newRltPeriod.getBeginTime(), "HH:mm"));
				}
			}catch (Exception e){

			}
			if((newRltPeriod.getBeginTime()!= null && compareTime(newRltPeriod.getBeginTime(), oldRltPeriod.getBeginTime(), oldRltPeriod.getEndTime()))
					|| oldRltPeriod.getTimeFlag().equals(newRltPeriod.getTimeFlag())){
				newRltPeriod.setPrice(oldRltPeriod.getPrice());
				if(!StringUtil.isNotBlank(newRltPeriod.getTimeFlag())){
					newRltPeriod.setTimeFlag(oldRltPeriod.getTimeFlag());
				}
				return;
			}else {
				newRltPeriod.setPrice("0");
				if(i>=oldRltPeriods.size()){
					return;
				}
				i++;
			}
		}
		throw new ChargeBillingRltException("接口传入的分时记录有错误，无法与本地分时时段对应");
	}


	/**
	 * @description boss计费，传递示数，分时计费
	 * */
	public void refreshPeriods(ChargeBillingRltBo cbrBo){
		List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();//平台配置分时记录集
		List<ChargeBillingRltPeriodsBo> newPeriods = cbrBo.getNewPeriods();//实时计费
		if(rltPeriods == null || rltPeriods.size() == 0){
			throw new ChargeBillingRltException("分时的时候，没有分时记录");
		}
		String endTime =toTime(cbrBo.getEndTime());
		//1、如果是第一次实时计费，根据结束时间判断落在哪个时段区间内
		if(newPeriods == null || newPeriods.size() == 0){
			newPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
			for(ChargeBillingRltPeriodsBo period:rltPeriods){
				if(endTime.compareTo(period.getBeginTime())>=0 && endTime.compareTo(period.getEndTime())<=0){
					period.setSn("1");
					period.setPq(cbrBo.gettPq());
					period.setRelBeginTime(DateTools.getStringDateShort(DateTools.YMDHMS));
					period.setRelBeginTime(cbrBo.getEndTime());
					newPeriods.add(period);
					break;
				}
			}
		}else{
			//2、如果不是第一次实时计费，先根据结束时间判断是否落在已存的时段区间内
			BigDecimal pq = obtainPqAmt(newPeriods);//已录入的分时总电量
			BigDecimal newPq = new BigDecimal(cbrBo.gettPq());//新传入的电量
			BigDecimal subPd = subtract(newPq, pq);//新增长的电量
			ChargeBillingRltPeriodsBo period = newPeriods.get(newPeriods.size()-1);
			//如果落在已存在的时段区间内，则累加原来该区间的电量
			if(endTime.compareTo(period.getBeginTime())>=0 && endTime.compareTo(period.getEndTime())<=0){
				period.setPq(BigDecimalToString(addition(strToBigDecimal(period.getPq(), ""), subPd)));
				period.setRelEndTime(cbrBo.getEndTime());
			}else{
				//如果没有落在已存在的时段区间内，则新增区间记录
				for(ChargeBillingRltPeriodsBo cbrPeriodsBo:rltPeriods){
					if(endTime.compareTo(cbrPeriodsBo.getBeginTime())>=0 && endTime.compareTo(cbrPeriodsBo.getEndTime())<=0){
						cbrPeriodsBo.setSn(String.valueOf(newPeriods.size()+1));
						cbrPeriodsBo.setPq(BigDecimalToString(subPd));
						cbrPeriodsBo.setRelBeginTime(DateTools.getStringDateShort(DateTools.YMD)+" "+cbrPeriodsBo.getBeginTime()+":00");
						cbrPeriodsBo.setRelEndTime(cbrBo.getEndTime());
						newPeriods.add(cbrPeriodsBo);
						break;
					}
				}
			}
		}
		cbrBo.setRltPeriods(newPeriods);

	}

	/**
	 * @description 根据分时记录信息List<ChargeBillingRltPeriodsBo>，获取总电量
	 * */
	private BigDecimal obtainPqAmt(List<ChargeBillingRltPeriodsBo> newPeriods){
		BigDecimal returnDecimal = new BigDecimal("0.00");
		if(newPeriods != null && newPeriods.size() > 0)
			for(ChargeBillingRltPeriodsBo periods:newPeriods){
				BigDecimal pq = new BigDecimal(periods.getPq());
				returnDecimal = addition(returnDecimal, pq);
			}
		return returnDecimal;
	}


	public void modifyReturnChargeSerItems(List<ChargeSerItemBo> csiBo,ChargeSerItemQueryCondition bo)throws Exception{
		String[] itemNo = bo.getItemNo().split(",");
		String[] price = bo.getPrice().split(",");
		if(itemNo.length != price.length)
			throw new Exception("项目编号与价格数量匹配不上，项目编号："+bo.getItemNo()+",价格："+bo.getPrice());
		if(csiBo==null || csiBo.size()!=itemNo.length)
			throw new Exception("查询出来的服务条数与项目编号数量匹配不上");

		Map<String,String> m = new HashMap<String,String>();
		for(int i=0;i<itemNo.length;i++){
			m.put(itemNo[i], price[i]);
		}
		for(ChargeSerItemBo csi:csiBo){
			csi.setServicePrice(m.get(csi.getItemNo()));
		}
	}

	/**
	 * @description boss计费，传递示数，分时计费
	 * */
	public void refreshPeriods(ChargeBillingRltBo cbrBo,String itemNo){
		List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();//平台配置分时记录集
		if(ChargeConstant.itemTypeNo.SERVICE.equals(itemNo)){
			rltPeriods = cbrBo.getSerPeriods();
		}else{
			rltPeriods = cbrBo.getRltPeriods();
		}
		List<ChargeBillingRltPeriodsBo> newPeriods = cbrBo.getNewPeriods();//实时计费
		if(rltPeriods != null && rltPeriods.size() > 0) {
//			throw new ChargeBillingRltException("分时的时候，没有分时记录");
			String endTime = toTime(cbrBo.getEndTime());
			//1、如果是第一次实时计费，根据结束时间判断落在哪个时段区间内
			if (newPeriods == null || newPeriods.size() == 0) {
				newPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
				for (ChargeBillingRltPeriodsBo period : rltPeriods) {
					if(itemNo.equals(period.getItemNo()) && endTime.compareTo(period.getBeginTime())>=0 && endTime.compareTo(period.getEndTime())<=0){
						period.setSn("1");
						period.setPq(cbrBo.gettPq());
						period.setRelBeginTime(DateTools.getStringDateShort(DateTools.YMDHMS));
						period.setRelBeginTime(cbrBo.getEndTime());
						newPeriods.add(period);
						break;
					}
				}
			} else {
				//2、如果不是第一次实时计费，先根据结束时间判断是否落在已存的时段区间内
				BigDecimal pq = obtainPqAmt(newPeriods);//已录入的分时总电量
				BigDecimal newPq = new BigDecimal(cbrBo.gettPq());//新传入的电量
				BigDecimal subPd = subtract(newPq, pq);//新增长的电量
				ChargeBillingRltPeriodsBo period = newPeriods.get(newPeriods.size() - 1);
				//如果落在已存在的时段区间内，则累加原来该区间的电量
				if (endTime.compareTo(period.getBeginTime()) >= 0 && endTime.compareTo(period.getEndTime()) <= 0) {
					period.setPq(BigDecimalToString(addition(strToBigDecimal(period.getPq(), ""), subPd)));
					period.setRelEndTime(cbrBo.getEndTime());
				} else {
					//如果没有落在已存在的时段区间内，则新增区间记录
					for (ChargeBillingRltPeriodsBo cbrPeriodsBo : rltPeriods) {
						if(itemNo.equals(cbrPeriodsBo.getItemNo()) && endTime.compareTo(cbrPeriodsBo.getBeginTime())>=0 && endTime.compareTo(cbrPeriodsBo.getEndTime())<=0){
							cbrPeriodsBo.setSn(String.valueOf(newPeriods.size() + 1));
							cbrPeriodsBo.setPq(BigDecimalToString(subPd));
							cbrPeriodsBo.setRelBeginTime(DateTools.getStringDateShort(DateTools.YMD) + " " + cbrPeriodsBo.getBeginTime() + ":00");
							cbrPeriodsBo.setRelEndTime(cbrBo.getEndTime());
							newPeriods.add(cbrPeriodsBo);
							break;
						}
					}
				}
			}
			if(ChargeConstant.itemTypeNo.SERVICE.equals(itemNo)){
				cbrBo.setSerRltPeriods(newPeriods);
			}else{
				cbrBo.setRltPeriods(newPeriods);
			}
		}
	}
}
