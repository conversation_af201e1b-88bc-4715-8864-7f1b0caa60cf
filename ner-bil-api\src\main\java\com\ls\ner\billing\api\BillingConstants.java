/**
 *
 * @(#) BillConstants.java
 * @Package com.ls.ner.billing.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api;

import java.util.Arrays;
import java.util.List;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: BillingConstants.java,v 1.6 2016/03/17 06:33:38 32495 Exp $ 
 *
 *  History:  2016年2月18日 上午9:57:54   lipf   Created.
 *           
 */
public class BillingConstants {


	/**
	 * 是 否
	 */
	public static final String JUDGE_FLAG = "ynJudgeFlag";

	/**
	 * 业务大类 租车 充电
	 * <AUTHOR>
	 *
	 */
	public static final class PeBe{
		public static final String CODE_TYPE = "peBe";
		/**
		 * 租车
		 */
		public static final String RENT = "01";
		/**
		 * 充电
		 */
		public static final String CHARGE = "02";
	}	
	
	/**
	 * 业务小类 时租 日租    改用rentType
	 * <AUTHOR>
	 *
	 */
	public static final class SubBe{
		
		
		
		public static final String CODE_TYPE = "rentType";
		/**
		 * 时租
		 */
		public static final String HOUR = "01";
		/**
		 * 日租
		 */
		public static final String DAY = "02";
		/**
		 * 物流
		 */
		public static final String DILIVER = "11";

		/**
		 * 时租-包电
		 */
		public static final String HOUR_BD = "0101";

		/**
		 * 日租-包电
		 */
		public static final String DAY_BD = "0201";

		/**
		 * 日租-不包电
		 */
		public static final String DAY_BD_NO = "0202";
		
		/**
		 * 日租-不包电-充多少算多少
		 */
		public static final String DAY_BD_REAL = "0203";

		/**
		 * 月租-包电
		 */
		public static final String MONTH_BD = "0301";

		/**
		 * 月租-不包电
		 */
		public static final String MONTH_BD_NO = "0302";
		
		/**
		 * 月租-不包电-充多少算多少
		 */
		public static final String MONTH_BD_REAL = "0303";

		/**
		 * 年租-包电
		 */
		public static final String YEAR_BD = "0401";

		/**
		 * 年租-不包电
		 */
		public static final String YEAR_BD_NO = "0402";
		
		/**
		 * 年租-不包电-充多少算多少
		 */
		public static final String YEAR_BD_REAL = "0403";

		public static final List<String> SUB_BE_HOURS = Arrays.asList(HOUR,HOUR_BD);

		public static final List<String> SUB_BE_DAYS = Arrays.asList(DAY,DAY_BD,DAY_BD_NO,DAY_BD_REAL);

		public static final List<String> SUB_BE_MONTHS = Arrays.asList(MONTH_BD,MONTH_BD_NO,MONTH_BD_REAL);

		public static final List<String> SUB_BE_YEARS = Arrays.asList(YEAR_BD,YEAR_BD_NO,YEAR_BD_REAL);
		
		public static final String[] VALID_SUB_BES = {SubBe.HOUR,SubBe.DILIVER,SubBe.DAY,SubBe.DAY_BD,SubBe.HOUR_BD,SubBe.MONTH_BD,SubBe.MONTH_BD_NO,SubBe.YEAR_BD,SubBe.YEAR_BD_NO};
	}
	
	/**
	 * 收费时间点
	 * <AUTHOR>
	 *
	 */
	public static final class ChargeTimePoint{
		
		public static final String CODE_TYPE = "billingTimePoint";
		/**
		 * 过滤专用-不过滤,全部参与计算
		 */
		public static final String ALL = "00";
		/**
		 * 过滤和配置用 预付费
		 */
		public static final String PRE = "01";
		/**
		 * 过滤和配置用 后付费
		 */
		public static final String POST = "02";
	}
	
	
	/**
	 * 计费方式 时间/里程/时间里程混合
	 * <AUTHOR>
	 *
	 */
	public static final class ChargeWay{
		
		public static final String CODE_TYPE = "billingChargeWay";
		
		/**
		 * 按时间收费
		 */
		public static final String BY_TIME = "0101";
		
		/**
		 * 按里程收费
		 */
		public static final String BY_MILL = "0102";
		
		/**
		 * 按 时间+里程 收费
		 */
		public static final String BY_TIME_AND_MILL = "0103";
	}
	
	/**
	 * 计费模式
	 * <AUTHOR>
	 *
	 */
	public static final class ChargeMode{
		
		public static final String CODE_TYPE = "billingChargeMode";
		/**
		 * 标准
		 */
		public static final String STANDARD = "01";
		
		/**
		 * 分时(峰谷) 不同的时间段有不同的加个
		 */
		public static final String PERIOD = "02";
		
		/**
		 * 阶梯
		 */
		public static final String STEP = "03";
	}
	
	
	/**
	 * 项目类型
	 * <AUTHOR>
	 *
	 */
	public static final class ItemType{
		public static final String CODE_TYPE = "billingAppendItemType";
		/**
		 * 附加收费项目
		 */
		public static final String ATTACH = "01";
		/**
		 * 押金收费项目
		 */
		public static final String DEPOSIT = "02";
	}
	/**
	 * 请求结算类型，01预收 02结算
	 * <AUTHOR>
	 *
	 */
	public static final class BillType{
		public static final String CODE_TYPE = "billType";
		/**
		 * 01预收
		 */
		public static final String PREPAY = "01";
		/**
		 * 02结算
		 */
		public static final String SETTLE = "02";
	}
	/**
	 * '状态，1有效、0无效、2草稿'
	 * <AUTHOR>
	 *
	 */
	public static final class ValidFlag{
		public static final String CODE_TYPE = "validFlag";
		/**
		 * 1有效
		 */
		public static final String VALID = "1";
		/**
		 * 0无效
		 */
		public static final String INVALID = "0";
		/**
		 * 2草稿
		 */
		public static final String DRAFT = "2";
	}
	/**
	 * 是否必购，01选购、02必购
	 * <AUTHOR>
	 *
	 */
	public static final class BuyType{
		public static final String CODE_TYPE = "billingAppendItemBuyType";
		/**
		 * 01选购
		 */
		public static final String OPTIONAL = "01";
		/**
		 * 02必购
		 */
		public static final String REQUIRED = "02";
	}
	/**
	 * 状态，1启用、0停用
	 * <AUTHOR>
	 *
	 */
	public static final class ItemStatus{
		public static final String CODE_TYPE = "enableFlag";
		/**
		 * 0停用
		 */
		public static final String DISABLE = "0";
		/**
		 * 1启用
		 */
		public static final String ENABLE = "1";
	}
	/**
	 * 定价适用日期默认值
	 */
	public static final String DEFAULT_BILLING_CONFIG_APPLY_DATE = "99999999";
	/**
	 * 租金默认费用项编码
	 */
	public static final String DEFAULT_TIME_CHARGE_ITEM_NO = "5201314";
	/**
	 * 里程费默认费用项编码
	 */
	public static final String DEFAULT_MILL_CHARGE_ITEM_NO = "7777777";

}
