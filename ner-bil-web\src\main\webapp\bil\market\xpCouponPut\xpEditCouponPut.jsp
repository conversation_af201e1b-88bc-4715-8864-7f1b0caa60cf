<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="小鹏编辑优惠券发放">
    <script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
    <style type="text/css">
        .hiddenFlow {
            overflow-x: hidden;
            overflow-y: hidden;
        }
    </style>
</ls:head>
<ls:body>
    <ls:form name="xpCouponEditForm" id="xpCouponEditForm" cssClass="hiddenFlow">
        <ls:text name="eftDate" visible="false" property="eftDate"></ls:text>
        <ls:text name="invDate" visible="false" property="invDate"></ls:text>
        <ls:text name="limGetNum" visible="false" property="limGetNum"></ls:text>
        <ls:text name="cpnTimeType" visible="false" property="cpnTimeType"></ls:text>
        <ls:text name="timeDuration" visible="false" property="timeDuration"></ls:text>
        <ls:text name="timeUnit" visible="false" property="timeUnit"></ls:text>

        <ls:text name="custInfoList" visible="false"></ls:text>

        <ls:title text="优惠券信息"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="优惠券名称"/></td>
                <td>
                    <ls:text name="cpnName" enabled="false" property="cpnName"></ls:text>
                    <ls:text name="cpnId" visible="false" property="cpnId"></ls:text>
                </td>

                <td><ls:label text="优惠内容"/></td>
                <td>
                    <ls:text name="busiTypeName" property="busiTypeName" enabled="false"></ls:text>
                    <ls:text name="busiType" property="busiType" visible="false"></ls:text>
                </td>

                <td><ls:label text="优惠券类型"/></td>
                <td>
                    <ls:text name="cpnTypeName" property="cpnTypeName" enabled="false"></ls:text>
                    <ls:text name="cpnType" property="cpnType" visible="false"></ls:text>
                </td>
            </tr>
            <tr>
                <td><ls:label text="面额/折扣"/></td>
                <td><ls:text name="cpnAmt" property="cpnAmt" enabled="false"></ls:text></td>

                <td><ls:label text="有效时间"/></td>
                <td>
                    <ls:text name="effectTime" enabled="false" property="effectTime"/>
                </td>

                <td><ls:label text="优惠券状态"/></td>
                <td>
                    <ls:text name="cpnStatusName" enabled="false" property="cpnStatusName"></ls:text>
                    <ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
                </td>
            </tr>

            <tr>
                <td><ls:label text="发行数量"/></td>
                <td><ls:text name="cpnNum" enabled="false" property="cpnNum"></ls:text>
                </td>

                <td><ls:label text="已领数量"/></td>
                <td>
                    <ls:text name="alrdyGetNum" enabled="false" property="alrdyGetNum"></ls:text>
                </td>

                <td><ls:label text="已发放数量"/></td>
                <td>
                    <ls:text name="putNum" enabled="false" property="putNum"></ls:text>
                </td>

                <td>&nbsp;</td>
                <td>&nbsp;</td>
            </tr>
        </table>
        <ls:title text="发放客户"></ls:title>
        <table align="center" class="tab_search">
            <ls:grid url="" name="custGrid" caption="" width="100%" singleSelect="false" height="180px"
                     primaryKey="uid" showRowNumber="false" showCheckBox="true">
                <ls:gridToolbar name="operation">
                    <ls:gridToolbarItem name="choice" text="选择客户" imageKey="search" onclick="choiceCust"></ls:gridToolbarItem>
                    <ls:gridToolbarItem name="del" text="删除" imageKey="delete" onclick="del"></ls:gridToolbarItem>
                    <ls:gridToolbarItem name="put" text="发放" imageKey="put" onclick="put"></ls:gridToolbarItem>
                </ls:gridToolbar>
                <ls:column caption="客户id" name="uid" readOnly="true"/>
                <ls:column caption="手机号码" name="mobile" readOnly="true"/>
                <ls:column caption="客户名称" name="nickName" readOnly="true"/>
                <ls:column caption="客户分类" name="custTypeName" readOnly="true" align="center"/>
                <ls:column caption="客户分类" name="custType" hidden="true"/>
            </ls:grid>
        </table>
    </ls:form>

    <ls:script>
        function del() {
            var items = custGrid.getCheckedItems();
            if (LS.isEmpty(items)) {
                LS.message("info", "请选择记录");
                return;
            }
            for (var i = 0; i < items.length; i++) {
                custGrid.removeItem(items[i]);
            }
        }

        function choiceCust() {
            LS.dialog("~/billing/couponPut/xp/custSelect", "选择客户", 800, 400, true);
        }

        window.selectCust = selectCust;
        function selectCust(items){
            var tmp = new Array();
            var gridItems = custGrid.getItems();
            if(gridItems.length > 0){
                var isRepeated;
                for(var j = 0;j < items.length;j++){
                    isRepeated = false;
                    for(var i = 0;i < gridItems.length;i++){
                        if(items[j].areaCode == gridItems[i].areaCode){
                            isRepeated = true;
                            break;
                        }
                    }
                    if (!isRepeated) {
                        tmp.push(items[j]);
                    }
                }
                custGrid.appendItem(tmp)
            }else{
                custGrid.appendItem(items)
            }
        }

        function put() {
            var allItem = custGrid.getItems();
            var len = allItem.length;
            if (len <= 0) {
                LS.message("info", "请选择客户！");
                return;
            }
            if (LS.isEmpty(cpnId.getValue())) {
                LS.message("info", "请选择优惠券！");
                return;
            }
            var _alrdyGetNum = parseInt(alrdyGetNum.getValue());
            var _cpnNum = parseInt(cpnNum.getValue());
            var _putNum = parseInt(putNum.getValue());
            if (_alrdyGetNum + len + _putNum > _cpnNum) {
                LS.message("info", "发放数量+已领数量不能大于发行数量！");
                return;
            }
            LS.confirm('确认是否发放？', function (result) {
                if (result) {
                    var allItem = custGrid.getItems();
                    custInfoList.setValue(JSON.stringify(allItem));
                    xpCouponEditForm.submit("~/billing/couponPut/xp/savePutInfo", function (data) {
                        var _msg = data.items[0];
                        if (_msg == "success") {
                            LS.message("info", "发放成功");
                            LS.parent().query();
                            LS.window.close();
                        } else {
                            LS.message("error", _msg);
                        }
                    });
                }
            });
        }
    </ls:script>

</ls:body>
</html>