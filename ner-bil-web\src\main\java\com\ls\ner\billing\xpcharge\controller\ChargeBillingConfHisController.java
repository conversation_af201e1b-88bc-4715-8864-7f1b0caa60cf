package com.ls.ner.billing.xpcharge.controller;

import com.alibaba.fastjson.JSON;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.xpcharge.controller
 * @ClassName: ChargeBillingConfHisController
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/3/31 22:22
 * @Version: 1.0
 */

@Controller
@RequestMapping("/billing/xpcbc/his")
public class ChargeBillingConfHisController extends QueryController<ChargeBillingConfHisQueryCondition> {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBillingConfHisController.class);

    @ServiceAutowired("chargeBillingConfService")
    private IChargeBillingConfService chargeBillingConfService;

    @RequestMapping(value = "/queryChargeBillingConfsHisList")
    public @ItemResponseBody
    QueryResultObject queryChargeBillingConfsHisList(@QueryRequestParam("params") RequestCondition params) {
        ChargeBillingConfHisQueryCondition condition = this.rCondition2QCondition(params);
        List<ChargeBillingConfHistoryBo> list = new ArrayList<>();
        int recordCount = chargeBillingConfService.queryChargeBillingConfHistoryCount(condition);
        if(recordCount > 0){
            list =chargeBillingConfService.queryChargeBillingConfHistory(condition);
        }
        return RestUtils.wrappQueryResult(list, recordCount);
    }

    @Override
    protected ChargeBillingConfHisQueryCondition initCondition() {
        return new ChargeBillingConfHisQueryCondition();
    }
}
