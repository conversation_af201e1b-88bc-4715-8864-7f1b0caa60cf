package com.ls.ner.billing.api.charge.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DESCRIPTION 查询订单分段计费结算信息
 * @create 2017-08-18 11:07
 **/
public class BillRltPeriodsBo implements Serializable {
    private static final long serialVersionUID = 1L;
    private Date dataOperTime;//数据操作时间
    private String dataOperType;//数据操作类型
    private String appNo;//订单编号
    private String chcNo;//充电计费编号
    private String sn;//排序号
    private String beginTime;//开始时段
    private String endTime;//结束时段
    private String price;//计费单价
    private String pq;//时段电量
    private String amt;//时段金额
    private String timeFlag;//时段标识：1：尖；2：峰；3：平；4：谷
    private String times;//分时时间段

    private String itemNo;
    private String serviceMode;  //服务费计费模式 0201：标准 0202分时
    private String serPrice;  //服务费
    private String serUnitPrice;  //服务费

    public String getSerUnitPrice() {
        return serUnitPrice;
    }

    public void setSerUnitPrice(String serUnitPrice) {
        this.serUnitPrice = serUnitPrice;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getServiceMode() {
        return serviceMode;
    }

    public void setServiceMode(String serviceMode) {
        this.serviceMode = serviceMode;
    }

    public String getSerPrice() {
        return serPrice;
    }

    public void setSerPrice(String serPrice) {
        this.serPrice = serPrice;
    }

    public Date getDataOperTime() {
        return dataOperTime;
    }

    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    public String getDataOperType() {
        return dataOperType;
    }

    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }

    public String getChcNo() {
        return chcNo;
    }

    public void setChcNo(String chcNo) {
        this.chcNo = chcNo;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }


    public String getTimeFlag() {
        return timeFlag;
    }

    public void setTimeFlag(String timeFlag) {
        this.timeFlag = timeFlag;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getPq() {
        return pq;
    }

    public void setPq(String pq) {
        this.pq = pq;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }
}