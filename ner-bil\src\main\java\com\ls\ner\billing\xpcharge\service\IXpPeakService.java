package com.ls.ner.billing.xpcharge.service;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.xpcharge.bo.PeakRelBo;
import com.ls.ner.billing.xpcharge.bo.PeakTimeBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description 尖峰电价
 * <AUTHOR>
 * @Date 2022/6/20 14:31
 */
public interface IXpPeakService {

    /**
     * @Description 是否开启尖峰计费
     * <AUTHOR>
     * @Date 2022/6/27 17:17
     */
    Boolean peakBillingSwitch();

    /**
     * @Description 保存尖峰电价时间设置
     * <AUTHOR>
     * @Date 2022/6/20 14:53
     * @param peakTimeBo
     */
    void savePeakTime(PeakTimeBo peakTimeBo);

    /**
     * @Description 查询有效计费
     * <AUTHOR>
     * @Date 2022/6/22 14:44
     */
    PeakTimeBo queryPeakTimeEffective();

    /**
     * @Description 批量保存尖峰电价关联
     * <AUTHOR>
     * @Date 2022/6/20 14:54
     * @param chcNos
     */
    String addPeakRels(String chcNos,Map<String,Object> map);

    /**
     * @Description 查询尖峰计费关联列表
     * <AUTHOR>
     * @Date 2022/6/22 14:46
     * @param condition
     */
    List<PeakRelBo> pagePeakRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 数量计算
     * <AUTHOR>
     * @Date 2022/6/22 14:47
     * @param condition
     */
    int countPagePeakRel(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 查询尖峰计费未关联列表
     * <AUTHOR>
     * @Date 2022/6/22 14:46
     * @param condition
     */
    List<PeakRelBo> pageUnPeakRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 未关联数量计算
     * <AUTHOR>
     * @Date 2022/6/22 14:47
     * @param condition
     */
    int countPageUnPeakRel(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 删除尖峰电价关联
     * <AUTHOR>
     * @Date 2022/6/20 14:57
     * @param chcNo
     */
    void delPeakRel(String chcNo,Map<String,Object> requestMap);

    /**
     * @Description 批量新增尖峰电价关联日志
     * <AUTHOR>
     * @Date 2022/6/20 14:56
     * @param peakTimeBo
     */
    void addPeakRelLogs(PeakTimeBo peakTimeBo);


    /**
     * @Description 获取分时计费明细-判断尖峰计费，有关联则返回尖峰计费的，没有关联的则返回正常的
     * <AUTHOR>
     * @Date 2022/6/21 14:51
     * @param condition
     */
    List<ChargePeriodsBo> queryPeriodListCheckPeak(ChargeBillingConfQueryCondition condition);
    List<ChargePeriodsBo> queryPeriodListPeak(ChargeBillingConfQueryCondition condition);

    /**
     * @Description 批量查询分时设置
     * <AUTHOR>
     * @Date 2022/6/23 18:28
     * @param chcNo
     * @param chcNos
     */
    List<Map> queryChargePeriods(String chcNo, String[] chcNos);

    /**
     * @Description 查询站点下发的计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getStationChargePeriods(Map map);

    /**
     * @Description 通过stationId查询下发的新版计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getNewStationChargePeriods(Map map);

    /**
     * @Description 通过stationId查询下发的新版计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getPileStationChargePeriods(Map map);

    /**
     * @Description 根据订单号查询计费模板，根据billCtlMode判断是否分时
     * <AUTHOR>
     * @Date 2022/6/23 20:01
     * @param map
     */
    List<Map> getChargeBillRltModel(Map map);

    /**
     * @Description 尖峰计费生效任务定时器
     * <AUTHOR>
     * @Date 2022/6/27 20:31
     */
    void peakEffTast();

    /**
     * @Description 查询所有关联
     * <AUTHOR>
     * @Date 2022/6/20 15:33
     */
    List<PeakRelBo> selectPeakRelAll();

    /**
     * @Description 校验订单使用的计费是否属于尖峰计费
     * <AUTHOR>
     * @Date 2022/7/5 14:22
     * @param orderNo
     */
    Boolean checkOrderIsPeak(String orderNo);

    /**
     * @Description 校验订单使用的计费是否属于尖峰计费,如果是则添加关联
     * <AUTHOR>
     * @Date 2022/7/5 14:22
     * @param chcNo
     * @param chcNo
     */
    void saveOrderExtend(String ordNo,String chcNo);
}
