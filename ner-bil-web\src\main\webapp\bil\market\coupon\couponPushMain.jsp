<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券推广">
</ls:head>
<ls:body>
    <ls:layout-use>

    <ls:form name="couponPushForm" id="couponPushForm">

        <ls:text name="actId" property="actId" visible="false"></ls:text>
        <ls:text name="actType" property="actType" visible="false"></ls:text>

        <ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
        <ls:layout-put into="container">
        <table>
            <tr>
                <td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
            </tr>
        </table>
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td><ls:label text="优惠券名称"></ls:label></td>
                <td><ls:text name="cpnName"></ls:text></td>

                <td><ls:label text="优惠券内容"></ls:label></td>
                <td>
                    <ls:select name="busiType">
<%--                        <ls:options property="busiTypeList" scope="request" text="codeName" value="codeValue"/>--%>
                        <ls:option text="充电" value="02"></ls:option>
                    </ls:select>
                </td>

                <td><ls:label text="优惠券状态" id="lab_cpnStatus"></ls:label></td>
                <td>
                    <ls:select name="cpnStatus" id="sel_cpnStatus">
                        <ls:options property="cpnStatusList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>

                <td><ls:label text="优惠类型" id="lab_cpnType"></ls:label></td>
                <td>
                    <ls:select name="cpnType" property="cpnType" id="sel_cpnType">
                        <ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
            </tr>
            <tr>
                <td colspan="8">
                    <div class="pull-right">
                        <ls:button text="清空" onclick="clearAll"/>
                        <ls:button text="查询" onclick="query"/>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="8"><ls:grid url="" name="couponPushGrid" caption="优惠券推广列表" showRowNumber="false"
                                         height="300px" showCheckBox="false" singleSelect="true">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="add" text="添加" imageKey="add"  onclick="selectCoupon"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="delete" text="删除" imageKey="delete"  onclick="deleteCouponPush"></ls:gridToolbarItem>
                    </ls:gridToolbar>
                    <ls:column caption="活动ID" name="actId" hidden="true"/>
                    <ls:column caption="优惠券推广ID" name="pushId" hidden="true"/>
                    <ls:column caption="优惠券ID" name="cpnId" hidden="true"/>
                    <ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="20%"/>
                    <ls:column caption="优惠券类型" name="cpnType" align="center" hidden="true"/>
                    <ls:column caption="优惠券类型" name="cpnTypeName" align="center" width="8%"/>
                    <ls:column caption="优惠券内容" name="busiType" align="center" hidden="true"/>
                    <ls:column caption="优惠券内容" name="busiTypeName" align="center" width="8%"/>
                    <ls:column caption="面额/折扣" name="cpnAmt" align="right" width="10%"/>
                    <ls:column caption="发行数量" name="cpnNum" align="center" width="6%"/>
                    <ls:column caption="每人限领" name="limGetNum" align="center" width="6%"/>
                    <ls:column caption="已领数量" name="alrdyGetNum" align="center" width="6%"/>
                    <ls:column caption="有效时间范围" name="effectTime" align="center" width="20%"/>
                    <ls:column caption="状态" name="cpnStatusName" align="center" hidden="true" width="5%"/>
                    <ls:column caption="状态" name="cpnStatus" hidden="true"/>
                    <ls:column caption="创建时间" name="creTime" align="center" width="15%"/>
                    <ls:column caption="生效时间" name="eftDate" hidden="true"/>
                    <ls:column caption="失效时间" name="invDate" hidden="true"/>
                    <ls:column caption="每人限领数量" name="limGetNum" hidden="true"/>
                    <ls:column caption="优惠券生效时间类型" name="cpnTimeType" hidden="true"/>
                    <ls:column caption="生效时间偏移量" name="timeDuration" hidden="true"/>
                    <ls:column caption="生效时间偏移单位" name="timeUnit" hidden="true"/>
                    <ls:pager pageSize="15,20"></ls:pager>
                </ls:grid></td>
            </tr>

        </table>
        <ls:title text="优惠券推广连接"></ls:title>
        <table class="tab_search"  style="height: 40px">
            <tr>
                <td width="20%">
                    <ls:label text="是否仅通过链接获取优惠券" ref="isLink"/>
                </td>
                <td width="20%">

                    <ls:checklist type="radio" name="isLink" property="isLink" value="1">
                        <ls:checkitem value="1" text="是"></ls:checkitem>
                        <ls:checkitem value="0" text="否"></ls:checkitem>
                    </ls:checklist>

                </td>
                <td width="10%"></td>
                <td width="20%"></td>
                <td width="10%"></td>
                <td width="20%"></td>
            </tr>
        </table>

        </ls:layout-put>
        <ls:layout-put into="footer">
            <div class="pull-right">
                <ls:button text="上一步" onclick="doUp" />
                <%--<ls:button text="下一步" onclick="doPut" />--%>
                <ls:button text="保存" onclick="doSave" />
                <ls:button text="发布" onclick="doRelease" />
            </div>
        </ls:layout-put>
    </ls:form>
    </ls:layout-use>
    <ls:script>


        window.onload = function(){
            $('#lab_cpnStatus').hide();
            $('#sel_cpnStatus').hide();
            query();
        }

        function clearAll(){
            couponPushForm.clear();
        }

        function query(){
            var datas={};
            datas = couponPushForm.getFormData();
            couponPushGrid.query("~/billing/couponPush/getCouponsPush",datas);
        }


        function linkFunc(rowdata){
            var _cpnId = rowdata.cpnId;
            var _cpnName = rowdata.cpnName;
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnId + "\")'>"+_cpnName+"</a></u>";
        }

        window.showCouponDetail = function(_cpnId){
            LS.dialog("~/billing/coupon/initCouponDetail?cpnId="+_cpnId,"优惠券详细信息",850, 450,true);
        }

        <%-- 描述:添加按钮  创建人:biaoxiangd  创建时间:2017-06-07 20:34 --%>
        function selectCoupon(){
            var items = couponPushGrid.getItems();
            var _actType = actType.getValue();
            if(_actType=='08'){
                if(items.length>=1){
                    LS.message("info","积分兑换只能添加一张优惠券!");
                    return ;
                }
            }
            LS.dialog("~/billing/coupon/couponManage/05/"+actId.getValue(),"选择优惠券",800,600,true, null);
        }
        window.setCoupon = function(item){
            var _cpnId = item.cpnId;
            var params = {
                cpnId:item.cpnId,
                actId:actId.getValue()
            }
            LS.ajax("~/billing/couponPush/saveCouponPush",params,function(data){
                if(data.successful){
                    LS.message("info","添加成功!");
                query();
                }else{
                    LS.message("info","添加失败!");
                }
            });
        }

        function deleteCouponPush(){
            var item = couponPushGrid.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
            var params = item;
            LS.ajax("~/billing/couponPush/delCouponPush",params,function(data){
                if(data.successful){
                    LS.message("info","删除成功!");
                    query();
                }else{
                    LS.message("info","删除失败!");
                }
            });
        }

<%-- 描述:上一步  创建人:biaoxiangd  创建时间:2017/7/6 16:32 --%>
        function doUp(){
            var actIdValue = actId.getValue();
            window.location.href="<%=request.getContextPath() %>/marketacts/edit?actId="+actIdValue+"&actType="+actType.getValue();
        }
<%-- 描述:发布  创建人:biaoxiangd  创建时间:2017/7/6 16:32 --%>
        function doPut(){
            <%--LS.window.close();--%>
            var actIdValue = actId.getValue();
            window.location.href="<%=request.getContextPath() %>/marketacts/actInfo?actId="+actIdValue+"&actType=01";
        }

        function doSave(){
            <%--LS.window.close();--%>
            var params = couponPushForm.getFormData();
                LS.ajax("~/marketacts/isLinkdoSave",params,function(data){
                    if(data.successful){
                        LS.window.close();
                    }else{
                        LS.window.close();
                    }
                });
        }

        function doRelease(){
            var params = couponPushForm.getFormData();
            LS.ajax("~/marketacts/releaseAct",params,function(data){
                if(data.successful){
                    LS.message("info",data.resultHint);
                    LS.parent().doSearch();
                    LS.window.close();
                }else{
                    LS.message("info",data.resultHint);
                }
            });
        }

    </ls:script>
</ls:body>
</html>