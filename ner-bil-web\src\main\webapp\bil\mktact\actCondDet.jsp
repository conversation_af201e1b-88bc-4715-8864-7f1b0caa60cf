<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠条件明细">
    <script type="text/javascript" charset="utf-8" src="<%=pubPath %>/pub/validate/validation.js"></script>
    <style>

    </style>
</ls:head>

<ls:body>
    <ls:layout-use>
        <ls:form id="actCondDetForm" name="actCondDetForm">
            <ls:text name="actId" property="actId" type="hidden"/>
            <ls:text name="actType" property="actType" type="hidden"/>
            <ls:text name="actCondId" property="actCondId" type="hidden"/>
            <ls:text name="dctLvl" property="dctLvl" type="hidden"/>
            <ls:text name="actDctProdObj" value="" type="hidden"/>

            <ls:layout-put into="container">
                <ls:title text="优惠明细" expand="false"></ls:title>
                <table id="tab_det" align="center" class="tab_search">
                    <colgroup>
                        <col width="15%"/>
                        <col width="35%"/>
                        <col width="18%"/>
                        <col width="32%"/>
                    </colgroup>
                    <tr>
                        <td><ls:label text="消费满" ref="dctCondValue"/></td>
                        <td>
                            <ls:text name="dctCondValue" property="dctCondValue" required="true"></ls:text>
                        </td>
                        <td><ls:label text="优惠类别" ref="dctType"/></td>
                        <td>
                            <ls:select name="dctType" property="dctType" required="true" onchanged="doDctType">
                                <ls:options property="dctTypeList" scope="request" text="codeName"
                                            value="codeValue"></ls:options>
                            </ls:select>
                        </td>
                    </tr>
                    <tr id="tr_allFree">
                        <td><ls:label text="优惠值" name="discountValueLabel"/></td>
                        <td colspan="2">
                            <table>
                                <tr>
                                    <td width="20%">
                                        <ls:checklist name="allFree" property="allFree" onchanged="checkAllFree">
                                            <ls:checkitem value="1" text="全免"></ls:checkitem>
                                        </ls:checklist>
                                    </td>
                                    <td width="40%" id="td_dctCalcMethod">
                                        <ls:checklist type="radio" name="dctCalcMethod" value="01" property="dctCalcMethod" onchanged="changeCalcMethod">
                                            <ls:checkitem value="01" text="减免" ></ls:checkitem>
                                            <ls:checkitem value="02" text="折扣" ></ls:checkitem>
                                        </ls:checklist>
                                    </td>
                                    <td width="35%" id="td_dctValue"><ls:text name="dctValue" property="dctValue"></ls:text></td>
                                    <td width="5%" id="td_unit">&nbsp;<span id="td_unit_span">元</span></td>
                                </tr>
                            </table>
                        </td>
                        <td></td>
                    </tr>
                    <tr id="tr_grid" align="center">
                        <td colspan="5">
                            <ls:grid url="" name="actDctGrid" caption="优惠内容" showRowNumber="true" primaryKey="id"
                                     height="150px" showCheckBox="false" singleSelect="true" >
                                <ls:gridToolbar name="operation">
                                    <ls:gridToolbarItem name="addBtn" text="添加" imageKey="add" onclick="addAct"></ls:gridToolbarItem>
                                    <ls:gridToolbarItem name="delBtn" text="删除" imageKey="delete" onclick="delAct"></ls:gridToolbarItem>
                                </ls:gridToolbar>
                                <ls:column caption="优惠对象" name="actDctProdName" editableEdit="false" width="50%">
                                    <ls:selectEditor property="actDctProdIdList"
                                                     valueMember="PROD_ID" name="actDctProdId" required="true"
                                                     displayMember="PROD_NAME" onchanged="selActDctProd" onValidate="">
                                    </ls:selectEditor>
                                </ls:column>
                                <ls:column name="actDctAllFree" caption="" editableEdit="false" width="10%">
                                    <ls:checkListEditor type="checkbox">
                                        <ls:checkListItemEditor value="1" text="全免"></ls:checkListItemEditor>
                                    </ls:checkListEditor>
                                </ls:column>
                                <ls:column name="dctValue" caption="优惠数值" editableEdit="false" width="25%" >
                                    <ls:textEditor type="number"/>
                                </ls:column>
                                <ls:column name="unitY" caption=""  width="15%">
                                    <ls:textEditor value="元" readOnly="true" />
                                </ls:column>
                                <ls:column name="actDctCondUnitName" caption="" editableEdit="false" width="15%">
                                    <ls:selectEditor property="actDctCondUnitList"
                                                     valueMember="codeValue" name="actDctCondUnit" displayMember="codeName">
                                    </ls:selectEditor>
                                </ls:column>
                            </ls:grid>
                        </td>
                    </tr>
                </table>
            </ls:layout-put>
            <ls:layout-put into="footer">
                <div class="pull-right">
                    <ls:button text="保存" onclick="doSave"/>
                    <ls:button text="取消" onclick="doCanl"/>
                </div>
            </ls:layout-put>
        </ls:form>
    </ls:layout-use>

    <ls:script>
        discountValueLabel.$e.find("span").html('<font class="sign" color="red">*</font><font>优惠值</font>');


        window.actDctGrid = actDctGrid;

        window.onload = function(){
            doDctType();
            checkAllFree();
            $('#_id_actDctGrid_dctValue').removeClass("ui-state-default ui-th-column ui-th-ltr");
            $('#_id_actDctGrid_actDctCondUnitName').removeClass("ui-state-default ui-th-column ui-th-ltr");
            $('#_id_actDctGrid_unitY').removeClass("ui-state-default ui-th-column ui-th-ltr");
        }
        function checkAllFree(){
            var _allFree = allFree.getValue();
            if('1' == _allFree){
                $('#td_dctValue').hide();
                $('#td_unit').hide();
                $('#td_dctCalcMethod').hide();
            }else{
                $('#td_dctValue').show();
                $('#td_unit').show();
                $('#td_dctCalcMethod').show();
            }
        }

        function changeCalcMethod(){
            var _dctCalcMethod = dctCalcMethod.getValue();
            if(_dctCalcMethod == '02'){
                $('#td_unit_span').html('折');
            }else{
                $('#td_unit_span').html('元');
            }
        }

<%-- 描述:优惠对象变更  创建人:biaoxiangd  创建时间:2017/7/1 16:24 --%>
        function selActDctProd(rowid,rowdata,rowEditor){
            var _actDctProdId = '';
            var _prodId = "",_method = "";
            var items = actDctGrid.getItems();
            for(var i =0;i < items.length;i++){
                if(items[i].id == rowid){
                    _actDctProdId = items[i].actDctProdId;
                    break;
                }
            }
            if(!LS.isEmpty(_actDctProdId)){
                var arrProd = _actDctProdId.split('$');
                _prodId = arrProd[0];
                _method = arrProd[1];
            }
            if(!LS.isEmpty(_method) && !LS.isEmpty(_prodId)){
                var param = {
                    actDctProdObj: actDctProdObj.getValue(),
                    actDctProdJson: _actDctProdId,
                    methodCode: _method
                }
                LS.ajax("~/marketacts/firstFreeManage/changeActDctProd",param,function(data) {
                    var _obj = data.items[0].resultValue;
                    if(_obj.successful){
                        var _dctType = dctType.getValue();
                        if('0101' == _dctType){
                        rowEditor["actDctCondUnitName"].appendItems(_obj.resultValue);
                        }
                        actDctProdObj.setValue(_obj.actDctProdObj);
                    }else{
                        rowEditor["actDctProdName"].setValue('');
                        LS.message("error",_obj.resultHint);
                        return;
                    }
                });
            }
        }
<%-- 描述:优惠类别  创建人:biaoxiangd  创建时间:2017/6/30 10:44 --%>
        function doDctType(){
            var _dctType = dctType.getValue();
            if('0100' == _dctType || '0401' == _dctType){
                $('#tr_allFree').show();
                $('#tr_grid').hide();
            }else if('0101' == _dctType){
                $('#tr_allFree').hide();
                $('#tr_grid').show();
                actDctGrid.setHideCol(['unitY']);
                actDctGrid.setShowCol(['actDctCondUnitName']);
            }else if('0102' == _dctType){
                $('#tr_allFree').hide();
                $('#tr_grid').show();
                actDctGrid.setShowCol(['unitY']);
                actDctGrid.setHideCol(['actDctCondUnitName']);
            }else{
                $('#tr_allFree').hide();
                $('#tr_grid').show();
                actDctGrid.setHideCol(['unitY']);
                actDctGrid.setShowCol(['actDctCondUnitName']);
            }
        }
<%-- 描述:表格新增  创建人:biaoxiangd  创建时间:2017/6/30 11:10 --%>
        function addActAfter(rowId ,cellName, value, column, rowEditor){
       <%--
            var rowEditor = this.gridTable[0].tableEditor[rowid];

            var _actDctProdObj = actDctProdObj.getValue();
            if(LS.isEmpty(_actDctProdObj)){
                actDctGrid.addRow();
            }else{
                LS.ajax("~/marketacts/firstFreeManage/addActDctProd?actDctProdObj="+_actDctProdObj,'',function(data) {

                    var _obj = data.items[0];
                    if(_obj.successful){

                    }
                });
            }--%>
        }
    function addAct(){
        var _dctLvl = dctLvl.getValue();
        var allItems = actDctGrid.getItems();
        if('1' == _dctLvl && allItems.length > 5){
            LS.message("info","优惠条件内容最多只支持5级");
            return false;
        }
        actDctGrid.addRow();
    }
<%-- 描述:删除  创建人:biaoxiangd  创建时间:2017/6/30 11:18 --%>
    function delAct(){
        var checkedItems = actDctGrid.getCheckedItems();
        if(checkedItems==null || checkedItems.length!=1){
            LS.message("info","请选择一条记录!");
            return;
        }
        var allItems = actDctGrid.getItems();
        if(allItems && allItems.length>0) {
            for(var i=0; i< allItems.length; i++) {
                if(checkedItems[0].id == allItems[i].id){
                    var _actDctProdId = allItems[i].actDctProdId;
                    var _actDctProdObj = actDctProdObj.getValue();
                    if(!LS.isEmpty(_actDctProdId) && !LS.isEmpty(_actDctProdObj)){
                        var bk = _actDctProdObj.replace(_actDctProdId,'');
                        actDctProdObj.setValue(bk);
                    }
                    actDctGrid.removeItem(allItems[i]);
                }
            }
        }
    }

        <%-- 描述:保存  创建人:biaoxiangd  创建时间:2017/6/29 15:24 --%>
        function doSave(){
            if(!actCondDetForm.valid()){
                return;
            }

            var _dctLvl = dctLvl.getValue();
            var allItems = actDctGrid.getItems();
            if('1' == _dctLvl && allItems.length > 5){
                LS.message("info","优惠条件内容最多只支持5级");
                return false;
            }

            //优惠值全免或者指定金额二选一
            var allFreeValue = allFree.getValue();
            var dctValueValue = dctValue.getValue();
            if(LS.isEmpty(allFreeValue) && LS.isEmpty(dctValueValue)){
                LS.message("error","优惠值必填");
                return;
            }

            var dctCalcMethodValue = dctCalcMethod.getValue();
            if(dctCalcMethodValue == "02"){
                if(dctValueValue > 10 || dctValueValue < 0 ||(dctValueValue.indexOf(".")>0 && dctValueValue.split(".")[1].length > 2)){
                    LS.message("error","折扣为0.01-9.99的数字");
                    return;
                }
            }

            var dataParams = actCondDetForm.getFormData();
            var changeItems = actDctGrid.getItems();
            for(var i=0;i < changeItems.length;i++){
                if(LS.isEmpty(changeItems[i].actDctProdName)){
                    LS.message("error","存在未选择的优惠内容");
                    return;
                }
            }
            dataParams.detItems = changeItems;
            LS.ajax("~/marketacts/firstFreeManage/saveActCondDet",dataParams,function(data) {
                if(data.successful){
                    LS.message("info","保存成功");
                    LS.parent().queryGrid();
                    LS.window.close();
                }else{
                    LS.message("error",data.resultHint);
                }
            });
        }
        <%-- 描述:取消  创建人:biaoxiangd  创建时间:2017/6/29 15:17 --%>
        function doCanl(){
            LS.window.close();
        }

        function remove(array,index){
            if(index <= (array.length - 1)){
                for(var i = index; i < array.length; i++){
                    array[i] = array[i + 1 ];
                }
            }else{
                throw new Error("超出最大索引");
            }
            array.length = array.lenght - 1;
            return array;
        }

    </ls:script>
</ls:body>
</html>