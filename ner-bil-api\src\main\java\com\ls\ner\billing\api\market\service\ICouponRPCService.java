package com.ls.ner.billing.api.market.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.market.vo.CouponPutRpcBo;
import org.apache.ibatis.annotations.Param;

/**
 * 描述:RPC05-11 优惠券RPC接口
 * ICouponRPCService.java
 * 作者：biaoxiangd
 * 创建日期：2017-06-06 17:16
 **/
public interface ICouponRPCService {

    /**
     * 描述:RPC05-11-01 优惠券详情
     *
     * @param: map<"cpnList",List<String(spnId)>>
     * @return: map<"cpnList",Map<cpnId,json(couponBO)>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 22:57
     */
    public Map<String, Object> couponInfo(Map<String, Object> map) throws Exception;

    /**
     * 描述:RPC05-11-02 验证优惠券可用性
     *
     * @param: map<"cpnList",List<String(spnId)>>
     * @return: map<"cpnList",Map<cpnId,json(couponBO)>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 22:57
     */
    public Map<String, Object> couponValid(Map<String, Object> map) throws Exception;

    /**
     * 描述:RPC05-11-03 优惠券优惠计算
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 17:18
     */
    public Map<String, Object> couponCalc(Map<String, Object> map) throws Exception;

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-01 可领用的优惠券
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    public List<Map<String, Object>> couponsRestGET(Map<String, String> map) throws Exception;

    /**
     * 描述:REST05-11 优惠券
     * REST05-11-02 优惠券领取
     *
     * @param: [map]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 14:27
     */
    public Map<String, Object> couponsRestPOST(Map<String, String> map) throws Exception;


    /**
     * 描述: RPC05-11-04验证优惠券可用性（订单）
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/10 3:15
     */
    List<Map<String, Object>> couponValidByOrder(Map<String, Object> map) throws Exception;


    /**
     * 描述:有条件的计算
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 21:30
     */
    Map<String, Object> calcDctCond(Map<String, String> map);

    /**
     * 描述:优惠类别 计算
     *
     * @param: [Map<String,String>]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 20:41
     */
    Map<String, Object> calcDctType(Map<String, String> map);

    /**
     * 描述:提供给发放功能调用，更新已领属性
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-05 12:07
     */
    int rpcUpdateCoupon(Map<String, String> map);

    /**
     * @param map 描述:提供给注册送，邀请送发放使用，更新优惠券发放表及优惠券发放明细表
     * @description
     * <AUTHOR>
     * @create 2018-05-24 16:21:30
     */
    void rpcUpdateCouponPutAndDet(Map<String, String> map);

    /**
     * @param inMap
     * @description 可用优惠券验证（新增站点城市校验）
     * <AUTHOR>
     * @create 2018-06-26 10:21:37
     */
    List<Map<String, Object>> couponCityAndStationValid(Map<String, Object> inMap) throws Exception;

    /**
     * @param map
     * @description 查询优惠活动
     * <AUTHOR>
     * @create 2018-11-06 14:47:27
     */
    List<Map<String,Object>> couponActInfo(Map<String, Object> map);

    List<Map> qryCouponList(@Param("cpnIds") String[] cpnIds);

    /**
     * @param makActMap
     * @description 记录里发放信息
     * <AUTHOR>
     * @create 2018-12-19 14:35:33
     */
    void insertPutCpn(Map makActMap);


   /**
    * @param map
    * @description  我的优惠券详情
    * <AUTHOR>
    * @create 2019-05-26 12:12:15
    */
   public Map<String, Object> myCouponInfo(Map<String, Object> map) throws Exception;

    /**
     * @param map
     * @description  优惠券可用性
     * <AUTHOR>
     * @create 2019-05-26 15:55:23
     */
    public Map<String, Object> myCouponValid(Map<String, Object> map) throws Exception;


    /**
     * @param map
     * @description
     * <AUTHOR>
     * @create 2019-05-26 20:58:04
     */
    public List<Map<String, Object>> couponsGET(Map<String, Object> map) throws Exception;
    /**
     * @param map
     * @description 优惠券详情（新）
     * <AUTHOR>
     * @create 2019-06-10 10:51:26
     */
    public List<Map<String, Object>> couponsDetailInfo(Map<String, Object> map);

    public  List<Map<String, Object>> getGoodsVrCoupon() ;

    public Map<String, Object> getGoodsVrCouponDetail(Map<String, Object> map);

    Map<String, Object> queryCpnByActId(Map<String, String> map) throws Exception;

    Map<String, Object> queryCpnNum(Map<String, Object> map) throws Exception;

    Map<String,String>  couponPut(CouponPutRpcBo delayPutBo);

    List<CouponPutRpcBo>  pollDelayCouponPutInfo(Date delayTime);

    Map<String,String>  getSuperpositionFlag(String cpnId);


    /**
     * 获取首次绑定爱车 用途的 优惠卷
     * @param cpnId
     * @return
     */
    Map<String,Object>  getMyCarCoupon();

    /**
     * 获取首次绑定爱车  优惠卷 发放
     * @param delayPutBo
     * @return
     */
    Map<String,String>  myCarCouponPut(CouponPutRpcBo delayPutBo);
}

