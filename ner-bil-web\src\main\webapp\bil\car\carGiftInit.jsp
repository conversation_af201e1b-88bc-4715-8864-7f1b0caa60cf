<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://www.longshine.com/taglib/ner" prefix="ner"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车辆管理">
	<script type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:label text="车牌号"/></td>
				<td><ls:text name="licenseNo"></ls:text></td>
				<td>
					<ls:label text="车架号"/>
				</td>
				<td>
					<ls:text name="vin"></ls:text>
				</td>
				<td>
					<ls:label text="身份证号"/>
				</td>
				<td>
					<ls:text name="certNo"></ls:text>
				</td>
				<%--<td>
					<ls:label text="申请时间"></ls:label>
				</td>
				<td>
					<table>
						<tr>
							<td>
								<ls:date name="applyTimeBgn"></ls:date>
							</td>
							<td>
								<span>至</span>
							</td>
							<td>
								<ls:date name="applyTimeEnd"></ls:date>
							</td>
						</tr>
					</table>
				</td>--%>
			</tr>
			<tr>
				<td>
					<ls:label text="审核时间"></ls:label>
				</td>
				<td>
					<table>
						<tr>
							<td>
								<ls:date name="auditTimeBegin" format="yyyy-MM-dd"/>
							</td>
							<td>
								<span>至</span>
							</td>
							<td>
								<ls:date name="auditTimeEnd" format="yyyy-MM-dd"/>
							</td>
						</tr>
					</table>
				</td>
				<td>
					<ls:label text="领取状态" ></ls:label>
				</td>
				<td>
					<ls:select name="giftStatus">
						<ls:options property="carGiftGetStatusList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<%--<td>
					<ls:label text="是否营运车辆" />
				</td>
				<td>
					<ls:select name="carType">
						<ls:options property="giftCarTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>--%>
				<td>
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch"/>
						<ls:button text="清空" onclick="doClear"/>
					</div>
				</td>
			</tr>
		</table>
		<table  align="center" class="tab_search">
			<tr>
				<td>
					<ls:grid url="" caption="领取列表" showRowNumber="false" name="carGiftGrid" height="250px" showCheckBox="false"
							 allowScroll="true" exportFilename="领取列表记录">
						<ls:gridToolbar name="carGiftBar">
							<ls:gridToolbarItem name="importEquip" text="导入" imageKey="add"
												onclick="importEquip"></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column caption="车牌号" name="licenseNo" width="100px"></ls:column>
						<ls:column caption="车架号" name="vin" width="150px"></ls:column>
						<ls:column caption="身份证号" name="certNo" width="200px"></ls:column>
						<ls:column caption="购买人姓名" name="buyerName" width="100px"></ls:column>
						<ls:column caption="申请时间" name="applyTime" width="200px"></ls:column>
						<ls:column caption="手机号" name="mobile" width="150px"></ls:column>
						<ls:column caption="支付宝帐号" name="alipayNo" width="150px"></ls:column>
						<ls:column caption="购车4S店" name="buyAddr" width="200px"></ls:column>
<%--						<ls:column caption="审核时间" name="auditTime" width="200px"></ls:column>--%>
						<ls:column caption="领取状态" name="giftStatusName" width="100px"></ls:column>
						<ls:column caption="操作" name=""  formatFunc="linkFunc"></ls:column>
						<ls:pager pageSize="15" pageIndex="1"></ls:pager>
					</ls:grid>
				</td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
		function doClear(){
			searchForm.clear();
		}
        //查询
        window.doSearch = doSearch;
        doSearch();
        function doSearch() {
            if(!LS.isEmpty(auditTimeBegin.getValue()) && !LS.isEmpty(auditTimeEnd.getValue())){
                if(auditTimeBegin.getValue()>auditTimeEnd.getValue()){
                    LS.message("info","审核开始时间不能大于结束时间，请重新输入");
                    return;
                }
            }
            var params = searchForm.getFormData();
            if(!LS.isEmpty(auditTimeBegin.getValue())){
                params.auditTimeBegin = params.auditTimeBegin+" 00:00:00"
            }
            if(!LS.isEmpty(auditTimeEnd.getValue())){
                params.auditTimeEnd = params.auditTimeEnd+" 23:59:59"
            }
            carGiftGrid.query('~/bil/carGift/querys', params, function (obj) {});
        }


        window.onload = function () {
            initGridHeight('searchForm','carGiftGrid');
        }

        function linkFunc(rowdata) {
            var _giftStatus = rowdata.giftStatus;
            if("03" == _giftStatus){
                return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doPush(\"" + rowdata.giftId + "\")'>发放</a></u>";
            }else {
                return "";
			}
        }

		window.doPush = doPush;
        function doPush(giftId) {
            LS.dialog("~/bil/carGift/confirm?giftId=" + giftId, "购车礼金发放", 500, 300, true, null);
        }

        //导入
        function importEquip() {
            LS.dialog("~/bil/carGift/importFilePage", "购车信息导入", 400, 150, true, null);
        }

	</ls:script>
</ls:body>
</html>
