package com.ls.ner.billing.mktact.service;

import com.ls.ner.billing.mktact.bo.ActInfoBo;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 描述:资讯信息
 * IActInfoService.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 11:47
 **/
public interface IActInfoService {

    /**
     * 描述: 获取资讯信息
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    public ActInfoBo queryActInfo(ActInfoBo bo);
    /**
     * 描述:资讯信息保存
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间:2017/6/27 11:48
     */
    public int saveActInfo(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception;

    /**
     * 描述:变更资讯信息
     *
     * @param: [infoBo, request]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 18:04
     */
    public int updateActInfo(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception;


    /**
     * @param
     * @description
     * <AUTHOR>
     * @create 2019/6/4 11:31
     */
    public int saveActInfoNew(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception;

    /**
     * @param
     * @description
     * <AUTHOR>
     * @create 2019/6/4 11:31
     */
    public int updateActInfoNew(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception;


    /**
     * @param
     * @description  活动发布
     * <AUTHOR>
     * @create 2020-09-22 14:28:29
     */
    void releaseAct(String actId,String isLink) throws Exception;


    /**
     * 更新活动是否仅通过链接获取优惠券 0否1是
     * @param infoBo
     * @return
     * @throws Exception
     */
    void updateActForIsLink(MarketActBo infoBo)  throws Exception;
}
