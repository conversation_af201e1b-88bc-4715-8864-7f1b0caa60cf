package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.Date;

public class MainChargeConfgBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String mainNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal price;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.MIN_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal minCost;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.MAX_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal maxCost;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.CHARG_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_config.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Integer chargeNum;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.SYSTEM_ID
     *
     * @return the value of e_main_charge_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.SYSTEM_ID
     *
     * @param systemId the value for e_main_charge_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.MAIN_NO
     *
     * @return the value of e_main_charge_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getMainNo() {
        return mainNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.MAIN_NO
     *
     * @param mainNo the value for e_main_charge_config.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setMainNo(String mainNo) {
        this.mainNo = mainNo == null ? null : mainNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.BILLING_NO
     *
     * @return the value of e_main_charge_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.BILLING_NO
     *
     * @param billingNo the value for e_main_charge_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBillingNo(Long billingNo) {
        this.billingNo = billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.PRICE
     *
     * @return the value of e_main_charge_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.PRICE
     *
     * @param price the value for e_main_charge_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.MIN_COST
     *
     * @return the value of e_main_charge_config.MIN_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getMinCost() {
        return minCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.MIN_COST
     *
     * @param minCost the value for e_main_charge_config.MIN_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setMinCost(BigDecimal minCost) {
        this.minCost = minCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.MAX_COST
     *
     * @return the value of e_main_charge_config.MAX_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getMaxCost() {
        return maxCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.MAX_COST
     *
     * @param maxCost the value for e_main_charge_config.MAX_COST
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setMaxCost(BigDecimal maxCost) {
        this.maxCost = maxCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.DATA_OPER_TIME
     *
     * @return the value of e_main_charge_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_main_charge_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.DATA_OPER_TYPE
     *
     * @return the value of e_main_charge_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_main_charge_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.CHARG_MODE
     *
     * @return the value of e_main_charge_config.CHARG_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargMode() {
        return chargMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.CHARG_MODE
     *
     * @param chargMode the value for e_main_charge_config.CHARG_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargMode(String chargMode) {
        this.chargMode = chargMode == null ? null : chargMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.CHARGE_TYPE
     *
     * @return the value of e_main_charge_config.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeType() {
        return chargeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.CHARGE_TYPE
     *
     * @param chargeType the value for e_main_charge_config.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.CHARGE_UNIT
     *
     * @return the value of e_main_charge_config.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeUnit() {
        return chargeUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.CHARGE_UNIT
     *
     * @param chargeUnit the value for e_main_charge_config.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeUnit(String chargeUnit) {
        this.chargeUnit = chargeUnit == null ? null : chargeUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_config.CHARGE_NUM
     *
     * @return the value of e_main_charge_config.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Integer getChargeNum() {
        return chargeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_config.CHARGE_NUM
     *
     * @param chargeNum the value for e_main_charge_config.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeNum(Integer chargeNum) {
        this.chargeNum = chargeNum;
    }
}