package com.ls.ner.billing.api.charge.condition;

import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;
import java.util.List;

/**
 * 充电计费配置查询条件
 * <AUTHOR>
 */
public class ChargeBillingConfQueryCondition extends QueryCondition {

	private String orgCode;//管理单位
	private String orgCodeName;
	private String chcNo;//充电计费编号
	private String stationName;//充电站名称
	private String stationId;//充电站编号
	private String chcStatus;//状态，01有效、02无效、03草稿
	private String endEftDate;//生效日期止
	private String unChcStatus;//不生效状态
	private String custId;//用户Id
	private String billType;//计费类型
	private List orgList;//管理单位list
	private String pileId;//桩标识

	//费用编号
	private String itemNo ;
	//服务费计费模式  01标准，02分时
	private String serviceMode;

	public List getOrgList() {
		return orgList;
	}

	public void setOrgList(List orgList) {
		this.orgList = orgList;
	}

	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	public String getCustId() {
		return custId;
	}

	public void setCustId(String custId) {
		this.custId = custId;
	}

	public String getUnChcStatus() {
		return unChcStatus;
	}
	public void setUnChcStatus(String unChcStatus) {
		this.unChcStatus = unChcStatus;
	}
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	public String[] getStationIds() {
		if(StringUtils.nullOrBlank(stationId))
			return null;
		return stationId.split(",");
	}
	public String getChcStatus() {
		return chcStatus;
	}
	public void setChcStatus(String chcStatus) {
		this.chcStatus = chcStatus;
	}
	public String getEndEftDate() {
		return endEftDate;
	}
	public void setEndEftDate(String endEftDate) {
		this.endEftDate = endEftDate;
	}

	public String getPileId() {
		return pileId;
	}

	public void setPileId(String pileId) {
		this.pileId = pileId;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getServiceMode() {
		return serviceMode;
	}

	public void setServiceMode(String serviceMode) {
		this.serviceMode = serviceMode;
	}
}
