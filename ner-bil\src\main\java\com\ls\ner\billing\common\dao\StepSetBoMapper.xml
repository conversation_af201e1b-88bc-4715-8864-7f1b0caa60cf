<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.StepSetBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.StepSetBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="STEP_SET_ID" jdbcType="BIGINT" property="stepSetId" />
    <result column="MAIN_NO" jdbcType="VARCHAR" property="mainNo" />
    <result column="SN" jdbcType="DECIMAL" property="sn" />
    <result column="START_POINT" jdbcType="DECIMAL" property="startPoint" />
    <result column="END_POINT" jdbcType="DECIMAL" property="endPoint" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, STEP_SET_ID, MAIN_NO, SN, START_POINT, END_POINT, DATA_OPER_TIME, DATA_OPER_TYPE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.StepSetBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_step_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_step_set
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_step_set
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.StepSetBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_step_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.StepSetBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_step_set (SYSTEM_ID, STEP_SET_ID, MAIN_NO, 
      SN, START_POINT, END_POINT, 
      DATA_OPER_TIME, DATA_OPER_TYPE)
    values (#{systemId,jdbcType=BIGINT}, #{stepSetId,jdbcType=BIGINT}, #{mainNo,jdbcType=VARCHAR}, 
      #{sn,jdbcType=DECIMAL}, #{startPoint,jdbcType=DECIMAL}, #{endPoint,jdbcType=DECIMAL}, 
      #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.StepSetBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_step_set
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="stepSetId != null">
        STEP_SET_ID,
      </if>
      <if test="mainNo != null">
        MAIN_NO,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="startPoint != null">
        START_POINT,
      </if>
      <if test="endPoint != null">
        END_POINT,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="stepSetId != null">
        #{stepSetId,jdbcType=BIGINT},
      </if>
      <if test="mainNo != null">
        #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=DECIMAL},
      </if>
      <if test="startPoint != null">
        #{startPoint,jdbcType=DECIMAL},
      </if>
      <if test="endPoint != null">
        #{endPoint,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.StepSetBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_step_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_step_set
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.stepSetId != null">
        STEP_SET_ID = #{record.stepSetId,jdbcType=BIGINT},
      </if>
      <if test="record.mainNo != null">
        MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        SN = #{record.sn,jdbcType=DECIMAL},
      </if>
      <if test="record.startPoint != null">
        START_POINT = #{record.startPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.endPoint != null">
        END_POINT = #{record.endPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_step_set
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      STEP_SET_ID = #{record.stepSetId,jdbcType=BIGINT},
      MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      SN = #{record.sn,jdbcType=DECIMAL},
      START_POINT = #{record.startPoint,jdbcType=DECIMAL},
      END_POINT = #{record.endPoint,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.StepSetBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_step_set
    <set>
      <if test="stepSetId != null">
        STEP_SET_ID = #{stepSetId,jdbcType=BIGINT},
      </if>
      <if test="mainNo != null">
        MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        SN = #{sn,jdbcType=DECIMAL},
      </if>
      <if test="startPoint != null">
        START_POINT = #{startPoint,jdbcType=DECIMAL},
      </if>
      <if test="endPoint != null">
        END_POINT = #{endPoint,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.StepSetBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_step_set
    set STEP_SET_ID = #{stepSetId,jdbcType=BIGINT},
      MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      SN = #{sn,jdbcType=DECIMAL},
      START_POINT = #{startPoint,jdbcType=DECIMAL},
      END_POINT = #{endPoint,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>