package com.ls.ner.billing.xpcharge.dao;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.xpcharge.bo.PeakRelBo;
import com.ls.ner.billing.xpcharge.bo.PeakTimeBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface IXpPeakDao {

    /**
     * @Description 插入尖峰电价设置
     * <AUTHOR>
     * @Date 2022/6/20 15:23
     * @param peakTimeBo
     */
    void insertTimePeak(PeakTimeBo peakTimeBo);

    /**
     * @Description 查询有效的尖峰电价设置
     * <AUTHOR>
     * @Date 2022/6/20 15:30
     */
    PeakTimeBo selectPeakTimeEffective();

    /**
     * @Description 批量插入尖峰电价关联
     * <AUTHOR>
     * @Date 2022/6/20 15:24
     * @param peakRelBos
     */
    void insertTimePeakRels(@Param("peakRelBos") List<PeakRelBo> peakRelBos);

    /**
     * @Description 通过计费编号删除关联
     * <AUTHOR>
     * @Date 2022/6/20 15:27
     * @param chcNo
     */
    void deleteTimePeakRelByChcNo(@Param("chcNo") String chcNo);

    /**
     * @Description 查询所有关联
     * <AUTHOR>
     * @Date 2022/6/20 15:33
     */
    List<PeakRelBo> selectPeakRelAll();

    /**
     * @Description 分页查询
     * <AUTHOR>
     * @Date 2022/6/22 15:23
     * @param condition
     */
    List<PeakRelBo> pagePeakRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 分页数量
     * <AUTHOR>
     * @Date 2022/6/22 15:24
     * @param condition
     */
    int countPageRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 分页查询-未关联
     * <AUTHOR>
     * @Date 2022/6/22 15:23
     * @param condition
     */
    List<PeakRelBo> pageUnPeakRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 分页数量-未关联
     * <AUTHOR>
     * @Date 2022/6/22 15:24
     * @param condition
     */
    int countPageUnRelBoList(XpChargeBillingConfQueryCondition condition);

    /**
     * @Description 查询
     * <AUTHOR>
     * @Date 2022/6/20 16:36
     * @param chcNo
     */
    PeakRelBo selectPeakRelByChcNo(@Param("chcNo")String chcNo);
    List<PeakRelBo> selectPeakRelByChcNos(@Param("chcNos")String[] chcNos);

    /**
     * @Description 批量插入尖峰电价关联日志
     * <AUTHOR>
     * @Date 2022/6/20 15:26
     * @param peakRelBos
     */
    void insertTimePeakRelLogs(@Param("peakRelBos") List<PeakRelBo> peakRelBos);

    /**
     * @Description 插入分时费用明细
     * <AUTHOR>
     * @Date 2022/6/21 14:21
     * @param periodsBos
     */
    void insertPeriodPeaks(@Param("periodsBos") List<ChargePeriodsBo> periodsBos);

    /**
     * @Description 通过计费编号查询尖峰计费
     * <AUTHOR>
     * @Date 2022/6/21 14:40
     * @param condition
     */
    List<ChargePeriodsBo> selectPeriodPeakListByChcNo(ChargeBillingConfQueryCondition condition);

    /**
     * @Description 通过计费编号删除对应尖峰计费
     * <AUTHOR>
     * @Date 2022/6/21 14:42
     * @param chcNo
     */
    void deletePeriodPeakByChcNo(@Param("chcNo")String chcNo);

    /**
     * @Description 批量查询分时计费明细
     * <AUTHOR>
     * @Date 2022/6/23 18:25
     * @param chcNo
     * @param chcNos
     */
    List<Map> queryChargePeriods(@Param("chcNo") String chcNo, @Param("chcNos") String[] chcNos);

    /**
     * @Description 查询站点下发的计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getStationChargePeriods(Map map);

    /**
     * @Description 通过stationId查询下发的新版计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getNewStationChargePeriods(Map map);

    /**
     * @Description 通过站点查询关联的计费
     * <AUTHOR>
     * @Date 2022/6/23 19:49
     * @param map
     */
    String selectChcNoByStation(Map map);

    /**
     * @Description 通过stationId查询下发的新版计费模型
     * <AUTHOR>
     * @Date 2022/6/23 19:35
     * @param map
     */
    List<Map<String,Object>> getPileStationChargePeriods(Map map);

    /**
     * @Description 根据订单号查询计费模板，根据billCtlMode判断是否分时
     * <AUTHOR>
     * @Date 2022/6/23 20:01
     * @param map
     */
    List<Map> getChargeBillRltModel(Map map);

    /**
     * @Description 通过计费编号修改下发成功状态
     * <AUTHOR>
     * @Date 2022/6/27 20:07
     * @param chcNo
     */
    void updateSendLogSendStatus(@Param("chcNo") String chcNo);

    /**
     * @Description 通过计费查询关联的站点
     * <AUTHOR>
     * @Date 2022/6/23 19:49
     * @param chcNo
     */
    List<String> selectStationByChcNo(@Param("chcNo") String chcNo);
}
