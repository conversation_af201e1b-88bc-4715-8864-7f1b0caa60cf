package com.ls.ner.billing.api.rent.model;


public class BillingLocateCondition implements IBillingLocateCondition {

	
	
	private String autoModelNo;
	private String orgCode;
	private String rtId;
	private String rtNo;
	@Override
	public String getAutoModelNo() {
		return autoModelNo;
	}
	@Override
	public void setAutoModelNo(String autoModelNo) {
		this.autoModelNo = autoModelNo;
	}
	@Override
	public String getOrgCode() {
		return orgCode;
	}
	@Override
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	@Override
	public String getRtId() {
		return rtId;
	}
	@Override
	public void setRtId(String rtId) {
		this.rtId = rtId;
	}
	@Override
	public String getRtNo() {
		return rtNo;
	}
	@Override
	public void setRtNo(String rtNo) {
		this.rtNo = rtNo;
	}
	
	
	
}
