package com.ls.ner.billing.api.rent.service;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.rent.model.BillingFunc;
import com.ls.ner.billing.api.rent.model.IBillingLocateCondition;
import com.ls.ner.billing.api.rent.model.OrderBillingRela;
import com.ls.ner.billing.api.rent.model.OrderBillingRelaResult;
import com.ls.ner.billing.api.rent.model.RentRun;
import com.ls.ner.billing.api.rent.vo.OrderBillingPriceResponse;
import com.ls.ner.billing.api.rent.vo.OrderBillingResponse;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.api.common.bo.BillingOrderRelaBo;

/**
 * 分时租赁 计费服务
 * <AUTHOR>
 *
 */
public interface IRentbillService {
	
	/**
	 * 根据公司、租赁类型、车型编号获取定价配置。对应关系错误，此方法不可用。
	 * @param orgCode
	 * @param rentType 对应subBe @see BillingConstants.SubBe
	 * @param autoModelNos
	 * @return key是车型编号，value是定价配置的map
	 */
	@Deprecated
	public Map<String,BillingConfigBo> getBillingConfigBoMap(String rentType, List<String> orgAutoModelKeys);
	
	/**
	 * 指定时租/日租等，查询各租赁点车型下所有可选的定价列表  用于批量查询
	 * @param rentType
	 * @param List[tNo、orgCode、autoModelNo]
	 * @return
	 * <AUTHOR>
	 */
	public Map<String, BillingConfigBo> locateBillingConfigBoMap(
            String rentType, List<? extends IBillingLocateCondition> billingLocateConditions);

	/**
	 * 不指定时租/日租等，查询各租赁点车型下所有可选的定价列表，列表中按照时租、日租...的顺序排列   用于批量查询 
	 * 入参：rtNo、orgCode、autoModelNo
	 * @param billingLocateConditions
	 * @return
	 */
	public Map<String, List<BillingConfigBo>> locateBillingConfigBoListMap(
            List<? extends IBillingLocateCondition> billingLocateConditions);
	
	/**
	 * 
	 * 方法说明：通过订单编号appNo和计费类型billingType获取订单总费用和明细
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月2日 上午12:02:49
	 * History:  2016年5月2日 上午12:02:49   lipf   Created.
	 *
	 * @param paraMap
	 * @return
	 *
	 */
	public Map<String, Object> getOrderPrcCharge(Map<String, String> paraMap);
	
	/**
	 * 保存订单和定价相关的部分,返回相关的预付费的计费结果  即预付费  同时保存预收计费明细信息  要传入saveLogFlag标识true 用于租车下单提交计算预收费用
	 * @param orderBillingRela
	 */
	public OrderBillingRelaResult saveOrderBillingRela(OrderBillingRela orderBillingRela);
	
	/**
	 * 
	 * 方法说明：根据appNo、billType查询订单计费信息(billType-01预收、02结算)
	 *		出参billingOrderRela、billingOrderPricingList 
	 * Author：        lipf                
	 * Create Date：   2016年4月7日 下午3:38:58
	 * History:  2016年4月7日 下午3:38:58   lipf   Created.
	 *
	 * @param OrderBillingPriceResponse
	 * @return
	 *
	 */
	public OrderBillingPriceResponse getBillingOrderPriceInfo(BillingOrderRelaBo billingOrderRelaBo);

	/**
	 * 供app下单前查找车辆的定价信息使用。返回app展示需要的订单信息  展示每项预收费用
	 * @param billingLocateCondition
	 * @return
	 */
	public OrderBillingResponse getOrderBillingInfo(OrderBillingRela orderBillingRela);
	
	/**
	 * 
	 * 方法说明：根据appNo查找按日期（applyDate）分组好的定价配置
	 *		主要用于分时计费模式中订单结算时 统计每日每个分时段的时间和里程数
	 *		出参MAP中key格式:"20160407" 
	 * Author：        lipf                
	 * Create Date：   2016年4月7日 下午1:56:17
	 * History:  2016年4月7日 下午1:56:17   lipf   Created.
	 *
	 * @param orderBillingRela
	 * @return
	 *
	 */
	public Map<String,BillingFunc> getApplyDateBillingConfig(OrderBillingRela orderBillingRela);

	/**
	 * 根据租赁行驶记录计费  租赁结算时候使用
	 * @param rentRun 租赁行驶记录
	 * @param chargeTimePointFilter 付费时间点 00 算完整费用 01 只算预付费部分 02 只算后付费部分 @see  com.ls.ner.billing.api.BillingConstants.ChargeTimePoint
	 * @return
	 */
	public OrderBillingRelaResult calculate(RentRun rentRun, String chargeTimePointFilter);
	
	/**
	 * 定位到定价信息。一般用于从车辆、订单等关联的查询 用于单一查询
	 * @param rentType
	 * @param billingLocateCondition
	 * @return
	 */
	public BillingConfigBo locateBillingConfigBo(String rentType, IBillingLocateCondition billingLocateCondition);
	
	
	/**
	 * 
	 * 方法说明：租赁预收费用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月29日 下午8:40:21
	 * History:  2016年4月29日 下午8:40:21   lipf   Created.
	 *
	 * @param orderBillingRela
	 * @return
	 *
	 */
	public Map<String,Object> rentPrepayForHour(OrderBillingRela orderBillingRela);


	Map<String, Object> rentPrepayForDaily(OrderBillingRela orderBillingRela);
	
}
