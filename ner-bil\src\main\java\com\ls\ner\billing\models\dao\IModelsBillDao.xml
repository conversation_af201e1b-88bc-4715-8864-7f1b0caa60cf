<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.models.dao.IModelsBillDao">
	<resultMap id="BaseResultMap" type="com.ls.ner.billing.models.bo.ModelsBillBo">
		<result column="SUB_BE" property="subBe" />
		<result column="BILLCONF_FLAG" property="billconfFlag" />
		<result column="PROD_NO" property="prodNo" />
		<result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
	</resultMap>



	<select id="getModelRentalList" resultMap="BaseResultMap"
		parameterType="com.ls.ner.billing.models.bo.ModelsBillBo">
		SELECT
		DISTINCT
		e.ORG_CODE,e.SUB_BE,e.BIL<PERSON>ONF_FLAG,e.PROD_NO,e.AUTO_MODEL_NO modelId
		FROM
		e_auto_model_rental e
		<where>
			<if test="subBe !=null and subBe !=''">
				and e.sub_be = #{subBe}
			</if>
			<if test="subBeFlag !=null and subBeFlag !=''">
				and e.sub_be !='' and e.sub_be is not null
			</if>
			<if test="billconfFlag !=null and billconfFlag !=''">
				and e.BILLCONF_FLAG = #{billconfFlag}
			</if>
			<if test="orgCode !=null and orgCode != ''">
				and e.ORG_CODE = #{orgCode}
			</if>
		</where>
		<if test="end!=null and end!=0">
				limit #{begin} ,#{end}
		</if>
	</select>

	<select id="getModelRentalListNum" resultType="int"
		parameterType="com.ls.ner.billing.models.bo.ModelsBillBo">
		select count(1) from e_auto_model_rental e
		<where>
			<if test="subBe !=null and subBe !=''">
				and e.sub_be = #{subBe}
			</if>
			<if test="subBeFlag !=null and subBeFlag !=''">
				and e.sub_be !='' and e.sub_be is not null
			</if>
			<if test="billconfFlag !=null and billconfFlag !=''">
				and e.BILLCONF_FLAG = #{billconfFlag}
			</if>
			<if test="orgCode !=null and orgCode != ''">
				and e.ORG_CODE = #{orgCode}
			</if>
		</where>
	</select>

	<!-- 车型租赁方式设置保存 -->
	<insert id="saveModelRental" parameterType="com.ls.ner.billing.models.bo.ModelsBillBo">
		insert into
		e_auto_model_rental(
		PROD_NO,ORG_CODE,AUTO_MODEL_NO,SUB_BE,BILLCONF_FLAG,DATA_OPER_TIME,DATA_OPER_TYPE
		)
		values(#{prodNo},#{orgCode},#{modelId},#{subBe},'0',now(),'I')
	</insert>
	<!-- 删除车型租赁方式 -->
	<delete id="delModelRental" parameterType="string">
		delete from e_auto_model_rental where PROD_NO=#{prodNo}
	</delete>
</mapper>