package com.ls.ner.billing.common.bo;

import java.util.Date;

import com.ls.ner.billing.api.BillingConstants.BuyType;
import com.ls.ner.billing.api.BillingConstants.ItemStatus;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.pt.poseidon.api.framework.DicAttribute;

public class AppendChargeItemBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.ITEM_NO
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private String itemNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.ITEM_NAME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private String itemName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.ITEM_UNIT
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private String itemUnit;
    
    @DicAttribute(dicName="codeDict",key="itemUnit",subType=PriceUnit.CODE_TYPE)
    private String itemUnitName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.ITEM_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private String itemType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_append_charge_item.SN
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    private Integer sn;
    
    private String buyType;
    @DicAttribute(dicName="codeDict",key="buyType",subType=BuyType.CODE_TYPE)
    private String buyTypeName;
    
    private String itemStatus;
    @DicAttribute(dicName="codeDict",key="itemStatus",subType=ItemStatus.CODE_TYPE)
    private String itemStatusName;
    private String remarks;
    private String pBe;
    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.SYSTEM_ID
     *
     * @return the value of e_append_charge_item.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.SYSTEM_ID
     *
     * @param systemId the value for e_append_charge_item.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.DATA_OPER_TIME
     *
     * @return the value of e_append_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_append_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.DATA_OPER_TYPE
     *
     * @return the value of e_append_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_append_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.ITEM_NO
     *
     * @return the value of e_append_charge_item.ITEM_NO
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.ITEM_NO
     *
     * @param itemNo the value for e_append_charge_item.ITEM_NO
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.ITEM_NAME
     *
     * @return the value of e_append_charge_item.ITEM_NAME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.ITEM_NAME
     *
     * @param itemName the value for e_append_charge_item.ITEM_NAME
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.ITEM_UNIT
     *
     * @return the value of e_append_charge_item.ITEM_UNIT
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public String getItemUnit() {
        return itemUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.ITEM_UNIT
     *
     * @param itemUnit the value for e_append_charge_item.ITEM_UNIT
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit == null ? null : itemUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.ITEM_TYPE
     *
     * @return the value of e_append_charge_item.ITEM_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public String getItemType() {
        return itemType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.ITEM_TYPE
     *
     * @param itemType the value for e_append_charge_item.ITEM_TYPE
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setItemType(String itemType) {
        this.itemType = itemType == null ? null : itemType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_append_charge_item.SN
     *
     * @return the value of e_append_charge_item.SN
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public Integer getSn() {
        return sn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_append_charge_item.SN
     *
     * @param sn the value for e_append_charge_item.SN
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    public void setSn(Integer sn) {
        this.sn = sn;
    }

	public String getItemUnitName() {
		return itemUnitName;
	}

	public void setItemUnitName(String itemUnitName) {
		this.itemUnitName = itemUnitName;
	}

	public String getBuyType() {
		return buyType;
	}

	public void setBuyType(String buyType) {
		this.buyType = buyType;
	}

	public String getBuyTypeName() {
		return buyTypeName;
	}

	public void setBuyTypeName(String buyTypeName) {
		this.buyTypeName = buyTypeName;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getItemStatus() {
		return itemStatus;
	}

	public void setItemStatus(String itemStatus) {
		this.itemStatus = itemStatus;
	}

	public String getItemStatusName() {
		return itemStatusName;
	}

	public void setItemStatusName(String itemStatusName) {
		this.itemStatusName = itemStatusName;
	}

	public String getpBe() {
		return pBe;
	}

	public void setpBe(String pBe) {
		this.pBe = pBe;
	}
    
    
}