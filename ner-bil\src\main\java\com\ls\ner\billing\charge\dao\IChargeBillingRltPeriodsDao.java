package com.ls.ner.billing.charge.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @dateTime 2016-03-28
 * @description 充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
 */
public interface IChargeBillingRltPeriodsDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 查询充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS by appNo
	 */
	public List<ChargeBillingRltPeriodsBo> queryChargeBillingRltPeriod(String appNo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 新增充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	 */
	public void insertChargeBillingRltPeriods(ChargeBillingRltPeriodsBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-28
	 * @description 更新充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	 */
	public void updateChargeBillingRltPeriods(ChargeBillingRltPeriodsBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-30
	 * @description 删除充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	 */
	public void deleteChargeBillingRltPeriod(String appNo);

	/**
	  *@Description: 查询结算分段计费明细
	  *@Author: qianghuang
	  *@Time: 2017/8/18 16:14
	  */
	List<BillRltPeriodsBo> getChargeBillRltPeriod(Map map);


	List<BillRltPeriodsBo> getChargeBillRltPeriodByOrder(Map map);

	/**
	 * @param map
	 * @description 根据订单号查询计费模板，根据billCtlMode判断是否分时
	 * <AUTHOR>
	 * @create 2018-11-01 11:24:01
	 */
	List<Map> getChargeBillRltModel(Map map);

	/**
	 * 查询服务费
	 * @param periodMap
	 * @return
	 */
	List<ChargeBillingRltPeriodsBo> queryBillingRltPeriod(Map periodMap);
}
