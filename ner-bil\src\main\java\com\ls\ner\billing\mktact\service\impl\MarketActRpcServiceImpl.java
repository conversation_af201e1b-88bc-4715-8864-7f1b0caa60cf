package com.ls.ner.billing.mktact.service.impl;

import com.ls.ner.ast.api.station.service.IStationRpcService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.PresentCycleType;
import com.ls.ner.billing.api.charge.service.IChargeBillRpcService;
import com.ls.ner.billing.api.mktact.service.IMarketActRpcService;
import com.ls.ner.billing.market.bo.BillBo;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.IntegeralCustBo;
import com.ls.ner.billing.market.bo.IntegeralCustLogBo;
import com.ls.ner.billing.market.dao.ICouponRPCDao;
import com.ls.ner.billing.market.dao.IIntegeralCustRPCDao;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.bo.MarketTypeBo;
import com.ls.ner.billing.mktact.dao.IActCondDao;
import com.ls.ner.billing.mktact.dao.IMarketActDao;
import com.ls.ner.billing.mktact.dao.IMarketActRpcDao;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.ls.ner.cust.api.service.ICustCenterRpcService;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.constants.Constants;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre><b><font color="blue">MarketActRpcServiceImpl</font></b></pre>
 *
 * <pre><b>&nbsp;--营销活动RPC--</b></pre>
 * <pre></pre>
 * <pre>
 * <b>--样例--</b>
 *
 * </pre>
 * JDK版本：JDK1.7
 *
 * <AUTHOR>
 */
@Service(target = {ServiceType.RPC}, value = "marketActRpcService")
public class MarketActRpcServiceImpl implements IMarketActRpcService {
    private static final Logger logger = LoggerFactory.getLogger(MarketActRpcServiceImpl.class);

    @Autowired
    private IMarketActRpcDao dao;

    @Autowired
    private ICouponRPCDao couponRPCDao;

    @Autowired
    private IActCondDao actCondDao;

    @Autowired(required=true)
    private IMarketActDao marketActDao;

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    @ServiceAutowired("preSectionService")
    private IPreSectionService preSectionService;

    @ServiceAutowired("couponService")
    private ICouponService couponService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC, value="chargeBillRpcService")
    private IChargeBillRpcService chargeBillRpcService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC, value="stationRpcService")
    private IStationRpcService stationRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "custCenterRpcService")
    private ICustCenterRpcService custCenterRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "attachRpcService")
    private IAttachRpcService attachService;

    @Autowired
    private IIntegeralCustRPCDao integeralCustRPCDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "seqRpcService")
    private ISeqRpcService seqRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
    private IDefrayAccountRpcService defrayAccountRpcService;

    /**
     * 查询‘预存赠送’活动
     *
     * @param map {
     *            custType用户分类
     *            custLvl用户等级
     *            channel渠道:01APP 02现场03微信04支付宝
     *            }
     * @return actList{
     * actId活动ID,actLbl活动标签,
     * actName活动名称,actMarks活动描述,
     * effTime生效时间,expTime失效时间,
     * presentRuleList[
     * presentBalanceType赠送余额类型,
     * presentRuleDesc赠送规则描述
     * ]}
     * <AUTHOR>
     */
    public List<Map<String, Object>> qryMarketActives(Map<String, Object> map) {
        logger.debug(">>>>>IMarketActRpcService.qryMarketActives:{}", map);
        List<Map<String, Object>> retlistmap = new ArrayList<Map<String, Object>>();

        StringUtil.checkParam(map.get("channel"), "渠道不能为空");
        retlistmap = marketActService.qryMarketActivesMap(map);
        for (Map<String, Object> actmap : retlistmap) {
            List<Map<String, Object>> sectionList = preSectionService.qryPreSectionsMap(actmap);
            actmap.put("presentRuleList", sectionList);
        }
        //retmap.put("actList", actList);
        return retlistmap;
    }

    @Override
    public void aysncCustIntegral(Map<String, Object> map) {
        String integralNumber = StringUtil.nullToString(map.get("integralNum"));
        try {
            String custId = StringUtil.nullToString(map.get("custId"));
            String mobile = StringUtil.nullToString(map.get("mobile"));
            if (StringUtil.isEmpty(mobile)) {
                Map<String, Object> objectMap = defrayAccountRpcService.qryAccMapByCustId(custId);
                mobile = StringUtil.nullToString(objectMap.get("mobile"));
            }

            //赠送积分
            IntegeralCustBo bo = new IntegeralCustBo();
            IntegeralCustLogBo logBo = new IntegeralCustLogBo();
            bo.setCustNo(custId);
            int count = integeralCustRPCDao.ixExistsCustNum(bo);
            //记录积分流水
            if (count > 0) {
                //用户已有积分,更新用户积分
                //获取用户积分
                String num = integeralCustRPCDao.getCustIntegeralNum(bo);
                bo.setIntegralNumber(MathUtils.add(integralNumber, num));
                logBo.setIntegralNumber(MathUtils.add(integralNumber, num));
                integeralCustRPCDao.updateIntegeralCust(bo);
            } else {
                //添加用户积分
                bo.setIntegralNumber(integralNumber);
                logBo.setIntegralNumber(integralNumber);
                integeralCustRPCDao.addIntegeralCust(bo);
            }
            logBo.setIntegralNo(seqRpcService.getAppNo());//积分流水号
            logBo.setPhone(mobile);
            logBo.setChargeNum(integralNumber);
            logBo.setEventName(StringUtil.nullToString(map.get("eventName")));
            logBo.setEventType(StringUtil.nullToString(map.get("eventType")));
            logBo.setChargeType("01");
            integeralCustRPCDao.addIntegeralCustLog(logBo);

            logger.info("预存赠送积分成功，用户ID：{}，赠送积分：{}", custId, integralNumber);
        } catch (Exception e) {
            logger.error("预存赠送积分失败，用户ID：{}，赠送积分：{}，错误：{}",
                    StringUtil.nullToString(map.get("custId")), integralNumber, e.getMessage(), e);
        }
    }

    /**
     * 执行‘预存赠送’活动
     *
     * @param map {
     *            actId活动ID
     *            custType用户分类
     *            custLvl用户等级
     *            channel渠道:01APP 02现场03微信04支付宝
     *            payBalanceValue预存金额
     *            payBalanceTime预存时间
     *            }
     * @return
     * <AUTHOR>
     */
    public Map<String, Object> doPresentSection(Map<String, Object> map) {
        logger.info(">>>>>IMarketActRpcService.doPresentSection:{}", JsonUtil.obj2Json(map));
        Map<String, Object> retmap = new HashMap<String, Object>();

        //StringUtil.checkParam(map.get("actId"), "活动ID不能为空。");
        StringUtil.checkParam(map.get("channel"), "渠道不能为空。");
        StringUtil.checkParam(map.get("payBalanceValue"), "预存金额不能为空。");
        StringUtil.checkParam(map.get("payBalanceTime"), "预存时间不能为空。");
        List<Map<String, Object>> actlist = marketActService.qryMarketActivesMap(map);
        if (actlist != null && actlist.size() > 0) {
            retmap.putAll(actlist.get(0));

            map.put("presentBalType",actlist.get(0).get("actSubType"));
            map.put("actId", actlist.get(0).get("actId"));
            Map<String, Object> sectionmap = preSectionService.qryPreSectionValue(map);
            logger.info(">>>>>IMarketActRpcService.sectionmap:{}", JsonUtil.obj2Json(sectionmap));
            if (sectionmap == null) {
                return null;
            }

            // 赠送积分逻辑 - 无论优惠券还是馈赠金，只要配置了积分就赠送
            Long integralNum = 0L;
            if (sectionmap.get("integralNum") != null) {
                integralNum = ((BigDecimal) sectionmap.get("integralNum")).longValue();
            }
            if (integralNum > 0) {
                map.put("integralNum", integralNum);
                map.put("eventName", "预充送积分");
                map.put("eventType", "06");
                aysncCustIntegral(map);
            }
            if(BillConstants.ActSubType.ACT_SUB_TYPE_0702.equals(actlist.get(0).get("actSubType"))){//优惠券
                logger.debug("==========段落明细：" + sectionmap);
                String maxValue = (StringUtil.isNotEmpty(sectionmap.get("presentMaxValue"))?sectionmap.get("presentMaxValue"):"-1").toString();
                if(!"-1".equals(maxValue)){//-1为无上限
                    int joinActSum = dao.queryJoinActCount(map);
                    int maxNum = Integer.valueOf(maxValue);
                    if(joinActSum > maxNum){
                        logger.debug("参与该活动次数达到上限，不可再参与!");
                        return null;
                    }
                }
                int baseValue = 0;
                if(StringUtil.isNotEmpty(sectionmap.get("baseValue"))){
                    baseValue = Integer.valueOf(sectionmap.get("baseValue").toString());
                }else{
                    logger.debug("优惠券赠送数量为空，当做赠送次数为0处理，即为不赠送!");
                    return null;
                }
                String[] arrCpnId = StringUtil.getString(sectionmap.get("cpnId")).split(",");
                List<String> cpnIdList = Arrays.asList(arrCpnId);
                MarketCondition condition = new MarketCondition();
                condition.setEnd(0); // 不做分页
                condition.setCpnIdList(cpnIdList);
                condition.setCpnStatus("1"); // 在用
                List<CouponBo> couponList = couponService.getCoupons(condition);
                if (couponList != null && couponList.size() > 0) {
                    List<Map<String, String>> retList = new ArrayList<Map<String, String>>();
                    for (CouponBo c : couponList) {
                        // 此优惠券不能再发放
                        String cpnOverFlag = Constants.IsFlag.NO;
                        String putNum = StringUtil.isNotBlank(c.getPutNum()) ? c.getPutNum():"0";// 已发放数量
                        String cpnNum = StringUtil.isNotBlank(c.getCpnNum()) ? c.getCpnNum():"0";// 发行数量
                        String alreadyGetNum = StringUtil.isNotBlank(c.getAlrdyGetNum()) ?c.getAlrdyGetNum():"0";// 已领取数量
                        if(Integer.valueOf(putNum) + Integer.valueOf(alreadyGetNum)  >= Integer.valueOf(cpnNum)){
                            cpnOverFlag = Constants.IsFlag.YES;
                        }
                        for (int a = 0;a < baseValue;a++){
                            Map<String, String> childMap = new HashMap<String, String>();
                            childMap.put("cpnId", c.getCpnId());
                            childMap.put("prodBusiType", c.getBusiType());
                            childMap.put("limGetNum", c.getLimGetNum());
                            childMap.put("eftDate", c.getEftDate());
                            childMap.put("invDate", c.getInvDate());
                            childMap.put("putTime", StringUtil.isNotEmpty(actlist.get(0).get("putTime"))?
                                    StringUtil.getString(actlist.get(0).get("putTime")):
                                    DateTools.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
                            childMap.put("timeDuration", c.getTimeDuration());
                            childMap.put("timeUnit", c.getTimeUnit());
                            childMap.put("cpnOverFlag", cpnOverFlag);
                            retList.add(childMap);
                        }
                    }
                    retmap.put("cpnList",retList);
                    retmap.put("integralNum", String.valueOf(integralNum));
                }else{
                    return null;
                }
                        }else{//馈赠金
                String presentCycleType = (String) sectionmap.get("presentCycleType");
                Long maxValue = ((BigDecimal) sectionmap.get("presentMaxValue")).longValue();
                Long baseValue = ((BigDecimal) sectionmap.get("baseValue")).longValue();
                if (baseValue > maxValue) {
                    retmap.put("presentValue", maxValue.toString());
                } else {
                    retmap.put("presentValue", baseValue.toString());
                }
                retmap.put("integralNum", String.valueOf(integralNum));
                retmap.put("presentCycleType", presentCycleType);

                if (PresentCycleType.ONE.equals(presentCycleType)) {
                    long timeLimitId = (long) sectionmap.get("timeLimitId");
                    Map<String, Object> timeLimitMap = preSectionService.qryTimeLimitById(timeLimitId);
                    retmap.putAll(timeLimitMap);
                } else if (PresentCycleType.CYCLE.equals(presentCycleType)) {
                    long cycleTypeId = (long) sectionmap.get("cycleTypeId");
                    Map<String, Object> cycleMap = preSectionService.qryCycleById(cycleTypeId);
                    retmap.putAll(cycleMap);
                    retmap.put("cycleUpper", sectionmap.get("cycleUpper"));
                    retmap.put("cycleLower", sectionmap.get("cycleLower"));
                }
            }
            logger.debug(">>>>>IMarketActRpcService.doPresentSection.out:{}",JsonUtil.obj2Json(retmap));
            return retmap;
        }
        return null;
    }

    /**
     * 描述 RPC05-11-05订单可参加的活动（还车后）
     * 针对活动为：02	限时打折；03	满减满送；04	首单免
     *
     * @param: [map]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/10 3:15
     */
    @Override
    public Map<String, Object> actByOrder(Map<String, Object> map) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        if (map != null && map.size() > 0) {
            String orderNo = String.valueOf(map.get("orderNo"));
            if (StringUtil.isNotBlank(orderNo)) {
                List<Map> actInfoList = dao.queryActAll();
                if (actInfoList != null && actInfoList.size() > 0) {
                    for (int i = 0; i < actInfoList.size(); i++) {
                        String actCondFlag = actInfoList.get(0).get("actCondFlag").toString();
                        String dctCondType = actInfoList.get(0).get("dctCondType").toString();
                        String actValue = ""; // 活动优惠条件数值
                        // 1无条件 2条件
                        switch (actCondFlag) {
                            case "1":
                                break;
                            default:
                                // 00整单金额; 01计价数量; 02计价金额
                                switch (dctCondType) {
                                    case "00":
                                        break;
                                    case "01":
                                        break;
                                    case "02":
                                        break;
                                    default:
                                        // 限时打折
                                        break;
                                }
                                break;
                        }
                    }
                } else {
                    throw new RuntimeException("无可用活动优惠");
                }
            } else {
                throw new RuntimeException("接口入参[orderNo]不能为空");
            }
        } else {
            throw new RuntimeException("接口入参错误");
        }
        return retMap;
    }

    /**
     * @param inMap
     * @Description: 3.5.5    RPC05-11-05订单可参加的活动（还车后）
     * @method: getOrderAvailAct
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * @Author: cailianL
     * @Time: 2017/7/12 11:36
     */
    @Override
    public Map<String, Object> getOrderAvailAct(Map<String, Object> inMap) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String orderAmt = "";
        String orderNo = inMap.get("orderNo").toString();
        List<BillBo> billBoList = couponRPCDao.queryBillInfo(orderNo);
        String custId = "";
        if (!CollectionUtils.isEmpty(billBoList)) {
            orderAmt = billBoList.get(0).getBillAmt();
            custId = billBoList.get(0).getCustId();
        } else {
            retMap.put("ret", "400");
            retMap.put("msg", "订单计费不存在");
            return retMap;
        }
        Map<String, Object> iMap = new HashMap<>();
        iMap.put("custId", custId);
        iMap.put("orderState", BillConstants.OrderState.ORDER_STATE_1);
        //查询客户参加的订单数量
        int orderNum = couponRPCDao.queryBillNum(iMap);
        List<Map<String, Object>> actsList = new ArrayList<>();
        //查询满足条件可参加的活动
        List<Map> actList = dao.queryActAll();
        if(!CollectionUtils.isEmpty(actList)){
            for (Map map : actList) {
                Map<String, Object> actMap = new HashMap<>();
                //判断是不是ACT_TYPE=04首单免活动
                if (BillConstants.ActType.ACT_TYPE_04.equals(map.get("actType"))) {
                    if (orderNum > 1) {//表示不是首单，直接跳过
                        continue;
                    }
                }
                String leftValue = "";
                if (BillConstants.DctCondFlag.TWO.equals(map.get("dctCondFlag"))//优惠整单金额
                        && BillConstants.DctCondType.ALL_ORDER_AMT.equals(map.get("dctCondType"))) {
                    leftValue = orderAmt;
                } else if (BillConstants.DctCondFlag.TWO.equals(map.get("dctCondFlag"))//计价数量
                        && BillConstants.DctCondType.PROD_NUM.equals(map.get("dctCondType"))) {
                    leftValue = dao.prodNumSum(map);
                } else if (BillConstants.DctCondFlag.TWO.equals(map.get("dctCondFlag"))//计价金额
                        && BillConstants.DctCondType.PROD_AMT.equals(map.get("dctCondType"))) {
                    leftValue = dao.prodAmtSum(map);
                } else if (BillConstants.DctCondFlag.ONE.equals(map.get("dctCondFlag"))) {//无要求
                    leftValue = null;
                }
                List<MarketTypeBo> actCondDetList = new ArrayList<>();
                List<MarketTypeBo> actDctList = new ArrayList<>();
                MarketTypeBo bo = new MarketTypeBo();
                bo.setActCondId(StringUtil.getString(map.get("actCondId")));
                if (leftValue != null) {
                    bo.setDctCondValue(leftValue);
                    actCondDetList = actCondDao.queryMktActCondDet(bo);
                } else {
                    bo.setDctCondValue("null");
                    actCondDetList = actCondDao.queryMktActCondDet(bo);
                }
                String actSpAmt = "";
                if (!CollectionUtils.isEmpty(actCondDetList)) {
                    bo.setActCondId(StringUtil.getString(actCondDetList.get(0).getActCondId()));
                    actDctList = actCondDao.queryActDctAll(bo);
                    if (!CollectionUtils.isEmpty(actDctList)) {
                        if (BillConstants.DctType.ALL_ORDER_AMT.equals(actDctList.get(0).getDctType())) {//整单金额
                            if ("是".equals(actDctList.get(0).getAllFree())) {
                                actSpAmt = orderAmt;
                            } else {
                                if (MathUtils.compareTo(actDctList.get(0).getDctValue(), orderAmt) == 1) {
                                    actSpAmt = orderAmt;
                                } else {
                                    actSpAmt = actDctList.get(0).getDctValue();
                                }
                            }
                        } else if (BillConstants.DctType.PROD_AMT.equals(actDctList.get(0).getDctType())) {//计价金额
                            if ("是".equals(actDctList.get(0).getAllFree())) {
                                actSpAmt = leftValue;
                            } else {
                                if (MathUtils.compareTo(actDctList.get(0).getDctValue(), leftValue) == 1) {
                                    actSpAmt = leftValue;
                                } else {
                                    actSpAmt = actDctList.get(0).getDctValue();
                                }
                            }
                        }
                    }
                    actMap.put("actId", map.get("actId"));
                    actMap.put("actType", map.get("actType"));
                    actMap.put("actName", map.get("actName"));
                    actMap.put("actMarks", map.get("actMarks"));
                    actMap.put("actSpAmt", actSpAmt);
                    actsList.add(actMap);
                }
            }
            retMap.put("acts", actsList);
        }
        return retMap;
    }

    /**
     * @param map 暂无
     * @return actList{
     * dctGiveType 赠送类别 ,
     * cpns 赠送的优惠券列表[
     * cpnId 优惠券ID,
     * prodBusiType 产品业务类，
     * limGetNum 每人限领，
     * eftDate 生效日期，
     * invDate 失效日期，
     * putTime 发放时间
     * ]}
     * @description RPC05-12-03 获取注册送活动
     * @author: biaoxiangd
     * @create:2017-09-27
     */
    public Map<String, Object> getRegisterSendAct(Map<String, Object> map) throws Exception {
        logger.debug(">>>>>查询注册送/邀请送活动  getRegisterSendAct  map:{}",map);
        Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String, Object> inMap = new HashMap<String, Object>();
        inMap.put("actState", "2"); // 活动进行中
        inMap.put("actType", map.get("actType")); // 活动类型
        inMap.put("dctGiveType", "04"); // 赠送类别=优惠券
        inMap.put("prodBusiType", map.get("prodBusiType")); // 产品业务类，租车、充电等
        List<Map> actList = dao.getRegisterSendAct(inMap);
        if (actList != null && actList.size() > 0) {
            for (Map<String, Object> objMap : actList) {
                String strCpnId = String.valueOf(objMap.get("cpnId"));
                String dctGiveType = String.valueOf(objMap.get("dctGiveType"));
                String putTime = String.valueOf(objMap.get("putTime"));
                if (StringUtil.isNotBlank(dctGiveType) && StringUtil.isNotBlank(strCpnId)) {
                    Map<String,Object> m = new HashMap<String,Object>();
//                    retMap.put("dctGiveType", dctGiveType);
                    String[] arrCpnId = strCpnId.split(",");
                    if (arrCpnId != null && arrCpnId.length > 0) {
                        List<String> cpnIdList = Arrays.asList(arrCpnId);
                        MarketCondition condition = new MarketCondition();
                        condition.setEnd(0); // 不做分页
                        condition.setCpnIdList(cpnIdList);
                        condition.setCpnStatus("1"); // 在用
                        List<CouponBo> couponList = couponService.getCoupons(condition);
                        if (couponList != null && couponList.size() > 0) {
                            List<Map<String, String>> retList = new ArrayList<Map<String, String>>();
                            for (CouponBo c : couponList) {
                                Map<String, String> childMap = new HashMap<String, String>();
                                childMap.put("cpnId", c.getCpnId());
                                childMap.put("prodBusiType", c.getBusiType());
                                childMap.put("limGetNum", c.getLimGetNum());
                                childMap.put("eftDate", c.getEftDate());
                                childMap.put("invDate", c.getInvDate());
                                childMap.put("putTime", putTime);
                                childMap.put("timeDuration", c.getTimeDuration());
                                childMap.put("timeUnit", c.getTimeUnit());
                                retList.add(childMap);
                            }
                            m.put("cpns", retList);
                        } else {
                            logger.error("[RPC05-12-03 获取注册送活动]RPC接口未查询到注册送优惠券信息");
                        }
                    } else {
                        logger.error("[RPC05-12-03 获取注册送活动]RPC接口未查询到注册送优惠券活动信息");
                    }
                    retMap.put(dctGiveType,m);

                } else {
                    logger.error("[RPC05-12-03 获取注册送活动]RPC接口未查询到注册送活动对应的优惠券信息");
                }
            }
        } else {
            logger.error("[RPC05-12-03 获取注册送活动]RPC接口未查询到注册送活动信息");
        }
        return retMap;
    }

    /**
     * @param map
     * @description 获取邀请送活动
     * <AUTHOR>
     * @create 2018-05-16 09:57:09
     */
    @Override
    public List<Map<String, Object>> getActiveActInfo(Map<String, Object> map) throws Exception {
        logger.debug("==============获取所有进行中的活动入参:" + JsonUtil.obj2Json(map));
        String pubPath = PublicConstants.ApplicationPath.getPubPath();
        List<Map<String,Object>> actList = dao.getActiveActInfo(map);
        if(CollectionUtils.isNotEmpty(actList)){
            for (Map<String, Object> actMap : actList) {
                if("1".equals(StringUtil.getString(actMap.get("infoType")))){//图文
                    actMap.put("actUrl",pubPath + "/api/v0.1/attachs/" + actMap.get("contentUrl"));
                }else{//外部链接
                    actMap.put("actUrl",actMap.get("linkUrl"));
                }
            }
        }
        logger.debug("==============获取所有进行中的活动出参:" + JsonUtil.obj2Json(actList));
        return actList;
    }

    /**
     * @param map
     * @description 记录参与活动（预存赠送记录参与活动次数）
     * <AUTHOR>
     * @create 2018-07-05 15:32:29
     */
    @Override
    public void joinActRecord(Map<String, Object> map) {
        dao.joinActRecord(map);
    }

    /**
     * @param inMap
     * @description 满减计算，减免金额
     * <AUTHOR>
     * @create 2018-09-25 10:40:45
     */
    @Override
    public Map<String, Object> fullSaleCalc(Map<String, Object> inMap) {
        //查询订单是否是企业计费
        boolean isCustBill =  chargeBillRpcService.qryOrderBillConfFlag(inMap);
        if(isCustBill){
            inMap.put("pBillFlag","true");
        }

        Map<String, Object> retMap = new HashMap<>();
        String actId ="";
        try {
            Map<String,Object> custMap =  new HashMap<>();
            //查询支付账户是属于哪个类型
            custMap.put("custId",inMap.get("payCustId"));
            Map<String,Object> custReMap = custCenterRpcService.getCustInfo(custMap);
            //查询是否存在限时折扣的活动  zhangdan
            logger.info("查询该站点是否有活动入参------------------------"+inMap);
            Map<String,Object> xsaAtMap = new HashMap<>();
            xsaAtMap.put("stationId",inMap.get("stationId"));
            xsaAtMap.put("actType","02");
            if (custReMap.containsKey("custType")){
                xsaAtMap.put("custType",custReMap.get("custType"));
            }
            List<Map> xsActList = queryChargingStationsAct(xsaAtMap);
            logger.info("站点详情查看是否有活动结果---------------"+xsActList);
            //充电费
            String elecAmt = StringUtil.trimToEmpty(inMap.get("elecAmt"));
            //服务费
            String serviceAmt = StringUtil.trimToEmpty(inMap.get("serviceAmt"));
            if (CollectionUtils.isNotEmpty(xsActList)){
                Map map = xsActList.get(0);
                if ("02".equals(StringUtil.trimToEmpty(map.get("actType"))) && StringUtil.isNotBlank(elecAmt) && StringUtil.isNotBlank(serviceAmt)){
                    logger.info("elecAmt============"+elecAmt);
                    logger.info("serviceAmt============"+serviceAmt);
                    BigDecimal elecAmtBigDecimal = new BigDecimal(elecAmt);
                    BigDecimal serviceAmtBigDecimal = new BigDecimal(serviceAmt);
                    logger.info("elecAmtBigDecimal============"+elecAmtBigDecimal);
                    logger.info("serviceAmtDouble============"+serviceAmtBigDecimal);
                    //活动价
                    BigDecimal actElecAmt = new BigDecimal(0);
                    BigDecimal actServiceAmt = new BigDecimal(0);
                    Map<String,Object> dctMap = new HashMap<>();
                    actId = StringUtil.trimToEmpty(map.get("actId"));
                    dctMap.put("actId",StringUtil.trimToEmpty(map.get("actId")));
                    List<Map> dctList = qryActContDct(dctMap);
                    logger.info("优惠内容------"+dctList);
                    if (CollectionUtils.isNotEmpty(dctList)){
                        for (Map dct:dctList){
                            Double contValue = 0.00;
                            String dctValue = String.valueOf(dct.get("dctValue"));
                            logger.info("dctValue------------"+dctValue);
                            contValue = (Double.valueOf(dctValue)) * 0.1;
                            logger.info("contValue------------"+contValue);
                            //电费
                            if (BizConstants.PriceCode.CHARGE_PRICE_CODE.equals(String.valueOf(dct.get("prodId")))){
                                actElecAmt = elecAmtBigDecimal.multiply(new BigDecimal(contValue));
                            }
                            //服务费
                            if ("1000001848".equals(String.valueOf(dct.get("prodId")))){
                                actServiceAmt = serviceAmtBigDecimal.multiply(new BigDecimal(contValue));
                            }
                        }
                    }else {
                        logger.debug("限时折扣没有优惠内容-----------"+StringUtil.trimToEmpty(map.get("actId")));
                        actElecAmt = elecAmtBigDecimal;
                        actServiceAmt = serviceAmtBigDecimal;
                    }

                    if (actElecAmt.compareTo(new BigDecimal(0))==0){
                        actElecAmt = elecAmtBigDecimal;
                    }
                    if (actServiceAmt.compareTo(new BigDecimal(0))==0){
                        actServiceAmt = serviceAmtBigDecimal;
                    }
                    DecimalFormat df = new DecimalFormat("#.00");
                    String actElecAmtStr = df.format(actElecAmt);
                    String actServiceAmtStr = df.format(actServiceAmt);

                    logger.info("活动电费---------"+actElecAmtStr);
                    logger.info("活动服务费---------"+actServiceAmtStr);
                    retMap.put("actElecAmt", actElecAmtStr);
                    retMap.put("actServiceAmt", actServiceAmtStr);
                    String spAmt = df.format(elecAmtBigDecimal.add(serviceAmtBigDecimal).subtract(actElecAmt).subtract(actServiceAmt));
                    retMap.put("spAmt", spAmt);
                }
            }else{
                //查询是否存在满减的活动
                inMap.put("actType", BillConstants.ActType.ACT_TYPE_03);
                inMap.put("dctCondValue", inMap.get("settleTBal"));
                List<Map<String,Object>> actList = dao.queryFullSaleInfo(inMap);
                logger.debug("----满减活动:"+actList);

                //所有折扣价格排序
                List<Map<String,String>> actDiscountList = new ArrayList();

                if (CollectionUtils.isNotEmpty(actList)){
                    for(Map<String,Object> actMap:actList){
                        // 判断是否共享企业计费
                        // 判断是否否和站点
                        String method = MapUtils.getValue(actMap,"dctCalcMethod");
                        String settleTBal =  MapUtils.getValue(inMap,"settleTBal");
                        String dctValue =  MapUtils.getValue(actMap,"dctValue");
                        //活动类型
                        String dctType = MapUtils.getValue(actMap,"dctType");
                        String allFree = MapUtils.getValue(actMap,"allFree");
                        DecimalFormat df = new DecimalFormat("##.##");
                        String disActId = StringUtil.trimToEmpty(StringUtil.trimToEmpty(actMap.get("actId")));
                        Map actDiscountMap = new HashedMap();
                        actDiscountMap.put("actId",disActId);
                        if("0".equals(actMap.get("allStationFlag"))){//部分站点
                            //查询关联站点
                            inMap.put("actId",actMap.get("actId"));
                            StringUtil.trimToEmpty(actMap.get("actId"));
                            int count = dao.qryActStationCount(inMap);
                            if(count>0){
                                if (BillConstants.DctType.ALL_ORDER_SER_AMT.equals(dctType)){
                                    processServieActDiscount(retMap, serviceAmt, method, dctValue, df,"1".equals(allFree));
                                }else{
                                    if ("02".equals(method)){
                                        retMap.put("spAmt", MathUtils.multiply(settleTBal,MathUtils.subtract("1",MathUtils.divide(dctValue,"10"))));
                                    }else{
                                        if ("1".equals(allFree)){
                                            //全免
                                            retMap.put("spAmt",settleTBal);
                                        }else{
                                            retMap.put("spAmt", dctValue);
                                        }
                                    }
                                }
                                actDiscountMap.put("spAmt",retMap.get("spAmt"));
                                actDiscountList.add(actDiscountMap);
                            }
                        } else{
                            if (BillConstants.DctType.ALL_ORDER_SER_AMT.equals(dctType)){
                                processServieActDiscount(retMap, serviceAmt, method, dctValue, df,"1".equals(allFree));
                            }else{
                                if ("02".equals(method)){
                                    retMap.put("spAmt", MathUtils.multiply(settleTBal,MathUtils.subtract("1",MathUtils.divide(dctValue,"10"))));
                                }else{
                                    if ("1".equals(allFree)){
                                        retMap.put("spAmt",settleTBal);
                                    }else{
                                        retMap.put("spAmt", dctValue);
                                    }
                                }
                            }
                            actDiscountMap.put("spAmt",retMap.get("spAmt"));
                            actDiscountList.add(actDiscountMap);
                        }
                    }
                    //按价格降序
                    Collections.sort(actDiscountList, new Comparator<Map<String, String>>() {
                        @Override
                        public int compare(Map<String, String> o1, Map<String, String> o2) {
                            String sptAmt1 = o1.get("spAmt");
                            String sptAmt2 = o2.get("spAmt");
                            return MathUtils.compareTo(sptAmt2,sptAmt1);
                        }
                    });
                    logger.debug("排序后：{}",actDiscountList);
                    if (actDiscountList != null && actDiscountList.size() > 0){
                        //重写spAmt
                        retMap.put("spAmt", actDiscountList.get(0).get("spAmt"));
                        actId = actDiscountList.get(0).get("actId");
                    }else{
                        return null;
                    }

                }else {
                    return null;
                }
            }

            //参与活动
            Map<String,Object> actJoinMap = new HashMap<>();
            actJoinMap.put("custId",inMap.get("custId"));
            actJoinMap.put("orderNo",inMap.get("orderNo"));
            actJoinMap.put("actId",actId);
            joinActRecord(actJoinMap);
        }catch (Exception e){
            e.printStackTrace();
            retMap.put("spAmt", 0);
            return retMap;
        }


        return retMap;
    }

    /**
     * @param retMap
     * @param serviceAmt
     * @param method
     * @param dctValue
     * @param df
     * @description
     * <AUTHOR>
     * @create 2020-09-21 16:20:47
     */
    private void processServieActDiscount(Map<String, Object> retMap, String serviceAmt, String method, String dctValue, DecimalFormat df, Boolean allFreeFlag) {
        logger.debug("服务费：{},折扣类型：{},折扣值:{}", serviceAmt, method, dctValue);
        //折扣
        BigDecimal serviceAmtBigDecimal = new BigDecimal(serviceAmt);
        if ("02".equals(method)) {
            //服务费
            //折扣
            Double contValue = (Double.valueOf(dctValue)) * 0.1;
            BigDecimal actServiceAmt = serviceAmtBigDecimal.multiply(new BigDecimal(1-contValue));
            String actServiceAmtStr = df.format(actServiceAmt);
//            retMap.put("actServiceAmt", actServiceAmtStr);
            retMap.put("spAmt", actServiceAmtStr);
        } else {
            //减免
            //服务费大于折扣金额
            if (allFreeFlag) {
                retMap.put("spAmt", serviceAmt);
            } else {
                if (serviceAmtBigDecimal.compareTo(new BigDecimal(dctValue)) >= 0) {
                    retMap.put("spAmt", dctValue);
                } else {
                    //服务费小于折扣金额，减免的就是服务费
                    //                retMap.put("actServiceAmt", serviceAmt);
                    retMap.put("spAmt", serviceAmt);
                }
            }
        }


    }

    /**
     * @description 查询活动
     * <AUTHOR>
     * @create 2018-12-17 10:28:07
     */
    public List<Map> qryMktActList(Map<String, Object> inMap) {
        List  mktActList = dao.qryMktActList(inMap);
        return mktActList;
    }

    /**
     * @description 查询充值活动
     * <AUTHOR>
     * @create 2018-12-17 16:53:05
     */
    public List<Map> qryRecMktActList(Map<String, Object> inMap) {
        List  mktActList = dao.qryRecMktActList(inMap);
        return mktActList;
    }

    /**
     * <AUTHOR>
     * @description 查询所有以站点的活动适用范围
     * @create 2019/5/27 19:16
     *
     */
    @Override
    public List<Map> queryChargingStationsAct(Map<String, Object> inMap) throws Exception {
        List<Map> resultList = new ArrayList<>();
        String stationId = "";
        if (inMap.containsKey("stationId")){
            stationId = StringUtil.trimToEmpty(inMap.get("stationId"));
        }
        Map resultMap = qryAllMktActforStation(inMap);
        List<Map> scopeType1 = (List<Map>)resultMap.get("1");
        List<Map> scopeType2 = (List<Map>)resultMap.get("2");
        List<Map> scopeType3 = (List<Map>)resultMap.get("3");
        List<Map> scopeType4 = (List<Map>)resultMap.get("4");
        logger.info("查询后--scopeType1---------"+scopeType1);
        logger.info("查询后--scopeType2---------"+scopeType2);
        logger.info("查询后--scopeType3---------"+scopeType3);
        logger.info("查询后--scopeType4---------"+scopeType4);
        if (StringUtil.isEmpty(stationId)){
            for (Map map3 : scopeType3){
                String stationId3 = StringUtil.trimToEmpty(map3.get("stationId"));
                int c = 0 ;
                if (scopeType4!=null && scopeType4.size()>0){
                    for (Map map4 :scopeType4){
                        if (!stationId3.equals(StringUtil.trimToEmpty(map4.get("stationId")))){
                            c++;
                        }
                    }
                    if (scopeType4.size()==c){
                        scopeType4.add(map3);
                    }
                }else {
                    scopeType4.add(map3);
                }

            }
            for (Map map2 : scopeType2){
                String stationId2 = StringUtil.trimToEmpty(map2.get("stationId"));
                int c = 0;
                if (scopeType4!=null && scopeType4.size()>0){
                    for (Map map4 : scopeType4){
                        if (!stationId2.equals(StringUtil.trimToEmpty(map4.get("stationId")))){
                            c++;
                        }
                    }
                    if (c==scopeType4.size()){
                        scopeType4.add(map2);
                    }
                }else{
                    scopeType4.add(map2);
                }
            }

            logger.info("最后之前--scopeType4---------"+scopeType4);
            resultList.addAll(scopeType4);
            logger.info("最后之前--------------"+resultList);
            if (CollectionUtils.isNotEmpty(scopeType1)){
                resultList.addAll(scopeType1);
            }
        }else {
            if (CollectionUtils.isNotEmpty(scopeType4)){
                resultList = scopeType4;
            }else if (CollectionUtils.isNotEmpty(scopeType3)){
                resultList = scopeType3;
            }else if (CollectionUtils.isNotEmpty(scopeType2)){
                resultList = scopeType2;
            }else {
                resultList = scopeType1;
            }
        }
        logger.info("总的活动resultList-------------"+resultList);
        return resultList;
    }

    /**
     * <AUTHOR>
     * @description 查询所有以站点的活动适用范围
     * @create 2019/5/27 19:16
     *
     */
    @Override
    public Map qryAllMktActforStation(Map<String, Object> inMap) {
        Map resultMap = new HashMap<>();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("scopeType","4");
        String stationId = "";
        String custType = "";
        String actType = "";
        if (inMap.containsKey("stationId")){
            stationId = StringUtil.trimToEmpty(inMap.get("stationId"));
            logger.info("有站点id----------"+stationId);
            paramMap.put("stationId",stationId);
        }
        if (inMap.containsKey("custType")){
            logger.info("custType----------"+StringUtil.trimToEmpty(inMap.get("custType")));
            paramMap.put("custType",StringUtil.trimToEmpty(inMap.get("custType")));
            custType = StringUtil.trimToEmpty(inMap.get("custType"));
        }
        if (inMap.containsKey("actType")){
            logger.info("actType----------"+StringUtil.trimToEmpty(inMap.get("actType")));
            paramMap.put("actType",StringUtil.trimToEmpty(inMap.get("actType")));
            actType = StringUtil.trimToEmpty(inMap.get("actType"));
        }

        logger.info("查询所有活动scopeType=4入参--------------"+paramMap);
        List<Map> scopeType4 = dao.qryMktActByScope(paramMap);
        logger.info("查询所有活动scopeType=4结果--------------"+scopeType4);
        List<Map> scopeType1 = new ArrayList<>();
        List<Map> scopeType2 = new ArrayList<>();
        List<Map> scopeType3 = new ArrayList<>();
        if (inMap.containsKey("stationId")){
            Map<String,Object> paramMap2 = new HashMap<>();
            if (StringUtil.isNotBlank(custType)){
                paramMap2.put("custType",custType);
            }
            if (StringUtil.isNotBlank(actType)){
                paramMap2.put("actType",actType);
            }
            paramMap2.put("scopeType","1");
            scopeType1 = dao.qryMktActByScope(paramMap2);

            paramMap2.put("scopeType","2");
            Map<String,Object> inMap2 = new HashMap<>();
            inMap.put("stationId",inMap.get("stationId"));
            List<Map<String,Object>> bulldList = stationRpcService.queryStationBuildByStationId(inMap);
            if (CollectionUtils.isNotEmpty(bulldList)){
                paramMap2.put("buildId",bulldList.get(0).get("buildId"));
            }
            scopeType2 = dao.qryMktActByScope(paramMap2);

            paramMap2.put("scopeType","3");
            List<Map<String,Object>> cityList = stationRpcService.qryStationByStationId(inMap);
            if (CollectionUtils.isNotEmpty(cityList)){
                paramMap2.put("city",cityList.get(0).get("city"));
            }
            scopeType3 = dao.qryMktActByScope(paramMap2);

        }else {
            paramMap.put("scopeType","0");
            List<Map> unStationL = dao.qryMktActByScope(paramMap);
            logger.info("查询所有活动scopeType=0入参--------------"+paramMap);
            logger.info("查询所有活动scopeType=0--------------"+unStationL);
            List<String> cityList = new ArrayList<>();
            List<String> station2List = new ArrayList<>();
            List<String> station3List = new ArrayList<>();
            for (Map map : unStationL){
                if ("1".equals(StringUtil.trimToEmpty(map.get("scopeType")))){
                    scopeType1.add(map);
                }
                if ("2".equals(StringUtil.trimToEmpty(map.get("scopeType")))){
                    String buildId = StringUtil.trimToEmpty(map.get("buildId"));
                    if (StringUtil.isNotBlank(buildId)){
                        logger.info("查询所有活动scopeType=2根据运营商查站点的运营商id--------------"+buildId);
                        List<Map<String,Object>> mapList = stationRpcService.queryStationByBuild(buildId);
                        logger.info("查询所有活动scopeType=2根据运营商查站点结果--------------"+mapList);
                        for (int i = 0; i < mapList.size();i++){
                            Map<String,Object> conStaitonMap = new HashMap<>();
                            conStaitonMap.putAll(map);
                            conStaitonMap.put("stationId",mapList.get(i).get("stationId"));
                            scopeType2.add(conStaitonMap);
                        }
                    }
                }
                if ("3".equals(StringUtil.trimToEmpty(map.get("scopeType")))){
                    String city = StringUtil.trimToEmpty(map.get("city"));
                    String buildId = StringUtil.trimToEmpty(map.get("buildId"));
                    if (StringUtil.isNotBlank(city)){
                        cityList.add(city);
                    }
                    if (CollectionUtils.isNotEmpty(cityList)){
                        Map<String,Object> cityBuildMap = new HashMap<>();
                        cityBuildMap.put("buildId",buildId);
                        cityBuildMap.put("cityList",cityList);
                        List<Map<String,Object>> cityStationList  = stationRpcService.queryStationByBuildAndCity(cityBuildMap);
                        logger.info("查询所有活动scopeType=3根据城市查站点--------------"+cityStationList);
                        for (int i =0 ;i<cityStationList.size();i++){
                            Map<String,Object> conStaitonMap = new HashMap<>();
                            conStaitonMap.putAll(map);
                            conStaitonMap.put("stationId",cityStationList.get(i).get("stationId"));
                            scopeType3.add(conStaitonMap);
                        }
                    }
                }

            }


        }
        resultMap.put("1",scopeType1);
        resultMap.put("2",scopeType2);
        resultMap.put("3",scopeType3);
        resultMap.put("4",scopeType4);

        return resultMap;
    }

    /**
     * <AUTHOR>
     * @description  查询优惠内容（限时折扣）
     * @create 2019/5/28 15:00
     *
     */
    @Override
    public List<Map> qryActContDct(Map<String, Object> inMap) {
        return dao.qryActContDct(inMap);
    }

    /**
     * <AUTHOR>
     * @description 查询活动和资讯信息
     * @create 2019/5/28 17:20
     *
     */
    public List<Map> getActAndInfo(Map<String, Object> map) throws Exception {
        logger.debug("==============获取所有进行中的活动入参:" + JsonUtil.obj2Json(map));
        String pubPath = PublicConstants.ApplicationPath.getPubPath();
        List<Map> actList = dao.qryActAndInfo(map);
        if(CollectionUtils.isNotEmpty(actList)){
            for (Map actMap : actList) {
                if("1".equals(StringUtil.getString(actMap.get("infoType")))){//图文
                    actMap.put("actUrl",pubPath + "/api/v0.1/attachs/" + actMap.get("contentUrl"));
                }else{//外部链接
                    actMap.put("actUrl",actMap.get("linkUrl"));
                }
            }
        }
        logger.debug("==============获取所有进行中的活动出参:" + JsonUtil.obj2Json(actList));
        return actList;
    }

    /**
     * @param
     * @description  根据订单查询活动参与
     * <AUTHOR>
     * @create 2019/6/4 18:25
     */
    public List<Map> qeyActJoin(Map<String, Object> inMap) {
        List<Map> joinList = dao.qeyActJoin(inMap);
        return joinList;
    }

    /**
     * @param actId
     * @description 根据Id获取活动信息
     * <AUTHOR>
     * @create 2020-11-12 16:55
     */
    @Override
    public Map<String, Object> getActivityInfoById(String actId) {
        Map<String,Object> resultMap = new HashMap<>(16);
        MarketActBo qryActBo = new MarketActBo();
        qryActBo.setActId(Long.parseLong(actId));
        List<MarketActBo> marketActBoList = marketActDao.qryMarketActives(qryActBo);
        if (marketActBoList != null && marketActBoList.size() > 0){
            MarketActBo resultBo = marketActBoList.get(0);
            resultMap = MapUtils.beanToMap(resultBo);
        }
        return resultMap;
    }

    /**
     * @param actId
     * @Description: 关闭活动
     * @param: actId: 活动id
     * <AUTHOR>
     * @date 2023/5/12 15:10
     */
    @Override
    public void stopAct(Long actId) {
        MarketActBo marketActBo = new MarketActBo();
        marketActBo.setActId(actId);
        marketActBo.setActState("4");//活动状态 0草稿 1未开始、2进行中、3已结束、4已关闭
        marketActBo.setCloseTime(DateTools.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
        marketActDao.updateMarketActive(marketActBo);
    }

    @Override
    public Map<String, Object> queryActInProgress(Map<String, Object> map) {
        MarketActBo marketActBo = new MarketActBo();
        marketActBo.setActType(MapUtils.getValue(map,"actType"));
        marketActBo.setActState(MapUtils.getValue(map,"actState"));//活动状态 0草稿 1未开始、2进行中、3已结束、4已关闭
        Map<String, Object> returnMap = marketActDao.queryActInProgress(marketActBo);
        if (returnMap == null) {
            return MapUtils.createSucResult();
        }

        AttachBo attachBo=new AttachBo();
        //查询图片
        attachBo.setRelaTable("actImg");
        attachBo.setRelaId(MapUtils.getValue(returnMap,"actId"));
        logger.info("查询图片入参：{}", attachBo);
        AttachBo picBo=  attachService.qryAttachInfo(attachBo);
        if (picBo!=null){
            returnMap.put("fileName",picBo.getFileName());
            returnMap.put("fileId",picBo.getAttachId());
            returnMap.put("filePath",picBo.getFilePath());
        }

        return returnMap;
    }
}
