package com.ls.ner.billing.mktact.bo;

import java.util.List;

import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

public class MarketActBo extends QueryCondition {

	/**
	  * 活动ID
	  */
	private long actId=0;
	/**
	  * 管理单位
	  */
	private String orgCode;
	/**
	  * 管理单位名称
	  */
	@DicAttribute(dicName = "orgDict", key = "orgCode", subType = "")
	private String orgCodeName;
	private List<String> orgCodes;
	/**
	  * 活动编号
	  */
	private String actNo;
	/**
	  * 活动类型
	  */
	private String actType;
	/**
	  * 活动名称
	  */
	private String actName;
	/**
	  * 活动渠道
	  */
	private String actChannel;
	/**
	  * 活动标签
	  */
	private String actLbl;
	/**
	  * 活动描述
	  */
	private String actMarks;
	/**
	 * 活动详情描述
	 */
	private String actDetailMarks;
	/**
	  * 生效时间
	  */
	private String effTime;
	/**
	  * 失效时间
	  */
	private String expTime;
	/**
	  * 活动状态
	  */
	private String actState;
	/**
	  * 状态时间
	  */
	private String stateTime;
	/**
	  * 创建时间
	  */
	private String creTime;
	/**
	  * 创建人员
	  */
	private String creEmp;
	/**
	 * 适用用户 0-个人，1-企业
	 */
	private String appUser;
	/**
	 * 是否享受企业计费
	 */
	private String pBillFlag;

	/**
	 * 是否全部站点
	 */
	private String allStationFlag;
	private String busiType;

	/**
	 * 记录数
	 */
	private int nums;

	/**
	 * 参与次数
	 */
	private int totalTimes;
	/**
	 * 参与人数
	 */
	private int totalNums;
	/**
	 * 参与情况
	 */
	private String detailRemark;


	/**
	  * 活动类型名称
	  */
	@DicAttribute(dicName = "codeDict", key = "actType", subType = BillConstants.ACT_TYPE)
	private String actTypeName;
	/**
	  * 活动状态名称
	  */
	@DicAttribute(dicName = "codeDict", key = "actState", subType = ActStatus.CODE_TYPE)
	private String actStateName;
	/**
	 * 发布标识
	 */
	private String release;
	/**
	 * 活动查询，不查询状态为草稿的记录，这里做一下标识
	 */
	private String actQryFlag;
	/**
	 * 兑换优惠券所需积分数量
	 */
	private String needPointNum;
	/**
	 * 活动子类型
	 */
	private String actSubType;


	//是否立即 1是 0不是
	private String isImmediate;
	//关闭时间
	private String closeTime;
	//适用对象，01个人 02企业
	private String custType;

	//运营商  全部1 无运营商限制0 具体运营商 运营商id
	private String buildId;

	// 宁德新增字段控制本次活动充值金是否允许提现， 0 否，1 是
	private String allowWithdraw;

	private String dctCondType;

	private String dctLvl;

	/**
	 * 图片id
	 */
	private String fileId;

	private String mainFlag;// 判断是否为活动主页面查询

	// 宁德新增字段是否仅通过链接获取优惠券 0否1是
	private String isLink;

	public String getIsLink() {
		return isLink;
	}

	public void setIsLink(String isLink) {
		this.isLink = isLink;
	}

	public String getNeedPointNum() {
		return needPointNum;
	}

	public void setNeedPointNum(String needPointNum) {
		this.needPointNum = needPointNum;
	}

	/**
	 * 获取活动查询，不查询状态为草稿的记录，这里做一下标识
	 * @return actQryFlag 活动查询，不查询状态为草稿的记录，这里做一下标识
	 */
	public String getActQryFlag() {
		return actQryFlag;
	}
	/**
	 * 设置活动查询，不查询状态为草稿的记录，这里做一下标识
	 * @param actQryFlag 活动查询，不查询状态为草稿的记录，这里做一下标识
	 */
	public void setActQryFlag(String actQryFlag) {
		this.actQryFlag = actQryFlag;
	}
	/**
	 * 获取活动ID
	 * @return actId 活动ID
	 */
	public long getActId() {
		return actId;
	}
	/**
	 * 设置活动ID
	 * @param actId 活动ID
	 */
	public void setActId(long actId) {
		this.actId = actId;
	}
	/**
	 * 获取管理单位
	 * @return orgCode 管理单位
	 */
	public String getOrgCode() {
		return orgCode;
	}
	/**
	 * 设置管理单位
	 * @param orgCode 管理单位
	 */
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	/**
	 * 获取活动编号
	 * @return actNo 活动编号
	 */
	public String getActNo() {
		return actNo;
	}
	/**
	 * 设置活动编号
	 * @param actNo 活动编号
	 */
	public void setActNo(String actNo) {
		this.actNo = actNo;
	}
	/**
	 * 获取活动类型
	 * @return actType 活动类型
	 */
	public String getActType() {
		return actType;
	}
	/**
	 * 设置活动类型
	 * @param actType 活动类型
	 */
	public void setActType(String actType) {
		this.actType = actType;
	}
	/**
	 * 获取活动名称
	 * @return actName 活动名称
	 */
	public String getActName() {
		return actName;
	}
	/**
	 * 设置活动名称
	 * @param actName 活动名称
	 */
	public void setActName(String actName) {
		this.actName = actName;
	}
	/**
	 * 获取活动渠道
	 * @return actChannel 活动渠道
	 */
	public String getActChannel() {
		return actChannel;
	}
	/**
	 * 设置活动渠道
	 * @param actChannel 活动渠道
	 */
	public void setActChannel(String actChannel) {
		this.actChannel = actChannel;
	}
	/**
	 * 获取活动标签
	 * @return actLbl 活动标签
	 */
	public String getActLbl() {
		return actLbl;
	}
	/**
	 * 设置活动标签
	 * @param actLbl 活动标签
	 */
	public void setActLbl(String actLbl) {
		this.actLbl = actLbl;
	}
	/**
	 * 获取活动描述
	 * @return actMarks 活动描述
	 */
	public String getActMarks() {
		return actMarks;
	}
	/**
	 * 设置活动描述
	 * @param actMarks 活动描述
	 */
	public void setActMarks(String actMarks) {
		this.actMarks = actMarks;
	}

	public String getActDetailMarks() {
		return actDetailMarks;
	}

	public void setActDetailMarks(String actDetailMarks) {
		this.actDetailMarks = actDetailMarks;
	}

	/**
	 * 获取生效时间
	 * @return effTime 生效时间
	 */
	public String getEffTime() {
		return effTime;
	}
	/**
	 * 设置生效时间
	 * @param effTime 生效时间
	 */
	public void setEffTime(String effTime) {
		this.effTime = effTime;
	}
	/**
	 * 获取失效时间
	 * @return expTime 失效时间
	 */
	public String getExpTime() {
		return expTime;
	}
	/**
	 * 设置失效时间
	 * @param expTime 失效时间
	 */
	public void setExpTime(String expTime) {
		this.expTime = expTime;
	}
	/**
	 * 获取活动状态
	 * @return actState 活动状态
	 */
	public String getActState() {
		return actState;
	}
	/**
	 * 设置活动状态
	 * @param actState 活动状态
	 */
	public void setActState(String actState) {
		this.actState = actState;
	}
	/**
	 * 获取状态时间
	 * @return stateTime 状态时间
	 */
	public String getStateTime() {
		return stateTime;
	}
	/**
	 * 设置状态时间
	 * @param stateTime 状态时间
	 */
	public void setStateTime(String stateTime) {
		this.stateTime = stateTime;
	}
	/**
	 * 获取创建时间
	 * @return creTime 创建时间
	 */
	public String getCreTime() {
		return creTime;
	}
	/**
	 * 设置创建时间
	 * @param creTime 创建时间
	 */
	public void setCreTime(String creTime) {
		this.creTime = creTime;
	}
	/**
	 * 获取创建人员
	 * @return creEmp 创建人员
	 */
	public String getCreEmp() {
		return creEmp;
	}
	/**
	 * 设置创建人员
	 * @param creEmp 创建人员
	 */
	public void setCreEmp(String creEmp) {
		this.creEmp = creEmp;
	}
	/**
	 * 获取记录数
	 * @return nums 记录数
	 */
	public int getNums() {
		return nums;
	}
	/**
	 * 设置记录数
	 * @param nums 记录数
	 */
	public void setNums(int nums) {
		this.nums = nums;
	}
	/**
	 * 获取活动类型名称
	 * @return actTypeName 活动类型名称
	 */
	public String getActTypeName() {
		return actTypeName;
	}
	/**
	 * 设置活动类型名称
	 * @param actTypeName 活动类型名称
	 */
	public void setActTypeName(String actTypeName) {
		this.actTypeName = actTypeName;
	}
	/**
	 * 获取活动状态名称
	 * @return actStateName 活动状态名称
	 */
	public String getActStateName() {
		return actStateName;
	}
	/**
	 * 设置活动状态名称
	 * @param actStateName 活动状态名称
	 */
	public void setActStateName(String actStateName) {
		this.actStateName = actStateName;
	}
	/**
	 * 获取发布标识
	 * @return release 发布标识
	 */
	public String getRelease() {
		return release;
	}
	/**
	 * 设置发布标识
	 * @param release 发布标识
	 */
	public void setRelease(String release) {
		this.release = release;
	}
	/**
	 * 获取管理单位名称
	 * @return orgCodeName 管理单位名称
	 */
	public String getOrgCodeName() {
		return orgCodeName;
	}
	/**
	 * 设置管理单位名称
	 * @param orgCodeName 管理单位名称
	 */
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	/**
	 * 获取orgCodes
	 * @return orgCodes orgCodes
	 */
	public List<String> getOrgCodes() {
		return orgCodes;
	}
	/**
	 * 设置orgCodes
	 * @param orgCodes orgCodes
	 */
	public void setOrgCodes(List<String> orgCodes) {
		this.orgCodes = orgCodes;
	}

	private String prodBusiType; // 产品业务类

	public String getProdBusiType() {
		return prodBusiType;
	}

	public void setProdBusiType(String prodBusiType) {
		this.prodBusiType = prodBusiType;
	}

	public String getActSubType() {
		return actSubType;
	}

	public void setActSubType(String actSubType) {
		this.actSubType = actSubType;
	}

	/**
	 * 赠送余额类型，引用支付中心代码accType
	 */
	private String presentBalType;
	/**
	 * 赠送规则的标识
	 */
	private String presentSectionId;

	public String getPresentBalType() {
		return presentBalType;
	}

	public void setPresentBalType(String presentBalType) {
		this.presentBalType = presentBalType;
	}

	public String getPresentSectionId() {
		return presentSectionId;
	}

	public void setPresentSectionId(String presentSectionId) {
		this.presentSectionId = presentSectionId;
	}

	private String infoType; //资讯类别，1图文资讯 2链接资讯
	private String content; // 内容
	private String contentUrl; // 资讯类别=1时有效，图文网址，为CONTENT生成的静态html
	private String linkUrl;// 资讯类别=2时有效，链接网址
	private String infoImgUrl;// 资讯的封面图片
	private String actCondId; // 优惠条件ID

	public String getActCondId() {
		return actCondId;
	}

	public void setActCondId(String actCondId) {
		this.actCondId = actCondId;
	}

	public String getInfoType() {
		return infoType;
	}

	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getContentUrl() {
		return contentUrl;
	}

	public void setContentUrl(String contentUrl) {
		this.contentUrl = contentUrl;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	public String getInfoImgUrl() {
		return infoImgUrl;
	}

	public void setInfoImgUrl(String infoImgUrl) {
		this.infoImgUrl = infoImgUrl;
	}

	public String getAppUser() {
		return appUser;
	}

	public void setAppUser(String appUser) {
		this.appUser = appUser;
	}

	public String getpBillFlag() {
		return pBillFlag;
	}

	public void setpBillFlag(String pBillFlag) {
		this.pBillFlag = pBillFlag;
	}

	public String getAllStationFlag() {
		return allStationFlag;
	}

	public void setAllStationFlag(String allStationFlag) {
		this.allStationFlag = allStationFlag;
	}

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}

	public String getIsImmediate() {
		return isImmediate;
	}

	public void setIsImmediate(String isImmediate) {
		this.isImmediate = isImmediate;
	}

	public String getCloseTime() {
		return closeTime;
	}

	public void setCloseTime(String closeTime) {
		this.closeTime = closeTime;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

    public String getBuildId() {
        return buildId;
    }

    public void setBuildId(String buildId) {
        this.buildId = buildId;
    }

	public String getAllowWithdraw() {
		return allowWithdraw;
	}

	public void setAllowWithdraw(String allowWithdraw) {
		this.allowWithdraw = allowWithdraw;
	}

	public String getDctCondType() {
		return dctCondType;
	}

	public void setDctCondType(String dctCondType) {
		this.dctCondType = dctCondType;
	}

	public String getDctLvl() {
		return dctLvl;
	}

	public void setDctLvl(String dctLvl) {
		this.dctLvl = dctLvl;
	}

	public int getTotalTimes() {
		return totalTimes;
	}

	public void setTotalTimes(int totalTimes) {
		this.totalTimes = totalTimes;
	}

	public int getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(int totalNums) {
		this.totalNums = totalNums;
	}

	public String getDetailRemark() {
		return detailRemark;
	}

	public void setDetailRemark(String detailRemark) {
		this.detailRemark = detailRemark;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getMainFlag() {
		return mainFlag;
	}

	public void setMainFlag(String mainFlag) {
		this.mainFlag = mainFlag;
	}
}
