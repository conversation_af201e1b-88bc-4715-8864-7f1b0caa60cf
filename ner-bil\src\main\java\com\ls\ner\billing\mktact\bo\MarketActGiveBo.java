package com.ls.ner.billing.mktact.bo;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 注册送和邀请送的赠送内容
 * @create 2017-08-09 11:09
 */
public class MarketActGiveBo extends QueryCondition implements Serializable {
    /**
     * 活动ID
     */
    private String actId;
    /**
     * 赠送类别dctGiveType
     */
    private String dctGiveType;
    /**
     * 优惠条件明细ID
     */
    private String actCondDetId;
    /**
     * 优惠券ID
     */
    private String cpnId;
    /**
     * 优惠条件ID
     */
    private String actCondId;

    public String getActId() {
        return actId;
    }

    public void setActId(String actId) {
        this.actId = actId;
    }

    public String getDctGiveType() {
        return dctGiveType;
    }

    public void setDctGiveType(String dctGiveType) {
        this.dctGiveType = dctGiveType;
    }

    public String getActCondDetId() {
        return actCondDetId;
    }

    public void setActCondDetId(String actCondDetId) {
        this.actCondDetId = actCondDetId;
    }

    public String getCpnId() {
        return cpnId;
    }

    public void setCpnId(String cpnId) {
        this.cpnId = cpnId;
    }

    public String getActCondId() {
        return actCondId;
    }

    public void setActCondId(String actCondId) {
        this.actCondId = actCondId;
    }
}
