package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.ChargeItemBo;
import com.ls.ner.billing.common.bo.ChargeItemBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ChargeItemBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(ChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(ChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(ChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(ChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<ChargeItemBo> selectByExample(ChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    ChargeItemBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") ChargeItemBo record, @Param("example") ChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") ChargeItemBo record, @Param("example") ChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(ChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(ChargeItemBo record);
}