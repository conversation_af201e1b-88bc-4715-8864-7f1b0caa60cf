package com.ls.ner.billing.gift.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.billing.gift.bo.AccessTokenResult;
import com.ls.ner.billing.gift.bo.CertUserInfoResult;
import com.ls.ner.billing.gift.bo.GetAccessTokenRequest;
import com.ls.ner.billing.gift.bo.ResultBean;
import com.ls.ner.billing.gift.util.BusinessException;
import com.ls.ner.billing.gift.util.HttpUtils;
import com.ls.ner.billing.gift.util.SignUtils;


import java.util.HashMap;
import java.util.Map;

public class UserOAuthServiceImpl {
    public static AccessTokenResult getAccessToken(GetAccessTokenRequest input, String secertKey, String getAccessTokenUrl) throws Exception {
        AccessTokenResult tokenInfo;
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("code", input.getCode());
        requestMap.put("clientId", input.getClientId());
        requestMap.put("grantType", input.getGrantType());
        requestMap.put("redirectUri", input.getRedirectUri());
        requestMap.put("clientSecret", secertKey);
        String sign = SignUtils.signByMD5(requestMap, "");
        requestMap.put("sign", sign);
        requestMap.remove("clientSecret");
        String result = "";
        result=  HttpUtils.postRequest( getAccessTokenUrl, requestMap);
        if(null == result){
            throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_CODE,"请求返回为null");
        }
        ResultBean resultBean = JSONObject.parseObject(result, ResultBean.class);
        if ("0".equals(resultBean.getMeta().getCode())) {
            tokenInfo = JSONObject.parseObject(resultBean.getData(), AccessTokenResult.class);
        } else {
            throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.CODE_ERROR, "code已失效");
        }
        return tokenInfo;
    }

    public static CertUserInfoResult getUserInfo(String accessToken, String getUserInfoUrl) {
        CertUserInfoResult userInfo;
        String getUserInfoUri = getUserInfoUrl + "?accessToken=" + accessToken;
        String userResp = HttpUtils.getRequest(getUserInfoUri);
        if (null == userResp) {
            throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_CODE, "请求返回为null");
        }
        System.out.println("getUserInfo result11: "+userResp);
        ResultBean userResultBean = JSONObject.parseObject(userResp, ResultBean.class);

        if ("0".equals(userResultBean.getMeta().getCode())) {
            userInfo = JSONObject.parseObject(userResultBean.getData(), CertUserInfoResult.class);
        } else {
            throw new BusinessException(com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_PARAMS, "参数异常");
        }
        return userInfo;
    }
}
