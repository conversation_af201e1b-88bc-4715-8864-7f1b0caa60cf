<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="注册送管理">
	<script type="text/javascript" charset="utf-8" src="<%=pubPath %>/pub/validate/validation.js"></script>
	<style>

	</style>
</ls:head>

<ls:body>
	<ls:layout-use>
		<ls:form id="registerSendForm" name="registerSendForm">
			<ls:text name="actId" property="actId" type="hidden"/>
			<ls:text name="actType" property="actType" type="hidden"/>
			<ls:text name="dctType" property="dctType" type="hidden"/>
			<ls:text name="actCondId" property="actCondId" type="hidden"/>
			<ls:text name="actCondDetId" property="actCondDetId" type="hidden"/>
			<ls:text name="cpnObj" value="" type="hidden"/>
			<ls:text name="cpnName" type="hidden"/>
			<ls:text name="cpnId"  type="hidden"/>

			<ls:layout-put into="container">
				<table>
					<tr>
						<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
					</tr>
				</table>
				<table class="tab_search">
					<tr>
						<td>
							<ls:grid url="" name="actDctGrid" caption="" showRowNumber="true"
									 primaryKey="id" showCheckBox="false" singleSelect="true" cellEditAfter="addRowAfter">
								<ls:gridToolbar name="operation">
									<ls:gridToolbarItem name="addBtn" text="添加" imageKey="add" onclick="addAct"></ls:gridToolbarItem>
									<ls:gridToolbarItem name="delBtn" text="删除" imageKey="delete" onclick="delAct"></ls:gridToolbarItem>
								</ls:gridToolbar>
								<ls:column caption="优惠券" name="cpnName" editableEdit="false" width="99.8%">
									<ls:textEditor imageKey="search" onClickImage="choiceCoupon"/>
								</ls:column>
								<ls:column  name="cpnId" caption="" width="0.1%"><ls:textEditor  /></ls:column>
								<ls:column name="cpnArr" caption="" width="0.1%"><ls:textEditor  /></ls:column>
							</ls:grid>
						</td>
					</tr>
				</table>
			</ls:layout-put>
			<ls:layout-put into="footer">
				<div class="pull-right">
					<ls:button text="上一步" onclick="doUp"/>
					<%--<ls:button text="下一步" onclick="doPut"/>--%>
					<ls:button text="保存" onclick="doSave" />
					<ls:button text="发布" onclick="doPut" />
				</div>
			</ls:layout-put>
		</ls:form>
	</ls:layout-use>

	<ls:script>

		window.actDctGrid = actDctGrid;

		window.onload = function(){
			$('#_id_actDctGrid_cpnId').removeClass("ui-state-default ui-th-column ui-th-ltr");
			$('#_id_actDctGrid_cpnArr').removeClass("ui-state-default ui-th-column ui-th-ltr");
			queryGrid();
		}

		function queryGrid(){
			var params =  registerSendForm.getFormData();
			actDctGrid.query("~/marketacts/registerSendManage/queryActCpn",params);
		}
		<%-- 描述:添加  创建人:biaoxiangd  创建时间:2017/7/7 1:10 --%>
		function addAct(){
			actDctGrid.addRow();
		}

		<%-- 描述:删除  创建人:biaoxiangd  创建时间:2017/6/30 11:18 --%>
		function delAct(){
			var dataParams = registerSendForm.getFormData();
			var checkedItems = actDctGrid.getCheckedItems();
			if(checkedItems==null || checkedItems.length!=1){
				LS.message("info","请选择一条记录!");
				return;
			}
			var allItems = actDctGrid.getItems();
			if(allItems && allItems.length>0) {
				for(var i=0; i< allItems.length; i++) {
					if(checkedItems[0].id == allItems[i].id){
						var _cpnArr = allItems[i].cpnArr;
						var _cpnObj = cpnObj.getValue();
						if(!LS.isEmpty(_cpnArr) && !LS.isEmpty(_cpnObj)){
							var bk = _cpnObj.replace(_cpnArr,'');
							cpnObj.setValue(bk);
						}
						dataParams.cpnId = allItems[i].cpnId;
						actDctGrid.removeItem(allItems[i]);
					}
				}
			}
			LS.ajax("~/marketacts/registerSendManage/delMktActGive",dataParams,function(data) {
				if(data.successful){
					LS.message("info","删除成功");
				}else{
					LS.message("error",data.resultHint);
				}
			});
		}

		<%-- 描述:保存  创建人:biaoxiangd  创建时间:2017/6/29 15:24 --%>
		function doSave(){
			if(!registerSendForm.valid()){
				return false;
			}

			var dataParams = registerSendForm.getFormData();
			var changeItems = actDctGrid.getItems();
			for(var i=0;i < changeItems.length;i++){
				if(LS.isEmpty(changeItems[i].cpnName)){
					LS.message("error","存在未选择的优惠券信息");
					return false;
				}
			}
			dataParams.detItems = changeItems;
			LS.ajax("~/marketacts/registerSendManage/saveMktActGive",dataParams,function(data) {
				if(data.successful){
					LS.message("info","保存成功");
					return true;
				}else{
					LS.message("error",data.resultHint);
					return false;
				}
			});
		}
		<%-- 描述:发布doPut 创建人:biaoxiangd  创建时间:2017/6/29 15:17 --%>
		function doPut(){
			if(!registerSendForm.valid()){
				return false;
			}

			var dataParams = registerSendForm.getFormData();
			var changeItems = actDctGrid.getItems();
			for(var i=0;i < changeItems.length;i++){
				if(LS.isEmpty(changeItems[i].cpnName)){
					LS.message("error","存在未选择的优惠券信息");
					return false;
				}
			}
			dataParams.detItems = changeItems;
			LS.ajax("~/marketacts/registerSendManage/saveMktActGive",dataParams,function(data) {
				if(data.successful){
					var params = registerSendForm.getFormData();
					LS.ajax("~/marketacts/releaseAct",params,function(data){
						if(data.successful){
							LS.message("info",data.resultHint);
							LS.parent().doSearch();
							LS.window.close();
						}else{
							LS.message("info",data.resultHint);
						}
					});
				}else{
					LS.message("error",data.resultHint);
				}
			});
			<%--if(doSave()){
				var actIdValue = actId.getValue();
				window.location.href="<%=request.getContextPath() %>/marketacts/actInfo?actId="+actIdValue+"&actType=";
			}--%>
		}

		<%-- 描述:上一步  创建人:biaoxiangd  创建时间:2017/6/29 15:19 --%>
		function doUp(){
			var actIdValue = actId.getValue();
			window.location.href="<%=request.getContextPath() %>/marketacts/edit?actId="+actIdValue;
		}

<%-- 描述:选择优惠券并回调   创建人:biaoxiangd  创建时间:2017/7/7 10:24 --%>
	function addRowAfter(rowId ,cellName, value, column){
		var _id = rowId+'_'+cellName+'_'+rowId+'_'+cellName;
		$("#" + _id).attr("disabled",true);
		<%--if('cpnName' != cellName){
			actDctGrid.setHideCol([cellName]);
		}--%>
	}
	var _rowId = '';
	window.setCoupon = function(item){

		var tempCpnName = item.cpnName;
		var tempcpnId = item.cpnId;
		cpnName.setValue(tempCpnName);
		cpnId.setValue(tempcpnId);
		actDctGrid.setCell(_rowId, "cpnName", tempCpnName);
		actDctGrid.setCell(_rowId, "cpnId", tempcpnId);
	}
	function choiceCoupon(rowid,rowdata,rowEditor){

		_rowId = rowid;
		var _cpnObj = cpnObj.getValue();
		cpnName.setValue("");
		cpnId.setValue("");
		LS.dialog("~/billing/coupon/couponManage/06","选择优惠券",800,600,true, {
			afterClose : function(){

				var _cpnName = cpnName.getValue();
				var _cpnId = cpnId.getValue();
				var _cpnArr = _cpnName + '$' + _cpnId + ',';
				if(LS.isEmpty(_cpnObj)){
					cpnObj.setValue(_cpnArr);
				}else{
					if(_cpnObj.indexOf(_cpnArr) >= 0){
						LS.message("error","选择的优惠券已存在列表中，请重新添加");
						return;
					}else{
						cpnObj.setValue(_cpnObj + '' + _cpnArr);
					}
				}
				<%--rowEditor['cpnName'].setValue(_cpnName);--%>
				<%--rowEditor['cpnId'].setValue(_cpnId);--%>
				<%--rowEditor['cpnArr'].setValue(_cpnArr);--%>
			}
		});
	}

	</ls:script>
</ls:body>
</html>