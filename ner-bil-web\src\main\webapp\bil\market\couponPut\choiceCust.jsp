<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="客户档案查询">
</ls:head>


<ls:body>
	<ls:form name="custForm" >
		<ls:title text="查询客户信息"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:label text="客户编号"></ls:label></td>
				<td><ls:text name="custNo"></ls:text></td>
				<td><ls:label text="客户名称"></ls:label></td>
				<td><ls:text name="custName"></ls:text></td>
				<td><ls:label text="客户分类"></ls:label></td>
				<td><ls:select name="custSortCode">
						<ls:options property="custSortCodeList" scope="request"
							text="codeName" value="codeValue" />
					</ls:select></td>
			</tr>
			<tr>
				<td><ls:label text="手机号码"></ls:label></td>
				<td><ls:text name="mobile"></ls:text></td>
				<td><ls:label text="地址"></ls:label></td>
				<td><ls:text name="custAddr"></ls:text></td>
				<td></td>
				<td><ls:button text="查询" onclick="query" /> <ls:button
						text="清空" onclick="clearAll" /></td>
			</tr>
		</table>
		<ls:title text="客户信息列表"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="custProfileGrid" height="300px"
						width="90%" showCheckBox="true">
						<ls:gridToolbar name="operation">
							<ls:gridToolbarItem name="sure" text="选定" onclick="sure"></ls:gridToolbarItem>
							<ls:gridToolbarItem name="cancel" text="取消" onclick="cancel"></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column name="custId" caption="" hidden="true" />
						<ls:column caption="客户编号" name="custNo" align="center"
							formatFunc="operation" />
						<ls:column caption="客户名称" name="custName" align="left" />
						<ls:column caption="手机号码" name="mobile" />
						<ls:column caption="身份证号码" name="certNo" />
						<ls:column caption="客户分类" name="custSortCodeName" />
						<ls:column caption="客户地址" name="custAddr" align="left" />
						<ls:pager pageSize="10"></ls:pager>
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
	window.getCustDetail=getCustDetail;
	function operation(rowdata){
      return "<a style='text-decoration: underline;'href='javascript:void(0);'onclick='getCustDetail(\"" + rowdata.custId +"\")'>"+rowdata.custNo+"</a>";
    }
    function query(){
    	 var datas={};
    	 datas = custForm.getFormData();
      	custProfileGrid.query("~/cust/custMgr/queryCust",datas);
    }
    function clearAll(){
      custForm.clear();
    };
    function getCustDetail(custId){
      LS.dialog(rootUrl+"cust/custMgr/getCustDetail/"+custId, "客户档案信息", 980, 580, true, null);
    }
    function sure(){      
      var items = custProfileGrid.getCheckedItems();
      if(items.length>0){
        LS.parent().setCust(items);
        setTimeout("LS.window.close()",300);
      }else{
        LS.message("info","请选择记录!");
      }
    }
    function cancel(){
    	LS.window.close();
    }
    </ls:script>
</ls:body>
</html>