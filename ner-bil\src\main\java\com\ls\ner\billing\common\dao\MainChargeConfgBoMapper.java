package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.MainChargeConfgBo;
import com.ls.ner.billing.common.bo.MainChargeConfgBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface MainChargeConfgBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(MainChargeConfgBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(MainChargeConfgBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(MainChargeConfgBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(MainChargeConfgBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<MainChargeConfgBo> selectByExample(MainChargeConfgBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    MainChargeConfgBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") MainChargeConfgBo record, @Param("example") MainChargeConfgBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") MainChargeConfgBo record, @Param("example") MainChargeConfgBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(MainChargeConfgBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(MainChargeConfgBo record);
}