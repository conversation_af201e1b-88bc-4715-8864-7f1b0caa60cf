<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券详情" />
<ls:body>
    <ls:form name="couponForm" id="couponForm" enctype="multipart/form-data">
        <ls:text name="cpnId" property="cpnId" visible="false"></ls:text>
        <ls:text name="cpnStatus" property="cpnStatus" visible="false"></ls:text>
        <ls:text name="prodId" property="prodId" visible="false"></ls:text>
        <ls:text name="filePath" property="filePath"  visible="false"></ls:text>
        <ls:text name="attachId" property="attachId"  visible="false"></ls:text>
        <ls:text name="dctCondCity" property="dctCondCity"  visible="false"></ls:text>
        <ls:text name="dctCondStation" property="dctCondStation"  visible="false"></ls:text>
        <ls:layout-use>
            <ls:layout-put into="container">
                <table align="center" class="tab_search">
                    <colgroup>
                        <col width="20%"/>
                        <col width="15%"/>
                        <col width="15%"/>
                        <col width="20%"/>
                        <col width="15%"/>
                        <col width="15%"/>
                    </colgroup>
                    <tr>
                        <td>
                            <ls:label text="优惠券名称" />
                        </td>
                        <td colspan="2">
                            <ls:text name="cpnName" property="cpnName" enabled="false"></ls:text>
                        </td>

                        <td>
                            <ls:label text="优惠券类型"  />
                        </td>
                        <td>
                            <ls:text name="cpnType" property="cpnType" visible="false"></ls:text>
                            <ls:text name="cpnTypeName" property="cpnTypeName" enabled="false"></ls:text>
                        </td>

                        <td rowspan="4">
                            <div id="couponImage" style="width:200px;height:120px;background:gray">
                                <img src="" id="headImg"  width="200" height="120"></img>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="优惠内容" />
                        </td>
                        <td>
                            <ls:text name="busiType" property="busiType" visible="false"></ls:text>
                            <ls:text name="busiTypeName" property="busiTypeName" enabled="false"></ls:text>
                        </td>
                        <td>
                            <ls:text name="dctType" property="dctType" visible="false"></ls:text>
                            <ls:text name="dctTypeName" property="dctTypeName" enabled="false"></ls:text>
                        </td>
                        <td>
                            <ls:label text="减免面额"   name="cpnAmtLable"/>
                        </td>
                        <td>
                            <ls:text name="cpnAmt" id="cpnAmt" property="cpnAmt"  enabled="false"></ls:text>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="发行数量" />
                        </td>
                        <td colspan="2">
                            <table>
                                <tr>
                                    <td><ls:text name="cpnNum" property="cpnNum" enabled="false"></ls:text></td>
                                    <td>张</td>
                                </tr>
                            </table>
                        </td>
                        <td>
                            <ls:label text="每人限领"/>
                        </td>
                        <td>
                            <table>
                                <tr>
                                    <td><ls:text name="limGetNum" property="limGetNum" enabled="false"></ls:text></td>
                                    <td>张</td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="优惠券描述"/>
                        </td>
                        <td colspan="4">
                            <ls:text name="cpnMarks" property="cpnMarks" type="textarea" enabled="false"></ls:text>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="有效时间" />
                        </td>
                        <td colspan="4">
                            <table>
                                <tr>
                                    <td>
                                        <ls:checklist type="radio" name="cpnTimeType" property="cpnTimeType" enabled="false">
                                            <ls:checkitem value="1" text="日期范围"></ls:checkitem>
                                            <ls:checkitem value="2" text="领用或发放后"></ls:checkitem>
                                        </ls:checklist>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td colspan="3">
                            <table>
                                <tr id="dateType01">
                                    <td>
                                        <ls:date name="eftDate" property="eftDate" format="yyyy-MM-dd" enabled="false"></ls:date>
                                    </td>
                                    <td>
                                        <ls:label text="至" />
                                    </td>
                                    <td>
                                        <ls:date name="invDate" property="invDate" format="yyyy-MM-dd" enabled="false"></ls:date>
                                    </td>
                                </tr>
                                <tr style="display:none" id="dateType02">
                                    <td style="width: 60%">
                                        <ls:text name="timeDuration" property="timeDuration" enabled="false"></ls:text>
                                    </td>
                                    <td style="width: 20%">
                                        <ls:text name="timeUnit" property="timeUnit" visible="false"></ls:text>
                                        <ls:text name="timeUnitName" property="timeUnitName" enabled="false"></ls:text>

                                    </td>
                                    <td style="width: 20%">内有效</td>
                                </tr>
                            </table>
                        </td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="计算结果精度"/>
                        </td>
                        <td>
                            <ls:text name="calcPrecisionDigits" property="calcPrecisionDigits" visible="false"></ls:text>
                            <ls:text name="calcPrecisionDigitsName" property="calcPrecisionDigitsName" enabled="false"></ls:text>
                        </td>
                        <td>
                            <ls:text name="calcPrecision" property="calcPrecision" visible="false"></ls:text>
                            <ls:text name="calcPrecisionName" property="calcPrecisionName" enabled="false"></ls:text>
                        </td>
                        <td scope="2"></td>
                    </tr>

                    <tr>
                        <td>
                            <ls:label text="用途" ref="cpnPurpose"/>
                        </td>
                        <td>
                            <ls:select name="cpnPurpose" property="cpnPurpose" value="2" enabled="false">
                                <ls:option value="1" text="首次添加爱车"></ls:option>
                                <ls:option value="2" text="其他"></ls:option>
                            </ls:select>
                        </td>
                        <td colspan="3">
                            <div id="cpnPurposeText">此类型优惠券只能同时生效一种，若有多种，将以最新配置的为准。</div>
                        </td>
                    </tr>
                    <tr>
                        <td><ls:label text="赠送积分：" ref="integralNum"/></td>
                        <td colspan="3">
                            <div style="display: flex; align-items: center; flex-wrap: nowrap;">
                                <ls:text name="integralNum" property="integralNum" readOnly="true" style="width: 33%; margin-right: 2px;"/>
                                <span style="white-space: nowrap;">积分</span>
                            </div>
                            <div style="font-size: 12px; color: #888; margin-top: 2px;">（填0或留空为不赠送）</div>
                        </td>
                    </tr>
                </table>
                <ls:title text="优惠条件" expand="false"></ls:title>
                <table align="center" class="tab_search">
                    <tr>
                        <td width="10%">
                            <ls:label text="条件类型" />
                        </td>
                        <td width="20%">
                            <ls:checklist type="radio" name="dctCondFlag" property="dctCondFlag"  enabled="false">
                                <ls:checkitem value="1" text="无条件"></ls:checkitem>
                                <ls:checkitem value="2" text="条件"></ls:checkitem>
                            </ls:checklist>
                        </td>
                        <td width="10%"></td>
                        <td width="20%"></td>
                        <td width="10%"></td>
                        <td width="20%"></td>
                    </tr>
                    <tr id="dctCondVal2">
<%--                        <ls:text name="dctCondId" property="dctCondId" visible="false"></ls:text>--%>
<%--                        <td width="10%">--%>
<%--                            <ls:label text="优惠条件类别"  />--%>
<%--                        </td>--%>
<%--                        <td width="20%">--%>
<%--                            <ls:text name="dctCondType" property="dctCondType" visible="false"></ls:text>--%>
<%--                            <ls:text name="dctCondTypeName" property="dctCondTypeName" enabled="false"></ls:text>--%>
<%--                        </td>--%>
<%--                        <td width="10%" id="lab_dctProdId">--%>
<%--                            <ls:label text="优惠条件内容"  />--%>
<%--                        </td>--%>
<%--                        <td width="20%" id="txt_dctProdId">--%>
<%--                            <ls:text name="dctProdId" property="dctProdId" visible="false"></ls:text>--%>
<%--                            <ls:text name="dctProdName" property="dctProdName" enabled="false"></ls:text>--%>
<%--                        </td>--%>
<%--                        <td width="10%">--%>
<%--                            <ls:label text="条件数值" />--%>
<%--                        </td>--%>
<%--                        <td width="20%">--%>
<%--                            <ls:text name="dctCondValue" property="dctCondValue" enabled="false"></ls:text>--%>
<%--                        </td>--%>
                            <td width="10%" id="dctCondHide" style="display:none;">
                                <ls:label text="最低消费金额"  ref="dctCondValue"/>
                            </td>
                            <td width="20%" id="dctCondValueHide" style="display:none;">
                                <ls:text  name="dctCondValue" property="dctCondValue" enabled="false"></ls:text>
                            </td>
                            <td width="10%" id="maximumDiscountAmtHide" style="display:none;">
                                <ls:label text="最高抵扣金额" ref="maximumDiscountAmt"/>
                            </td>
                            <td width="20%" id="maximumDiscountAmtValueHide" style="display:none;">
                                <ls:text  name="maximumDiscountAmt" property="maximumDiscountAmt" enabled="false"></ls:text>
                            </td>
                            <td colspan="3" id="dctCondRemark" style="display:none;"> 最低消费金额、最高抵扣金额根据优惠内容所选择的整单金额/服务费进行变动 </td>
                    </tr>
                </table>
                <table class="tab_search">
                    <tr>
                        <td width="10%">
                            <ls:label text="时间段限制" ref="timeCondFlag"/>
                        </td>
                        <td width="20%">
                            <ls:checklist type="radio" name="timeCondFlag" property="timeCondFlag" enabled="false">
                                <ls:checkitem value="1" text="全部"></ls:checkitem>
                                <ls:checkitem value="2" text="其他时段"></ls:checkitem>
                            </ls:checklist>
                        </td>
                        <td width="1%"></td>
                        <td width="7%"></td>
                        <td width="35%"></td>
                        <td width="17%"></td>
                    </tr>
                </table>
                <table class="tab_search yhsjd">
                    <tr>
                        <td width="10%">
                            <ls:label text="优惠时间段"/>
                        </td>
                        <td width="7%">
                            <ls:text name="timeCondBeg" property="timeCondBeg" type="hidden"/>
                            <input type="time" id="begTime"  name="begTime" disabled="disabled"  placeholder=""
                                   onChange="timeBeginChange(this.value)"/>
                        </td>
                        <td width="1%" style="text-align: center;">
                            至
                        </td>
                        <td width="7%">
                            <ls:text name="timeCondEnd" property="timeCondEnd" type="hidden"/>
                            <input type="time" id="endTime" name="endTime" disabled="disabled"  placeholder=""
                                   onChange="timeEndChange(this.value)"/>
                        </td>
                        <td width="35%"></td>
                        <td width="30%"></td>
                    </tr>
                </table>
                <table class="tab_search">
                    <tr>
                        <td width="10%">
                            <ls:label text="使用区域"/>
                        </td>
                        <td width="20%">
                            <ls:checklist type="radio" name="dctCondAreaFlag" property="dctCondAreaFlag" enabled="false">
                                <ls:checkitem value="1" text="无限制"></ls:checkitem>
                                <ls:checkitem value="2" text="有限制"></ls:checkitem>
                            </ls:checklist>
                        </td>
                        <td width="10%"></td>
                        <td width="20%"></td>
                        <td width="10%"></td>
                        <td width="20%"></td>
                    </tr>
                    <tr id="dctCondVal3">
                        <td width="10%"></td>
                        <td colspan="2" width="40%">
                            <ls:form name="cityForm">
                                <table>
                                    <tr><td colspan="2">
                                        <ls:grid url="" name="cpnCityGrid" height="150px" showCheckBox="false" caption="城市列表">
                                            <ls:column name="areaCode" caption="" hidden="true" />
                                            <ls:column name="areaName" caption="城市"/>
                                        </ls:grid>
                                    </td></tr>
                                </table>
                            </ls:form>
                        </td>
                        <td colspan="2" width="40%">
                            <ls:form name="stationForm">
                                <table>
                                    <tr><td colspan="2">
                                        <ls:grid url="" name="cpnStationGrid" height="150px" showCheckBox="false" caption="站点列表">
                                            <ls:column name="stationId" caption="" hidden="true" />
                                            <ls:column name="stationName" caption="站点"/>
                                        </ls:grid>
                                    </td></tr>
                                </table>
                            </ls:form>
                        </td>
                        <td width="10%"></td>
                    </tr>
                </table>
            </ls:layout-put>
        </ls:layout-use>
    </ls:form>

    <ls:script>
        var headImg = document.getElementById('headImg');
        var pubPath = '${pubPath }';
        onload();
         function onload(){
            var _cpnType = cpnType.getValue();
            var hiddenCell1 = document.getElementById('maximumDiscountAmtHide');
            var hiddenCell2 = document.getElementById('maximumDiscountAmtValueHide');
            var hiddenCell3 = document.getElementById('dctCondHide');
            var hiddenCell4 = document.getElementById('dctCondValueHide');
            var hiddenCell5 = document.getElementById('dctCondRemark');
            if(_cpnType == '01'){
                cpnAmtLable.setText("减免面额");
                hiddenCell1.style.display = 'none';
                hiddenCell2.style.display = 'none';
                hiddenCell3.style.display = 'table-cell';
                hiddenCell4.style.display = 'table-cell';
                hiddenCell5.style.display = 'table-cell';
            }else{
                cpnAmtLable.setText("折扣值");
                hiddenCell1.style.display = 'table-cell';
                hiddenCell2.style.display = 'table-cell';
                hiddenCell3.style.display = 'table-cell';
                hiddenCell4.style.display = 'table-cell';
                hiddenCell5.style.display = 'table-cell';
            }

            setDisplayUseCond();
            setDateType();
            //setDctCondType();
            checkUseCondition();
            setCpnPurposeFun();
            if(!LS.isEmpty(cpnId.getValue()) && !LS.isEmpty(attachId.getValue())){
                headImg.src = pubPath + '/attach/'+attachId.getValue();
            }else{
                headImg.src = pubPath + '/pub/image/js/add.png';
            }
            cityQuery();
            stationInitQuery();
        }

        function setDisplayUseCond(){
        var _dctCondFlag = dctCondFlag.getValue();
            if("2" === _dctCondFlag){
                $("#dctCondVal2").show();
            }else if("1"== _dctCondFlag){
                $("#dctCondVal2").hide();
            }else{
                $("#dctCondVal2").hide();
            }
        }

        function setCpnPurposeFun(){
            if(cpnPurpose.getValue() == "1"){
                $("#cpnPurposeText").show();
            }else{
                $("#cpnPurposeText").hide();
            }
        }

        function setDateType(){
            if("1" === cpnTimeType.getValue()){
                $("#dateType01").show();
                $("#dateType02").hide();
            }else{
                $("#dateType01").hide();
                $("#dateType02").show();
            }
        }

        function setDctCondType(){
            var _dctCondType = dctCondType.value;
            if("00" === _dctCondType){
                $("#lab_dctProdId").hide();
                $("#txt_dctProdId").hide();
            }else{
                $("#lab_dctProdId").show();
                $("#txt_dctProdId").show();
            }
        }
        //区域限制类型改变事件
        function checkUseCondition(){
            var _useAreaFlag = dctCondAreaFlag.getValue();
            if("2"==_useAreaFlag){
                $("#dctCondVal3").show();
            }else if("1"==_useAreaFlag){
                $("#dctCondVal3").hide();
            }else{
                $("#dctCondVal3").hide();
            }
        }
        //城市表格查询方法
        function cityQuery(){
            if(!LS.isEmpty(dctCondCity.getValue())){
                var data = {
                    citys : dctCondCity.getValue()
                };
                cpnCityGrid.query("~/billing/coupon/queryCityInfo", data);
            }
        }
        //修改----初始化站点表格查询方法
        function stationInitQuery() {
            if(!LS.isEmpty(dctCondStation.getValue())){
                var data = {};
                data.stations = dctCondStation.getValue();
                data.busiType = busiType.getValue();
                cpnStationGrid.query("~/billing/coupon/queryStationInfo", data);
            }
        }
        window.timeBeginChange = timeBeginChange;
        window.timeEndChange = timeEndChange;
        function timeBeginChange(Value) {
            timeCondBeg.setValue(Value);
        }
        function timeEndChange(Value) {
            timeCondEnd.setValue(Value);
        }
        setDisplayTimeCond();
        //使用条件类型改变事件
        function setDisplayTimeCond(){
            var _timeCondFlag = timeCondFlag.getValue();
            if("1"==_timeCondFlag){
                timeCondBeg.setValue('00:00');
                timeCondEnd.setValue('23:59');
                $('#begTime').val(timeCondBeg.getValue());
                $('#endTime').val(timeCondEnd.getValue());
                $('.yhsjd').hide();
            }else{
                $('#begTime').val(timeCondBeg.getValue());
                $('#endTime').val(timeCondEnd.getValue());
                $('.yhsjd').show();
            }
        }
    </ls:script>
</ls:body>
</html>