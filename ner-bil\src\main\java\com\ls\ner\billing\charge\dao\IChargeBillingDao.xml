<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillingDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingRltBo">
		<result column="APP_NO" property="appNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="BILL_CTL_MODE" property="billCtlMode" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="T_PQ" property="tPq" />
		<result column="CHARGE_PRICE" property="chargePrice" />
		<result column="T_AMT" property="tAmt" />
		<result column="ITEM_T_AMT" property="itemTAmt" />
		<result column="AMT" property="amt" />
		<result column="ATTACH_ITEM_NOS" property="attachItemNos" />
		<result column="ATTACH_ITEM_PRICES" property="attachItemPrices" />
		<result column="ATTACH_ITEM_UNITS" property="attachItemUnits" />
		<result column="ATTACH_ITEM_NUMS" property="attachItemNums" />
		<result column="ATTACH_ITEM_AMTS" property="attachItemAmts" />
		<result column="T_TIME" property="tTime" />
	 </resultMap>
	 
	 <sql id="chargeBillingItems">
	 	a.APP_NO,a.CHC_NO,a.BILL_CTL_MODE,a.CHARGE_MODE,a.T_PQ,a.CHARGE_PRICE,a.T_AMT,a.ITEM_T_AMT,a.AMT,a.ATTACH_ITEM_NOS,
	 	a.ATTACH_ITEM_PRICES,a.ATTACH_ITEM_UNITS,a.ATTACH_ITEM_NUMS,a.ATTACH_ITEM_AMTS,a.T_TIME
	 </sql>
	 
	<!-- 查询充电实时计费E_CHARGE_BILLING-->
	<select id="queryChargeBilling" parameterType="string" resultMap="BaseResultMap">
		select  
			<include refid="chargeBillingItems"></include>
		from E_CHARGE_BILLING a
		<where>
			a.APP_NO = #{appNo}
		</where>
	</select>
	
	<!-- 查询充电实时计费条数E_CHARGE_BILLING-->
	<select id="queryChargeBillingNum" parameterType="string" resultType="int">
		select count(1)
		from E_CHARGE_BILLING a
		<where>
			a.APP_NO = #{appNo}
		</where>
	</select>
	
	<!-- 新增充电实时计费E_CHARGE_BILLING-->
	<insert id="insertChargeBilling" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_BILLING
		<trim prefix="(" suffix=")">
			<if test="appNo !=null and appNo !=''">APP_NO,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE,</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE,</if>
			<if test="tPq !=null and tPq !=''">T_PQ,</if>
			<if test="chargePrice !=null and chargePrice !=''">CHARGE_PRICE,</if>
			<if test="tAmt !=null and tAmt !=''">T_AMT,</if>
			<if test="itemTAmt !=null and itemTAmt !=''">ITEM_T_AMT,</if>
			<if test="amt !=null and amt !=''">AMT,</if>
			<if test="attachItemNos !=null and attachItemNos !=''">ATTACH_ITEM_NOS,</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">ATTACH_ITEM_PRICES,</if>
			<if test="attachItemUnits !=null and attachItemUnits !=''">ATTACH_ITEM_UNITS,</if>
			<if test="attachItemNums !=null and attachItemNums !=''">ATTACH_ITEM_NUMS,</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">ATTACH_ITEM_AMTS,</if>
			<if test="tTime !=null and tTime !=''">T_TIME,</if>
			DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="appNo !=null and appNo !=''">#{appNo},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">#{chargeMode},</if>
			<if test="tPq !=null and tPq !=''">#{tPq},</if>
			<if test="chargePrice !=null and chargePrice !=''">#{chargePrice},</if>
			<if test="tAmt !=null and tAmt !=''">#{tAmt},</if>
			<if test="itemTAmt !=null and itemTAmt !=''">#{itemTAmt},</if>
			<if test="amt !=null and amt !=''">#{amt},</if>
			<if test="attachItemNos !=null and attachItemNos !=''">#{attachItemNos},</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">#{attachItemPrices},</if>
			<if test="attachItemUnits !=null and attachItemUnits !=''">#{attachItemUnits},</if>
			<if test="attachItemNums !=null and attachItemNums !=''">#{attachItemNums},</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">#{attachItemAmts},</if>
			<if test="tTime !=null and tTime !=''">#{tTime},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电实时计费E_CHARGE_BILLING -->
	<update id="updateChargeBilling" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltBo">
		update E_CHARGE_BILLING
		<set>
			<if test="chcNo !=null and chcNo !=''">CHC_NO =#{chcNo},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE =#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE =#{chargeMode},</if>
			<if test="tPq !=null and tPq !=''">T_PQ =#{tPq},</if>
			<if test="chargePrice !=null and chargePrice !=''">CHARGE_PRICE =#{chargePrice},</if>
			<if test="tAmt !=null and tAmt !=''">T_AMT =#{tAmt},</if>
			<if test="itemTAmt !=null and itemTAmt !=''">ITEM_T_AMT =#{itemTAmt},</if>
			<if test="amt !=null and amt !=''">AMT =#{amt},</if>
			<if test="attachItemNos !=null and attachItemNos !=''">ATTACH_ITEM_NOS =#{attachItemNos},</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">ATTACH_ITEM_PRICES =#{attachItemPrices},</if>
			<if test="attachItemUnits !=null and attachItemUnits !=''">ATTACH_ITEM_UNITS =#{attachItemUnits},</if>
			<if test="attachItemNums !=null and attachItemNums !=''">ATTACH_ITEM_NUMS =#{attachItemNums},</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">ATTACH_ITEM_AMTS =#{attachItemAmts},</if>
			<if test="tTime !=null and tTime !=''">T_TIME =#{tTime},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			APP_NO =#{appNo}
		</where>
	</update>
	
</mapper>