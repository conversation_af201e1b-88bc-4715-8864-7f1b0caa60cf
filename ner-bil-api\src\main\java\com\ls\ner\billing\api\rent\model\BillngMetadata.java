package com.ls.ner.billing.api.rent.model;

public class BillngMetadata {

	private String type;//考虑用来区分 资费标准 定价 结果计算
	//资费标准时
	
	private String templateNo;
	private String templateName;
	
	//定价时
	
	private String schemeNo;
	private String schemeName;
	//========以下是location部分======
	/**
	 * 对应公司
	 */
	private String orgCode;
	/**
	 * 适用车型
	 */
	private String autoModelNo;
	
	private String autoModelName;
	/**
	 * 对应日期
	 */
	private String date;
	//是否应用门店 是否公司统一定价 这个在查的时候应该是能够通过上述信息推定？
	
	//是否是
	
	//是否是调整后的定价。。。调整相关信息 日期等
	private boolean adjusted;
	//
	
	private String introduction;
	//订单号？
	
	private String appNo;
}
