<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="活动资讯发布" >
	<script type="text/javascript" charset="utf-8" src="<%=pubPath %>/pub/validate/validation.js"></script>
  	<style>
		.file-img{
			display:block;
			width: 200px;
			height: 120px;
			position: relative;
		}
		.file-img img{
			display: block;
			width: 100%;
			height: 100%;
			overflow: hidden;
			background:gray;
		}
		.file-img .span_add{
			position: absolute;
			left: 0;
			bottom: 0;
			display: none;
			width: 50%;
			height: 30px;
			line-height: 30px;
			background-repeat: no-repeat;
			background-position: center;
			background-color: rgba(0,0,0,.5);
			text-align: center;
			background-image:url(<%=request.getContextPath() %>/bil/mktact/img/add.png);
		}
		.file-img .span_del{
			position: absolute;
			right: 0;
			bottom: 0;
			display: none;
			width: 50%;
			height: 30px;
			line-height: 30px;
			background-repeat: no-repeat;
			background-position: center;
			background-color: rgba(0,0,0,.5);
			text-align: center;
			background-image:url(<%=request.getContextPath() %>/bil/mktact/img/delete.png);
		}
		.file-img:hover span{
			display: block;
		}
		.file-img input {
			position: absolute;
			font-size: 100px;
			right: 0;
			top: 0;
			opacity: 0;
		}
	</style>
	<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/themes/default/default.css" />
	<link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.css" />
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/kindeditor-all.js"></script>
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/lang/zh-CN.js"></script>
	<script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.js"></script>
</ls:head>

<ls:body>
	<ls:layout-use>

		<ls:form id="actinfoForm" name="actinfoForm" enctype="multipart/form-data">
		<ls:text name="actId" property="actId" type="hidden" />
		<ls:text name="actType" property="actType" type="hidden" />
		<ls:text name="actInfoId" property="actInfoId" type="hidden" />
		<ls:text name="relaId" property="relaId" type="hidden"/>
		<ls:text name="attachId" property="attachId" type="hidden" />
		<ls:text name="contentUrl" property="contentUrl" type="hidden" />
		<ls:text name="picFlag" property="picFlag" type="hidden" />

		<ls:layout-put into="container">
			<table>
				<tr>
					<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step3.png" alt=""></td>
				</tr>
			</table>
			<table>
				<tr style="height: 10px;background-color: #D3E3DF"><td></td></tr>
			</table>
			<table align="center" class="tab_search">
				<colgroup>
					<col width="20%"/>
					<col width="80%"/>
				</colgroup>
				<tr>
					<td><ls:label text="封面图片" ref="coberPic" /></td>
					<td>
						<div>
							<a class="file-img" href="javascript:void(0);">
								<img src="" id="headImg"></img>
								<span id="span_add" class="span_add">
									<input id="fileData" accept="image/*" class="text" name="fileData"
										   type="file" onchange="ShowImage(this)"/>
								</span>
								<span id="span_del" class="span_del" onclick="doDelPic()"></span>
							</a>
						</div>
					</td>
				</tr>
				<tr>
					<td><ls:label text="资讯内容"  ref="infoType"/></td>
					<td>
						<ls:checklist type="radio" name="infoType" property="infoType" required="true" onchanged="doInfoType">
							<ls:checkitems property="infoTypeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
				</tr>
				<tr id="tr_content">
					<td><ls:label text="活动描述" /></td>
					<td height="180px">
						<div id="metacontent">
						</div>
						<ls:text name="content" property="content" visible="false"></ls:text>
							<%--<ls:ckeditor id="metacontent" name="content" property="content" />--%>
					</td>
				</tr>
				<tr id="tr_linkUrl">
					<td><ls:label text="链接网址" /></td>
					<td><ls:text name="linkUrl" property="linkUrl" /></td>
				</tr>
			</table>
		</ls:layout-put>
		<ls:layout-put into="footer">
			<div class="pull-right">
				<ls:button text="取消资讯" onclick="doCal" />
				<ls:button text="发布活动" onclick="doPut" />
			</div>
		</ls:layout-put>
	</ls:form>
	</ls:layout-use>

	<ls:script>
		var editor;
		var pubPath = '${pubPath}';

		KindEditor.ready(function(K) {
		editor = K.create('#metacontent', {
		cssPath : pubPath + '/pub/kindeditor/plugins/code/prettify.css',
		uploadJson : pubPath + '/pub/picture/upload',
		items : [
		'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
		'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
		'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
		'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
		'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
		'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
		'table', 'hr', 'emoticons', 'pagebreak',
		'link', 'unlink'
		],
		extraFileUploadParams : {
		relaTable: 'mktActInfo',
		relaId: actId.getValue()
		},
		afterUpload : function(data) {

		},
		<%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
		allowFileManager : true,
		afterCreate : function() {
		}
		});
		prettyPrint();
		});



		var suppPath = pubPath;<%--'${suppPath}';--%>

		window.onload = function(){
			if(!LS.isEmpty(content.getValue())){
				editor.html(content.getValue());
				contentUrl.setValue(content.getValue())
			}
			var _actInfoId = actInfoId.getValue();
			var _attachId = attachId.getValue();
			var _relaId=relaId.getValue();
			headImg.src = pubPath + '/pub/image/js/add.png';
			if(LS.isEmpty(_actInfoId) || LS.isEmpty(_attachId)){
				headImg.src = pubPath + '/pub/image/js/add.png';
			}else{
				headImg.src = pubPath + '/attach/attachs/mkt_act_info/02/' + _relaId;
			}
			doInfoType();
		}
<%-- 描述:删除图片  创建人:biaoxiangd  创建时间:2017/6/28 14:21 --%>
	window.doDelPic = function(){
		var _picFlag = picFlag.getValue();
		if('0' == _picFlag){
			headImg.src = pubPath + '/pub/image/js/add.png';
			picFlag.setValue('1');
		}
	}
<%-- 描述:资讯内容变更  创建人:biaoxiangd  创建时间:2017/6/28 10:52 --%>
	function doInfoType(){
		var _infoType = infoType.getValue();
		if('2' == _infoType){
			$('#tr_content').hide();
			$('#tr_linkUrl').show();
		}else{
			$('#tr_content').show();
			$('#tr_linkUrl').hide();
		}
	}
<%-- 描述: 发布按钮 创建人:biaoxiangd  创建时间:2017/6/28 14:25 --%>
	function doPut() {
		//将富文本内容设置回文本框
		var html = editor.html();
		content.setValue(html);
		contentUrl.setValue(html);

		var _infoType = infoType.getValue();
		if('1' == _infoType){
			if(LS.isEmpty(content.getValue())){
				LS.message("error","发布活动资讯信息中的内容为空");
				return;
			}
			linkUrl.setValue("");
		}else{
			if(LS.isEmpty(linkUrl.getValue())){
				LS.message("error","发布活动资讯信息中的外部链接为空");
				return;
			}
			content.setValue("");
			contentUrl.setValue("");
		}

		actinfoForm.submit("~/marketacts/saveActInfo",function(data){
			if(data.successful){
				LS.message("info",data.resultHint);
				LS.parent().doSearch();
				LS.window.close();
			}else{
				LS.message("error",data.resultHint);
			}
		});
	}

<%-- 描述:添加图片  创建人:biaoxiangd  创建时间:2017/6/28 14:24 --%>
	window.ShowImage = function(file){
		if($.browser.msie){
			$("#headImg").attr("src",$(this).val())
		}else{
			if (file.files || file.files[0] != undefined) {
				var objUrl = getObjectURL(file.files[0]);
				if(objUrl){
					$("#headImg").attr("src",objUrl);
					picFlag.setValue("0");
				}
			}
		}
	}

	function getObjectURL(file) {
		var url = null ;
		if(file != undefined){
			if (window.createObjectURL!=undefined) {
				url = window.createObjectURL(file) ;
			} else if (window.URL!=undefined) {
				url = window.URL.createObjectURL(file) ;
			} else if (window.webkitURL!=undefined) {
				url = window.webkitURL.createObjectURL(file) ;
			}
		}
		return url ;
	}

	function doCal(){
		LS.window.close();
	}


    </ls:script>
</ls:body>
</html>