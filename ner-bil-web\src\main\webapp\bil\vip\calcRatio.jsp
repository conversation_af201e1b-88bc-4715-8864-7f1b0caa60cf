<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="会员等级配置切换" />
<ls:body>
    <ls:form id="rankConfigForm" name="rankConfigForm" enctype="multipart/form-data">
        <table class="tab_search">
            <tr>
                <td>
                    <ls:label text="${rankConfigForm.calcMode eq 'quantity' ? '按累计充电量计算' : (rankConfigForm.calcMode eq 'amount' ? '按累计充电金额计算' : '切换比例')}" ref="amountPerKwh" />
                    <ls:text type="hidden" name="calcMode" property="calcMode"/>
                </td>
                <td colspan="4">
                    <ls:text name="${rankConfigForm.calcMode eq 'quantity' ? 'kwhPerAmount' :'amountPerKwh' }"
                             property="${rankConfigForm.calcMode eq 'quantity' ? 'kwhPerAmount' :'amountPerKwh' }"
                             type="number" maxlength="11" max="9999"/>
                </td>
                <td>
                    ${rankConfigForm.calcMode eq 'quantity' ? 'kWh/元' : '元/kWh'}
                </td>
            </tr>
            <tr>
                <td>
                </td>
                <td>
                    <ls:button text="确定" onclick="saveVipConfigs"></ls:button>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>

        function saveVipConfigs(){
        LS.confirm('确定要修改该配置?',function(result){
                if(result){
                    rankConfigForm.submit("~/vip/regular/saveConfig",function(data){
                        if(data){
                            LS.message("info","修改成功");
                            LS.parent().query();
                            LS.window.close();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

    </ls:script>
</ls:body>
</html>