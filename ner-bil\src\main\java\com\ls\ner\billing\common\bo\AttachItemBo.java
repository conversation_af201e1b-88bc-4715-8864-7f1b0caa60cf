package com.ls.ner.billing.common.bo;

public class AttachItemBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String planNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.ATTACH_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String attachNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.BUY_IDENTITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String buyIdentity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_attach_item.REMARK
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String remark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.SYSTEM_ID
     *
     * @return the value of e_attach_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.SYSTEM_ID
     *
     * @param systemId the value for e_attach_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.PLAN_NO
     *
     * @return the value of e_attach_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getPlanNo() {
        return planNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.PLAN_NO
     *
     * @param planNo the value for e_attach_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPlanNo(String planNo) {
        this.planNo = planNo == null ? null : planNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.ATTACH_NO
     *
     * @return the value of e_attach_item.ATTACH_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getAttachNo() {
        return attachNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.ATTACH_NO
     *
     * @param attachNo the value for e_attach_item.ATTACH_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setAttachNo(String attachNo) {
        this.attachNo = attachNo == null ? null : attachNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.ITEM_NAME
     *
     * @return the value of e_attach_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.ITEM_NAME
     *
     * @param itemName the value for e_attach_item.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.ITEM_NO
     *
     * @return the value of e_attach_item.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.ITEM_NO
     *
     * @param itemNo the value for e_attach_item.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.CHARGE_TYPE
     *
     * @return the value of e_attach_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeType() {
        return chargeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.CHARGE_TYPE
     *
     * @param chargeType the value for e_attach_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.BUY_IDENTITY
     *
     * @return the value of e_attach_item.BUY_IDENTITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getBuyIdentity() {
        return buyIdentity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.BUY_IDENTITY
     *
     * @param buyIdentity the value for e_attach_item.BUY_IDENTITY
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBuyIdentity(String buyIdentity) {
        this.buyIdentity = buyIdentity == null ? null : buyIdentity.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_attach_item.REMARK
     *
     * @return the value of e_attach_item.REMARK
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_attach_item.REMARK
     *
     * @param remark the value for e_attach_item.REMARK
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}