/**
 *
 * @(#) TariffServiceImpl.java
 * @Package com.ls.ner.billing.tariff.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.billing.tariff.bo.AttachItemBo;
import com.ls.ner.billing.tariff.bo.TariffPlanBo;
import com.ls.ner.billing.tariff.dao.ITariffDao;
import com.ls.ner.billing.tariff.service.ITariffService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: TariffServiceImpl.java,v 1.1 2016/02/26 02:34:02 34544 Exp $
 * 
 *          History: 2016年2月23日 下午5:31:52 lipf Created.
 * 
 */
@Service(target = { ServiceType.APPLICATION }, value = "tariffService")
public class TariffServiceImpl implements ITariffService {

	@Autowired(required = true)
	private ITariffDao dao;

	@ServiceAutowired(serviceTypes=ServiceType.RPC, value="seqRpcService")
	private ISeqRpcService seqRpcService;
	
	@ServiceAutowired
	Authentication authentication;
	/**
	 * 
	 */
	@Override
	public void saveTariffPlan(List<AttachItemBo> dataList, TariffPlanBo formBo) {
		AccountBo account = authentication.getCurrentAccount();
		formBo.setOperName(account.getAccountName());
		String planNo=seqRpcService.getDefNo();
		formBo.setPlanNo(planNo);
		dao.saveTariff(formBo);
		String mainNo=seqRpcService.getDefNo();
		formBo.setMainNo(mainNo);
		dao.saveMainItem(formBo);
		if (dataList != null && dataList.size() > 0) {
			for (AttachItemBo attachItemBo : dataList) {
				attachItemBo.setPlanNo(planNo);
				attachItemBo.setAttachNo(seqRpcService.getDefNo());
				dao.saveAttachItem(attachItemBo);
			}
		}
	}

	/**
	 * 
	 */
	@Override
	public void updateTariffPlan(List<AttachItemBo> dataList, TariffPlanBo formBo) {
		if (dataList != null && dataList.size() > 0) {
			for (AttachItemBo attachItemBo : dataList) {
				dao.saveAttachItem(attachItemBo);
			}
		}
		dao.saveTariff(formBo);
	}

	/**
	 * 资费查询
	 */
	@Override
	public List<TariffPlanBo> getTariff(TariffPlanBo bo) {
		return dao.getTariff(bo);
	}

	/**
	 * 资费数目查询
	 */
	@Override
	public int getTariffCount(TariffPlanBo bo) {
		return dao.getTariffCount(bo);
	}

}
