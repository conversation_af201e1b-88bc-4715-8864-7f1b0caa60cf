package com.ls.ner.billing.market.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.integral.IntegeralRuleSignExtendDTO;
import com.ls.ner.billing.api.integral.IntegralRuleDTO;
import com.ls.ner.billing.api.integral.MemberBenefitsDTO;
import com.ls.ner.billing.api.market.service.IIntegeralCustRPCService;
import com.ls.ner.billing.api.market.vo.IntegralCustLogVo;
import com.ls.ner.billing.gift.constant.IntegralEnum;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.IntegeralCustBo;
import com.ls.ner.billing.market.bo.IntegeralCustLogBo;
import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.dao.IIntegeralCustRPCDao;
import com.ls.ner.billing.market.dao.IIntegeralRuleDao;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.service.IIntegeralCustService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.billing.vip.bo.VipLevelCalcConfig;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.service.IVipLevelCalcConfigService;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.order.api.service.IChargeOrderRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service(target = {ServiceType.RPC}, value = "integeralCustRPCService")
public class IIntegeralCustRPCServiceImpl implements IIntegeralCustRPCService {

	private static final Logger logger = LoggerFactory.getLogger(IIntegeralCustRPCServiceImpl.class);

	@Autowired(required = true)
    private IIntegeralRuleDao ruleDao;

	@Autowired(required = true)
    private IIntegeralCustRPCDao dao;

	@Autowired
	private IMemberBenefitsDao memberBenefitsDao;

	@ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "seqRpcService")
    private ISeqRpcService seqRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private IChargeOrderRpcService chargeOrderRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
	private IDefrayAccountRpcService defrayAccountRpcService;

	@ServiceAutowired(value = "IntegeralCustService", serviceTypes = ServiceType.LOCAL)
	private IIntegeralCustService integeralCustService;

	@ServiceAutowired(value = "vipLevelCalcConfigService", serviceTypes = ServiceType.LOCAL)
	private IVipLevelCalcConfigService configService;

	@ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
	private ICouponService couponService;
	//轻享
	private static final List<String> LIGHT_VIP = Collections.unmodifiableList(
			Arrays.asList("01", "02", "03", "04")
	);
	//尊享
	private static final List<String> NOBLE_VIP = Collections.unmodifiableList(
			Arrays.asList("05", "06", "07", "08")
	);
	/**
	 * 更新用户积分并记录积分流水
	 * @param map
	 * 		custId  用户id
	 * 		mobile  用户手机号
	 * 		eventType  事件类型（商品兑换不用）
	 * 		userType 用户等级
	 * 		chargingNum 商品兑换积分
	 * 		chargingPq  充电消费实际充电量
	 *
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> saveIntegralCustInfo(Map<String, Object> map) throws Exception {
		Map<String, Object> retMap = new HashMap<String, Object>();
		IntegeralCustBo bo = new IntegeralCustBo();
		IntegeralCustLogBo logBo = new IntegeralCustLogBo();
		logger.info("====更新用户积分并记录积分流水====入参:"+map);
		int count = 0;
		//更新用户积分
		//判断用户是否已有积分
		if (!StringUtil.isNotBlank(map.get("custId"))) {
			throw new RuntimeException("更新用户积分用户Id为空");
		}
		String custNo = map.get("custId").toString();
		String integralNumber = "0";
		bo.setCustNo(custNo);
		try {
			count = dao.ixExistsCustNum(bo);
			String phone = StringUtil.isNotBlank(map.get("mobile"))?
					map.get("mobile").toString():"";
			bo.setCustPhone(phone);

			logBo.setIntegralNo(seqRpcService.getAppNo());//积分流水号
			logBo.setPhone(phone);
			//获取积分规则
			IntegeralRuleBo integeralRuleBo = new IntegeralRuleBo();
			integeralRuleBo.setEventType(StringUtil.nullToString(map.get("eventType")));
			integeralRuleBo.setUserType(StringUtil.nullToString(map.get("userType")));
			integeralRuleBo.setRuleStatus("02");

			 CodeBO code = codeService.getStandardCode("integralEventType",integeralRuleBo.getEventType(), null);
	         if (code != null) {
	        	 logBo.setEventName(code.getCodeName());
	        	 logBo.setChargeType("01");
	         } else {
	        	 logBo.setEventName(IntegralEnum.PRODUCT_REDEMPTION.getCode());
	        	 logBo.setChargeType("02");
	         }
			logBo.setEventType(integeralRuleBo.getEventType());

	        List<Map<String,Object>> ruleList = ruleDao.getRuleInfoByUser(integeralRuleBo);
	        logger.info("====获取积分规则====ruleList:"+ruleList);
			String custVipType = integeralCustService.getCustVipType(custNo);
	        if (ruleList != null && ruleList.size()>0) {
	        	String eventType = StringUtil.nullToString(ruleList.get(0).get("eventType"));
	        	if (eventType.equals("04")) {//充电消费
					String chargingPqStr1 =  StringUtil.nullToString(ruleList.get(0).get("chargingPq"));
					//实际充电量
					String chargingPqStr2 =  StringUtil.nullToString(map.get("chargingPq"));
					integralNumber = MathUtils.toInt(MathUtils.divide(chargingPqStr2, chargingPqStr1));
					logger.info("====实际充电量====:"+chargingPqStr2+"==获得积分===:"+integralNumber);
				} else {
		        	integralNumber = StringUtil.nullToString(ruleList.get(0).get("money"));
		        	if (eventType.equals("02") || eventType.equals("03") ) {
		        		int maxTimes = StringUtil.digestForString(ruleList.get(0).get("maxTimes"));
		        		int allNum = dao.getLogNum(logBo);
		        		if (allNum >= maxTimes) {
		        			//只记录流水记录
		        			logBo.setChargeNum("0");
		        			logBo.setIntegralNumber(integralNumber);
		        			dao.addIntegeralCustLog(logBo);
							retMap.put("integralNumber","max times");
		        	        retMap.put("msg", "保存成功");
		        	        retMap.put("ret", "200");
		        			return retMap;
						}
					}
					//05代表签到
					if (eventType.equals("05")) {
						String extendJson = StringUtil.nullToString(ruleList.get(0).get("extendJson"));
						if (StringUtil.isBlank(extendJson)) {
							integralNumber = "0";
						} else {
							String maxMoney = JSONUtil.parseObj(extendJson).get("maxMoney", String.class);
							String minMoney = JSONUtil.parseObj(extendJson).get("minMoney", String.class);
							long maxMoneyLong = Long.parseLong(maxMoney);
							long minMoneyLong = Long.parseLong(minMoney);
							long continuousDayNum = Long.parseLong(String.valueOf(map.get("continuousDayNum")));
							long integralNumberLong = minMoneyLong + continuousDayNum;
							if (integralNumberLong >= maxMoneyLong) {
								integralNumberLong = maxMoneyLong;
							}
							integralNumber = String.valueOf(integralNumberLong);
						}
					}
				}
				//判断用户是否付费会员
				Map<String, Object> inMap = new HashMap();
				inMap.put("custId", custNo);
				Map<String,Object> accountMap = defrayAccountRpcService.qryAccount(inMap);
				Boolean vipFlag = true;
				if (null != accountMap.get("vipExpireTime")) {
					String vipExpireTimeStr = StringUtil.nullToString(accountMap.get("vipExpireTime"));
					LocalDateTime vipExpireTime = LocalDateTime.parse(vipExpireTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
					LocalDateTime now = LocalDateTime.now();
					if (vipExpireTime.compareTo(now) > 0) {
						vipFlag = false;
					}
				}
				MemberBenefitsBo userMemberBenefits = new MemberBenefitsBo();
				if (vipFlag){
					//不是付费会员 通过累计充电量充电金额获取会员等级
					Map chargePqMap = chargeOrderRpcService.getChargePqByCustId(custNo);
					logger.info("====获取用户累计充电量====chargePqMap:"+ JsonUtil.obj2Json(chargePqMap));
					VipLevelCalcConfig latestConfig = configService.getLatestConfig();
					if (latestConfig.getCalcMode().equals("quantity")&&chargePqMap!=null&&chargePqMap.containsKey("chargePq")){
						BigDecimal chargePq = (BigDecimal) chargePqMap.get("chargePq");
						//获取用户会员权益
						userMemberBenefits = memberBenefitsDao.getUserMemberBenefits(chargePq);
						logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(userMemberBenefits));
					}
					if (latestConfig.getCalcMode().equals("amount")&&chargePqMap!=null&&chargePqMap.containsKey("chargeAmt")){
						BigDecimal chargeAmt = (BigDecimal) chargePqMap.get("chargeAmt");
						//获取用户会员权益
						userMemberBenefits = memberBenefitsDao.getUserMemberBenefitsByAmt(chargeAmt);
						logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(userMemberBenefits));
					}
				}else {

					MemberBenefitsBo req = new MemberBenefitsBo();
					req.setVipType("02");
					if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
						req.setVipLevel("6");
					}else {
						req.setVipLevel("5");
					}
					userMemberBenefits = memberBenefitsDao.getMemberBenefits(req);
				}
				String isDouble = userMemberBenefits.getIsDouble();
				if ("1".equals(isDouble)){
					//加倍积分
					BigDecimal multiple = userMemberBenefits.getMultiple();
					BigDecimal bigDecimal = new BigDecimal(integralNumber).multiply(multiple).setScale(0, RoundingMode.CEILING);
					integralNumber = bigDecimal.toString();
				}

	        	//记录积分流水
	    		if (count>0) {
	    			//用户已有积分,更新用户积分
	    			//获取用户积分
	    			String num = dao.getCustIntegeralNum(bo);
	    			bo.setIntegralNumber(MathUtils.add(integralNumber,num));
					logBo.setIntegralNumber(MathUtils.add(integralNumber,num));
	    			dao.updateIntegeralCust(bo);
	    		} else {
	    			//添加用户积分
	    			bo.setIntegralNumber(integralNumber);
					logBo.setIntegralNumber(integralNumber);
	    			dao.addIntegeralCust(bo);
	    		}
	    		logBo.setChargeNum(integralNumber);
			} else {
				//没有积分规则
				logBo.setChargeType(StringUtil.nullToString(map.get("chargeType")));
				logBo.setChargeNum(StringUtil.nullToString(map.get("chargingNum")));
				if (count>0) {
					String num = dao.getCustIntegeralNum(bo);
	    			bo.setIntegralNumber(MathUtils.subtract(num,logBo.getChargeNum()));
					logBo.setIntegralNumber(num);
	    			dao.updateIntegeralCust(bo);
				}
			}
			if(StringUtils.isEmpty(logBo.getChargeNum())){
				logBo.setChargeNum("0");
			}
	        dao.addIntegeralCustLog(logBo);
			retMap.put("integralNumber",integralNumber);
	        retMap.put("msg", "保存成功");
	        retMap.put("ret", "200");
		} catch (Exception e) {
			logger.info("====saveIntegralCustInfo===失败:",e);
			retMap.put("integralNumber","not exist");
			retMap.put("msg", e.getMessage());
	        retMap.put("ret", "400");
		}
		return retMap;
	}

	@Override
	public MemberBenefitsDTO userMemberBenefit(Long custId) {
		//判断用户是否付费会员
		Map<String, Object> inMap = new HashMap();
		inMap.put("custId", custId);
		Map<String,Object> accountMap = defrayAccountRpcService.qryAccount(inMap);
		String custVipType = integeralCustService.getCustVipType(custId.toString());
		Boolean vipFlag = true;
		if (null != accountMap.get("vipExpireTime")) {
			String vipExpireTimeStr = StringUtil.nullToString(accountMap.get("vipExpireTime"));
			LocalDateTime vipExpireTime = LocalDateTime.parse(vipExpireTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			LocalDateTime now = LocalDateTime.now();
			if (vipExpireTime.compareTo(now) > 0) {
				vipFlag = false;
			}
		}
		MemberBenefitsBo userMemberBenefits = new MemberBenefitsBo();
		if (vipFlag){
			//不是付费会员 通过累计充电量获取会员等级
			Map chargePqMap = chargeOrderRpcService.getChargePqByCustId(String.valueOf(custId));
			logger.info("====获取用户累计充电量====chargePqMap:"+ JsonUtil.obj2Json(chargePqMap));
			VipLevelCalcConfig latestConfig = configService.getLatestConfig();
			if (latestConfig.getCalcMode().equals("quantity")&&chargePqMap!=null&&chargePqMap.containsKey("chargePq")){
				BigDecimal chargePq = (BigDecimal) chargePqMap.get("chargePq");
				//获取用户会员权益
				userMemberBenefits = memberBenefitsDao.getUserMemberBenefits(chargePq);
				logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(userMemberBenefits));
			}
			if (latestConfig.getCalcMode().equals("amount")&&chargePqMap!=null&&chargePqMap.containsKey("chargeAmt")){
				BigDecimal chargeAmt = (BigDecimal) chargePqMap.get("chargeAmt");
				//获取用户会员权益
				userMemberBenefits = memberBenefitsDao.getUserMemberBenefitsByAmt(chargeAmt);
				logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(userMemberBenefits));
			}
		}else {
			MemberBenefitsBo req = new MemberBenefitsBo();
			req.setVipType("02");
			if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
				req.setVipLevel("6");
			}else {
				req.setVipLevel("5");
			}
			userMemberBenefits = memberBenefitsDao.getMemberBenefits(req);
		}

		return BeanUtil.toBean(userMemberBenefits, MemberBenefitsDTO.class);
	}

	@Override
	public Map<String, Map<String, String>> getCustIntegralNumber(List<String> custIdList){
		Map<String,Object> iMap = new HashMap<String, Object>();
		iMap.put("custIdList",custIdList);
		List<IntegeralCustBo> list = dao.getCustIntegralNumber(iMap);
		Map<String, LocalDateTime> vipExpireMap = defrayAccountRpcService.qryCustVipExpireTime(custIdList);
		if (CollectionUtils.isNotEmpty(list)){
			LocalDateTime now = LocalDateTime.now();
			Map<String, Map<String, String>> custBoMap = list.stream()
					.collect(Collectors.toMap(
							IntegeralCustBo::getCustNo, // 外层键
							bo -> new HashMap<String, String>() {{
								put("integralNumber", bo.getIntegralNumber());
								put("vipLevel", bo.getVipLevel());
								if (vipExpireMap!=null&&vipExpireMap.containsKey(bo.getCustNo())){
									LocalDateTime vipExpireTime = (LocalDateTime) vipExpireMap.get("vipExpireTime");
									if (vipExpireTime.compareTo(now) > 0) {
										put("vipLevel", "PLUS会员");
									}
								}
								put("chargeTime", bo.getChargeTime());
							}}
					));
			return custBoMap;
		}
		return null;
	}

	/**
	 * 获取用户积分
	 *
	 * @param custId
	 * @return
	 */
	@Override
	public String getCustIntegralNumberById(String custId) {
		return dao.getCustIntegralNumberById(custId);
	}

	/**
	 * 获取用户积分流水记录列表
	 * @param queryVo
	 * @return
	 */
	@Override
	public Map getIntegralCustLog(IntegralCustLogVo queryVo) {
		IntegeralCustLogBo bo = new IntegeralCustLogBo();
		bo.setPhone(queryVo.getPhone());
		bo.setChargeType(queryVo.getChargeType());
		int pageNum = queryVo.getPageNum();
		int totalNum = queryVo.getTotalNum();
		// 计算开始位置
		int begin = (pageNum - 1) * totalNum;
		bo.setStart(begin);
		bo.setLimit(totalNum);
		int count = dao.getIntegeralCustLogCount(bo);
		Map returnMap = new HashMap<>();
		if (count>0){
			List<IntegeralCustLogBo> integeralCustLogList = dao.getIntegeralCustLogListByWX(bo);
			if (CollectionUtils.isNotEmpty(integeralCustLogList)){
				for (IntegeralCustLogBo logBo : integeralCustLogList) {
					DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
					DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
					// 解析输入的时间字符串
					LocalDateTime dateTime = LocalDateTime.parse(logBo.getChargeTime(), inputFormatter);
					// 格式化输出，去掉毫秒部分
					String formattedDateTime = dateTime.format(outputFormatter);
					logBo.setChargeTime(formattedDateTime);
				}
			}
			returnMap.put("integralList",integeralCustLogList);
		}
		returnMap.put("count",count);
		return returnMap;
	}

	@Override
	public Map<String, Object> getIntegralTask(String mobile) throws Exception {
		Map returnMap = new HashMap<>();
		returnMap.put("bindCar",0);
		//签到任务 每天一次
		IntegeralCustLogBo checkBo = new IntegeralCustLogBo();
		checkBo.setPhone(mobile);
		checkBo.setEventType(IntegralEnum.CHECK_IN.getCode());
		checkBo.setBeginTime(DateUtil.dateToStr(new Date()));
		checkBo.setEndTime(DateUtil.dateToStr(new Date()));
		int checkCount = dao.getIntegeralCustLogCount(checkBo);
		returnMap.put("checkIn",checkCount);
		//绑定车辆 配置次数
		IntegeralRuleBo integeralRuleBo = new IntegeralRuleBo();
		integeralRuleBo.setEventType(IntegralEnum.BIND_VEHICLE.getCode());
		integeralRuleBo.setRuleStatus("02");
		List<Map<String,Object>> ruleList = ruleDao.getRuleInfoByUser(integeralRuleBo);
		logger.info("====获取积分规则====ruleList:"+ruleList);
		if (ruleList.size()>0){
			int maxTimes = StringUtil.digestForString(ruleList.get(0).get("maxTimes"));
			IntegeralCustLogBo logBo = new IntegeralCustLogBo();
			logBo.setEventName(IntegralEnum.BIND_VEHICLE.getName());
			int allNum = dao.getLogNum(logBo);
			if (allNum >= maxTimes) {
				returnMap.put("bindCar",1);
			}
		}
		return returnMap;
	}

	@Override
	public Map<String, Object> getBenefitsUser(String custId) {
		logger.info("====获取会员权益入参====custId:"+ custId);
		Map<String,Object> returnMap = new HashMap<String,Object>();
		MemberBenefitsBo memberBenefitsBo = new MemberBenefitsBo();
		try {
			//判断用户是否付费会员
			Map<String, Object> inMap = new HashMap();
			inMap.put("custId", custId);
			Map<String,Object> accountMap = defrayAccountRpcService.qryAccount(inMap);
			logger.info("====获取用户是否付费会员====accountMap:"+ accountMap);
			Boolean vipFlag = true;
			if (null != accountMap.get("vipExpireTime")) {
				String vipExpireTimeStr = StringUtil.nullToString(accountMap.get("vipExpireTime"));
				LocalDateTime vipExpireTime = LocalDateTime.parse(vipExpireTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
				LocalDateTime now = LocalDateTime.now();
				if (vipExpireTime.compareTo(now) > 0) {
					vipFlag = false;
				}
			}

			if (vipFlag){
				//不是付费会员
				MemberBenefitsBo bo = new MemberBenefitsBo();
				bo.setCustId(custId);
				List<MemberBenefitsBo> benefitsUser = memberBenefitsDao.getBenefitsUser(bo);
				logger.info("====获取非付费会员用户权益====benefitsUser:"+ JsonUtil.obj2Json(benefitsUser));
				if (benefitsUser.size()>0){
					memberBenefitsBo = benefitsUser.get(0);
				}else {
					Map chargePqMap = chargeOrderRpcService.getChargePqByCustId(custId);
					logger.info("====获取用户累计充电量====chargePqMap:"+ JsonUtil.obj2Json(chargePqMap));
					VipLevelCalcConfig latestConfig = configService.getLatestConfig();
					if (latestConfig.getCalcMode().equals("quantity")&&chargePqMap!=null&&chargePqMap.containsKey("chargePq")){
						BigDecimal chargePq = (BigDecimal) chargePqMap.get("chargePq");
						//获取用户会员权益
						memberBenefitsBo = memberBenefitsDao.getUserMemberBenefits(chargePq);
						logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(memberBenefitsBo));
					}
					if (latestConfig.getCalcMode().equals("amount")&&chargePqMap!=null&&chargePqMap.containsKey("chargeAmt")){
						BigDecimal chargeAmt = (BigDecimal) chargePqMap.get("chargeAmt");
						//获取用户会员权益
						memberBenefitsBo = memberBenefitsDao.getUserMemberBenefitsByAmt(chargeAmt);
						logger.info("====获取非付费会员用户权益====userMemberBenefits:"+ JsonUtil.obj2Json(memberBenefitsBo));
					}
				}
			}else {
				String custVipType = integeralCustService.getCustVipType(custId.toString());

				MemberBenefitsBo req = new MemberBenefitsBo();
				req.setVipType("02");
				if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
					req.setVipLevel("6");
				}else {
					req.setVipLevel("5");
				}
				memberBenefitsBo = memberBenefitsDao.getMemberBenefits(req);
				logger.info("====获取付费会员用户权益====memberBenefitsBo:"+ JsonUtil.obj2Json(memberBenefitsBo));
			}
			returnMap = org.apache.commons.beanutils.BeanUtils.describe(memberBenefitsBo);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		}
		return returnMap;
	}

	@Override
	public List<Map<String, Object>> getIntegralRule() {
		List<Map<String, Object>> list = ruleDao.queryIntegralRule();
		boolean exists = list.stream()
				.anyMatch(map -> "02".equals(map.get("eventType")));
		if(!exists){
			try{
				MarketCondition condition = new MarketCondition();
				condition.setCpnPurpose("1");
				condition.setCpnStatus("1");
				CouponBo couponBo = couponService.getCouponCpnPurposeDetail(condition);
				if(couponBo!=null){
					Map<String, Object> map = new HashMap<>();
					map.put("isCoupon","true");
					map.put("couponName",couponBo.getCpnName());
					map.put("ruleName","绑定车辆");
					map.put("eventType","02");
					list.add(map);
				}
			}catch (Exception e){
				logger.error("getIntegralRule拼接失败:{}",e);
			}
		}


		return list;
	}

	@Override
	public List<IntegralRuleDTO> getIntegralRule2() {
		List<IntegeralRuleBo> integralRule2 = ruleDao.getIntegralRule2();
		return integralRule2.stream().map(integeralRuleBo -> {
			IntegralRuleDTO integralRuleDTO = new IntegralRuleDTO();
			BeanUtils.copyProperties(integeralRuleBo, integralRuleDTO);
			if (StringUtils.isNotBlank(integeralRuleBo.getExtendJson()) &&
					"05".equals(integeralRuleBo.getEventType())) {
				integralRuleDTO.setRuleSignExtend(JSONUtil.parseObj(integeralRuleBo.getExtendJson())
						.toBean(IntegeralRuleSignExtendDTO.class));
			}
			return integralRuleDTO;
		}).collect(Collectors.toList());

	}



}
