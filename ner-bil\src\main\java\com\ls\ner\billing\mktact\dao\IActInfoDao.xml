<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IActInfoDao" >

<!-- 描述:获取资讯信息  创建人:biaoxiangd  创建时间: 2017/6/27 20:41 -->
	<select id="queryActInfo" parameterType="com.ls.ner.billing.mktact.bo.ActInfoBo" resultType="com.ls.ner.billing.mktact.bo.ActInfoBo">
		SELECT
			act_info_id AS actInfoId,act_id AS actId,cober_pic AS coberPic,info_type AS infoType,content,content_url AS contentUrl,link_url AS linkUrl
		FROM e_mkt_act_info
		WHERE ACT_ID = #{actId}
		ORDER BY DATA_OPER_TIME DESC
		limit 1
	</select>
<!-- 描述:新增资讯信息  创建人:biaoxiangd  创建时间: 2017/6/27 17:53 -->
	<insert id="saveActInfo" parameterType="com.ls.ner.billing.mktact.bo.ActInfoBo" useGeneratedKeys="true" keyProperty="actInfoId">
		INSERT INTO e_mkt_act_info(act_id,cober_pic,info_type,content,content_url,link_url,data_oper_time,data_oper_type)
		VALUES(#{actId},'',#{infoType},#{content},#{contentUrl},#{linkUrl},now(),'I')
	</insert>

<!-- 描述:更新资讯信息  创建人:biaoxiangd  创建时间: 2017/6/27 18:01 -->
	<update id="updateActInfo" parameterType="com.ls.ner.billing.mktact.bo.ActInfoBo">
		UPDATE e_mkt_act_info
		<set>
			<if test="coberPic != null">
				cober_pic = #{coberPic},
			</if>
			<if test="infoType != null">
				info_type = #{infoType},
			</if>
			<if test="content != null">
				content = #{content},
			</if>
			<if test="contentUrl != null">
				content_url = #{contentUrl},
			</if>
			<if test="linkUrl != null">
				link_url = #{linkUrl},
			</if>
		</set>
		WHERE act_info_id = #{actInfoId}
		AND act_id = #{actId}

	</update>

	<delete id="deleteActInfoByActId" parameterType="java.lang.Long">
  		DELETE FROM e_mkt_act_info WHERE act_id = #{actId}
  	</delete>
</mapper>