package com.ls.ner.billing.rest.coupon.controller;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.base.service.ITokenService;
import com.ls.ner.billing.api.market.service.IIntegeralCustRPCService;
import com.ls.ner.billing.api.market.vo.BilCouponCondition;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.cust.api.service.ISignRpcService;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.http.HttpClientUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;

import org.apache.commons.collections.map.HashedMap;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:REST05接口 优惠券
 * CouponBillRestController.java
 * 作者：biaoxiangd
 * 创建日期：2017-06-08 17:30
 **/
@Controller
@RequestMapping(value = "/api/open/integral")
public class IntegralBillRestController {

    private static final Logger logger = LoggerFactory.getLogger(IntegralBillRestController.class);

    @ServiceAutowired(value = "tokenService")
    private ITokenService tokenService;

    @ServiceAutowired(value = "integeralCustRPCService", serviceTypes = ServiceType.RPC)
    private IIntegeralCustRPCService service;

    @ServiceAutowired(serviceTypes = {ServiceType.RPC}, value = "signRpcService")
    private ISignRpcService signRpcService;

    /**
     * 描述:REST05-11-02 更新用户积分并记录积分流水
     *
     * @param: [request]
     * @return: org.springframework.http.ResponseEntity<?>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-13 11:57
     */
    @RequestMapping(value = "/v0.1/saveIntegralCustInfo", method = RequestMethod.POST)
    public ResponseEntity<?> saveIntegralCustInfo(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String, Object> inMap = HttpClientUtil.getReqParams(request,false);
//        	Map<String, String> tokenMsg = tokenService.getMsgFromToken();
//            String custId = tokenMsg.get("custId").toString();
//            String mobile = tokenMsg.get("mobile").toString();
//            inMap.put("custId", custId);
//            inMap.put("mobile", mobile);
        	resultMap = service.saveIntegralCustInfo(inMap);
        	return new ResponseEntity<Map>(resultMap, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("更新用户积分并记录积分流水接口获取异常:{}", e);
            resultMap = MapUtils.createFailResult( e.getMessage());
            return new ResponseEntity<Map>(resultMap, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取积分配置
     * <AUTHOR>
     * @date 2025/1/14
     */
    @RequestMapping(value="/v0.1/integralRule")
    public @ResponseBody Object qryIntegralRule() {
        Map<String,Object> resultMap = new HashedMap();
        try {
            List<Map<String, Object>> list = service.getIntegralRule();
            Map<String, String> tokenMsg = tokenService.getMsgFromToken();
            String custId = tokenMsg.get("custId").toString();
            BigDecimal signIntegeral = signRpcService.querySignIntegeral(Long.parseLong(custId));
            resultMap.put("ret",200);
            resultMap.put("list", list);
            resultMap.put("signIntegeral", signIntegeral);

        } catch (Exception e) {
            logger.error("获取用户积分配置列表异常:{}", e);
            resultMap = MapUtils.createFailResult("查询失败！");
        }
        return resultMap;
    }

}
