package com.ls.ner.billing.api.rent.model;

import java.util.List;

import com.ls.ner.billing.api.rent.model.IRangeAware;
import com.ls.ner.billing.api.rent.model.Range;

/**
 * 租车的运行记录集
 * <AUTHOR>
 *
 */
public class RentRun {
	/**
	 * 条目
	 * <AUTHOR>
	 *
	 */
	public static class Item{
		/**
		 * 记录日期 yyyyMMdd格式
		 */
		private String recordDate;
		/**
		 * 里程数 按米记录
		 */
		private long meters;
		/**
		 * 使用时长 按分钟记录
		 */
		private long minutes;
		public String getRecordDate() {
			return recordDate;
		}
		public void setRecordDate(String recordDate) {
			this.recordDate = recordDate;
		}

		public long getMeters() {
			return meters;
		}
		public void setMeters(long meters) {
			this.meters = meters;
		}
		public long getMinutes() {
			return minutes;
		}
		public void setMinutes(long minutes) {
			this.minutes = minutes;
		}
		
		
	}
	
	/**
	 * 日条目
	 * <AUTHOR>
	 *
	 */
	public static class DateItem extends Item {
		boolean firstDay;
		/**
		 * 需要应用的附加项编号（计费时不论必填 非必填 有应用的附加项都选上）
		 */
		String selectedAttachChargeItemNos;
		/**
		 * 当且仅当有分时策略的时候需要的数据
		 */
		private List<PeriodItem> periodItems;
		/**
		 * 日条目起始时间
		 */
		private String startTime;
		/**
		 * 日条目结束时间
		 */
		private String endTime;

		public boolean isFirstDay() {
			return firstDay;
		}

		public void setFirstDay(boolean firstDay) {
			this.firstDay = firstDay;
		}

		public String getSelectedAttachChargeItemNos() {
			return selectedAttachChargeItemNos;
		}

		public void setSelectedAttachChargeItemNos(String selectedAttachChargeItemNos) {
			this.selectedAttachChargeItemNos = selectedAttachChargeItemNos;
		}

		public List<PeriodItem> getPeriodItems() {
			return periodItems;
		}

		public void setPeriodItems(List<PeriodItem> periodItems) {
			this.periodItems = periodItems;
		}

		public String getStartTime() {
			return startTime;
		}

		public void setStartTime(String startTime) {
			this.startTime = startTime;
		}

		public String getEndTime() {
			return endTime;
		}

		public void setEndTime(String endTime) {
			this.endTime = endTime;
		}
		
		
	}
	
	/**
	 * 分时条目
	 * <AUTHOR>
	 *
	 */
	public static class PeriodItem extends Item implements IRangeAware {
		/**
		 * 分时的from和to的格式约定 按照 时分格式，如 00:00 08:00 16:00 24:00
		 */
		private Range range;

		public Range getRange() {
			return range;
		}

		public void setRange(Range range) {
			this.range = range;
		}
		
		
	}

	
	private String appNo;
	
//	private String selectedAttachItemNos;
	
	private List<DateItem> dateItems;

	/**
	 * 是否保存计价结果数据（到数据库）的标志
	 */
	private boolean saveLogFlag;
	

	public String getAppNo() {
		return appNo;
	}

	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}

//	public String getSelectedAttachItemNos() {
//		return selectedAttachItemNos;
//	}
//
//	public void setSelectedAttachItemNos(String selectedAttachItemNos) {
//		this.selectedAttachItemNos = selectedAttachItemNos;
//	}

	public List<DateItem> getDateItems() {
		return dateItems;
	}

	public void setDateItems(List<DateItem> dateItems) {
		this.dateItems = dateItems;
	}

	public boolean isSaveLogFlag() {
		return saveLogFlag;
	}

	public void setSaveLogFlag(boolean saveLogFlag) {
		this.saveLogFlag = saveLogFlag;
	}
	
	private String licenseNo ;


	public String getLicenseNo() {
		return licenseNo;
	}

	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}
	
	
}
