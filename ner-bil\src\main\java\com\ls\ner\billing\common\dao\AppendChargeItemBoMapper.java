package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.AppendChargeItemBo;
import com.ls.ner.billing.common.bo.AppendChargeItemBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AppendChargeItemBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int countByExample(AppendChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int deleteByExample(AppendChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int insert(AppendChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int insertSelective(AppendChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    List<AppendChargeItemBo> selectByExample(AppendChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    AppendChargeItemBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int updateByExampleSelective(@Param("record") AppendChargeItemBo record, @Param("example") AppendChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int updateByExample(@Param("record") AppendChargeItemBo record, @Param("example") AppendChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int updateByPrimaryKeySelective(AppendChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_append_charge_item
     *
     * @mbggenerated Wed Mar 09 16:23:35 CST 2016
     */
    int updateByPrimaryKey(AppendChargeItemBo record);
}