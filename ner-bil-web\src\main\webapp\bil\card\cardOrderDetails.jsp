<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="ls" uri="http://www.longshine.com/taglib/ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充值卡充电订单明细" />
<ls:body>
    <ls:form name="cardOrderForm" id="cardOrderForm">
        <ls:text name="cardDetailId" type="hidden" value="${cardDetailId}"></ls:text>
        <ls:title text="查询条件"></ls:title>
        <table align="center" class="tab_search">
            <tr>
                <td>
                    <ls:label text="使用时间"></ls:label>
                </td>
                <td>
                    <table>
                        <tr>
                            <td>
                                <ls:date name="settleTimeBegin" format="yyyy-MM-dd"/>
                            </td>
                            <td>
                                <span>至</span>
                            </td>
                            <td>
                                <ls:date name="settleTimeEnd" format="yyyy-MM-dd"/>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td colspan="6">
                    <div class="pull-right">
                        <ls:button text="查询" onclick="do_query"/>
                        <ls:button text="清空" onclick="doClear"/>
                    </div>
                </td>
            </tr>
        </table>
        <table align="center" class="tab_search">
            <tr class="chargeOrder">
                <td>
                    <ls:grid url="" showRowNumber="true" name="chargeOrderGrid" height="400px" primaryKey="cardOrderId">
                        <ls:column name="cardOrderId"  caption="主键ID" align="center" hidden="true"/>
                        <ls:column name="orderNo" formatFunc="linkFunc" caption="订单编号" align="center"/>
                        <ls:column name="actualAmt" caption="实际消费金额" align="center" />
                        <ls:column name="giftAmt" caption="赠送消费金额" align="center"/>
                        <ls:pager pageSize="15"></ls:pager>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>

        var cardDetailId = '${cardDetailId}';

        $(function() {
            do_query();
        });

        function do_query() {
                if(!LS.isEmpty(settleTimeBegin.getValue()) && !LS.isEmpty(settleTimeEnd.getValue())){
                    if(settleTimeBegin.getValue()>settleTimeEnd.getValue()){
                        LS.message("info","使用时间不能大于结束时间");
                        return;
                    }
                }

                var data = cardOrderForm.getFormData();
                chargeOrderGrid.query('~/bil/rechargeCard/qryCardOrderDetail',data,function(result) {
            });
        }

        function linkFunc(rowdata) {
            var _orderNo = rowdata.orderNo;
            return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='getOrderDetail(\"" + _orderNo + "\")'>" + _orderNo + "</a></u>";
        }

        window.getOrderDetail = function (_orderNo) {
            LS.dialog('${ordUrl}' + "/order/charge/initChargeDetail?orderNo=" + _orderNo, "充电订单详细信息", 1000, 600, true, null);
        }

        function doClear(){
            cardOrderForm.clear();
        }
    </ls:script>

</ls:body>
</html>