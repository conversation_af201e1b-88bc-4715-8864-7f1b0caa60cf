<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.MainChargeConfgBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.MainChargeConfgBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="MAIN_NO" jdbcType="VARCHAR" property="mainNo" />
    <result column="BILLING_NO" jdbcType="BIGINT" property="billingNo" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="MIN_COST" jdbcType="DECIMAL" property="minCost" />
    <result column="MAX_COST" jdbcType="DECIMAL" property="maxCost" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="CHARG_MODE" jdbcType="VARCHAR" property="chargMode" />
    <result column="CHARGE_TYPE" jdbcType="VARCHAR" property="chargeType" />
    <result column="CHARGE_UNIT" jdbcType="VARCHAR" property="chargeUnit" />
    <result column="CHARGE_NUM" jdbcType="DECIMAL" property="chargeNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, MAIN_NO, BILLING_NO, PRICE, MIN_COST, MAX_COST, DATA_OPER_TIME, DATA_OPER_TYPE, 
    CHARG_MODE, CHARGE_TYPE, CHARGE_UNIT, CHARGE_NUM
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_main_charge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_main_charge_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_main_charge_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_main_charge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_main_charge_config (SYSTEM_ID, MAIN_NO, BILLING_NO, 
      PRICE, MIN_COST, MAX_COST, 
      DATA_OPER_TIME, DATA_OPER_TYPE, CHARG_MODE, 
      CHARGE_TYPE, CHARGE_UNIT, CHARGE_NUM
      )
    values (#{systemId,jdbcType=BIGINT}, #{mainNo,jdbcType=VARCHAR}, #{billingNo,jdbcType=BIGINT}, 
      #{price,jdbcType=DECIMAL}, #{minCost,jdbcType=DECIMAL}, #{maxCost,jdbcType=DECIMAL}, 
      #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}, #{chargMode,jdbcType=VARCHAR}, 
      #{chargeType,jdbcType=VARCHAR}, #{chargeUnit,jdbcType=VARCHAR}, #{chargeNum,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_main_charge_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="mainNo != null">
        MAIN_NO,
      </if>
      <if test="billingNo != null">
        BILLING_NO,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="minCost != null">
        MIN_COST,
      </if>
      <if test="maxCost != null">
        MAX_COST,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
      <if test="chargMode != null">
        CHARG_MODE,
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE,
      </if>
      <if test="chargeUnit != null">
        CHARGE_UNIT,
      </if>
      <if test="chargeNum != null">
        CHARGE_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="mainNo != null">
        #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="billingNo != null">
        #{billingNo,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="minCost != null">
        #{minCost,jdbcType=DECIMAL},
      </if>
      <if test="maxCost != null">
        #{maxCost,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="chargMode != null">
        #{chargMode,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="chargeUnit != null">
        #{chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="chargeNum != null">
        #{chargeNum,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_main_charge_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_config
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.mainNo != null">
        MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      </if>
      <if test="record.billingNo != null">
        BILLING_NO = #{record.billingNo,jdbcType=BIGINT},
      </if>
      <if test="record.price != null">
        PRICE = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.minCost != null">
        MIN_COST = #{record.minCost,jdbcType=DECIMAL},
      </if>
      <if test="record.maxCost != null">
        MAX_COST = #{record.maxCost,jdbcType=DECIMAL},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.chargMode != null">
        CHARG_MODE = #{record.chargMode,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeType != null">
        CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeUnit != null">
        CHARGE_UNIT = #{record.chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeNum != null">
        CHARGE_NUM = #{record.chargeNum,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_config
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      BILLING_NO = #{record.billingNo,jdbcType=BIGINT},
      PRICE = #{record.price,jdbcType=DECIMAL},
      MIN_COST = #{record.minCost,jdbcType=DECIMAL},
      MAX_COST = #{record.maxCost,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      CHARG_MODE = #{record.chargMode,jdbcType=VARCHAR},
      CHARGE_TYPE = #{record.chargeType,jdbcType=VARCHAR},
      CHARGE_UNIT = #{record.chargeUnit,jdbcType=VARCHAR},
      CHARGE_NUM = #{record.chargeNum,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_config
    <set>
      <if test="mainNo != null">
        MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="billingNo != null">
        BILLING_NO = #{billingNo,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="minCost != null">
        MIN_COST = #{minCost,jdbcType=DECIMAL},
      </if>
      <if test="maxCost != null">
        MAX_COST = #{maxCost,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="chargMode != null">
        CHARG_MODE = #{chargMode,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="chargeUnit != null">
        CHARGE_UNIT = #{chargeUnit,jdbcType=VARCHAR},
      </if>
      <if test="chargeNum != null">
        CHARGE_NUM = #{chargeNum,jdbcType=DECIMAL},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.MainChargeConfgBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_main_charge_config
    set MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      BILLING_NO = #{billingNo,jdbcType=BIGINT},
      PRICE = #{price,jdbcType=DECIMAL},
      MIN_COST = #{minCost,jdbcType=DECIMAL},
      MAX_COST = #{maxCost,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      CHARG_MODE = #{chargMode,jdbcType=VARCHAR},
      CHARGE_TYPE = #{chargeType,jdbcType=VARCHAR},
      CHARGE_UNIT = #{chargeUnit,jdbcType=VARCHAR},
      CHARGE_NUM = #{chargeNum,jdbcType=DECIMAL}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>