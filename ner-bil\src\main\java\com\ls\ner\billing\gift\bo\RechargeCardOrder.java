package com.ls.ner.billing.gift.bo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.bo
 * @ClassName: RechargeCardOrder
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/5/15 17:52
 * @Version: 1.0
 */
public class RechargeCardOrder  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long cardOrderId;

    /**
     * 充值卡ID
     */
    private Long cardId;

    /**
     * 充值卡明细ID
     */
    private Long cardDetailId;

    /**
     * 卡号来源：0 后管卡密兑换 1 小程序 or APP积分兑换
     */
    private String cardSource;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 实际消费金额
     */
    private BigDecimal actualAmt;

    /**
     * 赠送部分消费金额
     */
    private BigDecimal giftAmt;

    /** 充电订单结算时间开始 */
    private String settleTimeBegin;

    /** 充电订单结算时间结束 */
    private String settleTimeEnd;

    public String getSettleTimeBegin() {
        return settleTimeBegin;
    }

    public void setSettleTimeBegin(String settleTimeBegin) {
        this.settleTimeBegin = settleTimeBegin;
    }

    public String getSettleTimeEnd() {
        return settleTimeEnd;
    }

    public void setSettleTimeEnd(String settleTimeEnd) {
        this.settleTimeEnd = settleTimeEnd;
    }

    public Long getCardOrderId() {
        return cardOrderId;
    }

    public void setCardOrderId(Long cardOrderId) {
        this.cardOrderId = cardOrderId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public Long getCardDetailId() {
        return cardDetailId;
    }

    public void setCardDetailId(Long cardDetailId) {
        this.cardDetailId = cardDetailId;
    }

    public String getCardSource() {
        return cardSource;
    }

    public void setCardSource(String cardSource) {
        this.cardSource = cardSource;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getActualAmt() {
        return actualAmt;
    }

    public void setActualAmt(BigDecimal actualAmt) {
        this.actualAmt = actualAmt;
    }

    public BigDecimal getGiftAmt() {
        return giftAmt;
    }

    public void setGiftAmt(BigDecimal giftAmt) {
        this.giftAmt = giftAmt;
    }
}
