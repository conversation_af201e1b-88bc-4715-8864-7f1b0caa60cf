<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.ItemPeriodsConfigBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.ItemPeriodsConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="MAIN_NO" jdbcType="VARCHAR" property="mainNo" />
    <result column="PRICES_METHOD" jdbcType="VARCHAR" property="pricesMethod" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="SN" jdbcType="DECIMAL" property="sn" />
    <result column="BEGIN_TIME" jdbcType="VARCHAR" property="beginTime" />
    <result column="END_TIME" jdbcType="VARCHAR" property="endTime" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, MAIN_NO, PRICES_METHOD, PRICE, SN, BEGIN_TIME, END_TIME, DATA_OPER_TIME, 
    DATA_OPER_TYPE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_item_periods_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_item_periods_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_item_periods_config
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_item_periods_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_item_periods_config (SYSTEM_ID, MAIN_NO, PRICES_METHOD, 
      PRICE, SN, BEGIN_TIME, 
      END_TIME, DATA_OPER_TIME, DATA_OPER_TYPE
      )
    values (#{systemId,jdbcType=BIGINT}, #{mainNo,jdbcType=VARCHAR}, #{pricesMethod,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{sn,jdbcType=DECIMAL}, #{beginTime,jdbcType=VARCHAR}, 
      #{endTime,jdbcType=VARCHAR}, #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_item_periods_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="mainNo != null">
        MAIN_NO,
      </if>
      <if test="pricesMethod != null">
        PRICES_METHOD,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="beginTime != null">
        BEGIN_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="mainNo != null">
        #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="pricesMethod != null">
        #{pricesMethod,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=DECIMAL},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_item_periods_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_item_periods_config
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.mainNo != null">
        MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      </if>
      <if test="record.pricesMethod != null">
        PRICES_METHOD = #{record.pricesMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        PRICE = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.sn != null">
        SN = #{record.sn,jdbcType=DECIMAL},
      </if>
      <if test="record.beginTime != null">
        BEGIN_TIME = #{record.beginTime,jdbcType=VARCHAR},
      </if>
      <if test="record.endTime != null">
        END_TIME = #{record.endTime,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_item_periods_config
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      MAIN_NO = #{record.mainNo,jdbcType=VARCHAR},
      PRICES_METHOD = #{record.pricesMethod,jdbcType=VARCHAR},
      PRICE = #{record.price,jdbcType=DECIMAL},
      SN = #{record.sn,jdbcType=DECIMAL},
      BEGIN_TIME = #{record.beginTime,jdbcType=VARCHAR},
      END_TIME = #{record.endTime,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_item_periods_config
    <set>
      <if test="mainNo != null">
        MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      </if>
      <if test="pricesMethod != null">
        PRICES_METHOD = #{pricesMethod,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="sn != null">
        SN = #{sn,jdbcType=DECIMAL},
      </if>
      <if test="beginTime != null">
        BEGIN_TIME = #{beginTime,jdbcType=VARCHAR},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.ItemPeriodsConfigBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_item_periods_config
    set MAIN_NO = #{mainNo,jdbcType=VARCHAR},
      PRICES_METHOD = #{pricesMethod,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      SN = #{sn,jdbcType=DECIMAL},
      BEGIN_TIME = #{beginTime,jdbcType=VARCHAR},
      END_TIME = #{endTime,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>