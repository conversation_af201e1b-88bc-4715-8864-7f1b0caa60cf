package com.ls.ner.billing.vip.service.impl;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.service.IMemberBenefitsService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @description MemberBenefitsServiceImpl
 */
@Service(target = { ServiceType.LOCAL }, value = "memberBenefitsService")
public class MemberBenefitsServiceImpl implements IMemberBenefitsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MemberBenefitsServiceImpl.class);

    @Autowired
    private IMemberBenefitsDao dao;

    @Override
    public List<MemberBenefitsBo> list() {
        return dao.list();
    }

    @Override
    public int count() {
        return dao.count();
    }

    @Override
    public MemberBenefitsBo getMemberBenefits(MemberBenefitsBo bo) {
        return dao.getMemberBenefits(bo);
    }

    @Override
    public void save(MemberBenefitsBo memberBenefitsBo) {
        dao.updateEVipBenefits(memberBenefitsBo);
    }
}
