package com.ls.ner.billing.task.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.service.IIntegeralCustService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;

@Controller
@RequestMapping("/task/integral")
public class IntegralCustTaskController {
	
	private static final Logger log = LoggerFactory.getLogger(IntegralCustTaskController.class);
	
	@ServiceAutowired(value = "IntegeralCustService", serviceTypes = ServiceType.LOCAL)
    private IIntegeralCustService integeralCustService;
	
	/**
	 * 
	 * @return
	 */
	@RequestMapping("/excuteIntegeralCustTask")
    public @ResponseBody Object excuteIntegeralCustTask() {
        log.info("开始执行用户积分清零更新任务调度");
        try {
        	integeralCustService.excuteIntegeralCustTask();
            return "0";
        } catch (Exception e) {
            log.error("执行用户积分清零更新任务调度失败", e);
            return "-1";
        }
    }

    /**
     * 会员专享优惠 每月发券
     * @return
     */
    @RequestMapping("/exclusiveOffer")
    public @ResponseBody Object exclusiveOffer() {
        log.info("开始执行会员专享优惠----每月发券");
        try {
            integeralCustService.excuteExclusiveOfferTask();
            return "0";
        } catch (Exception e) {
            log.error("执行专享优惠----每月发券任务调度失败", e);
            return "-1";
        }
    }
	

}
