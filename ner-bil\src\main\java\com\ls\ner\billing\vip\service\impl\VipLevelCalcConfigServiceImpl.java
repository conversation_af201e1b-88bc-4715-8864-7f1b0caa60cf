package com.ls.ner.billing.vip.service.impl;

import com.ls.ner.billing.vip.bo.VipLevelCalcConfig;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.dao.IVipLevelCalcConfigDao;
import com.ls.ner.billing.vip.service.IVipLevelCalcConfigService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Service(target = { ServiceType.LOCAL }, value = "vipLevelCalcConfigService")
public class VipLevelCalcConfigServiceImpl implements IVipLevelCalcConfigService {

    @Autowired
    private IVipLevelCalcConfigDao configDao;

    @Autowired
    private IMemberBenefitsDao memberBenefitsDao;

    @Override
    public VipLevelCalcConfig getLatestConfig() {
        return configDao.selectLatestConfig();
    }

    @Override
    @Transactional
    public void saveConfig(VipLevelCalcConfig config) {
        config.setUpdateTime(new Date());
        if ("quantity".equals(config.getCalcMode())) {
            config.setAmountPerKwh(null);
        } else if ("amount".equals(config.getCalcMode())) {
            config.setKwhPerAmount(null);
        }
        configDao.insertConfig(config);

        memberBenefitsDao.selectRegularVipConfigList().forEach(item -> {
            if (config.getKwhPerAmount()!=null){
                item.setRankMin(new BigDecimal(item.getRankAmtMin()).multiply(config.getKwhPerAmount()).longValue());
                item.setRankMax(new BigDecimal(item.getRankAmtMax()).multiply(config.getKwhPerAmount()).longValue());
            }
            if (config.getAmountPerKwh()!=null){
                item.setRankAmtMin(new BigDecimal(item.getRankMin()).multiply(config.getAmountPerKwh()).longValue());
                item.setRankAmtMax(new BigDecimal(item.getRankMax()).multiply(config.getAmountPerKwh()).longValue());
            }
            memberBenefitsDao.editRegularVipConfig(item);
        });
    }
}
 