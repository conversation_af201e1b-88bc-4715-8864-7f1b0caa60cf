<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型调价">
		<style type="text/css">
.textEditor textarea {
	resize: none
}
</style>
</ls:head>
<ls:body>
		<ls:form name="billingAdjustInitForm" id="billingAdjustInitForm">
		    <ls:text name="subBe" type="hidden"></ls:text>
				<table class="tab_search">
						<tr>
								<td>
										<ls:label text="车型" ref="autoModelName"/>
								</td>
								<td>
										<ls:text name="autoModelNo" type="hidden"></ls:text>
										<ls:text imageKey="search" onClickImage="getModelRental" name="autoModelName" required="true" readOnly="true" onchanged="changeAutoModelName"></ls:text>
								</td>
								<td>
										<ls:label text="适用日期" ref="applyDate" />
								</td>
								<td>
										<ls:date name="applyDate" required="true" format="yyyy-MM"></ls:date>
								</td>
								<td>
										<ls:button text="清空" onclick="doClear" />
										<ls:button text="查询" onclick="doSearch" />
								</td>
						</tr>
				</table>
		</ls:form>
		<%-- <ls:tab>
				<ls:tabpage name="billingAdjustGridTab" text="列表视图"> --%>
						<table align="center" class="tab_search">
								<tr>
										<td>
												<ls:grid url="" name="billingAdjustGrid" height="350px"  width="100%" showCheckBox="true" singleSelect="false">
														<ls:gridToolbar name="billingAdjustGridBar">
																<ls:gridToolbarItem imageKey="set" onclick="doAdjust" text="调价"></ls:gridToolbarItem>
														</ls:gridToolbar>
														<ls:column caption="日期" name="applyDate" />
														<ls:column caption="节假日" name="" />
														<ls:column caption="定价内容" name="details"/><%--  formatFunc="showDetails"  --%>
														<ls:column caption="最低消费" name="minLimitPrice" />
														<ls:column caption="最高消费" name="maxLimitPrice" />
														<ls:column caption="发布时间" name="createTime" />
														<ls:pager pageSize="15,20"></ls:pager>
												</ls:grid>
										</td>
								</tr>
						</table>
				<%-- </ls:tabpage>
				<ls:tabpage name="billingAdjustCalendarTab" text="日历视图">
						<ls:form name="adjustCalendarForm" id="adjustCalendarForm">
								<ls:calendar url="" name="adjustCalendar" refresh="false" refreshTime="5000">
								</ls:calendar>
						</ls:form>
				</ls:tabpage>
		</ls:tab> --%>
		<ls:script>
		window.doSearch = doSearch;
    function doSearch(){
      if(!billingAdjustInitForm.valid()){
        return;
      }
      var params = billingAdjustInitForm.getFormData();
      billingAdjustGrid.query("~/billing/adjust/getBillingAdjustList",params);
    }
    
    function doClear(){
      billingAdjustInitForm.clear();
      autoModelNo.setValue();
      subBe.setValue();
    }
    
    function doAdjust(){
      var items = billingAdjustGrid.getCheckedItems();
      if(items.length == 0){
	      LS.message('info','请选择一条记录');
	      return;
	    }
	    var applyDates = [];
      for(var i=0;i < items.length;i++){
        applyDates.push(items[i].applyDate);
      }
      var applyDate = applyDates.join(",");
      var planNo = billingAdjustGrid.getItems()[0].planNo;
      var autoModelNoValue = autoModelNo.getValue();
      var autoModelNameValue = autoModelName.getValue();
      LS.dialog("~/billing/adjust/initAdjustForm?planNo="+planNo+"&autoModelNo="+autoModelNoValue+"&applyDate="+applyDate+"&autoModelName="+encodeURI(encodeURI(autoModelNameValue)),"租赁调价",1000,600,true);
      //window.location.href=rootUrl+"billing/adjust/initAdjustForm?planNo="+planNo+"&autoModelNo="+autoModelNoValue+"&applyDate="+applyDate+"&autoModelName="+encodeURI(encodeURI(autoModelNameValue));
    }
	function getModelRental(){
      LS.dialog("<%=astPath %>"+"/ast/brand/init?flag=1","选择车型",1000,500,true);
    }
    function changeAutoModelName(){
      if(LS.isEmpty(autoModelName.getValue())){
        autoModelNo.setValue();
      }
    }
    window.setModelRental = setModelRental;
    function setModelRental(item) {
       autoModelNo.setValue(item.modelId);  
       autoModelName.setValue(item.modelName);
       subBe.setValue(item.subBe);
      // subBeName.setValue(item.subBeName);
    }
    
    function showDetails(rowData){
      var billingFunc = rowData.billingFunc;
      var sb = "";
	    if('0101'==billingFunc.mainChargeItem.chargeWay||'0103'==billingFunc.mainChargeItem.chargeWay){
	      //按时间
	      if("01"==billingFunc.mainChargeItem.chargeMode){
	        //标准
	        sb=sb+billingFunc.mainChargeItem.timeChargeItem.price+"元/"
	            + billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc+"<br />";
	      } else if("02"==billingFunc.mainChargeItem.chargeMode||"03"==billingFunc.mainChargeItem.chargeMode){
	        //分时
	        var rangeChargeItem = billingFunc.mainChargeItem.timeChargeItem.rangeChargeItems;
	        for (var i=0; i < rangeChargeItem.length ;i++) {
	         sb=sb+rangeChargeItem[i].range.desc+billingFunc.mainChargeItem.timeChargeItem.priceUnit.unitName+":"+ rangeChargeItem[i].price+"元/"+rangeChargeItem[i].priceUnit.desc+"<br />";
	        }
	      }
	    }
      if('0102'==billingFunc.mainChargeItem.chargeWay||'0103'==billingFunc.mainChargeItem.chargeWay){
	      //按里程
        if("01"==billingFunc.mainChargeItem.chargeMode){
	        //标准
	        sb=sb+billingFunc.mainChargeItem.millChargeItem.price+"元/"
              + billingFunc.mainChargeItem.millChargeItem.priceUnit.desc+"<br />";
        } else if("02"==billingFunc.mainChargeItem.chargeMode||"03"==billingFunc.mainChargeItem.chargeMode){
	        //分时
	        var rangeChargeItem = billingFunc.mainChargeItem.millChargeItem.rangeChargeItems;
          for (var i=0; i < rangeChargeItem.length ;i++) {
           sb=sb+rangeChargeItem[i].range.desc+billingFunc.mainChargeItem.millChargeItem.priceUnit.unitName+":"+ rangeChargeItem[i].price+"元/"+rangeChargeItem[i].priceUnit.desc+"<br />";
          }
	      }
	    }
	    return sb;
	  }
		</ls:script>
</ls:body>
</html>