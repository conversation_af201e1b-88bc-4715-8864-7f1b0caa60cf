<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑优惠券推广方式" />
<ls:body>
		<ls:form name="pushForm" id="pushForm">
				<table align="center" class="tab_search">
						<tr>
								<td width="100px">
										<ls:label text="推广方式" />
								</td>
								<td width="200px">
									<ls:select name="pushWay">
										<ls:options property="pushWayList" scope="request" text="codeName" value="codeValue" />
									</ls:select>
									<!-- <ls:text name="pushWayName" value="客户领取" readOnly="true"></ls:text>
									<ls:text name="pushWay" property="pushWay" visible="false"></ls:text> -->
									<ls:text name="cpnNo" property="cpnNo" visible="false"></ls:text>
									<ls:text name="pushOperType" value="01" visible="false"></ls:text>
									<ls:text name="pushStatus"  value="1" visible="false"></ls:text>
								</td>
						</tr>
						
						<tr>
								<td width="100px">
										<ls:label text="推广渠道" ref="pushChannel"/>
								</td>
								<td width="200px">
									<ls:checklist type="checkbox" name="pushChannel"  required="true" >
										<ls:checkitem value="01" text="手机"></ls:checkitem>
										<ls:checkitem value="02" text="微信"></ls:checkitem>
										<ls:checkitem value="03" text="支付宝"></ls:checkitem>
									</ls:checklist>
								</td>
								<td><ls:button text="全选" onclick="checkAll"></ls:button></td>
								
						</tr>
						
						<tr>
							<td colspan="3">
								<div class="pull-right">
									<ls:button text="推广" onclick="save"></ls:button>
									<ls:button text="返回" onclick="cancel"></ls:button>
								</div>
								
							</td>
						</tr>
						
				</table>
		</ls:form>
		
		<ls:script>
		
		function checkAll(){
			pushChannel.checkAll();
		}
		function save(){
			LS.confirm('推广后客户即可领取，确认推广吗？',function(result){
				if(result){
					pushForm.submit("~/market/coupon/savePushCoupon",function(data){
		   		 		if(data){
			   		 		LS.message("info","推广成功");
			   		 		LS.parent().parentQquery();
			   		 		LS.window.close();
			   		 	}else{
			   		 		LS.message("error","系统异常");
			   		 	}	
				   });
				}
			});	
			
		}
		function cancel(){
			LS.window.close();
		}
		
		</ls:script>
</ls:body>
</html>