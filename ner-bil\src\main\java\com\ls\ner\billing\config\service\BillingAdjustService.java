/**
 *
 * @(#) BillingAdjustService.java
 * @Package com.ls.ner.billing.config.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.config.bo.BillingConfigFunc;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.bo.CalendarDataVo;
import com.ls.ner.billing.config.service.impl.BillingAdjustServiceImpl;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年4月10日 下午2:05:24   lipf   Created.
 *           
 */
@Service(target = { ServiceType.APPLICATION }, value = "billingAdjustService")
public class BillingAdjustService implements IBillingAdjustService {
	@Autowired
	BillingAdjustServiceImpl billingAdjustServiceImpl;
	
	@Override
	public List<BillingConfigFunc> getBillingAdjustList(BillingConfigQueryCondition condition) throws IllegalAccessException, InvocationTargetException {
		return billingAdjustServiceImpl.getBillingAdjustList(condition);
	}

	@Override
	public int getBillingAdjustListCount(BillingConfigQueryCondition condition) {
		return billingAdjustServiceImpl.getBillingAdjustListCount(condition);
	}

	@Override
	public List<CalendarDataVo> getBillingAdjustCalendar(BillingConfigQueryCondition condition) {
		return billingAdjustServiceImpl.getBillingAdjustCalendar(condition);
	}

}
