package com.ls.ner.billing.mktact.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.condition.MarketActFormCondition;
import org.springframework.web.multipart.MultipartFile;


public interface IMarketActService {

	/**
	 * 获取活动 by PrimaryKey
	 *
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	public MarketActBo queryByPrimaryKey(Long actId);

	/**
	 * 获取活动 listMap
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryMarketActivesMap(Map<String, Object> map);

	/**
	 * 获取营销活动列表
	 * @param condition
	 * @return
	 * <AUTHOR>
	 */
	public List<MarketActBo> qryMarketActives(MarketActBo condition);

	/**
	 * 活动关闭或删除
	 *
	 * @param marketActBo
	 * <AUTHOR>
	 */
	public void operateMarketAct(String oprType, Long actId);

	/**
	 * 活动关闭或删除
	 *
	 * @param marketActBo
	 * <AUTHOR>
	 */
	public void operateMarketActNew(String oprType, Long actId);

	/**
	 * 保存营销活动
	 *
	 * @param marketActBo
	 * <AUTHOR>
	 */
	public Long saveMarketAct(MarketActBo marketAct, MultipartFile file) throws Exception;

	/**
	 * 活动状态轮询job
	 *
	 * <AUTHOR>
	 */
	public void marketStateJob();

	/**
	 * @param bo
	 * @description 查询注册送/邀请送，送的优惠券
	 * <AUTHOR>
	 * @create 2017-08-09 10:31:15
	 */
	public List<CouponBo> queryActCpn(MarketActBo bo);

	/**
	 * @param
	 * @description  根据活动查询优惠卷
	 * <AUTHOR>
	 * @create 2018-06-04 14:47:41
	 */
	List<Map<String, Object>> queryActCoupon(String actId);

	/**
	 * <AUTHOR>
	 * @description 判断活动是否交叉
	 * @create 2019/5/27 10:37
	 *
	 * @return
	 */
	JSONObject checkActIsCross(MarketActFormCondition marketActFormCondition);

	/**
	 * <AUTHOR>
	 * @description 限时折扣--保存
	 * @create 2019/5/27 23:47
	 *
	 * @return
	 */
	public long saveMarketActNew(MarketActBo marketAct) throws Exception;

	/**
	 * <AUTHOR>
	 * @description 保存act_scope
	 * @create 2019/5/27 23:48
	 *
	 * @return
	 */
	public void saveActScope(MarketActFormCondition marketActFormCondition, String actId, String actType)throws Exception;


	/**
	 * <AUTHOR>
	 * @description
	 * @create 2019/5/29 19:08
	 *
	 * @return
	 */
	public  Map<String,Object> qryMarketActivesNew(MarketActBo condition)throws Exception;
}
