package com.ls.ner.billing.charge.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;



/**
 * <AUTHOR>
 * @dateTime 2016-04-08
 * @description 桩电价下发信息   E_CHCBILL_PILESEND
 */
public interface IChcbillPileSendDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息  E_CHCBILL_PILESEND
	 */
	public List<ChcbillPileSendBo> queryChcbillPileSends(ChcbillSendQueryCondition bo);

	ChcbillPileSendBo queryChcbillStationPileSendsLogLast(ChcbillSendQueryCondition sendQueryCondition);

	public String queryChcbillPileSendsNumId(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息条数  E_CHCBILL_PILESEND
	 */
	public int queryChcbillPileSendsNum(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 新增桩电价下发信息   E_CHCBILL_PILESEND
	 */
	public void insertChcbillPileSend(ChcbillPileSendBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 更新桩电价下发信息   E_CHCBILL_PILESEND
	 */
	public void updateChcbillPileSend(ChcbillPileSendBo bo);

	public void updateChcbillPileSendById(ChcbillPileSendBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 删除桩电价下发信息   E_CHCBILL_PILESEND
	 */
	public void deleteChcbillSend(String chcNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-12-02
	 * @description 查询充电下发成功数
	 */
	public Map queryPileSendSuccessCount(@Param("chcNo") String chcNo, @Param("pileNo") String pileNo);

	/**
	  *@Description: 查找需要重新下发的记录
	  *@Author: qianghuang
	  *@Time: 2017/11/17 16:59
	  */
    Map queryBilSendFailRecord(Map paramMap);

	/**
	  *@Description:查询需要重新下发的记录
	  *@Author: qianghuang
	  *@Time: 2017/11/17 17:39
	  */
	Map queryReSendRecord(Map paramMap);

	/**
	  *@Description: 判断今天是否有成功下发记录
	  *@Author: qianghuang
	  *@Time: 2017/11/17 17:46
	  */
	int querySendSucceedCount(Map paramMap);

	/**
	  *@Description: 查询最新计费模型
	  *@Author: qianghuang
	  *@Time: 2017/12/13 17:05
	  */
	Map qyeruBilConfLast(Map<String, String> pileMap);

	/**
	 * 查询计费下发记录
	 * @param queryMap
	 * @return
	 */
    Map queryXpPileSendSuccessCount(Map<String, Object> queryMap);

    /**
     * @param pileMap
     * @description 查询最新计费模型
     * <AUTHOR>
     * @create 2018-04-27 14:55:43
     */
	Map qyeruBilConfLastNew(Map<String, String> pileMap);

	List<Map<String,Object>> queryChcbillPileSendsByNo(ChcbillSendQueryCondition condition);

	Map<String,Object> queryChcbillPileSendByNo(Map<String,Object> inMap);

    List<Map<String, Object>> queryStationBillConfs();

	List<Map<String, Object>> queryPileBillConfs();

}
