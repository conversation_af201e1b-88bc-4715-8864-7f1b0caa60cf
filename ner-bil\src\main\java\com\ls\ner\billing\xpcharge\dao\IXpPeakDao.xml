<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.xpcharge.dao.IXpPeakDao">

    <resultMap id="PeakTimeMap" type="com.ls.ner.billing.xpcharge.bo.PeakTimeBo">
        <result column="SYSTEM_ID" property="systemId"/>
        <result column="STATE" property="state"/>
        <result column="EFT_DATE" property="eftDate"/>
        <result column="INV_DATE" property="invDate"/>
        <result column="OPERATOR" property="operator"/>
    </resultMap>

    <resultMap id="PeakRelMap" type="com.ls.ner.billing.xpcharge.bo.PeakRelBo">
        <result column="ID" property="id"/>
        <result column="REL_ID" property="relId"/>
        <result column="CHC_NO" property="chcNo"/>
        <result column="LAST_STATE" property="lastState"/>
        <result column="CURRENT_STATE" property="currentState"/>
        <result column="EFT_DATE" property="eftDate"/>
        <result column="INV_DATE" property="invDate"/>
        <result column="GMT_CREATE" property="createTime"/>
        <result column="ORG_CODE" property="orgCode"/>
        <result column="CHC_NAME" property="chcName"/>
        <result column="CHC_STATUS" property="chcStatus"/>
        <result column="BILL_CTL_MODE" property="billCtlMode"/>
        <result column="CHARGE_MODE" property="chargeMode"/>
    </resultMap>

    <insert id="insertTimePeak" parameterType="com.ls.ner.billing.xpcharge.bo.PeakTimeBo">
        insert into e_peak_times(STATE, EFT_DATE, INV_DATE, OPERATOR)
        values (#{state}, str_to_date(#{eftDate}, '%Y-%m-%d'), str_to_date(#{invDate}, '%Y-%m-%d'), #{operator})
    </insert>

    <select id="selectPeakTimeEffective" resultMap="PeakTimeMap">
        select SYSTEM_ID,
               STATE,
               date_format(EFT_DATE, '%Y-%m-%d') EFT_DATE,
               date_format(INV_DATE, '%Y-%m-%d') INV_DATE,
               OPERATOR
        from e_peak_times
        order by GMT_CREATE desc limit 1
    </select>

    <insert id="insertTimePeakRels">
        insert into e_peak_rel(CHC_NO)
        values
        <foreach collection="peakRelBos" item="rel" separator=",">
            (#{rel.chcNo})
        </foreach>
    </insert>

    <delete id="deleteTimePeakRelByChcNo">
        delete
        from e_peak_rel
        where CHC_NO = #{chcNo}
    </delete>

    <select id="selectPeakRelAll" resultMap="PeakRelMap">
        select REL_ID, CHC_NO, date_format(GMT_CREATE, '%Y-%m-%d') GMT_CREATE
        from e_peak_rel
        order by GMT_CREATE desc
    </select>

    <select id="pagePeakRelBoList" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition"
            resultMap="PeakRelMap">
        SELECT conf.ORG_CODE,
               conf.CHC_NO,
               conf.CHC_NAME,
               conf.CHC_STATUS,
               conf.EFT_DATE
        FROM e_peak_rel rel
                 LEFT JOIN e_charge_billing_conf conf ON rel.CHC_NO = conf.CHC_NO
        order by conf.EFT_DATE desc
        <if test="end!=null and end!=0">
            limit #{begin} ,#{end}
        </if>
    </select>

    <select id="countPageRelBoList" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition"
            resultType="java.lang.Integer">
        select count(*)
        FROM e_peak_rel rel
        LEFT JOIN e_charge_billing_conf conf ON rel.CHC_NO = conf.CHC_NO
    </select>

    <select id="pageUnPeakRelBoList" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition"
            resultMap="PeakRelMap">
        SELECT
            conf.ORG_CODE,
            conf.CHC_NO,
            conf.CHC_NAME,
            conf.BILL_CTL_MODE,
            conf.CHARGE_MODE,
            conf.CHC_STATUS,
            conf.EFT_DATE
        FROM
            e_charge_billing_conf conf
                LEFT JOIN (
                SELECT
                    period.CHC_NO,
                    sum( CASE WHEN period.TIME_FLAG = '1' THEN 1 ELSE 0 END ) AS jCount,
                    sum( CASE WHEN period.TIME_FLAG = '2' THEN 1 ELSE 0 END ) AS fCount
                FROM
                    e_charge_periods period
                GROUP BY
                    period.CHC_NO
            ) aaa ON conf.CHC_NO = aaa.CHC_NO
        WHERE
            conf.BILL_CTL_MODE = '2'
          AND conf.CHARGE_MODE = '0202'
          AND conf.CHC_STATUS = '1'
          AND conf.CHC_NO NOT IN ( SELECT DISTINCT CHC_NO FROM e_peak_rel )
          AND aaa.jCount = 0
          AND aaa.fCount &gt; 0
            <if test="orgCodeList !=null">
                and conf.ORG_CODE in
                <foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by conf.EFT_DATE desc
        <if test="end!=null and end!=0">
            limit #{begin} ,#{end}
        </if>
    </select>

    <select id="countPageUnRelBoList" parameterType="com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition"
            resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        e_charge_billing_conf conf
        LEFT JOIN (
        SELECT
        period.CHC_NO,
        sum( CASE WHEN period.TIME_FLAG = '1' THEN 1 ELSE 0 END ) AS jCount,
        sum( CASE WHEN period.TIME_FLAG = '2' THEN 1 ELSE 0 END ) AS fCount
        FROM
        e_charge_periods period
        GROUP BY
        period.CHC_NO
        ) aaa ON conf.CHC_NO = aaa.CHC_NO
        WHERE
        conf.BILL_CTL_MODE = '2'
        AND conf.CHARGE_MODE = '0202'
        AND conf.CHC_STATUS = '1'
        AND conf.CHC_NO NOT IN ( SELECT DISTINCT CHC_NO FROM e_peak_rel )
        AND aaa.jCount = 0
        AND aaa.fCount &gt; 0
        <if test="orgCodeList !=null">
            and conf.ORG_CODE in
            <foreach item="item" index="index" collection="orgCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectPeakRelByChcNo" resultMap="PeakRelMap">
        select REL_ID,
               CHC_NO,
               date_format(GMT_CREATE, '%Y-%m-%d') GMT_CREATE
        from e_peak_rel
        where CHC_NO = #{chcNo}
    </select>

    <select id="selectPeakRelByChcNos" resultMap="PeakRelMap">
        select REL_ID,
               CHC_NO,
               date_format(GMT_CREATE, '%Y-%m-%d') GMT_CREATE
        from e_peak_rel
        <where>
            <if test="chcNos !=null">
                and CHC_NO in
                <foreach item="item" index="index" collection="chcNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertTimePeakRelLogs">
        insert into e_peak_rel_log(CHC_NO,LAST_STATE,CURRENT_STATE,EFT_DATE,INV_DATE)
        values
        <foreach collection="peakRelBos" item="rel" separator=",">
            (#{rel.chcNo},
            #{rel.lastState},
            #{rel.currentState},
            str_to_date(#{rel.eftDate},'%Y-%m-%d'),
            str_to_date(#{rel.invDate},'%Y-%m-%d'))
        </foreach>
    </insert>

    <insert id="insertPeriodPeaks">
        insert into e_charge_periods_peak
        (CHC_NO,SN,BEGIN_TIME,END_TIME,PRICE,TIME_FLAG,ITEM_NO)
        values
        <foreach collection="periodsBos" item="peak" separator="," index="index">
            (#{peak.chcNo},
            #{peak.sn},
            #{peak.beginTime},
            #{peak.endTime},
            #{peak.price},
            #{peak.timeFlag},
            #{peak.itemNo})
        </foreach>
    </insert>

    <select id="selectPeriodPeakListByChcNo" parameterType="com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition"
            resultType="com.ls.ner.billing.charge.bo.ChargePeriodsBo">
        select CHC_NO     chcNo,
               SN         sn,
               BEGIN_TIME beginTime,
               END_TIME   endTime,
               PRICE      price,
               TIME_FLAG  timeFlag,
               ITEM_NO    itemNo
        from e_charge_periods_peak
        <where>
            <if test="chcNo !=null and chcNo !=''">
                and CHC_NO =#{chcNo}
            </if>
            <if test="itemNo !=null and itemNo !=''">
                and ITEM_NO =#{itemNo}
            </if>
        </where>
        order by sn asc
        <if test="end!=null and end!=0">
            limit #{begin} ,#{end}
        </if>
    </select>

    <delete id="deletePeriodPeakByChcNo">
        delete
        from e_charge_periods_peak
        where CHC_NO = #{chcNo}
    </delete>

    <select id="queryChargePeriods" resultType="java.util.Map">
        select
        b.CHC_NO,b.SN,b.BEGIN_TIME,b.END_TIME,b.PRICE,b.TIME_FLAG,b.ITEM_NO
        from
        e_charge_periods_peak b
        <where>
            <if test="chcNo !=null and chcNo !=''">
                and b.CHC_NO =#{chcNo}
            </if>
            <if test="chcNos !=null">
                and b.CHC_NO in
                <foreach item="item" index="index" collection="chcNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by b.CHC_NO,b.sn asc
    </select>


    <!-- 通过stationId查询下发的计费模型 -->
    <select id="getStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        a.CHC_NO chcNo,
        a.STATION_ID stationId,
        b.SYSTEM_ID systemId,
        b.SN,
        b.BEGIN_TIME beginTime,
        b.END_TIME endTime,
        FORMAT(b.PRICE, CAST(#{place} AS SIGNED )) PRICE,
        b.TIME_FLAG timeFlag
        FROM
        e_charge_billing_conf AS a
        INNER JOIN e_charge_periods_peak AS b ON a.CHC_NO = b.CHC_NO
        <where>
            a.CHC_STATUS = '1'
            and NOW() >= a.EFT_DATE
            <if test="stationId !=null and stationId !=''">
                and a.STATION_ID =#{stationId}
            </if>
            <if test="chcNo !=null and chcNo !=''">
                and a.CHC_NO =#{chcNo}
            </if>
            <if test="itemNo !=null and itemNo !=''">
                and b.ITEM_NO =#{itemNo}
            </if>
        </where>
        order by b.TIME_FLAG asc
    </select>

    <!-- 通过stationId查询下发的新版计费模型 -->
    <select id="getNewStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        t.CHC_NO     chcNo,
        t.STATION_ID stationId,
        b.SYSTEM_ID  systemId,
        b.SN,
        b.BEGIN_TIME beginTime,
        b.END_TIME endTime,
        FORMAT(b.PRICE, CAST(#{place} AS SIGNED )) PRICE,
        b.TIME_FLAG timeFlag,
        b.ITEM_NO itemNo
        FROM
        (
        SELECT
        a.CHC_NO,
        c.STATION_ID
        FROM
        e_charge_billing_conf a,
        e_station_billing c
        <where>
            a.CHC_STATUS = '1'
            and a.CHC_NO = c.CHC_NO
            <if test="stationId !=null and stationId !=''">
                and c.STATION_ID =#{stationId}
            </if>
        </where>
        ) t
        INNER JOIN e_charge_periods_peak b ON t.CHC_NO = b.CHC_NO
        <where>
            <if test="itemNo !=null and itemNo !=''">
                and b.ITEM_NO =#{itemNo}
            </if>
        </where>
        ORDER BY
        b.TIME_FLAG ASC
    </select>

    <select id="selectChcNoByStation" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT t.CHC_NO
        FROM (
                 SELECT a.CHC_NO,
                        c.STATION_ID
                 FROM e_charge_billing_conf a,
                      e_station_billing c
                 where a.CHC_STATUS = '1'
                   and a.CHC_NO = c.CHC_NO
                   and c.STATION_ID = #{stationId}
             ) t
    </select>

    <!-- 通过stationId查询下发的新版计费模型 -->
    <select id="getPileStationChargePeriods" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        t.CHC_NO     chcNo,
        t.STATION_ID stationId,
        b.SYSTEM_ID  systemId,
        b.SN,
        b.BEGIN_TIME beginTime,
        b.END_TIME endTime,
        FORMAT(b.PRICE, 2) PRICE,
        b.TIME_FLAG timeFlag
        FROM
        (
        SELECT
        a.CHC_NO,
        c.STATION_ID
        FROM
        e_charge_billing_conf a,
        e_pile_billing c
        <where>
            a.CHC_STATUS = '1'
            and a.CHC_NO = c.CHC_NO
            <if test="stationId !=null and stationId !=''">
                and c.STATION_ID =#{stationId}
            </if>
            <if test="pileId !=null and pileId !=''">
                and c.PILE_ID =#{pileId}
            </if>
        </where>
        ) t
        INNER JOIN e_charge_periods_peak b ON t.CHC_NO = b.CHC_NO
        <where>
            <if test="itemNo !=null and itemNo !=''">
                and b.ITEM_NO =#{itemNo}
            </if>
        </where>
        ORDER BY
        b.TIME_FLAG ASC
    </select>

    <select id="getChargeBillRltModel" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        aa.APP_NO appNo,
        aa.CHC_NO chcNo,
        aa.BILL_CTL_MODE billCtlMode,
        aa.CHARGE_MODE chargeMode,
        Round(aa.CHARGE_PRICE,2) chargePrice,
        Round(aa.price,2) periodPrice,
        aa.TIME_FLAG timeFlag,
        GROUP_CONCAT( aa.time ) times,
        aa.ITEM_CHARGE_MODE itemChargeMode,
        aa.ATTACH_ITEM_NOS attachItemNos
        FROM
        ( SELECT
        b.APP_NO,
        b.CHC_NO,
        c.BILL_CTL_MODE,
        c.CHARGE_MODE,
        c.CHARGE_PRICE,
        a.BEGIN_TIME,
        a.END_TIME,
        a.PRICE,
        a.TIME_FLAG,
        CONCAT( a.BEGIN_TIME, '~', a.END_TIME ) time,
        c.ITEM_CHARGE_MODE,
        c.ATTACH_ITEM_NOS
        FROM
        e_charge_billing_rlt b
        LEFT JOIN e_charge_billing_conf c ON b.CHC_NO = c.CHC_NO
        LEFT JOIN e_charge_periods_peak a ON b.CHC_NO = a.CHC_NO
        <where>
            b.APP_NO = #{orderNo}
            <if test="itemNo != null and itemNo != ''">
                AND a.ITEM_NO = #{itemNo}
            </if>
        </where>
        ) aa
        GROUP BY
        aa.TIME_FLAG
    </select>

    <update id="updateSendLogSendStatus">
        update
            E_CHCBILL_PILESEND_LOG
            set
                PILE_SEND_STATUS = '004'
        where CHC_NO = #{chcNo}
        and PILE_SEND_STATUS = '001'
    </update>

    <select id="selectStationByChcNo" resultType="java.lang.String">
        SELECT t.STATION_ID
        FROM (
                 SELECT a.CHC_NO,
                        c.STATION_ID
                 FROM e_charge_billing_conf a,
                      e_station_billing c
                 where a.CHC_STATUS = '1'
                   and a.CHC_NO = c.CHC_NO
                   and a.CHC_NO = #{chcNo}
             ) t
    </select>
</mapper>