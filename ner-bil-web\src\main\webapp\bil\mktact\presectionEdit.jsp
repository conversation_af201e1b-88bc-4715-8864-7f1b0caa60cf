<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="编辑段落"/>
<script type="text/javascript" src="../../pub/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="preSectionForm" name="preSectionForm">
		<ls:text name="actId" property="actId" type="hidden"/>
		<ls:text name="presentSectionId" property="presentSectionId" type="hidden"/>
		<ls:text name="release" property="release" type="hidden"/>
		<ls:text name="presentBalType" property="presentBalType" type="hidden"/>
		<table>
			<tr>
				<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
			</tr>
		</table>
		<table class="tab_search">
			<tr style="display: none">
				<td>
					<ls:label text="段落关系" ref="sectionRelaType"/>
				</td>
				<td colspan="6">
					<ls:checklist name="sectionRelaType" property="sectionRelaType" type="radio" required="true">
						<ls:checkitems property="presentSectRelaTypeCheckList" scope="request" text="codeName"
									   value="codeValue"/>
					</ls:checklist>
				</td>
			</tr>
			<tr style="display: none">
				<td>
					<ls:label text="段落名称" ref="sectionName"/>
				</td>
				<td colspan="4">
					<ls:text name="sectionName" property="sectionName" required="true"/>
				</td>
				<td colspan="2"></td>
			</tr>
			<tr>
				<td style="display: none">
					<ls:label text="段落类型" ref="sectionType"/>
				</td>
				<td colspan="2" style="display: none">
					<ls:checklist name="sectionType" property="sectionType" type="radio" required="true">
						<ls:checkitems property="presentSectTypeCheckList" scope="request" text="codeName"
									   value="codeValue"/>
					</ls:checklist>
				</td>
				<td>
					<ls:label text="赠送最大值" ref="maxValue"/>
				</td>
				<td>
					<ls:text name="maxValue" property="maxValue" required="true"
							 onValidate="VD.positiveInteger(maxValue)"/>
				</td>
				<td>
					<ls:label text="计算结果精度" ref="calcPrecision"/>
				</td>
				<td>
					<ls:select name="calcPrecision" property="calcPrecision" required="true">
						<ls:options property="calcPrecisionList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
			</tr>
			<tr id="sectionDetTr">
				<td width="100px">
					<ls:label text="段落明细" ref="presentSectionDets"/>
				</td>
				<td colspan="6">
					<ls:grid url="" name="sectionDetGrid" primaryKey="sectionDetSn" cellEdit="true" treeGrid="true"
							 expandColumn="value" expand="true" treeGridModel="sectionDet" height="100px" width="100%">
						<ls:gridToolbar name="sectionDetGridBar">
							<ls:gridToolbarItem imageKey="add" onclick="doAddSectionDet" text="新增"/>
							<ls:gridToolbarItem imageKey="delete" onclick="doDelSectionDet" text="删除"/>
						</ls:gridToolbar>
						<ls:column caption="预存段落ID" name="sectionDetId" editableEdit="false" hidden="true"/>
						<ls:column caption="排序号" name="sectionDetSn" editableEdit="false"/>
						<ls:column caption="起始值(>=)" name="refCeil" editableEdit="true">
							<ls:textEditor required="true" type="number"/>
						</ls:column>
						<ls:column caption="终止值(<)" name="refFloor" editableEdit="true">
							<ls:textEditor required="true" type="number"/>
						</ls:column>
						<ls:column caption="赠送值" name="baseValue" editableEdit="true">
							<ls:textEditor required="true" type="number"/>
						</ls:column>
						<ls:column caption="赠送积分" name="integralNum" editableEdit="true">
							<ls:textEditor required="false" type="number"/>
						</ls:column>
					</ls:grid>
					<ls:text name="presentSectionDets" property="presentSectionDets" required="true" type="hidden"/>
				</td>
			</tr>
			<tr>
				<td>
					<ls:label text="赠送周期类型" ref="presentCycleType"/>
				</td>
				<td colspan="2">
					<ls:select name="presentCycleType" property="presentCycleType" onchanged="cycleTypeChg"
							   required="true">
						<ls:options property="cycleTypeList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td id="cycleUseTd1" style="display:none">
					<ls:label text="周期使用下限" ref="cycleLower"/>
				</td>
				<td id="cycleUseTd2" style="display:none">
					<ls:text name="cycleLower" required="true"/>
				</td>
				<td id="cycleUseTd3" style="display:none">
					<ls:label text="周期使用上限" ref="cycleUpper"/>
				</td>
				<td id="cycleUseTd4" style="display:none">
					<ls:text name="cycleUpper" required="true"/>
				</td>
			</tr>
			<tr id="bgnTr" style="display:none">
				<td>
					<ls:label text="生效时间" ref="beginTimeType"/>
				</td>
				<td>
					<ls:checklist name="beginTimeType" property="beginTimeType" type="radio" required="true"
								  onchanged="bgnTimeTypeChg">
						<ls:checkitems property="dateTypeCheckList" scope="request" text="codeName" value="codeValue"/>
					</ls:checklist>
				</td>
				<td id="bgnTd1" style="display:none">
					<ls:text name="beginTimeDuration" property="beginTimeDuration" required="true"/>
				</td>
				<td id="bgnTd2" style="display:none">
					<ls:select name="beginTimeUnit" property="beginTimeUnit" required="true">
						<ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td id="bgnTd3" style="display:none" colspan="2">
					<ls:date name="beginCalcObject" property="beginCalcObject" format="yyyy-MM-dd"/>
				</td>
				<td></td>
			</tr>
			<tr id="endTr" style="display:none">
				<td>
					<ls:label text="失效时间" ref="endTimeType"/>
				</td>
				<td>
					<ls:checklist name="endTimeType" property="endTimeType" type="radio" required="true"
								  onchanged="endTimeTypeChg">
						<ls:checkitems property="dateTypeCheckList" scope="request" text="codeName" value="codeValue"/>
					</ls:checklist>
				</td>
				<td id="endTd1" style="display:none">
					<ls:text name="endTimeDuration" property="endTimeDuration" required="true"/>
				</td>
				<td id="endTd2" style="display:none">
					<ls:select name="endTimeUnit" property="endTimeUnit" required="true">
						<ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td id="endTd3" style="display:none" colspan="2">
					<ls:date name="endCalcObject" property="endCalcObject" format="yyyy-MM-dd"/>
				</td>
				<td></td>
			</tr>
			<tr id="cycleTr" style="display:none">
				<td>
					<ls:label text="周期数" ref="cycleUnitCount"/>
				</td>
				<td width="90px">
					<ls:text name="cycleUnitCount"/>
				</td>
				<td width="60px">
					<ls:select name="cycleUnit" property="cycleUnit" required="true">
						<ls:options property="cycleUnitList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>
				</td>
				<td>
					<ls:label text="间隔偏移量" ref="cycleDuration"/>
				</td>
				<td>
					<ls:text name="cycleDuration"/>
				</td>
				<td>
					<ls:label text="偏移天数" ref="cycleDurationDays"/>
				</td>
				<td>
					<ls:text name="cycleDurationDays"/>
				</td>
			</tr>
			<tr>
				<td>
					<ls:label text="规则描述" ref="presentRuleDesc"/>
				</td>
				<td colspan="6">
					<ls:text name="presentRuleDesc" property="presentRuleDesc" type="textarea" required="true"/>
				</td>
			</tr>
		</table>
	</ls:form>
	<table>
		<tr>
			<td>
				<div class="pull-right">
					<ls:button text="上一步" onclick="doUpStep"/>
					<%--<ls:button text="下一步" onclick="doRelease"/>--%>
					<ls:button text="保存" onclick="doSave" />
					<ls:button text="发布" onclick="doRelease" />
				</div>
			</td>
		</tr>
	</table>
	<ls:script>
        doSearch();
        function doSearch() {
            var actIdValue = actId.getValue();
            var balTypeValue = presentBalType.getValue();
            var sectionIdValue = presentSectionId.getValue();
            if (!LS.isEmpty(actIdValue)) {
                sectionDetGrid.query('~/cpn/sections?actId=' + actIdValue + "&presentSectionId=" + sectionIdValue + "&presentBalType=" + balTypeValue, null);
            }
        }
		cycleTypeChg();
        function cycleTypeChg() {
            var cycleType = presentCycleType.getValue();
            if (cycleType == "1") {
                $('#bgnTr').show();
                $('#endTr').show();
                $('#cycleTr').hide();
                $('#cycleUseTd1').hide();
                $('#cycleUseTd2').hide();
                $('#cycleUseTd3').hide();
                $('#cycleUseTd4').hide();
            } else if (cycleType == "2") {
                $('#bgnTr').hide();
                $('#endTr').hide();
                $('#cycleTr').show();
                $('#cycleUseTd1').show();
                $('#cycleUseTd2').show();
                $('#cycleUseTd3').show();
                $('#cycleUseTd4').show();
            }
        }
		bgnTimeTypeChg();
        function bgnTimeTypeChg() {
            var bgnType = beginTimeType.getValue();
            if (bgnType == "1") {
                $('#bgnTd1').hide();
                $('#bgnTd2').hide();
                $('#bgnTd3').show();
            } else if (bgnType == "2") {
                $('#bgnTd1').show();
                $('#bgnTd2').show();
                $('#bgnTd3').hide();
            }
        }

		endTimeTypeChg();
        function endTimeTypeChg() {
            var endType = endTimeType.getValue();
            if (endType == "1") {
                $('#endTd1').hide();
                $('#endTd2').hide();
                $('#endTd3').show();
            } else if (endType == "2") {
                $('#endTd1').show();
                $('#endTd2').show();
                $('#endTd3').hide();
            }
        }

        //新增
        function doAddSectionDet() {
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                var data = {sectionDetSn: '1', refCeil: '', refFloor: '', integralNum: 0};
                sectionDetGrid.appendItem(data);
            } else {
                if (LS.isEmpty(items[items.length - 1].refFloor)) {
                    LS.message("error", "上一条终止值不能为空！");
                    return;
                }
                var data = {
                    sectionDetSn: items.length + 1,
                    refCeil: parseInt(items[items.length - 1].refFloor),
                    refFloor: '',
                    integralNum: 0
                };
                sectionDetGrid.appendItem(data);
            }
        }

        //删除
        function doDelSectionDet() {
            var item = sectionDetGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择要删除的记录");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 1 || item.sectionDetSn == items[0].sectionDetSn) {
                LS.message("error", "第一条记录不能删除");
                return;
            }
            var newItems = [];
            sectionDetGrid.setCell(parseInt(item.sectionDetSn) + 1, "refCeil", items[parseInt(item.sectionDetSn) - 2].refFloor);
            items = sectionDetGrid.getItems();
            for (var i = 0; i < items.length; i++) {
                if (item.sectionDetSn != items[i].sectionDetSn) {
                    newItems.push(items[i]);
                } else {
                    if (!LS.isEmpty(item.sectionDetId)) {
                        LS.ajax("~/sections/cpn/delete", {sectionDetId: item.sectionDetId}, function (e) {
                        });
                    }
                }
            }
            sectionDetGrid.removeAllItems();
            for (var i = 0; i < newItems.length; i++) {
                newItems[i].sectionDetSn = i + 1;
            }
            sectionDetGrid.appendItem(newItems);
        }

        function sectionDetValidate(value, eValidator) {
            var rowid = parseInt(eValidator.rowid);
            return endValidate(sectionDetGrid, rowid, value);
        }

        function endValidate(grid, rowid, value) {
            var beginValue = '', endValue = value;
            var isValid = true;
            if (!LS.isEmpty(endValue)) {
                isValid = /^\d{1,}$/.test(value);
            }
            return {isValid: isValid, message: "请输入整数"}

            beginValue = grid.getCell(rowid, "refCeil");
            if (LS.isEmpty(beginValue) || LS.isEmpty(endValue)) {
                isValid = true;
            }
            else {
                var begin = parseFloat(beginValue);
                var end = parseFloat(endValue);
                isValid = (beginValue < endValue);
            }
            if (isValid) {
                grid.setCell(rowid + 1, "refCeil", value);
            }
            return {isValid: isValid, message: "结束数值必须大于开始数值"}
        }

        //保存
        function doSave() {
            if (!preSectionForm.valid()) {
                return;
            }
            if (!sectionDetGrid) {
                LS.message("error", "出错了，请联系管理员");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                LS.message("info", "段落明细请至少设置一条记录！");
                return;
            }
            var newItems = [];
            for (var m = 0; m < items.length; m++) {
                newItems.push(JSON.stringify(items[m]));
            }
            presentSectionDets.setValue(newItems.join(";"));

            preSectionForm.submit("~/sections/saving", function (e) {
                if (e.items[0][0] == "T") {
                    presentSectionId.setValue(e.items[0][1]);
                    LS.message("info", "保存成功");
                    LS.parent().doSearch();
                }
                else {
                    LS.message("info", "操作失败或网络异常，请重试！");
                }
            });
        }

        //上一步
        function doUpStep() {
            var actIdValue = actId.getValue();
            window.location.href = "<%=request.getContextPath() %>/marketacts/edit?actId=" + actIdValue;
        }

        //发布
        function doRelease() {
            doSave();
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                LS.message("info", "请先完成段落规则维护！");
                return;
            }
            <%--release.setValue("true");--%>

			var params = preSectionForm.getFormData();
			LS.ajax("~/marketacts/releaseAct",params,function(data){
				if(data.successful){
					LS.message("info",data.resultHint);
					LS.parent().doSearch();
					LS.window.close();
				}else{
					LS.message("info",data.resultHint);
				}
			});

			<%--preSectionForm.submit("~/marketacts/saving", function (e) {--%>
                <%--if (e.items[0][0] == "T") {--%>
                <%--}--%>
                <%--else {--%>
                    <%--LS.message("info", "操作失败或网络异常，请重试！");--%>
                <%--}--%>
            <%--});--%>
        }
	</ls:script>
</ls:body>
</html>