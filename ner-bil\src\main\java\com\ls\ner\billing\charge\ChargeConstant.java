package com.ls.ner.billing.charge;


import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class ChargeConstant {

	/**
	 * 充电计费配置--状态     有效、无效、草稿      标准代码：BizConstants.CodeType.VALID_FLAG
	 */
	public static class validFlag{
		/**
		 *有效
		 */
		public static final String ENABLE = "1";
		
		/**
		 *无效
		 */
		public static final String UNENABLE = "0";
		
		/**
		 *草稿
		 */
		public static final String DRAFT = "2";

		/**
		 * 待生效
		 */
		public static final String WAIT_EFFECTIVE = "3";
		
	}

	/**
	 * 计费模式       标准代码：BizConstants.CodeType.CHC_BILLING_CHARGE_MODE
	 */
	public static class chcBillingChargeMode{
		/**
		 *01标准
		 */
		public static final String STANDARD = "0201";
		
		/**
		 *02分时  
		 */
		public static final String TIME_SHARE = "0202";
		
	}

	/**
	 * 充电计价单位      标准代码：BizConstants.CodeType.CHC_BILLING_PRICE_UNIT
	 */
	public static class chcBillingPriceUnit{
		/**
		 * kWh
		 */
		public static final String KWH = "0201";
		
		/**
		 *次
		 */
		public static final String COUNT = "0200";

		/**
		 *小时
		 */
		public static final String HOUR = "0202";

		/**
		 *半小时
		 */
		public static final String HALF_HOUR = "0203";
	}

	/**
	 * 费控模式  标准代码:
	 */
	public static class billCtlMode{
		/**
		 * 本地
		 */
		public static final String LOCAL = "1";
		
		/**
		 * 远程
		 */
		public static final String REMOTE = "2";
	}

	/**
	 * 数据类型 标准代码:
	 */
	public static class dateType{

		/**
		 * 1 BOSS计费，推送数据为示数
		 */
		public static final String BOSS_ONE = "1";

		/**
		 * 2 BOSS计费，推送数据为电量
		 */
		public static final String BOSS_TWO = "2";

		/**
		 * 3  桩计费，推送数据为量、价、费
		 */
		public static final String REMOTE_ONE = "3";
	}

	/**
	 * 采集数据类型    标准代码:BizConstants.CodeType.GATHER_DATA_TYPE
	 */
	public static class gatherDataType{

		/**
		 * 1表示数
		 */
		public static final String NUM = "1";

		/**
		 * 2电量
		 */
		public static final String ELEC = "2";

		/**
		 * 3量价费
		 */
		public static final String PRICE = "3";
	}

	/**
	 * 电价下发状态    标准代码:BizConstants.CodeType.CHC_BILL_SEND_STATUS
	 */
	public static class chcBillSendStatus{

		/**
		 * 01未下发
		 */
		public static final String NOT = "01";

		/**
		 * 02下发失败
		 */
		public static final String FAIL = "02";

		/**
		 * 03部分成功
		 */
		public static final String PART = "03";

		/**
		 * 04下发成功
		 */
		public static final String SUCCESS = "04";

		/**
		 * 05正在下发
		 */
		public static final String UNDER_WAY = "05";
	}

	/**
	 * 充电桩下发状态  0 失败  1 成功
	 */
	public static class successFlag{

		/**
		 * 0 失败
		 */
		public static final String FAIL = "003";

		/**
		 * 1 成功
		 */
		public static final String SUCCESS = "001";

		/**
		 * 2下发中
		 */
		public static final String UNDER_WAY = "002";
	}
	
	/**
	 * 时段标识：1：尖；2：峰；3：平；4：谷
	 * <AUTHOR>
	 *
	 */
	public static class TimeFlag{

		/**
		 * 1：尖
		 */
		public static final String TOP_PEAK = "1";

		/**
		 * 2：峰
		 */
		public static final String PEAK = "2";
		
		/**
		 * 3：平
		 */
		public static final String LEVEL = "3";
		
		/**
		 * 4：谷
		 */
		public static final String VALLEY = "4";
	}

	public static class itemTypeNo{

		/**
		 * 所有服务费代码枚举
		 */
		public final static Set<String> SERVICE_CODE_SET = new HashSet<>();
		/**
		 * 电费
		 */
		public static final String CHARGE = "1000000000";

		/**
		 * 	充电前占位费
		 */
		public static final String CHARGE_BFOR = "1000002001";

		/**
		 * 充电后占位费
		 */
		public static final String CHAEGE_AFT = "1000002002";


		/**
		 * 	充电前插枪占位费
		 */
		public static final String PLUG_BFOR = "1000002003";


		/**
		 * 充电后拔枪占位费
		 */
		public static final String PLUG_AFT = "1000002004";


		/**
		 * 服务费
		 */
		public static final String SERVICE = "1000001000";


		/**
		 * 低速附加费
		 */
		public static final String CHARGE_LOW = "1000003000";

		static {
			SERVICE_CODE_SET.addAll(Arrays.asList(SERVICE,CHARGE,CHAEGE_AFT,CHARGE_BFOR,CHARGE_LOW,PLUG_AFT,PLUG_BFOR));
		}

	}
}
