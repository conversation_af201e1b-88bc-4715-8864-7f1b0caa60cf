<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="尖峰电价设置" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
    <ls:title text="待调整计费"></ls:title>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="~/billing/xpcbc/peakrellist" name="chargePeakRelGrid" height="300px;" width="100%" showCheckBox="false" singleSelect="true" caption="尖峰计费列表" primaryKey="chcNo" >
					<ls:gridToolbar name="chargePeakRelGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doPeakSet" text="计费版本选择"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="计费编号" name="chcNo"/>
					<ls:column caption="计费名称" name="chcName" formatFunc="showDetail"/>
					<ls:column caption="状态" name="chcStatusName" />
					<ls:column caption="生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:column caption="操作" name="opr"  formatFunc="opr"/>
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid>
            </td>
		</tr>
    </table>
    <ls:form id="peakTimeForm" name="peakTimeForm">
        <ls:title text="尖峰调价"></ls:title>
        <table align="center">
           <%-- <tr>
                <td>
                    <ls:label text="尖峰计费状态" ref="state"/>
                </td>
                <td>
                    <ls:checklist  type="radio" name="state" property="state" required="true"
                                   value="1">
                        <ls:checkitem text="启用" value="1"/>
                        <ls:checkitem text="禁用" value="2"/>
                    </ls:checklist>
                </td>
                <td>
                </td>
                <td>
                </td>
            </tr>--%>
            <tr>
                <td><ls:label text="生效日期" ref="eftDate" /></td>
                <td><ls:date name="eftDate" property="eftDate" required="true" format="yyyy-MM-dd"/></td>
                <td><ls:label text="失效日期" ref="invDate" /></td>
                <td><ls:date name="invDate" property="invDate" required="true" format="yyyy-MM-dd" onValidate="VD.dateTimeOfTodayValidate(invDate)"/></td>
            </tr>
        </table>
        <ls:title text="调价说明"></ls:title>
        <div>
            （1）执行时段 <br/>
                &nbsp;&nbsp;尖峰电价每天的执行时段为<%=request.getAttribute("peakBillingTimeSlot").toString()%>，新增2个尖时段。<br/>
            （2）尖峰电价水平<br/>
            &nbsp;&nbsp;尖峰电价为峰分时电价的<%=request.getAttribute("peakBillingRate").toString()%>倍。<br/>
        </div>
        <table>
            <tr>
                <td>
                    <div class="pull-right">
                        <ls:button text="返回" onclick="doReturn" />
                        <ls:button text="保存" onclick="doSave" />
                    </div>
                </td>
            </tr>
        </table>
    </ls:form>
	<ls:script>
        window.chargePeakRelGrid = chargePeakRelGrid;

        //尖峰电价设置
        function doPeakSet(){
            LS.dialog("~/billing/xpcbc/peakrelpage","计费版本选择",900, 530,true, null);
        }

        window.doSearchRel = doSearchRel;
        function doSearchRel(){
<%--            var params = chargePeakRelGrid.getFormData();--%>
            chargePeakRelGrid.query('~/billing/xpcbc/peakrellist',null,function(){});
        }

        function doReturn(){
            setTimeout("LS.window.close()",200);
        }
        function showDetail(rowdata){
            return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowDetail(\"" + rowdata.chcNo +"\")'>"+rowdata.chcName+"</a>";
        }

        window.doShowDetail=doShowDetail;
        function doShowDetail(upChcNo){
            LS.dialog("~/billing/xpcbc/newShowChargeBillingConf?chcNo="+upChcNo,"查看充电费用配置",1000, 600,true, null);
        }

        window.doDel =doDel;
        function doDel(chcNo){
            LS.confirm('确定要移除该计费?',function(result){
                if(result){
                    LS.ajax("~/billing/xpcbc/removepeakrel?chcNos="+chcNo,null,function(data){
                        if(data==true){
                            LS.message("info","移除成功");
                            doSearchRel();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }
        window.doSave =doSave;
        function doSave(){
            if(!peakTimeForm.valid()){
                return;
            }
            LS.confirm('确定要保存?',function(result){
                if(result){
                    <%--peakTimeForm.submit("~/billing/xpcbc/savepeaktime",function(data){
                        if(data.successful==true){
                            LS.message("info","保存成功");
                            LS.parent().doSearch();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });--%>
                    var param = peakTimeForm.getFormData();
                    LS.ajax("~/billing/xpcbc/savepeaktime",param,function(data){
                        if(data==true){
                            LS.message("info","保存成功");
                            doSearchRel();
                            LS.parent().doSearch();
                            return;
                        }else{
                            LS.message("info","操作失败或网络异常，请重试！");
                            return;
                        }
                    });
                }
            });
        }

        function getOrgs(){
            var initValue = [];
            js_util.selectOrgTree(false, null, true, initValue, false, function(node){
                orgCodeName.setValue(node.text);
                orgCode.setValue(node.id);
            });
        }

        function changeOrgName(){
            if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
                orgCode.setValue();
            }
        }

        function opr(rowdata){
            return "<a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='doDel(\"" + rowdata.chcNo + "\")'>移除</a>" ;
        }

        //====================================充电站查询=============================================================
        function changeStationName(){
            if(stationName.getValue()==null || stationName.getValue()==''){
                stationId.setValue();
            }
        }

        window.setStation = setStation;
        function setStation(item) {
            stationId.setValue(item.stationId);
            stationName.setValue(item.stationName);
        }
		function init(){
			chargePeakRelGrid.toolbarItems.sendBarItem.setEnabled(false);
		}

      <%--  window.onload = function() {
                chcStatus.setValue("1");
                initGridHeight('searchForm','chargePeakRelGrid');
                &lt;%&ndash;init();&ndash;%&gt;
				doSearch();
            }--%>
		</ls:script>
</ls:body>
</html>