package com.ls.ner.billing.market.controller;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.ast.api.oper.service.IOperRpcService;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.IntegeralRuleBo;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.service.IIntegeralRuleService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.def.api.operators.service.IDefrayOperRpcService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.OrgCodeUtil;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.owasp.esapi.ESAPI;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.*;

/**
 * 描述:优惠券管理
 * 创建人:biaoxiangd
 * 创建时间: 2017-06-03 12:08
 */
@Controller
@RequestMapping("/billing/coupon")
public class CouponController extends QueryController<MarketCondition> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponController.class);

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService;

    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "accuCouponRpcService", serviceTypes = ServiceType.RPC)
    private ICouponRpcService couponRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value="areaRpcService")
    private IAreaRpcService areaRpcService;

    @ServiceAutowired(serviceTypes =  ServiceType.RPC,value = "archivesRpcService")
    private IArchivesRpcService archivesRpcService;

    @ServiceAutowired(value = "operRpcService", serviceTypes = ServiceType.RPC)
    private IOperRpcService operRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defrayOperRpcService")
    private IDefrayOperRpcService defrayOperRpcService;

    @ServiceAutowired(serviceTypes = {ServiceType.RPC}, value = "accountService")
    private IAccountService accountService;

    @ServiceAutowired(value = "integeralRuleService", serviceTypes = ServiceType.LOCAL)
    private IIntegeralRuleService integeralRuleService;


    private void init(Model m, String chargeType) {
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        if (StringUtil.isNotBlank(chargeType)) {
            List<CodeBO> newTypeList = new ArrayList<CodeBO>();
            for (int i = 0; i < cpnTypeList.size(); i++) {
                CodeBO bo = cpnTypeList.get(i);
                if (bo.getCodeValue().startsWith(chargeType)) {
                    newTypeList.add(bo);
                }
            }
            m.addAttribute("cpnTypeList", newTypeList);
        }
        m.addAttribute("chargeType", chargeType);

    }

    /**
     * 描述:设置优惠内容
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:43
     */
    private void setBusiTypeList(Model m) {
        List<CodeBO> orderTypeList = codeService.getStandardCodes("orderType", null);
        List<CodeBO> busiTypeList = new ArrayList<CodeBO>();
        if (orderTypeList != null && orderTypeList.size() > 0) {
            for (CodeBO codeBo : orderTypeList) {
                String codeValue = codeBo.getCodeValue();
                String codeName = codeBo.getCodeName();
                if (("01".equals(codeValue) && "租车".equals(codeName)) || ("02".equals(codeValue) && "充电".equals(codeName))) {
                    busiTypeList.add(codeBo);
                }
            }
        } else {
            CodeBO codeZC = new CodeBO();
            codeZC.setCodeName("租车");
            codeZC.setCodeValue("01");
            busiTypeList.add(codeZC);
            CodeBO codeCD = new CodeBO();
            codeCD.setCodeName("充电");
            codeCD.setCodeValue("02");
            busiTypeList.add(codeCD);
        }

        m.addAttribute("busiTypeList", busiTypeList);
    }


    /**
     * 描述:优惠券管理
     *
     * @param: [couponBo, m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:43
     */
    @RequestMapping("/couponManage/{handleType}")
    public String couponManage(CouponBo couponBo,Model m, @PathVariable("handleType")
            String handleType, HttpServletRequest request) {
        String singleSelect = ESAPI.encoder().encodeForJavaScript(request.getParameter("singleSelect"));
        String chargeType = "01";//租车优惠券
        List<CodeBO> cpnStatusList = codeService.getStandardCodes("cpnState", null);
        m.addAttribute("cpnStatusList", cpnStatusList);
        init(m, chargeType);

        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        couponBo.setBusiType("01");

        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);
        couponBo.setCpnType("01");
        couponBo.setHandleType(handleType);
        if (couponBo.getHandleType().equals("03")){
            m.addAttribute("readOnly","true");
        }else {
            m.addAttribute("readOnly","false");
        }
        m.addAttribute("couponForm", couponBo);
        m.addAttribute("singleSelect",singleSelect);

        return "/bil/market/coupon/couponManage";
    }

    /**
     * 描述:优惠券管理
     *
     * @param: [couponBo, m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:43
     */
    @RequestMapping(value = "/couponManage/{handleType}/{actId}")
    public String couponPushManage(CouponBo couponBo, Model m, @PathVariable("handleType")
            String handleType, @PathVariable("actId") String actId) throws Exception{

        String chargeType = "01";//租车优惠券
        List<CodeBO> cpnStatusList = codeService.getStandardCodes("cpnState", null);
        m.addAttribute("cpnStatusList", cpnStatusList);
        init(m, chargeType);

        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        couponBo.setBusiType("01");

        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);
        couponBo.setCpnType("01");
        couponBo.setHandleType(handleType);
        couponBo.setActId(actId);

        m.addAttribute("readOnly","false");
        m.addAttribute("couponForm", couponBo);

        List<String> orgList = new ArrayList<>();
        AccountBo currentAccount = authentication.getCurrentAccount();
        OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
        if (orgBo != null) {
            orgList = OrgCodeUtil.getSubOrg(orgBo.getOrgCode());
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("orgCodeList",orgList);
//        paramMap.put("statusCode","01");
        List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
        m.addAttribute("buildList",operInfoList);


        //获取当前账号
        String accountName = authentication.getCurrentAccount().getAccountName();
        //判断当前账号是否是系统账号
        LOGGER.debug("========current{}",accountName);
        if (!accountService.isAdmin(accountName)) {
            LOGGER.debug("=======>不是系统账号");
            //不是系统账号，获取当前系统账号的管理单位
            OrgBo orgBoT = orgService.getOrgByAccountName(accountName);
            if (orgBoT != null) {
                String orgCode = orgBoT.getOrgCode();
                if (StringUtil.isNotBlank(orgCode)) {
                    //通过管理单位code查找运营商id
                    Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                    if (buildMap != null) {
                        String operId = StringUtil.nullToString(buildMap.get("operId"));
                        LOGGER.debug("======>operid:{}",operId);
                        couponBo.setBuildId(operId);
                    }
                }
            }
        }else{
            m.addAttribute("currentUser","SYSADMIN");
        }

        return "/bil/market/coupon/couponManage";
    }


    /**
     * 描述:优惠券查询
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:46
     */
    @RequestMapping("/couponQuery")
    public String couponQuery(Model m, HttpServletRequest request) {
        String chargeType = "01";
        List<CodeBO> cpnStatusList = codeService.getStandardCodes("cpnState", null);
        m.addAttribute("cpnStatusList", cpnStatusList);
        init(m, chargeType);
        return "/bil/market/coupon/couponQuery";
    }

    /**
     * 描述:优惠券列表数据查询
     *
     * @param: [params]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:47
     */
    @RequestMapping("/getCoupons")
    public @ItemResponseBody
    QueryResultObject getCoupons(@QueryRequestParam("params")
                                         RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        List<String> stringList =new ArrayList<>();
        if(StringUtil.isNotEmpty(condition.getCpnList())){
           String[] str = condition.getCpnList().split(",");
           for(int i=0;i<str.length;i++){
               stringList.add(str[i]);
           }
            condition.setCpnIdList(stringList);
        }


        if (StringUtil.isNotBlank(condition.getBuildId())){
            Map<String,Object> orgCodeMap  = defrayOperRpcService.qryOrgCodeByOperId(condition.getBuildId());
            if (orgCodeMap != null){
                String orgCode = StringUtil.nullToString(orgCodeMap.get("orgCode"));
                if (StringUtil.isNotBlank(orgCode)){
                    condition.setOrgCode(orgCode);
                }
            }
        }

        if (StringUtils.nullOrBlank(condition.getOrgCode())) {
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if (!StringUtils.nullOrBlank(condition.getQuerySubOrgFLag())) {
                //獲取上級單位
                if (orgBo != null) {
                    List<OrgBo> orgList = orgService.getSuperAndSubOrgByOrgNo(orgBo.getOrgCode());
                    String orgCodes = "";
                    for (int i = 0; i < orgList.size(); i++) {
                        orgCodes += orgList.get(i).getOrgCode() + ",";
                    }
                    orgCodes += orgCodes + orgBo.getOrgCode();
                    condition.setOrgCode(orgBo.getOrgCode());
                }
            } else {
                if (orgBo != null)
                    condition.setOrgCode(orgBo.getOrgCode());
            }
        }
        if (StringUtil.isNotBlank(condition.getActId())) {
            condition.setAllCoupon("no");
        }
        LOGGER.info("获取优惠券入参信息：{}", JsonUtil.obj2Json(condition));
        List<CouponBo> dataList = couponService.getCoupons(condition);
        if(CollectionUtils.isNotEmpty(dataList)){
            for(CouponBo couponBo : dataList){
                Map<String,String> map =new HashMap<>();
                map.put("useStatus","03");
                map.put("cpnId",couponBo.getCpnId());
                List<Map<String, Object>> mapList = new ArrayList<>();
                try{
                     mapList= couponRpcService.queryCoupon(map);
                }catch (Exception e){
                    e.printStackTrace();
                }
                if(CollectionUtils.isNotEmpty(mapList)){
                    couponBo.setUseCount(StringUtil.nullForString(mapList.size()));
                    String[] cpnAmts = couponBo.getCpnAmt().split("\\.");
                    int useMoney = mapList.size()* Integer.parseInt(cpnAmts[0]);
                    couponBo.setUseMoney(String.valueOf(useMoney));
                }
                LOGGER.debug(">>>>>>>"+JsonUtil.obj2Json(couponBo));

            }
        }
        int count = couponService.getCouponsCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 描述:编辑优惠券
     *
     * @param: [bo, m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:50
     */
    @RequestMapping("/editCoupon")
    public String editCoupon(CouponBo bo, Model m, HttpServletRequest request) {
        String handleType = request.getParameter("handleType");
        if (StringUtil.isNotBlank(handleType)) {
            bo.setHandleType(handleType);
        }
        String cpnId = request.getParameter("cpnId");
        // 优惠券类型
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);
        // 优惠券内容1
        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        // 优惠券内容2
        List<CodeBO> dctTypeList = codeService.getStandardCodes("cpnDctType", null);
        m.addAttribute("dctTypeList", dctTypeList);
        // 有效日/月/年
        List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
        m.addAttribute("timeUnitList", timeUnitList);
        // 计算小数位
        List<CodeBO> calcPrecisionDigitsList = codeService.getStandardCodes("calcPrecisionDigits", null);
        m.addAttribute("calcPrecisionDigitsList", calcPrecisionDigitsList);
        // 计算精度
        List<CodeBO> calcPrecisionList = codeService.getStandardCodes("calcPrecision", null);
        m.addAttribute("calcPrecisionList", calcPrecisionList);
        // 优惠券条件
        List<CodeBO> dctCondTypeList = codeService.getStandardCodes("dctCondType", null);
        m.addAttribute("dctCondTypeList", dctCondTypeList);
        // 优惠券条件内容
        List<Map> prodList = new ArrayList<Map>();
        Map<String, String> inMap = new HashMap<String, String>();
        inMap.put("prodId", "");
        prodList = couponService.queryBProd(inMap);
        List<CodeBO> dctProdIdList = new ArrayList<CodeBO>();
        if (prodList != null && prodList.size() > 0) {
            for (Map map : prodList) {
                CodeBO codeBo = new CodeBO();
                codeBo.setCodeName(StringUtil.getString(map.get("PROD_NAME")));
                codeBo.setCodeValue(StringUtil.getString(map.get("PROD_ID")));
                dctProdIdList.add(codeBo);
            }
        } else {
            CodeBO codeBo = new CodeBO();
            codeBo.setCodeName("无");
            codeBo.setCodeValue("0");
            dctProdIdList.add(codeBo);
        }
        LOGGER.debug("dctProdIdList---:{}",dctProdIdList);
        m.addAttribute("dctProdIdList", dctProdIdList);

        String nowDate = DateTools.getStringDateShort();

        if (StringUtil.isNotBlank(cpnId)) {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            condition.setAllCoupon("1");
            bo = couponService.getCouponDetail(condition);
            // 通过关联关系、获取条件列表
            inMap.put("cpnId", cpnId);
            List<CouponBo> dctCondList = couponService.queryDctCond(inMap);
            if (dctCondList.size() > 0) {
                CouponBo condCoupon = dctCondList.get(0);
                bo.setDctCondFlag(condCoupon.getDctCondFlag());
                bo.setTimeCondFlag(condCoupon.getTimeCondFlag());
                bo.setTimeCondBeg(condCoupon.getTimeCondBeg());
                bo.setTimeCondEnd(condCoupon.getTimeCondEnd());
                bo.setSuperpositionFlag(condCoupon.getSuperpositionFlag());
                String prodId = condCoupon.getProdId();
                bo.setDctCondId(condCoupon.getDctCondId());
                bo.setProdId(prodId);
                bo.setDctCondType(condCoupon.getDctCondType());
                bo.setDctProdId(prodId);
                bo.setDctCondValue(condCoupon.getDctCondValue());
                bo.setDctCondAreaFlag(StringUtil.isBlank(condCoupon.getDctCondAreaFlag())?"1":condCoupon.getDctCondAreaFlag());
                bo.setDctCondCity(condCoupon.getDctCondCity());
                bo.setDctCondStation(condCoupon.getDctCondStation());
                String eftDate = bo.getEftDate();
                if (StringUtil.isNotBlank(eftDate) && nowDate.equals(eftDate)) {
                    bo.setIsEffect("1");
                    bo.setEffectDate(eftDate);
                }
            }
        } else {
            bo.setCpnType("01");
            bo.setBusiType("01");
            bo.setDctType("0100");
            bo.setCpnTimeType("1");
            bo.setTimeUnit("1");
            bo.setCalcPrecisionDigits("0");
            bo.setCalcPrecision("1");
            bo.setDctCondType("00");
            bo.setDctCondFlag("1");
            bo.setDctCondAreaFlag("1");
            bo.setTimeCondFlag("1");
            bo.setDctProdId("01");
            bo.setIsEffect("1");
            bo.setEffectDate(nowDate);
            bo.setSuperpositionFlag("1");
//            // 无关联关系、获取条件列表
//            inMap.put("cpnNo","");
//            dctCondList = couponService.queryDctCond(inMap);
        }
//        request.setAttribute("dctCondList",dctCondList);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("couponForm", bo);
        return "/bil/market/coupon/editCoupon";
    }

    /**
     * 描述:保存优惠券信息
     *
     * @param: [bo, m, request]
     * @return: com.pt.poseidon.webcommon.rest.object.WrappedResult
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:49
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/saveCoupon", method = RequestMethod.POST)
    public @ItemResponseBody WrappedResult saveCoupon(CouponBo bo, Model m, MultipartHttpServletRequest request) throws Exception {
        WrappedResult result = new WrappedResult();
        if (StringUtil.isBlank(bo.getOrgCode())) {
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if (orgBo != null)
                bo.setOrgCode(orgBo.getOrgCode());
        }
        AccountBo userBo = authentication.getCurrentAccount();
        bo.setCreEmp(userBo != null ? userBo.getAccountName() : "SYSADMIN");
        int flag = 0;
        String outMsg = "";
        String isEffect = bo.getIsEffect();
        // 如果立即生效，以当前时间为准
        if ("1".equals(isEffect) && "1".equals(bo.getCpnTimeType())) {
            bo.setEftDate(bo.getEffectDate());
        }

//        //完善信息是支持送积分&优惠券的 如果后管配置积分&优惠券 只能同时存在一种情况 这里做校验积分规则是否配置了生效的绑定车辆
//        //用途要是 首次添加爱车的时候才校验
//        if("1".equals(bo.getCpnPurpose())){
//            IntegeralRuleBo integeralRuleBo = new IntegeralRuleBo();
//            integeralRuleBo.setEventType("02");
//            integeralRuleBo.setRuleStatus("02");
//            List<IntegeralRuleBo> integeralRuleBoList = integeralRuleService.queryRuleList(integeralRuleBo);
//            if(CollectionUtils.isNotEmpty(integeralRuleBoList)){
//                outMsg = "积分规则已配置绑定车辆送积分,无法创建和发布用途为首次添加爱车的优惠卷";
//                result.setResultValue(outMsg);
//                return result;
//            }
//        }



        if (StringUtil.isBlank(bo.getCpnId())) {
            flag = couponService.saveCoupon(bo, request);
            outMsg = "优惠券新增成功";
        } else {
            flag = couponService.updateCoupon(bo, request);
            outMsg = "优惠券更新成功";
        }
        if (flag > 0) {
            result.setSuccessful(true);
            return result;
        } else {
            result.setResultValue("保存优惠卷失败");
            return result;
        }
    }

    /**
     * 描述:优惠券详细信息
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:49
     */
    @RequestMapping("/initCouponDetail")
    public String initCouponDetail(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        request.setAttribute("cpnId", cpnId);
        return "/bil/market/coupon/couponDetailMain";
    }

    /**
     * 描述:优惠券主体详细信息
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:48
     */
    @RequestMapping("/couponInfo")
    public String couponInfo(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponBo bo = new CouponBo();
        if (StringUtil.isNotBlank(cpnId)) {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            condition.setAllCoupon("No");
            bo = couponService.getCouponDetail(condition);
            CodeBO codeBO = new CodeBO();
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                codeBO = codeService.getStandardCode("cpnType", cpnType, null);
                if (codeBO != null) {
                    bo.setCpnTypeName(codeBO.getCodeName());
                }
            }
            String busiType = bo.getBusiType();
            if (StringUtil.isNotBlank(busiType)) {
                codeBO = codeService.getStandardCode("orderType", busiType, null);
                if (codeBO != null) {
                    bo.setBusiTypeName(codeBO.getCodeName());
                }
            }
            String dctType = bo.getDctType();
            if (StringUtil.isNotBlank(dctType)) {
                List<CodeBO> dctTypeList = codeService.getStandardCodes("cpnDctType", null);
                if (dctTypeList != null && dctTypeList.size() > 0) {
                    for (CodeBO code : dctTypeList) {
                        if (dctType.equals(code.getCodeValue())) {
                            bo.setDctTypeName(code.getCodeName());
                            break;
                        }
                        if(BillConstants.CpnDctType.SERVICE_AMT.equals(dctType)){
                            bo.setDctTypeName("服务费");
                            break;
                        }
                    }
                }
            }
            String timeUnit = bo.getTimeUnit();
            if (StringUtil.isNotBlank(timeUnit)) {
                codeBO = codeService.getStandardCode("timeUnit", timeUnit, null);
                if (codeBO != null) {
                    bo.setTimeUnitName(codeBO.getCodeName());
                }
            }
            String calcPrecisionDigits = bo.getCalcPrecisionDigits();
            if (StringUtil.isNotBlank(calcPrecisionDigits)) {
                codeBO = codeService.getStandardCode("calcPrecisionDigits", calcPrecisionDigits, null);
                if (codeBO != null) {
                    bo.setCalcPrecisionDigitsName(codeBO.getCodeName());
                }
            }
            String calcPrecision = bo.getTimeUnit();
            if (StringUtil.isNotBlank(calcPrecision)) {
                codeBO = codeService.getStandardCode("calcPrecision", calcPrecision, null);
                if (codeBO != null) {
                    bo.setCalcPrecisionName(codeBO.getCodeName());
                }
            }
            // 通过关联关系、获取条件列表
            Map<String, String> inMap = new HashMap<String, String>();
            inMap.put("cpnId", cpnId);
            List<CouponBo> dctCondList = couponService.queryDctCond(inMap);
            if (dctCondList.size() > 0) {
                CouponBo condCoupon = dctCondList.get(0);
                bo.setDctCondFlag(condCoupon.getDctCondFlag());
                String prodId = condCoupon.getProdId();
                bo.setDctCondId(condCoupon.getDctCondId());
                String dctCondType = condCoupon.getDctCondType();
                bo.setDctCondType(dctCondType);
                bo.setDctCondAreaFlag(StringUtil.isBlank(condCoupon.getDctCondAreaFlag())?"1":condCoupon.getDctCondAreaFlag());
                bo.setDctCondCity(condCoupon.getDctCondCity());
                bo.setDctCondStation(condCoupon.getDctCondStation());
                bo.setTimeCondFlag(condCoupon.getTimeCondFlag());
                bo.setTimeCondEnd(condCoupon.getTimeCondEnd());
                bo.setTimeCondBeg(condCoupon.getTimeCondBeg());
                bo.setSuperpositionFlag(condCoupon.getSuperpositionFlag());
                if (StringUtil.isNotBlank(dctCondType)) {
                    List<CodeBO> dctCondTypeList = codeService.getStandardCodes("dctCondType", null);
                    if (dctCondTypeList != null && dctCondTypeList.size() > 0) {
                        for (CodeBO dctCode : dctCondTypeList) {
                            if (dctCondType.equals(dctCode.getCodeValue())) {
                                bo.setDctCondTypeName(dctCode.getCodeName());
                                break;
                            }
                        }
                    }
                }
                bo.setDctProdId(prodId);
                if (StringUtil.isNotBlank(prodId)) {
                    Map<String, String> inPordMap = new HashMap<String, String>();
                    inPordMap.put("prodId", prodId);
                    List<Map> prodList = couponService.queryBProd(inPordMap);
                    if (prodList != null && prodList.size() > 0) {
                        inPordMap = prodList.get(0);
                        bo.setDctProdName(inPordMap.get("PROD_NAME"));
                    }
                }
                bo.setDctCondValue(condCoupon.getDctCondValue());
            }
        }
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("couponForm", bo);
        return "/bil/market/coupon/couponInfo";
    }

    /**
     * 优惠券推广详细信息
     * description
     * 创建时间 2016年4月11日下午4:21:16
     * 创建人 biaoxiangd
     */
    @RequestMapping("/pushInfo")
    public String pushInfo(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponBo bo = new CouponBo();
        bo.setCpnId(cpnId);
        m.addAttribute("couponPushForm", bo);
        return "/bil/market/coupon/pushInfo";
    }

    /**
     * 优惠券使用信息
     * description
     * 创建时间 2016年4月11日下午4:21:16
     * 创建人 biaoxiangd
     */
    @RequestMapping("/useInfo")
    public String useInfo(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponBo bo = new CouponBo();
        MarketCondition condition = new MarketCondition();
        condition.setCpnId(cpnId);
        condition.setAllCoupon("no");
        List<CouponBo> dataList = couponService.getCoupons(condition);
        if (dataList != null && dataList.size() > 0) {
            bo = dataList.get(0);
        }
        m.addAttribute("useForm", bo);
        return "/bil/market/coupon/useInfo";
    }

    /**
     * 描述:优惠券使用信息
     *
     * @param: [params]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-10 14:50
     */
    @RequestMapping("/getAccountCoupons")
    public @ItemResponseBody
    QueryResultObject getAccountCoupons(@QueryRequestParam("params")
                                                RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        List<CouponBo> dataList = couponService.getAccountCoupons(condition);
        int count = 0;
        if (dataList != null && dataList.size() > 0) {
            count = dataList.size();
        }
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 描述:更改优惠券状态
     *
     * @param: [dataParams, m]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:48
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public @ResponseBody WrappedResult updateStatus(
            @RequestBody Map<String, Object> dataParams, Model m)
            throws BusinessWarning {
        String mark = "success";
        try {
            CouponBo bo = new CouponBo();
            BeanUtils.populate(bo, dataParams);
            //完善信息是支持送积分&优惠券的 如果后管配置积分&优惠券 只能同时存在一种情况 这里做校验积分规则是否配置了生效的绑定车辆
            //用途要是 首次添加爱车的时候才校验
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(bo.getCpnId());
            if("1".equals(bo.getCpnStatus())){
                CouponBo couponBo = couponService.getCouponCpnPurposeDetail(condition);
                if(couponBo!=null && "1".equals(couponBo.getCpnPurpose())){
                    IntegeralRuleBo integeralRuleBo = new IntegeralRuleBo();
                    integeralRuleBo.setEventType("02");
                    integeralRuleBo.setRuleStatus("02");
                    List<IntegeralRuleBo> integeralRuleBoList = integeralRuleService.queryRuleList(integeralRuleBo);
                    if(CollectionUtils.isNotEmpty(integeralRuleBoList)){
                        String outMsg = "积分规则已配置绑定车辆送积分,无法启动用途为首次添加爱车的优惠卷";
                        return WrappedResult.failedWrappedResult(outMsg);
                    }
                }
            }

            couponService.updateCouponStatus(bo);
        } catch (Exception e) {
            LOGGER.error("优惠券启动失败:{}",e);
            return WrappedResult.failedWrappedResult("优惠券启动失败！");
        }
        return WrappedResult.successWrapedResult(true);
    }

    @RequestMapping("/queryCityInfo")
    public @ItemResponseBody QueryResultObject queryCityInfo(@QueryRequestParam("params") RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        LOGGER.debug("======查询城市====="+JsonUtil.obj2Json(condition));
        Map<String,Object> inMap = new HashMap<String, Object>();
        inMap.put("cityName",condition.getCityName());
        List<String> cList = new ArrayList<String>();
        if(StringUtil.isNotBlank(condition.getCitys())){
            String[] cityArr = condition.getCitys().split(",");
            for (int i = 0; i < cityArr.length;i++){
                cList.add(cityArr[i].trim());
            }
        }else{
            cList = null;
        }
        //过滤已选的城市
        List<String> removeCitys=new ArrayList<>();
        if(StringUtil.isNotBlank(condition.getRemoveCitys())){
            String[] removeCityArr=condition.getRemoveCitys().split(",");
            for(int i=0;i<removeCityArr.length;i++){
                removeCitys.add(removeCityArr[i]);
            }
        }
        inMap.put("removeAreaCodes",removeCitys);
        inMap.put("areaCodes",cList);
        LOGGER.debug(">>>>>>>>>>>>>>>>>>>>>>查询城市入参:{}",IJsonUtil.obj2Json(inMap));
        List<Map> cityList  = areaRpcService.citys(inMap);
        return RestUtils.wrappQueryResult(cityList);
    }

    @RequestMapping("/queryStationInfo")
    public @ItemResponseBody QueryResultObject queryStationInfo(@QueryRequestParam("params") RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        LOGGER.debug("======查询站点====="+JsonUtil.obj2Json(condition));
        Map<String,Object> inMap = new HashMap<String, Object>();
        inMap.put("stationName",condition.getStationName());
        inMap.put("busiType",condition.getBusiType());
        inMap.put("stationNo",condition.getStationNo());
        inMap.put("stationAddr",condition.getStationAddr());
        //城市查询条件
        List<String> cityList = new ArrayList<String>();
        if(StringUtil.isNotBlank(condition.getCitys())){
            String[] cityArr = condition.getCitys().split(",");
            for (int i = 0; i < cityArr.length;i++){
                cityList.add(cityArr[i].trim());
            }
        }else{
            cityList = null;
        }
        inMap.put("citys",cityList);
        //站点查询条件
        List<String> sList = new ArrayList<String>();
        if(StringUtil.isNotBlank(condition.getStations())){
            String[] stationArr = condition.getStations().split(",");
            for (int i = 0; i < stationArr.length;i++){
                sList.add(stationArr[i].trim());
            }
        }else{
            sList = null;
        }
        inMap.put("stations",sList);
        List<String> removeStations=new ArrayList<>();
        //过滤站点
        if(StringUtil.isNotBlank(condition.getRemoveStations())){
            String[] removeStationArr=condition.getRemoveStations().split(",");
            for (int i=0;i< removeStationArr.length;i++){
                removeStations.add(removeStationArr[i].trim());
            }
        }
        inMap.put("removeStations",removeStations);
        List<String> orgCodeList=new ArrayList<>();
        if(StringUtils.nullOrBlank(condition.getOrgCode())){
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if(orgBo != null){
                condition.setOrgCode(orgBo.getOrgCode());
            }
            orgCodeList=getSubOrg(condition.getOrgCode());
            inMap.put("orgCodeList",orgCodeList);
        }else {
            orgCodeList=getSubOrg(condition.getOrgCode());
            if (CollectionUtils.isNotEmpty(orgCodeList)) {
                inMap.put("orgCodeList", orgCodeList);
            }else{
                inMap.put("orgCode",condition.getOrgCode());
            }
        }

        LOGGER.debug(">>>>>>>>>>>>>查询站点条件:{}",IJsonUtil.obj2Json(inMap));
        List<Map<String,Object>> stationList  = archivesRpcService.queryStationList(inMap);
        if (CollectionUtils.isNotEmpty(stationList)) {
            for (Map<String, Object> stationMap :stationList){
                //管理单位
                if (StringUtil.isNotEmpty(stationMap.get("orgCode"))){
                    OrgBo org=orgService.getOrgByNo(StringUtil.getString(stationMap.get("orgCode")));
                    if(org != null) {
                        stationMap.put("orgCodeName", org.getOrgShortName());
                    }
                }
                //城市名称
                if(StringUtil.isNotEmpty(stationMap.get("city"))){
                    AreaCondition areaCondition=new AreaCondition();
                    areaCondition.setAreaCode(StringUtil.getString(stationMap.get("city")));
                    LOGGER.debug(">>>>>>>>>>>>>>>>>>>查询城市入参:{}",IJsonUtil.obj2Json(areaCondition));
                    Map cityMap= areaRpcService.queryCityByCode(areaCondition);
                    stationMap.put("cityName",cityMap.get("AREA_NAME"));

                }
            }
        }

        return RestUtils.wrappQueryResult(stationList);
    }


    @RequestMapping("/couponCitySelect")
    public String couponCitySelect(Model m,@RequestParam("params") String params) {
        LOGGER.debug(">>>>>>>>>>>城市选择页面controller入参:{}",IJsonUtil.obj2Json(params));
        Map<String,String> info=JsonUtil.parseJsonToMap(params);
        MarketCondition cityForm=new MarketCondition();
        cityForm.setRemoveCitys(info.get("removeCitys"));
        m.addAttribute("cityForm",cityForm);
        return "/bil/market/coupon/couponCitySelect";
    }

    /**
     * @param m
     * @param params
     * @description     站点选择页面
     * <AUTHOR>
     * @create 2019-05-07 14:16:52
     */
    @RequestMapping("/couponStationSelect")
    public String couponStationSelect(Model m,@RequestParam("params")String params) throws Exception{
        LOGGER.debug(">>>>>>>>>>>站点选择页面controller入参:{}",IJsonUtil.obj2Json(params));
        MarketCondition stationForm=new MarketCondition();
        Map<String,String> info = JsonUtil.parseJsonToMap(params);
       List<String> areaCodes= (List<String>) ((Object)info.get("citys"));
       //查询城市
        Map<String,Object> inMap=new HashMap<>();
        inMap.put("areaCodes",areaCodes);
        List<Map> citys=areaRpcService.queryCitysByCode(inMap);
        LOGGER.debug(">>>>>>>>>>>>>>>>>>>>查询城市出参:{}",IJsonUtil.obj2Json(citys));
       stationForm.setAreaCode(StringUtil.getString(citys.get(0).get("areaCode")));
        String busiType=info.get("busiType");
        m.addAttribute("citys",citys);
        //管理单位
        AccountBo currentAccount = authentication.getCurrentAccount();
        OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
        LOGGER.debug(">>>>>>>>>>>>>>>>>>>>>>>>>>>站点选择管理单位:{}",IJsonUtil.obj2Json(orgBo));
        if (orgBo!=null){
            stationForm.setOrgCode(orgBo.getOrgCode());
            stationForm.setOrgCodeName(orgBo.getOrgShortName());
        }

        stationForm.setBusiType(busiType);
        stationForm.setRemoveStations(info.get("stations"));
        m.addAttribute("stationForm",stationForm);
        return "/bil/market/coupon/couponStationSelect";
    }



    @Override
    protected MarketCondition initCondition() {
        return new MarketCondition();
    }

    /**
     *@Description:获取下级
     *@Author: tianyoupeng
     *@Time: 2017/6/19 10:13
     */
    private List<String> getSubOrg(String orgCode) {
        List<String> orgList = new ArrayList<String>();
        if(StringUtil.isNotBlank(orgCode)) {
            String []orgArr = orgCode.split(",");
            for(String orgCodeTemp : orgArr) {
                List<OrgBo> orgBoList = orgService.getSubOrg(orgCodeTemp, null, null);
                if(!CollectionUtils.isEmpty(orgBoList)) {
                    for(OrgBo bo : orgBoList) {
                        orgList.add(bo.getOrgCode());
                    }
                }
            }
        }else {
            return null;
        }
        return orgList;
    }



    @RequestMapping("/newCouponManage")
    public String newCouponManage(CouponBo couponBo,Model m, HttpServletRequest request) {
        String singleSelect = ESAPI.encoder().encodeForJavaScript(request.getParameter("singleSelect"));
        String chargeType = "01";//租车优惠券

        //运营商
        List<Map> builds = operRpcService.queryBuildByType("05");
        m.addAttribute("buildList", builds);

        List<CodeBO> cpnStatusList = codeService.getStandardCodes("cpnState", null);
        m.addAttribute("cpnStatusList", cpnStatusList);
        init(m, chargeType);

        couponBo.setBusiType("01");

        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);
        couponBo.setCpnType("01");

        m.addAttribute("couponForm", couponBo);
        //获取当前账号
        String accountName = authentication.getCurrentAccount().getAccountName();
        //判断当前账号是否是系统账号
        if (!accountService.isAdmin(accountName)) {
            LOGGER.debug("=======>noAdmin");
            //不是系统账号，获取当前系统账号的管理单位
            OrgBo orgBo = orgService.getOrgByAccountName(accountName);
            if (orgBo != null){
                String orgCode = orgBo.getOrgCode();
                if (StringUtil.isNotBlank(orgCode)){
                    //通过管理单位code查找运营商id
                    Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                    if (buildMap != null){
                        String operId = StringUtil.nullToString(buildMap.get("operId"));
                        LOGGER.debug("========>当前账号对应的运营商id:{}",operId);
                        couponBo.setBuildId(operId);
                    }
                }
            }
            return "/bil/market/coupon/newCouponManageNoAdmin";
        }else{
            LOGGER.debug("=======>isAdmin");
            return "/bil/market/coupon/newCouponManage";
        }
    }



    @RequestMapping("/newGetCoupons")
    public @ItemResponseBody
    QueryResultObject newGetCoupons(@QueryRequestParam("params")
                                         RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        LOGGER.debug("=============>newGetCoupons");
        //获取当前账号
        String accountName = authentication.getCurrentAccount().getAccountName();
        //判断当前账号是否是系统账号
        if (!accountService.isAdmin(accountName)) {
            //不是系统账号，获取当前系统账号的管理单位
            OrgBo orgBo = orgService.getOrgByAccountName(accountName);
            if (orgBo != null){
                String orgCode = orgBo.getOrgCode();
                if (StringUtil.isNotBlank(orgCode)){
                    //通过管理单位code查找运营商id
                    Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                    if (buildMap != null){
                        String operId = StringUtil.nullToString(buildMap.get("operId"));
                        LOGGER.debug("========>当前账号对应的运营商id:{}",operId);
                        condition.setBuildId(operId);
                    }
                }
            }
        }


        condition.setAllCoupon("1");
        List<CouponBo> dataList = couponService.newGetCoupons(condition);
        if(CollectionUtils.isNotEmpty(dataList)){
            for(CouponBo couponBo : dataList){
                Map<String,String> map =new HashMap<>();
                map.put("useStatus","03");
                map.put("cpnId",couponBo.getCpnId());
                List<Map<String, Object>> mapList = new ArrayList<>();
                try{
                    mapList= couponRpcService.queryCoupon(map);
                }catch (Exception e){
                    e.printStackTrace();
                }
                if(CollectionUtils.isNotEmpty(mapList)){
                    couponBo.setUseCount(StringUtil.nullForString(mapList.size()));
                    String[] cpnAmts = couponBo.getCpnAmt().split("\\.");
                    int useMoney = mapList.size()* Integer.parseInt(cpnAmts[0]);
                    couponBo.setUseMoney(String.valueOf(useMoney));
                }
                LOGGER.debug(">>>>>>>"+JsonUtil.obj2Json(couponBo));

            }
        }
        int count = couponService.getCouponsCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }



    @RequestMapping("/newEditCoupon")
    public String newEditCoupon(CouponBo bo, Model m, HttpServletRequest request) {
        String ifEnable = StringUtil.nullHandeler(ESAPI.encoder().encodeForJavaScript(request.getParameter("ifEnable")));
        String cpnId = request.getParameter("cpnId");
        // 优惠券类型
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        m.addAttribute("cpnTypeList", cpnTypeList);
        // 优惠券内容1
        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        // 优惠券内容2
        List<CodeBO> dctTypeList = codeService.getStandardCodes("cpnDctType", null);
        m.addAttribute("dctTypeList", dctTypeList);
        // 有效日/月/年
        List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
        m.addAttribute("timeUnitList", timeUnitList);
        // 计算小数位
        List<CodeBO> calcPrecisionDigitsList = codeService.getStandardCodes("calcPrecisionDigits", null);
        m.addAttribute("calcPrecisionDigitsList", calcPrecisionDigitsList);
        // 计算精度
        List<CodeBO> calcPrecisionList = codeService.getStandardCodes("calcPrecision", null);
        m.addAttribute("calcPrecisionList", calcPrecisionList);
        // 优惠券条件
        List<CodeBO> dctCondTypeList = codeService.getStandardCodes("dctCondType", null);
        m.addAttribute("dctCondTypeList", dctCondTypeList);
        // 优惠券条件内容
        List<Map> prodList = new ArrayList<Map>();
        Map<String, String> inMap = new HashMap<String, String>();
        inMap.put("prodId", "");
        prodList = couponService.queryBProd(inMap);
        List<CodeBO> dctProdIdList = new ArrayList<CodeBO>();
        if (prodList != null && prodList.size() > 0) {
            for (Map map : prodList) {
                CodeBO codeBo = new CodeBO();
                codeBo.setCodeName(StringUtil.getString(map.get("PROD_NAME")));
                codeBo.setCodeValue(StringUtil.getString(map.get("PROD_ID")));
                dctProdIdList.add(codeBo);
            }
        } else {
            CodeBO codeBo = new CodeBO();
            codeBo.setCodeName("无");
            codeBo.setCodeValue("0");
            dctProdIdList.add(codeBo);
        }
        m.addAttribute("dctProdIdList", dctProdIdList);

        String nowDate = DateTools.getStringDateShort();

        if (StringUtil.isNotBlank(cpnId)) {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            condition.setAllCoupon("1");
            bo = couponService.getCouponDetail(condition);
            LOGGER.debug("=============beforeEftDate:========="+IJsonUtil.obj2Json(bo));
            // 通过关联关系、获取条件列表
            inMap.put("cpnId", cpnId);
            List<CouponBo> dctCondList = couponService.newQueryDctCond(inMap);
            if (dctCondList.size() > 0) {
                CouponBo condCoupon = dctCondList.get(0);
                LOGGER.debug("------------------------------------condCoupon:"+IJsonUtil.obj2Json(condCoupon));
                bo.setDctCondFlag(condCoupon.getDctCondFlag());
                bo.setTimeCondFlag(condCoupon.getTimeCondFlag());
                bo.setTimeCondBeg(condCoupon.getTimeCondBeg());
                bo.setTimeCondEnd(condCoupon.getTimeCondEnd());
                String prodId = condCoupon.getProdId();
                bo.setDctCondId(condCoupon.getDctCondId());
                bo.setProdId(prodId);
                bo.setDctCondType(condCoupon.getDctCondType());
                bo.setDctProdId(prodId);
                bo.setDctCondValue(condCoupon.getDctCondValue());
                bo.setDctCondAreaFlag(StringUtil.isBlank(condCoupon.getDctCondAreaFlag())?"1":condCoupon.getDctCondAreaFlag());
                bo.setDctCondCity(condCoupon.getDctCondCity());
                bo.setDctCondStation(condCoupon.getDctCondStation());
                bo.setBuildId(condCoupon.getDctCondBuild());
                bo.setCityCodes(condCoupon.getDctCondCity());
                bo.setStationIds(condCoupon.getDctCondStation());
                String cpnAmt = bo.getCpnAmt();
                if (StringUtil.isNotBlank(cpnAmt) && cpnAmt.length()>3){
                    bo.setCpnAmt(cpnAmt.substring(0,cpnAmt.indexOf('.')));
                }else{
                    bo.setCpnAmt(cpnAmt);
                }
                String eftDate = bo.getEftDate();
                if (StringUtil.isNotBlank(eftDate) && nowDate.equals(eftDate)) {
                    bo.setIsEffect("1");
                }else {
                    bo.setIsEffect("0");
                }
                bo.setEffectDate(eftDate);
                LOGGER.debug("------------------------------------bo:"+IJsonUtil.obj2Json(bo));
            }
            m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
            m.addAttribute("couponForm", bo);
            Map<String,Object> paramMap = new HashMap<>();
            List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
            m.addAttribute("buildList",operInfoList);

            //获取当前账号
            String accountName = authentication.getCurrentAccount().getAccountName();
            //判断当前账号是否是系统账号
            if (!accountService.isAdmin(accountName)) {
                //不是系统账号，获取当前系统账号的管理单位
                OrgBo orgBo = orgService.getOrgByAccountName(accountName);
                if (orgBo != null){
                    String orgCode = orgBo.getOrgCode();
                    if (StringUtil.isNotBlank(orgCode)){
                        //通过管理单位code查找运营商id
                        Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                        if (buildMap != null){
                            String operId = StringUtil.nullToString(buildMap.get("operId"));
                            LOGGER.debug("========>当前账号对应的运营商id:{}",operId);
                            m.addAttribute("buildId",operId);
                        }
                    }
                }
                return "/bil/market/coupon/newEditCouponNoAdmin";
            }else{
                return "/bil/market/coupon/newEditCoupon";
            }

        } else {
            bo.setCpnType("01");
            bo.setBusiType("01");
            bo.setDctType("0100");
            bo.setCpnTimeType("1");
            bo.setTimeUnit("1");
            bo.setCalcPrecisionDigits("0");
            bo.setCalcPrecision("1");
            bo.setDctCondType("00");
            bo.setDctCondFlag("1");
            bo.setDctCondAreaFlag("1");
            bo.setTimeCondFlag("1");
            bo.setDctProdId("01");
            bo.setIsEffect("1");
            bo.setEffectDate(nowDate);
            m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
            m.addAttribute("couponForm", bo);
            Map<String,Object> paramMap = new HashMap<>();
            List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
            m.addAttribute("buildList",operInfoList);

            //获取当前账号
            String accountName = authentication.getCurrentAccount().getAccountName();
            //判断当前账号是否是系统账号
            if (!accountService.isAdmin(accountName)) {
                //不是系统账号，获取当前系统账号的管理单位
                OrgBo orgBo = orgService.getOrgByAccountName(accountName);
                if (orgBo != null){
                    String orgCode = orgBo.getOrgCode();
                    if (StringUtil.isNotBlank(orgCode)){
                        //通过管理单位code查找运营商id
                        Map<String, Object> buildMap = defrayOperRpcService.qryOperByOrgCode(orgCode);
                        if (buildMap != null){
                            String operId = StringUtil.nullToString(buildMap.get("operId"));
                            LOGGER.debug("========>当前账号对应的运营商id:{}",operId);
                            bo.setBuildId(operId);
                        }
                    }
                }
                return "/bil/market/coupon/newSaveCouponNoAdmin";
            }else{
                return "/bil/market/coupon/newSaveCoupon";
            }

        }


    }


    /**
     * @param m
     * @param request
     * @description  优惠券详细信息
     * <AUTHOR>
     * @create 2019-05-28 10:13:06
     */
    @RequestMapping("/newInitCouponDetail")
    public String newInitCouponDetail(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        request.setAttribute("cpnId", cpnId);
        return "/bil/market/coupon/newCouponDetailMain";
    }



    /**
     * @param m
     * @param request
     * @description  优惠券主体详细信息
     * <AUTHOR>
     * @create 2019-05-28 10:13:53
     */
    @RequestMapping("/newCouponInfo")
    public String newCouponInfo(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        CouponBo bo = new CouponBo();
        if (StringUtil.isNotBlank(cpnId)) {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            condition.setAllCoupon("No");
            bo = couponService.getCouponDetail(condition);
            CodeBO codeBO = new CodeBO();
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                codeBO = codeService.getStandardCode("cpnType", cpnType, null);
                if (codeBO != null) {
                    bo.setCpnTypeName(codeBO.getCodeName());
                }
            }
            String busiType = bo.getBusiType();
            if (StringUtil.isNotBlank(busiType)) {
                codeBO = codeService.getStandardCode("orderType", busiType, null);
                if (codeBO != null) {
                    bo.setBusiTypeName(codeBO.getCodeName());
                }
            }
            String dctType = bo.getDctType();
            if (StringUtil.isNotBlank(dctType)) {
                List<CodeBO> dctTypeList = codeService.getStandardCodes("cpnDctType", null);
                if (dctTypeList != null && dctTypeList.size() > 0) {
                    for (CodeBO code : dctTypeList) {
                        if (dctType.equals(code.getCodeValue())) {
                            bo.setDctTypeName(code.getCodeName());
                            break;
                        }
                        if(BillConstants.CpnDctType.SERVICE_AMT.equals(dctType)){
                            bo.setDctTypeName("服务费");
                            break;
                        }
                    }
                }
            }
            String timeUnit = bo.getTimeUnit();
            if (StringUtil.isNotBlank(timeUnit)) {
                codeBO = codeService.getStandardCode("timeUnit", timeUnit, null);
                if (codeBO != null) {
                    bo.setTimeUnitName(codeBO.getCodeName());
                }
            }
            String calcPrecisionDigits = bo.getCalcPrecisionDigits();
            if (StringUtil.isNotBlank(calcPrecisionDigits)) {
                codeBO = codeService.getStandardCode("calcPrecisionDigits", calcPrecisionDigits, null);
                if (codeBO != null) {
                    bo.setCalcPrecisionDigitsName(codeBO.getCodeName());
                }
            }
            String calcPrecision = bo.getTimeUnit();
            if (StringUtil.isNotBlank(calcPrecision)) {
                codeBO = codeService.getStandardCode("calcPrecision", calcPrecision, null);
                if (codeBO != null) {
                    bo.setCalcPrecisionName(codeBO.getCodeName());
                }
            }
            // 通过关联关系、获取条件列表
            Map<String, String> inMap = new HashMap<String, String>();
            inMap.put("cpnId", cpnId);
            List<CouponBo> dctCondList = couponService.newQueryDctCond(inMap);
            if (dctCondList.size() > 0) {
                CouponBo condCoupon = dctCondList.get(0);
                bo.setDctCondFlag(condCoupon.getDctCondFlag());
                String prodId = condCoupon.getProdId();
                bo.setDctCondId(condCoupon.getDctCondId());
                String dctCondType = condCoupon.getDctCondType();
                bo.setDctCondType(dctCondType);
                bo.setDctCondAreaFlag(StringUtil.isBlank(condCoupon.getDctCondAreaFlag())?"1":condCoupon.getDctCondAreaFlag());
                bo.setDctCondCity(condCoupon.getDctCondCity());
                bo.setDctCondStation(condCoupon.getDctCondStation());
                bo.setTimeCondFlag(condCoupon.getTimeCondFlag());
                bo.setTimeCondEnd(condCoupon.getTimeCondEnd());
                bo.setTimeCondBeg(condCoupon.getTimeCondBeg());
                bo.setBuildId(condCoupon.getDctCondBuild());
                bo.setCityCodes(condCoupon.getDctCondCity());
                bo.setStationIds(condCoupon.getDctCondStation());
                bo.setSuperpositionFlag(condCoupon.getSuperpositionFlag());
                String cpnAmt = bo.getCpnAmt();
                if (StringUtil.isNotBlank(cpnAmt) && cpnAmt.length()>3){
                    bo.setCpnAmt(cpnAmt);
                }
                if (StringUtil.isNotBlank(dctCondType)) {
                    List<CodeBO> dctCondTypeList = codeService.getStandardCodes("dctCondType", null);
                    if (dctCondTypeList != null && dctCondTypeList.size() > 0) {
                        for (CodeBO dctCode : dctCondTypeList) {
                            if (dctCondType.equals(dctCode.getCodeValue())) {
                                bo.setDctCondTypeName(dctCode.getCodeName());
                                break;
                            }
                        }
                    }
                }
                bo.setDctCondValue(condCoupon.getDctCondValue());
            }
        }
        LOGGER.debug("-------------------------bo:"+JsonUtil.obj2Json(bo));
        Map<String,Object> paramMap = new HashMap<>();
        List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
        m.addAttribute("buildList",operInfoList);

        // 优惠券内容1
        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        m.addAttribute("busiTypeList", busiTypeList);
        // 优惠券内容2
        List<CodeBO> dctTypeList = codeService.getStandardCodes("cpnDctType", null);
        m.addAttribute("dctTypeList", dctTypeList);

        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("couponForm", bo);
        return "/bil/market/coupon/newCouponInfo";
    }


    /**
     * @param bo
     * @param m
     * @param request
     * @description   保存优惠券信息
     * <AUTHOR>
     * @create 2019-05-28 12:31:56
     */
    @RequestMapping(value = "/newSaveCoupon", method = RequestMethod.POST)
    public @ResponseBody WrappedResult newSaveCoupon(CouponBo bo, Model m, MultipartHttpServletRequest request) throws Exception {

        if (StringUtil.isBlank(bo.getOrgCode())) {
            AccountBo currentAccount = authentication.getCurrentAccount();
            OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
            if (orgBo != null)
                bo.setOrgCode(orgBo.getOrgCode());
        }
        AccountBo userBo = authentication.getCurrentAccount();
        bo.setCreEmp(userBo != null ? userBo.getAccountName() : "SYSADMIN");
        int flag = 0;
        String outMsg = "";
        String isEffect = bo.getIsEffect();
        // 如果立即生效，以当前时间为准
        if ("1".equals(isEffect) && "1".equals(bo.getCpnTimeType())) {
            String nowDate = DateTools.getStringDateShort();
            bo.setEftDate(nowDate);
        }

        LOGGER.debug("---------------------------bo-------------------------"+JsonUtil.obj2Json(bo));
        if (StringUtil.isBlank(bo.getCpnId())) {
            flag = couponService.newSaveCoupon(bo, request);
            outMsg = "优惠券新增失败";
        } else {
            flag = couponService.newUpdateCoupon(bo, request);
            outMsg = "优惠券更新失败";
        }
        if (flag > 0) {
            return WrappedResult.successWrapedResult(bo);
        } else {
            return WrappedResult.failedWrappedResult(outMsg);
        }
    }


    /**
     * 描述:更改优惠券状态
     *
     * @param: [dataParams, m]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-12 11:48
     */
    @RequestMapping(value = "/deleteCoupon", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject deleteCoupon(
            @RequestBody Map<String, Object> dataParams, Model m)
            throws BusinessWarning {
        String mark = "success";
        try {
            CouponBo bo = new CouponBo();
            BeanUtils.populate(bo, dataParams);
            //草稿状态下
            if (bo.getCpnStatus().equals("0") && StringUtil.isNotBlank(bo.getCpnId())){
                couponService.deleteByCpnId(bo.getCpnId());
            }
        } catch (Exception e) {
            mark = "error";
            throw new BusinessWarning("优惠券启动失败！", CouponController.class);
        }
        return RestUtils.wrappQueryResult(mark);
    }

}
