package com.ls.ner.billing.common.bo;

import java.util.Date;
import java.util.List;

import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.BillingConstants.ValidFlag;
import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

public class TariffPlanBo extends QueryCondition {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.SYSTEM_ID
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.PLAN_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String planNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.PLAN_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String planName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.OPER_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String operNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.OPER_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String operName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.CREATE_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.PE_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String peBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.SUB_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String subBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.CHARGE_WAY
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String chargeWay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.CHARGE_MODE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String chargeMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.TIME_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private Integer timePerPriceValue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.TIME_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String timePerPriceUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.MILL_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private Integer millPerPriceValue;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.MILL_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String millPerPriceUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.PERIOD_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String periodSplitPoints;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.MILL_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String millStepSplitPoints;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.TIME_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String timeStepSplitPoints;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.DEPOSIT_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String depositItemNos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.ATTACH_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String attachItemNos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.PLAN_REMARK
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String planRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.DATA_OPER_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan.DATA_OPER_TYPE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    private String dataOperType;
    
    private String orgCode;
    
    private String orgCodeName;
    
    private List<String> orgCodes;
    
    private String validFlag;
    
    @DicAttribute(dicName = "codeDict", key = "validFlag", subType = ValidFlag.CODE_TYPE)
	private String validFlagName;
    @DicAttribute(dicName = "codeDict", key = "subBe", subType = SubBe.CODE_TYPE)
	private String subBeName;
    @DicAttribute(dicName = "codeDict", key = "chargeWay", subType = ChargeWay.CODE_TYPE)
	private String chargeWayName;
    @DicAttribute(dicName = "codeDict", key = "chargeMode", subType = ChargeMode.CODE_TYPE)
	private String chargeModeName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.SYSTEM_ID
     *
     * @return the value of e_tariff_plan.SYSTEM_ID
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.SYSTEM_ID
     *
     * @param systemId the value for e_tariff_plan.SYSTEM_ID
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.PLAN_NO
     *
     * @return the value of e_tariff_plan.PLAN_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getPlanNo() {
        return planNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.PLAN_NO
     *
     * @param planNo the value for e_tariff_plan.PLAN_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setPlanNo(String planNo) {
        this.planNo = planNo == null ? null : planNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.PLAN_NAME
     *
     * @return the value of e_tariff_plan.PLAN_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getPlanName() {
        return planName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.PLAN_NAME
     *
     * @param planName the value for e_tariff_plan.PLAN_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setPlanName(String planName) {
        this.planName = planName == null ? null : planName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.OPER_NO
     *
     * @return the value of e_tariff_plan.OPER_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getOperNo() {
        return operNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.OPER_NO
     *
     * @param operNo the value for e_tariff_plan.OPER_NO
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setOperNo(String operNo) {
        this.operNo = operNo == null ? null : operNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.OPER_NAME
     *
     * @return the value of e_tariff_plan.OPER_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getOperName() {
        return operName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.OPER_NAME
     *
     * @param operName the value for e_tariff_plan.OPER_NAME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setOperName(String operName) {
        this.operName = operName == null ? null : operName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.CREATE_TIME
     *
     * @return the value of e_tariff_plan.CREATE_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.CREATE_TIME
     *
     * @param createTime the value for e_tariff_plan.CREATE_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.PE_BE
     *
     * @return the value of e_tariff_plan.PE_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getPeBe() {
        return peBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.PE_BE
     *
     * @param peBe the value for e_tariff_plan.PE_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setPeBe(String peBe) {
        this.peBe = peBe == null ? null : peBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.SUB_BE
     *
     * @return the value of e_tariff_plan.SUB_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getSubBe() {
        return subBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.SUB_BE
     *
     * @param subBe the value for e_tariff_plan.SUB_BE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setSubBe(String subBe) {
        this.subBe = subBe == null ? null : subBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.CHARGE_WAY
     *
     * @return the value of e_tariff_plan.CHARGE_WAY
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getChargeWay() {
        return chargeWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.CHARGE_WAY
     *
     * @param chargeWay the value for e_tariff_plan.CHARGE_WAY
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setChargeWay(String chargeWay) {
        this.chargeWay = chargeWay == null ? null : chargeWay.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.CHARGE_MODE
     *
     * @return the value of e_tariff_plan.CHARGE_MODE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getChargeMode() {
        return chargeMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.CHARGE_MODE
     *
     * @param chargeMode the value for e_tariff_plan.CHARGE_MODE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode == null ? null : chargeMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.TIME_PER_PRICE_VALUE
     *
     * @return the value of e_tariff_plan.TIME_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Integer getTimePerPriceValue() {
        return timePerPriceValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.TIME_PER_PRICE_VALUE
     *
     * @param timePerPriceValue the value for e_tariff_plan.TIME_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setTimePerPriceValue(Integer timePerPriceValue) {
        this.timePerPriceValue = timePerPriceValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.TIME_PER_PRICE_UNIT
     *
     * @return the value of e_tariff_plan.TIME_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getTimePerPriceUnit() {
        return timePerPriceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.TIME_PER_PRICE_UNIT
     *
     * @param timePerPriceUnit the value for e_tariff_plan.TIME_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setTimePerPriceUnit(String timePerPriceUnit) {
        this.timePerPriceUnit = timePerPriceUnit == null ? null : timePerPriceUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.MILL_PER_PRICE_VALUE
     *
     * @return the value of e_tariff_plan.MILL_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Integer getMillPerPriceValue() {
        return millPerPriceValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.MILL_PER_PRICE_VALUE
     *
     * @param millPerPriceValue the value for e_tariff_plan.MILL_PER_PRICE_VALUE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setMillPerPriceValue(Integer millPerPriceValue) {
        this.millPerPriceValue = millPerPriceValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.MILL_PER_PRICE_UNIT
     *
     * @return the value of e_tariff_plan.MILL_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getMillPerPriceUnit() {
        return millPerPriceUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.MILL_PER_PRICE_UNIT
     *
     * @param millPerPriceUnit the value for e_tariff_plan.MILL_PER_PRICE_UNIT
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setMillPerPriceUnit(String millPerPriceUnit) {
        this.millPerPriceUnit = millPerPriceUnit == null ? null : millPerPriceUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.PERIOD_SPLIT_POINTS
     *
     * @return the value of e_tariff_plan.PERIOD_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getPeriodSplitPoints() {
        return periodSplitPoints;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.PERIOD_SPLIT_POINTS
     *
     * @param periodSplitPoints the value for e_tariff_plan.PERIOD_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setPeriodSplitPoints(String periodSplitPoints) {
        this.periodSplitPoints = periodSplitPoints == null ? null : periodSplitPoints.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.MILL_STEP_SPLIT_POINTS
     *
     * @return the value of e_tariff_plan.MILL_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getMillStepSplitPoints() {
        return millStepSplitPoints;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.MILL_STEP_SPLIT_POINTS
     *
     * @param millStepSplitPoints the value for e_tariff_plan.MILL_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setMillStepSplitPoints(String millStepSplitPoints) {
        this.millStepSplitPoints = millStepSplitPoints == null ? null : millStepSplitPoints.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.TIME_STEP_SPLIT_POINTS
     *
     * @return the value of e_tariff_plan.TIME_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getTimeStepSplitPoints() {
        return timeStepSplitPoints;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.TIME_STEP_SPLIT_POINTS
     *
     * @param timeStepSplitPoints the value for e_tariff_plan.TIME_STEP_SPLIT_POINTS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setTimeStepSplitPoints(String timeStepSplitPoints) {
        this.timeStepSplitPoints = timeStepSplitPoints == null ? null : timeStepSplitPoints.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.DEPOSIT_ITEM_NOS
     *
     * @return the value of e_tariff_plan.DEPOSIT_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getDepositItemNos() {
        return depositItemNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.DEPOSIT_ITEM_NOS
     *
     * @param depositItemNos the value for e_tariff_plan.DEPOSIT_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setDepositItemNos(String depositItemNos) {
        this.depositItemNos = depositItemNos == null ? null : depositItemNos.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.ATTACH_ITEM_NOS
     *
     * @return the value of e_tariff_plan.ATTACH_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getAttachItemNos() {
        return attachItemNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.ATTACH_ITEM_NOS
     *
     * @param attachItemNos the value for e_tariff_plan.ATTACH_ITEM_NOS
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setAttachItemNos(String attachItemNos) {
        this.attachItemNos = attachItemNos == null ? null : attachItemNos.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.PLAN_REMARK
     *
     * @return the value of e_tariff_plan.PLAN_REMARK
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getPlanRemark() {
        return planRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.PLAN_REMARK
     *
     * @param planRemark the value for e_tariff_plan.PLAN_REMARK
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setPlanRemark(String planRemark) {
        this.planRemark = planRemark == null ? null : planRemark.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.DATA_OPER_TIME
     *
     * @return the value of e_tariff_plan.DATA_OPER_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_tariff_plan.DATA_OPER_TIME
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan.DATA_OPER_TYPE
     *
     * @return the value of e_tariff_plan.DATA_OPER_TYPE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_tariff_plan.DATA_OPER_TYPE
     *
     * @mbggenerated Thu Mar 10 20:29:22 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getValidFlag() {
		return validFlag;
	}

	public void setValidFlag(String validFlag) {
		this.validFlag = validFlag;
	}

	public String getValidFlagName() {
		return validFlagName;
	}

	public void setValidFlagName(String validFlagName) {
		this.validFlagName = validFlagName;
	}

	public String getSubBeName() {
		return subBeName;
	}

	public void setSubBeName(String subBeName) {
		this.subBeName = subBeName;
	}

	public String getChargeWayName() {
		return chargeWayName;
	}

	public void setChargeWayName(String chargeWayName) {
		this.chargeWayName = chargeWayName;
	}

	public String getChargeModeName() {
		return chargeModeName;
	}

	public void setChargeModeName(String chargeModeName) {
		this.chargeModeName = chargeModeName;
	}

	public String getOrgCodeName() {
		return orgCodeName;
	}

	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}

	public List<String> getOrgCodes() {
		return orgCodes;
	}

	public void setOrgCodes(List<String> orgCodes) {
		this.orgCodes = orgCodes;
	}
    
}