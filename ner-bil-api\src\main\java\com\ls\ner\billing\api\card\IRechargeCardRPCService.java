package com.ls.ner.billing.api.card;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.api.card
 * @ClassName: IRechargeCardRPCService
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/5/8 15:56
 * @Version: 1.0
 */
public interface IRechargeCardRPCService {

    /**
     * 卡密充值
     * @param map
     * @return
     */
    Map<String,Object> rechargeCardCode(Map<String,String> map);

    /**
     * 我的充值卡
     * @param map
     * @return
     */
    Map<String,Object> myRechargeCardInfos(Map<String,String> map);

    /**
     * 查询充值卡列表
     * @return
     */
    List<Map<String, String>> getRechargeCardList();

    /**
     * 充值卡详情
     * @param map
     * @return
     */
    Map<String,Object> rechargeCardInfo(Map<String,String> map);

    /**
     * 根据订单结算传的信息更新对应充值卡
     */
    void updateRechargeCardByOrder(Map<String,String> map) throws Exception;

    /**
     * 充值卡明细跳转订单列表
     * @param cardId
     * @return
     */
    List<String> cardOrderNoList(String cardId, String cardDetailId);

    /**
     * 开票的时候 排除部分使用卡密兑换充值卡的充电订单的金额
     * @param map
     * @return
     */
    List<Map<String, Object>> queryCardOrderByOrderNo(Map<String, Object> map);

    /**
     * 用于给充电订单列表展示相关充值卡卡号 充值卡实际抵扣金额 相关充值卡卡号 字段使用
     */
    List<Map<String,Object>> queryCardInfosByChargeOrderList(Map<String, Object> map);

    /**
     * 通过充值卡卡号查询对应充电订单
     * @param map
     * @return
     */
    List<String> getOrderListByCardNo(Map<String, Object> map);

}
