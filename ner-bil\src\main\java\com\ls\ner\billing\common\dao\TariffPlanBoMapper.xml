<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.TariffPlanBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.TariffPlanBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="PLAN_NO" jdbcType="VARCHAR" property="planNo" />
    <result column="PLAN_NAME" jdbcType="VARCHAR" property="planName" />
    <result column="OPER_NO" jdbcType="VARCHAR" property="operNo" />
    <result column="OPER_NAME" jdbcType="VARCHAR" property="operName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="PE_BE" jdbcType="VARCHAR" property="peBe" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="CHARGE_WAY" jdbcType="VARCHAR" property="chargeWay" />
    <result column="CHARGE_MODE" jdbcType="VARCHAR" property="chargeMode" />
    <result column="TIME_PER_PRICE_VALUE" jdbcType="INTEGER" property="timePerPriceValue" />
    <result column="TIME_PER_PRICE_UNIT" jdbcType="VARCHAR" property="timePerPriceUnit" />
    <result column="MILL_PER_PRICE_VALUE" jdbcType="INTEGER" property="millPerPriceValue" />
    <result column="MILL_PER_PRICE_UNIT" jdbcType="VARCHAR" property="millPerPriceUnit" />
    <result column="PERIOD_SPLIT_POINTS" jdbcType="VARCHAR" property="periodSplitPoints" />
    <result column="MILL_STEP_SPLIT_POINTS" jdbcType="VARCHAR" property="millStepSplitPoints" />
    <result column="TIME_STEP_SPLIT_POINTS" jdbcType="VARCHAR" property="timeStepSplitPoints" />
    <result column="DEPOSIT_ITEM_NOS" jdbcType="VARCHAR" property="depositItemNos" />
    <result column="ATTACH_ITEM_NOS" jdbcType="VARCHAR" property="attachItemNos" />
    <result column="PLAN_REMARK" jdbcType="VARCHAR" property="planRemark" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    SYSTEM_ID, PLAN_NO, PLAN_NAME, OPER_NO, OPER_NAME, CREATE_TIME, PE_BE, SUB_BE, CHARGE_WAY, 
    CHARGE_MODE, TIME_PER_PRICE_VALUE, TIME_PER_PRICE_UNIT, MILL_PER_PRICE_VALUE, MILL_PER_PRICE_UNIT, 
    PERIOD_SPLIT_POINTS, MILL_STEP_SPLIT_POINTS, TIME_STEP_SPLIT_POINTS, DEPOSIT_ITEM_NOS, 
    ATTACH_ITEM_NOS, PLAN_REMARK, DATA_OPER_TIME, DATA_OPER_TYPE,ORG_CODE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.TariffPlanBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_tariff_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_tariff_plan
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    delete from e_tariff_plan
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.TariffPlanBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    delete from e_tariff_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    insert into e_tariff_plan (SYSTEM_ID, PLAN_NO, PLAN_NAME, 
      OPER_NO, OPER_NAME, CREATE_TIME, 
      PE_BE, SUB_BE, CHARGE_WAY, 
      CHARGE_MODE, TIME_PER_PRICE_VALUE, TIME_PER_PRICE_UNIT, 
      MILL_PER_PRICE_VALUE, MILL_PER_PRICE_UNIT, PERIOD_SPLIT_POINTS, 
      MILL_STEP_SPLIT_POINTS, TIME_STEP_SPLIT_POINTS, 
      DEPOSIT_ITEM_NOS, ATTACH_ITEM_NOS, PLAN_REMARK, 
      DATA_OPER_TIME, DATA_OPER_TYPE)
    values (#{systemId,jdbcType=BIGINT}, #{planNo,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, 
      #{operNo,jdbcType=VARCHAR}, #{operName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{peBe,jdbcType=VARCHAR}, #{subBe,jdbcType=VARCHAR}, #{chargeWay,jdbcType=VARCHAR}, 
      #{chargeMode,jdbcType=VARCHAR}, #{timePerPriceValue,jdbcType=INTEGER}, #{timePerPriceUnit,jdbcType=VARCHAR}, 
      #{millPerPriceValue,jdbcType=INTEGER}, #{millPerPriceUnit,jdbcType=VARCHAR}, #{periodSplitPoints,jdbcType=VARCHAR}, 
      #{millStepSplitPoints,jdbcType=VARCHAR}, #{timeStepSplitPoints,jdbcType=VARCHAR}, 
      #{depositItemNos,jdbcType=VARCHAR}, #{attachItemNos,jdbcType=VARCHAR}, #{planRemark,jdbcType=VARCHAR}, 
      #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    insert into e_tariff_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="planNo != null">
        PLAN_NO,
      </if>
      <if test="planName != null">
        PLAN_NAME,
      </if>
      <if test="operNo != null">
        OPER_NO,
      </if>
      <if test="operName != null">
        OPER_NAME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="peBe != null">
        PE_BE,
      </if>
      <if test="subBe != null">
        SUB_BE,
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY,
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE,
      </if>
      <if test="timePerPriceValue != null">
        TIME_PER_PRICE_VALUE,
      </if>
      <if test="timePerPriceUnit != null">
        TIME_PER_PRICE_UNIT,
      </if>
      <if test="millPerPriceValue != null">
        MILL_PER_PRICE_VALUE,
      </if>
      <if test="millPerPriceUnit != null">
        MILL_PER_PRICE_UNIT,
      </if>
      <if test="periodSplitPoints != null">
        PERIOD_SPLIT_POINTS,
      </if>
      <if test="millStepSplitPoints != null">
        MILL_STEP_SPLIT_POINTS,
      </if>
      <if test="timeStepSplitPoints != null">
        TIME_STEP_SPLIT_POINTS,
      </if>
      <if test="depositItemNos != null">
        DEPOSIT_ITEM_NOS,
      </if>
      <if test="attachItemNos != null">
        ATTACH_ITEM_NOS,
      </if>
      <if test="planRemark != null">
        PLAN_REMARK,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="planNo != null">
        #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="operNo != null">
        #{operNo,jdbcType=VARCHAR},
      </if>
      <if test="operName != null">
        #{operName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="peBe != null">
        #{peBe,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="chargeWay != null">
        #{chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="timePerPriceValue != null">
        #{timePerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="timePerPriceUnit != null">
        #{timePerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="millPerPriceValue != null">
        #{millPerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="millPerPriceUnit != null">
        #{millPerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="periodSplitPoints != null">
        #{periodSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="millStepSplitPoints != null">
        #{millStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="timeStepSplitPoints != null">
        #{timeStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="depositItemNos != null">
        #{depositItemNos,jdbcType=VARCHAR},
      </if>
      <if test="attachItemNos != null">
        #{attachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="planRemark != null">
        #{planRemark,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.TariffPlanBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    select count(*) from e_tariff_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    update e_tariff_plan
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.planNo != null">
        PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      </if>
      <if test="record.planName != null">
        PLAN_NAME = #{record.planName,jdbcType=VARCHAR},
      </if>
      <if test="record.operNo != null">
        OPER_NO = #{record.operNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operName != null">
        OPER_NAME = #{record.operName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.peBe != null">
        PE_BE = #{record.peBe,jdbcType=VARCHAR},
      </if>
      <if test="record.subBe != null">
        SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeWay != null">
        CHARGE_WAY = #{record.chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeMode != null">
        CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="record.timePerPriceValue != null">
        TIME_PER_PRICE_VALUE = #{record.timePerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="record.timePerPriceUnit != null">
        TIME_PER_PRICE_UNIT = #{record.timePerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.millPerPriceValue != null">
        MILL_PER_PRICE_VALUE = #{record.millPerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="record.millPerPriceUnit != null">
        MILL_PER_PRICE_UNIT = #{record.millPerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.periodSplitPoints != null">
        PERIOD_SPLIT_POINTS = #{record.periodSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="record.millStepSplitPoints != null">
        MILL_STEP_SPLIT_POINTS = #{record.millStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="record.timeStepSplitPoints != null">
        TIME_STEP_SPLIT_POINTS = #{record.timeStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="record.depositItemNos != null">
        DEPOSIT_ITEM_NOS = #{record.depositItemNos,jdbcType=VARCHAR},
      </if>
      <if test="record.attachItemNos != null">
        ATTACH_ITEM_NOS = #{record.attachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="record.planRemark != null">
        PLAN_REMARK = #{record.planRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.validFlag != null">
        VALID_FLAG = #{record.validFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    update e_tariff_plan
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      PLAN_NAME = #{record.planName,jdbcType=VARCHAR},
      OPER_NO = #{record.operNo,jdbcType=VARCHAR},
      OPER_NAME = #{record.operName,jdbcType=VARCHAR},
      CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      PE_BE = #{record.peBe,jdbcType=VARCHAR},
      SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      CHARGE_WAY = #{record.chargeWay,jdbcType=VARCHAR},
      CHARGE_MODE = #{record.chargeMode,jdbcType=VARCHAR},
      TIME_PER_PRICE_VALUE = #{record.timePerPriceValue,jdbcType=INTEGER},
      TIME_PER_PRICE_UNIT = #{record.timePerPriceUnit,jdbcType=VARCHAR},
      MILL_PER_PRICE_VALUE = #{record.millPerPriceValue,jdbcType=INTEGER},
      MILL_PER_PRICE_UNIT = #{record.millPerPriceUnit,jdbcType=VARCHAR},
      PERIOD_SPLIT_POINTS = #{record.periodSplitPoints,jdbcType=VARCHAR},
      MILL_STEP_SPLIT_POINTS = #{record.millStepSplitPoints,jdbcType=VARCHAR},
      TIME_STEP_SPLIT_POINTS = #{record.timeStepSplitPoints,jdbcType=VARCHAR},
      DEPOSIT_ITEM_NOS = #{record.depositItemNos,jdbcType=VARCHAR},
      ATTACH_ITEM_NOS = #{record.attachItemNos,jdbcType=VARCHAR},
      PLAN_REMARK = #{record.planRemark,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    update e_tariff_plan
    <set>
      <if test="planNo != null">
        PLAN_NO = #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="planName != null">
        PLAN_NAME = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="operNo != null">
        OPER_NO = #{operNo,jdbcType=VARCHAR},
      </if>
      <if test="operName != null">
        OPER_NAME = #{operName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="peBe != null">
        PE_BE = #{peBe,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        SUB_BE = #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="chargeWay != null">
        CHARGE_WAY = #{chargeWay,jdbcType=VARCHAR},
      </if>
      <if test="chargeMode != null">
        CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      </if>
      <if test="timePerPriceValue != null">
        TIME_PER_PRICE_VALUE = #{timePerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="timePerPriceUnit != null">
        TIME_PER_PRICE_UNIT = #{timePerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="millPerPriceValue != null">
        MILL_PER_PRICE_VALUE = #{millPerPriceValue,jdbcType=INTEGER},
      </if>
      <if test="millPerPriceUnit != null">
        MILL_PER_PRICE_UNIT = #{millPerPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="periodSplitPoints != null">
        PERIOD_SPLIT_POINTS = #{periodSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="millStepSplitPoints != null">
        MILL_STEP_SPLIT_POINTS = #{millStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="timeStepSplitPoints != null">
        TIME_STEP_SPLIT_POINTS = #{timeStepSplitPoints,jdbcType=VARCHAR},
      </if>
      <if test="depositItemNos != null">
        DEPOSIT_ITEM_NOS = #{depositItemNos,jdbcType=VARCHAR},
      </if>
      <if test="attachItemNos != null">
        ATTACH_ITEM_NOS = #{attachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="planRemark != null">
        PLAN_REMARK = #{planRemark,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.TariffPlanBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 10 20:29:22 CST 2016.
    -->
    update e_tariff_plan
    set PLAN_NO = #{planNo,jdbcType=VARCHAR},
      PLAN_NAME = #{planName,jdbcType=VARCHAR},
      OPER_NO = #{operNo,jdbcType=VARCHAR},
      OPER_NAME = #{operName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      PE_BE = #{peBe,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      CHARGE_WAY = #{chargeWay,jdbcType=VARCHAR},
      CHARGE_MODE = #{chargeMode,jdbcType=VARCHAR},
      TIME_PER_PRICE_VALUE = #{timePerPriceValue,jdbcType=INTEGER},
      TIME_PER_PRICE_UNIT = #{timePerPriceUnit,jdbcType=VARCHAR},
      MILL_PER_PRICE_VALUE = #{millPerPriceValue,jdbcType=INTEGER},
      MILL_PER_PRICE_UNIT = #{millPerPriceUnit,jdbcType=VARCHAR},
      PERIOD_SPLIT_POINTS = #{periodSplitPoints,jdbcType=VARCHAR},
      MILL_STEP_SPLIT_POINTS = #{millStepSplitPoints,jdbcType=VARCHAR},
      TIME_STEP_SPLIT_POINTS = #{timeStepSplitPoints,jdbcType=VARCHAR},
      DEPOSIT_ITEM_NOS = #{depositItemNos,jdbcType=VARCHAR},
      ATTACH_ITEM_NOS = #{attachItemNos,jdbcType=VARCHAR},
      PLAN_REMARK = #{planRemark,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>