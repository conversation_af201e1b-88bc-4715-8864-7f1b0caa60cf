package com.ls.ner.billing.vip.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.condition.VipCondition;
import com.ls.ner.billing.vip.condition.VipRecordCondition;
import com.ls.ner.billing.vip.service.IVipService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14
 */
@Controller
@RequestMapping("/vip/record")
public class VipRecordController extends QueryController<VipRecordCondition> {

    private static final Logger logger = LoggerFactory.getLogger(VipRecordController.class);

    @ServiceAutowired(value = "vipService", serviceTypes = ServiceType.LOCAL)
    private IVipService vipService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @RequestMapping("/getVipPayRecordList")
    public @ItemResponseBody
    QueryResultObject getVipPayRecordList(@QueryRequestParam("params") RequestCondition params) {
        VipRecordCondition condition = this.rCondition2QCondition(params);
        List<VipPayRecordBo> dataList = vipService.getRecordList(condition);
        List<CodeBO> payTypeList = codeService.getStandardCodes("vipPayType", null);
        List<CodeBO> vipTypeList = codeService.getStandardCodes("vipType", null);
        List<CodeBO> vipPayChannel = codeService.getStandardCodes("vipPayChannel", null);

        for (VipPayRecordBo vipPayRecordBo : dataList) {

            for (CodeBO code1 : payTypeList) {
                if (code1.getCodeValue().equals(vipPayRecordBo.getPayType())) {
                    vipPayRecordBo.setPayTypeName(code1.getCodeName());
                }
            }
            for (CodeBO code2 : vipTypeList) {
                if (code2.getCodeValue().equals(vipPayRecordBo.getVipType())) {
                    vipPayRecordBo.setVipTypeName(code2.getCodeName());
                }
            }
            for (CodeBO code3 : vipPayChannel) {
                if (code3.getCodeValue().equals(vipPayRecordBo.getPayChannel())) {
                    vipPayRecordBo.setPayChannelName(code3.getCodeName());
                }
            }
        }
        int count = vipService.getRecordListCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    @RequestMapping("/init")
    public String recordInit(Model m) {
        logger.debug("/bil/vip/vipPayRecord");
        List<CodeBO> payTypeList = codeService.getStandardCodes("vipPayType", null);
        List<CodeBO> vipTypeList = codeService.getStandardCodes("vipType", null);
        m.addAttribute("payTypeList", payTypeList);
        m.addAttribute("vipTypeList", vipTypeList);
        m.addAttribute("recordForm", new VipPayRecordBo());
        return "/bil/vip/vipPayRecord";
    }

    @Override
    protected VipRecordCondition initCondition() {
        return new VipRecordCondition();
    }

}
