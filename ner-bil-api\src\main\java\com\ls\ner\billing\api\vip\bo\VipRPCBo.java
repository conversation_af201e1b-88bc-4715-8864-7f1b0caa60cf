package com.ls.ner.billing.api.vip.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/16
 */
public class VipRPCBo implements Serializable {

    private String vipId;

    private String vipType;

    private String vipTypeName;

    private BigDecimal vipPrice;

    private BigDecimal vipPriceNew;

    private BigDecimal vipPriceOld;

    private String status;

    private String statusName;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String custId;

    private String mobile;

    private BigDecimal chargePq;

    public BigDecimal getChargePq() {
        return chargePq;
    }

    public void setChargePq(BigDecimal chargePq) {
        this.chargePq = chargePq;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getVipType() {
        return vipType;
    }

    public void setVipType(String vipType) {
        this.vipType = vipType;
    }

    public String getVipTypeName() {
        return vipTypeName;
    }

    public void setVipTypeName(String vipTypeName) {
        this.vipTypeName = vipTypeName;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public BigDecimal getVipPriceNew() {
        return vipPriceNew;
    }

    public void setVipPriceNew(BigDecimal vipPriceNew) {
        this.vipPriceNew = vipPriceNew;
    }

    public BigDecimal getVipPriceOld() {
        return vipPriceOld;
    }

    public void setVipPriceOld(BigDecimal vipPriceOld) {
        this.vipPriceOld = vipPriceOld;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

}
