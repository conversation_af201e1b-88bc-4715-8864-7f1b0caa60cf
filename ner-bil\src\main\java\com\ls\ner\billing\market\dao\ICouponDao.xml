<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.ICouponDao">

	<resultMap id="couponBo" type="com.ls.ner.billing.market.bo.CouponBo">
		<result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
		<result column="CPN_NO" property="cpnNo" jdbcType="VARCHAR" />
		<result column="CPN_NAME" property="cpnName" jdbcType="VARCHAR" />
		<result column="CPN_TYPE" property="cpnType" jdbcType="VARCHAR" />
		<result column="CPN_AMT" property="cpnAmt" jdbcType="VARCHAR" />
		<result column="EFT_DATE" property="eftDate" jdbcType="VARCHAR" />
		<result column="INV_DATE" property="invDate" jdbcType="VARCHAR" />
		<result column="CPN_NUM" property="cpnNum" jdbcType="VARCHAR" />
		<result column="LIM_GET_NUM" property="limGetNum" jdbcType="VARCHAR" />
		<result column="ALRDY_GET_NUM" property="alrdyGetNum" jdbcType="VARCHAR" />
		<result column="CPN_MARKS" property="cpnMarks" jdbcType="VARCHAR" />
		<result column="CPN_STATUS" property="cpnStatus" jdbcType="VARCHAR" />
		<result column="CRE_TIME" property="creTime" jdbcType="VARCHAR" />
		<result column="CRE_EMP" property="creEmp" jdbcType="VARCHAR" />
		<result column="EFFECT_TIME" property="effectTime" jdbcType="VARCHAR" />
		<result column="DATE_TYPE" property="dateType" jdbcType="VARCHAR" />
		<result column="EFT_LEN" property="eftLen" jdbcType="VARCHAR" />
		<result column="GET_NO" property="getNo" jdbcType="VARCHAR" />
		<result column="GET_TIME" property="getTime" jdbcType="VARCHAR" />
		<result column="USE_TIME" property="useTime" jdbcType="VARCHAR" />
		<result column="TIME_UNIT" property="timeUnit"  />
		<result column="CPN_TIME_TYPE" property="cpnTimeType" />
		<result column="TIME_DURATION" property="timeDuration" />
		<result column="BUSI_TYPE" property="busiType" />
		<result column="CPN_DCT_TYPE" property="dctType" />
		<result column="CPN_COND_TYPE" property="cpnCondType" />
		<result column="CPN_ID" property="cpnId" />
		<result column="PROD_ID" property="prodId" />
		<result column="DCT_COND_ID" property="dctCondId" />
		<result column="CALC_PRECISION" property="calcPrecision" />
		<result column="CALC_PRECISION_DIGITS" property="calcPrecisionDigits" />
		<result column="DCT_PROD_ID" property="dctProdId" />
		<result column="DCT_BUSI_TYPE" property="dctBusiType" />
		<result column="DCT_COND_TYPE" property="dctCondType" />
		<result column="DCT_COND_VALUE" property="dctCondValue" />
		<result column="DCT_COND_FLAG" property="dctCondFlag" />
		<result column="DCT_CALC_METHOD" property="dctCalcMethod" />
		<result column="FILE_PATH" property="filePath" />
		<result column="CPN_ARR" property="cpnArr" />
		<result column="ACT_ID" property="ActId" />
		<result column="NEED_POINT_NUM" property="needPointNum" />
		<result column="DCT_COND_AREA_FLAG" property="dctCondAreaFlag" />
		<result column="DCT_COND_CITY" property="dctCondCity" />
		<result column="DCT_COND_STATION" property="dctCondStation" />
		<result column="PUT_NUM" property="putNum"/>
		<result column="TIME_COND_FLAG" property="timeCondFlag"/>
		<result column="TIME_COND_BEG" property="timeCondBeg"/>
		<result column="TIME_COND_END" property="timeCondEnd"/>
		<result column="DCT_COND_BUILD" property="dctCondBuild"/>
		<result column="SUPERPOSITION_FLAG" property="superpositionFlag"/>
		<result column="FULL_REDUCTION_AMT" property="fullReductionAmt"/>
		<result column="MAXIMUM_DISCOUNT_AMT" property="maximumDiscountAmt"/>
		<result column="CPN_PURPOSE" property="cpnPurpose"/>
		<result column="INTEGRAL_NUM_FLAG" property="integralNumFlag" jdbcType="VARCHAR" />
		<result column="INTEGRAL_NUM" property="integralNum" jdbcType="INTEGER" />
	</resultMap>

	<sql id="getCouponWhere">
		<if test="actType !=null and actType !=''">
			AND t.ACT_TYPE =#{actType}
		</if>
		<if test="cpnId != null and cpnId != ''">
			and c.CPN_ID = #{cpnId}
		</if>
		<if test="cpnNo != null and cpnNo != ''">
			and c.CPN_NO = #{cpnNo}
		</if>
		<if test="cpnType != null and cpnType != ''">
			and c.CPN_TYPE = #{cpnType}
		</if>
		<if test="cpnName != null and cpnName != ''">
			and c.CPN_NAME like concat('%',#{cpnName},'%')
		</if>
		<if test="cpnStatus != null and cpnStatus != ''">
			and c.CPN_STATE= #{cpnStatus}
		</if>
		<if test="invDate != null and invDate == 'now'">
			and ((c.CPN_TIME_TYPE = '1' and c.INV_DATE >= CURRENT_DATE()) or c.CPN_TIME_TYPE = '2')
		</if>
		<if test="orgCode != null and orgCode != ''">
			and c.ORG_CODE in(#{orgCode})
		</if>
		<if test="busiType != null and busiType != ''">
			and c.PROD_BUSI_TYPE = #{busiType}
		</if>
		<if test="actId != null and actId != ''">
			AND NOT EXISTS(SELECT 1 FROM e_coupon_push b WHERE b.cpn_id=c.cpn_id AND b.act_id=#{actId})
		</if>
		<if test="allCoupon == null">
			AND ( ( c.EFT_DATE &lt;= now() AND c.INV_DATE &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d') ) OR c.EFT_DATE IS NULL )
		</if>
		<!-- @description:批量查询  @author:biaoxiangd  @create: 2017-09-28 0:03 -->
		<if test="cpnIdList != null and cpnIdList.size() > 0">
			AND cpn_id in
			<foreach item="item" index="index" collection="cpnIdList" open="(" separator="," close=")">
				#{item}
			</foreach>

		</if>
	</sql>

<!-- 描述:优惠券管理-查询功能  创建人:biaoxiangd  创建时间:2017-06-03 11:23 -->
	<select id="getCoupons" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT c.cpn_id,c.cpn_name,cpn_type,
		prod_busi_type as busi_type,cpn_dct_type,cpn_amt,dct_calc_method,c.cpn_time_type,
		date_format(eft_date, '%Y-%m-%d') eft_date, date_format(inv_date, '%Y-%m-%d') inv_date,
		time_duration,c.time_unit,
		ifnull(cpn_num,'0')as cpn_num, ifnull(lim_get_num,'0') as lim_get_num, ifnull(alrdy_get_num,'0')  as alrdy_get_num,
		IFNULL((SELECT sum(put_num) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'0') put_num,
		cpn_marks,c.cpn_state as cpn_status,
		c.INTEGRAL_NUM as integralNum,
 		if ( cpn_time_type = '1',
			concat( date_format(eft_date, '%Y-%m-%d'), '~', date_format(inv_date, '%Y-%m-%d') ),
			concat( '领用或发放后', ifnull(time_duration,'0'))
		) effect_time,
		calc_precision,
		calc_precision_digits,
		date_format(c.cre_time, '%Y-%m-%d %H:%i') cre_time,
		file_path,
		FULL_REDUCTION_AMT,
		MAXIMUM_DISCOUNT_AMT,
		CPN_PURPOSE
		FROM
		e_coupon c
		<where>
			<include refid="getCouponWhere"></include>
		</where>
		order by CRE_TIME desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="getCouponsCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		select
		count(1)
		from e_coupon c
		<where>
			<include refid="getCouponWhere"></include>
		</where>
	</select>

<!-- 描述:获取计费参数(b_prod)，优惠条件内容  创建人:biaoxiangd  创建时间:2017-06-12 11:40 -->
	<select id="queryBProd" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT
		 CONCAT(PROD_NAME,'-',PROD_COMMENTS) AS PROD_NAME,PROD_ID,MEASURE_METHOD
		FROM b_prod a
		<where>
			MANDATORY_TYPE = '1'
			AND MEASURE_FLAG = '1'
			<if test="prodId != null and prodId != ''">
				AND a.prod_id = #{prodId}
			</if>
			<if test="busiType != null and busiType != ''">
				AND prod_busi_type = #{busiType}
			</if>
		</where>
		ORDER  by BUILD_DATE desc
	</select>

<!-- 描述:获取条件列表  创建人:biaoxiangd  创建时间:2017-06-12 11:40 -->
	<select id="queryDctCond" parameterType="string" resultMap="couponBo">
		SELECT DCT_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,SUPERPOSITION_FLAG,
		LEFT(STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') - 1)  DCT_PROD_ID,
		substring(STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') + 1)  DCT_BUSI_TYPE
		from(
		SELECT DCT_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,
		(SELECT CONCAT(PROD_ID,'_',PROD_BUSI_TYPE) FROM B_PROD p where p.PROD_ID = PROD_ID LIMIT 1) STR_BUSI_TYPE,SUPERPOSITION_FLAG
		FROM
		 E_DCT_COND b
		WHERE b.CPN_ID = #{cpnId}
		) ee
	</select>

<!-- 描述:新增优惠券  创建人:biaoxiangd  创建时间:2017-06-07 17:58 -->
	<insert id="insertCoupon" parameterType="com.ls.ner.billing.market.bo.CouponBo" useGeneratedKeys="true" keyProperty="cpnId">
		insert into e_coupon(
			ORG_CODE,CPN_NAME,CPN_TYPE,CPN_AMT,PROD_ID,PROD_BUSI_TYPE,CPN_DCT_TYPE,
			DCT_CALC_METHOD,CPN_TIME_TYPE,
			EFT_DATE,INV_DATE,TIME_DURATION,TIME_UNIT,CPN_NUM,LIM_GET_NUM,
			ALRDY_GET_NUM,CPN_MARKS,CPN_STATE,STATE_TIME,CRE_TIME,CRE_EMP,
			CALC_PRECISION,CALC_PRECISION_DIGITS,FULL_REDUCTION_AMT,MAXIMUM_DISCOUNT_AMT,CPN_PURPOSE,
			INTEGRAL_NUM
		)
		values(
			#{orgCode},
			#{cpnName},
			#{cpnType},
			#{cpnAmt},
			0,
			#{busiType},
			#{dctType},
			#{dctCalcMethod},
			#{cpnTimeType},
			#{eftDate},
			#{invDate},
			#{timeDuration},
			#{timeUnit},
			#{cpnNum},
			#{limGetNum},
			#{alrdyGetNum},
			#{cpnMarks},
			#{cpnStatus},
			now(),
			now(),
			#{creEmp},
			#{calcPrecision},
			#{calcPrecisionDigits},
			#{dctCondValue},
			#{maximumDiscountAmt},
		    #{cpnPurpose},
		    #{integralNum}
			)
	</insert>

	<!-- add biaoxiangd 新增优惠券条件 -->
	<insert id="insertDctCond" parameterType="com.ls.ner.billing.market.bo.CouponBo">
		INSERT into e_dct_cond(CPN_ID,DCT_COND_FLAG,DCT_COND_TYPE,PROD_ID,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,SUPERPOSITION_FLAG)
		values(#{cpnId},#{dctCondFlag},#{dctCondType},#{dctProdId},#{dctCondValue},#{dctCondAreaFlag},#{dctCondCity},#{dctCondStation},#{timeCondFlag},#{timeCondBeg},#{timeCondEnd},#{superpositionFlag})
	</insert>
	<!-- add biaoxiangd 更新优惠券条件 -->
	<update id="updateDctCond" parameterType="com.ls.ner.billing.market.bo.CouponBo">
		UPDATE e_dct_cond
		<set>
			<if test="dctCondFlag != null and dctCondFlag != ''">
				DCT_COND_FLAG = #{dctCondFlag},
			</if>
			<if test="dctCondType != null and dctCondType != ''">
				DCT_COND_TYPE = #{dctCondType},
			</if>
			<if test="dctProdId != null and dctProdId != ''">
				PROD_ID = #{dctProdId},
			</if>
			<if test="dctCondValue != null and dctCondValue != ''">
				DCT_COND_VALUE = #{dctCondValue},
			</if>
			DCT_COND_AREA_FLAG = #{dctCondAreaFlag},
			DCT_COND_CITY = #{dctCondCity},
			DCT_COND_STATION = #{dctCondStation},
			TIME_COND_FLAG = #{timeCondFlag},
			TIME_COND_BEG = #{timeCondBeg},
			TIME_COND_END = #{timeCondEnd},
			SUPERPOSITION_FLAG = #{superpositionFlag}
		</set>
		<where>
			<if test="ActId != null and ActId != ''">
				and ACT_ID = #{ActId}
			</if>
			<if test="cpnId != null and cpnId != ''">
				and CPN_ID = #{cpnId}
			</if>
		</where>
	</update>

<!-- 描述:更新优惠券  创建人:biaoxiangd  创建时间:2017-06-05 12:09 -->
	<update id="updateCoupon" parameterType="com.ls.ner.billing.market.bo.CouponBo">
		update e_coupon
		<set>
			<if test="orgCode != null and orgCode !='' ">
				ORG_CODE = #{orgCode},
			</if>
			<if test="cpnType != null and cpnType !=''">
				CPN_TYPE = #{cpnType},
			</if>
			<if test="cpnAmt != null and cpnAmt!='' ">
				CPN_AMT = #{cpnAmt},
			</if>
			<if test="busiType != null and busiType!='' ">
				PROD_BUSI_TYPE = #{busiType},
			</if>
			<if test="dctType != null and dctType!='' ">
				CPN_DCT_TYPE = #{dctType},
			</if>
			<if test="cpnTimeType != null and cpnTimeType!='' ">
				CPN_TIME_TYPE = #{cpnTimeType},
			</if>
			<if test="eftDate != null and eftDate !=''">
				EFT_DATE = #{eftDate},
			</if>
			<if test="invDate != null and invDate !=''">
				INV_DATE = #{invDate},
			</if>
			<if test="timeDuration != null and timeDuration !=''">
				TIME_DURATION = #{timeDuration},
			</if>
			<if test="timeUnit != null and timeUnit !=''">
				TIME_UNIT = #{timeUnit},
			</if>
			<if test="cpnNum != null and cpnNum !=''">
				CPN_NUM = #{cpnNum},
			</if>
			<if test="limGetNum != null and limGetNum !=''">
				LIM_GET_NUM = #{limGetNum},
			</if>
			<if test="alrdyGetNum != null and alrdyGetNum !=''">
				ALRDY_GET_NUM = #{alrdyGetNum},
			</if>
<!-- 描述:调用RPC04-02-003优惠券发放接口后更新   创建人:biaoxiangd  创建时间:2017-06-05 12:25 -->
			<if test="putNum != null and putNum !=''">
				ALRDY_GET_NUM = (IFNULL(ALRDY_GET_NUM,0) + CONVERT(#{putNum},SIGNED)),
			</if>
			<if test="cpnMarks != null and cpnMarks !=''">
				CPN_MARKS = #{cpnMarks},
			</if>
			<if test="cpnStatus != null and cpnStatus !=''">
				CPN_STATE = #{cpnStatus},
			</if>
			<if test="calcPrecision != null and calcPrecision !=''">
				CALC_PRECISION = #{calcPrecision},
			</if>
			<if test="calcPrecisionDigits != null and calcPrecisionDigits !=''">
				CALC_PRECISION_DIGITS = #{calcPrecisionDigits},
			</if>
			<if test="filePath != null and filePath !=''">
				FILE_PATH = #{filePath},
			</if>
			<if test="cpnName != null and cpnName !=''">
				CPN_NAME = #{cpnName},
			</if>
			<if test="isDeleteImg != null and isDeleteImg == '1'.toString()">
				FILE_PATH = #{filePath},
			</if>
			<if test="maximumDiscountAmt != null">
				MAXIMUM_DISCOUNT_AMT = #{maximumDiscountAmt},
			</if>
			<if test="cpnPurpose != null and cpnPurpose !='' ">
				CPN_PURPOSE = #{cpnPurpose},
			</if>
			<if test="integralNum != null">
				INTEGRAL_NUM = #{integralNum},
			</if>
		</set>
		<where>
			<if test="cpnId != null and cpnId !=''">
				and CPN_ID = #{cpnId}
			</if>
		</where>
	</update>
	<!-- 描述:查询注册送/邀请送，送的优惠券  创建人:cailianL  创建时间:2017-08-09 10:33 -->
	<select id="queryActCpn" parameterType="com.ls.ner.billing.mktact.bo.MarketActBo" resultMap="couponBo">
		SELECT
			b.CPN_ID,
			b.CPN_NAME,
			CONCAT(b.CPN_NAME, '$', b.CPN_ID) cpn_arr
		FROM
			e_mkt_act_give a,
			e_coupon b
		WHERE
			a.CPN_ID = b.CPN_ID
			AND a.ACT_ID = #{actId}
	</select>

	<select id="getRestCoupons" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT
			c.cpn_id,
			c.cpn_name,
			c.cpn_type,
			c.prod_busi_type as busi_type,
			c.cpn_dct_type,cpn_amt,
			c.dct_calc_method,
			c.cpn_time_type,
		    date_format(c.eft_date, '%Y-%m-%d') eft_date,
			date_format(c.inv_date, '%Y-%m-%d') inv_date,
			c.time_duration,
			c.time_unit,
			ifnull(c.cpn_num,'0')as cpn_num,
			ifnull(c.lim_get_num,'0') as lim_get_num,
			ifnull(c.alrdy_get_num,'0')  as alrdy_get_num,
			IFNULL((SELECT sum(put_num) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'0') put_num,
			c.cpn_marks,
			c.cpn_state as cpn_status,
			if ( c.cpn_time_type = '1',
				concat( date_format(c.eft_date, '%Y-%m-%d'), '~', date_format(c.inv_date, '%Y-%m-%d') ),
				concat( '领用或发放后', ifnull(c.time_duration,'0'))
			) effect_time,
			c.calc_precision,
			c.calc_precision_digits,
			date_format(c.cre_time, '%Y-%m-%d %H:%i') cre_time,
			c.file_path,
			t.ACT_ID,
			t.NEED_POINT_NUM
		FROM
			e_coupon c,E_COUPON_PUSH p,E_MKT_ACT t
		<where>
			c.CPN_ID = p.CPN_ID
			AND p.ACT_ID = t.ACT_ID
			AND t.ACT_STATE = '2'
			<if test="sourceApi == null or sourceApi != 'qrcode' ">
			AND t.IS_LINK <![CDATA[ <> ]]> '1'
			</if>
			AND <![CDATA[date_format(t.EFF_TIME, '%Y-%m-%d %H:%i') <= date_format(now(), '%Y-%m-%d %H:%i')]]>
			AND <![CDATA[date_format(t.EXP_TIME, '%Y-%m-%d %H:%i') >= date_format(now(), '%Y-%m-%d %H:%i')]]>
			<include refid="getCouponWhere"></include>
			<if test="aId != null and aId != ''">
				AND t.act_id = #{aId}
			</if>
		</where>
		order by CRE_TIME desc
	</select>

	<select id="queryInvCoupon" resultType="java.util.Map">
		SELECT
			CPN_ID cpnId,
			CPN_NAME cpnName,
			CPN_TYPE cpnType,
			PROD_BUSI_TYPE prodBusiType,
			CPN_AMT cpnAmt,
			EFT_DATE eftDate,
			INV_DATE invDate,
			TIME_DURATION timeDuration,
			TIME_UNIT timeUnit,
			CPN_STATE cpnState
		FROM
			e_coupon
		WHERE
			CPN_STATE = '1'
		AND
			<![CDATA[DATE_FORMAT(INV_DATE,"%Y-%m-%d") < DATE_FORMAT(now(),"%Y-%m-%d")]]>
	</select>

	<update id="updateCpnInv" parameterType="java.util.Map">
		UPDATE e_coupon SET
			CPN_STATE = #{cpnState}
		WHERE
		   	cpn_id in
			<foreach item="item" index="index" collection="cpnIdList" open="(" separator="," close=")">
				#{item}
			</foreach>
	</update>

	<select id="queryAct" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT
			a.ACT_ID actId, IFNULL(a.ACT_TYPE,"") actType,IFNULL(a.ACT_NAME,"") actName,IFNULL(a.ACT_MARKS,"") actMarks,IFNULL(a.PROD_BUSI_TYPE,"") prodBusiType,IFNULL(b.DCT_TYPE,"") dctType,IFNULL(c.DCT_COND_VALUE,"") dctCondValue,
			 IFNULL(d.DCT_VALUE,"") dctValue,IFNULL(a.act_sub_type,"") actSubType
		FROM e_mkt_act a LEFT JOIN e_mkt_act_cond b ON a.ACT_ID=b.ACT_ID
		LEFT JOIN e_mkt_act_cond_det c ON b.ACT_COND_ID=c.ACT_COND_ID
		LEFT JOIN e_mkt_act_dct d ON c.ACT_COND_DET_ID=d.ACT_COND_DET_ID
		where
		a.ACT_STATE='2'
		<if test="actType != null and actType !=''">
			 AND a.ACT_TYPE =#{actType}
		</if>
		<if test="actType != null and actType !='' and actType == '07'.toString()">
			order by a.CRE_TIME desc limit 1
		</if>

	</select>
	<select id="querySectionList" resultType="java.util.Map" parameterType="java.util.Map">
		SELECT a.ACT_ID actId,IFNULL(a.PRESENT_SECTION_ID,"") presentSectionId, IFNULL(b.SECTION_DET_ID,"") sectionDetId,
			IFNULL(b.REF_CEIL,"") refCeil, IFNULL(b.REF_FLOOR,"") refFloor,IFNULL(b.BASE_VALUE,"") baseValue,IFNULL(b.CPN_ID,"") cpnIds,IFNULL(a.PRESENT_BAL_TYPE,"") presentBalType,IFNULL(c.ACT_SUB_TYPE,"") actSubType
			 FROM  e_bal_present_section a LEFT JOIN e_bal_present_section_det  b ON a.PRESENT_SECTION_ID=b.PRESENT_SECTION_ID
			 LEFT JOIN e_mkt_act c ON  a.ACT_ID=c.ACT_ID
		<where>
			<if test=" actId != null and actId != ''">
				AND a.ACT_ID=#{actId}
			</if>
			<if test=" actSubType != null and actSubType != ''">
				AND c.ACT_SUB_TYPE = #{actSubType}
			</if>
		</where>
	</select>
	<select id="queryCpnById" resultType="java.util.Map">
		SELECT
		a.CPN_ID cpnId,IFNULL(a.CPN_NAME,"") cpnName,IFNULL(a.CPN_TYPE,"") cpnType,IFNULL(a.CPN_DCT_TYPE,"")
		cpnDctType,IFNULL(a.CPN_AMT,"") cpnAmt,IFNULL(a.EFT_DATE,"") eftDate,
		IFNULL(a.INV_DATE,"") expDate,IFNULL(a.CPN_MARKS,"") cpnMarks,IFNULL(a.CPN_STATE,"")
		cpnState,IFNULL(a.CPN_AMT,"") cpnSpAmt, TIME_DURATION timeDuration
		FROM e_coupon a
		WHERE a.CPN_ID IN
		<foreach collection="array" open="(" separator="," close=")" item="item">
			#{item}
		</foreach>

	</select>

	<select id="qryCouponList" resultType="java.util.Map">
		SELECT
			a.cpn_id   cpnId,
			a.CPN_NAME cpnName,
			a.CPN_AMT cpnAmt,
			a.CPN_MARKS cpnMarks,
			b.DCT_COND_CITY city,
			b.DCT_COND_STATION stationId
		FROM
			e_coupon a
			LEFT JOIN e_dct_cond b on a.CPN_ID = b.CPN_ID
			WHERE a.cpn_id in
				<foreach item="item" index="index" collection="cpnIds" open="(" separator="," close=")">
					#{item}
				</foreach>
	</select>

	<select id="queryCpnNum" resultType="java.util.Map">
		select  cpn_id cpnId,
				LIM_GET_NUM limitNum
		from e_coupon
		where cpn_id in
		<foreach item="item" index="index" collection="cpnIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
		group by cpn_id
	</select>

	<select id="queryGetCpnNum" resultType="java.lang.Integer">
		select count(1)
		from ner_defray.d_account a
				 join ner_defray.d_account_coupon b on a.acc_id = b.acc_id
		where b.cpn_id = #{cpnId} and a.cust_id = #{custId}
	</select>

	<select id="getMyCarCoupon" resultType="java.util.Map">
		select CPN_ID as cpnId,CPN_PURPOSE as cpnPurpose,CPN_NAME as cpnName
		from e_coupon
		where
		    CPN_PURPOSE = '1'
		    and CPN_STATE = '1'
		    order by DATA_OPER_TIME desc
		limit 1
	</select>

	<select id="getAllowCoupons" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT
		c.cpn_id,
		c.cpn_name,
		c.cpn_type,
		c.prod_busi_type as busi_type,
		c.cpn_dct_type,cpn_amt,
		c.dct_calc_method,
		c.cpn_time_type,
		date_format(c.eft_date, '%Y-%m-%d') eft_date,
		date_format(c.inv_date, '%Y-%m-%d') inv_date,
		c.time_duration,
		c.time_unit,
		ifnull(c.cpn_num,'0')as cpn_num,
		ifnull(c.lim_get_num,'0') as lim_get_num,
		ifnull(c.alrdy_get_num,'0')  as alrdy_get_num,
		IFNULL((SELECT sum(put_num) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'0') put_num,
		c.cpn_marks,
		c.cpn_state as cpn_status,
		if ( c.cpn_time_type = '1',
		concat( date_format(c.eft_date, '%Y-%m-%d'), '~', date_format(c.inv_date, '%Y-%m-%d') ),
		concat( '领用或发放后', ifnull(c.time_duration,'0'))
		) effect_time,
		c.calc_precision,
		c.calc_precision_digits,
		date_format(c.cre_time, '%Y-%m-%d %H:%i') cre_time,
		c.file_path,
		t.ACT_ID,
		t.NEED_POINT_NUM
		FROM
		e_coupon c,E_COUPON_PUSH p,E_MKT_ACT t,e_dct_cond d
		<where>
			c.CPN_ID = p.CPN_ID
			AND c.CPN_ID = d.CPN_ID
			AND p.ACT_ID = t.ACT_ID
			AND t.ACT_STATE = '2'
			AND <![CDATA[date_format(t.EFF_TIME, '%Y-%m-%d %H:%i') <= date_format(now(), '%Y-%m-%d %H:%i')]]>
			AND <![CDATA[date_format(t.EXP_TIME, '%Y-%m-%d %H:%i') >= date_format(now(), '%Y-%m-%d %H:%i')]]>
			<include refid="getCouponWhere"></include>
			<if test="allFlag != null and allFlag !='' and allFlag == '0'.toString()">

				and (IFNULL(d.DCT_COND_BUILD,'')=1 or d.DCT_COND_BUILD LIKE CONCAT('%',#{buildId},'%'))
				and (IFNULL(d.DCT_COND_CITY,'')=1 or d.DCT_COND_CITY LIKE CONCAT('%',#{city},'%'))
				and (IFNULL(d.DCT_COND_STATION,'')=1 or d.DCT_COND_STATION LIKE CONCAT('%',#{stationId},'%'))
			</if>
		</where>
		order by CRE_TIME desc
	</select>

	<select id="newGetCoupons" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT c.cpn_id,c.cpn_name,cpn_type,
		prod_busi_type as busi_type,cpn_dct_type,cpn_amt,dct_calc_method,c.cpn_time_type,
		date_format(eft_date, '%Y-%m-%d') eft_date, date_format(inv_date, '%Y-%m-%d') inv_date,
		time_duration,c.time_unit,
		ifnull(cpn_num,'0')as cpn_num, ifnull(lim_get_num,'0') as lim_get_num, ifnull(alrdy_get_num,'0')  as alrdy_get_num,
		IFNULL((SELECT sum(put_num) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'0') put_num,
		cpn_marks,c.cpn_state as cpn_status,
		if ( cpn_time_type = '1',
		concat( date_format(eft_date, '%Y-%m-%d'), '~', date_format(inv_date, '%Y-%m-%d') ),
		concat( '领用或发放后', ifnull(time_duration,'0'))
		) effect_time,
		calc_precision,
		calc_precision_digits,
		date_format(c.cre_time, '%Y-%m-%d %H:%i') cre_time,
		file_path
		FROM
		e_coupon c left join e_dct_cond d on c.cpn_id = d.cpn_id
		<where>
			<include refid="getCouponWhere"></include>
			<if test="cpnAmtBgn != null and cpnAmtBgn !=''">
				AND <![CDATA[c.CPN_AMT >= #{cpnAmtBgn}]]>
			</if>
			<if test="cpnAmtEnd != null and cpnAmtEnd !=''">
				AND <![CDATA[c.CPN_AMT <= #{cpnAmtEnd}]]>
			</if>
			<if test="buildId !=null and buildId == '1'.toString()">
				AND (d.DCT_COND_BUILD = 1)
			</if>
			<if test="buildId !=null and buildId !='' and buildId != '1'.toString() ">
                AND (d.DCT_COND_BUILD LIKE "%"#{buildId}"%")
			</if>
		</where>
		order by CRE_TIME desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="newGetCouponsCount" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultType="int">
		select
		count(1)
		from e_coupon c left join e_dct_cond d on c.cpn_id = d.cpn_id
		<where>
			<include refid="getCouponWhere"></include>
			<if test="cpnAmtBgn != null and cpnAmtBgn !=''">
				AND <![CDATA[d.CPN_AMT >= #{cpnAmtBgn}]]>
			</if>
			<if test="cpnAmtEnd != null and cpnAmtEnd !=''">
				AND <![CDATA[d.CPN_AMT <= #{cpnAmtEnd}]]>
			</if>
			<if test="buildId !=null and buildId !=''">
				AND (d.DCT_COND_BUILD LIKE "%"#{buildId}"%")
			</if>
		</where>
	</select>

	<delete id="deleteByCpnId" parameterType="String" >
		delete from e_coupon
		where CPN_ID = #{cpnId}
	</delete>


	<insert id="insertDctCondNew" parameterType="com.ls.ner.billing.market.bo.CouponBo">
		INSERT into e_dct_cond(CPN_ID,DCT_COND_FLAG,DCT_COND_TYPE,PROD_ID,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,DCT_COND_BUILD)
		values(#{cpnId},#{dctCondFlag},#{dctCondType},#{dctProdId},#{dctCondValue},#{dctCondAreaFlag},#{dctCondCity},#{dctCondStation},#{timeCondFlag},#{timeCondBeg},#{timeCondEnd},#{dctCondBuild})
	</insert>
	<update id="updateDctCondNew" parameterType="com.ls.ner.billing.market.bo.CouponBo">
		UPDATE e_dct_cond
		<set>
			<if test="dctCondFlag != null and dctCondFlag != ''">
				DCT_COND_FLAG = #{dctCondFlag},
			</if>
			<if test="dctCondType != null and dctCondType != ''">
				DCT_COND_TYPE = #{dctCondType},
			</if>
			<if test="dctProdId != null and dctProdId != ''">
				PROD_ID = #{dctProdId},
			</if>
			<if test="dctCondValue != null and dctCondValue != ''">
				DCT_COND_VALUE = #{dctCondValue},
			</if>
			DCT_COND_AREA_FLAG = #{dctCondAreaFlag},
			DCT_COND_CITY = #{dctCondCity},
			DCT_COND_STATION = #{dctCondStation},
			DCT_COND_BUILD = #{dctCondBuild},
			TIME_COND_FLAG = #{timeCondFlag},
			TIME_COND_BEG = #{timeCondBeg},
			TIME_COND_END = #{timeCondEnd}
		</set>
		<where>
			<if test="ActId != null and ActId != ''">
				and ACT_ID = #{ActId}
			</if>
			<if test="cpnId != null and cpnId != ''">
				and CPN_ID = #{cpnId}
			</if>
		</where>
	</update>


	<select id="queryDctCondNew" parameterType="string" resultMap="couponBo">
		SELECT DCT_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,SUPERPOSITION_FLAG,
		LEFT(STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') - 1)  DCT_PROD_ID,
		substring(STR_BUSI_TYPE,INSTR(STR_BUSI_TYPE, '_') + 1)  DCT_BUSI_TYPE,DCT_COND_BUILD
		from(
		SELECT DCT_COND_ID,DCT_COND_FLAG,DCT_COND_TYPE,DCT_COND_VALUE,DCT_COND_AREA_FLAG,DCT_COND_CITY,DCT_COND_STATION,TIME_COND_FLAG,TIME_COND_BEG,TIME_COND_END,SUPERPOSITION_FLAG,
		(SELECT CONCAT(PROD_ID,'_',PROD_BUSI_TYPE) FROM B_PROD p where p.PROD_ID = PROD_ID LIMIT 1) STR_BUSI_TYPE,DCT_COND_BUILD
		FROM
		E_DCT_COND b
		WHERE b.CPN_ID = #{cpnId}
		) ee
	</select>

	<select id="couponsDetailInfo" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			c.cpn_id cpnId,
			c.cpn_name cpnName,
			cpn_type cpnType,
			prod_busi_type as busiType,
			cpn_dct_type cpnDctType,
			cpn_amt cpnAmt,
			dct_calc_method dctCalcMethod,
			c.cpn_time_type cpnTimeType,
			date_format(eft_date, '%Y-%m-%d') eftDate,
			date_format(inv_date, '%Y-%m-%d') invDate,
			time_duration timeDuration,
			c.time_unit timeUnit,
			ifnull(cpn_num,'0') as cpnNum,
			ifnull(lim_get_num,'0') as limGetNum,
			ifnull(alrdy_get_num,'0')  as alrdyGetNum,
			concat((SELECT ifnull(sum(put_num),0) FROM e_coupon_put p WHERE p.cpn_id = c.cpn_id),'') as putNum,
			cpn_marks cpnMarks,
			c.cpn_state as cpnState,
			c.INTEGRAL_NUM as integralNum,
			if ( cpn_time_type = '1',
			concat( date_format(eft_date, '%Y-%m-%d'), '~', date_format(inv_date, '%Y-%m-%d') ),
			concat( '领用或发放后', ifnull(time_duration,'0'))
			) effectTime,
			calc_precision calcPrecision,
			calc_precision_digits calcPrecisionDigits,
			date_format(c.cre_time, '%Y-%m-%d %H:%i') creTime,
			file_path filePath
		FROM
			e_coupon c
		<where>
			<include refid="getCouponWhere"></include>
		</where>
		order by CRE_TIME desc
	</select>
	<select id="getGoodsVrCoupon"  resultType="java.util.Map">
		select cpn_id, cpn_name,time_unit,cpn_time_type,lim_get_num,cpn_amt,cpn_marks,
		concat( date_format(eft_date, '%Y-%m-%d'), '至', date_format(inv_date, '%Y-%m-%d') )effect_time
		from e_coupon  where		cpn_time_type = '1'
		and CPN_STATE='1'
		AND <![CDATA[date_format(eft_date, '%Y-%m-%d') <= date_format(now(), '%Y-%m-%d')]]>
		AND <![CDATA[date_format(inv_date, '%Y-%m-%d') >= date_format(now(), '%Y-%m-%d')]]>
		UNION all
		select cpn_id, cpn_name,time_unit,cpn_time_type,lim_get_num,cpn_amt,cpn_marks,
		concat( '领用或发放后', ifnull(time_duration,'0')) effect_time from e_coupon  where	cpn_time_type = '2'

		and CPN_STATE='1'
	</select>
    <select id="selectCouponId" resultType="java.lang.Long">
		SELECT
		p.CPN_ID
		FROM
		ner_billing.e_coupon_push p
		<where>
			<if test="actId != null">
				p.ACT_ID = #{actId}
			</if>
		</where>
	</select>

	<select id="getCouponCpnPurposeDetail" parameterType="com.ls.ner.billing.market.vo.MarketCondition" resultMap="couponBo">
		SELECT c.cpn_id,c.cpn_name,cpn_type,CPN_PURPOSE, c.INTEGRAL_NUM as integralNum
		FROM
		e_coupon c
		<where>
			<if test="cpnStatus != null and cpnStatus != ''">
				c.CPN_STATE = #{cpnStatus}
			</if>
			<if test="cpnId != null and cpnId != ''">
				and c.CPN_ID = #{cpnId}
			</if>
			<if test="cpnPurpose != null and cpnPurpose != ''">
				and c.CPN_PURPOSE = #{cpnPurpose}
			</if>
		</where>
		order by c.CRE_TIME desc
		limit 1
	</select>

	<select id="qryCustCouponList" resultType="java.util.Map">
		select cpn_id cpnId, EFT_DATE eftDate, EXP_DATE expDate
		from ner_defray.d_account_coupon
		<where>
			<if test="cpnIdList != null and cpnIdList.size() > 0">
				and cpn_id in
				<foreach collection="cpnIdList" item="cpnId" open="(" close=")" separator=",">
					#{cpnId}
				</foreach>
			</if>
			order by DATA_OPER_TIME desc
		</where>
	</select>
</mapper>
