package com.ls.ner.billing.tariff2.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.Validate;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ls.ner.base.constants.BizConstants.DataOperType;
import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.ValidFlag;
import com.ls.ner.billing.tariff2.service.ITariffPlanService;
import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.common.bo.TariffPlanBoExample;
import com.ls.ner.billing.common.dao.TariffPlanBoMapper;
import com.ls.ner.billing.tariff2.dao.ITariffPlanDao;
import com.ls.ner.billing.tariff2.util.TimePointUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;

@Component
public class TariffPlanServiceImpl implements ITariffPlanService {

	@Autowired
	TariffPlanBoMapper tariffPlanBoMapper;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;
	
	@Autowired
	ITariffPlanDao tariffPlanDao;
	@ServiceAutowired
	Authentication authentication; 
	@ServiceAutowired(serviceTypes = ServiceType.RPC,value="orgRpcService")
	private IOrgRpcService orgRpcService;
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	
	public TariffPlanBo getByPlanNo(String planNo){
		TariffPlanBoExample example = new TariffPlanBoExample();
		example.createCriteria().andPlanNoEqualTo(planNo);
		example.setOrderByClause("SYSTEM_ID desc");
		List<TariffPlanBo> result = tariffPlanBoMapper.selectByExample(example);
		if(result!=null&&result.size()>0){
			for (TariffPlanBo tariffPlanBo : result) {
				if(!StringUtils.nullOrBlank(tariffPlanBo.getOrgCode())){
					OrgBo org=orgService.getOrgByNo(tariffPlanBo.getOrgCode());
					if(org!=null){
						tariffPlanBo.setOrgCodeName(org.getOrgShortName());
					}
				}
			}
		}
		return result.get(0);
	}
	
	public List<TariffPlanBo> getTariffPlanList(TariffPlanBo bo){
		if(!StringUtils.nullOrBlank(bo.getOrgCode())){
			String[] orgCode=bo.getOrgCode().split(",");
			List<String> orgCodes=new ArrayList<String>();
			for (String string : orgCode) {
				orgCodes.add(string);
			}
			bo.setOrgCodes(orgCodes);
			bo.setOrgCode(null);
		}else{
			try {
				bo.setOrgCodes(orgRpcService.getSubOrgCodes());
			} catch (Exception e) {
				Validate.isTrue(false,e.getMessage());
			}
		}
		int count = tariffPlanDao.countTariff(bo);
		bo.setRows(count);
		List<TariffPlanBo> list = tariffPlanDao.pagiTariff(bo);
		if(list!=null&&list.size()>0){
			for (TariffPlanBo tariffPlanBo : list) {
				if(!StringUtils.nullOrBlank(tariffPlanBo.getOrgCode())){
					OrgBo org=orgService.getOrgByNo(tariffPlanBo.getOrgCode());
					if(org!=null){
						tariffPlanBo.setOrgCodeName(org.getOrgShortName());
					}
				}
			}
		}
		return list;
	}
	
	public String create(TariffPlanBo bo){
		
//		1、车辆租赁费用： 以里程+按时计费方式
//
//		   1）计时最小单位为小时，不足1小时按1小时收费
//
//		   2）里程最短10公里起，  <= 10公里，按10计费，>10公里，按实际里程计费
//
//		2、基本保险费： 按次收费
//
//		3、手续费：按次收费
//
//		4、超里程服务费：每单元24小时内，最长里程为300公里，超过部分将收取一次性服务费

		TimePointUtil util = new TimePointUtil();
		bo.setMillStepSplitPoints(util.getResStrCom(bo.getMillStepSplitPoints()));
		bo.setPeriodSplitPoints(util.getResStr(bo.getPeriodSplitPoints()));
		bo.setTimeStepSplitPoints(util.getResStrCom(bo.getTimeStepSplitPoints()));
		
//		bo.setPlanRemark("");
		AccountBo currentAccount = authentication.getCurrentAccount();
		if(currentAccount==null){
			Validate.isTrue(false,"当前人员没有登录，或是会话已失效");
		}
		if(StringUtils.nullOrBlank(bo.getOrgCode())){
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo == null || StringUtils.nullOrBlank( orgBo.getOrgCode() ) ){
				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
			}
			bo.setOrgCode(orgBo.getOrgCode());
		}
		
		bo.setOperName(currentAccount.getAccountName());
		bo.setDataOperTime(new DateTime().toDate());
		bo.setValidFlag(ValidFlag.DRAFT);
		String planNo = bo.getPlanNo();
		if(StringUtils.nullOrBlank(planNo)){
			planNo = genPlanNo();
			bo.setPlanNo(planNo);
			bo.setCreateTime(new DateTime().toDate());
			bo.setDataOperType(DataOperType.I);
			tariffPlanBoMapper.insertSelective(bo);
		} else {
			bo.setDataOperType(DataOperType.U);
			bo.setPlanNo(null);
			TariffPlanBoExample example = new TariffPlanBoExample();
			example.createCriteria().andPlanNoEqualTo(planNo);
			tariffPlanBoMapper.updateByExampleSelective(bo, example);
		}
		
		return planNo;
	}
	
	public void update(TariffPlanBo bo){
		String chargeWay = bo.getChargeWay();
		String chargeMode = bo.getChargeMode();
		TimePointUtil util = new TimePointUtil();
		if(ChargeWay.BY_TIME.equals(chargeWay)){
			bo.setMillPerPriceUnit(null);
			bo.setMillPerPriceValue(null);
			if(ChargeMode.STANDARD.equals(chargeMode)){
				bo.setMillStepSplitPoints(null);
				bo.setTimeStepSplitPoints(null);
				bo.setPeriodSplitPoints(null);
			} else if(ChargeMode.PERIOD.equals(chargeMode)){
				bo.setMillStepSplitPoints(null);
				bo.setTimeStepSplitPoints(null);
			} else {
				bo.setMillStepSplitPoints(null);
				bo.setPeriodSplitPoints(null);
			}
		}else if(ChargeWay.BY_MILL.equals(chargeWay)){
			bo.setTimePerPriceUnit(null);
			bo.setTimePerPriceValue(null);
			if(ChargeMode.STANDARD.equals(chargeMode)){
				bo.setTimeStepSplitPoints(null);
				bo.setMillStepSplitPoints(null);
				bo.setPeriodSplitPoints(null);
			} else if(ChargeMode.PERIOD.equals(chargeMode)){
				bo.setTimeStepSplitPoints(null);
				bo.setMillStepSplitPoints(null);
			} else {
				bo.setTimeStepSplitPoints(null);
				bo.setPeriodSplitPoints(null);
			}
		} else {
			if(ChargeMode.STANDARD.equals(chargeMode)){
				bo.setTimeStepSplitPoints(null);
				bo.setMillStepSplitPoints(null);
				bo.setPeriodSplitPoints(null);
			} else if(ChargeMode.PERIOD.equals(chargeMode)){
				bo.setTimeStepSplitPoints(null);
				bo.setMillStepSplitPoints(null);
			} else {
				bo.setPeriodSplitPoints(null);
			}
		}
		if(!StringUtils.nullOrBlank(bo.getMillStepSplitPoints())){
			bo.setMillStepSplitPoints(util.getResStrCom(bo.getMillStepSplitPoints()));
		}
		if(!StringUtils.nullOrBlank(bo.getPeriodSplitPoints())){
			bo.setPeriodSplitPoints(util.getResStr(bo.getPeriodSplitPoints()));
		}
		if(!StringUtils.nullOrBlank(bo.getTimeStepSplitPoints())){
			bo.setTimeStepSplitPoints(util.getResStrCom(bo.getTimeStepSplitPoints()));
		}
		AccountBo currentAccount = authentication.getCurrentAccount();
		if(currentAccount==null){
			Validate.isTrue(false,"当前人员没有登录，或是会话已失效");
		}
		if(StringUtils.nullOrBlank(bo.getOrgCode())){
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo == null || StringUtils.nullOrBlank( orgBo.getOrgCode() ) ){
				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
			}
			bo.setOrgCode(orgBo.getOrgCode());
		}
		bo.setOperName(currentAccount.getAccountName());
		bo.setDataOperType(DataOperType.U);
		bo.setDataOperTime(new DateTime().toDate());
		tariffPlanDao.updateByPlanNo(bo);
	}
	protected String genPlanNo() {
		return seqRpcService.getDefNo();
	}

	@Override
	public void deletePlans(String[] ids) {
		for (String planNo : ids) {
			TariffPlanBoExample example = new TariffPlanBoExample();
			example.createCriteria().andPlanNoEqualTo(planNo);
			tariffPlanBoMapper.deleteByExample(example);
		}
	}

	@Override
	public void startTariff(String[] ids) {
		for (String planNo : ids) {
			TariffPlanBo record = new TariffPlanBo();
			record.setValidFlag(ValidFlag.VALID);
			record.setDataOperType(DataOperType.U);
			record.setDataOperTime(new DateTime().toDate());
			TariffPlanBoExample example = new TariffPlanBoExample();
			example.createCriteria().andPlanNoEqualTo(planNo);
			tariffPlanBoMapper.updateByExampleSelective(record , example );
		}
	}

	@Override
	public void stopTariff(String[] ids) {
		for (String planNo : ids) {
			TariffPlanBo record = new TariffPlanBo();
			record.setValidFlag(ValidFlag.INVALID);
			record.setDataOperType(DataOperType.U);
			record.setDataOperTime(new DateTime().toDate());
			TariffPlanBoExample example = new TariffPlanBoExample();
			example.createCriteria().andPlanNoEqualTo(planNo);
			tariffPlanBoMapper.updateByExampleSelective(record , example );
		}
	}

	@Override
	public void copyTariff(TariffPlanBo bo) {
		TariffPlanBoExample example = new TariffPlanBoExample();
		example.createCriteria().andPlanNoEqualTo(bo.getPlanNo());
		List<TariffPlanBo> list = tariffPlanBoMapper.selectByExample(example );
		if(list!=null&&list.size()>0){
			AccountBo currentAccount = authentication.getCurrentAccount();
			if(currentAccount==null){
				Validate.isTrue(false,"当前人员没有登录，或是会话已失效");
			}
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo == null || StringUtils.nullOrBlank( orgBo.getOrgCode() ) ){
				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
			}
			TariffPlanBo tariffPlanBo = list.get(0);
			tariffPlanBo.setPlanNo(genPlanNo());
			tariffPlanBo.setDataOperType(DataOperType.I);
			tariffPlanBo.setDataOperTime(new DateTime().toDate());
			tariffPlanBo.setPlanName(tariffPlanBo.getPlanName()+"-复制模版");
			tariffPlanBo.setOrgCode(orgBo.getOrgCode());
			tariffPlanBo.setOperName(currentAccount.getAccountName());
			tariffPlanBo.setCreateTime(new DateTime().toDate());
			tariffPlanBo.setValidFlag(ValidFlag.DRAFT);
			tariffPlanBo.setSystemId(null);
			tariffPlanBoMapper.insertSelective(tariffPlanBo);
		}
	}
}
