/**
 *
 * @(#) RentBillRpcServiceImpl.java
 * @Package com.ls.ner.billing.rent.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.rent.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.rent.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo;
import com.ls.ner.billing.api.rent.model.RentRun.DateItem;
import com.ls.ner.billing.api.rent.model.RentRun.Item;
import com.ls.ner.billing.api.rent.model.RentRun.PeriodItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem.Limit;
import com.ls.ner.billing.api.rent.service.IRentBillRpcService;
import com.ls.ner.billing.api.rent.service.IRentbillService;
import com.ls.ner.billing.config.service.impl.BillingConfigServiceImpl;
import com.ls.ner.util.AssertUtil;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.JodaDateTime;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.ls.ner.util.json.JsonMapper;
import com.ls.ner.util.webservice.WsclientUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;

/**
 *  Description : 租车定价RPC
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月27日 上午10:49:50   qixiyu   Created.
 *           
 */
@Service(target = { ServiceType.RPC }, value = "rentBillRpcService")
public class RentBillRpcServiceImpl implements IRentBillRpcService {
	private static final Logger logger = LoggerFactory.getLogger(RentBillRpcServiceImpl.class);
	
	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	
	@ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
	private ICodeService codeService;
	
	@ServiceAutowired("rentbillService")
	private IRentbillService rentbillService;
	
	@Autowired
	private BillingConfigServiceImpl billingConfigServiceImpl;
	
	@Autowired
	private RentBillingCalculator billingCalculator;
	/**
	 * 获取租车产品定价描述
	 * <li> map.put("prodBusiType") 产品业务类
	 * <li> map.put("prodType") 产品类别
	 * <li> map.put("prodModeId") 产品模式ID
	 * <li> map.put("city") 城市
	 * <li> map.put("stationId") 站点ID
	 * <li> map.put("modelList") 车辆型号列表<modelId>
	 * 		
	 */
	@Override
	public Map<String, Object> getRentPrice(Map<String, Object> inMap) {
		logger.debug("getRentPrice--in:{}",inMap);
		Map<String, Object> resultMap = Maps.newHashMap();
		List<CodeBO> rentTypeList = codeService.getStandardCodes(SubBe.CODE_TYPE,null);
		List<String> rentTypes = new ArrayList<String>();
		if(rentTypeList!=null&&rentTypeList.size()>0){
			for (CodeBO codeBO : rentTypeList) {
				rentTypes.add(codeBO.getCodeValue());
			}
		}
		if(inMap.get("prodModeId")!=null&&!inMap.get("prodModeId").equals("")){
			if(String.valueOf(inMap.get("prodModeId")).length()==2){
				List<CodeBO> codeList = codeService.getSubStandardCodes(SubBe.CODE_TYPE, String.valueOf(inMap.get("prodModeId")), null);
				rentTypes = Lists.newArrayList();
				if(CollectionUtils.isNotEmpty(codeList)){
					for (CodeBO codeBO : codeList) {
						rentTypes.add(codeBO.getCodeValue());
					}
                    if (SubBe.HOUR.equals(inMap.get("prodModeId"))) {
                        rentTypes.add(SubBe.HOUR);
                    }
                } else {
					return resultMap;
				}
			} else {
				if(!rentTypes.contains(inMap.get("prodModeId"))){
					logger.error("租车模式参数错误");
					resultMap.put("prodModeList", Lists.newArrayList());
					return resultMap;
				}
				rentTypes = Lists.newArrayList();
				rentTypes.add((String)inMap.get("prodModeId"));
			}
		} else {
			rentTypes = Lists.newArrayList();
			rentTypes.add(SubBe.HOUR);
		}
		resultMap = this.getCommonPrice(inMap, rentTypes);
		logger.debug("getRentPrice--out:{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}
	
	@SuppressWarnings("unchecked")
	private Map<String, Object> getCommonPrice(Map<String, Object> inMap,List<String> rentTypes) {
		Map<String, Object> resultMap = Maps.newHashMap();
		List<OrgBo> orgBoList = orgService.getRootOrgs();
		List<Map<String,Object>> modelList =(List<Map<String,Object>>)inMap.get("modelList");
		List<Map<String,Object>> prodModeList = Lists.newArrayList();
		resultMap.put("prodModeList", prodModeList);
		for (String rentType : rentTypes) {//时租日租等租车方式循环
			Map<String,Object> prodModeMap = Maps.newHashMap();
			prodModeMap.put("prodModeId", rentType);
			prodModeMap.put("prodModeName", codeService.getStandardCode(SubBe.CODE_TYPE, rentType, null).getCodeName());
			prodModeMap.put("startTime", "");
			prodModeMap.put("endTime", "");
			prodModeMap.put("prodModeDesc", "");
			List<Map<String,Object>> autoModelList = Lists.newArrayList();
			
			for (Map<String,Object> autoModelNo : modelList) {//车型循环
				OrderBillingRela orderBillingRela = new OrderBillingRela();
				orderBillingRela.setRtNo((String)inMap.get("stationId"));
				orderBillingRela.setOrgCode(orgBoList.get(0).getOrgCode());
				orderBillingRela.setAutoModelNo(autoModelNo.get("modelId")+"");
				BillingConfigBo billingConfigBo = rentbillService.locateBillingConfigBo(rentType, orderBillingRela);
				if(billingConfigBo==null){
					continue;
				}
				BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingConfigBo.getBillingNo());
				Map<String,Object> autoModelMap = Maps.newHashMap();
				autoModelList.add(autoModelMap);
				autoModelMap.put("stationId", (String)inMap.get("stationId"));
				autoModelMap.put("modelId", autoModelNo.get("modelId")+"");
				BigDecimal depositResult = new BigDecimal("0.00");
				List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
				if(depositChargeItems!=null&&depositChargeItems.size()>0){
					for (DepositChargeItem depositChargeItem : depositChargeItems) {
							depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
					}
				}
				autoModelMap.put("depositVal", billingCalculator.bigDecimal2String(depositResult));
				List<Map<String,Object>> prodList = Lists.newArrayList();
				autoModelMap.put("prodList", prodList);
				Map<String,Object> prodMap = Maps.newHashMap();
				List<Map<String,Object>> pricingCombineList = Lists.newArrayList();
				Map<String,Object> pricingCombineMap = Maps.newHashMap();
				String chargeWay = billingConfigBo.getChargeWay();
				String chargeMode = billingConfigBo.getChargeMode();
				//按时间计费
				if (ChargeWay.BY_TIME.equals(chargeWay)|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
					prodMap = Maps.newHashMap();
					prodList.add(prodMap);
					prodMap.put("pProdId", "");
					prodMap.put("pProdName", "");
					prodMap.put("prodId", BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
					prodMap.put("prodName", "租金");
					prodMap.put("prodComments", "");
					prodMap.put("mandatoryType", BillingConstants.BuyType.REQUIRED);
					prodMap.put("pricingCombine", pricingCombineList);
					if (ChargeMode.STANDARD.equals(chargeMode)) {
						pricingCombineMap = Maps.newHashMap();
						pricingCombineMap.put("pricingSectName", billingFunc.getMainChargeItem().getTimeChargeItem().getPrice()+"元/"+billingFunc.getMainChargeItem().getTimeChargeItem().getPriceUnit().getDesc());
						pricingCombineMap.put("pricingSectDesc", billingFunc.getMainChargeItem().getTimeChargeItem().getPriceRemark());
						pricingCombineList.add(pricingCombineMap);
					} else if (ChargeMode.PERIOD.equals(chargeMode)||ChargeMode.STEP.equals(chargeMode)) {
						SubChargeItem timeChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
						List<RangeChargeItem> rangeChargeItems = timeChargeItem.getRangeChargeItems();
//						for (RangeChargeItem rangeChargeItem : rangeChargeItems) {
							pricingCombineMap = Maps.newHashMap();
							pricingCombineMap.put("pricingSectName", rangeChargeItems.get(0).getPrice()+"元/"+rangeChargeItems.get(0).getPriceUnit().getDesc());
							pricingCombineMap.put("pricingSectDesc", billingFunc.getMainChargeItem().getTimeChargeItem().getPriceRemark());
							pricingCombineList.add(pricingCombineMap);
//						}
					}
				}
				//按里程计费
				if (ChargeWay.BY_MILL.equals(chargeWay)|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
					prodMap = Maps.newHashMap();
					prodList.add(prodMap);
					prodMap.put("pProdId", "");
					prodMap.put("pProdName", "");
					prodMap.put("prodId", BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
					prodMap.put("prodName", "里程费");
					prodMap.put("prodComments", "");
					prodMap.put("mandatoryType", BillingConstants.BuyType.REQUIRED);
					pricingCombineList = Lists.newArrayList();
					prodMap.put("pricingCombine", pricingCombineList);
					if (ChargeMode.STANDARD.equals(chargeMode)) {
						pricingCombineMap = Maps.newHashMap();
						pricingCombineMap.put("pricingSectName", billingFunc.getMainChargeItem().getMillChargeItem().getPrice()+"元/"+billingFunc.getMainChargeItem().getMillChargeItem().getPriceUnit().getDesc());
						pricingCombineMap.put("pricingSectDesc", billingFunc.getMainChargeItem().getMillChargeItem().getPriceRemark());
						pricingCombineList.add(pricingCombineMap);
					} else if (ChargeMode.PERIOD.equals(chargeMode)||ChargeMode.STEP.equals(chargeMode)) {
						SubChargeItem millChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
						List<RangeChargeItem> rangeChargeItems = millChargeItem.getRangeChargeItems();
//						for (RangeChargeItem rangeChargeItem : rangeChargeItems) {
							pricingCombineMap = Maps.newHashMap();
                            pricingCombineMap.put("pricingSectName", rangeChargeItems.get(rangeChargeItems.size()-1).getPrice()+"元/"+rangeChargeItems.get(rangeChargeItems.size()-1).getPriceUnit().getDesc()+"(前"+rangeChargeItems.get(0).getRange().getTo()+"公里免费)");
//                            pricingCombineMap.put("pricingSectName", rangeChargeItems.get(0).getRange().getDesc()+" "+rangeChargeItems.get(0).getPrice()+"元/"+rangeChargeItems.get(0).getPriceUnit().getDesc());
							pricingCombineMap.put("pricingSectDesc", billingFunc.getMainChargeItem().getMillChargeItem().getPriceRemark());
							pricingCombineList.add(pricingCombineMap);
//						}
					}
				}
				//主要用于RentBillRestController中计算物流定价返回费用
				if("true".equals(inMap.get("isNeedItem"))){
					prodMap.put("mainChargeItem", billingFunc.getMainChargeItem());
				}
				//0所有1主产品 主产品是指租车价格，不包含服务费、保险费等附属产品。
				if(!"0".equals(inMap.get("prodType"))){
					continue;
				}
				//附加费用
				List<AppendChargeItem> attachChargeItems = billingFunc.getAttachChargeItems();
				if(attachChargeItems!=null&&attachChargeItems.size()>0){
					for (AppendChargeItem attachChargeItem : attachChargeItems) {
						prodMap = Maps.newHashMap();
						prodList.add(prodMap);
						prodMap.put("pProdId", "");
						prodMap.put("pProdName", "");
						prodMap.put("prodId", attachChargeItem.getItemNo());
						prodMap.put("prodName", attachChargeItem.getItemName());
						prodMap.put("prodComments", "");
						prodMap.put("mandatoryType", attachChargeItem.getBuyType());
						pricingCombineList = Lists.newArrayList();
						prodMap.put("pricingCombine", pricingCombineList);
						pricingCombineMap = Maps.newHashMap();
						pricingCombineList.add(pricingCombineMap);
						pricingCombineMap.put("pricingSectName", attachChargeItem.getPrice()+"元/"+attachChargeItem.getPriceUnit().getDesc());
						pricingCombineMap.put("pricingSectDesc", attachChargeItem.getPriceRemarks());
					}
				}
			}
			if(CollectionUtils.isNotEmpty(autoModelList)){
				prodModeMap.put("modelList", autoModelList);
				prodModeList.add(prodModeMap);
			}
		}
		return resultMap;
	}
	
	/**
	 * 租车计费统一入口（含预收、结算）
	 * @throws Exception 
	 */
	@Override
	public Map<String, Object> rentBilling(Map<String, Object> inMap) throws Exception {
		AssertUtil.notEmptyForString(inMap.get("prodModeId"), "产品模式不能为空");
		inMap.put("rentType", inMap.get("prodModeId"));
		if("2".equals(inMap.get("settlementType"))){
			return this.rentPrepay(inMap);
		} else if("1".equals(inMap.get("settlementType"))||"3".equals(inMap.get("settlementType"))){
			return this.rentSettle(inMap);
		}
		return null;
	}

	/**
	 * 预收
	 * <li> map.put("settlementType") 结算类型请求计费的类型。1预估2预付3结算
	 * <li> map.put("orderNo") 订单编号   必填
	 * <li> map.put("dataTime") 数据时间
	 * <li> map.put("prodBusiType") 产品业务类
	 * <li> map.put("prodModeId") 产品模式ID
	 * <li> map.put("city") 城市
	 * <li> map.put("stationId") 取车点  必填
	 * <li> map.put("modelId") 车型  必填
	 * <li> map.put("prodOptionList") 可选产品列表<prodId> 必填
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> rentPrepay(Map<String, Object> inMap) {
		logger.debug("rentPrepay--in:{}",inMap);
		OrderBillingRela orderBillingRela = new OrderBillingRela();
		orderBillingRela.setAppNo(inMap.get("orderNo")+"");
		orderBillingRela.setRtNo(inMap.get("stationId")+"");
		orderBillingRela.setAutoModelNo(inMap.get("modelId")+"");
		orderBillingRela.setRentType(inMap.get("rentType")+"");
		orderBillingRela.setSaveLogFlag(true);
		orderBillingRela.setMeters(Long.valueOf((inMap.get("rentMileage")==null?"0":inMap.get("rentMileage"))+""));
		List<Map<String,Object>> equipList = (List<Map<String,Object>>)inMap.get("equipList");
		if (equipList != null && equipList.size() > 0) {
			for (Map<String, Object> equipMap : equipList) {
				orderBillingRela.setPlanQcTime(AssertUtil.notEmptyForString(equipMap.get("pickTime"), "取车时间不能为空"));
				orderBillingRela.setMeters(Long.valueOf((StringUtil.isEmpty(equipMap.get("rentMileage"))?"0":equipMap.get("rentMileage"))+""));
			}
		}
		
		List<Map<String,Object>> prodOptionList = (List<Map<String, Object>>) inMap.get("prodOptionList");
		String selectedAttachItemNos="";
		if(prodOptionList!=null&&prodOptionList.size()>0){
			for (Map<String, Object> map : prodOptionList) {
				selectedAttachItemNos += map.get("prodId");
			}
		}
		orderBillingRela.setSelectedAttachItemNos(selectedAttachItemNos);
		orderBillingRela.setOrgCode(orgService.getRootOrgs().get(0).getOrgCode());
		OrderBillingRelaResult result = rentbillService.saveOrderBillingRela(orderBillingRela );
		Map<String, Object> resultMap = Maps.newHashMap();
		resultMap.put("prodBillId", "");
		resultMap.put("settlementType", "2");
		resultMap.put("prePay", billingCalculator.bigDecimal2String(result.getResult()));
		resultMap.put("depositVal", billingCalculator.bigDecimal2String(result.getDepositResult()));
		resultMap.put("billAmt", billingCalculator.bigDecimal2String(result.getResult().add(result.getDepositResult())));
		List<Map<String,Object>> retEquipList = Lists.newArrayList();
		Map<String,Object> retEquipMap = Maps.newHashMap();
		retEquipList.add(retEquipMap);
		retEquipMap.put("equipId", inMap.get("modelId"));
		List<Map<String,Object>> prodBillDetList = Lists.newArrayList();
		Map<String,Object> prodMap = Maps.newHashMap();
		
		List<EPrcChargeDetBo> ePrcChargeDetBoList = (List<EPrcChargeDetBo>) result.getPrcCharge().get("ePrcChargeDetList");
		
		if(ePrcChargeDetBoList!=null&&ePrcChargeDetBoList.size()>0){
			for (EPrcChargeDetBo ePrcChargeDetBo : ePrcChargeDetBoList) {
				prodMap = Maps.newHashMap();
				prodBillDetList.add(prodMap);
				prodMap.put("pProdId", "");
				prodMap.put("pProdName", "");
				prodMap.put("prodId", ePrcChargeDetBo.getItemCode());
				prodMap.put("prodName", ePrcChargeDetBo.getItemName());
				prodMap.put("prodComments", "");
				prodMap.put("billDetNum", ePrcChargeDetBo.getAmount());
				prodMap.put("billDetAmt", ePrcChargeDetBo.getPricingAmt());
				List<Map<String,Object>> pricingCombineList = Lists.newArrayList();
				prodMap.put("pricingCombine", pricingCombineList);
				Map<String,Object> pricingCombineMap = Maps.newHashMap();
				pricingCombineList.add(pricingCombineMap);
				pricingCombineMap.put("pricingSectName", "");
				pricingCombineMap.put("pricingSectDesc", ePrcChargeDetBo.getItemDesc());
				pricingCombineMap.put("cycleType", "");
				pricingCombineMap.put("cycleUnitCount", "");
				pricingCombineMap.put("cycleMaxValue", "");
				pricingCombineMap.put("cycleMinValue", "");
				pricingCombineMap.put("cycleValueType", "");
				pricingCombineMap.put("dailyBillDet", Lists.newArrayList());
				if("次".equals(ePrcChargeDetBo.getItemUnits())){
					continue;
				}
				pricingCombineMap.put("dailyBillDet", ePrcChargeDetBo.getDailyBillDetList());
			}
		}
		retEquipMap.put("prodBillDet", prodBillDetList);
		resultMap.put("equipList", retEquipList);
//		resultMap.put("prcCharge", result.getPrcCharge());
		logger.debug("rentPrepay--out:{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}
	
	/**
	 * 结算
	 * <li> map.put("settlementType") 结算类型请求计费的类型。1预估2预付3结算
	 * <li> map.put("orderNo") 订单编号   必填
	 * <li> map.put("dataTime") 数据时间
	 * <li> map.put("prodBusiType") 产品业务类
	 * <li> map.put("prodModeId") 产品模式ID
	 * <li> map.put("city") 城市
	 * <li> map.put("stationId") 取车点  必填
	 * <li> map.put("modelId") 车型  必填
	 * <li> map.put("equipList") 支持订单多辆车时，按车辆的租赁时长、里程等进行计费。
	 * 		<li> map.put("equipId") 设备ID
	 * 		<li> map.put("pickTime") 取车时间
	 * 		<li> map.put("offTime") 还车时间
	 * 		<li> map.put("rentLength") 租赁时长
	 * 		<li> map.put("rentMileage") 租赁里程
	 * 		<li> map.put("overtimeLength") 超时时长
	 * 		<li> map.put("overtimeMileage") 超时里程
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> rentSettle(Map<String, Object> inMap) throws Exception {
		logger.debug("rentSettle--in:{}", inMap);
		OrderBillingRela orderBillingRela = new OrderBillingRela();
		orderBillingRela.setAppNo(String.valueOf(inMap.get("orderNo")));
		Map<String, BillingFunc> map = rentbillService.getApplyDateBillingConfig(orderBillingRela);
		List<Map<String,Object>> equipList = (List<Map<String,Object>>)inMap.get("equipList");
		BigDecimal totolPrice = new BigDecimal("0.00");
		List<Map<String,Object>> retEquipList = Lists.newArrayList();
		if(equipList!=null && equipList.size()>0){
			Map<String,Object> retEquipMap = null;
			for (Map<String, Object> equipMap : equipList) {
				List<String> timeAxis = new ArrayList<String>();
                String qcacttime = String.valueOf(equipMap.get("pickTime"));
				String hcacttime = String.valueOf(equipMap.get("offTime"));
				String qcymd= qcacttime.substring(0, 10).replace("-", "");
				String hcymd= hcacttime.substring(0, 10).replace("-", "");
				List<String> dateList =null;
				if(SubBe.SUB_BE_MONTHS.contains(inMap.get("prodModeId"))||SubBe.SUB_BE_YEARS.contains(inMap.get("prodModeId"))){
					dateList = JodaDateTime.getMonthArray(qcacttime, hcacttime, "yyyy-MM-dd HH:mm:ss");
				} else {
					dateList = JodaDateTime.getYmdArray(qcymd, hcymd,"yyyyMMdd");
				}
				int betDays = 0;
				//月租、年租 暂定都是按月扣费
				if(SubBe.SUB_BE_MONTHS.contains(inMap.get("prodModeId"))||SubBe.SUB_BE_YEARS.contains(inMap.get("prodModeId"))){
					betDays = JodaDateTime.getMonthsBetween(qcacttime, hcacttime) ;
					if(!hcacttime.equals(JodaDateTime.getPreOrFutureMonth(betDays,qcacttime,"yyyy-MM-dd HH:mm:ss"))){
                        betDays = betDays+1;
                    }
				} else {
					betDays = JodaDateTime.getDaysBetween(qcacttime, hcacttime) + 1;
				}
				if (betDays != dateList.size()) {
					dateList.remove(betDays);
				}
				BillingFunc func = map.get(qcymd);
				if (null == func) {
					func = map.get("99999999");
				}
				String chargeMode = func.getMainChargeItem().getChargeMode();
				String chargeWay = func.getMainChargeItem().getChargeWay();
                boolean isNeedMeters = false;
                if (!ChargeWay.BY_TIME.equals(chargeWay)) {
                    isNeedMeters = true ;
                }
                if(!isNeedMeters&&CollectionUtils.isNotEmpty(func.getAttachChargeItems())){
                    for (int i = 0; i < func.getAttachChargeItems().size(); i++) {
                        String unit =  func.getAttachChargeItems().get(i).getPriceUnit().getUnit();
                        if(PriceUnit.M.equals(unit)||PriceUnit.KM.equals(unit)){
                            isNeedMeters = true ;
                            break;
                        }
                    }
                }
				RentRun rentRun = new RentRun();
				List<DateItem> dateItems = new ArrayList<DateItem>();
				Map<String, Object> condMap = new HashMap<String, Object>();
				if (ChargeMode.PERIOD.equals(chargeMode)) {
					for (int m = 0; m < dateList.size(); m++) {
						String temphctime = (m == dateList.size() - 1) ? hcacttime
								: DateTools.getPreOrFutureDay(1, qcacttime + ":00",
										"yyyy-MM-dd HH:mm:ss");
						if (qcacttime.compareTo(temphctime) > 0) {
							break;
						}
						String recordDate = dateList.get(m);
						// 每天的行驶记录
						long meters = 0;
						long minutes = 0;
						DateItem dateItem = new DateItem();
						dateItem.setRecordDate(recordDate);
						dateItem.setStartTime(qcacttime);
						// 获取每一天分时段里程
						BillingFunc each = map.get(recordDate);
						if (null == each) {
							each = map.get("99999999");
						}
						MainChargeItem mainChargeItem = each.getMainChargeItem();
						List<RangeChargeItem> rangeItems = null;
						List<PeriodItem> periodItems = new ArrayList<PeriodItem>();
						if (ChargeWay.BY_TIME.equals(chargeWay)) {
							rangeItems = mainChargeItem.getTimeChargeItem().getRangeChargeItems();
						} else {
							rangeItems = mainChargeItem.getMillChargeItem().getRangeChargeItems();
						}
						if (rangeItems != null) {
							while (true) {
								RangeChargeItem rangeItem = new RangeChargeItem();
								String tempQcTimeHM = qcacttime.substring(11, 16);
								for (RangeChargeItem tempRangeItem : rangeItems) {
									if (tempRangeItem.getRange().getFrom().compareTo(tempRangeItem.getRange().getTo()) > 0
											&& "24:00".compareTo(tempRangeItem.getRange().getFrom()) >= 0
											&& "00:00".compareTo(tempRangeItem.getRange().getTo()) <= 0) {
										if ((tempQcTimeHM.compareTo(tempRangeItem.getRange().getFrom()) >= 0)
												|| tempQcTimeHM.compareTo(tempRangeItem.getRange().getTo()) < 0) {
											rangeItem = tempRangeItem;
											break;
										}
									} else if ((tempQcTimeHM.compareTo(tempRangeItem.getRange().getFrom()) >= 0)
											&& (tempQcTimeHM.compareTo(tempRangeItem.getRange().getTo()) < 0)) {
										rangeItem = tempRangeItem;
										break;
									}
								}
								PeriodItem periodItem = new PeriodItem();
								boolean flag = false;
								Range range = rangeItem.getRange();
								String date = qcacttime.substring(0, 10);
								String rangeFrom = date + " " + range.getFrom() + ":00";
								String rangeTo = date + " " + range.getTo() + ":00";
								if (rangeFrom.compareTo(rangeTo) > 0|| "24:00".equals(range.getTo())) {
									String tempDate = DateTools.getPreOrFutureDay(1,date.replace("-", ""), "yyyyMMdd");
									date = tempDate.substring(0, 4) + "-"+ tempDate.substring(4, 6) + "-"+ tempDate.substring(6, 8);
									rangeTo = date+ " "+ ("24:00".equals(range.getTo()) ? "00:00": range.getTo()) + ":00";
								}
								if (qcacttime.compareTo(rangeFrom) >= 0&& qcacttime.compareTo(rangeTo) <= 0) {
									flag = true;
									if (temphctime.compareTo(rangeTo) > 0) {
										condMap.put("dateTimeFrom", qcacttime);
										condMap.put("dateTimeTo", rangeTo);
									} else {
										condMap.put("dateTimeFrom", qcacttime);
										condMap.put("dateTimeTo", temphctime);
									}
								} else if (temphctime.compareTo(rangeFrom) >= 0&& temphctime.compareTo(rangeTo) <= 0) {
									condMap.put("dateTimeFrom", rangeFrom);
									condMap.put("dateTimeTo", temphctime);
									flag = true;
								} else if (rangeFrom.compareTo(qcacttime) >= 0&& rangeTo.compareTo(temphctime) <= 0) {
									condMap.put("dateTimeFrom", rangeFrom);
									condMap.put("dateTimeTo", rangeTo);
									flag = true;
								}
								if (flag) {
									if (isNeedMeters) {
										timeAxis.add(condMap.get("dateTimeFrom")+"");
										timeAxis.add(condMap.get("dateTimeTo")+"");
//										Map<String,Object> msgmap = new HashMap<String, Object>();
//										msgmap.put("dataSource", "2");
//										msgmap.put("equipNo", equipMap.get("equipNo"));
//										msgmap.put("timeAxis", date + " " + condMap.get("dateTimeFrom")+","+condMap.get("dateTimeTo"));
//										logger.debug("445行，请求里程入参{}",msgmap);
//										String retValue=WsclientUtil.connectCXFWs("excuteWs", "qryRentCarHisMileage", IJsonUtil.obj2Json(msgmap));
//										logger.debug("445行，请求里程出参{}",retValue);
//										if(!StringUtils.nullOrBlank(retValue)){
//											Map<String,String> retMap = IJsonUtil.json2Obj(retValue, Map.class);
//											String mileages = retMap.get("mileages")==null?"0":retMap.get("mileages");
//											double periodMeters = Double.valueOf(mileages)* 1000;
//											logger.debug("445行，里程计算值{}",periodMeters);
//											meters += Math.round(periodMeters);
//											periodItem.setMeters(Math.round(periodMeters));
//										} else {
//											throw new Exception("车辆里程获取失败");
//										}
										inMap.put("qryTime",condMap.get("dateTimeFrom"));
//										Map<String, Object> minMap = carHistoryService.getCarRunMileage(inMap);
										inMap.put("qryTime", condMap.get("dateTimeTo"));
//										Map<String, Object> maxMap = carHistoryService.getCarRunMileage(inMap);
//										if (null != maxMap && null != minMap) {
//											double periodMeters = (Double.valueOf(maxMap.get("MILEAGE").toString()) 
//													- Double.valueOf(minMap.get("MILEAGE").toString()))
//													* 1000;
//											double periodMeters = 10000;
//											meters += Math.round(periodMeters);
//											periodItem.setMeters(Math.round(periodMeters));
//										}
									}

									Date beginDate = JodaDateTime.parseToDate(condMap.get("dateTimeFrom").toString(), "yyyy-MM-dd HH:mm:ss");
									Date endDate = JodaDateTime.parseToDate(condMap.get("dateTimeTo").toString(), "yyyy-MM-dd HH:mm:ss");
									double minute = ((double) (endDate.getTime() - beginDate.getTime())) / (60 * 1000);
									minutes += Math.ceil(minute);
									periodItem.setMinutes((long) Math.ceil(minute));
									periodItem.setRecordDate(recordDate);
									periodItem.setRange(rangeItem.getRange());
									periodItems.add(periodItem);
								}
								qcacttime = rangeTo;
								if (qcacttime.compareTo(temphctime) >= 0) {
									qcacttime = temphctime;
									break;
								}

							}
						}
						dateItem.setEndTime(temphctime);
						dateItem.setPeriodItems(periodItems);
						dateItem.setMeters(meters);
						dateItem.setMinutes(minutes);
						dateItems.add(dateItem);
					}
				} else {
					Date beginDate = JodaDateTime.parseToDate(qcacttime, "yyyy-MM-dd HH:mm:ss");
					Date endDate = JodaDateTime.parseToDate(hcacttime, "yyyy-MM-dd HH:mm:ss");
					double minute = ((double) (endDate.getTime() - beginDate.getTime()))/ (60 * 1000);
					int minutes = (int) Math.ceil(minute);
					long days = minutes / 1440;
					long b = minutes % 1440;
					long quantity = days;
					if (b != 0) {
						quantity += 1;
					}
					if(SubBe.SUB_BE_MONTHS.contains(inMap.get("prodModeId"))||SubBe.SUB_BE_YEARS.contains(inMap.get("prodModeId"))) {
						for (int i = 0; i < dateList.size(); i++) {
							DateItem item = new DateItem();
							item.setRecordDate(dateList.get(i).substring(0, 10).replace("-", ""));
							if(ChargeMode.STEP.equals(chargeMode)){
								i = dateList.size() - 1;
								item.setMinutes(dateList.size());
							} else {
								item.setMinutes(1);
							}
							String fromTime = dateList.get(i);
							String toTime = "";

							if (i == dateList.size() - 1) {
								toTime = hcacttime;
							} else {
								toTime = dateList.get(i+1);
							}
							if (isNeedMeters) {
								timeAxis.add(fromTime);
								timeAxis.add(toTime);
							}
							item.setStartTime(fromTime);
							item.setEndTime(toTime);
							dateItems.add(item);
						}
					} else {
						for (int i = 0; i < quantity; i++) {
							DateItem item = new DateItem();
							item.setRecordDate(DateTools.getPreOrFutureDay(i, qcymd, "yyyyMMdd"));
							String fromTime = DateTools.getPreOrFutureDay(i, qcacttime, "yyyy-MM-dd HH:mm:ss");
							String toTime = "";
							if (i == quantity - 1) {
								toTime = hcacttime;
							} else {
								toTime = DateTools.getPreOrFutureDay(i + 1, qcacttime, "yyyy-MM-dd HH:mm:ss");
							}
							if (isNeedMeters) {
								//Map<String,Object> msgmap = new HashMap<String, Object>();
								//msgmap.put("dataSource", "2");
								//msgmap.put("equipNo", equipMap.get("equipNo"));
								timeAxis.add(fromTime);
								timeAxis.add(toTime);
								//msgmap.put("timeAxis", fromTime +","+toTime);
								//logger.debug("520行，请求里程入参{}",msgmap);
								//String retValue=WsclientUtil.connectCXFWs("excuteWs", "qryRentCarHisMileage", IJsonUtil.obj2Json(msgmap));
								//logger.debug("522行，请求里程出参{}",retValue);
								//if(!StringUtils.nullOrBlank(retValue)){
								//	Map<String,String> retMap = IJsonUtil.json2Obj(retValue, Map.class);
								//	String mileages = retMap.get("mileages")==null?"0":retMap.get("mileages");
								//	double periodMeters = Double.valueOf(mileages)* 1000;
								//	logger.debug("522行，里程计算值{}",periodMeters);
								//	item.setMeters(Math.round(periodMeters));
								//} else {
								//	throw new Exception("车辆里程获取失败");
								//}

								inMap.put("qryTime", DateTools.getPreOrFutureDay(i, qcacttime, "yyyy-MM-dd HH:mm:ss"));
								//Map<String, Object> minMap = carHistoryService.getCarRunMileage(inMap);
								if (i == quantity - 1) {
									inMap.put("qryTime", hcacttime);
								} else {
									inMap.put("qryTime", DateTools.getPreOrFutureDay(i + 1, qcacttime, "yyyy-MM-dd HH:mm:ss"));
								}
								//Map<String, Object> maxMap = carHistoryService.getCarRunMileage(inMap);

								//if (null != maxMap && null != minMap) {
								//	double periodMeters = (Double.valueOf(maxMap.get("MILEAGE").toString()) - Double.valueOf(minMap
								//								.get("MILEAGE").toString())) * 1000;
								//	double periodMeters = 10000;
								//	item.setMeters(Math.round(periodMeters));
								//}
							}
							if (quantity == 1 || i == quantity - 1) {
								item.setMinutes(b == 0 ? 1440 : b);
							} else {
								item.setMinutes(1440);
							}
							item.setStartTime(fromTime);
							item.setEndTime(toTime);
							dateItems.add(item);
						}
					}
				}
				if(CollectionUtils.isNotEmpty(timeAxis)){
					List<String> timeAxisList = new ArrayList<String>(new HashSet<String>(timeAxis));
					Collections.sort(timeAxisList);
					Map<String,Object> msgmap = new HashMap<String, Object>();
					msgmap.put("dataSource", "2");
					msgmap.put("equipNo", equipMap.get("equipNo"));
					msgmap.put("timeAxis", StringUtils.join(timeAxisList.iterator(), ","));
					logger.debug("订单号{}，请求里程入参{}",inMap.get("orderNo"),msgmap);
					String retValue=WsclientUtil.connectCXFWs("excuteWs", "qryRentCarHisMileage", IJsonUtil.obj2Json(msgmap));
					logger.debug("订单号{}，请求里程出参{}",inMap.get("orderNo"),retValue);
					if(!StringUtils.nullOrBlank(retValue)){
						Map<String,String> retMap = IJsonUtil.json2Obj(retValue, Map.class);
						if(!"0".equals(retMap.get("ret"))){
							throw new Exception(retMap.get("msg"));
						}
						String mileages = retMap.get("mileages")==null?"0":retMap.get("mileages");
						if(!"0".equals(mileages)){
							String[] mileage = mileages.split(",");
							int flag = 0;
							for (int i = 0 ; i < dateItems.size();i++)  {
								List<PeriodItem> itemList = dateItems.get(i).getPeriodItems();
								long dayMeters = 0;
								if(CollectionUtils.isNotEmpty(dateItems.get(i).getPeriodItems())){
									for (int j = 0; j < itemList.size(); j++) {
										dayMeters = dayMeters + Math.round(Double.valueOf(mileage[flag])* 1000);
										itemList.get(j).setMeters(Math.round(Double.valueOf(mileage[flag])* 1000));
										flag++;
									}
								} else {
									dayMeters = Math.round(Double.valueOf(mileage[flag])* 1000);
									flag++;
								}
								dateItems.get(i).setMeters(dayMeters);
							}
						}
					} else {
						throw new Exception("车辆里程获取失败");
					}
				}
				rentRun.setDateItems(dateItems);
				rentRun.setAppNo(String.valueOf(inMap.get("orderNo")));
				if ("3".equals(inMap.get("settlementType"))) {
					rentRun.setSaveLogFlag(true);
				} else if ("1".equals(inMap.get("settlementType"))) {
					// 获取租车订单费用相关情况
					rentRun.setSaveLogFlag(false);
				}
				rentRun.setLicenseNo(String.valueOf(equipMap.get("equipId")));
				// 调用计费接口入参取车时间，还车时间（服务端计费的时间）获取计费结果
				OrderBillingRelaResult result = rentbillService.calculate(rentRun,"02");
				totolPrice = totolPrice.add(result.getResult());
				retEquipMap = Maps.newHashMap();
				retEquipList.add(retEquipMap);
				retEquipMap.put("equipId", equipMap.get("equipId"));
				List<Map<String,Object>> prodBillDetList = Lists.newArrayList();
				retEquipMap.put("prodBillDet", prodBillDetList);
//				retEquipMap.put("prcCharge", result.getPrcCharge());
				Map<String,Object> prodMap = Maps.newHashMap();
				
				List<EPrcChargeDetBo> ePrcChargeDetBoList = (List<EPrcChargeDetBo>) result.getPrcCharge().get("ePrcChargeDetList");
				
				if(ePrcChargeDetBoList!=null&&ePrcChargeDetBoList.size()>0){
					for (EPrcChargeDetBo ePrcChargeDetBo : ePrcChargeDetBoList) {
						prodMap = Maps.newHashMap();
						prodBillDetList.add(prodMap);
						prodMap.put("pProdId", "");
						prodMap.put("pProdName", "");
						prodMap.put("prodId", ePrcChargeDetBo.getItemCode());
						prodMap.put("prodName", ePrcChargeDetBo.getItemName());
						prodMap.put("prodComments", "");
						prodMap.put("billDetNum", ePrcChargeDetBo.getAmount());
						prodMap.put("billDetAmt", ePrcChargeDetBo.getPricingAmt());
						List<Map<String,Object>> pricingCombineList = Lists.newArrayList();
						prodMap.put("pricingCombine", pricingCombineList);
						Map<String,Object> pricingCombineMap = Maps.newHashMap();
						pricingCombineList.add(pricingCombineMap);
						pricingCombineMap.put("pricingSectName", "");
						pricingCombineMap.put("pricingSectDesc", ePrcChargeDetBo.getItemDesc());
						pricingCombineMap.put("cycleType", "");
						pricingCombineMap.put("cycleUnitCount", "");
						pricingCombineMap.put("cycleMaxValue", "");
						pricingCombineMap.put("cycleMinValue", "");
						pricingCombineMap.put("cycleValueType", "");
						pricingCombineMap.put("dailyBillDet", Lists.newArrayList());
						if("次".equals(ePrcChargeDetBo.getItemUnits())){
							continue;
						}
						pricingCombineMap.put("dailyBillDet", ePrcChargeDetBo.getDailyBillDetList());
					}
				}
			}
		}
		
		Map<String, Object> resultMap = Maps.newHashMap();
		resultMap.put("prodBillId", "");
		resultMap.put("settlementType", inMap.get("settlementType"));
		resultMap.put("prePay", "0.00");
		resultMap.put("depositVal","0.00");
		resultMap.put("billAmt",billingCalculator.bigDecimal2String(totolPrice));
		resultMap.put("equipList", retEquipList);
		logger.debug("rentSettle--out:{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}

	/**
	 * 查询订单计费结果
	 */
	@Override
	public Map<String, Object> getOrderPrice(Map<String, Object> inMap) {
		
		return null;
	}

	/**
	 * 查询物流定价
	 */
	@Override
	public Map<String, Object> getDeliverPrice(Map<String, Object> inMap) {
		logger.debug("getDeliverPrice--in:{}",inMap);
		Map<String, Object> resultMap = Maps.newHashMap();
		List<CodeBO> rentTypeList = codeService.getStandardCodes(SubBe.CODE_TYPE,null);
		List<String> rentTypes = new ArrayList<String>();
		if(rentTypeList!=null&&rentTypeList.size()>0){
			for (CodeBO codeBO : rentTypeList) {
				rentTypes.add(codeBO.getCodeValue());
			}
		}
		if(inMap.get("prodModeId")!=null&&!inMap.get("prodModeId").equals("")){
			if(!rentTypes.contains(inMap.get("prodModeId"))){
				logger.error("租车模式参数错误");
				resultMap.put("prodModeList", Lists.newArrayList());
				return resultMap;
			}
			rentTypes = Lists.newArrayList();
			rentTypes.add((String)inMap.get("prodModeId"));
		} else {
			rentTypes = Lists.newArrayList();
			rentTypes.add(SubBe.DILIVER);
		}
		resultMap = this.getCommonPrice(inMap, rentTypes);
		logger.debug("getDeliverPrice--out:{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}

	@Override
	public Map<String, Object> deliverBilling(Map<String, Object> inMap) {
		inMap.put("rentType", SubBe.DILIVER);
		if("2".equals(inMap.get("settlementType"))){
			return this.rentPrepay(inMap);
		} else if("3".equals(inMap.get("settlementType"))){
			return this.deliverSettle(inMap);
		}
		return null;
	}

	private Map<String, Object> deliverSettle(Map<String, Object> inMap) {
		logger.debug("deliverSettle--in:{}", inMap);
		OrderBillingRela orderBillingRela = new OrderBillingRela();
		orderBillingRela.setAppNo(String.valueOf(inMap.get("orderNo")));
		Map<String, BillingFunc> map = rentbillService.getApplyDateBillingConfig(orderBillingRela);
		BillingFunc billingFunc = map.get("99999999");
		SubChargeItem millChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
		Item item = new Item();
		item.setMeters(Long.valueOf(inMap.get("rentMileage")+""));
		BigDecimal millResult = new BigDecimal("0.00");
		Limit min = millChargeItem.getMin();
		Limit max = millChargeItem.getMax();
		long limitedQuantity = billingCalculator.limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), item.getMeters());
		item.setMeters(limitedQuantity);
		millResult = millResult.add(billingCalculator.calculateMillChargeItem(millChargeItem , item ));
		millResult = billingCalculator.limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), millResult);
		Map<String, Object> resultMap = Maps.newHashMap();
		resultMap.put("billAmt",billingCalculator.bigDecimal2String(millResult));
		logger.debug("deliverSettle--out:{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}
}
