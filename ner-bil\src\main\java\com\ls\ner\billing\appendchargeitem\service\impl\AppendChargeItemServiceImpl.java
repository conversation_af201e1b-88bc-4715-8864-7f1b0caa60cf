package com.ls.ner.billing.appendchargeitem.service.impl;

import java.util.List;

import javax.annotation.PostConstruct;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.billing.api.BillingConstants.BuyType;
import com.ls.ner.billing.api.BillingConstants.ItemStatus;
import com.ls.ner.billing.api.BillingConstants.ItemType;
import com.ls.ner.billing.api.BillingConstants.PeBe;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.appendchargeitem.dao.IAppendChargeItemDao;
import com.ls.ner.billing.appendchargeitem.service.IAppendChargeItemService;
import com.ls.ner.billing.common.bo.AppendChargeItemBo;
import com.ls.ner.billing.common.bo.AppendChargeItemBoExample;
import com.ls.ner.billing.common.dao.AppendChargeItemBoMapper;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.tools.StringUtils;

/**
 * 
 * 管理资费标准中 押金、附加费用等一组内条目不定的费用项
 * <AUTHOR>
 *
 */
@Component
public class AppendChargeItemServiceImpl implements IAppendChargeItemService {
private static final Logger LOGGER = LoggerFactory.getLogger(AppendChargeItemServiceImpl.class);

	@Autowired
	private AppendChargeItemBoMapper appendChargeItemBoMapper;
	@Autowired
	private IAppendChargeItemDao appendChargeItemDao;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;
	
	@PostConstruct
	public void init(){
		LOGGER.debug("init "+this.getClass().getName());
	}
	
	/**
	 * 新增附加收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public String createAttachChargeItem(AppendChargeItemBo appendChargeItemBo){
		appendChargeItemBo.setItemType(ItemType.ATTACH);
		return  create(appendChargeItemBo);
		
	}
	/**
	 * 新增押金收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public String createDepositChargeItem(AppendChargeItemBo appendChargeItemBo){
		appendChargeItemBo.setItemType(ItemType.DEPOSIT);
		appendChargeItemBo.setItemUnit(PriceUnit.TIMES);
		appendChargeItemBo.setBuyType(BuyType.REQUIRED);
		return create(appendChargeItemBo);
	}	
	protected String create(AppendChargeItemBo appendChargeItemBo){
		String itemNo = genItemNo();
		appendChargeItemBo.setSystemId(null);
		appendChargeItemBo.setItemNo(itemNo);		
		appendChargeItemBo.setDataOperType("I");
		appendChargeItemBo.setDataOperTime(new DateTime().toDate());
		appendChargeItemBo.setItemStatus(ItemStatus.ENABLE);
		appendChargeItemBo.setpBe(PeBe.RENT);
		appendChargeItemBoMapper.insertSelective(appendChargeItemBo);
		return itemNo;
	}

	protected String genItemNo() {
		return seqRpcService.getDefNo();
	}
	
	/**
	 * 更新附加收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public void updateAttachChargeItem(AppendChargeItemBo appendChargeItemBo){
		appendChargeItemBo.setItemType(ItemType.ATTACH);
		update(appendChargeItemBo);
	}
	/**
	 * 更新押金收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public void updateDepositChargeItem(AppendChargeItemBo appendChargeItemBo){
		appendChargeItemBo.setItemType(ItemType.DEPOSIT);
		appendChargeItemBo.setItemUnit(PriceUnit.TIMES);
		appendChargeItemBo.setBuyType(BuyType.REQUIRED);
		update(appendChargeItemBo);
	}
	
	public void update(AppendChargeItemBo appendChargeItemBo){
		
		
		appendChargeItemBo.setDataOperType("U");
		appendChargeItemBo.setDataOperTime(new DateTime().toDate());
		int result = appendChargeItemDao.updateByItemNoAndItemType(appendChargeItemBo);
//		AppendChargeItemBoExample example = new AppendChargeItemBoExample();
//		example.createCriteria().andItemNoEqualTo(appendChargeItemBo.getItemNo()).andItemTypeEqualTo(appendChargeItemBo.getItemType());
//		int result = appendChargeItemBoMapper.updateByExampleSelective(appendChargeItemBo, example);
		if(result == 0 ){
			//没有影响到的记录是否需要处理了
			//BusinessException
			
		}
	}

	/**
	 * 删除附加收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public void deleteAttachChargeItem(String itemNo){
		delete(ItemType.ATTACH,itemNo);
		
	}
	/**
	 * 删除押金收费项
	 * @param appendChargeItemBo
	 */
	@Override
	public void deleteDepositChargeItem(String itemNo){
		delete(ItemType.DEPOSIT,itemNo);
	}	
	
	public void delete(String itemType,String itemNo){
		AppendChargeItemBo appendChargeItemBo = new AppendChargeItemBo();
		appendChargeItemBo.setItemType(itemType);
		appendChargeItemBo.setItemNo(itemNo);
		delete(appendChargeItemBo);
	}
	public void delete(AppendChargeItemBo appendChargeItemBo){
		appendChargeItemDao.deleteByItemNoAndItemType(appendChargeItemBo);
	}
	
	@Override
	public List<AppendChargeItemBo> listAttachChargeItem(AppendChargeItemBo bo){
		bo.setItemType(ItemType.ATTACH);
		return list(bo);
	}

	@Override
	public List<AppendChargeItemBo> listDepositChargeItem(AppendChargeItemBo bo){
		bo.setItemType(ItemType.DEPOSIT);
		return list(bo);
	}
	
	
	public List<AppendChargeItemBo> list(AppendChargeItemBo bo){
		AppendChargeItemBoExample appendChargeItemBoExample = new AppendChargeItemBoExample();
		AppendChargeItemBoExample.Criteria c = appendChargeItemBoExample.createCriteria().andItemTypeEqualTo(bo.getItemType()).andPBeEqualTo(PeBe.RENT);
		if(!StringUtils.nullOrBlank(bo.getItemStatus())){
			c.andItemStatusEqualTo(bo.getItemStatus());
		}
		appendChargeItemBoExample.setOrderByClause("SN asc, SYSTEM_ID asc ");
		List<AppendChargeItemBo> result = appendChargeItemBoMapper.selectByExample(appendChargeItemBoExample);
		if(result == null){
			return Lists.newArrayList();
		}
		return result;
	}

	@Override
	public void updateItems(List<AppendChargeItemBo> list) {
		for (AppendChargeItemBo appendChargeItemBo : list) {
			AppendChargeItemBo record = new AppendChargeItemBo();
			record.setItemStatus(appendChargeItemBo.getItemStatus());
			record.setDataOperType("U");
			record.setDataOperTime(new DateTime().toDate());
			AppendChargeItemBoExample example = new AppendChargeItemBoExample();
			example.createCriteria().andItemNoEqualTo(appendChargeItemBo.getItemNo());
			appendChargeItemBoMapper.updateByExampleSelective(record, example );
		}
	}
	

}
