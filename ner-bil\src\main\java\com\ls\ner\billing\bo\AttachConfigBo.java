/**
 *
 * @(#) AttachConfigBo.java
 * @Package com.ls.ner.billing.api.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.bo;

import java.io.Serializable;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: AttachConfigBo.java,v 1.1 2016/02/26 02:35:23 34544 Exp $
 * 
 *          History: 2016年2月25日 下午9:56:07 lipf Created.
 * 
 */
public class AttachConfigBo implements Serializable {

	private static final long serialVersionUID = 1934517689593218212L;
	private String billingNo;
	private String price;
	private String itemName;
	private String itemNo;
	private String chargeType;
	private String buyIdentity;
	private String remark;

	public String getBillingNo() {
		return billingNo;
	}

	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}

	public String getBuyIdentity() {
		return buyIdentity;
	}

	public void setBuyIdentity(String buyIdentity) {
		this.buyIdentity = buyIdentity;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
