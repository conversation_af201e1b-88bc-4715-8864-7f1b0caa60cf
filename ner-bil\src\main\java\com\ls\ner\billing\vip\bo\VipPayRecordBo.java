package com.ls.ner.billing.vip.bo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
public class VipPayRecordBo implements Serializable {

    private String recordId;

    private String mobile;

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    private String custId;

    private String vipType;
    private String vipTypeName;

    private String payType;
    private String payTypeName;

    private BigDecimal amount;
    private BigDecimal integral;

    private String payChannel;

    private String payChannelName;

    private LocalDateTime payTime;
    private String payTimeStr;

    private LocalDateTime effectTime;
    private String effectTimeStr;

    private LocalDateTime expireTime;
    private String expireTimeStr;

    private BigDecimal intervalDays;

    public BigDecimal getIntegral() {
        return integral;
    }

    public void setIntegral(BigDecimal integral) {
        this.integral = integral;
    }

    public BigDecimal getIntervalDays() {
        return intervalDays;
    }

    public void setIntervalDays(BigDecimal intervalDays) {
        this.intervalDays = intervalDays;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getVipType() {
        return vipType;
    }

    public void setVipType(String vipType) {
        this.vipType = vipType;
    }

    public String getVipTypeName() {
        return vipTypeName;
    }

    public void setVipTypeName(String vipTypeName) {
        this.vipTypeName = vipTypeName;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayChannelName() {
        return payChannelName;
    }

    public void setPayChannelName(String payChannelName) {
        this.payChannelName = payChannelName;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public String getPayTimeStr() {
        return payTimeStr;
    }

    public void setPayTimeStr(String payTimeStr) {
        this.payTimeStr = payTimeStr;
    }

    public LocalDateTime getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(LocalDateTime effectTime) {
        this.effectTime = effectTime;
    }

    public String getEffectTimeStr() {
        return effectTimeStr;
    }

    public void setEffectTimeStr(String effectTimeStr) {
        this.effectTimeStr = effectTimeStr;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getExpireTimeStr() {
        return expireTimeStr;
    }

    public void setExpireTimeStr(String expireTimeStr) {
        this.expireTimeStr = expireTimeStr;
    }
}
