<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.mktact.dao.IMarketActRpcDao" >

<!-- 描述:获取可参加的活动  创建人:biaoxiangd  创建时间: 2017/7/10 4:39 -->
	<select id="queryActAll" parameterType="string" resultType="java.util.Map">
		SELECT
			a.act_id AS actId,a.act_name AS actName,a.act_marks AS actMarks,a.act_type AS actType,
			b.act_cond_id AS actCondId,b.dct_cond_flag AS dctCondFlag,b.dct_lvl AS dctLvl,b.dct_cond_type AS dctCondType,
			b.prod_id AS prodId,b.dct_cond_unit AS dctCondUnit
		FROM e_mkt_act a,e_mkt_act_cond b
		WHERE a.act_id = b.act_id
		AND a.act_type IN ('02','03','04')
		AND a.ACT_STATE = '2'
		<![CDATA[
			AND a.eff_time <= now()
			AND a.exp_time >= now()
		]]>
	</select>
	<!--计价数量-->
	<select id="prodNumSum" parameterType="java.util.Map" resultType="String">
		select sum(PROD_NUM) from B_BILL_CYCLE_DET where PROD_ID=#{prodId}
	</select>
	<!--计价金额-->
	<select id="prodAmtSum" parameterType="java.util.Map" resultType="String">
		select sum(PROD_AMT+PROD_OFFSET_AMT) from B_BILL_CYCLE_DET where PROD_ID=#{prodId}
	</select>
	<!--查询订单数量-->
	<select id="getOrderNum" parameterType="java.util.Map" resultType="int">
		select count(1) from B_BILL where ORDER_STATE=#{orderState} and CUST_ID = #{custId}
	</select>

	<!-- @description:RPC05-12-03 获取注册送活动  @author:biaoxiangd  @create: 2017-09-27 23:11 -->
	<select id="getRegisterSendAct" parameterType="java.util.Map" resultType="java.util.Map">
		select str_to_date(IFNULL(a.cre_time,now()),'%Y-%m-%d %H:%i:%s') as putTime,b.dct_give_type as dctGiveType,group_concat(distinct b.cpn_id) cpnId
		from e_mkt_act a, e_mkt_act_give b
		where a.act_id = b.act_id
		and a.act_state = #{actState}
		and a.act_type = #{actType}
		<if test="dctGiveType != null and dctGiveType != ''">
			and b.dct_give_type = #{dctGiveType}
		</if>
		<if test="prodBusiType != null and prodBusiType != ''">
			and a.PROD_BUSI_TYPE = #{prodBusiType}
		</if>
		<![CDATA[
		and a.eff_time <= now()
		and a.exp_time >= now()
		]]>
		group by b.dct_give_type
	</select>

	<select id="getActiveActInfo" parameterType="java.util.Map" resultType="java.util.Map">
		select
			a.ACT_ID actId,
			a.ACT_TYPE actType,
			a.ACT_NAME actName,
			a.ACT_MARKS actMarks,
			a.ACT_STATE actState,
			a.PROD_BUSI_TYPE prodBusiType,
			b.TITLE title,
			b.INFO_TYPE infoType,
			b.CONTENT content,
			b.COBER_PIC coberPic,
			b.CONTENT_LINK contentLink,
			b.LINK_NAME linkName,
			b.LINK_URL linkUrl,
			b.CONTENT_URL contentUrl
		from e_mkt_act a, e_mkt_act_info b
		where a.act_id = b.act_id
		and a.eff_time <![CDATA[ <= ]]> now()
		and a.exp_time <![CDATA[ >= ]]> now()
		<if test="actState != null and actState != ''">
			and a.act_state = #{actState}
		</if>
		<if test="actType != null and actType != ''">
			and a.act_type = #{actType}
		</if>
	</select>

	<select id="queryJoinActCount" parameterType="java.util.Map" resultType="int">
		SELECT
			count(1)
		FROM
			e_act_cust_join
		WHERE CUST_ID = #{custId}
		AND ACT_ID = #{actId}
	</select>

	<insert id="joinActRecord" parameterType="java.util.Map">
		INSERT INTO e_act_cust_join(
		<if test="custId != null and custId != ''">CUST_ID,</if>
		<if test="actId != null and actId != ''">ACT_ID,</if>
		<if test="orderNo != null and orderNo != ''">ORDER_NO,</if>
			JOIN_TIME
		)VALUES (
		<if test="custId != null and custId != ''">#{custId},</if>
		<if test="actId != null and actId != ''">#{actId},</if>
		<if test="orderNo != null and orderNo != ''">#{orderNo},</if>
			now()
		)
	</insert>

	<select id="queryFullSaleInfo" parameterType="Map" resultType="Map">
		SELECT
		a.ACT_ID actId,c.DCT_COND_VALUE dctCondValue,d.DCT_VALUE dctValue,d.DCT_CALC_METHOD dctCalcMethod,
		ifnull(b.APP_USER,'03') appUser,ifnull(b.P_BILL_FLAG,'0') pBillFlag,ifnull(b.ALL_STATION_FLAG,'1') allStationFlag,c.DCT_TYPE dctType,
		d.ALL_FREE allFree
		FROM
		e_mkt_act a,
		e_mkt_act_cond b,
		e_mkt_act_cond_det c,
		e_mkt_act_dct d
		<where>
			a.ACT_ID = b.ACT_ID
			AND b.ACT_COND_ID = c.ACT_COND_ID
			AND d.ACT_COND_DET_ID = c.ACT_COND_DET_ID
			AND a.ACT_STATE = '2'
			and b.APP_USER in ('03',#{custType})
			<if test="pBillFlag != null and pBillFlag != ''">
				and b.P_BILL_FLAG = '1'
			</if>
			<if test="orderType != null and orderType != ''">
				AND a.PROD_BUSI_TYPE = #{orderType}
			</if>
			<if test="actType != null and actType != ''">
				AND a.ACT_TYPE = #{actType}
			</if>
			<if test="dctCondValue != null and dctCondValue != ''">
				AND c.DCT_COND_VALUE &lt;= #{dctCondValue}
			</if>
		</where>
		ORDER BY d.DCT_VALUE DESC
	</select>

	<select id="qryMktActList" parameterType="java.util.Map" resultType="java.util.Map">
			SELECT
				a.ACT_ID actId,
				b.CPN_ID cpnId,
				c.EFT_DATE eftDate,
				c.INV_DATE invDate,
				c.CPN_TIME_TYPE cpnTimeType,
				c.TIME_DURATION timeDuration,
				c.TIME_UNIT timeUnit,
				c.CPN_NAME cpnName,
				c.CPN_AMT cpnAmt
				FROM
				e_mkt_act a,
				e_mkt_act_give b,
				e_coupon c
				WHERE
				a.act_id = b.act_id
				AND b.CPN_ID = c.CPN_ID
				<if test="actStatusList != null">
					AND a.act_state IN 
					<foreach collection="actStatusList" open="(" item="item" separator="," close=")">
						#{item}
					</foreach>
				</if>
				AND a.act_type = #{actType}
				<if test="dctGiveType != null and dctGiveType != ''">
					and b.dct_give_type = #{dctGiveType}
				</if>
				<![CDATA[
				AND a.eff_time <= now()
				AND a.exp_time >= now()
				]]>
	</select>

	<select id="qryRecMktActList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
		a.ACT_ID actId,
		d.CPN_ID cpnId,
		c.EFT_DATE eftDate,
		c.INV_DATE invDate,
		c.CPN_TIME_TYPE cpnTimeType,
		c.TIME_DURATION timeDuration,
		c.TIME_UNIT timeUnit,
		c.CPN_NAME cpnName,
		b.MAX_VALUE maxNum,
		d.BASE_VALUE baseValue,
		c.CPN_AMT cpnAmt
		FROM
		e_mkt_act a,
		e_bal_present_section b,
		e_bal_present_section_det d,
		e_coupon c
		WHERE
		a.act_id = b.act_id
		and 	b.PRESENT_SECTION_ID= d.PRESENT_SECTION_ID
		and d.CPN_ID = c.CPN_ID
		AND d.CPN_ID = c.CPN_ID
		<if test="actStatusList != null">
			AND a.act_state IN
			<foreach collection="actStatusList" open="(" item="item" separator="," close=")">
				#{item}
			</foreach>
		</if>
		AND a.act_type = #{actType}
		<![CDATA[
				AND a.eff_time <= now()
				AND a.exp_time >= now()
				and d.REF_CEIL<=#{amt}
						]]>
		and d.REF_FLOOR>=#{amt}
	</select>


	<select id="qryActStationCount" parameterType="java.util.Map" resultType="int">
		SELECT
		count(1)
		FROM
		e_act_station a
		WHERE
		a.ACT_ID = #{actId}
		AND a.STATION_ID = #{stationId}
	</select>

	<select id="qryMktActByScope" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.ACT_ID actId,
			b.ACT_TYPE actType,
			a.BUILD_ID buildId,
			a.CITY city,
			a.STATION_ID stationId,
			a.SCOPE_TYPE scopeType
		FROM
		e_mkt_act_scope a LEFT JOIN e_mkt_act b ON a.ACT_ID = b.ACT_ID
		where (b.ACT_STATE = '2' OR b.ACT_STATE = '1')
			<if test="custType!=null and custType!=''">
				AND b.CUST_TYPE like concat('%', #{custType},'%')
			</if>
			<if test="actType!=null and actType!=''">
				AND b.ACT_TYPE = #{actType}
			</if>
			<if test="scopeType!=null and scopeType!='' and scopeType!='0'.toString()">
				AND a.SCOPE_TYPE = #{scopeType}
			</if>
			<if test="scopeType!=null and scopeType!='' and scopeType =='0'.toString()">
				AND a.SCOPE_TYPE != '4'
			</if>
			<if test="buildId !=null and buildId !=''">
				AND a.BUILD_ID =#{buildId}
			</if>
			<if test="city !=null and city !=''">
				AND a.CITY =#{city}
			</if>
			<if test="stationId !=null and stationId !=''">
				AND a.STATION_ID =#{stationId}
			</if>
			<if test="buildList!=null">
				AND a.BUILD_ID in
				<foreach collection="buildList" open="(" close=")" item="item" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="cityList!=null">
				AND a.CITY in
				<foreach collection="cityList" open="(" close=")" item="item" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="stationList!=null">
				AND a.STATION_ID in
				<foreach collection="stationList" open="(" close=")" item="item" separator=",">
					#{item}
				</foreach>
			</if>
		<![CDATA[
			AND b.eff_time <= now()
			AND b.exp_time >= now()
		]]>
	</select>

	<select id="qryActContDct"  parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			c.PROD_ID prodId,
			c.DCT_VALUE dctValue
		FROM
			e_mkt_act_cond a
		LEFT JOIN e_mkt_act_cond_det b ON a.ACT_COND_ID = b.ACT_COND_ID
		LEFT JOIN e_mkt_act_dct c ON b.ACT_COND_DET_ID = c.ACT_COND_DET_ID
		<where>
			<if test="actId!=null and actId!=''">
				AND a.ACT_ID = #{actId}
			</if>
		</where>
	</select>

	<select id="qryActAndInfo"  parameterType="java.util.Map" resultType="java.util.Map">
		select
			a.ACT_ID actId,
			a.ACT_TYPE actType,
			a.ACT_NAME actName,
			a.ACT_MARKS actMarks,
			a.ACT_STATE actState,
			a.ACT_SUB_TYPE actSubType,
			a.PROD_BUSI_TYPE prodBusiType,
			b.TITLE title,
			b.INFO_TYPE infoType,
			b.CONTENT content,
			b.COBER_PIC coberPic,
			b.CONTENT_LINK contentLink,
			b.LINK_NAME linkName,
			b.LINK_URL linkUrl,
			b.CONTENT_URL contentUrl
		from e_mkt_act a, e_mkt_act_info b
		where a.act_id = b.act_id
		<if test="actId != null and actId != ''">
			and a.ACT_ID = #{actId}
		</if>
		<if test="actType != null and actType != ''">
			and a.act_type = #{actType}
		</if>
	</select>

	<select id="qeyActJoin"  parameterType="java.util.Map" resultType="java.util.Map">
		SELECT a.ACT_ID actId,a.ORDER_NO orderNo,a.CUST_ID custId,b.ACT_NAME actName,b.ACT_TYPE actType FROM e_act_cust_join a LEFT JOIN e_mkt_act b ON a.ACT_ID=b.ACT_ID
		<where>
			<if test="actId!=null and actId!=''">
				AND a.ACT_ID = #{actId}
			</if>
			<if test="orderNo!=null and orderNo!=''">
				AND a.ORDER_NO = #{orderNo}
			</if>
			<if test="custId!=null and custId!=''">
				AND a.CUST_ID = #{custId}
			</if>
		</where>
	</select>
</mapper>