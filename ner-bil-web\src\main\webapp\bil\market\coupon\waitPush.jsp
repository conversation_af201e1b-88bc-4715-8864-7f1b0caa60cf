<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券待推广">
<style type="text/css">
.hiddenFlow{
overflow-x:hidden; 
}
</style>
</ls:head>
<ls:body>
	<ls:form name="waitPush" cssClass="hiddenFlow">
		<ls:text name="cpnStatus" value="1" visible="false"></ls:text>
		<ls:text name="pushStatus" value="0" visible="false"></ls:text>
		<ls:text name="invDate" value="true" visible="false"></ls:text>
		<ls:text name="alrdyGetNum" value="0" visible="false"></ls:text>
		<ls:text name="chargeType" value="${chargeType}" visible="false"></ls:text>
		<ls:title text="查询条件"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td width="120px"><ls:label text="优惠券名称"></ls:label></td>
				<td width="250px"><ls:text name="cpnName"></ls:text></td>
				<td><ls:label text="优惠券类型"></ls:label></td>
				<td>
					<ls:select name="cpnType">
						<ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td width="150px"><ls:label text="面额/折扣"></ls:label></td>
				<td width="200px"><ls:text name="cpnAmt"></ls:text></td>
				<td >
					<div class="pull-right">
						<ls:button text="清空" onclick="clearAll" />
						<ls:button text="查询" onclick="query" />
					</div>
				</td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="waitPushGrid" caption="优惠券列表" showRowNumber="false"
						height="410px" width="100%" showCheckBox="false" singleSelect="true" >
						<ls:gridToolbar name="operation" >
                    		<ls:gridToolbarItem name="push" text="推广" imageKey="set" onclick="pushCoupon"></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column name="cpnNo" caption="优惠券编号" hidden="true" />
						<ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="150px"/>
						<ls:column caption="优惠券类型" name="cpnTypeName" align="center" />
						<ls:column caption="面额/折扣" name="cpnAmt" align="right" />
						<ls:column caption="发行数量" name="cpnNum" align="right" width="100px"/>
						<ls:column caption="每人限领" name="limGetNum" align="right" width="80px"/>
						<ls:column caption="有效时间" name="effectTime" align="center" />
						<ls:pager pageSize="15,20"></ls:pager>
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
	function pushCoupon(){
		var item = waitPushGrid.getSelectedItem();
    	if(item==null){
    		 LS.message("info","请选择一条记录!");
    		 return;
    	}
    	
    	LS.dialog("~/market/coupon/pushCoupon?cpnNo="+item.cpnNo,"优惠券推广",400,150,true, null);
	}
	window.parentQquery=query;
    function query(){
      var datas={};
      datas = waitPush.getFormData();
      waitPushGrid.query("~/market/coupon/getCoupons",datas);
    }
    function clearAll(){
      waitPush.clear();
    };
    
 	function linkFunc(rowdata){
    	var _cpnNo = rowdata.cpnNo;
    	var _cpnName = rowdata.cpnName;
    	return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnNo + "\")'>"+_cpnName+"</a></u>";
    }
    
    window.showCouponDetail = function(_cpnNo){
    	LS.dialog("~/market/coupon/initCouponDetail?cpnNo="+_cpnNo,"优惠券详细信息",1000, 600,true, null);
    }
    
    
   
    </ls:script>
</ls:body>
</html>