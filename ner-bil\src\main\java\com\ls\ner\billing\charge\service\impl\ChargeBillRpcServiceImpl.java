package com.ls.ner.billing.charge.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.cache.lua.RedisLuaScriptUtil;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.exception.BaseBusinessException;
import com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo;
import com.ls.ner.billing.api.charge.bo.ChargeBillConfigServiceModeBO;
import com.ls.ner.billing.api.charge.bo.ChargeBillStaionInfoBO;
import com.ls.ner.billing.appendchargeitem.dao.IAppendChargeItemDao;
import com.ls.ner.billing.charge.bo.*;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.dao.*;
import com.ls.ner.billing.charge.service.IChargeBillingRltService;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import com.ls.ner.billing.utils.RedisDistributedLock;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.billing.xpcharge.service.IXpPileSendService;
import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.ls.ner.util.*;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.api.charge.service.IChargeBillRpcService;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;

@Service(target = { ServiceType.RPC }, value = "chargeBillRpcService")
public class ChargeBillRpcServiceImpl implements IChargeBillRpcService {

	private static final Logger LOGGER = LoggerFactory.getLogger(ChargeBillRpcServiceImpl.class);

	@ServiceAutowired(serviceTypes =ServiceType.LOCAL, value = "chargeBillingRltService")
	private IChargeBillingRltService chargeBillingRltService;

	@Autowired
	private IChargeBillingRltDao chargeBillingRltDao;

	@Autowired
	private ChargeBillingRltUtil cbRltUtil;

	@Autowired
	private IChargePeriodsDao chargePeriodsDao;


	@Autowired
	private IChcbillPileSendDao chcbillPileSendDao;

	@ServiceAutowired("chargeSerItemService")
	private IChargeSerItemService chargeSerItemService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@Autowired
	private ChargeBillingRltCalculator chargeBillingRltCalculator;

	@Autowired
	private IChargeBillingRltPeriodsDao chargeBillingRltPeriodsDao;

	@Autowired
	private IChargeBillingPeriodsDao chargeBillingPeriodsDao;

	@Autowired
	private IChargeBillingDao chargeBillingDao;

	@Autowired
	private IChargeBillRpcDao chargeBillRpcDao;

	@Autowired
	private IChargeBillingConfDao chargeBillingConfDao;

	@Autowired
	private IChargeSerItemDao chargeSerItemDao;

	@Autowired
	private IAppendChargeItemDao appendChargeItemDao;

	@ServiceAutowired("xpPileSendService")
	private IXpPileSendService xpPileSendService;

	@ServiceAutowired(serviceTypes= ServiceType.RPC)
	private ISysParamService sysParamService;

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@ServiceAutowired("xpStationSendService")
	private IXpStationSendService xpStationSendService;
	@ServiceAutowired("servicePriceService")
	private ITimeShareServicePriceService servicePriceService;
	@ServiceAutowired("peakService")
	private IXpPeakService peakService;
	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	private RedisCluster redisService = RedisCluster.getInstance();

	private static final String RLT_PER = "RLT_";//实际计费结果redis的前缀
	private static final String PERIODS_PER = "PERIODS_";//实际计费分时明细redis的前缀
	private static final String SER_ITEM = "ITEMS_";//实际计费服务费redis的前缀
	private static final int EXPIRE = 3600;//超时时间   一小时
	private static final String SER_PERIODS_PER = "SER_PERIODS_";//服务费实时计费分时明细redis的前缀
	private final RedisDistributedLock redisLock = new RedisDistributedLock(RedisCluster.getInstance());


	/**
	 * <AUTHOR>
	 * @throws Exception
	 * @dateTime 2016-11-30
	 * @description *******    RPC05-02-02查询充电定价
	 */
	public List<Map<String, Object>> getChargePrice(Map<String,Object> inMap) throws Exception{
		//如果是分时服务费模式，则走分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.getChargePrice(inMap);
		}

		List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
		String isAssistPileBilling = sysParamService.getSysParamsValues("isAssistPileBilling");// 是否支持桩计费
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		if(stationList != null){
			List<Map<String,Object>> pBilStationList = new ArrayList<>();
			List<Map<String,Object>> sBilStationList = new ArrayList<>();
			if (PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
				for (Map<String, Object> sMap : stationList) {
					if (sMap.containsKey("pileIds")){
						pBilStationList.add(sMap);
					}else{
						sBilStationList.add(sMap);
					}
				}
			} else {
				sBilStationList = stationList;
			}
			// 站点为桩计费处理
			String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
			if (CollectionUtils.isNotEmpty(pBilStationList)){
				Map<String,Object> pInMap = new HashMap<>();
				pInMap.put("prodType", inMap.get("prodType"));
				pInMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
				pInMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
				for (Map<String, Object> pBilMap : pBilStationList) {
					pInMap.put("pileIds", pBilMap.get("pileIds"));
					pInMap.put("stationId", pBilMap.get("stationId"));

					Map<String,Object> stationCharge = Maps.newHashMap();
					stationCharges.add(stationCharge);
					stationCharge.put("stationId", pBilMap.get("stationId"));
					List<Map> prodList = new LinkedList<Map>();
					stationCharge.put("prodList", prodList);
					//充电费组合
					Map<String,Object> prodOne = new HashMap<String, Object>();
					prodList.add(prodOne);
					List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
					prodOne.put("pricingCombine", pricingCombine);
					Map<String,Object> pricing = Maps.newHashMap();
					pricingCombine.add(pricing);
					pricing.put("chcRemark","");

					LOGGER.debug("============pInMap:{}",pInMap);
					List<Map> confs = getChargeConfs(pInMap);
					List<Map<String,Object>> pileCharges = new LinkedList<Map<String,Object>>();
					List<Map<String, Object>> pileConfs = new LinkedList<Map<String, Object>>();
					List<Map<String, Object>> pileBillList = new LinkedList<Map<String, Object>>();
					String currentPileId = null;
					StringBuilder chcNoSb = new StringBuilder();
					StringBuilder itemNoSb = new StringBuilder();
					for (Map conf : confs) {
						String pileId = cbRltUtil.returnNotNull(conf.get("PILE_ID"));
						String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
						String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
						if (currentPileId == null || !currentPileId.equals(pileId)) {//第一个pileId
							currentPileId = pileId;
							pileConfs.add(conf);
							chcNoSb.append(chcNo).append(",");
							itemNoSb.append(itemNo).append(",");
						} else {//如果不是第一个，将多余的充电计费配置状态改为无效
							if (!"".equals(chcNo)) {
								ChargeBillingConfBo cbc = new ChargeBillingConfBo();
								cbc.setChcNo(chcNo);
								cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
								chargeBillingConfDao.updateChargeBillingConf(cbc);
							}
						}
					}
					//2、查询充电分时设置
					String chcNos = chcNoSb.toString();
					if (!StringUtils.isEmpty(chcNos)) {
						chcNos = chcNos.substring(0, chcNos.length() - 1);
						List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
						List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
						MergeUtil.mergeList(pileConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
					}
					//3、查询服务费用
					Map<String, Object> itemMap = null;
					if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
						String itemNos = itemNoSb.toString();
						if (!StringUtils.isEmpty(itemNos)) {
							itemNos = itemNos.substring(0, itemNos.length() - 1);
							ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
							bo.setItemNo(itemNos);
							List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
							if (csItems != null && csItems.size() > 0) {
								itemMap = new HashMap<String, Object>();
								for (ChargeSerItemBo csBo : csItems)
									itemMap.put(csBo.getItemNo(), csBo);
							}
						}
					}
					//4、组合数据
					Map<String,Object> pileCharge = Maps.newHashMap();
					Double minPrice = null;
					Double maxPrice = null;
					for(Map sconf : pileConfs){
						List<Map<String,Object>> chargeBillDetailList = new ArrayList();
						Double price = null;
						//充电费
						if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
							price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
						}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
							List<Map> periods = (List<Map>)sconf.get("periods");
							if(periods != null){
								double periodPrice = 0.0;
								for(Map prerod : periods){
									double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
									String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
									String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
									if(isBetweenDate(beginTime,endTime)){
										periodPrice = p;
									}
								}
								price = periodPrice;
							}
						}
						//服务费
						if(itemMap != null){
							double servicePrice = 0.0;
							String unit ="度";
							String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
							String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
							if(!StringUtils.isEmpty(itemNos)){
								String[] iNos = itemNos.split(",");
								String[] iPrices = itemPrices.split(",");
								for(int i = 0;i < iNos.length ; i ++){
									if(itemMap.get(iNos[i]) != null) {
										ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
										if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
											double iprice = Double.parseDouble(iPrices[i]);
											servicePrice = iprice;
											unit = csBo.getItemUnitValueName();
										}
										if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
											double iprice = Double.parseDouble(iPrices[i]);
											if (iprice > servicePrice) {
												servicePrice = iprice;
												unit = csBo.getItemUnitValueName();
											}
										}
									}
								}
							}
							if (CollectionUtils.isNotEmpty(chargeBillDetailList)){
								for (Map<String, Object> detailMap : chargeBillDetailList) {
									detailMap.put("chargePriceValue",servicePrice);
									detailMap.put("servicePrice",servicePrice + "元/度");
								}
							}
							price += servicePrice;
						}
						// 计算最小值
						if (minPrice != null){
							if (MathUtils.compareTo(StringUtil.getString(price),StringUtil.getString(minPrice)) < 0){
								minPrice = price;
							}
						} else {
							minPrice = price;
						}

						// 计算最大值
						if (minPrice != null){
							if (MathUtils.compareTo(StringUtil.getString(price),StringUtil.getString(maxPrice)) > 0){
								maxPrice = price;
							}
						} else {
							maxPrice = price;
						}
					}
					if (!StringUtils.isEmpty(minPrice)){
						pricing.put("chcRemark",FormatUtil.formatNumber(String.valueOf(minPrice),4,4) +  "元/度起");
						pricing.put("priceRemarkNum",FormatUtil.formatNumber(String.valueOf(minPrice),4,4));
					}
				}
			}


			// ==================站点为站计费处理=======================
			if (CollectionUtils.isNotEmpty(sBilStationList)){
				Map<String,Object> sInMap = new HashMap<>();
				sInMap.put("prodType", inMap.get("prodType"));
				sInMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
				sInMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
				sInMap.put("stationList",sBilStationList);
				String stationIds = StringUtil.nullForString(inMap.get("stationId"));
				if(StringUtil.isNotBlank(stationIds)){
					sInMap.put("stationIds", stationIds.split(","));
				}
				//1、查询计费配置
				List<Map> confs = getChargeConfs(sInMap);
				if(confs != null && confs.size() > 0){
					String currentStationId = null;
					StringBuilder chcNoSb = new StringBuilder();
					StringBuilder itemNoSb = new StringBuilder();
					List<Map<String,Object>> stationConfs = new LinkedList<Map<String,Object>>();
					for(Map conf : confs){
						String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
						String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
						String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
						if(currentStationId == null || !currentStationId.equals(stationId)){//第一个stationId
							currentStationId = stationId;
							stationConfs.add(conf);
							chcNoSb.append(chcNo).append(",");
							itemNoSb.append(itemNo).append(",");
						}else{//如果不是第一个，将多余的充电计费配置状态改为无效
							if(!"".equals(chcNo)){
								ChargeBillingConfBo cbc = new ChargeBillingConfBo();
								cbc.setChcNo(chcNo);
								cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
								chargeBillingConfDao.updateChargeBillingConf(cbc);
							}
						}
					}
					//2、查询充电分时设置
					String chcNos = chcNoSb.toString();
					if(!StringUtils.isEmpty(chcNos)){
						chcNos = chcNos.substring(0, chcNos.length() - 1);
						List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
						List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
						MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
					}
					//3、查询服务费用
					Map<String,Object> itemMap = null;
					if("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))){//所有产品
						String itemNos = itemNoSb.toString();
						if(!StringUtils.isEmpty(itemNos)){
							itemNos = itemNos.substring(0, itemNos.length() - 1);
							ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
							bo.setItemNo(itemNos);
							List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
							if(csItems != null && csItems.size() > 0){
								itemMap = new HashMap<String, Object>();
								for(ChargeSerItemBo csBo : csItems)
									itemMap.put(csBo.getItemNo(), csBo);
							}
						}
					}
					LOGGER.debug("getChargePrice-itemMap:"+itemMap);

					//4、组合数据
					for(Map sconf : stationConfs){
						Map<String,Object> stationCharge = Maps.newHashMap();
						stationCharges.add(stationCharge);
						stationCharge.put("stationId", sconf.get("STATION_ID"));
						List<Map> prodList = new LinkedList<Map>();
						stationCharge.put("prodList", prodList);
						//充电费组合
						Map<String,Object> prodOne = new HashMap<String, Object>();
						prodList.add(prodOne);
						prodOne.put("prodId", sconf.get("CHC_NO"));
						prodOne.put("prodName", "充电费");
						prodOne.put("mandatoryType", "02");
						List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
						prodOne.put("pricingCombine", pricingCombine);
						String details = "";
						String beginStr = null;
						String endStr = null;
						Double price = null;
						if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
							details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
							if(beginStr == null || cbRltUtil.isGt(beginStr, String.valueOf(sconf.get("CHARGE_PRICE")))){
								beginStr = FormatUtil.formatNumber(String.valueOf(sconf.get("CHARGE_PRICE")),4,2);
							}
							if(endStr == null || !cbRltUtil.isGt(endStr, String.valueOf(sconf.get("CHARGE_PRICE")))){
								endStr = FormatUtil.formatNumber(String.valueOf(sconf.get("CHARGE_PRICE")),4,2);
							}
							price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
						}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
							List<Map> periods = (List<Map>)sconf.get("periods");
							if(periods != null) {
								double periodPrice = 0.0;
								for (Map prerod : periods) {
									details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") + " " + FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度;";
									if (beginStr == null || cbRltUtil.isGt(beginStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
										beginStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
									}
									if (endStr == null || !cbRltUtil.isGt(endStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
										endStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
									}
									String beginTime = StringUtil.nullToString(prerod.get("BEGIN_TIME"));
									String endTime = StringUtil.nullToString(prerod.get("END_TIME"));
									if (isBetweenDate(beginTime, endTime)) {
										periodPrice = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
									}
								}
								price = periodPrice;
							}
						}
						Map<String,Object> pricing = Maps.newHashMap();
						pricingCombine.add(pricing);
						if(!cbRltUtil.isEq(beginStr, endStr))
							pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4,4) + "-" + FormatUtil.formatNumber(endStr,4,2) + "元/度");
						else
							pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4,4) +  "元/度");
						pricing.put("pricingSectDesc", details);
						//服务费组合
						if(itemMap != null){
							double servicePrice = 0.0;
							String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
							String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
							if(!StringUtils.isEmpty(itemNos)){
								String[] iNos = itemNos.split(",");
								String[] iPrices = itemPrices.split(",");
								for(int i = 0;i < iNos.length ; i ++){
									if(itemMap.get(iNos[i]) != null){
										ChargeSerItemBo csBo = (ChargeSerItemBo)itemMap.get(iNos[i]);
										Map<String,Object> prodTwo = Maps.newHashMap();
										prodList.add(prodTwo);
										prodTwo.put("prodId", csBo.getItemNo());
										prodTwo.put("prodName", csBo.getItemName());
										List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
										prodTwo.put("pricingCombine", pricingCombine2);
										Map<String,Object> pricing2 = Maps.newHashMap();
										pricingCombine2.add(pricing2);
										pricing2.put("pricingSectName",  FormatUtil.formatNumber(iPrices[i],4) + "元/" + csBo.getItemUnitValueName());
										pricing2.put("pricingSectDesc", StringUtil.isBlank(csBo.getRemarks()) ? pricing2.get("pricingSectName") : csBo.getRemarks());
										pricing2.put("pricingSectValue", FormatUtil.formatNumber(iPrices[i], 4));
										if(BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)){
											double iprice = Double.parseDouble(iPrices[i]);
											servicePrice = iprice;
										}
										if("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)){
											double iprice = Double.parseDouble(iPrices[i]);
											if(iprice > servicePrice){
												servicePrice = iprice;
											}
										}
									}
								}
							}
							price += servicePrice;
						}
						//复制备注
						pricing.put("chcRemark",FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
					}
				}
			}
		}
		List<Map<String,Object>> prodModeList = new LinkedList<Map<String,Object>>();
		Map<String,Object> prodMode = Maps.newHashMap();
		prodMode.put("prodModeId", "1");
		prodMode.put("stationList", stationCharges);
		prodModeList.add(prodMode);
		return prodModeList;
	}


	/**
	 * @description 充电价格描述，取服务费(最大值)+充电费(最大值)之和
	 * <AUTHOR>
	 * @create 2018-01-22 15:35:11
	 */
	public List<Map<String, Object>> getChargePriceRemark(Map<String,Object> inMap) throws Exception{
		List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		if(stationList != null || inMap.get("stationId") != null){
			inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
			String stationIds = StringUtil.nullForString(inMap.get("stationId"));
			if(!StringUtil.isBlank(stationIds)){
				inMap.put("stationIds", stationIds.split(","));
			}
			//1、查询计费配置
			List<Map> confs = getChargeConfs(inMap);
			if(confs != null && confs.size() > 0) {
				String currentStationId = null;
				StringBuilder chcNoSb = new StringBuilder();
				StringBuilder itemNoSb = new StringBuilder();
				List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
				for (Map conf : confs) {
					String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
					String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
					String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
					if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
						currentStationId = stationId;
						stationConfs.add(conf);
						chcNoSb.append(chcNo).append(",");
						itemNoSb.append(itemNo).append(",");
					} else {//如果不是第一个，将多余的充电计费配置状态改为无效
						if (!"".equals(chcNo)) {
							ChargeBillingConfBo cbc = new ChargeBillingConfBo();
							cbc.setChcNo(chcNo);
							cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
							chargeBillingConfDao.updateChargeBillingConf(cbc);
						}
					}
				}
				//2、查询充电分时设置
				String chcNos = chcNoSb.toString();
				if (!StringUtils.isEmpty(chcNos)) {
					chcNos = chcNos.substring(0, chcNos.length() - 1);
					List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
					List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
					MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
				}
				//3、查询服务费用
				Map<String, Object> itemMap = null;
				if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
					String itemNos = itemNoSb.toString();
					if (!StringUtils.isEmpty(itemNos)) {
						itemNos = itemNos.substring(0, itemNos.length() - 1);
						ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
						bo.setItemNo(itemNos);
						List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
						if (csItems != null && csItems.size() > 0) {
							itemMap = new HashMap<String, Object>();
							for (ChargeSerItemBo csBo : csItems)
								itemMap.put(csBo.getItemNo(), csBo);
						}
					}
				}
				//4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
				for(Map sconf : stationConfs){
					Map<String,Object> stationCharge = Maps.newHashMap();
					stationCharges.add(stationCharge);
					stationCharge.put("stationId", sconf.get("STATION_ID"));
					Double price = null;
					//充电费
					if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
						price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
					}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
						List<Map> periods = (List<Map>)sconf.get("periods");
						if(periods != null){
							double periodPrice = 0.0;
							for(Map prerod : periods){
								double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
								if(p > periodPrice){
									periodPrice = p;
								}
							}
							price = periodPrice;
						}
					}
//					LOGGER.debug("充电费 price：" + price);
					//服务费
					if(itemMap != null){
						double servicePrice = 0.0;
						String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
						String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
						if(!StringUtils.isEmpty(itemNos)){
							String[] iNos = itemNos.split(",");
							String[] iPrices = itemPrices.split(",");
							for(int i = 0;i < iNos.length ; i ++){
								if(itemMap.get(iNos[i]) != null) {
									ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
									if ("服务费".equals(csBo.getItemName())) {
										double iprice = Double.parseDouble(iPrices[i]);
										if (iprice > servicePrice) {
											servicePrice = iprice;
										}
									}
								}
							}
						}
						price += servicePrice;
					}
					stationCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
				}
			}
		}
		return stationCharges;
	}

	/**
	 * @description 充电费、服务费、充电费描述 取尖峰谷平的时段的价格进行描述，如“00:00-06:00 1.0元/度”
	 * <AUTHOR>
	 * @create 2018-01-22 15:35:11
	 */
	public List<Map<String, Object>> getElecAmtRemark(Map<String,Object> inMap) throws Exception{
		List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		if(stationList != null || inMap.get("stationId") != null){
			inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
			String stationIds = StringUtil.nullForString(inMap.get("stationId"));
			if(!StringUtil.isBlank(stationIds)){
				inMap.put("stationIds", stationIds.split(","));
			}
			//1、查询计费配置
			List<Map> confs = getChargeConfs(inMap);
			if(confs != null && confs.size() > 0) {
				String currentStationId = null;
				StringBuilder chcNoSb = new StringBuilder();
				StringBuilder itemNoSb = new StringBuilder();
				List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
				for (Map conf : confs) {
					String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
					String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
					String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
					if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
						currentStationId = stationId;
						stationConfs.add(conf);
						chcNoSb.append(chcNo).append(",");
						itemNoSb.append(itemNo).append(",");
					} else {//如果不是第一个，将多余的充电计费配置状态改为无效
						if (!"".equals(chcNo)) {
							ChargeBillingConfBo cbc = new ChargeBillingConfBo();
							cbc.setChcNo(chcNo);
							cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
							chargeBillingConfDao.updateChargeBillingConf(cbc);
						}
					}
				}
				//2、查询充电分时设置
				String chcNos = chcNoSb.toString();
				if (!StringUtils.isEmpty(chcNos)) {
					chcNos = chcNos.substring(0, chcNos.length() - 1);
					List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
					List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
					MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
				}
				//3、查询服务费用
				Map<String, Object> itemMap = null;
				if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
					String itemNos = itemNoSb.toString();
					if (!StringUtils.isEmpty(itemNos)) {
						itemNos = itemNos.substring(0, itemNos.length() - 1);
						ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
						bo.setItemNo(itemNos);
						List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
						if (csItems != null && csItems.size() > 0) {
							itemMap = new HashMap<String, Object>();
							for (ChargeSerItemBo csBo : csItems)
								itemMap.put(csBo.getItemNo(), csBo);
						}
					}
				}
				//4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
				for(Map sconf : stationConfs){
					Map<String,Object> stationCharge = Maps.newHashMap();
					stationCharges.add(stationCharge);
					stationCharge.put("stationId", sconf.get("STATION_ID"));
					Double price = null;
					String details = "";
					//充电费
					if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
						price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
						details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
					}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
						List<Map> periods = (List<Map>)sconf.get("periods");
						if(periods != null){
							double periodPrice = 0.0;
							for(Map prerod : periods){
								details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") +" "+ FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2) + "元/度;";
								double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
								if(p > periodPrice){
									periodPrice = p;
								}
							}
							price = periodPrice;
						}
					}
					stationCharge.put("elecAmt", FormatUtil.formatNumber(String.valueOf(price),4,2) +  "元/度");
					stationCharge.put("elecAmtRemark", details);
//					LOGGER.debug("充电费 price：" + price);
					//服务费
					if(itemMap != null){
						double servicePrice = 0.0;
						String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
						String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
						if(!StringUtils.isEmpty(itemNos)){
							String[] iNos = itemNos.split(",");
							String[] iPrices = itemPrices.split(",");
							for(int i = 0;i < iNos.length ; i ++){
								if(itemMap.get(iNos[i]) != null) {
									ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
									if ("服务费".equals(csBo.getItemName())) {
										double iprice = Double.parseDouble(iPrices[i]);
										if (iprice > servicePrice) {
											servicePrice = iprice;
										}
									}
								}
							}
						}
						stationCharge.put("servicePrice", FormatUtil.formatNumber(String.valueOf(servicePrice),4,2) +  "元/度");
					}
				}
			}
		}
		return stationCharges;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-23
	 * @description *******    RPC05-02-25充电计费 – 预估
	 */
	public Map<String, Object> estimate(Map<String, Object> inMap)throws Exception{
		//判断是否是分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.estimate(inMap);
		}
		//1、判断传参是否正确
		String orderNo = cbRltUtil.isNotEmpty(inMap.get("orderNo"), "订单编号不能为空");
		//2、查询该订单的充电计费信息
		Object obj = redisService.get(RLT_PER + orderNo);
		ChargeBillingRltBo cbrBo = null;
		List<ChargeBillingRltPeriodsBo> cbrList = null;
		if(obj != null){
			cbrBo = IJsonUtil.json2Obj((String)obj, ChargeBillingRltBo.class);
			cbrList = cbrBo.getRltPeriods();
		}else{
			cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
			if(cbrBo == null)
				throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");
			cbrList = chargeBillingRltPeriodsDao.queryChargeBillingRltPeriod(orderNo);
			cbrBo.setRltPeriods(cbrList);
			redisService.setex(RLT_PER + orderNo, cbrBo, EXPIRE);
		}

		//初始化数据
		servicePriceService.initItemChargeModeRltBo(cbrBo);

		//3、装配数据
		cbrBo.setBgnTime(cbRltUtil.isNotEmpty(inMap.get("chargingBgnTime"), "充电开始时间不能为空"));
		cbrBo.setEndTime(cbRltUtil.isNotEmpty(inMap.get("chargingEndTime"), "充电结束时间不能为空"));
		cbrBo.settTime(cbRltUtil.startToEndTime(cbrBo.getBgnTime(), cbrBo.getEndTime()));//充电时间，单位：分钟
		cbrBo.settPq(cbRltUtil.isNotEmpty(inMap.get("tPq"), "缺少充电总电量"));
		if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
			String edElecAmt = StringUtil.nullToString(inMap.get("edElecAmt"));//CCTIA表示充电费 其他协议表示总金额
			String edServiceAmt = StringUtil.nullToString(inMap.get("edServiceAmt"));//CCTIA表示服务费
			String edAmt = StringUtil.nullToString(inMap.get("edAmt"));//CCTIA表示总金额
			if(StringUtil.isEmpty(edAmt)){//如果没上报edAmt，则不是CCTIA协议，edElecAmt表示总金额
				cbrBo.setAmt(cbRltUtil.isNotEmpty(edElecAmt, "缺少总金额"));
			}else{
				cbrBo.settAmt(cbRltUtil.isNotEmpty(edElecAmt, "缺少充电费"));
				cbrBo.setItemTAmt(StringUtils.isEmpty(edServiceAmt)?"0.00":String.valueOf(edServiceAmt));
				cbrBo.setAmt(cbRltUtil.isNotEmpty(edAmt, "缺少总金额"));
			}
		}else{//远程计费
			if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
				Map orderMap = new HashMap();
				orderMap.put("appNo",orderNo);
				Object pObj = redisService.get(PERIODS_PER + orderNo);
				if(pObj != null){
					Gson gson=new Gson();
					List<ChargeBillingRltPeriodsBo> newPeriods =gson.fromJson((String)pObj, new TypeToken<List<ChargeBillingRltPeriodsBo>>(){}.getType());
					cbrBo.setNewPeriods(newPeriods);
				}else{
					List<ChargeBillingRltPeriodsBo> newPeriods = chargeBillingPeriodsDao.queryChargeBillingPeriod(orderMap);
					cbrBo.setNewPeriods(newPeriods);
				}
				cbRltUtil.refreshPeriods(cbrBo);
			}
			//4、计算费用
			String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
			if("toMoreStation".equals(chargeVesion)){//新版计费
				chargeBillingRltCalculator.calculatorNew(cbrBo,inMap);
			}else{
				chargeBillingRltCalculator.calculator(cbrBo);
			}		}
		//5、存储充电计费信息
		List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
		if(ChargeConstant.billCtlMode.REMOTE.equals(cbrBo.getBillCtlMode()) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//远程计费、分时计费
			if(periods!=null){
				ChargeBillingRltPeriodsBo p = periods.get(periods.size()-1);
				if(StringUtils.isEmpty(p.getSystemId())){
					chargeBillingPeriodsDao.insertChargeBillingPeriods(p);
				}else{
					// 分布式操作
					executeWithDistributedLock(p);
				}
				redisService.setex(PERIODS_PER + orderNo, periods, EXPIRE);
			}
		}
		//6、返回数据
		Map<String,Object> returnMap = Maps.newHashMap();
		returnMap.put("ctlMode",cbrBo.getBillCtlMode());//计费方式：1：本地、2：远程
		returnMap.put("prodBillId", cbrBo.getAppNo());//产品订单实例ID
		returnMap.put("billAmt", cbrBo.getAmt());//总金额
		List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
		returnMap.put("prodBillDet", prodBillDet);
		if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
			Map<String,Object> prodOne = Maps.newHashMap();
			prodBillDet.add(prodOne);
			prodOne.put("prodId", cbrBo.getChcNo());
			prodOne.put("prodName", "充电费");
			prodOne.put("billDetNum", cbrBo.gettPq());
			prodOne.put("billDetAmt", cbrBo.gettAmt());
			Map<String,Object> prodTwo = Maps.newHashMap();
			prodBillDet.add(prodTwo);
			prodTwo.put("prodId", cbrBo.getChcNo());
			prodTwo.put("prodName", "服务费");
			prodTwo.put("billDetAmt", cbrBo.getItemTAmt());
		}else{//远程计费
			Map<String,Object> prodOne = Maps.newHashMap();
			prodBillDet.add(prodOne);
			prodOne.put("prodId", cbrBo.getChcNo());
			prodOne.put("prodName", "充电费");
			prodOne.put("billDetNum", cbrBo.gettPq());
			prodOne.put("billDetAmt", cbrBo.gettAmt());
			List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
			prodOne.put("pricingCombine", pricingCombine);
			String details = "";
			String beginStr = null;
			String endStr = null;
			if(cbrList != null && cbrList.size() > 0){
				for(ChargeBillingRltPeriodsBo prerod : cbrList){
					details += prerod.getBeginTime() + "~" + prerod.getEndTime() +" "+ FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
					if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
						beginStr = prerod.getPrice();
					}
					if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
						endStr = prerod.getPrice();
					}
				}
			}else{
				details += "00:00~24:00 "  + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
				if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
					beginStr = cbrBo.getChargePrice();
				}
				if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
					endStr = cbrBo.getChargePrice();
				}
			}
			Map<String,Object> pricing = Maps.newHashMap();
			pricingCombine.add(pricing);
			if(!cbRltUtil.isEq(beginStr, endStr))
				pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
			else
				pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4) +  "元/度");
			pricing.put("pricingSectDesc", details);
			//获取服务费用项
			if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
				ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
				csCondition.setItemNo(cbrBo.getAttachItemNos());
				Object pObj = redisService.get(SER_ITEM + orderNo);
				List<ChargeSerItemBo> csList = null;
				if(pObj != null){
					Gson gson=new Gson();
					csList =gson.fromJson((String)pObj, new TypeToken<List<ChargeSerItemBo>>(){}.getType());
				}else{
					csList = chargeSerItemDao.queryChargeSerItems(csCondition);
					redisService.setex(SER_ITEM + orderNo, csList, EXPIRE);
				}
				if(csList != null){
					Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
					for(ChargeSerItemBo csi:csList){
						m.put(csi.getItemNo(), csi);
					}
					String[] itemNo = cbrBo.getAttachItemNos().split(",");
					String[] price = cbrBo.getAttachItemPrices().split(",");
					String[] nums = cbrBo.getAttachItemNums().split(",");
					String[] amts = cbrBo.getAttachItemAmts().split(",");
					for(int i = 0;i < itemNo.length;i ++){
						ChargeSerItemBo bo = m.get(itemNo[i]);
						if(bo != null){
							Map<String,Object> prodTwo = Maps.newHashMap();
							prodBillDet.add(prodTwo);
							prodTwo.put("prodId", bo.getItemNo());
							prodTwo.put("prodName", bo.getItemName());
							prodTwo.put("billDetNum", nums[i]);
							prodTwo.put("billDetAmt", amts[i]);
							prodTwo.put("price", FormatUtil.formatNumber(price[i],2));
							List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
							prodTwo.put("pricingCombine", pricingCombine2);
							Map<String,Object> pricing2 = Maps.newHashMap();
							pricingCombine2.add(pricing2);
							pricing2.put("pricingSectName",  FormatUtil.formatNumber(price[i],4) + "元/" + bo.getItemUnitValueName());
						}
					}
				}
			}
		}
		return returnMap;
	}

	/**
	 * 在分布式锁保护下执行缓存检查与数据库更新操作
	 */
	private void executeWithDistributedLock(ChargeBillingRltPeriodsBo p) throws InterruptedException {
		String systemId = p.getSystemId();
		String uuid = null;
		try {
			// 获取 Redis 锁的过期时间（默认 5 秒）
			String timeShareServiceRedisTime = sysParamService.getSysParamsValues("timeShareServiceRedisTime");
			if (StringUtils.isEmpty(timeShareServiceRedisTime)) {
				timeShareServiceRedisTime = "5";
			}
			int expireTime = Integer.parseInt(timeShareServiceRedisTime);

			// 获取分布式锁（最多重试 3 次）
			for (int i = 0; i < RedisLuaScriptUtil.DEFAULT_RETRY_TIMES; i++) {
				uuid = RedisLuaScriptUtil.acquireLock("lock:charge:" + systemId, expireTime);
				if (uuid != null) {
					LOGGER.info("获取锁成功，systemId={}", systemId);
					break;
				}
				LOGGER.warn("第 {} 次尝试获取锁失败，等待 {} ms 后重试", i + 1, RedisLuaScriptUtil.SLEEP_INTERVAL);
				Thread.sleep(RedisLuaScriptUtil.SLEEP_INTERVAL);
			}

			if (uuid == null) {
				LOGGER.warn("多次尝试获取锁失败，跳过处理 systemId={}", systemId);
				throw new BaseBusinessException("获取锁失败，跳过处理 systemId=" + systemId);
			}

			// ✅ 加锁成功，执行业务操作
			String cacheKey = "timeShare_d_" + systemId;
			String queuingNumber = (String) redisService.get(cacheKey);

			if (StringUtils.isEmpty(queuingNumber)) {
				chargeBillingPeriodsDao.updateChargeBillingPeriods(p);
			}

			redisService.setex(cacheKey, systemId, expireTime);

		} finally {
			// 安全释放锁
			if (uuid != null) {
				RedisLuaScriptUtil.releaseLock("lock:charge:" + systemId, uuid);
			}
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2019-01-23
	 * @description 删除充电计费
	 */
	@Override
	public Map<String, Object> deleteChargeBillingRlt(Map<String, Object> inMap) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if(StringUtil.isNotBlank(inMap.get("orderNo"))){
			chargeBillingRltDao.deleteChargeBillingRlt(StringUtil.nullForString(inMap.get("orderNo")));
			resultMap.put("ret", "200");
			resultMap.put("msg", "删除充电计费成功");
		}else{
			resultMap.put("ret", "400");
			resultMap.put("msg", "订单号参数为空");
		}
		return resultMap;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-25
	 * @description *******	RPC05-02-24充电计费 – 预收/计费版本
	 */
	public Map<String, Object> payInAdvance(Map<String, Object> inMap)throws Exception{
		//判断是否开启分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.payInAdvance(inMap);
		}

		Map<String, Object> cbMap = Maps.newHashMap();
		Map<String, Object> param = Maps.newHashMap();
		param.put("appNo", inMap.get("orderNo"));
		param.put("stationId", inMap.get("stationId"));
		param.put("pileId", inMap.get("pileId"));
		param.put("custId", inMap.get("custId"));
		param.put("groupId", inMap.get("groupId"));
		param.put("pCustId",inMap.get("pCustId"));
		chargeBillingRltService.createChargeOrderVersionForOrder(param);
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(String.valueOf(inMap.get("orderNo")));
		cbMap.put("prodBillId", cbrBo.getSystemId());
		List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
		cbMap.put("prodBillDet", prodBillDet);
		Map<String,Object> prodOne = Maps.newHashMap();
		prodBillDet.add(prodOne);
		prodOne.put("prodId", cbrBo.getChcNo());
		prodOne.put("prodName", "充电费");
		List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
		prodOne.put("pricingCombine", pricingCombine);
		String details = "";
		String beginStr = null;
		String endStr = null;
		//分时明细
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//如果是标准计费方式
			details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
			if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
				beginStr = cbrBo.getChargePrice();
			}
			if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
				endStr = cbrBo.getChargePrice();
			}
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//如果是分时计费方式
			ChargeBillingConfQueryCondition cperiodsCondition = new ChargeBillingConfQueryCondition();
			cperiodsCondition.setChcNo(cbrBo.getChcNo());
			List<ChargePeriodsBo> cperiodsList = peakService.queryPeriodListCheckPeak(cperiodsCondition);
			if(cperiodsList!=null)
				for(ChargePeriodsBo cpBo : cperiodsList){
					details += cpBo.getBeginTime() + "~" + cpBo.getEndTime() + "  " + FormatUtil.formatNumber(cpBo.getPrice(),4) + "元/度;";
					if(beginStr == null || cbRltUtil.isGt(beginStr, cpBo.getPrice())){
						beginStr = cpBo.getPrice();
					}
					if(endStr == null || !cbRltUtil.isGt(endStr, cpBo.getPrice())){
						endStr = cpBo.getPrice();
					}
				}
		}
		Map<String,Object> pricing = Maps.newHashMap();
		pricingCombine.add(pricing);
		if(!cbRltUtil.isEq(beginStr, endStr))
			pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
		else
			pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
		pricing.put("pricingSectDesc", details);
		//获取服务费用项
		if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
			ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
			csCondition.setItemNo(cbrBo.getAttachItemNos());
			csCondition.setPrice(cbrBo.getAttachItemPrices());
			List<ChargeSerItemBo> csList = chargeSerItemService.querySerItemsByNo(csCondition);
			if(csList != null)
				for(ChargeSerItemBo bo : csList){
					Map<String,Object> prodTwo = Maps.newHashMap();
					prodBillDet.add(prodTwo);
					prodTwo.put("prodId", bo.getItemNo());
					prodTwo.put("prodName", bo.getItemName());
					List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
					prodTwo.put("pricingCombine", pricingCombine2);
					Map<String,Object> pricing2 = Maps.newHashMap();
					pricingCombine2.add(pricing2);
					pricing2.put("pricingSectName",  FormatUtil.formatNumber(bo.getServicePrice(),4) + "元/" + bo.getItemUnitValueName());
				}
		}
		return cbMap;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-25
	 * @description *******	RPC05-02-26充电计费 – 结算
	 */
	public  Map<String, Object> settlement(Map<String, Object> inMap)throws Exception{
		//判断是否开启分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.settlement(inMap);
		}
		//1、判断传参是否正确
		String orderNo = cbRltUtil.isNotEmpty(inMap.get("orderNo"), "订单编号不能为空");
		//2、查询该订单的充电计费信息
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
		if(cbrBo == null)
			throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");
		List<ChargeBillingRltPeriodsBo> cbrList = chargeBillingRltPeriodsDao.queryChargeBillingRltPeriod(orderNo);

		//初始化数据
		servicePriceService.initItemChargeModeRltBo(cbrBo);

		cbrBo.setRltPeriods(cbrList);
		//3、装配数据
		cbrBo.setBgnTime(cbRltUtil.isNotEmpty(inMap.get("chargingBgnTime"), "充电开始时间不能为空"));
		cbrBo.setEndTime(cbRltUtil.isNotEmpty(inMap.get("chargingEndTime"), "充电结束时间不能为空"));
		cbrBo.settTime(cbRltUtil.startToEndTime(cbrBo.getBgnTime(), cbrBo.getEndTime()));//充电时间，单位：分钟
		cbrBo.setApplyEndTime("true");//请求结束时间
		cbrBo.settPq(cbRltUtil.isNotEmpty(inMap.get("tPq"), "缺少充电总电量"));
		List<Map<String,Object>> periodsList = (List<Map<String,Object>>)inMap.get("periodsList");
		String chargeMode = ChargeConstant.chcBillingChargeMode.TIME_SHARE;//计费方式
		if(periodsList == null || periodsList.size() == 0){
			chargeMode = ChargeConstant.chcBillingChargeMode.STANDARD;
		}
		if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
			cbrBo.settAmt(cbRltUtil.isNotEmpty(inMap.get("elecAmt"), "缺少充电费"));
			cbrBo.setItemTAmt(StringUtils.isEmpty(inMap.get("serviceAmt"))?"0.00":String.valueOf(inMap.get("serviceAmt")));
			cbrBo.setAmt(cbRltUtil.isNotEmpty(inMap.get("tAmt"), "缺少总金额"));
			cbrBo.setChargeMode(chargeMode);//计费方式
			if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
				List<ChargeBillingRltPeriodsBo> periods = cbRltUtil.rltPeriods((List<Map<String,Object>>)inMap.get("periodsList"));
				cbRltUtil.chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());
				cbrBo.setRltPeriods(periods);//分时记录
			}
		}else{//远程计费
			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(chargeMode)){//inMap传入的是标准计费

				}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(chargeMode)){//inMap传入的是分时计费

				}
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(chargeMode)){//inMap传入的是标准计费
					cbrBo.setChargeMode(chargeMode);
					List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();
					if(rltPeriods == null || rltPeriods.size() == 0)
						throw new ChargeBillingRltException("充电计费配置分时计费方式，没有分时记录");
					for(ChargeBillingRltPeriodsBo rltPeriod:rltPeriods){
						if(cbRltUtil.compareTime(cbRltUtil.toTime(cbrBo.getBgnTime()), rltPeriod.getBeginTime(), rltPeriod.getEndTime())){
							cbrBo.setChargePrice(rltPeriod.getPrice());
							break;
						}
					}
					cbrBo.setRltPeriods(null);
				}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(chargeMode)){//inMap传入的是分时计费
					List<ChargeBillingRltPeriodsBo> periods = cbRltUtil.rltPeriods((List<Map<String,Object>>)inMap.get("periodsList"));
					cbRltUtil.chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());
					LOGGER.debug("billRpc-分时组装前:"+ JsonUtil.obj2Json(periods));
					cbrBo.setRltPeriods(cbRltUtil.priceForRltPeriods(periods, cbrBo.getRltPeriods()));
					LOGGER.debug("billRpc-分时组装后:"+ JsonUtil.obj2Json(cbrBo.getRltPeriods()));
				}
			}

			//4、计算费用
			String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
			if("toMoreStation".equals(chargeVesion)){//新版计费
				chargeBillingRltCalculator.calculatorNew(cbrBo,inMap);
			}else{
				chargeBillingRltCalculator.calculator(cbrBo);
			}

		}
		//5、存储充电计费信息,互联互通过来的结算只计算金额不保存
		if (!"hlhtSettle".equals(StringUtil.getString(inMap.get("from")))){
			chargeBillingRltDao.updateChargeBillingRlt(cbrBo);
			chargeBillingRltPeriodsDao.deleteChargeBillingRltPeriod(orderNo);
			List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
			LOGGER.debug("billRpc-分时计算后:"+ JsonUtil.obj2Json(periods));
			if(periods !=null)
				for(ChargeBillingRltPeriodsBo period:periods)
					chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(period);
		}
		//6、返回数据
		Map<String,Object> returnMap = Maps.newHashMap();
		returnMap.put("prodBillId", cbrBo.getAppNo());//产品订单实例ID
		returnMap.put("billAmt", cbrBo.getAmt());//总金额
		List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
		returnMap.put("prodBillDet", prodBillDet);
		if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
			Map<String,Object> prodOne = Maps.newHashMap();
			prodBillDet.add(prodOne);
			prodOne.put("prodId", cbrBo.getChcNo());
			prodOne.put("prodName", "充电费");
			prodOne.put("billDetNum", cbrBo.gettPq());
			prodOne.put("billDetAmt", cbrBo.gettAmt());
			List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
			prodOne.put("pricingCombine", pricingCombine);
			String details = "";
			String beginStr = null;
			String endStr = null;
			if(cbrList != null && cbrList.size() > 0){
				for(ChargeBillingRltPeriodsBo prerod : cbrList){
					details += prerod.getBeginTime() + "~" + prerod.getEndTime() + "  " + FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
					if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
						beginStr = prerod.getPrice();
					}
					if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
						endStr = prerod.getPrice();
					}
				}
			}else{
				details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
				if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
					beginStr = cbrBo.getChargePrice();
				}
				if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
					endStr = cbrBo.getChargePrice();
				}
			}
			Map<String,Object> pricing = Maps.newHashMap();
			pricingCombine.add(pricing);
			if(!cbRltUtil.isEq(beginStr, endStr))
				pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
			else
				pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
			pricing.put("pricingSectDesc", details);

			//服务费
			Map<String,Object> prodTwo = Maps.newHashMap();
			prodBillDet.add(prodTwo);
			prodTwo.put("prodId", cbrBo.getChcNo());
			prodTwo.put("prodName", "服务费");
			prodTwo.put("billDetNum", cbrBo.gettPq());
			prodTwo.put("billDetAmt", cbrBo.getItemTAmt());
			if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
				ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
				csCondition.setItemNo(cbrBo.getAttachItemNos());
				List<ChargeSerItemBo> csList = chargeSerItemDao.queryChargeSerItems(csCondition);
				if(csList != null){
					Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
					for(ChargeSerItemBo csi:csList){
						m.put(csi.getItemNo(), csi);
					}
					String[] itemNo = cbrBo.getAttachItemNos().split(",");
					String[] price = cbrBo.getAttachItemPrices().split(",");
					String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
					if("toMoreStation".equals(chargeVesion)){//新版计费
						for(int i = 0;i < itemNo.length;i ++){
							if(BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(itemNo[i])) {
								ChargeSerItemBo bo = m.get(itemNo[i]);
								if (bo != null) {
									List<Map<String, Object>> pricingCombine2 = new LinkedList<Map<String, Object>>();
									prodTwo.put("pricingCombine", pricingCombine2);
									Map<String, Object> pricing2 = Maps.newHashMap();
									pricingCombine2.add(pricing2);
									pricing2.put("pricingSectName", "" + FormatUtil.formatNumber(price[i], 4) + "元/" + bo.getItemUnitValueName());
								}
								break;
							}
						}
					}else{
						ChargeSerItemBo bo = m.get(itemNo[0]);
						if(bo != null){
							List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
							prodTwo.put("pricingCombine", pricingCombine2);
							Map<String,Object> pricing2 = Maps.newHashMap();
							pricingCombine2.add(pricing2);
							pricing2.put("pricingSectName", "" + FormatUtil.formatNumber(price[0],4) + "元/" + bo.getItemUnitValueName());
						}
					}
				}
			}
		}else{//远程计费
			Map<String,Object> prodOne = Maps.newHashMap();
			prodBillDet.add(prodOne);
			prodOne.put("prodId", cbrBo.getChcNo());
			prodOne.put("prodName", "充电费");
			prodOne.put("billDetNum", cbrBo.gettPq());
			prodOne.put("billDetAmt", cbrBo.gettAmt());
			List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
			prodOne.put("pricingCombine", pricingCombine);
			String details = "";
			String beginStr = null;
			String endStr = null;
			if(cbrList != null && cbrList.size() > 0){
				for(ChargeBillingRltPeriodsBo prerod : cbrList){
					details += prerod.getBeginTime() + "~" + prerod.getEndTime() + "  " + FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
					if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
						beginStr = prerod.getPrice();
					}
					if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
						endStr = prerod.getPrice();
					}
				}
			}else{
				details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
				if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
					beginStr = cbrBo.getChargePrice();
				}
				if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
					endStr = cbrBo.getChargePrice();
				}
			}
			Map<String,Object> pricing = Maps.newHashMap();
			pricingCombine.add(pricing);
			LOGGER.debug(">>>>>>>>>>>>>>>>>计费充电费：beginStr:"+beginStr+",endStr:"+endStr);
			if(!cbRltUtil.isEq(beginStr, endStr))
				pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
			else
				pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
			pricing.put("pricingSectDesc", details);
			//获取服务费用项
			if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
				ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
				csCondition.setItemNo(cbrBo.getAttachItemNos());
				List<ChargeSerItemBo> csList = chargeSerItemDao.queryChargeSerItems(csCondition);
				if(csList != null){
					Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
					for(ChargeSerItemBo csi:csList){
						m.put(csi.getItemNo(), csi);
					}
					String[] itemNo = cbrBo.getAttachItemNos().split(",");
					String[] price = cbrBo.getAttachItemPrices().split(",");
					String[] nums = cbrBo.getAttachItemNums().split(",");
					String[] amts = cbrBo.getAttachItemAmts().split(",");
					for(int i = 0;i < itemNo.length;i ++){
						ChargeSerItemBo bo = m.get(itemNo[i]);
						if(bo != null){
							Map<String,Object> prodTwo = Maps.newHashMap();
							prodBillDet.add(prodTwo);
							prodTwo.put("prodId", bo.getItemNo());
							prodTwo.put("prodName", bo.getItemName());
							prodTwo.put("billDetNum", nums[i]);
							prodTwo.put("billDetAmt", amts[i]);
							prodTwo.put("price", price[i]);
							List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
							prodTwo.put("pricingCombine", pricingCombine2);
							Map<String,Object> pricing2 = Maps.newHashMap();
							pricingCombine2.add(pricing2);
							pricing2.put("pricingSectName", "" + FormatUtil.formatNumber(price[i],4) + "元/" + bo.getItemUnitValueName());
						}
					}
				}
			}
		}
		return returnMap;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2017-05-09
	 * @description 4充电计费 – 预收/计费版本 + 结算
	 */
	public Map<String, Object> payInAdvanceAndSettlement(Map<String, Object> inMap)throws Exception{
		Map<String, Object> param = Maps.newHashMap();
		param.put("appNo", inMap.get("orderNo"));
		param.put("stationId", inMap.get("stationId"));
		if (StringUtil.isNotBlank(inMap.get("pileId"))){
			param.put("pileId", inMap.get("pileId"));
		}
		param.put("custId", inMap.get("custId"));
		param.put("pCustId", inMap.get("pCustId"));
		param.put("groupId", inMap.get("groupId"));
		//operType=2 已生成过计费版本 不需要再次生成
		if(!"2".equals(inMap.get("operType"))){
			chargeBillingRltService.createChargeOrderVersionForOrder(param);//生成计费版本
		}
		return settlement(inMap);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2017-05-03
	 * @description 下发充电计费模型
	 */
	public void issuredStationOrPile(String stationId,String pileNo,String chcNo)throws Exception{
		if (StringUtil.isBlank(chcNo)){
			ChargeBillingConfBo conf = chargeBillingRltService.obtainChargeBillingConf(stationId,null,null,null,null,null);
			if (conf != null){
				chcNo = conf.getChcNo();
			}
		}
		if(StringUtil.isBlank(chcNo)){
			throw new RuntimeException("找不到有效的计费配置");
		}
		String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
		if("toMoreStation".equals(chargeVesion)){//新版计费
			if(StringUtils.isEmpty(pileNo)) {//下发到站
				xpStationSendService.issuredStation(chcNo, stationId,null);
			}else{//下发到桩
				xpPileSendService.issuredPile(chcNo, pileNo, stationId);
			}
		}else{
			if(StringUtils.isEmpty(pileNo)) {//下发到站
				chcbillSendService.issuredStation(chcNo,null);
			}else{ //下发到桩
				chcbillSendService.issuredPile(chcNo,pileNo,null);
			}
		}
	}

	@Override
	public void issuredStationBillSend(String stationId, String pileNo) {
		if (StringUtil.isEmpty(stationId) || StringUtil.isEmpty(pileNo)){
			LOGGER.debug("下发计费参数为空=========issuredStationBillSend =>stationId:{},pileNo:{}",stationId,pileNo);
			return;
		}
		String submitOrderSendBill = sysParamService.getSysParamsValues("submitOrderSendBill");
		if (PublicConstants.YN.TRUE.equals(submitOrderSendBill)){
			ChargeBillingConfBo conf = chargeBillingRltService.obtainChargeBillingConf(stationId,null,null,null,null,null);
			if (conf != null){
				String chcNo = conf.getChcNo();
				LOGGER.debug("下发计费=========issuredStationBillSend =>chcNo:{},stationId:{},pileNo:{}",chcNo,stationId,pileNo);
				String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
				if("toMoreStation".equals(chargeVesion)){//新版计费
					xpStationSendService.issuredStationBillToPile(chcNo, stationId,pileNo);
				}
			}
		}
	}

	@Override
	public List<BillRltPeriodsBo> getChargeBillRltPeriod(Map map) {
		//判断是否开启分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.getChargeBillRltPeriod(map);
		}

		//20220513返回数据过滤电量为0的数据
		List<BillRltPeriodsBo> result = new ArrayList<>();

		//查询订单分时结果
		List<BillRltPeriodsBo> chargeBillRltPeriodList = chargeBillingRltPeriodsDao.getChargeBillRltPeriod(map);
		if(chargeBillRltPeriodList != null && chargeBillRltPeriodList.size()>0){
			//查询订单对应计费模板
			List<Map> chargeBillRltModelList = peakService.getChargeBillRltModel(map);
			if (chargeBillRltModelList != null && chargeBillRltModelList.size() > 0) {
				chargeBillRltPeriodList = chargeBillingRltPeriodsDao.getChargeBillRltPeriod(map);
				//0201标准 0202分时
				if(chargeBillRltPeriodList != null && chargeBillRltPeriodList.size()>0){
					if ("0202".equals(chargeBillRltModelList.get(0).get("chargeMode"))) {

						for (BillRltPeriodsBo billRltPeriodsBo : chargeBillRltPeriodList) {
							for (Map chargeBillRltModel : chargeBillRltModelList) {
								//金额为空
								if (billRltPeriodsBo.getTimeFlag().equals(chargeBillRltModel.get("timeFlag"))
										&& MathUtils.compareTo(BigDecimal.ZERO.toPlainString(), billRltPeriodsBo.getPq()) < 0) {
									String periodPrice = StringUtil.nullToString(chargeBillRltModel.get("periodPrice"));
									String times = StringUtil.nullToString(chargeBillRltModel.get("times"));
									if (!StringUtil.isNotBlank(billRltPeriodsBo.getPrice())) {
										billRltPeriodsBo.setPrice(periodPrice);
									}
									if (!StringUtil.isNotBlank(billRltPeriodsBo.getAmt())) {
										if (billRltPeriodsBo.getPq() != null) {
											billRltPeriodsBo.setAmt(MathUtils.multiply(periodPrice, billRltPeriodsBo.getPq()));
										}
									}
									billRltPeriodsBo.setTimes(times);
									//如果电量不为空则添加
									result.add(billRltPeriodsBo);
									break;
								}
							}
						}
					} else {
						for (BillRltPeriodsBo billRltPeriodsBo : chargeBillRltPeriodList) {
							String price = StringUtil.nullToString(chargeBillRltModelList.get(0).get("chargePrice"));
							if (!StringUtil.isNotBlank(billRltPeriodsBo.getPrice())) {
								billRltPeriodsBo.setPrice(price);
							}
							if (billRltPeriodsBo.getPq() != null) {
								billRltPeriodsBo.setAmt(MathUtils.multiply(price,billRltPeriodsBo.getPq()));
							}
						}
						return chargeBillRltPeriodList;
					}
				}
			}
		}

		return result;
	}

	/**
	 * @param orderNoList
	 * @Description: 查询结算分段计费明细(简单单表查询)
	 * @Author: hdf
	 * @Time: 2022/07/11 15:09
	 */
	@Override
	public List<BillRltPeriodsBo> getChargeBillRltPeriodByOrder(Map map) {
		List<BillRltPeriodsBo> dataList = null;
		List<String> orderNoList = map.get("orderNoList") == null?new ArrayList<String>(): (List<String>) map.get("orderNoList");
		String orderNo = MapUtils.getValue(map,"orderNo");
		if(CollectionUtils.isNotEmpty(orderNoList) ||  StringUtil.isNotBlank(orderNo)){
			dataList = chargeBillingRltPeriodsDao.getChargeBillRltPeriodByOrder(map);
		}
		return CollectionUtils.isNotEmpty(dataList)?dataList:new ArrayList<BillRltPeriodsBo>();
	}

	/**
	 *@Description: 查询站点下发的计费模型
	 *@Author: laibihui
	 *@Time: 2017/9/26 10:09
	 */
	@Override
	public Map<String, Object> getStationChargePeriods(Map map) {
		if (!map.containsKey("place")) {
			map.put("place", 2);
		}
		List<Map<String, Object>> chargePeriods = new ArrayList<>();
		String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
		String isAssistPileBilling=sysParamService.getSysParamsValues("isAssistPileBilling");
		if("toMoreStation".equals(chargeVesion)) {//新版计费
			if (StringUtil.isNotBlank(map.get("pileId")) && PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
				// 桩计费
				chargePeriods = chargeBillRpcDao.getPileStationChargePeriods(map);
			}
			if (CollectionUtils.isEmpty(chargePeriods)){
				// 站计费
				chargePeriods = peakService.getNewStationChargePeriods(map);
			}
		}else{
			chargePeriods = chargeBillRpcDao.getStationChargePeriods(map);
		}

		Map<String,Object> result = Maps.newHashMap();
		result.put("chargePeriods",chargePeriods);
		return result;
	}

	/**
	 * @param inMap
	 * @description 获取服务费项维护
	 * <AUTHOR>
	 * @create 2017-10-23 15:04:36
	 */
	public List<Map<String,Object>> getAppendCharItem(Map<String, Object> inMap){
		inMap.put("pBe","02");
		inMap.put("itemType","01");
		inMap.put("itemStatus","1");
		return chargeBillRpcDao.getAppendCharItem(inMap);
	}

	/**
	 * 获取计费配置
	 * @param inMap ====={stationIds:[]}
	 * @return
	 */
	public List<Map> queryChargeBillingConfs(Map<String, Object> inMap) {
		List<Map> chargeConfs = getChargeConfs(inMap);
		//获取站点计费最后下发时间
		if (inMap.containsKey("stationIds") && null != inMap.get("stationIds")){
			List<String> stationList = (List<String>)inMap.get("stationIds");
			if(chargeConfs != null && chargeConfs.size() > 0) {
				Map<String,Object> map = chargeConfs.get(0);
				ChcbillSendBo sendBo =
						chcbillSendService.querySendLongByStationIdAndChcNo(StringUtil.nullToString(stationList.get(0)),
						StringUtil.nullToString(map.get("CHC_NO")));
				if (null != sendBo){
					chargeConfs.get(0).put("sendTime", sendBo.getSendTime());
				}
			}
		}
		return chargeConfs;
	}

	/**
	 * 新增站点计费配置
	 * @param inMap==={stationId:"12344",chcNo:"12345",orgCode:"112344"}
	 */
	public void insertChargeBilling(Map<String, Object> inMap) {
		LOGGER.info("站桩计费下发入参：{}", JsonUtil.obj2Json(inMap));
		String isAssistPileBilling = sysParamService.getSysParamsValues("isAssistPileBilling");
		if (PublicConstants.YN.TRUE.equals(isAssistPileBilling) && AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(inMap,"billingConfig"))){
			if (StringUtil.isNotEmpty(inMap.get("pileBillList"))){
				chargeBillRpcDao.delPileChargeBilling(inMap);
				chargeBillRpcDao.insertPileChargeBilling(inMap);//再新增桩计费
			}
		}else{
			chargeBillRpcDao.delChargeBilling(inMap);
			chargeBillRpcDao.insertChargeBilling(inMap);//再新增站计费
		}
	}

	/**
	 * 修改站点计费配置
	 * @param inMap==={stationId:"12344",chcNo:"12345",orgCode:"112344"}
	 */
	public void updateChargeBilling(Map<String, Object> inMap) {
		chargeBillRpcDao.updateChargeBilling(inMap);
	}

	/**
	 * 删除站点计费配置
	 * @param inMap==={stationId:"12344"}
	 */
	public void delChargeBilling(Map<String, Object> inMap) {
		String type = MapUtils.getValue(inMap,"type");
		if ("pile".equals(type)){
			chargeBillRpcDao.delPileChargeBilling(inMap);
		}else{
			chargeBillRpcDao.delChargeBilling(inMap);
			chargeBillRpcDao.delPileChargeBilling(inMap);
		}
	}

	/**
	 * 查询计费描述
	 * @param inMap
	 * @return
	 * @throws Exception
	 */
	public List<Map<String, Object>> getNewElecAmtRemark(Map<String, Object> inMap) throws Exception {
		//判断是否开启分时付费模式
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.getNewElecAmtRemark(inMap);
		}

		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		String  chcNoCd = StringUtil.nullToString(inMap.get("chcNo"));
		/*	inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));*/
		inMap.put("chcNo", chcNoCd);
		String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
		//1、查询计费配置
		List<Map> confs = chargeBillRpcDao.queryChargeBillingConfs(inMap);
		if(confs != null && confs.size() > 0) {
			StringBuilder chcNoSb = new StringBuilder();
			StringBuilder itemNoSb = new StringBuilder();
			List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
			Map conf = confs.get(0);
			String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
			String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
			stationConfs.add(conf);
			chcNoSb.append(chcNo).append(",");
			itemNoSb.append(itemNo).append(",");

			//2、查询充电分时设置
			String chcNos = chcNoSb.toString();
			if (!StringUtils.isEmpty(chcNos)) {
				chcNos = chcNos.substring(0, chcNos.length() - 1);
				List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
				List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
				MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
			}
			//3、查询服务费用
			Map<String, Object> itemMap = null;
			if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
				String itemNos = itemNoSb.toString();
				if (!StringUtils.isEmpty(itemNos)) {
					itemNos = itemNos.substring(0, itemNos.length() - 1);
					ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
					bo.setItemNo(itemNos);
					List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
					if (csItems != null && csItems.size() > 0) {
						itemMap = new HashMap<String, Object>();
						for (ChargeSerItemBo csBo : csItems)
							itemMap.put(csBo.getItemNo(), csBo);
					}
				}
			}
			//4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
			for(Map sconf : stationConfs){
				Map<String,Object> stationCharge = Maps.newHashMap();
				stationCharge.put("eftDate",sconf.get("EFT_DATE"));
				stationCharges.add(stationCharge);
				Double price = null;
				String details = "";
				//充电费
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
					price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
					details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
				}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
					List<Map> periods = (List<Map>)sconf.get("periods");
					if(periods != null){
						double periodPrice = 0.0;
						for(Map prerod : periods){
							details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") +" "+ FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2) + "元/度;";
							double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
							if("toMoreStation".equals(chargeVesion)){
								String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
								String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
								if(isBetweenDate(beginTime,endTime)){
									periodPrice = p;
								}
							}else{
								if(p > periodPrice){
									periodPrice = p;
								}
							}
						}
						price = periodPrice;
					}
				}
				stationCharge.put("elecAmt", FormatUtil.formatNumber(String.valueOf(price),4,2) +  "元/度");
				stationCharge.put("elecAmtRemark", details);
//					LOGGER.debug("充电费 price：" + price);
				//服务费
				if(itemMap != null){
					double servicePrice = 0.0;
					String unit = "度";
					String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
					String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
					if(!StringUtils.isEmpty(itemNos)){
						String[] iNos = itemNos.split(",");
						String[] iPrices = itemPrices.split(",");
						for(int i = 0;i < iNos.length ; i ++){
							if(itemMap.get(iNos[i]) != null) {
								ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
								if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
									double iprice = Double.parseDouble(iPrices[i]);
									servicePrice = iprice;
									unit = csBo.getItemUnitValueName();
								}
								if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
									double iprice = Double.parseDouble(iPrices[i]);
									if (iprice > servicePrice) {
										servicePrice = iprice;
										unit = csBo.getItemUnitValueName();
									}
								}
							}
						}
					}
					stationCharge.put("servicePrice", FormatUtil.formatNumber(String.valueOf(servicePrice),4,2) +"元/"+unit);
				}
			}
		}
		return stationCharges;
	}

	public List<Map> queryChargeBillingList(Map<String, Object> inMap) {
		return chargeBillRpcDao.queryChargeBillingConfs(inMap);
	}

	@Override
	public Map queryPileBillingList(String pileId) {
		return chargeBillRpcDao.queryPileBillingList(pileId);
	}

	/**
	 * 获取计费配置
	 * @param inMap
	 * @return
	 */
	private List<Map> getChargeConfs(Map<String, Object> inMap) {
		List<Map> confs = new ArrayList<>();
		String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
		String isAssistPileBilling=sysParamService.getSysParamsValues("isAssistPileBilling"); // 是否支持桩计费
		if("toMoreStation".equals(chargeVesion)){//新版计费
			if(inMap.containsKey("groupId") && !CollectionUtils.isNotEmpty(confs)){//大客户计费
				confs = chargeBillRpcDao.queryCustGroupChargeBilling(inMap);
			}
			if ((inMap.containsKey("pileId") || inMap.containsKey("pileIds")) && PublicConstants.YN.TRUE.equals(isAssistPileBilling) && !CollectionUtils.isNotEmpty(confs)){
				confs = chargeBillRpcDao.queryNewChargePileBillingConfs(inMap); // 桩计费
			}else if(!CollectionUtils.isNotEmpty(confs)){
				confs=chargeBillRpcDao.queryNewChargeBillingConfs(inMap);//站点计费
			}
		}else{
			if(!CollectionUtils.isNotEmpty(confs)){
				confs=chargeBillRpcDao.queryChargeBillingConfs(inMap);
			}
		}
		return  confs;
	}

	/**
	 * @param inMap
	 * @description 查询当前时间 充电费+服务费/充电费分时列表+其他费列表
	 * <AUTHOR>
	 * @create 2018-05-21 17:35:04
	 */
	public List<Map<String, Object>> getCurChargePriceRemark(Map<String,Object> inMap) throws Exception{
		//判断是否开启分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.getCurChargePriceRemark(inMap);
		}

		List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		if(stationList != null || inMap.get("stationId") != null){
			inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
			String stationIds = StringUtil.nullForString(inMap.get("stationId"));
			if(StringUtil.isNotBlank(stationIds)){
				inMap.put("stationIds", stationIds.split(","));
			}
			//1、查询计费配置
			List<Map> confs = getChargeConfs(inMap);
			if(confs != null && confs.size() > 0) {
				String isAssistPileBilling = sysParamService.getSysParamsValues("isAssistPileBilling");
				if (AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(inMap,"billingConfig")) && PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
					stationCharges = pileChargeBillingMethod(confs,inMap);
				} else {
					stationCharges = stationChargeBillingMethod(confs,inMap);
				}
			}
		}
		return stationCharges;
	}

	/**
	 * @param confs
	 * @param inMap
	 * @description 桩计费计算
	 * <AUTHOR>
	 * @create 2020-10-13 10:42:43
	 */
	private List<Map<String, Object>> pileChargeBillingMethod(List<Map> confs,Map<String,Object> inMap) throws Exception{
		List<Map<String,Object>> pileCharges = new LinkedList<Map<String,Object>>();
		List<Map<String, Object>> pileConfs = new LinkedList<Map<String, Object>>();
		List<Map<String, Object>> pileBillList = new LinkedList<Map<String, Object>>();
		//亨通电站计费展示
		List<Map<String, Object>> allPileBillList = new LinkedList<Map<String, Object>>();
		String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
		String currentPileId = null;
		StringBuilder chcNoSb = new StringBuilder();
		StringBuilder itemNoSb = new StringBuilder();
		for (Map conf : confs) {
			String pileId = cbRltUtil.returnNotNull(conf.get("PILE_ID"));
			String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
			String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
			if (currentPileId == null || !currentPileId.equals(pileId)) {//第一个pileId
				currentPileId = pileId;
				pileConfs.add(conf);
				chcNoSb.append(chcNo).append(",");
				itemNoSb.append(itemNo).append(",");
			} else {//如果不是第一个，将多余的充电计费配置状态改为无效
				if (!"".equals(chcNo)) {
					ChargeBillingConfBo cbc = new ChargeBillingConfBo();
					cbc.setChcNo(chcNo);
					cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
					chargeBillingConfDao.updateChargeBillingConf(cbc);
				}
			}
		}
		//2、查询充电分时设置
		String chcNos = chcNoSb.toString();
		if (!StringUtils.isEmpty(chcNos)) {
			chcNos = chcNos.substring(0, chcNos.length() - 1);
			List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
			List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
			MergeUtil.mergeList(pileConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
		}
		//3、查询服务费用
		Map<String, Object> itemMap = null;
		if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
			String itemNos = itemNoSb.toString();
			if (!StringUtils.isEmpty(itemNos)) {
				itemNos = itemNos.substring(0, itemNos.length() - 1);
				ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
				bo.setItemNo(itemNos);
				List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
				if (csItems != null && csItems.size() > 0) {
					itemMap = new HashMap<String, Object>();
					for (ChargeSerItemBo csBo : csItems)
						itemMap.put(csBo.getItemNo(), csBo);
				}
			}
		}
		//4、组合数据
		Map<String,Object> pileCharge = Maps.newHashMap();
		Double minPrice = null;
		Double maxPrice = null;

		Map<String,Object> billingTagMap = Maps.newHashMap();
		List<CodeBO> billingTagList = codeService.getStandardCodes(BizConstants.ProductType.BILLING_TAG,null);
		if (CollectionUtils.isNotEmpty(billingTagList)){
			for (CodeBO codeBO : billingTagList) {
				billingTagMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}

		List<String> chcNoList = new ArrayList<>();
		List chargeIteamList = new ArrayList();
		for(Map sconf : pileConfs){
			String chcNo = MapUtils.getValue(sconf,"CHC_NO");
			Map<String,Object> pileBillMap = Maps.newHashMap();
			List<Map<String,Object>> chargeBillDetailList = new ArrayList();
			Double price = null;
			if (StringUtil.isNotBlank(MapUtils.getValue(sconf,"BILLING_TAG"))){
				pileBillMap.put("billingTag",MapUtils.getValue(sconf,"BILLING_TAG"));
				pileBillMap.put("billingTagName",billingTagMap.get(MapUtils.getValue(sconf,"BILLING_TAG")));
			}
			//充电费
			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
				price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
				Map<String,Object> detailMap = new HashMap();
				//2022523 添加返回桩编号
				detailMap.put("pileId", sconf.get("PILE_ID"));
				detailMap.put("endValue","24:00");
				detailMap.put("startValue","00:00");
				detailMap.put("chargePriceValue",FormatUtil.formatNumber(sconf.get("CHARGE_PRICE") != null ? String.valueOf(sconf.get("CHARGE_PRICE")):"",4,4));
				detailMap.put("chargePrice",detailMap.get("chargePriceValue") + "元/度");
				chargeBillDetailList.add(detailMap);
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
				List<Map> periods = (List<Map>)sconf.get("periods");
				if(periods != null){
					double periodPrice = 0.0;
					for(Map prerod : periods){
						Map<String,Object> detailMap = new HashMap();
						double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
						String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
						String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
						if(isBetweenDate(beginTime,endTime)){
							periodPrice = p;
							detailMap.put("isCurrent","true");
						}
						//2022523 添加返回桩编号
						detailMap.put("pileId", sconf.get("PILE_ID"));
						detailMap.put("startValue",prerod.get("BEGIN_TIME"));
						detailMap.put("endValue",prerod.get("END_TIME"));
						detailMap.put("timeFlag",prerod.get("TIME_FLAG"));
						detailMap.put("chargePriceValue",FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2));
						detailMap.put("chargePrice",FormatUtil.formatNumber(String.valueOf(detailMap.get("chargePriceValue")),4,4) + "元/度");
						chargeBillDetailList.add(detailMap);

						Map chargeIteam = new HashMap();
						chargeIteam.put("startValue",prerod.get("BEGIN_TIME"));
						chargeIteam.put("endValue",prerod.get("END_TIME"));
						chargeIteam.put("timeFlag",prerod.get("TIME_FLAG"));
						chargeIteam.put("chargePriceValue",FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2));
						chargeIteam.put("chargePrice",chargeIteam.get("chargePriceValue") + "元/度");
						chargeIteamList.add(chargeIteam);
					}
					price = periodPrice;
				}
			}
			//服务费
			if(itemMap != null){
				double servicePrice = 0.0;
				String unit ="度";
				String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
				String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
				if(!StringUtils.isEmpty(itemNos)){
					String[] iNos = itemNos.split(",");
					String[] iPrices = itemPrices.split(",");
					for(int i = 0;i < iNos.length ; i ++){
						if(itemMap.get(iNos[i]) != null) {
							ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
							if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
								double iprice = Double.parseDouble(iPrices[i]);
								servicePrice = iprice;
								unit = csBo.getItemUnitValueName();
							}
							if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
								double iprice = Double.parseDouble(iPrices[i]);
								if (iprice > servicePrice) {
									servicePrice = iprice;
									unit = csBo.getItemUnitValueName();
								}
							}
						}
					}
				}
				price += servicePrice;
				if (CollectionUtils.isNotEmpty(chargeBillDetailList)){
					for (Map<String, Object> detailMap : chargeBillDetailList) {
						detailMap.put("servicePriceValue",servicePrice);
						detailMap.put("servicePrice",FormatUtil.formatNumber(String.valueOf(servicePrice),4,4) + "元/度");
						detailMap.put("price",FormatUtil.formatNumber(MathUtils.add(String.valueOf(servicePrice),StringUtil.getString(detailMap.get("chargePriceValue"))),4,4) + "元/度");
					}
				}
			} else {
				double servicePrice = 0.0;
				if (CollectionUtils.isNotEmpty(chargeBillDetailList)){
					for (Map<String, Object> detailMap : chargeBillDetailList) {
						detailMap.put("servicePriceValue",servicePrice);
						detailMap.put("servicePrice",FormatUtil.formatNumber(String.valueOf(servicePrice),4,4) + "元/度");
						detailMap.put("price",FormatUtil.formatNumber(MathUtils.add(String.valueOf(servicePrice),StringUtil.getString(detailMap.get("chargePriceValue"))),4,4) + "元/度");
					}
				}
			}
			pileBillMap.put("chargeBillDetailList",chargeBillDetailList);
			//所有桩计费,用于亨通计算桩计费
			allPileBillList.add(pileBillMap);
			if (!chcNoList.contains(chcNo)){
				chcNoList.add(chcNo);
				pileBillList.add(pileBillMap);
			}
			// 计算最小值
			if (minPrice != null){
				if (MathUtils.compareTo(StringUtil.getString(price),StringUtil.getString(minPrice)) < 0){
					minPrice = price;
				}
			} else {
				minPrice = price;
			}

			// 计算最大值
			if (minPrice != null){
				if (MathUtils.compareTo(StringUtil.getString(price),StringUtil.getString(maxPrice)) > 0){
					maxPrice = price;
				}
			} else {
				maxPrice = price;
			}
		}

		if (StringUtil.isNotBlank(MapUtils.getValue(inMap,"pileId"))){
			pileCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(minPrice),4,4) +  "元/度");
		} else {
			pileCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(minPrice),4,4) +  "元/度起");
		}
		pileCharge.put("charge-item-list", chargeIteamList);
		pileCharge.put("priceRemarkNum", FormatUtil.formatNumber(String.valueOf(minPrice),4,4));
		pileCharge.put("pileBillList", pileBillList);
		pileCharge.put("allPileBillList", allPileBillList);
		pileCharges.add(pileCharge);
		return pileCharges;
	}

	/**
	 * @param confs
	 * @param inMap
	 * @description 站点计费计算
	 * <AUTHOR>
	 * @create 2020-10-13 10:42:24
	 */
	private List<Map<String, Object>> stationChargeBillingMethod(List<Map> confs,Map<String,Object> inMap) throws Exception{
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		List<Map<String, Object>> pileBillList = new LinkedList<Map<String, Object>>();
		String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
		String currentStationId = null;
		StringBuilder chcNoSb = new StringBuilder();
		StringBuilder itemNoSb = new StringBuilder();
		List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
		for (Map conf : confs) {
			String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
			String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
			String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
			if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
				currentStationId = stationId;
				stationConfs.add(conf);
				chcNoSb.append(chcNo).append(",");
				itemNoSb.append(itemNo).append(",");
			} else {//如果不是第一个，将多余的充电计费配置状态改为无效
				if (!"".equals(chcNo)) {
					ChargeBillingConfBo cbc = new ChargeBillingConfBo();
					cbc.setChcNo(chcNo);
					cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
					chargeBillingConfDao.updateChargeBillingConf(cbc);
				}
			}
		}
		//2、查询充电分时设置
		String chcNos = chcNoSb.toString();
		if (!StringUtils.isEmpty(chcNos)) {
			chcNos = chcNos.substring(0, chcNos.length() - 1);
			List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
			List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
			MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
		}
		//3、查询服务费用
		Map<String, Object> itemMap = null;
		if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
			String itemNos = itemNoSb.toString();
			if (!StringUtils.isEmpty(itemNos)) {
				itemNos = itemNos.substring(0, itemNos.length() - 1);
				ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
				bo.setItemNo(itemNos);
				List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
				if (csItems != null && csItems.size() > 0) {
					itemMap = new HashMap<String, Object>();
					for (ChargeSerItemBo csBo : csItems)
						itemMap.put(csBo.getItemNo(), csBo);
				}
			}
		}
		//4、组合数据
		Map<String,Object> billingTagMap = Maps.newHashMap();
		List<CodeBO> billingTagList = codeService.getStandardCodes(BizConstants.ProductType.BILLING_TAG,null);
		if (CollectionUtils.isNotEmpty(billingTagList)){
			for (CodeBO codeBO : billingTagList) {
				billingTagMap.put(codeBO.getCodeValue(),codeBO.getCodeName());
			}
		}
		List<String> chcNoList = new ArrayList<>();
		for(Map sconf : stationConfs){
			String chcNo = MapUtils.getValue(sconf,"CHC_NO");
			Map<String,Object> pileBillMap = Maps.newHashMap();
			List<Map<String,Object>> chargeBillDetailList = new ArrayList();
			if (StringUtil.isNotBlank(MapUtils.getValue(sconf,"BILLING_TAG"))){
				pileBillMap.put("billingTag",MapUtils.getValue(sconf,"BILLING_TAG"));
				pileBillMap.put("billingTagName",billingTagMap.get(MapUtils.getValue(sconf,"BILLING_TAG")));
			}
			List chargeIteamList = new ArrayList();
			List inceChargeItem = new ArrayList();
			Map<String,Object> stationCharge = Maps.newHashMap();
			stationCharge.put("stationId", sconf.get("STATION_ID"));
			stationCharge.put("billCtlMode", sconf.get("BILL_CTL_MODE"));
			stationCharge.put("chargeMode", sconf.get("CHARGE_MODE"));
			stationCharge.put("gathErDataType", sconf.get("GATHER_DATA_TYPE"));
			Double price = null;
			String details = "";
			//充电费
			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
				price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
				details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
				Map<String,Object> detailMap = new HashMap();
				detailMap.put("endValue","24:00");
				detailMap.put("startValue","00:00");
				detailMap.put("chargePriceValue",FormatUtil.formatNumber(sconf.get("CHARGE_PRICE") != null ? String.valueOf(sconf.get("CHARGE_PRICE")):"",4,4));
				detailMap.put("chargePrice",detailMap.get("chargePriceValue") + "元/度");
				chargeBillDetailList.add(detailMap);

				Map chargeIteam = new HashMap();
				chargeIteam.put("startValue","00:00");
				chargeIteam.put("endValue","24:00");
				chargeIteam.put("chargePriceValue",FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2));
				chargeIteam.put("chargePrice",chargeIteam.get("chargePriceValue") + "元/度");
				chargeIteamList.add(chargeIteam);
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
				List<Map> periods = (List<Map>)sconf.get("periods");
				if(periods != null){
					double periodPrice = 0.0;
					for(Map prerod : periods){
						Map<String,Object> detailMap = new HashMap();
						details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") +" "+ FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2) + "元/度;";
						double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
						String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
						String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
						if(isBetweenDate(beginTime,endTime)){
							periodPrice = p;
							detailMap.put("isCurrent","true");
						}
						detailMap.put("startValue",prerod.get("BEGIN_TIME"));
						detailMap.put("endValue",prerod.get("END_TIME"));
						detailMap.put("timeFlag",prerod.get("TIME_FLAG"));
						detailMap.put("chargePriceValue",FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,4));
						detailMap.put("chargePrice",detailMap.get("chargePriceValue") + "元/度");
						chargeBillDetailList.add(detailMap);

						Map chargeIteam = new HashMap();
						chargeIteam.put("startValue",prerod.get("BEGIN_TIME"));
						chargeIteam.put("endValue",prerod.get("END_TIME"));
						chargeIteam.put("timeFlag",prerod.get("TIME_FLAG"));
						chargeIteam.put("chargePriceValue",FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2));
						chargeIteam.put("chargePrice",chargeIteam.get("chargePriceValue") + "元/度");
						chargeIteamList.add(chargeIteam);

					}
					price = periodPrice;
				}
			}
			stationCharge.put("chargeAmt", FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
			stationCharge.put("chargeAmtNum", FormatUtil.formatNumber(String.valueOf(price),4,4));
			stationCharge.put("elecAmtRemark", details);

			//服务费
			if(itemMap != null){
				double servicePrice = 0.0;
				String unit ="度";
				String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
				String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
				if(!StringUtils.isEmpty(itemNos)){
					String[] iNos = itemNos.split(",");
					String[] iPrices = itemPrices.split(",");
					for(int i = 0;i < iNos.length ; i ++){
						if(itemMap.get(iNos[i]) != null) {
							ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
							if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
								double iprice = Double.parseDouble(iPrices[i]);
								servicePrice = iprice;
								unit = csBo.getItemUnitValueName();
							}
							if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
								double iprice = Double.parseDouble(iPrices[i]);
								if (iprice > servicePrice) {
									servicePrice = iprice;
									unit = csBo.getItemUnitValueName();
								}
							}

							Map<String, Object> prodTwo = Maps.newHashMap();
							prodTwo.put("itemCode", csBo.getItemNo());
							prodTwo.put("itemName", csBo.getItemName());
							prodTwo.put("itemPrice", iPrices[i]);
							prodTwo.put("itemUnitName", csBo.getItemUnitValueName());
							prodTwo.put("itemDesc", csBo.getRemarks());
							inceChargeItem.add(prodTwo);
						}
					}
				}
				price += servicePrice;

				if (CollectionUtils.isNotEmpty(chargeBillDetailList)){
					for (Map<String, Object> detailMap : chargeBillDetailList) {
						detailMap.put("servicePriceValue",servicePrice);
						detailMap.put("servicePrice",FormatUtil.formatNumber(String.valueOf(servicePrice),4,4) + "元/度");
						detailMap.put("price",FormatUtil.formatNumber(MathUtils.add(String.valueOf(servicePrice),StringUtil.getString(detailMap.get("chargePriceValue"))),4,4) + "元/度");
					}
				}

				stationCharge.put("serviceAmt", FormatUtil.formatNumber(String.valueOf(servicePrice),4,4) +"元/"+unit);
				stationCharge.put("serviceAmtNum", FormatUtil.formatNumber(String.valueOf(servicePrice),4,4));
			} else {
				double servicePrice = 0.0;
				if (CollectionUtils.isNotEmpty(chargeBillDetailList)){
					for (Map<String, Object> detailMap : chargeBillDetailList) {
						detailMap.put("servicePriceValue",servicePrice);
						detailMap.put("servicePrice",FormatUtil.formatNumber(String.valueOf(servicePrice),4,4) + "元/度");
						detailMap.put("price",FormatUtil.formatNumber(MathUtils.add(String.valueOf(servicePrice),StringUtil.getString(detailMap.get("chargePriceValue"))),4,4) + "元/度");
					}
				}
			}

			pileBillMap.put("chargeBillDetailList",chargeBillDetailList);
			if (!chcNoList.contains(chcNo)){
				chcNoList.add(chcNo);
				pileBillList.add(pileBillMap);
			}
			stationCharge.put("pileBillList", pileBillList);
			stationCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
			stationCharge.put("priceRemarkNum", FormatUtil.formatNumber(String.valueOf(price),4,4));
			stationCharge.put("charge-item-list", chargeIteamList);
			stationCharge.put("increment-item-list", inceChargeItem);
			stationCharges.add(stationCharge);
		}
		return stationCharges;
	}

	public List<Map<String, Object>> getXpChargePriceRemark(Map<String, Object> inMap) throws Exception {
		//判断是否开启分时服务费
		if (servicePriceService.getServicePriceSwitch()){
			return servicePriceService.getXpChargePriceRemark(inMap);
		}

		List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
		List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
		if(stationList != null || inMap.get("stationId") != null){
			inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
			String stationIds = StringUtil.nullForString(inMap.get("stationId"));
			if(!StringUtil.isBlank(stationIds)){
				inMap.put("stationIds", stationIds.split(","));
			}
			//1、查询计费配置
			List<Map> confs = getChargeConfs(inMap);
			if(confs != null && confs.size() > 0) {
				String currentStationId = null;
				StringBuilder chcNoSb = new StringBuilder();
				StringBuilder itemNoSb = new StringBuilder();
				List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
				for (Map conf : confs) {
					String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
					String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
					String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
					if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
						currentStationId = stationId;
						stationConfs.add(conf);
						chcNoSb.append(chcNo).append(",");
						itemNoSb.append(itemNo).append(",");
					} else {//如果不是第一个，将多余的充电计费配置状态改为无效
						if (!"".equals(chcNo)) {
							ChargeBillingConfBo cbc = new ChargeBillingConfBo();
							cbc.setChcNo(chcNo);
							cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
							chargeBillingConfDao.updateChargeBillingConf(cbc);
						}
					}
				}
				//2、查询充电分时设置
				String chcNos = chcNoSb.toString();
				if (!StringUtils.isEmpty(chcNos)) {
					chcNos = chcNos.substring(0, chcNos.length() - 1);
					List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
					List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
					MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
				}
				//3、查询服务费用
				Map<String, Object> itemMap = null;
				if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
					String itemNos = itemNoSb.toString();
					if (!StringUtils.isEmpty(itemNos)) {
						itemNos = itemNos.substring(0, itemNos.length() - 1);
						ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
						bo.setItemNo(itemNos);
						List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
						if (csItems != null && csItems.size() > 0) {
							itemMap = new HashMap<String, Object>();
							for (ChargeSerItemBo csBo : csItems)
								itemMap.put(csBo.getItemNo(), csBo);
						}
					}
				}
				//4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
				for(Map sconf : stationConfs){
					List chargeIteamList = new ArrayList();
					List inceChargeItem = new ArrayList();
					Map<String,Object> stationCharge = Maps.newHashMap();
					stationCharge.put("stationId", sconf.get("STATION_ID"));
					Double price = null;
					//充电费
					if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
						price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
						stationCharge.put("chargeAmt", price);
					}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
						List<Map> periods = (List<Map>)sconf.get("periods");
						if(periods != null){
							double periodPrice = 0.0;
							for(Map prerod : periods){
								double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
								String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
								String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
								if(isBetweenDate(beginTime,endTime)){
									periodPrice = p;
								}

								Map chargeIteam = new HashMap();
								chargeIteam.put("startValue",prerod.get("BEGIN_TIME"));
								chargeIteam.put("endValue",prerod.get("END_TIME"));
								chargeIteam.put("timeFlag",prerod.get("TIME_FLAG"));
								chargeIteam.put("chargePrice",FormatUtil.formatNumber(prerod.get("PRICE")!=null?String.valueOf(prerod.get("PRICE")):"",4,2));
								chargeIteamList.add(chargeIteam);

							}
							price = periodPrice;
							stationCharge.put("chargeAmt", FormatUtil.formatNumber(String.valueOf(periodPrice),4,2));
							stationCharge.put("chargeAmtUnit", "元/度");
						}
					}
//					LOGGER.debug("充电费 price：" + price);
					//服务费
					if(itemMap != null){
						double servicePrice = 0.0;
						String unit ="";
						String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
						String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
						if(!StringUtils.isEmpty(itemNos)){
							String[] iNos = itemNos.split(",");
							String[] iPrices = itemPrices.split(",");
							String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
							for(int i = 0;i < iNos.length ; i ++){
								if(itemMap.get(iNos[i]) != null) {
									ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
									if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
										double iprice = Double.parseDouble(iPrices[i]);
										servicePrice = iprice;
										unit = csBo.getItemUnitValueName();
									}
									if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
										double iprice = Double.parseDouble(iPrices[i]);
										if (iprice > servicePrice) {
											servicePrice = iprice;
											unit = csBo.getItemUnitValueName();
										}
									}

									Map<String, Object> prodTwo = Maps.newHashMap();
									prodTwo.put("itemCode", csBo.getItemNo());
									prodTwo.put("itemName", csBo.getItemName());
									prodTwo.put("itemPrice", iPrices[i]);
									prodTwo.put("itemUnitName", csBo.getItemUnitValueName());
									prodTwo.put("itemDesc", csBo.getRemarks());
									inceChargeItem.add(prodTwo);
								}
							}
						}
						price += servicePrice;
						stationCharge.put("serviceAmt", FormatUtil.formatNumber(String.valueOf(servicePrice),4,2));
						stationCharge.put("serviceAmtUnit", unit);

					}
					stationCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
					stationCharge.put("charge-item-list", chargeIteamList);
					stationCharge.put("increment-item-list", inceChargeItem);
					stationCharges.add(stationCharge);
				}
			}
		}
		return stationCharges;
	}

	/**
	 * @param inMap
	 * @description 查询订单充电费用项
	 * <AUTHOR>
	 * @create 2018-08-02 23:20:52
	 */
	public Map<String, Object> qryOrderChargeIteams(Map<String, Object> inMap) throws Exception {
		//1、判断传参是否正确
		String orderNo = cbRltUtil.isNotEmpty(inMap.get("orderNo"), "订单编号不能为空");
		//2、查询该订单的充电计费信息
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
		String[] itemPrices = cbrBo.getAttachItemPrices().split(",");//单价
		String[] itemUnits = cbrBo.getAttachItemUnits().split(",");//单位
		String[] itemNos = cbrBo.getAttachItemNos().split(",");//编号
		String[] freeNums = cbrBo.getFreeNum().split(",");//免费量

		Map<String, Object> rltMap =new HashedMap();
		for(int i = 0; i < itemPrices.length; i ++){
			if(BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(itemNos[i])) {//服务费
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put(BizConstants.PriceCode.SERVICE_PRICE_CODE,iteamMap);
			}
			if("1000002001".equals(itemNos[i])) {//充电前插枪占位费：(插枪时间-降地锁时间-免费时间量)*单价---时间是分钟
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put("1000002001",iteamMap);
			}
			if("1000002002".equals(itemNos[i])) {//充电后占位费：(拔枪时间-结束充电时间-免费时间量)*单价
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put("1000002002",iteamMap);
			}
			if("1000002003".equals(itemNos[i])) {//：充电前占位费:(充电开始时间-插枪时间-免费时间量)*单价
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put("1000002003",iteamMap);
			}
			if("1000002004".equals(itemNos[i])) {//充电后插枪占位费：(升地锁时间-拔枪时间-免费时间量)*单价
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put("1000002004",iteamMap);
			}
			if("1000003000".equals(itemNos[i])) {//低速附加费：（电量-免费量）*单价
				Map<String, Object> iteamMap =new HashedMap();
				iteamMap.put("price",itemPrices[i]);
				iteamMap.put("unit",itemUnits[i]);
				iteamMap.put("iteamNo",itemNos[i]);
				iteamMap.put("freeNum",freeNums[i]);
				rltMap.put("1000003000",iteamMap);
			}
		}
		return rltMap;
	}

	/**
	 * 新增大客户计费配置
	 *
	 * @param inMap ==={groupId:"12344",chcNo:"12345"}
	 */
	@Override
	public void insertCustGroupChargeBilling(Map<String, Object> inMap) {
		chargeBillRpcDao.insertCustGroupChargeBilling(inMap);

	}

	/**
	 * 删除大客户计费配置
	 *
	 * @param inMap ==={groupId:"12344"}
	 */
	@Override
	public void delCustGroupChargeBilling(Map<String, Object> inMap) {
		chargeBillRpcDao.delCustGroupChargeBilling(inMap);
	}

	/**
	 * 获取大客户计费配置
	 *
	 * @return list
	 */
	public List<Map> queryCustGroupChargeBilling(Map<String, Object> inMap) {
		return chargeBillRpcDao.queryCustGroupChargeBilling(inMap);
	}

	/**
	 * @param inMap {orderNo:xxxx}
	 * @description 查询订单计费信息
	 * <AUTHOR>
	 * @create 2019-04-24 21:14:40
	 */
	public Map<String, Object> qryOrderBillInfo(Map<String, Object> inMap) throws Exception {
		Map<String, Object> rltMap = new HashMap<>();
		List<Map<String,String>> periodsList = new ArrayList<>();
		String bilType = StringUtil.nullForString(inMap.get("bilType"));
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(String.valueOf(inMap.get("orderNo")));
		if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//如果是分时计费方式
			List<ChargeBillingRltPeriodsBo> periods = new ArrayList<>();
			if("0".equals(bilType)){//实时计费
				periods= chargeBillingPeriodsDao.queryChargeBillingPeriod(inMap);
			}else{
				periods= chargeBillingRltPeriodsDao.queryChargeBillingRltPeriod(String.valueOf(inMap.get("orderNo")));
			}

			if(periods!=null)
				for(ChargeBillingRltPeriodsBo cpBo : periods){
					Map<String,String> periodMap = new HashMap<>();
					periodMap.put("pq",cpBo.getPq());
					periodMap.put("price",cpBo.getPrice());
					periodMap.put("amt",cpBo.getAmt());
					periodMap.put("beginTime",cpBo.getRelBeginTime());
					periodMap.put("endTime",cpBo.getRelEndTime());
					periodsList.add(periodMap);
				}
		}

		//电费
		List<Map> periods = new ArrayList<>();
		String chcNos = cbrBo.getChcNo();
		if (!StringUtils.isEmpty(chcNos)) {
			periods = peakService.queryChargePeriods(null, chcNos.split(","));
		}

		Double price = 0.0;
		//充电费
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
			price = Double.parseDouble(String.valueOf(cbrBo.getChargePrice()));
		}else {
			if (periods != null) {
				for(Map prerod : periods){
					double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
					String beginTime=StringUtil.nullToString(prerod.get("BEGIN_TIME"));
					String endTime=StringUtil.nullToString(prerod.get("END_TIME"));
					if (isBetweenDate(beginTime, endTime)) {
						price = p;
						break;
					}
				}
			}
		}

		//服务费
		rltMap.put("servicePrice","0");//服务费
		Map<String, Object> itemMap = null;
		String itemNo = cbrBo.getAttachItemNos();
		ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
		bo.setItemNo(itemNo);
		List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
		if (csItems != null && csItems.size() > 0) {
			itemMap = new HashMap<String, Object>();
			for (ChargeSerItemBo csBo : csItems)
				itemMap.put(csBo.getItemNo(), csBo);
		}

		if(itemMap != null){
			double servicePrice = 0.0;
			String itemNos = cbrBo.getAttachItemNos();
			String itemPrices = cbrBo.getAttachItemPrices();
			if(!StringUtils.isEmpty(itemNos)){
				String[] iNos = itemNos.split(",");
				String[] iPrices = itemPrices.split(",");
				String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
				for(int i = 0;i < iNos.length ; i ++){
					if(itemMap.get(iNos[i]) != null) {
						ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
						if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
							double iprice = Double.parseDouble(iPrices[i]);
							servicePrice = iprice;
						}
						if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
							double iprice = Double.parseDouble(iPrices[i]);
							if (iprice > servicePrice) {
								servicePrice = iprice;
							}
						}
					}
				}
				rltMap.put("servicePrice",servicePrice);//服务费
			}

		}

		rltMap.put("chargePrice",price);//电费
		rltMap.put("periods",periodsList);//分时集合
		rltMap.put("billCtlMode",cbrBo.getBillCtlMode());//费控模式，1本地 2远程
		rltMap.put("chargeMode",cbrBo.getChargeMode());////计费模式 01标准 02分时
		return rltMap;
	}

	/**
	 * @param inMap
	 * @description 通过订单查询计费信息
	 * <AUTHOR>
	 * @create 2019-05-16 16:50:01
	 */
	public Boolean qryOrderBillConfFlag(Map<String, Object> inMap){
		int count = chargeBillingRltDao.qryOrderBillConfFlag(inMap);
		if(count > 0){
			return true;
		}
		return false;
	}

	/**
	 * @param stationId
	 * @description
	 * <AUTHOR> 更新站点下面计费的管理单位
	 * @create 2019-05-27 12:20:44
	 */
	@Override
	public void updateChargeBillingOrgCode(Map<String,Object> iMap) {
		chargeBillingRltDao.updateChargeBillingOrgCode(iMap);
	}

	/**
	 * @param begin 10:00
	 * @param end    12:00
	 * @description 判断当前时间是否在时间段范围内
	 * <AUTHOR>
	 * @create 2018-05-16 16:11:28
	 */
	public static boolean isBetweenDate(String beginT, String endT) {
		boolean flag = false;
		SimpleDateFormat df = new SimpleDateFormat("HH:mm");//设置日期格式
		Date nowTime = null;
		Date beginTime = null;
		Date endTime = null;
		try {
			//格式化当前时间格式
			nowTime = df.parse(df.format(new Date()));
			//定义开始时间
			beginTime = df.parse(beginT);
			//定义结束时间
			endTime = df.parse(endT);

			//设置当前时间
			Calendar date = Calendar.getInstance();
			date.setTime(nowTime);
			//设置开始时间
			Calendar begin = Calendar.getInstance();
			begin.setTime(beginTime);
			//设置结束时间
			Calendar end = Calendar.getInstance();
			end.setTime(endTime);
			//处于开始时间之后，和结束时间之前的判断
			if ((date.after(begin) || date.equals(begin)) && date.before(end)) {
				flag = true;
			} else {
				flag = false;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return flag;
	}

	/**
	 * @param inMap
	 * @description 查询有效计费  按照 custId>groupId>stationId
	 * <AUTHOR> (songyuhang搬运)
	 * @create 2019-08-22 17:16:41
	 */
	@Override
	public Map getChargConfig(Map inMap){
		String stationId = StringUtil.getString(inMap.get("stationId"));
		String endEftDate = StringUtil.getString(inMap.get("endEftDate"));
		String custId = StringUtil.getString(inMap.get("custId"));
		String groupId = StringUtil.getString(inMap.get("groupId"));
		String pCustId = StringUtil.getString(inMap.get("pCustId"));
		String pileId = StringUtil.getString(inMap.get("pileId"));
		ChargeBillingConfBo chargeBillingConfBo = chargeBillingRltService.obtainChargeBillingConf(stationId,pileId,endEftDate,custId,groupId,pCustId);
		if (chargeBillingConfBo != null) {
			if (StringUtil.isNotBlank(chargeBillingConfBo.getChcNo())) {
				Map stationPeriods = new HashMap();
				stationPeriods.put("chcNo", chargeBillingConfBo.getChcNo());
				stationPeriods.put("chcCustId", StringUtil.nullToString(chargeBillingConfBo.getCustId()));
				return stationPeriods;
			}
		}
		return null;
	}

	@Override
	public String queryItemMode(Map<String, Object> iMap) {
		String orderNo = cbRltUtil.isNotEmpty(iMap.get("orderNo"), "订单编号不能为空");
		String itemNo = cbRltUtil.isNotEmpty(iMap.get("itemNo"), "费用项编号不能为空");

		//桩在第三方无充电计费信息
		ChargeBillingRltBo chargeBillingRltBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);

		if (chargeBillingRltBo!=null && StringUtil.isNotBlank(chargeBillingRltBo.getItemChargeMode()) && StringUtil.isNotBlank(chargeBillingRltBo.getAttachItemNos())){
			String[] itemNos = chargeBillingRltBo.getAttachItemNos().split(",");
			String[] itemChargeModes = chargeBillingRltBo.getItemChargeMode().split(",");

			for (int i = 0;i < itemNos.length;i++){
				if (itemNo.equals(itemNos[i])){
					return itemChargeModes[i];
				}
			}

		}

		return null;
	}

	@Override
	public List<Map<String, Object>> getChargePeriodsItemsPq(Map inMap) {
		return chargeBillingRltDao.getChargePeriodsItemsPq(inMap);
	}

	@Override
	public List<BillRltPeriodsBo> getChargePeriodsByOrderNo(String orderNo) {
		List<BillRltPeriodsBo> result = new ArrayList<>();
		ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
		if (cbrBo == null){
			return result;
		}
		List<ChargePeriodsBo> chargePeriodsBos = new ArrayList<>();
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
		condition.setChcNo(cbrBo.getChcNo());
		if (peakService.checkOrderIsPeak(orderNo)){
			chargePeriodsBos = peakService.queryPeriodListPeak(condition);
		}else {
			chargePeriodsBos = chargePeriodsDao.queryChargePeriods(condition);
		}

		String servicePriceStr = "";
		//获取服务费用项
		if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
			ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
			csCondition.setItemNo(cbrBo.getAttachItemNos());
			List<ChargeSerItemBo> csList = chargeSerItemDao.queryChargeSerItems(csCondition);
			if(csList != null){
				Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
				for(ChargeSerItemBo csi:csList){
					m.put(csi.getItemNo(), csi);
				}
				String[] itemNo = cbrBo.getAttachItemNos().split(",");
				String[] price = cbrBo.getAttachItemPrices().split(",");
				for(int i = 0;i < itemNo.length;i ++){
					ChargeSerItemBo bo = m.get(itemNo[i]);
					if(bo != null && itemNo[i].equals(ChargeConstant.itemTypeNo.SERVICE)){
						servicePriceStr = price[i];
					}
				}
			}
		}

		List<ChargePeriodsBo> elecTemp = new ArrayList<>();
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
			ChargePeriodsBo bo = new ChargePeriodsBo();
			bo.setBeginTime("00:00");
			bo.setEndTime("24:00");
			bo.setPrice(cbrBo.getChargePrice());
			elecTemp.add(bo);
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())) {//分时计费
			for (ChargePeriodsBo periodsBo : chargePeriodsBos) {
				if (!ChargeConstant.itemTypeNo.SERVICE.equals(periodsBo.getItemNo())){
					elecTemp.add(periodsBo);
				}
			}
		}

		for (ChargePeriodsBo periodsBo : elecTemp) {
			BillRltPeriodsBo billRltPeriodsBo = new BillRltPeriodsBo();
			billRltPeriodsBo.setBeginTime(periodsBo.getBeginTime());
			billRltPeriodsBo.setEndTime(periodsBo.getEndTime());
			billRltPeriodsBo.setTimeFlag(periodsBo.getTimeFlag());
			billRltPeriodsBo.setSn(periodsBo.getSn());
//			billRltPeriodsBo.setPrice(code.get(periodsBo.getTimeFlag()) + periodsBo.getPrice());
//			billRltPeriodsBo.setPrice(periodsBo.getPrice());
			result.add(billRltPeriodsBo);
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getUserChargePriceRemarkByCustId(String custId, String stationId) throws Exception {
		if(StringUtil.isNotBlank(custId)){
			Map<String, Object> inMap = new HashMap<>();
			inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
			inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
			inMap.put("custId", custId);
			inMap.put("prodType", "0");
			//1、查询计费配置
			Map conf = chargeBillRpcDao.queryUserChargeBilling(inMap);
			if(conf != null) {
				// 判断计费使用范围
				if("0".equals(StringUtil.nullToString(conf.get("rangeFlag")))
						&& !(chargeBillRpcDao.checkIsExistByStationIdAndChcNo(stationId, StringUtil.nullToString(conf.get("CHC_NO"))) > 0)){
					LOGGER.debug("用户cust" + custId + "，站点" + stationId
							+ "未在企业计费" + StringUtil.nullToString(conf.get("CHC_NO")) + "使用范围内");
					return null;
				}
				return stationChargeBillingMethod(Collections.singletonList(conf),inMap);
			}
		}
		return null;
	}

	@Override
	public ChargeBillConfigServiceModeBO queryServiceMode(String appNo) {
		return chargeBillingConfDao.queryServiceMode(appNo);
	}

	@Override
	public ChargeBillStaionInfoBO queryChargePriceHistory(String stationId, String sendTime) {
		ChargeBillStaionInfoBO staionInfoBO = new ChargeBillStaionInfoBO();
		//2、查询充电站下所有未下发成功的桩
		List<Map<String, String>> piles = archivesRpcService.queryPilesByStationId(stationId, null);

		List<String> pileNo = piles.stream().map(x -> x.getOrDefault("pileNo", "")).collect(Collectors.toList());
		ChcbillSendQueryCondition sendQueryCondition = new ChcbillSendQueryCondition();
		sendQueryCondition.setPileNos(pileNo);
		sendQueryCondition.setSendTime(sendTime);
		sendQueryCondition.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);

		ChcbillPileSendBo chcbillPileSendBo = chcbillPileSendDao.queryChcbillStationPileSendsLogLast(sendQueryCondition);
        if (ObjectUtil.isNull(chcbillPileSendBo)){
			return staionInfoBO;
		}
		ChargeBillingConfQueryCondition searchBo = new ChargeBillingConfQueryCondition();
		searchBo.setChcNo(chcbillPileSendBo.getChcNo());
		List<ChargeBillingConfBo> cbList = chargeBillingConfDao.queryChargeBillingConfs(searchBo);
		ChargeBillingConfBo billingConfBo = cbList.get(0);
		staionInfoBO.setChcNo(chcbillPileSendBo.getChcNo());
		staionInfoBO.setEftDate(billingConfBo.getEftDate());
		staionInfoBO.setSendTime(chcbillPileSendBo.getSendTime());
		staionInfoBO.setChcName(billingConfBo.getChcName());
		//2、查询充电分时设置

		List<Map> periods = peakService.queryChargePeriods(null, new String[]{billingConfBo.getChcNo()});

		staionInfoBO.setChargeMode(billingConfBo.getChargeMode());
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(billingConfBo.getChargeMode())) {//标准计费
			staionInfoBO.setElecAmt(new BigDecimal(billingConfBo.getChargePrice()));
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(billingConfBo.getChargeMode())) {//分时计费
			for (Map period : periods) {
				switch (StringUtil.nullToString(period.get("TIME_FLAG"))) {
					case ChargeConstant.TimeFlag.TOP_PEAK:
						String price = String.valueOf(period.get("PRICE"));
						staionInfoBO.setChargePricePoint(new BigDecimal(price));
						// TOP_PEAK的处理代码
						break;
					case ChargeConstant.TimeFlag.PEAK:
						String price2 = String.valueOf(period.get("PRICE"));
						staionInfoBO.setChargePricePeak(new BigDecimal(price2));
						// PEAK的处理代码
						break;
					case ChargeConstant.TimeFlag.LEVEL:
						String price3 = String.valueOf(period.get("PRICE"));
						staionInfoBO.setChargePriceFlat(new BigDecimal(price3));
						// LEVEL的处理代码
						break;
					case ChargeConstant.TimeFlag.VALLEY:
						String price4 = String.valueOf(period.get("PRICE"));
						staionInfoBO.setChargePriceVally(new BigDecimal(price4));
						// VALLEY的处理代码
						break;
					default:
						break;
				}
			}

		}

		//3、查询服务费用

          // 站点为桩计费处理
		String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
		ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
		bo.setItemNo(billingConfBo.getAttachItemNos());
		List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
		Map<String, ChargeSerItemBo> serItemBoMap = csItems.stream().collect(Collectors.toMap(ChargeSerItemBo::getItemNo, Function.identity()));

		String attachItemNos = billingConfBo.getAttachItemNos();
		String attachItemPrices = billingConfBo.getAttachItemPrices();
		String[] iNos = attachItemNos.split(",");
		String[] iPrices = attachItemPrices.split(",");
		for (int i = 0; i < iNos.length; i++) {
			if (ObjectUtil.isNotNull(serItemBoMap.get(iNos[i]))){
				ChargeSerItemBo itemBo = serItemBoMap.get(iNos[i]);
				if(BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(itemBo.getItemNo()) && "toMoreStation".equals(chargeVesion)){
					BigDecimal servicePrice = new BigDecimal(iPrices[i]);
					staionInfoBO.setServiceAmt(servicePrice);
				}
				if("服务费".equals(itemBo.getItemName()) && !"toMoreStation".equals(chargeVesion)){
					BigDecimal servicePrice = new BigDecimal(iPrices[i]);
					staionInfoBO.setServiceAmt(servicePrice);
				}
			}

		}

		return staionInfoBO;
	}
}
