package com.ls.ner.billing.rent.service.impl;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeTimePoint;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.rent.model.*;
import com.ls.ner.billing.api.rent.model.RentRun.DateItem;
import com.ls.ner.billing.api.rent.model.RentRun.Item;
import com.ls.ner.billing.api.rent.model.RentRun.PeriodItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem.Limit;
import com.ls.ner.util.json.JsonMapper;
import com.pt.poseidon.common.utils.Validate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 电动汽车租赁业务核心计费器
 * 
 * <AUTHOR>
 *
 */
@Component
public class RentBillingCalculator {
	
	
	private static final Logger LOGGER = LoggerFactory
			.getLogger(RentBillingCalculator.class);

	private static final int HOURS_PER_DAY = 24;
	private static final int KILO = 1000;
	private static final int MINUTES_PER_HOUR = 60;
	/**
	 * 
	 * @param billingFunc
	 *            计价核心对象
	 * @param chargeMethodFilter
	 *            用于区分当前计算的是预收费还是后付费，只过滤出对应的计费项
	 */
	public BigDecimal calculate(BillingFunc billingFunc, DateItem dateItem,
			String chargeMethodFilter) {
		// 如果有小计的需求，那么返回的对象要修改了么。。。可能要在BillingFunc里算好全部返回了
		BigDecimal result = new BigDecimal("0.00");
		// 主项目
		if (isChargeTimePointMatched(billingFunc.getMainChargeItem(),
				chargeMethodFilter)) {
			result = result.add(calculate(billingFunc.getMainChargeItem(),
					dateItem));
		}
		// 押金项目
		List<DepositChargeItem> depositChargeItems = billingFunc
				.getDepositChargeItems();
		if (depositChargeItems != null && depositChargeItems.size() > 0) {
			for (DepositChargeItem depositChargeItem : depositChargeItems) {
				if (isChargeTimePointMatched(depositChargeItem, chargeMethodFilter)) {
					result = result.add(calculate(depositChargeItem, dateItem));
				}
			}
		}
		// 附加项目
		// 如果不存在用户订购的附加项，就不需要计算这部分内容了
		if (StringUtils.isNotBlank(dateItem.getSelectedAttachChargeItemNos())) {
			List<AppendChargeItem> attachChargeItems = billingFunc
					.getAttachChargeItems();
			if (attachChargeItems != null && attachChargeItems.size() > 0) {
				for (AppendChargeItem attachChargeItem : attachChargeItems) {
					// 只有选中的项目才需要计算
					if (dateItem.getSelectedAttachChargeItemNos().indexOf(attachChargeItem.getItemNo()) >= 0) {
						if (isChargeTimePointMatched(attachChargeItem,
								chargeMethodFilter)) {
							result = result.add(calculate(attachChargeItem,
									dateItem));
						}
					}
				}
			}
		}
		billingFunc.setResult(result);
		return result;
	}
	/**
	 * 是否符合收费时间点 区分统计入预收费项还是后收费项
	 * @param chargeItem
	 * @param chargeTimePointFilter
	 * @return
	 */
	public boolean isChargeTimePointMatched(IChargeTimePoint chargeItem,
			String chargeTimePointFilter) {
		String chargeTimePoint = chargeItem.getChargeTimePoint();
		return ChargeTimePoint.ALL.equals(chargeTimePointFilter)
				|| chargeTimePoint.equals(chargeTimePointFilter);
	}

	public BigDecimal calculate(MainChargeItem mainChargeItem, DateItem dateItem) {
		LOGGER.info("主计费项:{}", mainChargeItem);

		String chargeMode = mainChargeItem.getChargeMode();
		LOGGER.info("计费模式:{}", chargeMode);
		if (ChargeMode.STANDARD.equals(chargeMode)) {
			return calculateStandardChargeItem(mainChargeItem, dateItem);
		} else if (ChargeMode.PERIOD.equals(chargeMode)) {
			return calculatePeriodChargeItem(mainChargeItem, dateItem);
		} else if (ChargeMode.STEP.equals(chargeMode)) {
			return calculateStepChargeItem(mainChargeItem, dateItem);
		}
		
		throw new IllegalStateException("不支持的计费模式:" + chargeMode);
	}

	/**
	 * 标准计费算法
	 * 
	 * @param mainChargeItem
	 * @param dateItem
	 * @return
	 */
	public BigDecimal calculateStandardChargeItem(
			MainChargeItem mainChargeItem, DateItem dateItem) {
		LOGGER.info("标准计费");
		String chargeWay = mainChargeItem.getChargeWay();
		LOGGER.info("计费方式:{}", chargeWay);

		BigDecimal result = new BigDecimal("0.00");

		//时间计费只有租金费用 里程计费只有里程费用  时间+里程有租金和里程费用（两项要单独出来）
		if (ChargeWay.BY_MILL.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			SubChargeItem millChargeItem = mainChargeItem.getMillChargeItem();
			Limit min = millChargeItem.getMin();
			Limit max = millChargeItem.getMax();

			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最低消费");
			}
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最高消费");
			}
			BigDecimal millResult = new BigDecimal("0.00");
			millChargeItem.setOriginalAmount(dateItem.getMeters());
			millResult = calculateMillChargeItem(millChargeItem,
					dateItem, Optional.fromNullable(min),
					Optional.fromNullable(max));
			mainChargeItem.setMillResult(millResult);
			result = result.add(millResult);
		}
		if (ChargeWay.BY_TIME.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			SubChargeItem timeChargeItem = mainChargeItem.getTimeChargeItem();
			Limit min = timeChargeItem.getMin();
			Limit max = timeChargeItem.getMax();

			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("时间无最低消费");
			}
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("时间无最高消费");
			}
			BigDecimal timeResult = new BigDecimal("0.00");
			timeChargeItem.setOriginalAmount(dateItem.getMinutes());
			timeResult = calculateTimeChargeItem(
					timeChargeItem, dateItem,
					Optional.fromNullable(min), Optional.fromNullable(max));
			mainChargeItem.setTimeResult(timeResult);
			result = result.add(timeResult);
		}
		mainChargeItem.setResult(result);
		return result;
	}

	/**
	 * 分时计费算法
	 * 
	 * @param mainChargeItem
	 * @param dateItem
	 * @return
	 */
	public BigDecimal calculatePeriodChargeItem(MainChargeItem mainChargeItem,
			DateItem dateItem) {
		LOGGER.info("分时计费");
		String chargeWay = mainChargeItem.getChargeWay();
		LOGGER.info("计费方式:{}", chargeWay);
		BigDecimal result = new BigDecimal("0.00");
		
		if (ChargeWay.BY_MILL.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			BigDecimal millResult = new BigDecimal("0.00");
			SubChargeItem millChargeItem = mainChargeItem.getMillChargeItem();
			//====里程最低消费计算计费单元开始
			Limit min = millChargeItem.getMin();
			Limit max = millChargeItem.getMax();
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最低消费");
			}
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最高消费");
			}
			PriceUnit priceUnit = millChargeItem.getPriceUnit();
			Validate.notNull(priceUnit, "没有设置计价单位");
			long meters = dateItem.getMeters();
			millChargeItem.setOriginalAmount(meters);
			LOGGER.info("实际里程:{}",meters);
			Validate.notNull(meters, "没有里程数");
			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.KM.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * KILO;
			}
			long quantity = transQuantity(meters, priceUnitValue);
			long limitedQuantity = priceUnitValue*limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), quantity);
			LOGGER.info("计算最低最高后里程:{}",limitedQuantity);
			dateItem.setMeters(limitedQuantity);
			millChargeItem.setActAmount(limitedQuantity);
			//====里程最低消费计算计费单元结束
			List<RangeChargeItem> rangeChargeItems = millChargeItem
					.getRangeChargeItems();
			Map<String, RangeChargeItem> rangeChargeItemMap = rangeChargeItems2Map(rangeChargeItems);
			List<PeriodItem> periodItems = dateItem.getPeriodItems();
			long tempQuantity = 0;
			List<RangeChargeItem> retRangeChargeItemList = new ArrayList<RangeChargeItem>();
			for (int i = 0; i < periodItems.size(); i++) {
				PeriodItem periodItem = periodItems.get(i);
				RangeChargeItem rangeChargeItem = new RangeChargeItem();
				//findMatch(rangeChargeItemMap, periodItem)会得到同样RangeChargeItem对象，使得后面更新rangeChargeItem的时候前面相同的rangeChargeItem也会被同时更新，因此复制一个出来
				BeanUtils.copyProperties(findMatch(rangeChargeItemMap, periodItem),rangeChargeItem);
				rangeChargeItem.setOriginalAmount(periodItem.getMeters());
				if(i == periodItems.size()-1){
					if(tempQuantity + periodItem.getMeters()<limitedQuantity){
						periodItem.setMeters(limitedQuantity-tempQuantity);
					}
				}
				if(tempQuantity + periodItem.getMeters()>limitedQuantity){
					periodItem.setMeters(limitedQuantity-tempQuantity);
				}
				tempQuantity = tempQuantity + periodItem.getMeters();
				
				if ("1".equals(rangeChargeItem.getIsPack())) {
					millResult = millResult.add(string2BigDecimal(rangeChargeItem.getPriceAmt()));
					rangeChargeItem.setActAmount(periodItem.getMeters());
					LOGGER.info("{}为包段，价格为{}", rangeChargeItem.getRange().getDesc(), millResult);
				} else {
					millResult = millResult.add(calculateMillChargeItem(rangeChargeItem, periodItem));
				}
				retRangeChargeItemList.add(rangeChargeItem);
			}
			millChargeItem.setRangeChargeItems(retRangeChargeItemList);
//			Map<String, PeriodItem> periodItemMap = periodItems2Map(dateItem);
//			for (int i = 0; i < rangeChargeItems.size(); i++) {
//				RangeChargeItem rangeChargeItem = rangeChargeItems.get(i);
//				// rangeChargeItems.put(rangeChargeItem.getRange(),
//				// rangeChargeItem);
//				// 用匹配的区间计费项来计费
//				Item item = findMatch(periodItemMap, rangeChargeItem);
//				if(item==null){
//					continue;
//				}
//				rangeChargeItem.setOriginalAmount(item.getMeters());
//				long tempMeters = transQuantity(item.getMeters(), priceUnitValue);
//				if(i == rangeChargeItems.size()-1){
//					if((tempQuantity + tempMeters)<limitedQuantity){
//						item.setMeters((limitedQuantity-tempQuantity)*priceUnitValue);
//					}
//				}
//				if((tempQuantity + tempMeters)>limitedQuantity){
//					item.setMeters((limitedQuantity-tempQuantity)*priceUnitValue);
//				}
//				tempQuantity = tempQuantity + tempMeters;
//				if ("1".equals(rangeChargeItem.getIsPack())) {
//					millResult = millResult.add(string2BigDecimal(rangeChargeItem.getPriceAmt()));
//					rangeChargeItem.setActAmount(item.getMeters());
//					LOGGER.info("{}为包段，价格为{}", rangeChargeItem.getRange().getDesc(), millResult);
//				} else {
//					millResult = millResult.add(calculateMillChargeItem(rangeChargeItem, item));
//				}
//				
//			};
			LOGGER.info("实际里程费用:{}",millResult);
			millResult = limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), millResult);
			LOGGER.info("计算最低最高后里程费用:{}",millResult);
			mainChargeItem.setMillResult(millResult);
			result = result.add(millResult);
		}
		if (ChargeWay.BY_TIME.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			SubChargeItem timeChargeItem = mainChargeItem.getTimeChargeItem();
			//====时间最低消费计算计费单元开始
			Limit min = timeChargeItem.getMin();
			Limit max = timeChargeItem.getMax();
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("时间无最低消费");
			}
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("时间无最高消费");
			}
			PriceUnit priceUnit = timeChargeItem.getPriceUnit();
			Validate.notNull(priceUnit, "没有设置计价单位");
			long minutes = dateItem.getMinutes();
			timeChargeItem.setOriginalAmount(minutes);
			LOGGER.info("实际分钟数:{}",minutes);
			Validate.notNull(minutes, "没有分钟数");
			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.HOUR.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR;
			} else if (PriceUnit.DAY.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR * HOURS_PER_DAY;
			}
			long quantity = transQuantity(minutes, priceUnitValue);
			long limitedQuantity = priceUnitValue*limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), quantity);
			LOGGER.info("计算最低最高后分钟数:{}",limitedQuantity);
			dateItem.setMinutes(limitedQuantity);
			timeChargeItem.setActAmount(limitedQuantity);
			//====时间最低消费计算计费单元结束
			BigDecimal timeResult = new BigDecimal("0.00");
			List<RangeChargeItem> rangeChargeItems = timeChargeItem
					.getRangeChargeItems();
			Map<String, RangeChargeItem> rangeChargeItemMap = rangeChargeItems2Map(rangeChargeItems);
			List<PeriodItem> periodItems = dateItem.getPeriodItems();
			long tempQuantity = 0;
			List<RangeChargeItem> retRangeChargeItemList = new ArrayList<RangeChargeItem>();
			for (int i = 0; i < periodItems.size(); i++) {
				PeriodItem periodItem = periodItems.get(i);
				RangeChargeItem rangeChargeItem = new RangeChargeItem();
				//findMatch(rangeChargeItemMap, periodItem)会得到同样RangeChargeItem对象，使得后面更新rangeChargeItem的时候前面相同的rangeChargeItem也会被同时更新，因此复制一个出来
				BeanUtils.copyProperties(findMatch(rangeChargeItemMap, periodItem),rangeChargeItem);
				rangeChargeItem.setOriginalAmount(periodItem.getMinutes());
				long tempMinutes = periodItem.getMinutes();
				if(i == periodItems.size()-1){
					if((tempQuantity + tempMinutes)<limitedQuantity){
						periodItem.setMinutes(limitedQuantity-tempQuantity);
					}
				}
				if((tempQuantity + tempMinutes)>limitedQuantity){
					periodItem.setMinutes(limitedQuantity-tempQuantity);
				}
				tempQuantity = tempQuantity + tempMinutes;
				if ("1".equals(rangeChargeItem.getIsPack())) {
					timeResult = timeResult.add(string2BigDecimal(rangeChargeItem.getPriceAmt()));
					rangeChargeItem.setActAmount(periodItem.getMinutes());
					LOGGER.info("{}为包段，价格为{}", rangeChargeItem.getRange().getDesc(), timeResult);
				} else {
					timeResult = timeResult.add(calculateTimeChargeItem(rangeChargeItem, periodItem));
				}
				retRangeChargeItemList.add(rangeChargeItem);
			}
			
//			Map<String, PeriodItem> periodItemMap = periodItems2Map(dateItem);//此段会将重复的分时段给剔除 怎么办？
//			for (int i = 0; i < rangeChargeItems.size(); i++) {
//				RangeChargeItem rangeChargeItem = rangeChargeItems.get(i);
//				// rangeChargeItems.put(rangeChargeItem.getRange(),
//				// rangeChargeItem);
//				// 用匹配的区间计费项来计费
//				Item item = findMatch(periodItemMap, rangeChargeItem);
//				if(item==null){
//					continue;
//				}
//				rangeChargeItem.setOriginalAmount(item.getMinutes());
//				long tempMinutes = transQuantity(item.getMinutes(), priceUnitValue);
//				if(i == rangeChargeItems.size()-1){
//					if((tempQuantity + tempMinutes)<limitedQuantity){
//						item.setMinutes((limitedQuantity-tempQuantity)*priceUnitValue);
//					}
//				}
//				if((tempQuantity + tempMinutes)>limitedQuantity){
//					item.setMinutes((limitedQuantity-tempQuantity)*priceUnitValue);
//				}
//				tempQuantity = tempQuantity + tempMinutes;
//				if ("1".equals(rangeChargeItem.getIsPack())) {
//					timeResult = timeResult.add(string2BigDecimal(rangeChargeItem.getPriceAmt()));
//					LOGGER.info("{}为包段，价格为{}", rangeChargeItem.getRange().getDesc(), timeResult);
//				} else {
//					timeResult = timeResult.add(calculateTimeChargeItem(rangeChargeItem, item));
//				}
//			}
			timeChargeItem.setRangeChargeItems(retRangeChargeItemList);
			LOGGER.info("实际时间费用:{}",timeResult);
			timeResult = limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), timeResult);
			LOGGER.info("计算最低最高后时间费用:{}",timeResult);
			mainChargeItem.setTimeResult(timeResult);
			result = result.add(timeResult);
		}
		mainChargeItem.setResult(result);
		return result;
	}

	/**
	 * 分时项列表转为map
	 * 
	 * @param periodItemMap
	 * @param rangeChargeItem
	 * @return
	 */
	protected Map<String, PeriodItem> periodItems2Map(DateItem dateItem) {
		Map<String, PeriodItem> periodItemMap = Maps.newHashMap();
		List<PeriodItem> periodItems = dateItem.getPeriodItems();

		for (PeriodItem periodItem : periodItems) {
			periodItemMap.put(periodItem.getRange().toString(), periodItem);
		}
		return periodItemMap;
	}
	/**
	 * 
	 * 方法说明：定价分时项转map
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月29日 下午4:13:25
	 * History:  2016年5月29日 下午4:13:25   lipf   Created.
	 *
	 * @param rangeChargeItems
	 * @return
	 *
	 */
	protected Map<String, RangeChargeItem> rangeChargeItems2Map(List<RangeChargeItem> rangeChargeItems) {
		Map<String, RangeChargeItem> rangeChargeItemMap = Maps.newHashMap();

		for (RangeChargeItem rangeChargeItem : rangeChargeItems) {
			rangeChargeItemMap.put(rangeChargeItem.getRange().toString(), rangeChargeItem);
		}
		return rangeChargeItemMap;
	}
	/**
	 * 
	 * 方法说明：找到匹配的定价分时项
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月29日 下午4:22:49
	 * History:  2016年5月29日 下午4:22:49   lipf   Created.
	 *
	 * @param rangeChargeItemMap
	 * @param periodItem
	 * @return
	 *
	 */
	protected RangeChargeItem findMatch(Map<String, RangeChargeItem> rangeChargeItemMap,
			PeriodItem periodItem) {
		return rangeChargeItemMap.get(periodItem.getRange().toString());
	}
	

	/**
	 * 找到匹配的分时项
	 * 
	 * @param periodItemMap
	 * @param rangeChargeItem
	 * @return
	 */
	protected PeriodItem findMatch(Map<String, PeriodItem> periodItemMap,
			RangeChargeItem rangeChargeItem) {
		return periodItemMap.get(rangeChargeItem.getRange().toString());
	}

	/**
	 * 阶梯计费算法
	 * 
	 * @param mainChargeItem
	 * @param dateItem
	 * @return
	 */
	public BigDecimal calculateStepChargeItem(MainChargeItem mainChargeItem,
			DateItem dateItem) {
		LOGGER.info("阶梯计费");
		String chargeWay = mainChargeItem.getChargeWay();
		LOGGER.info("计费方式:{}", chargeWay);
		BigDecimal result = new BigDecimal("0.00");

		if ((ChargeWay.BY_MILL.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay))&&dateItem.getMeters()>0) {
			BigDecimal millResult = new BigDecimal("0.00");
			SubChargeItem millChargeItem = mainChargeItem.getMillChargeItem();
			//====里程最低消费计算计费单元开始
			Limit min = millChargeItem.getMin();
			Limit max = millChargeItem.getMax();
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最低消费");
			}
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("里程无最高消费");
			}
			PriceUnit priceUnit = millChargeItem.getPriceUnit();
			Validate.notNull(priceUnit, "没有设置计价单位");
			long meters = dateItem.getMeters();
			millChargeItem.setOriginalAmount(meters);
			LOGGER.info("实际里程:{}",meters);
			Validate.notNull(meters, "没有里程数");
			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.KM.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * KILO;
			}
			long quantity = transQuantity(meters, priceUnitValue);
			long limitedQuantity = priceUnitValue*limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), quantity);
			LOGGER.info("计算最低最高后里程:{}",limitedQuantity);
			
			millChargeItem.setActAmount(limitedQuantity);
			//====里程最低消费计算计费单元结束
			List<RangeChargeItem> rangeChargeItems = millChargeItem
					.getRangeChargeItems();
			for (RangeChargeItem rangeChargeItem : rangeChargeItems) {
				Item stepItem = new Item();
				stepItem.setRecordDate(dateItem.getRecordDate());
				//RANGE里的值的单位固定是每公里
//				rangeChargeItem.setPriceUnit(new PriceUnit(1,PriceUnit.KM));
				String from = StringUtils.trimToEmpty(rangeChargeItem.getRange().getFrom());
				Validate.notEmpty(from, "阶梯设置不能为左开区间");
				String to = StringUtils.trimToEmpty(rangeChargeItem.getRange().getTo());
				long toValue = 0;
				if(!"".equals(to)){
					toValue = Long.parseLong(to);
				}
				long fromValue = Long.parseLong(from);
				priceUnit = rangeChargeItem.getPriceUnit();
				if (PriceUnit.KM.equals(priceUnit.getUnit())) {
					toValue = toValue * KILO;
					fromValue = fromValue * KILO;
				}
				//区间本身是递增连续，这个有效性不在这里做验证，而是交由calculator外部。这里约定好已经按照这个顺序排好，如 0-15 15-30 30-
				//如果是右开区间，或者右端点的值超出总数，那么把总数扣去左端点的值（做好单位换算），就是落在这个区间内的量。同时，也是最后一个区间，跳出循环
				if("".equals(to)||(toValue>=limitedQuantity)){
					if(fromValue>=dateItem.getMeters()){
						rangeChargeItem.setOriginalAmount(0);
						stepItem.setMeters(limitedQuantity-fromValue);
					} else {
						rangeChargeItem.setOriginalAmount(dateItem.getMeters()-fromValue);
						stepItem.setMeters(dateItem.getMeters()-fromValue);
						if(dateItem.getMeters()>limitedQuantity){
							stepItem.setMeters(limitedQuantity-fromValue);
						}
					}
					millResult = millResult.add(calculateMillChargeItem(rangeChargeItem,
							stepItem));
					
					break;
				}
				else{
					if(toValue>dateItem.getMeters()){
						rangeChargeItem.setOriginalAmount(dateItem.getMeters()-fromValue);
					} else if(fromValue>dateItem.getMeters()){
						rangeChargeItem.setOriginalAmount(0);
					} else {
						rangeChargeItem.setOriginalAmount(toValue-fromValue);
					}
					stepItem.setMeters(toValue-fromValue);
					millResult = millResult.add(calculateMillChargeItem(rangeChargeItem,
							stepItem));
				}
				
			}
			dateItem.setMeters(limitedQuantity);
			LOGGER.info("实际里程费用:{}",millResult);
			millResult = limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), millResult);
			LOGGER.info("计算最低最高后里程费用:{}",millResult);
			mainChargeItem.setMillResult(millResult);
			result = result.add(millResult);

		}
		if ((ChargeWay.BY_TIME.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay))&&dateItem.getMinutes()>0) {
			BigDecimal timeResult = new BigDecimal("0.00");
			SubChargeItem timeChargeItem = mainChargeItem.getTimeChargeItem();
			//====时间最低消费计算计费单元开始
			Limit min = timeChargeItem.getMin();
			Limit max = timeChargeItem.getMax();
			if (min == null || min.getLimitQuantity()==0) {
				LOGGER.info("时间无最低消费");
			}
			if (max == null || max.getLimitQuantity()==0) {
				LOGGER.info("时间无最高消费");
			}
			PriceUnit priceUnit = timeChargeItem.getPriceUnit();
			Validate.notNull(priceUnit, "没有设置计价单位");
			long minutes = dateItem.getMinutes();
			timeChargeItem.setOriginalAmount(minutes);
			LOGGER.info("实际分钟数:{}",minutes);
			Validate.notNull(minutes, "没有分钟数");
			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.HOUR.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR;
			} else if (PriceUnit.DAY.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR * HOURS_PER_DAY;
			}
			long quantity = transQuantity(minutes, priceUnitValue);
			long limitedQuantity = priceUnitValue*limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), quantity);
			LOGGER.info("计算最低最高后分钟数:{}",limitedQuantity);
			
			timeChargeItem.setActAmount(limitedQuantity);
			//====时间最低消费计算计费单元结束
			List<RangeChargeItem> rangeChargeItems = timeChargeItem
					.getRangeChargeItems();
			for (RangeChargeItem rangeChargeItem : rangeChargeItems) {
				Item stepItem = new Item();
				stepItem.setRecordDate(dateItem.getRecordDate());
				//RANGE里的值的单位固定是 每小时
//				rangeChargeItem.setPriceUnit(new PriceUnit(1,PriceUnit.HOUR));
				
				String from = StringUtils.trimToEmpty(rangeChargeItem.getRange().getFrom());
				Validate.notEmpty(from, "阶梯设置不能为左开区间");
				String to = StringUtils.trimToEmpty(rangeChargeItem.getRange().getTo());
				long toValue = 0;
				if(!"".equals(to)){
					toValue = Long.parseLong(to);
				}
				long fromValue = Long.parseLong(from);
				priceUnit = rangeChargeItem.getPriceUnit();
				if (PriceUnit.HOUR.equals(priceUnit.getUnit())) {
					toValue = toValue * MINUTES_PER_HOUR;
					fromValue = fromValue * MINUTES_PER_HOUR;
				} else if (PriceUnit.DAY.equals(priceUnit.getUnit())) {
					toValue = Long.parseLong(to) * MINUTES_PER_HOUR * HOURS_PER_DAY;
					fromValue = Long.parseLong(from) * MINUTES_PER_HOUR;
				}
				//区间本身是递增连续，这个有效性不在这里做验证，而是交由calculator外部。这里约定好已经按照这个顺序排好，如 0-15 15-30 30-
				//如果是右开区间，或者右端点的值超出总数，那么把总数扣去左端点的值（做好单位换算），就是落在这个区间内的量。同时，也是最后一个区间，跳出循环
				if("".equals(to)||(toValue>=limitedQuantity)){
					if(fromValue>=dateItem.getMinutes()){
						rangeChargeItem.setOriginalAmount(0);
						stepItem.setMinutes(limitedQuantity-fromValue);
					} else {
						rangeChargeItem.setOriginalAmount(dateItem.getMinutes()-fromValue);
						stepItem.setMinutes(dateItem.getMinutes()-fromValue);
						if(dateItem.getMinutes()>limitedQuantity){
							stepItem.setMinutes(limitedQuantity-fromValue);
						}
					}
					timeResult = timeResult.add(calculateTimeChargeItem(rangeChargeItem,
							stepItem));
					break;
				}
				else{
					if(toValue>dateItem.getMinutes()){
						rangeChargeItem.setOriginalAmount(dateItem.getMinutes()-fromValue);
					} else if(fromValue>dateItem.getMinutes()){
						rangeChargeItem.setOriginalAmount(0);
					} else {
						rangeChargeItem.setOriginalAmount(toValue-fromValue);
					}
					stepItem.setMinutes(toValue-fromValue);
					timeResult = timeResult.add(calculateTimeChargeItem(rangeChargeItem,
							stepItem));
				}
			}
			dateItem.setMinutes(limitedQuantity);
			LOGGER.info("实际时间费用:{}",timeResult);
			timeResult = limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), timeResult);
			LOGGER.info("计算最低最高后时间费用:{}",timeResult);
			mainChargeItem.setTimeResult(timeResult);
			result = result.add(timeResult);
		}
		LOGGER.debug("mainChargeItem:{}", JsonMapper.nonEmptyMapper().toJson(mainChargeItem));
		mainChargeItem.setResult(result);
		return result;
	}

	/**
	 * 单日里程计费算法
	 * 
	 * @param millChargeItem
	 * @return
	 */
	public BigDecimal calculateMillChargeItem(
			AbstractChargeItem millChargeItem, Item item) {
		return calculateMillChargeItem(millChargeItem, item,
				Optional.<Limit> absent(), Optional.<Limit> absent());
	}

	/**
	 * 单日里程计费算法
	 * 
	 * @param millChargeItem
	 * @return
	 */
	public BigDecimal calculateMillChargeItem(
			AbstractChargeItem millChargeItem, Item item, Optional<Limit> min,
			Optional<Limit> max) {

		String price = millChargeItem.getPrice();
		Validate.notNull(price, "没有设置单价");
		PriceUnit priceUnit = millChargeItem.getPriceUnit();
		Validate.notNull(priceUnit, "没有设置计价单位");
		BigDecimal result = null;
		// 计价单位的为单位一的 price就是总价 目前就分时用到
		if (priceUnit.equalToOnce()) {
			result = new BigDecimal(price);

		} else {
			long meters = item.getMeters();
			Validate.notNull(price, "没有里程数");
			
			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.KM.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * KILO;
			}
			long quantity = transQuantity(meters, priceUnitValue);
			long limitedQuantity = limitByUnit(min, max, quantity);
			result = getResult(price, limitedQuantity);
			item.setMeters(limitedQuantity*priceUnitValue);
			millChargeItem.setActAmount(limitedQuantity*priceUnitValue);
		}
		// min & max
		result = limitByAmount(min, max, result);
		millChargeItem.setResult(result);
		return result;
	}

	/**
	 * 通过单位来限制上下限 必须保证 min max run的单位是一致的
	 * 
	 * @param min
	 * @param max
	 * @param run
	 * @return
	 */
	public long limitByUnit(Optional<Limit> min, Optional<Limit> max, long run) {
		if (min.isPresent()
				&& Limit.LIMIT_TYPE_BY_UNIT.equals(min.get().getLimitType())&&min.get().getLimitQuantity()!=0) {
			LOGGER.info("运行数:{},最小计费数:{},", run, min.get().getLimitQuantity());
			// 如果运行数小于最低消费，取最小计费里程数
			if (run < min.get().getLimitQuantity()) {
				LOGGER.info("运行数小于最小计费数，取最小计费数");
				return min.get().getLimitQuantity();
			}
		}
		// 这里不能有else 不然进入上述的if判断后，else的部分就不执行了
		if (max.isPresent()
				&& Limit.LIMIT_TYPE_BY_UNIT.equals(max.get().getLimitType())&&max.get().getLimitQuantity()!=0) {
			LOGGER.info("运行数:{},最大计费数:{},", run, max.get().getLimitQuantity());
			// 如果运行的数大于最大计费数，取最大计费数
			if (run > max.get().getLimitQuantity()) {
				LOGGER.info("运行的数大于最大计费数，取最大计费数");
				return max.get().getLimitQuantity();
			}
		}
		return run;
	}

	/**
	 * 通过总价来限制上下限
	 * 
	 * @param min
	 * @param max
	 * @param result
	 * @return
	 */
	public BigDecimal limitByAmount(Optional<Limit> min, Optional<Limit> max,
			BigDecimal result) {
		result = scale(result);
		if (min.isPresent()
				&& Limit.LIMIT_TYPE_BY_AMOUNT.equals(min.get().getLimitType())&&min.get().getLimitQuantity()!=0) {
			LOGGER.info("运行数:{},最小计费数:{},", result, min.get()
					.getLimitQuantity());
			// 如果运行的总价小于最小计费数，取最小计费数
			BigDecimal minResult = scale(new BigDecimal(min.get()
					.getLimitQuantity()));
			if (result.compareTo(minResult) == -1) {
				return minResult;
			}
		}
		// 这里不能有else 不然进入上述的if判断后，else的部分就不执行了
		if (max.isPresent()
				&& Limit.LIMIT_TYPE_BY_AMOUNT.equals(max.get().getLimitType())&&max.get().getLimitQuantity()!=0) {
			LOGGER.info("运行数:{},最大计费数:{},", result, max.get()
					.getLimitQuantity());
			// 如果运行的总价大于最大计费数，取最大计费数
			BigDecimal maxResult = scale(new BigDecimal(max.get()
					.getLimitQuantity()));
			if (result.compareTo(maxResult) == 1) {
				return maxResult;
			}
		}
		return result;
	}

	/**
	 * 单日时间计费算法
	 * 
	 * @param millChargeItem
	 * @return
	 */
	public BigDecimal calculateTimeChargeItem(
			AbstractChargeItem timeChargeItem, Item item) {
		return calculateTimeChargeItem(timeChargeItem, item,
				Optional.<Limit> absent(), Optional.<Limit> absent());
	}

	/**
	 * 单日时间计费算法
	 * 
	 * @param millChargeItem
	 * @return
	 */
	public BigDecimal calculateTimeChargeItem(
			AbstractChargeItem timeChargeItem, Item item, Optional<Limit> min,
			Optional<Limit> max) {

		String price = timeChargeItem.getPrice();
		Validate.notNull(price, "没有设置单价");
		BigDecimal result = null;
		PriceUnit priceUnit = timeChargeItem.getPriceUnit();
		if (PriceUnit.YEAR.equals(priceUnit.getUnit())) {
			price = bigDecimal2String(new BigDecimal(price).divide(new BigDecimal("12"),2,BigDecimal.ROUND_HALF_UP));
		}
		Validate.notNull(priceUnit, "没有设置计价单位");
		// 计价单位为一次的 price就是总价
		if (priceUnit.equalToOnce()) {
			result = new BigDecimal(price);

		} else {
			long minutes = item.getMinutes();
			Validate.notNull(minutes, "没有分钟数");

			int priceUnitValue = priceUnit.getValue();
			if (PriceUnit.HOUR.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR;
			} else if (PriceUnit.DAY.equals(priceUnit.getUnit())) {
				priceUnitValue = priceUnitValue * MINUTES_PER_HOUR * HOURS_PER_DAY;
			}
			long quantity = transQuantity(minutes, priceUnitValue);
			long limitedQuantity = limitByUnit(min, max, quantity);
			result = getResult(price, limitedQuantity);
			timeChargeItem.setActAmount(limitedQuantity*priceUnitValue);
			item.setMinutes(limitedQuantity*priceUnitValue);
		}
		result = limitByAmount(min, max, result);
		timeChargeItem.setResult(result);
		return result;
	}

	public BigDecimal getResult(String price, long quantity) {
		return scale(new BigDecimal(price).multiply(new BigDecimal(quantity)));
	}

	/**
	 * 获取经过最小计费单位换算过的使用量
	 * 
	 * @param amount
	 * @param priceUnitValue
	 * @return
	 */
	public long transQuantity(long amount, int priceUnitValue) {
		long a = amount / priceUnitValue;
		long b = amount % priceUnitValue;
		long quantity = a;
		if (b != 0) {
			quantity += 1;
		}
		return quantity;
	}

	public BigDecimal scale(BigDecimal bigDecimal) {
		return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	public String bigDecimal2String(BigDecimal bigDecimal) {
		return scale(bigDecimal).toPlainString();
	}
	
	public BigDecimal string2BigDecimal(String string) {
		return scale(new BigDecimal(string));
	}

	/**
	 * 押金计费
	 * 
	 * @param depositChargeItem
	 * @param dateItem
	 * @return
	 */
	public BigDecimal calculate(DepositChargeItem depositChargeItem,
			DateItem dateItem) {
		String price = depositChargeItem.getPrice();
		return calculateOncePerRent(depositChargeItem, dateItem, price);
	}

	/**
	 * 附加计费项
	 * 
	 * @param appendChargeItem
	 * @param dateItem
	 * @return
	 */
	public BigDecimal calculate(AppendChargeItem appendChargeItem,
			DateItem dateItem) {
		String price = appendChargeItem.getPrice();
		Validate.notNull(price, "没有设置单价");
		// 没有计价单位的 price就是总价 而且。。。只能在一组数据的第一天计算。
		PriceUnit priceUnit = appendChargeItem.getPriceUnit();
		Validate.notNull(priceUnit, "没有设置计价单位");
		// 计价单位为一次的 price就是总价
		if (priceUnit.equalToOnce()) {
			return calculateOncePerRent(appendChargeItem, dateItem, price);
		}
		String unit = priceUnit.getUnit();
		// 米和公里按照里程算
		if (PriceUnit.M.equals(unit) || PriceUnit.KM.equals(unit)) {
			return calculateMillChargeItem(appendChargeItem, dateItem);
		}
		// 其他按照时间算
		else {
			return calculateTimeChargeItem(appendChargeItem, dateItem);
		}
	}

	/**
	 * 计算每次租赁只计算一次的计费项
	 * 
	 * @param chargeItem
	 * @param dateItem
	 * @param price
	 * @return
	 */
	protected BigDecimal calculateOncePerRent(AbstractChargeItem chargeItem,
			DateItem dateItem, String price) {
		// 当天数据是否是运行数据中的第一天，如果是，要计算一次付费的附加项。否则不用
		BigDecimal result = null;
		if (dateItem.isFirstDay()) {
			result = new BigDecimal(price);
		} else {
			result = new BigDecimal("0.00");
		}
		chargeItem.setResult(result);
		return result;
	}
}
