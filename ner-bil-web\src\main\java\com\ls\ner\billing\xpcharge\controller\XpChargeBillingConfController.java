package com.ls.ner.billing.xpcharge.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.ComConstant;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.service.IChargeBillRpcService;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.billing.xpcharge.bo.PeakRelBo;
import com.ls.ner.billing.xpcharge.bo.PeakTimeBo;
import com.ls.ner.billing.xpcharge.service.IXpChargeBillingConfService;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.def.api.constants.Constants;
import com.ls.ner.util.*;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 充电计费配置
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/xpcbc")
public class XpChargeBillingConfController extends QueryController<XpChargeBillingConfQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(XpChargeBillingConfController.class);

	@ServiceAutowired("xpChargeBillingConfService")
	private IXpChargeBillingConfService xpChargeBillingConfService;

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
	private ISysParamService sysParamService;


	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired
	Authentication authentication;

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private IChargeBillRpcService chargeBillRpcService;
	@ServiceAutowired("peakService")
	private IXpPeakService peakService;


	@Override
	protected XpChargeBillingConfQueryCondition initCondition() {
		return new XpChargeBillingConfQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-25
	 * @description 初始化充电计费配置
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
		AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
		if(orgBo != null){
			condition.setOrgCode(orgBo.getOrgCode());
			condition.setOrgCodeName(orgBo.getOrgShortName());
		}
		m.addAttribute("searchForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/xpchargeConf/xpchargeConfIndex";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-04-03
	 * @description 初始化计费配置选择页面
	 */
	@RequestMapping(value = "/initSelect", method = RequestMethod.GET)
	public String initSelect(Model m ,@RequestParam("orgCode") String orgCode,@RequestParam("isMain") String isMain,
							 @RequestParam(value = "isFrom",required = false,defaultValue = "0")String isFrom) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();

		condition.setOrgCode(orgCode);
		condition.setIsMain(isMain);
		condition.setEndEftDate(JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
		condition.setChcStatus(ChargeConstant.validFlag.ENABLE);
		m.addAttribute("searchForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("isFrom",isFrom);
		return "/bil/xpchargeConf/selectxpchargeConfIndex";
	}


	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-16
	 * @description 查询充电计费配置
	 */
	@RequestMapping(value = "/queryChargeBillingConfs")
	public @ItemResponseBody QueryResultObject queryChargeSerItems(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		XpChargeBillingConfQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				condition.setOrgCodeList(orgCodeList);
				condition.setOrgCode(null);
			}
		}
		if(StringUtil.isNotBlank(condition.getChcStatus())){
			condition.setChcStatusList(Arrays.asList(condition.getChcStatus().split(ComConstant.COMMA)));
			condition.setChcStatus(null);
		}
		String currentEnvironment = sysParamService.getSysParamsValues("currentEnvironment");
		String chargeVesion = sysParamService.getSysParamsValues("chargeVesion");
		if(!"xiaopeng".equals(currentEnvironment)&& "0".equals(condition.getIsMain())){
			condition.setBillCtlMode("2");
			if ("toMoreStation".equals(chargeVesion) && "1".equals(condition.getIsFrom())){
				//zhangdan  浙江新增
				condition.setChcStatus("1");
				condition.setEndEftDate(DateTools.dateToStr(new Date(),DateTools.YMDHM));
				condition.setStationIdIsNull("1");
			}
		}
		condition.setCustIdIsNull("1");
		List<ChargeBillingConfBo> list =xpChargeBillingConfService.queryChargeBillingConfs(condition);
		if(list!=null && list.size()>0){
			StringBuilder stationIdSb = new StringBuilder();
			for(ChargeBillingConfBo cbcBo:list){
				OrgBo org = orgService.getOrgByNo(cbcBo!=null?cbcBo.getOrgCode():"");
				if(org != null)
					cbcBo.setOrgCodeName(org.getOrgShortName());
			}
		}
		int recordCount = xpChargeBillingConfService.queryChargeBillingConfsNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}


	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增/修改充电计费配置
	 */
	@RequestMapping(value = "/addChargeBillingConf", method = RequestMethod.GET)
	public String addChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			m.addAttribute("chargeForm", cbc);
		} else {
			XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =xpChargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);
				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				m.addAttribute("chargeForm", bo);
			}else{
				m.addAttribute("chargeForm", new ChargeBillingConfBo());
			}
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		List<CodeBO> billingTagList = codeService.getStandardCodes(BizConstants.ProductType.BILLING_TAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("billingTagList", billingTagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/xpchargeConf/xpaddChargeConf";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 充电计费配置详情
	 */
	@RequestMapping(value = "/showChargeBillingConf", method = RequestMethod.GET)
	public String showChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			m.addAttribute("chargeForm", cbc);
		} else {
			XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =xpChargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);
				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				m.addAttribute("chargeForm", bo);
			}else
				m.addAttribute("chargeForm", new ChargeBillingConfBo());
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
        List<CodeBO> billingTagList = codeService.getStandardCodes(BizConstants.ProductType.BILLING_TAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
        m.addAttribute("billingTagList", billingTagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/xpchargeConf/showXpaddChargeConf";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 保存充电计费配置
	 */
	@RequestMapping(value = "/saveChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveChargeBillingConf(@RequestBody Map<String, Object> dataParams) throws IllegalAccessException, InvocationTargetException{
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try{
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				bo.setOrgCode(orgBo.getOrgCode());
			}
			String systemId = bo.getSystemId();
			bo.setOperNo(currentAccount.getAccountName());
			ChargeBillingConfHistoryBo historyBo = new ChargeBillingConfHistoryBo();
			historyBo.setChcNo(bo.getChcNo());
			historyBo.setOperNo(currentAccount.getAccountName());
			if (systemId != null && !"".equals(systemId)) {
				chargeBillingConfService.updateChargeBillingConf(bo);
				historyBo.setDataOperType("U");
			} else {
				String chcNo = chargeBillingConfService.insertChargeBillingConf(bo);
				historyBo.setDataOperType("I");
				historyBo.setChcNo(chcNo);
			}
			chargeBillingConfService.addChargeBillingConfHistory(historyBo);
			//新增和修改的时候 将当前操作人记录在修改历史表中
			return WrappedResult.successWrapedResult(bo);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * @Description 保存并生效计费
	 * <AUTHOR>
	 * @Date 2022/6/14 19:19
	 * @param dataParams
	 */
	@RequestMapping(value = "/effectiveChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult effectiveChargeBillingConf(@RequestBody Map<String, Object> dataParams) throws IllegalAccessException, InvocationTargetException{
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try{
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				bo.setOrgCode(orgBo.getOrgCode());
			}
			bo.setEftDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
			String systemId = bo.getSystemId();
			if (systemId != null && !"".equals(systemId)) {
				chargeBillingConfService.updateChargeBillingConf(bo);
			} else {
				chargeBillingConfService.insertChargeBillingConf(bo);
			}
			// 获取当前请求的属性
			ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			// 从请求属性中获取 HttpServletRequest 对象
			HttpServletRequest request = attrs.getRequest();
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());

			//立即生效操作
			chargeBillingConfService.effectiveChargeBillingConf(bo,map);
			return WrappedResult.successWrapedResult(bo);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 发布充电计费配置
	 */
	@RequestMapping(value = "/releaseChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult releaseChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		try{
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			bo.setChcNo(chcNo);
			xpChargeBillingConfService.releaseChargeBillingConf(bo);
			return WrappedResult.successWrapedResult(true);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 发布并更新充电计费配置
	 */
	@RequestMapping(value = "/releaseAndModifyChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult releaseAndModifyChargeBillingConf(Model m, @RequestBody Map<String, Object> dataParams) throws InvocationTargetException, IllegalAccessException {
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try{
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				bo.setOrgCode(orgBo.getOrgCode());
			}
			String systemId = bo.getSystemId();
			if (systemId != null && !"".equals(systemId)) {
				chargeBillingConfService.updateChargeBillingConf(bo);
			} else {
				chargeBillingConfService.insertChargeBillingConf(bo);
			}
			xpChargeBillingConfService.releaseChargeBillingConf(bo);
			return WrappedResult.successWrapedResult(bo);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 复制充电计费配置
	 */
	@RequestMapping(value = "/copyChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult copyChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		bo = chargeBillingConfService.copyChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(bo);
	}

	/**
	 * @Description 停用计费
	 * <AUTHOR>
	 * @Date 2022/6/14 17:45
	 * @param m
	 * @param chcNo
	 */
	@RequestMapping(value = "/deactivateChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult deactivateChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		List<ChargeBillingConfBo> chargeBillingConfBos = chargeBillingConfService.deactivateChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(chargeBillingConfBos);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 调价充电计费配置
	 */
	@RequestMapping(value = "/modifyPriceChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult modifyPriceChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		bo = chargeBillingConfService.modifyPriceChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(bo);
	}

	/**
	 * @param m
	 * @param chcNo
	 * @description 下发分时信息
	 * <AUTHOR>
	 * @create 2018-08-13 18:39:10
	 */
	@RequestMapping(value = "/sendChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult sendChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		chargeBillingConfService.sendChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(true);
	}


	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询分时时段设置
	 */
	@RequestMapping(value = "/queryChargePeriods")
	public @ResponseBody WrappedResult queryChargePeriods(@RequestBody ChargeBillingConfQueryCondition condition) {
//		condition.setPageEnd(0);
		List<ChargePeriodsBo> list =chargeBillingConfService.queryChargePeriods(condition);
		if(list!= null){
			for(ChargePeriodsBo cpBo:list){
				cpBo.setPrice(FormatUtil.formatNumber(cpBo.getPrice(),4));
//				if(!StringUtils.nullOrBlank(cpBo.getTimeFlag())){
//					CodeBO code = codeService.getStandardCode(BizConstants.ProductType.TIME_FLAG, cpBo.getTimeFlag(), null);
//					if(code!=null){
//						cpBo.setTimeFlag(code.getCodeName());
//					}
//				}
			}
		}

		logger.debug("调试用serviceMode:"+condition.getServiceMode());
		if ("0202".equals(condition.getServiceMode())){
			//分时
			condition.setItemNo("1000001000");
			List<ChargePeriodsBo> serList =chargeBillingConfService.queryChargePeriods(condition);
			if (list != null && serList != null && list.size() == serList.size()){
				for(ChargePeriodsBo cpBo:list){
					for (ChargePeriodsBo serBo : serList){
						if (cpBo.getTimeFlag().equals(serBo.getTimeFlag())){
							cpBo.setSerPrice(FormatUtil.formatNumber(serBo.getPrice(),4));
							break;
						}
					}
				}
			}
		}

		return WrappedResult.successWrapedResult(list);
	}

	//-------------------------------------------------------------新计费配置-------------------------------------------

	/**
	 * @param m
	 * @description   初始化充电计费配置，支持服务费分时
	 * <AUTHOR>
	 * @create 2020-03-17 17:01:16
	 */
	@RequestMapping(value = "/newInit", method = RequestMethod.GET)
	public String newInit(Model m) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
		AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
		if(orgBo != null){
			condition.setOrgCode(orgBo.getOrgCode());
			condition.setOrgCodeName(orgBo.getOrgShortName());
		}
		String peakBillingSwitch = sysParamService.getSysParamsValues("peakBillingSwitch");
		m.addAttribute("peakBillingSwitch", peakBillingSwitch);
		m.addAttribute("searchForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/xpchargeConf/newXpchargeConfIndex";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 充电计费配置详情
	 */
	@RequestMapping(value = "/newShowChargeBillingConf", method = RequestMethod.GET)
	public String newShowChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {

		//判断是否存在服务费是否分时
		// 01标准  02分时
		String serviceMode = "0201";

		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			m.addAttribute("chargeForm", cbc);
		} else {
			XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =xpChargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);


				if (StringUtil.isNotBlank(bo.getAttachItemNos()) && StringUtil.isNotBlank(bo.getItemChargeMode())){
					String[] itemNo = bo.getAttachItemNos().split(",");
					String[] itemChargeMode = bo.getItemChargeMode().split(",");
					for(int i = 0;i < itemNo.length;i ++){
						if("1000001000".equals(itemNo[i])){
							if ("0202".equals(itemChargeMode[i])){
								serviceMode = "0202";
							}
						}
					}
				}
				bo.setServiceMode(serviceMode);

				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				m.addAttribute("chargeForm", bo);
			}else {
				m.addAttribute("chargeForm", new ChargeBillingConfBo());
			}
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/xpchargeConf/newShowXpaddChargeConf";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增/修改充电计费配置
	 */
	@RequestMapping(value = "/newAddChargeBillingConf", method = RequestMethod.GET)
	public String newAddChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			//新增服务费默认为标准模式
			m.addAttribute("chargeForm", cbc);
		} else {
			XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =xpChargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);

				//判断是否存在服务费是否分时
				// 01标准  02分时
				String serviceMode = "0201";
				if (StringUtil.isNotBlank(bo.getAttachItemNos()) && StringUtil.isNotBlank(bo.getItemChargeMode())){
					String[] itemNo = bo.getAttachItemNos().split(",");
					String[] itemChargeMode = bo.getItemChargeMode().split(",");
					String itemNoStr = "";
					String itemChargeModeStr = "";
					for(int i = 0;i < itemNo.length;i ++){
						if("1000001000".equals(itemNo[i]) && "0202".equals(itemChargeMode[i])){
							serviceMode = "0202";
						}else{
							itemNoStr += itemNo[i] + ",";
							itemChargeModeStr += itemChargeMode[i] + ",";
						}
					}
					if (StringUtil.isNotBlank(itemNoStr) && StringUtil.isNotBlank(itemChargeModeStr)){
						//去掉末尾空格
						itemNoStr = itemNoStr.substring(0,itemNoStr.length() - 1);
						itemChargeModeStr = itemChargeModeStr.substring(0,itemChargeModeStr.length() - 1);
					}
					bo.setAttachItemNos(itemNoStr);
					bo.setItemChargeMode(itemChargeModeStr);
				}
				bo.setServiceMode(serviceMode);

				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				m.addAttribute("chargeForm", bo);
			}else
				m.addAttribute("chargeForm", new ChargeBillingConfBo());
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/xpchargeConf/newXpaddChargeConf";
	}

	@RequestMapping(value = "/queryChargeBillingConfHis", method = RequestMethod.GET)
	public String queryChargeBillingConfHis(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfHistoryBo bo = new ChargeBillingConfHistoryBo();
		bo.setChcNo(chcNo);
		m.addAttribute("searchForm", bo);
		return "/bil/xpchargeConf/newXpaddChargeConfHis";
	}


	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}


	/**
	 * @Description:获取下级
	 * @Author: tianyoupeng
	 * @Time: 2017/6/19 10:13
	 */
	private List<String> getSubOrg(String orgCode) {
		List<String> orgList = new ArrayList<String>();
		if (StringUtil.isNotBlank(orgCode)) {
			String[] orgArr = orgCode.split(",");
			for (String orgCodeTemp : orgArr) {
				List<OrgBo> orgBoList = orgService.getSubOrg(orgCodeTemp, null, null);
				if (!CollectionUtils.isEmpty(orgBoList)) {
					for (OrgBo bo : orgBoList) {
						orgList.add(bo.getOrgCode());
					}
				}
			}
		} else {
			return null;
		}
		orgList.add(orgCode);
		return orgList;
	}


	// --------------------------------尖峰电价设置-----------------------------------------

	/**
	 * @Description 尖峰电价设置页面
	 * <AUTHOR>
	 * @Date 2022/6/17 16:29
	 * @param m
	 */
	@RequestMapping(value = "/peaksetpage", method = RequestMethod.GET)
	public String peakSetPage(Model m) {
		PeakTimeBo peakTimeBo = peakService.queryPeakTimeEffective();
		logger.debug("------peaksetpage>>>>>>>>>>peakTimeBo:" + IJsonUtil.obj2Json(peakTimeBo));
		String peakBillingTimeSlot = sysParamService.getSysParamsValues("peakBillingTimeSlot");
		m.addAttribute("peakBillingTimeSlot", peakBillingTimeSlot);
		String peakBillingRate = sysParamService.getSysParamsValues("peakBillingRate");
		m.addAttribute("peakBillingRate", peakBillingRate);
		m.addAttribute("peakTimeForm",peakTimeBo);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/xpchargeConf/peakSetPage";
	}

	/**
	 * @Description 尖峰电价关联页面
	 * <AUTHOR>
	 * @Date 2022/6/17 16:29
	 * @param m
	 */
	@RequestMapping(value = "/peakrelpage", method = RequestMethod.GET)
	public String peakRelPage(Model m) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		XpChargeBillingConfQueryCondition condition = new XpChargeBillingConfQueryCondition();
		AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
		if(orgBo != null){
			condition.setOrgCode(orgBo.getOrgCode());
			condition.setOrgCodeName(orgBo.getOrgShortName());
		}
		m.addAttribute("searchUnPeakRelForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/xpchargeConf/peakRelPage";
	}

	/**
	 * @Description 查询计费关联列表
	 * <AUTHOR>
	 * @Date 2022/6/22 14:16
	 * @param params
	 */
	@RequestMapping(value = "/peakrellist")
	public @ItemResponseBody QueryResultObject queryPeakRelList(
			@QueryRequestParam("params") RequestCondition params){
		XpChargeBillingConfQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				condition.setOrgCodeList(orgCodeList);
				condition.setOrgCode(null);
			}
		}
		List<PeakRelBo> peakRelBos = new ArrayList<>();
		int count = peakService.countPagePeakRel(condition);
		if (count > 0){
			peakRelBos = peakService.pagePeakRelBoList(condition);
		}
		return RestUtils.wrappQueryResult(peakRelBos, count);
	}

	/**
	 * @Description 查询计费未关联列表
	 * <AUTHOR>
	 * @Date 2022/6/22 14:16
	 * @param params
	 */
	@RequestMapping(value = "/unpeakrellist")
	public @ItemResponseBody QueryResultObject queryUnPeakRelList(
			@QueryRequestParam("params") RequestCondition params){
		XpChargeBillingConfQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		if (StringUtil.isNotBlank(condition.getOrgCode())){
			List<String> orgCodeList = getSubOrg(condition.getOrgCode());
			if (CollectionUtils.isNotEmpty(orgCodeList)){
				condition.setOrgCodeList(orgCodeList);
				condition.setOrgCode(null);
			}
		}
		List<PeakRelBo> peakRelBos = new ArrayList<>();
		int count = peakService.countPageUnPeakRel(condition);
		if (count > 0){
			peakRelBos = peakService.pageUnPeakRelBoList(condition);
		}
		return RestUtils.wrappQueryResult(peakRelBos, count);
	}

	/**
	 * @Description 保存尖峰计费时间配置
	 * <AUTHOR>
	 * @Date 2022/6/14 17:45
	 * @param bo
	 */
	@RequestMapping(value = "/savepeaktime", method = RequestMethod.POST)
	public @ResponseBody WrappedResult savePeakTime(@RequestBody PeakTimeBo bo){
		logger.debug("peakTimeBo:" + IJsonUtil.obj2Json(bo));
    /*	List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		PeakTimeBo bo = new PeakTimeBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}*/
		AccountBo currentAccount = authentication.getCurrentAccount();
		bo.setOperator(currentAccount.getAccountName());
		peakService.savePeakTime(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * @Description 保存尖峰计费关联
	 * <AUTHOR>
	 * @Date 2022/6/14 17:45
	 * @param chcNos
	 */
	@RequestMapping(value = "/savepeakrel", method = RequestMethod.POST)
	public @ResponseBody WrappedResult savePeakRel(@RequestParam("chcNos") String chcNos) {
		try{
			// 获取当前请求的属性
			ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			// 从请求属性中获取 HttpServletRequest 对象
			HttpServletRequest request = attrs.getRequest();
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			String unRelChcNos = peakService.addPeakRels(chcNos,map);
			if (StringUtil.isNotEmpty(unRelChcNos)){
				return WrappedResult.successWrapedResult(unRelChcNos.substring(1));
			}
			return WrappedResult.successWrapedResult(true);
		}catch (Exception e){
			logger.error("savepeakrel---",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * @Description 移除尖峰计费关联
	 * <AUTHOR>
	 * @Date 2022/6/14 17:45
	 * @param chcNos
	 */
	@RequestMapping(value = "/removepeakrel", method = RequestMethod.POST)
	public @ResponseBody WrappedResult removePeakRel(@RequestParam("chcNos") String chcNos) {
		try{
			// 获取当前请求的属性
			ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			// 从请求属性中获取 HttpServletRequest 对象
			HttpServletRequest request = attrs.getRequest();
			String networkIp=IPUtil.getIpAdrress(request);
			String intranetIp=String.valueOf(InetAddress.getLocalHost().getHostAddress());
			AccountBo userBo = authentication.getCurrentAccount();
			Map<String,Object> map=new HashMap<String,Object>();
			map.put("networkIp",networkIp);
			map.put("intranetIp",intranetIp);
			map.put("operator",userBo.getAccountName());
			peakService.delPeakRel(chcNos,map);
			return WrappedResult.successWrapedResult(true);
		}catch (Exception e){
			logger.error("removepeakrel---",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}

	}
}
