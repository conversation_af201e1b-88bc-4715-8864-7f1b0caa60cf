package com.ls.ner.billing.config.bo;

import java.util.List;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

public class BillingConfigQueryCondition extends QueryCondition {

	private String autoModelNo;
	private String autoModelName;
	
	private String unionPrice;
	
	private String billingNo;
	private String billingConfigName;
	
	private String subBe;
	private String orgCode;
	private String orgCodeName;
    private List<String> orgCodes;
    private int rows;
    private String applyDate;
    private String isAdjusted;
    private String orgAutoModelKey;
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getAutoModelNo() {
		return autoModelNo;
	}
	public void setAutoModelNo(String autoModelNo) {
		this.autoModelNo = autoModelNo;
	}
	public String getAutoModelName() {
		return autoModelName;
	}
	public void setAutoModelName(String autoModelName) {
		this.autoModelName = autoModelName;
	}
	public String getUnionPrice() {
		return unionPrice;
	}
	public void setUnionPrice(String unionPrice) {
		this.unionPrice = unionPrice;
	}
	public String getBillingNo() {
		return billingNo;
	}
	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}
	public String getBillingConfigName() {
		return billingConfigName;
	}
	public void setBillingConfigName(String billingConfigName) {
		this.billingConfigName = billingConfigName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public List<String> getOrgCodes() {
		return orgCodes;
	}
	public void setOrgCodes(List<String> orgCodes) {
		this.orgCodes = orgCodes;
	}
	public String getSubBe() {
		return subBe;
	}
	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}
	public int getRows() {
		return rows;
	}
	public void setRows(int rows) {
		this.rows = rows;
	}
	public String getApplyDate() {
		return applyDate;
	}
	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}
	public String getIsAdjusted() {
		return isAdjusted;
	}
	public void setIsAdjusted(String isAdjusted) {
		this.isAdjusted = isAdjusted;
	}
	public String getOrgAutoModelKey() {
		return orgAutoModelKey;
	}
	public void setOrgAutoModelKey(String orgAutoModelKey) {
		this.orgAutoModelKey = orgAutoModelKey;
	}
	
}
