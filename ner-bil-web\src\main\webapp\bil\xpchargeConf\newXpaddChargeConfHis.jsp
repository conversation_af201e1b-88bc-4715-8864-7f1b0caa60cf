<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="历史修改列表"></ls:head>
<ls:body>
    <ls:form  id="searchForm" name="searchForm">
        <ls:text name="chcNo" property="chcNo" type="hidden" />
        <table>
            <tr>
                <td>
                    <ls:grid url="" name="chargeHisGrid" height="250px" showCheckBox="false"
                             width="100%" primaryKey="chcNo">
                        <ls:column caption="chcNo" name="chcNo" hidden="true"/>
                        <ls:column caption="修改人" name="operNo"/>
                        <ls:column caption="修改时间" name="dataOperTime"/>
                        <ls:column caption="修改类型" name="dataOperType" formatFunc="showDetail"/>
                        <ls:pager pageSize="15" pageIndex="1"></ls:pager>
                    </ls:grid>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>

        query();

        function query(){
            chargeHisGrid.query("~/billing/xpcbc/his/queryChargeBillingConfsHisList",searchForm.getFormData());
        }

        function showDetail(rowdata){
            if('I'== rowdata.dataOperType){
                return '新增';
            }else{
                return '修改';
            }
        }
    </ls:script>
</ls:body>
</html>