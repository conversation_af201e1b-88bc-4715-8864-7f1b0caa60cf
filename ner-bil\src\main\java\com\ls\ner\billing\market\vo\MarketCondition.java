package com.ls.ner.billing.market.vo;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.util.List;


/**营销管理查询条件Vo
 * description
 * 创建时间 2016年4月7日下午3:39:11
 * 创建人 lise
 */
public class MarketCondition extends QueryCondition{

	private String  orgCode;
	private String cpnNo; // 优惠券编号
	private String cpnName; // 优惠券名称
	private String cpnType;
	private String cpnAmt;
	private String eftDate;
	private String invDate;
	private String cpnNum;
	private String limGetNum;
	private String alrdyGetNum;
	private String cpnMarks;
	private String cpnStatus;
	private String pushStatus;
	private String creTime;
	private String creEmp;
	private String orgCodes;
	private String querySubOrgFLag;//查詢上級單位
	private String rentType;
	private String putEmpType;
	private String custName;
	private String mobile;
	private String getFlag;
	private String appNo;

	private String busiType;// 优惠券业务类型
	private String cpnId; // 优惠券主键
	private String getId; // 优惠券发放主键
	private String getChannel; // 来源

	private String pushId; // 优惠券推广ID
	private String actId; // 活动ID
	private String aId; // 活动ID

	private String buildId;
	private String city;
	private String stationId;
	private String cpnAmtBgn;
	private String cpnAmtEnd;
	private String allFlag;

	/**
	 * 是否是管理优惠券页面
	 */
	private String allCoupon;

	/**
	 * 活动类型，actType
	 */
	private String actType;

	private List<String> cpnIdList;  // 批量查询 add biaoxiangd 2017-09-27

	private String cpnList;
	private String orgName;
	private String useCount;
	private String useMoney;
	private String cityName;
	private String stationName;
	private String citys;
	private String stations;
	private String custSearchCondition;
	private String custSearchResult;
	private String orgCodeName;
	private String areaCode;	//城市编码
	private String removeStations;//排除的站点列表
	private String removeCitys;	//排除的站点
	private String stationNo;	//站点编号
	private String stationAddr;	//站点地址
	private String sourceApi;//源自扫链接二维码还是领劵中心领取
	private String cpnPurpose;//优惠卷用途 1.首次添加爱车 2.其他


	public String getCpnPurpose() {
		return cpnPurpose;
	}

	public void setCpnPurpose(String cpnPurpose) {
		this.cpnPurpose = cpnPurpose;
	}

	public String getSourceApi() {
		return sourceApi;
	}

	public void setSourceApi(String sourceApi) {
		this.sourceApi = sourceApi;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getStationNo() {
		return stationNo;
	}

	public void setStationNo(String stationNo) {
		this.stationNo = stationNo;
	}

	public String getStationAddr() {
		return stationAddr;
	}

	public void setStationAddr(String stationAddr) {
		this.stationAddr = stationAddr;
	}

	public String getRemoveCitys() {
		return removeCitys;
	}

	public void setRemoveCitys(String removeCitys) {
		this.removeCitys = removeCitys;
	}

	public String getRemoveStations() {
		return removeStations;
	}

	public void setRemoveStations(String removeStations) {
		this.removeStations = removeStations;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getOrgCodeName() {
		return orgCodeName;
	}

	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}


	public String getUseCount() {
		return useCount;
	}

	public void setUseCount(String useCount) {
		this.useCount = useCount;
	}

	public String getUseMoney() {
		return useMoney;
	}

	public void setUseMoney(String useMoney) {
		this.useMoney = useMoney;
	}

	public String getCpnList() {
		return cpnList;
	}

	public void setCpnList(String cpnList) {
		this.cpnList = cpnList;
	}

	public String getActType() {
		return actType;
	}

	public void setActType(String actType) {
		this.actType = actType;
	}

	public List<String> getCpnIdList() {
		return cpnIdList;
	}

	public void setCpnIdList(List<String> cpnIdList) {
		this.cpnIdList = cpnIdList;
	}

	public String getAllCoupon() {
		return allCoupon;
	}

	public void setAllCoupon(String allCoupon) {
		this.allCoupon = allCoupon;
	}
	public String getPushId() {
		return pushId;
	}

	public void setPushId(String pushId) {
		this.pushId = pushId;
	}

	public String getActId() {
		return actId;
	}

	public void setActId(String actId) {
		this.actId = actId;
	}

	public String getaId() {
		return aId;
	}

	public void setaId(String aId) {
		this.aId = aId;
	}

	public String getGetChannel() {
		return getChannel;
	}

	public void setGetChannel(String getChannel) {
		this.getChannel = getChannel;
	}

	public String getGetId() {
		return getId;
	}

	public void setGetId(String getId) {
		this.getId = getId;
	}

	public String getCpnId() {
		return cpnId;
	}

	public void setCpnId(String cpnId) {
		this.cpnId = cpnId;
	}

	public String getBusiType() {
		return busiType;
	}

	public void setBusiType(String busiType) {
		this.busiType = busiType;
	}
	public String getAppNo() {
		return appNo;
	}
	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}
	public String getGetFlag() {
		return getFlag;
	}
	public void setGetFlag(String getFlag) {
		this.getFlag = getFlag;
	}
	public String getPutEmpType() {
		return putEmpType;
	}
	public void setPutEmpType(String putEmpType) {
		this.putEmpType = putEmpType;
	}

	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getRentType() {
		return rentType;
	}
	public void setRentType(String rentType) {
		this.rentType = rentType;
	}
	public String getQuerySubOrgFLag() {
		return querySubOrgFLag;
	}
	public void setQuerySubOrgFLag(String querySubOrgFLag) {
		this.querySubOrgFLag = querySubOrgFLag;
	}
	public String getOrgCodes() {
		return orgCodes;
	}
	public void setOrgCodes(String orgCodes) {
		this.orgCodes = orgCodes;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getCpnNo() {
		return cpnNo;
	}
	public void setCpnNo(String cpnNo) {
		this.cpnNo = cpnNo;
	}
	public String getCpnName() {
		return cpnName;
	}
	public void setCpnName(String cpnName) {
		this.cpnName = cpnName;
	}
	public String getCpnType() {
		return cpnType;
	}
	public void setCpnType(String cpnType) {
		this.cpnType = cpnType;
	}
	public String getCpnAmt() {
		return cpnAmt;
	}
	public void setCpnAmt(String cpnAmt) {
		this.cpnAmt = cpnAmt;
	}
	public String getEftDate() {
		return eftDate;
	}
	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}
	public String getInvDate() {
		return invDate;
	}
	public void setInvDate(String invDate) {
		this.invDate = invDate;
	}
	public String getCpnNum() {
		return cpnNum;
	}
	public void setCpnNum(String cpnNum) {
		this.cpnNum = cpnNum;
	}
	public String getLimGetNum() {
		return limGetNum;
	}
	public void setLimGetNum(String limGetNum) {
		this.limGetNum = limGetNum;
	}
	public String getAlrdyGetNum() {
		return alrdyGetNum;
	}
	public void setAlrdyGetNum(String alrdyGetNum) {
		this.alrdyGetNum = alrdyGetNum;
	}
	public String getCpnMarks() {
		return cpnMarks;
	}
	public void setCpnMarks(String cpnMarks) {
		this.cpnMarks = cpnMarks;
	}
	public String getCpnStatus() {
		return cpnStatus;
	}
	public void setCpnStatus(String cpnStatus) {
		this.cpnStatus = cpnStatus;
	}
	public String getPushStatus() {
		return pushStatus;
	}
	public void setPushStatus(String pushStatus) {
		this.pushStatus = pushStatus;
	}
	public String getCreTime() {
		return creTime;
	}
	public void setCreTime(String creTime) {
		this.creTime = creTime;
	}
	public String getCreEmp() {
		return creEmp;
	}
	public void setCreEmp(String creEmp) {
		this.creEmp = creEmp;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getCitys() {
		return citys;
	}

	public void setCitys(String citys) {
		this.citys = citys;
	}

	public String getStations() {
		return stations;
	}

	public void setStations(String stations) {
		this.stations = stations;
	}

	public String getCustSearchCondition() {
		return custSearchCondition;
	}

	public void setCustSearchCondition(String custSearchCondition) {
		this.custSearchCondition = custSearchCondition;
	}

	public String getCustSearchResult() {
		return custSearchResult;
	}

	public void setCustSearchResult(String custSearchResult) {
		this.custSearchResult = custSearchResult;
	}


	public String getBuildId() {
		return buildId;
	}

	public void setBuildId(String buildId) {
		this.buildId = buildId;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getCpnAmtBgn() {
		return cpnAmtBgn;
	}

	public void setCpnAmtBgn(String cpnAmtBgn) {
		this.cpnAmtBgn = cpnAmtBgn;
	}

	public String getCpnAmtEnd() {
		return cpnAmtEnd;
	}

	public void setCpnAmtEnd(String cpnAmtEnd) {
		this.cpnAmtEnd = cpnAmtEnd;
	}

	public String getAllFlag() {
		return allFlag;
	}

	public void setAllFlag(String allFlag) {
		this.allFlag = allFlag;
	}
}
