<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费基本信息">
<style>
    .amber .checkBoxList{
        padding: 4px 2px;
    }
</style>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<div style="width: 99.50%">
		<ls:form id="chargeForm" name="chargeForm">
			<ls:title text="资费基本信息"></ls:title>
			<ls:text type="hidden" name="systemId" property="systemId"/>
			<ls:text type="hidden" name="orgCode" property="orgCode"></ls:text>
			<ls:text type="hidden" name="chcStatus" property="chcStatus"></ls:text>
			<ls:text type="hidden" name="attachItemNos" property="attachItemNos"/>
			<ls:text type="hidden" name="attachItemPrices" property="attachItemPrices"/>
			<ls:text type="hidden" name="freeNum" property="freeNum"/>
			<ls:text type="hidden" name="chargePeriodsStr"/>
			<ls:text type="hidden" name="itemChargeMode" property="itemChargeMode"/>
			<table class="tab_search">
				<tr>
					<td width="130px;"><ls:label text="充电计费编号" ref="chcNo" /></td>
					<td colspan="2"><ls:text name="chcNo" property="chcNo" readOnly="true"/></td>
					<td><ls:label text="充电计费名称" ref="chcName" /></td>
					<td colspan="2"><ls:text name="chcName" property="chcName" required="true" maxlength="50"/></td>
				</tr>
				<tr>
					<td><ls:label text="桩生效日期" ref="eftDate" /></td>
					<td colspan="2">
                        <div style="float: left;width: 30%">
                            <ls:checklist  type="checkbox"
                                          value="1"
                                          onchanged="getImmediate">
                                <ls:checkitem text="立即生效" value="1"/>
                            </ls:checklist>
                        </div>
                        <div style="float: left;width: 70%">
                        <ls:date name="eftDate" property="eftDate" required="true" format="yyyy-MM-dd HH:mm" onValidate="VD.dateTimeOfTodayValidate(eftDate)"/>
                        </div>
                    </td>
					<td><ls:label text="上个计费编号" ref="upChcNo" /></td>
					<td colspan="2"><ls:text name="upChcNo" property="upChcNo" readOnly="true"/></td>
				</tr>
				<tr>
					<td><ls:label text="终端显示的充电服务费" ref="chcRemark"/></td>
					<td colspan="5"><ls:text  name="chcRemark" property="chcRemark" type="textarea"
                                              maxlength="160"></ls:text></td>
				</tr>
			</table>
			<ls:title text="充电计费规则"></ls:title>
			<table class="tab_search">
				<tr>
					<td width="130px;"><ls:label text="费控模式" ref="billCtlMode" /></td>
					<td colspan="2">
						<ls:checklist type="radio" name="billCtlMode" property="billCtlMode" onchanged="changeBillCtlMode" required="true">
							<ls:checkitems property="billCtlModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeShowOne" width="100px;"><ls:label text="桩推送数据类型" ref="gatherDataType" /></td>
					<td id="gatherDataTypeShowTwo" colspan="2">
						<ls:checklist type="radio" name="gatherDataType" property="gatherDataType" required="true"
                                      onchanged="changePriceTrShow" value="2">
							<ls:checkitem value="1" text="计量表示数"></ls:checkitem>
							<ls:checkitem value="2" text="电量"></ls:checkitem>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeHide" colspan="3" style="display: none;"></td>
				</tr>
				<tr>
					<td width="100px;"><ls:label text="电费计费模式" ref="chargeMode" /></td>
					<td colspan="2">
						<ls:checklist type="radio" name="chargeMode" property="chargeMode" required="true" onchanged="changePriceTrShow">
							<ls:checkitems property="chcBillingChargeModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
					<td id="servicePriceDiv" width="100px;"><ls:label text="服务费计费模式" ref="serviceMode" /></td>
					<td id="servicePriceDivMode" colspan="2">
						<ls:checklist type="radio" name="serviceMode" property="serviceMode" required="true" onchanged="changeServicePriceTrShow">
							<ls:checkitems property="chcBillingChargeModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeHide1" colspan="3" style="display: none;"></td>
				</tr>
				<tr id="priceTr">
					<td><ls:label text="计费单价" ref="chargePrice" /></td>
					<td colspan="4"><ls:text name="chargePrice" property="chargePrice" required="true" type="number" maxlength="11"  max="9999" onValidate="chargePriceValidate()"/></td>
					<td>元/kWh</td>
				</tr>
				<tr id="timeTr">
					<td><ls:label text="分时时段设置"/></td>
					<td colspan="5">
						<ls:grid url="" name="timeShareGrid" primaryKey="sn" cellEdit="true" treeGrid="true"
                                 expandColumn="value"  expand="true"  treeGridModel="adjacency" height="210px;"
                                 width="100%"  export="false">
							<ls:gridToolbar name="timeShareGridBar">
								<ls:gridToolbarItem imageKey="add" onclick="doAddTimeShareGrid" text="新增"></ls:gridToolbarItem>
								<ls:gridToolbarItem imageKey="delete" onclick="doDelTimeShareGrid" text="删除"></ls:gridToolbarItem>
							</ls:gridToolbar>
							<ls:column caption="排序号" name="sn" hidden="true"/>
							<ls:column caption="开始时间" name="beginTime" editableEdit="false" readOnly="true"/>
							<ls:column caption="结束时间" name="endTime">
								<ls:textEditor onValidate="endTimeValidate()" required="true"/>
							</ls:column>
							<ls:column caption="时段标识" name="timeFlag" >
								<ls:selectEditor property="timeFlagList" name="timeFlag" valueMember="codeValue" required="true" displayMember="codeName" onchanged="changeTimeFlag">
								</ls:selectEditor>
							</ls:column>
							<ls:column caption="时段电价（元）" name="price">
								<ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
							</ls:column>
							<ls:column caption="时段服务费单价（元）" name="serPrice">
								<ls:textEditor maxlength="11" required="true"  type="number" max="9999" onValidate="priceValidate()"/>
							</ls:column>
						</ls:grid>
					</td>
				</tr>
			</table>
			<div id="serviceItemTitleName" style="display: block">
				<ls:title text="服务项目"></ls:title>
			</div>
			<div id="otherItemTitleName" style="display: none">
				<ls:title text="其它项目"></ls:title>
			</div>
			<table align="center" class="tab_search otherItemTitleName" >
				<tr>
					<td><ls:grid url="" name="serviceGrid" primaryKey="itemNo"  cellEdit="true" treeGrid="true"
                                 expandColumn="value"  expand="true"  treeGridModel="adjacency" height="60px;"
                                 width="99%"  export="false">
						<ls:gridToolbar name="serviceGridBar">
							<ls:gridToolbarItem imageKey="add" onclick="doAddSerGrid" text="新增"></ls:gridToolbarItem>
							<ls:gridToolbarItem imageKey="delete" onclick="doDelSerGrid" text="删除"></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column caption="费用项" name="itemNo"  hidden="true" />
						<ls:column caption="费用项名称" name="itemName"  readOnly="true"/>
						<ls:column caption="费用项编号" name="itemNo"  readOnly="true" />
						<ls:column caption="费用单价（元）" name="servicePrice">
							<ls:textEditor  maxlength="11" required="true" type="number"  max="9999"  onValidate="priceValidate()"/>
						</ls:column>
						<%--<ls:column caption="免费量" name="freeNum" hidden="true">
							<ls:textEditor  maxlength="11" required="true" type="number"  value="0" max="9999"  onValidate="priceValidate()"/>
						</ls:column>--%>
						<ls:column caption="费用项单位" name="itemUnitName" readOnly="true"/>
					</ls:grid></td>
				</tr>
			</table>
			<table style="height: 60px">
				<tr>
					<td>
						<div class="pull-right" style="position: fixed;    bottom: 10px;    right: 0px;">
							<ls:button text="返回" onclick="doReturn" />
							<ls:button text="保存" onclick="doSave" />
							<ls:button text="发布" onclick="doRelease" />
<%--							<ls:button text="立即生效" onclick="doEffective" />--%>
						</div>
					</td>
				</tr>
			</table>
		</ls:form>
	</div>
	<ls:script>
        var astPath = '${astPath }';
        init();
        function init(){

            if(systemId.getValue() == null || systemId.getValue() == ''){
                var item = {
                    sn:1,
                    beginTime:"00:00",
                    endTime:"24:00",
                    timeFlag:""
                };
                timeShareGrid.addRow(item,"last");
                chargeMode.setValue('0201');//标准
                serviceMode.setValue('0201');//标准
                billCtlMode.setValue('2');//费控模式   1表示本地，2远程
            }else{
                var mode = chargeMode.getValue();
                if(mode != '0201'){
                    timeShareGridSearch();
                }else{
                    var item = {
                        sn:1,
                        beginTime:"00:00",
                        endTime:"24:00"
                    };
                    timeShareGrid.addRow(item,"last");
                }
                serviceGridSearch();
            }
            changePriceTrShow();
            changeBillCtlMode();


            var serMode = serviceMode.getValue();

            if(systemId.getValue() == null || systemId.getValue() == '' || serMode == "0201"){
                timeShareGrid.setHideCol(['serPrice']);
            }
        }

        var isImmediate = true;
        eftDate.hide();
        function getImmediate(){
            if (isImmediate) {
                isImmediate = false;
                eftDate.show();
            } else {
                isImmediate = true;
                eftDate.hide();
            }
        }

        function doSave(){
            if(!chargeForm.valid()){
                return;
            }
            var mode = chargeMode.getValue();
            var serMode = serviceMode.getValue();
            var timeShareLength = 0;
            if(mode == '0202'){//分时
                timeShareLength = timeShareGrid.getItems().length;
                var timeShareItems = timeShareGrid.getItems();
                var serPriceStr = "";
                var poPrice = ""; // 尖电价
                var pePrice = ""; // 峰电价
                var avPrice = ""; // 平电价
                var vaPrice = ""; // 谷电价
                var poSerPrice = ""; // 尖服务价
                var peSerPrice = ""; // 峰服务价
                var avSerPrice = ""; // 平服务价
                var vaSerPrice = ""; // 谷服务价
                for(var i = 0;i < timeShareItems.length;i++){
                    var endTime = timeShareItems[i].endTime;
                    var price = timeShareItems[i].price;
                    var serPrice = timeShareItems[i].serPrice;
                    if(LS.isEmpty(endTime)){
                        LS.message("error","分时时段设置中的结束时间不能为空");
                        return;
                    }
                    if(LS.isEmpty(price)){
                        LS.message("error","分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(LS.isEmpty(serPrice) && serMode == '0202'){
                        LS.message("error","服务费分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(timeShareItems[i].timeFlag=="尖"){
                        timeShareItems[i].timeFlag="1";
                    }else if(timeShareItems[i].timeFlag=="峰"){
                        timeShareItems[i].timeFlag="2";
                    }else if(timeShareItems[i].timeFlag=="平"){
                        timeShareItems[i].timeFlag="3";
                    }else if(timeShareItems[i].timeFlag=="谷"){
                        timeShareItems[i].timeFlag="4";
                    }
                    if(i+1 != timeShareLength){
                        serPriceStr += timeShareItems[i].serPrice+"#";
                    }else{
                        serPriceStr += timeShareItems[i].serPrice;
                    }

                    // 时段标识价格判断
                    if(timeShareItems[i].timeFlag=="1"){
                        if(LS.isEmpty(poPrice)){
                            poPrice = price;
                        }else{
                            if(Number(poPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(poSerPrice)){
                                poSerPrice = serPrice;
                            }else{
                                if(Number(poSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="2"){
                        if(LS.isEmpty(pePrice)){
                            pePrice = price;
                        }else{
                            if(Number(pePrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(peSerPrice)){
                                peSerPrice = serPrice;
                            }else{
                                if(Number(peSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="3"){
                        if(LS.isEmpty(avPrice)){
                            avPrice = price;
                        }else{
                            if(Number(avPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(avSerPrice)){
                                avSerPrice = serPrice;
                            }else{
                                if(Number(avSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="4"){
                        if(LS.isEmpty(vaPrice)){
                            vaPrice = price;
                        }else{
                            if(Number(vaPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(vaSerPrice)){
                                vaSerPrice = serPrice;
                            }else{
                                if(Number(vaSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }
                }
                chargePeriodsStr.setValue(JSON.stringify(timeShareItems));
            }
            var serviceItems = serviceGrid.getItems();
            var itemNos = "";
            var itemPrices = "";
            var freeNums = "";
            var iteamChargeModes = "";
            for(var i=0;i < serviceItems.length;i++){
                if(LS.isEmpty(serviceItems[i].servicePrice)){
                    LS.message("error","服务项目中的费用单价不能为空");
                    return;
                }

                var servicePrice = serviceItems[i].servicePrice;
                var reg  = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
                if(!reg.test(servicePrice)){
                    LS.message("error",serviceItems[i].itemName+"单价错误,必须为正数");
                    return;
                }

                serviceItems[i].itemChargeMode = "0201";  //标准

                if(i+1!=serviceItems.length){
                    itemNos+=serviceItems[i].itemNo+",";
                    itemPrices+=serviceItems[i].servicePrice+",";
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0") + ",";
                    iteamChargeModes+=serviceItems[i].itemChargeMode+",";
                }else{
                    itemNos+=serviceItems[i].itemNo;
                    itemPrices+=serviceItems[i].servicePrice;
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0");
                    iteamChargeModes+=serviceItems[i].itemChargeMode;
                }
            }

            if(serMode == '0202' && mode == "0202"){  //分时构造服务费
                if(itemNos.indexOf("1000001000") != -1){
                    LS.message("error","服务费计费模式为分时，其他项目中不应再有服务费选项");
                    return;
                }

                if(itemNos == ""){
                    //需要加上服务费
                    itemNos+="1000001000";
                    itemPrices+=serPriceStr;
                    freeNums+="0";
                    iteamChargeModes+="0202";
                }else{
                    //需要加上服务费
                    itemNos+=",1000001000";
                    itemPrices+=","+serPriceStr;
                    freeNums+=",0";
                    iteamChargeModes+=",0202";
                }
            }

            attachItemNos.setValue(itemNos);
            attachItemPrices.setValue(itemPrices);
            freeNum.setValue(freeNums);
            itemChargeMode.setValue(iteamChargeModes);
            chargeForm.submit("~/billing/xpcbc/saveChargeBillingConf",function(data){
                if(data.systemId){
                    if(systemId.getValue()==null || systemId.getValue()==''){
                        systemId.setValue(data.systemId);
                        chcNo.setValue(data.chcNo);
                        chcStatus.setValue(data.chcStatus);
                        LS.message("info","新建成功");
                        LS.parent().doSearch();
                    }else{
                        LS.message("info","修改成功");
                        LS.parent().doSearch();
                    }
                }else{
                    LS.message("error","保存失败");
                }
            });
        }

        function doEffective(){
            eftDate.setVisible(false);
            if(!chargeForm.valid()){
                return;
            }
            var mode = chargeMode.getValue();
            var serMode = serviceMode.getValue();
            var timeShareLength = 0;
            if(mode == '0202'){//分时
                timeShareLength = timeShareGrid.getItems().length;
                var timeShareItems = timeShareGrid.getItems();
                var serPriceStr = "";
                var poPrice = ""; // 尖电价
                var pePrice = ""; // 峰电价
                var avPrice = ""; // 平电价
                var vaPrice = ""; // 谷电价
                var poSerPrice = ""; // 尖服务价
                var peSerPrice = ""; // 峰服务价
                var avSerPrice = ""; // 平服务价
                var vaSerPrice = ""; // 谷服务价
                for(var i = 0;i < timeShareItems.length;i++){
                    var endTime = timeShareItems[i].endTime;
                    var price = timeShareItems[i].price;
                    var serPrice = timeShareItems[i].serPrice;
                    if(LS.isEmpty(endTime)){
                        LS.message("error","分时时段设置中的结束时间不能为空");
                        return;
                    }
                    if(LS.isEmpty(price)){
                        LS.message("error","分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(LS.isEmpty(serPrice) && serMode == '0202'){
                        LS.message("error","服务费分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(timeShareItems[i].timeFlag=="尖"){
                        timeShareItems[i].timeFlag="1";
                    }else if(timeShareItems[i].timeFlag=="峰"){
                        timeShareItems[i].timeFlag="2";
                    }else if(timeShareItems[i].timeFlag=="平"){
                        timeShareItems[i].timeFlag="3";
                    }else if(timeShareItems[i].timeFlag=="谷"){
                        timeShareItems[i].timeFlag="4";
                    }
                    if(i+1 != timeShareLength){
                        serPriceStr += timeShareItems[i].serPrice+"#";
                    }else{
                        serPriceStr += timeShareItems[i].serPrice;
                    }

                    // 时段标识价格判断
                    if(timeShareItems[i].timeFlag=="1"){
                        if(LS.isEmpty(poPrice)){
                            poPrice = price;
                        }else{
                            if(Number(poPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(poSerPrice)){
                                poSerPrice = serPrice;
                            }else{
                                if(Number(poSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="2"){
                        if(LS.isEmpty(pePrice)){
                            pePrice = price;
                        }else{
                            if(Number(pePrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(peSerPrice)){
                                peSerPrice = serPrice;
                            }else{
                                if(Number(peSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="3"){
                        if(LS.isEmpty(avPrice)){
                            avPrice = price;
                        }else{
                            if(Number(avPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(avSerPrice)){
                                avSerPrice = serPrice;
                            }else{
                                if(Number(avSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="4"){
                        if(LS.isEmpty(vaPrice)){
                            vaPrice = price;
                        }else{
                            if(Number(vaPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(vaSerPrice)){
                                vaSerPrice = serPrice;
                            }else{
                                if(Number(vaSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }
                }
                chargePeriodsStr.setValue(JSON.stringify(timeShareItems));
            }
            var serviceItems = serviceGrid.getItems();
            var itemNos = "";
            var itemPrices = "";
            var freeNums = "";
            var iteamChargeModes = "";
            for(var i=0;i < serviceItems.length;i++){
                if(LS.isEmpty(serviceItems[i].servicePrice)){
                    LS.message("error","服务项目中的费用单价不能为空");
                    return;
                }

                var servicePrice = serviceItems[i].servicePrice;
                var reg  = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
                if(!reg.test(servicePrice)){
                    LS.message("error",serviceItems[i].itemName+"单价错误,必须为正数");
                    return;
                }

                serviceItems[i].itemChargeMode = "0201";  //标准

                if(i+1!=serviceItems.length){
                    itemNos+=serviceItems[i].itemNo+",";
                    itemPrices+=serviceItems[i].servicePrice+",";
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0") + ",";
                    iteamChargeModes+=serviceItems[i].itemChargeMode+",";
                }else{
                    itemNos+=serviceItems[i].itemNo;
                    itemPrices+=serviceItems[i].servicePrice;
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0");
                    iteamChargeModes+=serviceItems[i].itemChargeMode;
                }
            }

            if(serMode == '0202' && mode == "0202"){  //分时构造服务费
                if(itemNos.indexOf("1000001000") != -1){
                    LS.message("error","服务费计费模式为分时，其他项目中不应再有服务费选项");
                    return;
                }

                if(itemNos == ""){
                    //需要加上服务费
                    itemNos+="1000001000";
                    itemPrices+=serPriceStr;
                    freeNums+="0";
                    iteamChargeModes+="0202";
                }else{
                    //需要加上服务费
                    itemNos+=",1000001000";
                    itemPrices+=","+serPriceStr;
                    freeNums+=",0";
                    iteamChargeModes+=",0202";
                }
            }

            attachItemNos.setValue(itemNos);
            attachItemPrices.setValue(itemPrices);
            freeNum.setValue(freeNums);
            itemChargeMode.setValue(iteamChargeModes);
            chargeForm.submit("~/billing/xpcbc/effectiveChargeBillingConf",function(data){
                if(data.systemId){
                    systemId.setValue(data.systemId);
                    chcNo.setValue(data.chcNo);
                    chcStatus.setValue(data.chcStatus);
                    LS.message("info","操作成功");
                    LS.parent().doSearch();
                }else{
                    LS.message("error","保存失败");
                }
            });
        }

        function doRelease(){
            if(isImmediate){
                <%--if(${chargeForm.upChcNo != null && chargeForm.upChcNo != ''}){
                    LS.message("info","调价的电费无法立即生效");
                    return ;
                }--%>
                eftDate.setVisible(false);
            }
            if(!chargeForm.valid()){
                return ;
            }
            var mode = chargeMode.getValue();
            var serMode = serviceMode.getValue();
            var timeShareLength = 0;
            if(mode == '0202'){//分时
                timeShareLength = timeShareGrid.getItems().length;
                var timeShareItems = timeShareGrid.getItems();
                var serPriceStr = "";
                var poPrice = ""; // 尖电价
                var pePrice = ""; // 峰电价
                var avPrice = ""; // 平电价
                var vaPrice = ""; // 谷电价
                var poSerPrice = ""; // 尖服务价
                var peSerPrice = ""; // 峰服务价
                var avSerPrice = ""; // 平服务价
                var vaSerPrice = ""; // 谷服务价
                for(var i = 0;i < timeShareItems.length;i++){
                    var endTime = timeShareItems[i].endTime;
                    var price = timeShareItems[i].price;
                    var serPrice = timeShareItems[i].serPrice;
                    if(LS.isEmpty(endTime)){
                        LS.message("error","分时时段设置中的结束时间不能为空");
                        return;
                    }
                    if(LS.isEmpty(price)){
                        LS.message("error","分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(LS.isEmpty(serPrice) && serMode == '0202'){
                        LS.message("error","服务费分时时段设置中的计费单价不能为空");
                        return;
                    }
                    if(timeShareItems[i].timeFlag=="尖"){
                        timeShareItems[i].timeFlag="1";
                    }else if(timeShareItems[i].timeFlag=="峰"){
                        timeShareItems[i].timeFlag="2";
                    }else if(timeShareItems[i].timeFlag=="平"){
                        timeShareItems[i].timeFlag="3";
                    }else if(timeShareItems[i].timeFlag=="谷"){
                        timeShareItems[i].timeFlag="4";
                    }
                    if(i+1 != timeShareLength){
                        serPriceStr += timeShareItems[i].serPrice+"#";
                    }else{
                        serPriceStr += timeShareItems[i].serPrice;
                    }

                    // 时段标识价格判断
                    if(timeShareItems[i].timeFlag=="1"){
                        if(LS.isEmpty(poPrice)){
                            poPrice = price;
                        }else{
                            if(Number(poPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(poSerPrice)){
                                poSerPrice = serPrice;
                            }else{
                                if(Number(poSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="2"){
                        if(LS.isEmpty(pePrice)){
                            pePrice = price;
                        }else{
                            if(Number(pePrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(peSerPrice)){
                                peSerPrice = serPrice;
                            }else{
                                if(Number(peSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="3"){
                        if(LS.isEmpty(avPrice)){
                            avPrice = price;
                        }else{
                            if(Number(avPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(avSerPrice)){
                                avSerPrice = serPrice;
                            }else{
                                if(Number(avSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }else if(timeShareItems[i].timeFlag=="4"){
                        if(LS.isEmpty(vaPrice)){
                            vaPrice = price;
                        }else{
                            if(Number(vaPrice) != Number(price)){
                                LS.message("error","同一时段标识的充电费须一致");
                                return;
                            }
                        }
                        if(serMode == '0202'){
                            if(LS.isEmpty(vaSerPrice)){
                                vaSerPrice = serPrice;
                            }else{
                                if(Number(vaSerPrice) != Number(serPrice)){
                                    LS.message("error","同一时段标识的服务费须一致");
                                    return;
                                }
                            }
                        }
                    }
                }
                chargePeriodsStr.setValue(JSON.stringify(timeShareItems));
            }
            var serviceItems = serviceGrid.getItems();
            var itemNos = "";
            var itemPrices = "";
            var freeNums = "";
            var iteamChargeModes = "";
            for(var i=0;i < serviceItems.length;i++){
                if(LS.isEmpty(serviceItems[i].servicePrice)){
                    LS.message("error","服务项目中的费用单价不能为空");
                    return;
                }

                var servicePrice = serviceItems[i].servicePrice;
                var reg  = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/;
                if(!reg.test(servicePrice)){
                    LS.message("error",serviceItems[i].itemName+"单价错误,必须为正数");
                    return;
                }

                serviceItems[i].itemChargeMode = "0201";  //标准

                if(i+1!=serviceItems.length){
                    itemNos+=serviceItems[i].itemNo+",";
                    itemPrices+=serviceItems[i].servicePrice+",";
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0") + ",";
                    iteamChargeModes+=serviceItems[i].itemChargeMode+",";
                }else{
                    itemNos+=serviceItems[i].itemNo;
                    itemPrices+=serviceItems[i].servicePrice;
                    freeNums+=(serviceItems[i].freeNum ? serviceItems[i].freeNum : "0");
                    iteamChargeModes+=serviceItems[i].itemChargeMode;
                }
            }

            if(serMode == '0202' && mode == "0202"){  //分时构造服务费
                if(itemNos.indexOf("1000001000") != -1){
                    LS.message("error","服务费计费模式为分时，其他项目中不应再有服务费选项");
                    return;
                }

                if(itemNos == ""){
                    //需要加上服务费
                    itemNos+="1000001000";
                    itemPrices+=serPriceStr;
                    freeNums+="0";
                    iteamChargeModes+="0202";
                }else{
                    //需要加上服务费
                    itemNos+=",1000001000";
                    itemPrices+=","+serPriceStr;
                    freeNums+=",0";
                    iteamChargeModes+=",0202";
                }
            }

            attachItemNos.setValue(itemNos);
            attachItemPrices.setValue(itemPrices);
            freeNum.setValue(freeNums);
            itemChargeMode.setValue(iteamChargeModes);

            var xurl = "~/billing/xpcbc/releaseAndModifyChargeBillingConf";
            if(isImmediate){
                xurl = "~/billing/xpcbc/effectiveChargeBillingConf";
            }
            chargeForm.submit(xurl,function(data){
                if(data.systemId){
                    if(systemId.getValue()==null || systemId.getValue()==''){
                        systemId.setValue(data.systemId);
                        chcNo.setValue(data.chcNo);
                        chcStatus.setValue(data.chcStatus);
                        LS.parent().doSearch();
                    }else{
                        LS.parent().doSearch();
                    }
                    LS.message("info","发布成功。计费编号为：" + chcNo.getValue());
                    LS.window.close();
                }else{
                    LS.message("error","保存失败");
                }
            });

        }

        function doReturn(){
            setTimeout("LS.window.close()",200);
        }
        //========================================费控模式===========================================================
        function changeBillCtlMode(){
            var value = billCtlMode.getValue();
            $('#gatherDataTypeShowOne').hide();
            $('#gatherDataTypeShowTwo').hide();
            if(value == '1'){//本地
                $('#gatherDataTypeHide').show();
                $('#gatherDataTypeShowOne').hide();
                $('#gatherDataTypeShowTwo').hide();

            }else{//远程
                $('#gatherDataTypeHide').hide();
                //$('#gatherDataTypeShowOne').show();
                //$('#gatherDataTypeShowTwo').show();

            }
        }

        //========================================计费单价===========================================================
        function chargePriceValidate(){
            var bMoney = chargePrice.getValue();
            var isValid = true;
            if(!LS.isEmpty(bMoney)){
                var i = bMoney.indexOf(".");
                var str = "";
                if(i>=0){
                    str = bMoney.substring(i+1);
                }
                isValid = (str.length <= 4);
            }
            return {
                isValid : isValid,
                message : "小数位不能超过四位"
            }
        }
        //========================================分时时段设置=========================================================
        function timeShareGridSearch(){
            var params = {
                chcNo:chcNo.getValue(),
                itemNo:'1000000000',
                serviceMode:serviceMode.getValue()
            };
            LS.ajax("~/billing/xpcbc/queryChargePeriods",params,function(data){
                timeShareGrid.removeAllItems();
                if(data != null){
                    for(var i = 0 ; i < data.length ; i++){
                        timeShareGrid.addRow(data[i],"last");
                    }
                }
            });
        }

        function doAddTimeShareGrid(){
            var data = timeShareGrid.getItems();
            var mode = serviceMode.getValue();

            var item = {
                sn:data[data.length-1].sn+1,
                endTime:"24:00",
                timeFlag:""
            };

            if(mode == '0201'){
                //服务费计费模式为标准，计费服务单价取消隐藏
                timeShareGrid.setShowCol(['serPrice']);
            }

            timeShareGrid.addRow(item,"last");
            timeShareGrid.setCell(data[data.length-1].sn, "endTime", "");

            if(mode == '0201'){
                timeShareGrid.setHideCol(['serPrice']);
            }
        }

        function endTimeValidate(value,eValidator){
            var returnValid = {
                isValid : true,
                message : ""
            }
            if(LS.isEmpty(value)){
                returnValid.message = "必选填字段";
                returnValid.isValid = false;
                return returnValid;
            }
            if(!/((([0-1]\d)|(2[0-3])):[0-5]\d)|24:00/.test(value)){
                returnValid.message = "请输入正确的时间格式，如:03:00";
                returnValid.isValid = false;
                return returnValid;
            }
            var data = timeShareGrid.getItems();
            var j = 0;
            for(var i = 0;i < data.length;i++){
                if(data[i].sn == eValidator.rowid ){
                    j = i;
                    break;
                }
            }
            if(j+1 == data.length && value !='24:00'){
                returnValid.message = "分时时段最终的结束时间必须为24:00,不能更改";
                returnValid.isValid = false;
                return returnValid;
            }
            if(j!=0){
                var preEndTime = timeShareGrid.getCell(data[j-1].sn, "endTime");
                if(!LS.isEmpty(preEndTime) && !tranTime(preEndTime,value)){
                    returnValid.message = "当前的结束时间必须大于开始时间";
                    returnValid.isValid = false;
                    return returnValid;
                }
            }
            if(j+1!=data.length){
                var nextEndTime = timeShareGrid.getCell(data[j+1].sn, "endTime");
                if(!LS.isEmpty(nextEndTime) && !tranTime(value,nextEndTime)){
                    returnValid.message = "当前的结束时间必须小于下一个时间段的结束时间";
                    returnValid.isValid = false;
                    return returnValid;
                }
            }
            if(returnValid.isValid && j+1 != data.length){
                timeShareGrid.setCell(data[i+1].sn, "beginTime", value);
            }
            return returnValid;
        }

        function tranTime(minTime,maxTime){
            var min = minTime.split(":");
            var max = maxTime.split(":");
            var returnValue = true;
            if(Number(max[0]) < Number(min[0]))
                returnValue = false;
            if(Number(max[0]) == Number(min[0]) && Number(max[1]) <= Number(min[1]))
                returnValue = false;
            return returnValue;
        }

        function doDelTimeShareGrid(){
            var item = timeShareGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要删除的分时时段记录");
                return;
            }
            var items = timeShareGrid.getItems();
            if(items.length == 1){
                LS.message("error","至少需要有一条记录");
                return;
            }
            var newItems = [];
            for(var i=0;i < items.length;i++){
                if(item.sn == items[i].sn){
                    if(i==0){
                        items[1].beginTime = '00:00';
                    }else if(i==items.length-1){
                        newItems[i-1].endTime = '24:00';
                    }else{
                        items[i+1].beginTime = items[i-1].endTime;
                    }
                }else{
                    newItems.push(items[i]);
                }
            }
            timeShareGrid.removeAllItems();
            for(var i=0;i < newItems.length;i++){
                timeShareGrid.addRow(newItems[i],"last");
            }
        }

        //分时时段设置--计费单价验证
        function priceValidate(value){
            var bMoney = value;
            var isValid = true;
            if(!LS.isEmpty(bMoney)){
                var i = bMoney.indexOf(".");
                var str = "";
                if(i>=0){
                    str = bMoney.substring(i+1);
                }
                isValid = (str.length <= 4);
            }
            return {
                isValid : isValid,
                message : "小数位不能超过四位"
            }
        }

        //========================================服务项目=========================================================
        function serviceGridSearch(){
            var serMode = serviceMode.getValue();
            var itemNoValue = attachItemNos.getValue();
            var itemChargeModeValue = itemChargeMode.getValue();
            var attachItemPricesValue = attachItemPrices.getValue();
            var freeNumValue = freeNum.getValue();
            if(serMode == '0202'){

                //服务费为分时，过滤掉服务费项目
                itemNoValue = "";
                itemChargeModeValue = "";
                attachItemPricesValue = "";
                freeNumValue = "";

                var itemNoStr = attachItemNos.getValue();
                var itemChargeModeStr = itemChargeMode.getValue();
                var attachItemPricesStr = attachItemPrices.getValue();
                var freeNumStr = freeNum.getValue();

                if(itemNoStr!=null && itemNoStr!="" && itemNoStr!="null"){

                    var itemNoArr = itemNoStr.split(',');
                    var itemChargeModeArr = itemChargeModeStr.split(',');
                    var attachItemPricesArr = attachItemPricesStr.split(',');
                    var freeNumArr = freeNumStr.split(',');

                    for(var i=0;i < itemNoArr.length;i++){
                        if(itemNoArr[i] != "1000001000"){
                            itemNoValue+=itemNoArr[i]+",";
                            itemChargeModeValue+=itemChargeModeArr[i]+",";
                            attachItemPricesValue+=attachItemPricesArr[i]+",";
                            freeNumValue+=freeNumArr[i]+",";
                        }
                    }
                    if(itemNoValue != null && itemNoValue != ""){
                        //切除尾部逗号
                        itemNoValue = itemNoValue.substr(0, itemNoValue.length - 1);
                        itemChargeModeValue = itemChargeModeValue.substr(0, itemChargeModeValue.length - 1);
                        attachItemPricesValue = attachItemPricesValue.substr(0, attachItemPricesValue.length - 1);
                        freeNumValue = freeNumValue.substr(0, freeNumValue.length - 1);
                    }
                }
            }

            if(!LS.isEmpty(itemNoValue)){
                var params = {
                    itemNo:itemNoValue,
                    price:attachItemPricesValue,
                    freeNum:freeNumValue,
                    itemChargeMode:itemChargeModeValue
                };
                serviceGrid.query('~/billing/chargeServiceItem/querySerItemsByNo',params,function(){
                    var items = serviceGrid.getItems();
                    if(items) {
                        for(var i=0;i< items.length;i++) {
                            serviceGrid.editRow(items[i].itemNo);
                        }
                    }
                });
            }
        }

        function doAddSerGrid(){
            var items = serviceGrid.getItems();
            var nos = "";
            for(var i =0;i < items.length;i++){
                if(i!=items.length-1){
                    nos += items[i].itemNo+",";
                }else
                    nos += items[i].itemNo;
            }

            //服务费模式为分时的时候，不能选择服务费项目
            var mode = chargeMode.getValue();
            var serMode = serviceMode.getValue();
            if(serMode == '0202' && mode == '0202'){  //标准
                if(nos == ""){
                    nos = "1000001000";
                }else{
                    nos += ",1000001000";
                }
            }

            LS.dialog("~/billing/chargeServiceItem/select?itemStatus=1&unUseItemNos="+nos,"选择服务项目",1000, 400,true, null);
        }

        <%--window.setChargeSer = setChargeSer;--%>
        window.setChargeSer = function(item) {
            serviceGrid.appendItem(item);
            var items = serviceGrid.getItems();
            if(items) {
                for(var i=0;i< items.length;i++) {
                    serviceGrid.editRow(items[i].itemNo);
                }
            }
        }

        function doDelSerGrid(){
            var item = serviceGrid.getSelectedItem();
            if(LS.isEmpty(item)){
                LS.message("error","请选择要删除的服务项目");
                return;
            }
            serviceGrid.removeItem(item);
        }

        //服务项目设置--计费单价验证
        function servicePriceValidate(value){
            var bMoney = value;
            var isValid = true;
            if(!LS.isEmpty(bMoney)){
                var i = bMoney.indexOf(".");
                var str = "";
                if(i>=0){
                    str = bMoney.substring(i+1);
                }
                isValid = (str.length <= 2);
            }
            return {
                isValid : isValid,
                message : "小数位不能超过两位"
            }
        }

        //====================================其他=============================================================
        function getPile(){
            LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
            // LS.dialog("~/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
        }

        function changeStationName(){
            if(stationName.getValue()==null || stationName.getValue()==''){
                stationId.setValue();
            }
        }

        window.setStation = setStation;
        function setStation(item) {
            stationId.setValue(item.stationId);
            stationName.setValue(item.stationName);
            orgCode.setValue(item.orgCode);
        }

        function changePriceTrShow(){
            var mode = chargeMode.getValue();
            if(mode == '0201'){//标准、显示priceTr
                $('#priceTr').show();
                $('#timeTr').hide();
                $('#servicePriceDiv').hide();
                $("#servicePriceDivMode").hide();
                $("#gatherDataTypeHide1").hide();
            }else{//隐藏priceTr
                $('#priceTr').hide();
                $('#timeTr').show();
                $('#servicePriceDiv').show();
                $("#servicePriceDivMode").show();
                $("#gatherDataTypeHide1").show();
            }
        }

        //服务费计费模式
        function changeServicePriceTrShow(){
            var mode = serviceMode.getValue();
            if(mode == '0201'){  //标准
                timeShareGrid.setHideCol(['serPrice']);
                $('#serviceItemTitleName').css('display','block');
                $('#otherItemTitleName').css('display','none');
                $('.otherItemTitleName').css('display','block');
            }else if(mode == '0202'){ //分时
                timeShareGrid.setShowCol(['serPrice']);
                $('#serviceItemTitleName').css('display','none');
               // $('#otherItemTitleName').css('display','block');
                $('.otherItemTitleName').css('display','none');

                var data = timeShareGrid.getItems();
                timeShareGrid.removeAllItems();
                if(data != null){
                    for(var i = 0 ; i < data.length ; i++){
                        timeShareGrid.addRow(data[i],"last");
                    }
                }

            }
        }

        function changeTimeFlag(sn,item,itemElement){
            var flag = itemElement.timeFlag?itemElement.timeFlag.value:"";
            if(LS.isEmpty(flag)){
                return;
            }
            var price = null;
            var serPrice = null;
            var items = timeShareGrid.getItems();
            for(var i =0;i < items.length;i++){
                if( items[i].timeFlag == flag && !LS.isEmpty(items[i].price)){
                    serPrice = items[i].serPrice;
                    price = items[i].price;
                    break;
                }
            }
            for(var i =0;i < items.length;i++){
                if( items[i].timeFlag == flag && LS.isEmpty(items[i].price) && LS.isEmpty(items[i].serPrice)){
                    timeShareGrid.setCell(items[i].sn, "serPrice", serPrice);
                    timeShareGrid.setCell(items[i].sn, "price", price);
                }
            }
        }

        function changePrice(sn,item,itemElement){
            var flag = itemElement.timeFlag?itemElement.timeFlag.value:"";
            if(LS.isEmpty(flag)){
                return;
            }
            var price = null;
            var items = timeShareGrid.getItems();
            for(var i =0;i < items.length;i++){
                if( items[i].timeFlag == flag && !LS.isEmpty(items[i].price)){
                    price = items[i].price;
                    break;
                }
            }
            for(var i =0;i < items.length;i++){
                if( items[i].timeFlag == flag && LS.isEmpty(items[i].price)){
                    timeShareGrid.setCell(items[i].sn, "price", price);
                }
            }
        }

	</ls:script>
</ls:body>
</html>