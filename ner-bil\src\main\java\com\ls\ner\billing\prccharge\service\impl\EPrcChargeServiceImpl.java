/**
 *
 * @(#) EPrcChargeServiceImpl.java
 * @Package com.ls.ner.billing.prccharge.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.prccharge.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.api.prccharge.bo.EPrcChargeBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo;
import com.ls.ner.billing.api.prccharge.service.IEPrcChargeService;
import com.ls.ner.billing.prccharge.dao.IEPrcChargeDao;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年5月1日 上午10:27:47   lipf   Created.
 *           
 */
@Service(target = { ServiceType.APPLICATION }, value = "ePrcChargeService")
public class EPrcChargeServiceImpl implements IEPrcChargeService{
	@Autowired(required=true)
	private IEPrcChargeDao dao;
	@Override
	public void insertEPrcCharge(List<EPrcChargeBo> list) {
		if(null!=list&&list.size()>0){
			dao.insertEPrcCharge(list);
		}
	}

	@Override
	public void insertEPrcChargeDet(List<EPrcChargeDetBo> list) {
		if(null!=list&&list.size()>0){
			dao.insertEPrcChargeDet(list);
		}
	}

	@Override
	public List<EPrcChargeBo> getPrcCharge(Map<String, String> paraMap) {
		return dao.getPrcCharge(paraMap);
	}

	@Override
	public List<EPrcChargeDetBo> getPrcChargeDet(Map<String, String> paraMap) {
		return dao.getPrcChargeDet(paraMap);
	}

	@Override
	public void updateEprcCharge(Map<String, Object> tMap) {
		// TODO Auto-generated method stub
		dao.updateEprcCharge(tMap);
	}

}
