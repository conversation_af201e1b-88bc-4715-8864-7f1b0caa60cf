/**
 *
 * @(#) IBillingDao.java
 * @Package com.ls.ner.billing.tariff.dao
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.bo.AttachConfigBo;
import com.ls.ner.billing.bo.ItemPeriods;
import com.ls.ner.billing.bo.MainChargeConfigBo;
import com.ls.ner.billing.bo.StepConfig;
import com.ls.ner.billing.bo.VehicleBillingBo;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: IBillingDao.java,v 1.1 2016/02/26 02:34:01 34544 Exp $ 
 *
 *  History:  2016年2月25日 下午9:37:15   lipf   Created.
 *           
 */
public interface IBillingDao {

	/**
	 * 
	 * 方法说明：查询车型定价计费配置
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月25日 下午10:02:48
	 * History:  2016年2月25日 下午10:02:48   lipf   Created.
	 *
	 * @param inMap
	 * @return
	 *
	 */
	VehicleBillingBo getVehicleBilling(Map<String, String> inMap);

	/**
	 * 
	 * 方法说明：主收费配置 查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月25日 下午10:29:10
	 * History:  2016年2月25日 下午10:29:10   lipf   Created.
	 *
	 * @param tempMap
	 * @return
	 *
	 */
	List<MainChargeConfigBo> getMainChargeConfig(Map<String, String> tempMap);

	/**
	 * 
	 * 方法说明：附加收费配置查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月25日 下午10:45:51
	 * History:  2016年2月25日 下午10:45:51   lipf   Created.
	 *
	 * @param tempMap
	 * @return
	 *
	 */
	List<AttachConfigBo> getAttachConfig(Map<String, String> tempMap);

	/**
	 * 
	 * 方法说明：分时查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月25日 下午10:55:14
	 * History:  2016年2月25日 下午10:55:14   lipf   Created.
	 *
	 * @param temp
	 * @return
	 *
	 */
	List<ItemPeriods> getItemPeriods(Map<String, String> temp);

	/**
	 * 
	 * 方法说明：阶梯配置查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月25日 下午11:02:20
	 * History:  2016年2月25日 下午11:02:20   lipf   Created.
	 *
	 * @param temp
	 * @return
	 *
	 */
	List<StepConfig> getStepConfig(Map<String, String> temp);

}
