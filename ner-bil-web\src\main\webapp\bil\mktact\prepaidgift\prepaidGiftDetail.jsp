<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="预存赠送">
    <style>
        .file-img {
            display: block;
            width: 200px;
            height: 120px;
            position: relative;
        }

        .file-img img {
            display: block;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: gray;
        }

    </style>
</ls:head>
<ls:body>
    <ls:form id="marketactForm" name="marketactForm">
        <ls:text name="buildType" property="buildType" value="0" type="hidden"/>
        <ls:text name="city" property="city" value="0" type="hidden"/>
        <ls:text name="stationId" property="stationId" value="0" type="hidden"/>
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden" value="07"/>
        <table class="tab_search">
            <tr>
                <td style="float: left;margin-left:45px"><ls:label text="活动名称" ref="actName"/></td>
                <td style="float: left;margin-top:-5px;width: 400px"><ls:text name="actName" property="actName" required="true"
                                                                 readOnly="true"/></td>
                <td rowspan="4">
                    <div>
                        <a class="file-img" href="javascript:void(0);" style="left: 57px;top: 10px;">
                            <img src="" id="headImg"></img>
                        </a>
                    </div>
                </td>
            </tr>
            <tr>
                <td style="float: left;margin-left:45px"><ls:label text="生效时间" ref="effTime"/></td>
                <td style="float: left;margin-top:-5px"><ls:text name="effTime" property="effTime" required="true"
                                                                 readOnly="true"/></td>

            </tr>
            <tr>
                <td style="float: left;margin-left:45px"><ls:label text="失效时间" ref="expTime"/></td>
                <td style="float: left;margin-top:-5px"><ls:text name="expTime" property="expTime" required="true"
                                                                 readOnly="true"/></td>
            </tr>
            <tr>
                <td style="float: left;margin-left:45px"><ls:label text="适用对象" ref="custType"/></td>
                <td style="float: left;margin-top:-5px">
                    <ls:checklist name="custType" property="custType" type="checkbox" required="true" readOnly="true">
                        <ls:checkitem text="个人" value="01"/>
                        <ls:checkitem text="企业" value="02"/>
                    </ls:checklist>
                </td>

            </tr>
        </table>
    </ls:form>
    <ls:form id="preSectionForm" name="preSectionForm">
        <ls:text name="presentSectionId" property="presentSectionId" type="hidden"/>
        <ls:text name="presentBalType" property="presentBalType" type="hidden"/>

        <table class="tab_search">
            <tr>

                <td>
                    <ls:label text="赠送最大值" ref="maxValue"/>
                </td>
                <td>
                    <ls:text name="maxValue" property="maxValue" required="true"
                             onValidate="VD.positiveInteger(maxValue)" readOnly="true"/>
                </td>
            </tr>
            <tr id="sectionDetTr">
                <td width="100px">
                    <ls:label text="段落明细" ref="presentSectionDets"/>
                </td>
                <td colspan="6">
                    <ls:grid url="" name="sectionDetGrid" primaryKey="sectionDetSn" cellEdit="true" treeGrid="true"
                             expandColumn="value" expand="true" treeGridModel="sectionDet" height="100px" width="100%">
                        <ls:column caption="预存段落ID" name="sectionDetId" editableEdit="false" hidden="true"/>
                        <ls:column caption="排序号" name="sectionDetSn" editableEdit="false"/>
                        <ls:column caption="起始值(>=)" name="refCeil" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="终止值(<)" name="refFloor" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="赠送值" name="baseValue" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="赠送积分" name="integralNum" editableEdit="true">
                            <ls:textEditor required="false" type="number"/>
                        </ls:column>
                    </ls:grid>
                    <ls:text name="presentSectionDets" property="presentSectionDets" required="true" type="hidden"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="有效期限" ref="youxiao"/>
                    <ls:text type="hidden" name="youxiao" required="true"/></td>
                </td>
                <td width="10%">
                    <ls:checklist name="beginTimeType2" property="beginTimeType" type="radio" required="true"
                                  value="2" readOnly="true">
                        <ls:checkitem text="至参与后" value="2"/>
                    </ls:checklist>
                </td>
                <td colspan="2" id="bgnTd1" width="8%">
                    <ls:text name="endTimeDuration" property="endTimeDuration" required="true" readOnly="true"/>
                </td>
                <td id="bgnTd2" width="15%">
                    <ls:select name="endTimeUnit" property="endTimeUnit" required="true" value="1" readOnly="true">
                        <ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
                <td>
                    <ls:label text="有效" id="youxiao1"/>
                </td>
            </tr>
            <tr>
                <td width="10%">
                    <ls:label text=""/>
                </td>
                <td>
                    <ls:checklist name="beginTimeType1" property="beginTimeType" type="radio" required="true"
                                  readOnly="true">
                        <ls:checkitem text="具体日期" value="1"/>
                    </ls:checklist>
                </td>

                <td id="bgnTd3" width="15%">
                    <ls:date name="beginCalcObject" property="beginCalcObject" format="yyyy-MM-dd" readOnly="true"/>
                </td>
                <td id="bgnTd4" width="3%">
                    <ls:label text="至"/>
                </td>
                <td id="bgnTd5" width="15%">
                    <ls:date name="endCalcObject" property="endCalcObject" format="yyyy-MM-dd" readOnly="true"/>
                </td>
                <td>
                    <ls:label text="有效" id="youxiao2"/>
                </td>
            </tr>

            <tr>
                <td>
                    <ls:label text="简介"/>
                </td>
                <td colspan="6">
                    <ls:text name="presentRuleDesc" property="presentRuleDesc" type="textarea" required="true"
                             readOnly="true"/>
                </td>
            </tr>
            <tr>
                <td><ls:label text="活动资讯" ref="actInfo"/><ls:text type="hidden" name="actInfo" required="true"/></td>
                <td>
                    <a href="javascript:void(0)" onclick="toActInfo()">查看</a>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        var pubPath = '${pubPath }';
        var _actInfoId = '${actInfoId }';
        var _relaId = '${relaId }';
        doSearch();

        function doSearch() {
            var actIdValue = actId.getValue();
            var balTypeValue = presentBalType.getValue();
            var sectionIdValue = presentSectionId.getValue();
            if (!LS.isEmpty(actIdValue)) {
                sectionDetGrid.query('~/cpn/sections?actId=' + actIdValue + "&presentSectionId=" + sectionIdValue + "&presentBalType=" + balTypeValue, null);
            }
        }

        init();

        function init() {
            //var _beginTimeType = beginTimeType.getValue();
            var _beginTimeType1 = beginTimeType1.getValue();
            var _beginTimeType2 = beginTimeType2.getValue();

            if (!LS.isEmpty(_beginTimeType2) && _beginTimeType2=="2"){
                $('#bgnTd1').show();
                $('#bgnTd2').show();
                $('#bgnTd3').hide();
                $('#bgnTd4').hide();
                $('#bgnTd5').hide();
                document.getElementById('youxiao2').setAttribute("style","display: none");
                document.getElementById('youxiao1').setAttribute("style","width:10%");
            }

            if (!LS.isEmpty(_beginTimeType1) && _beginTimeType1=="1"){
                $('#bgnTd1').hide();
                $('#bgnTd2').hide();
                $('#bgnTd3').show();
                $('#bgnTd4').show();
                $('#bgnTd5').show();
                document.getElementById('youxiao1').setAttribute("style","display: none");
                document.getElementById('youxiao2').setAttribute("style","width:10%");
            }
            <%--if (_beginTimeType == "2") {
                $('#bgnTd1').show();
                $('#bgnTd2').show();
                $('#bgnTd3').hide();
                $('#bgnTd4').hide();
                $('#bgnTd5').hide();


            } else if (_beginTimeType == "1") {

                $('#bgnTd1').hide();
                $('#bgnTd2').hide();
                $('#bgnTd3').show();
                $('#bgnTd4').show();
                $('#bgnTd5').show();
            }--%>
        }

        var infoUrlValue = '${infoUrl}';

        window.toActInfo = function () {
            LS.dialog(infoUrlValue, "活动资讯", 1000, 600, true, null);
        }

        window.onload = function () {
            if (buildType.getValue() == 1) {
                $("#buildListTr").show();
            }

            if (city.getValue() == 1) {
                $("#cityListTr").show();
            }
            if (stationId.getValue() == 1) {
                doSearchStation();
            }

            //资讯
            headImg.src = pubPath + '/pub/image/js/add.png';
            if (!LS.isEmpty(_relaId) && !LS.isEmpty(_actInfoId)) {
                headImg.src = pubPath + '/attach/attachs/mkt_act_info/02/' + _relaId;
            }
        }

        function doSearchStation() {
            var stationIdsValue = stationIds.getValue();
            if (!LS.isEmpty(stationIdsValue)) {
                stationIdsValue = stationIdsValue.substring(0, stationIdsValue.length - 1)
                $("#stationListTr").show();
                station_grid.query("~/markertAct/getActStationList", {stationIds: stationIdsValue});
            }
        }
    </ls:script>
</ls:body>
</html>