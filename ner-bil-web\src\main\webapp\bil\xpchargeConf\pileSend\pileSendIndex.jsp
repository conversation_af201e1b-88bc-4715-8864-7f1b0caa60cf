<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="充电电价下发" >
	<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<ls:text name="orgCode" property="orgCode" type="hidden" />
		<table class="tab_search">
			<tr>
				<td><ls:label text="管理单位" /></td>
				<td><ls:text imageKey="search" onClickImage="getOrgs" name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" /></td>
				<td><ls:label text="充电站" ref="stationName"/></td>
				<td><ls:text type="hidden" name="stationId"></ls:text>
                   	<ls:text imageKey="search" onClickImage="getPile" name="stationName" enabled="false" onchanged="changeStationName"></ls:text>
               	</td>
				<td><ls:label text="下发状态" /></td>
				<td>
					<ls:select name="sendStatus" property="sendStatus">
						<ls:options property="chcBillSendStatusList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
			</tr>
			<tr>
				<td><ls:label text="当前计费版本"></ls:label></td>
				<td>
					<ls:text name="curChcNo" property="curChcNo" type="hidden"/>
					<ls:select name="curChcName" property="curChcName" allowEditing="true" onchanged="curChcNameChg" required="true">
						<ls:options property="chargeList" scope="request" text="chcName" value="chcNo"/>
					</ls:select>
				</td>
				<td><ls:label text="下个计费版本"></ls:label></td>
				<td>
					<ls:text name="newChcNo" property="newChcNo" type="hidden"/>
					<ls:select name="newChcName" property="newChcName" allowEditing="true" onchanged="newChcNameChg" required="true">
						<ls:options property="chargeList" scope="request" text="chcName" value="chcNo"/>
					</ls:select>
				</td>
				<td colspan="2">
					<div class="pull-right">
						<ls:button text="清空" onclick="doClear" />
						<ls:button text="查询" onclick="doSearch" />
					</div>
				</td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="chargeGrid" height="360px;" width="100%" showCheckBox="true" singleSelect="false" caption="充电计费配置列表">
					<ls:gridToolbar name="chargeGridBar">
						<ls:gridToolbarItem imageKey="add" onclick="doIssured" text="下发"></ls:gridToolbarItem>
						<ls:gridToolbarItem imageKey="edit" onclick="doDetail" text="下发明细"></ls:gridToolbarItem>
					</ls:gridToolbar>
					<ls:column caption="充电站ID" name="stationId" hidden="true"/>
					<ls:column caption="充电桩编号" name="pileNo" hidden="true"/>
					<ls:column caption="充电桩编号" name="pileId" hidden="true"/>
					<ls:column caption="管理单位" name="orgCodeName" />
					<ls:column caption="市区" name="city" hidden="true"/>
					<ls:column caption="充电站" name="stationName" />
					<ls:column caption="充电桩" name="pileName" />
					<ls:column caption="桩状态" name="runStatusName" />
					<ls:column caption="当前计费版本" name="curChcName" formatFunc="showCurChcDetail" />
					<ls:column caption="计费编号" name="curChcNo"/>
					<ls:column caption="下发状态" name="sendStatusName" formatFunc="sendStatusFunc"/>
					<ls:column caption="下发失败原因" name="failReason"/>
					<ls:column caption="最后一次下发时间" name="sendTime"  format="{sendTime,date,yyyy-MM-dd HH:mm}"/>
					<ls:column caption="最新充电计费编号" name="newChcNo" hidden="true"/>
					<ls:column caption="下个计费版本" name="newChcName" formatFunc="showNewChcDetail" />
					<ls:column caption="新版生效时间" name="eftDate" format="{eftDate,date,yyyy-MM-dd HH:mm}" />
					<ls:pager pageSize="15" pageIndex="1"></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	var astPath = '${astPath }';
	window.chargeGrid = chargeGrid;
    doSearch();
    window.doSearch = doSearch;
	function doSearch(){
		var params = searchForm.getFormData();
		chargeGrid.query('~/billing/pile-bill-send/queryChcbillSends',params,function(){});
	}
	
 	function doDetail(){
		var items = chargeGrid.getCheckedItems();
		if(items.length == 0){
			LS.message("error","请选择要查看明细的记录");
			return;
		}
		if(items.length > 1){
			LS.message("error","请选择其中一条记录进行查看");
			return;
		}
		LS.dialog("~/billing/pile-bill-send/detail?chcNo="+items[0].curChcNo+"&pileNo="+items[0].pileNo+"&stationId="+items[0].stationId,"桩电价下发明细",1000, 500,true, null);
 	}
 	
 	function doIssured(){
 		var items = chargeGrid.getCheckedItems();
		if(items.length == 0){
			LS.message("error","请选择要下发的充电计费配置");
			return;
		}
		var message = '确定要下发充电计费配置?';
		var chcNos = "";
		var pileNos = "";
		var pileIds = "";
		var stationIds = "";
		for(var i = 0;i < items.length; i++){
			if(LS.isEmpty(items[i].newChcNo)){
				LS.message("error","存在充电站没有计费配置，请检查");
				return;
			}
			if(items[i].runStatusName == '离线'){
				LS.message("error","离线的桩不能下发计费配置，请重新选择");
				return;
			}
			if(items[i].sendStatus == '04'){
				message = "存在计费配置已下发成功，确定需要重新下发？";
			}
			if(i == items.length - 1){
				chcNos = chcNos + items[i].newChcNo;
				pileNos = pileNos + items[i].pileNo;
				pileIds = pileIds + items[i].pileId;
				stationIds = stationIds + items[i].stationId;
			}else{
				chcNos = chcNos + items[i].newChcNo + ",";
				pileNos = pileNos + items[i].pileNo + ",";
				pileIds = pileIds + items[i].pileId + ",";
				stationIds = stationIds + items[i].stationId + ",";
			}
		}

	   LS.confirm(message,function(result){
			if(result){
				LS.ajax("~/billing/pile-bill-send/issured?chcNo="+chcNos+"&pileNo="+pileNos+"&stationId="+stationIds,null,function(data){
					if(data!=null){
						LS.message("info",data);
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});
 	}
 	
    function doClear(){
		orgCodeName.setValue();
		orgCode.setValue();
		stationId.setValue();
		stationName.setValue();
		sendStatus.setValue();
		curChcNo.setValue();
		curChcName.setValue();
		newChcNo.setValue();
		newChcName.setValue();
		searchForm.clear();
    }
    
	function getOrgs(){
		var initValue = [];
		js_util.selectOrgTree(false, null, true, initValue, false, function(node){
			orgCodeName.setValue(node.text);
			orgCode.setValue(node.id);
		});
	}
	
	function changeOrgName(){
		if(orgCodeName.getValue()==null || orgCodeName.getValue()==''){
		 orgCode.setValue();
		}
	}
	
	//====================================充电站查询=============================================================
    function getPile(){
       LS.dialog(astPath+"/ast/station/select?busiType=02","查询充电站信息",1000,500,true);
    }
    
    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }
    
    window.setStation = setStation;
    function setStation(item) {
       stationId.setValue(item.stationId);  
       stationName.setValue(item.stationName);
    }
		window.onload = function() {
		initGridHeight('searchForm','chargeGrid');
		}

	function sendStatusFunc(item){
			if(item.curChcNo == item.newChcNo){
				return item.sendStatusName?item.sendStatusName:'';
			}else{
				return "未下发";
			}
		}

		function showCurChcDetail(rowdata){
		if(rowdata.curChcName==null){
			return "--";
		}
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowChcDetail(\"" + rowdata.curChcNo +"\")'>"+rowdata.curChcName+"</a>";
		}

		function showNewChcDetail(rowdata){
			if(rowdata.newChcName==null){
			return "--";
			}
		return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='doShowChcDetail(\"" + rowdata.newChcNo +"\")'>"+rowdata.newChcName+"</a>";
		}
		window.doShowChcDetail=doShowChcDetail;
		function doShowChcDetail(chcNo){
		LS.dialog("~/billing/xpcbc/showChargeBillingConf?chcNo="+chcNo,"查看充电费用配置",1000, 600,true, null);
		}

		window.setStation=setStation;
		function setStation(obj){
		stationId.setValue(obj.stationId);
		stationName.setValue(obj.stationName);
		}


		//计费文本发现变化时触发
		function curChcNameChg() {
			curChcNo.setValue(curChcName.getValue());
			if (LS.isEmpty(curChcName.getValue())) {
				curChcNo.setValue();
			}
		}
		function newChcNameChg() {
			newChcNo.setValue(newChcName.getValue());
			if (LS.isEmpty(newChcName.getValue())) {
				newChcNo.setValue();
			}
		}
    </ls:script>
</ls:body>
</html>