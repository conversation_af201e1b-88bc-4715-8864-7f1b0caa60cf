package com.ls.ner.billing.mktact.dao;

import java.util.List;
import java.util.Map;

public interface IMarketActRpcDao {

	/**
	 * 描述:获取可参加的活动
	 *
	 * @param: [OrderNo]
	 * @return: java.util.List<java.util.Map>
	 * 创建人:biaoxiangd
	 * 创建时间:2017/7/10 4:22
	 */
	List<Map> queryActAll();

	/**
	 * @Description: 查询计价数量
	 * @method: prodNumSum
	 * @param inMap
	 * @return: java.lang.String
	 * @Author: cailianL
	 * @Time: 2017/7/12 16:09
	 */
	String prodNumSum(Map<String, Object> inMap);

	/**
	 * @Description: 查询计价金额
	 * @method: prodAmtSum
	 * @param inMap
	 * @return: java.lang.String
	 * @Author: cailianL
	 * @Time: 2017/7/12 16:09
	 */
	String prodAmtSum(Map<String, Object> inMap);


	/**
	 * @Description: 查询订单数量
	 * @method: getOrderNum
	 * @param inMap
	 * @return: int
	 * @Author: cailianL
	 * @Time: 2017/7/13 15:16
	 */
	int getOrderNum(Map<String, Object> inMap);

	/**
	 *
	  * @param inMap
	 * @description RPC05-12-03 获取注册送活动
	 * @author: biaoxiangd
	 * @create:2017-09-27
	 */
	List<Map> getRegisterSendAct(Map<String, Object> inMap);
	/**
	 * @param inMap
	 * @description
	 * <AUTHOR> @create 2018-05-16 10:03:18
	 */
	List<Map<String,Object>> getActiveActInfo(Map<String, Object> inMap);
	/**
	 * @param inMap
	 * @description 查询参与活动的次数（预存赠送有用到）
	 * <AUTHOR>
	 * @create 2018-07-05 15:15:20
	 */
	int queryJoinActCount(Map<String, Object> inMap);
	/**
	 * @param inMap
	 * @description 记录参与活动（预存赠送有用到）
	 * <AUTHOR>
	 * @create 2018-07-05 15:15:20
	 */
	void joinActRecord(Map<String, Object> inMap);

	/**
	 * @param inMap
	 * @description 查询满减活动信息
	 * <AUTHOR>
	 * @create 2018-09-25 11:17:45
	 */
	List<Map<String, Object>> queryFullSaleInfo(Map<String, Object> inMap);

	/**
	 * @param inMap
	 * @description 查询活动
	 * <AUTHOR>
	 * @create 2018-12-17 10:30:51
	 */
  List qryMktActList(Map<String, Object> inMap);

  /**
   * @param inMap
   * @description 查询充值活动
   * <AUTHOR>
   * @create 2018-12-17 17:21:40
   */
  List qryRecMktActList(Map<String, Object> inMap);

  /**
   * @param inMap
   * @description 查询活动关联站点数量
   * <AUTHOR>
   * @create 2019-05-10 10:44:25
   */
	int qryActStationCount(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 查询所有活动相关适用范围
	 * @create 2019/5/27 19:15
	 *
	 */
	List<Map> qryMktActByScope(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description  查询优惠内容（限时折扣）
	 * @create 2019/5/28 15:00
	 *
	 */
	List<Map> qryActContDct(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 查询活动和资讯信息
	 * @create 2019/5/28 17:20
	 *
	 */
	List<Map> qryActAndInfo(Map<String, Object> inMap);

	/**
	 * @param
	 * @description  查询活动参与
	 * <AUTHOR>
	 * @create 2019/6/4 18:25
	 */
	List<Map> qeyActJoin(Map<String, Object> inMap);
}