<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券查询">
<style type="text/css">
.hiddenFlow{
overflow-x:hidden; 
}
</style>
</ls:head>
<ls:body>
	<ls:form name="couponForm" cssClass="hiddenFlow">
		<ls:text name="querySubOrgFLag" value="true" visible="false"></ls:text>
		<ls:text name="chargeType" value="${chargeType}" visible="false"></ls:text>
		<ls:title text="查询条件"></ls:title>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:label text="优惠券名称"></ls:label></td>
				<td><ls:text name="cpnName"></ls:text></td>
				<td><ls:label text="优惠券类型"></ls:label></td>
				<td>
					<ls:select name="cpnType">
						<ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td><ls:label text="面额/折扣"></ls:label></td>
				<td><ls:text name="cpnAmt"></ls:text></td>
				<td><ls:label text="优惠券状态"></ls:label></td>
				<td>
					<ls:select name="cpnStatus">
						<ls:options property="cpnStatusList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				
			</tr>
			
			<tr>
				<td colspan="8">
					<div class="pull-right">
						<ls:button text="清空" onclick="clearAll" />
						<ls:button text="查询" onclick="query" />
					</div>
				</td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="couponGrid" caption="优惠券列表" showRowNumber="false"
						height="410px"  width="100%" showCheckBox="false" singleSelect="true" >
						
						<ls:column name="cpnNo" caption="优惠券编号" hidden="true" />
						<ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="150px"/>
						<ls:column caption="优惠券类型" name="cpnTypeName" align="center" />
						<ls:column caption="面额/折扣" name="cpnAmt" align="right" />
						<ls:column caption="发行数量" name="cpnNum" align="right" width="100px"/>
						<ls:column caption="每人限领" name="limGetNum" align="right" width="80px"/>
						<ls:column caption="使用条件" name="useCondVal" align="center"/>
						<ls:column caption="有效时间" name="effectTime" align="center" />
						<ls:column caption="优惠券状态" name="cpnStatusName" align="center" />
						<ls:column caption="推广状态" name="pushStatusName" align="center"/>
						<ls:column caption="创建单位" name="crtOrgName" align="center"/>
						<ls:column caption="创建时间" name="creTime" align="center"/>
						<ls:pager pageSize="15,20"></ls:pager>
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<ls:script>
    function query(){
      var datas={};
      datas = couponForm.getFormData();
      couponGrid.query("~/market/coupon/getCoupons",datas);
    }
    function clearAll(){
      couponForm.clear();
    };
 	function linkFunc(rowdata){
    	var _cpnNo = rowdata.cpnNo;
    	var _cpnName = rowdata.cpnName;
    	return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnNo + "\")'>"+_cpnName+"</a></u>";
    }
    window.showCouponDetail = function(_cpnNo){
    	LS.dialog("~/market/coupon/initCouponDetail?cpnNo="+_cpnNo,"优惠券详细信息",1000, 600,true, null);
    }
   
    </ls:script>
</ls:body>
</html>