<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.vip.dao.IVipLevelCalcConfigDao">
    <resultMap id="BaseResultMap" type="com.ls.ner.billing.vip.bo.VipLevelCalcConfig">
        <id column="id" property="id" />
        <result column="calc_mode" property="calcMode" />
        <result column="amount_per_kwh" property="amountPerKwh" />
        <result column="kwh_per_amount" property="kwhPerAmount" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="selectLatestConfig" resultMap="BaseResultMap">
        SELECT * FROM e_vip_level_calc_config ORDER BY update_time DESC LIMIT 1
    </select>

    <insert id="insertConfig" parameterType="com.ls.ner.billing.vip.bo.VipLevelCalcConfig">
        INSERT INTO e_vip_level_calc_config (calc_mode, amount_per_kwh, kwh_per_amount, update_time)
        VALUES (#{calcMode}, #{amountPerKwh}, #{kwhPerAmount}, #{updateTime})
    </insert>

    <update id="updateConfig" parameterType="com.ls.ner.billing.vip.bo.VipLevelCalcConfig">
        UPDATE e_vip_level_calc_config
        SET calc_mode=#{calcMode}, amount_per_kwh=#{amountPerKwh}, kwh_per_amount=#{kwhPerAmount}, update_time=#{updateTime}
        WHERE id=#{id}
    </update>
</mapper> 