<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper 
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.prccharge.dao.IEPrcChargeDao">
	<resultMap id="chargeBoBean" type="com.ls.ner.billing.api.prccharge.bo.EPrcChargeBo">
		<!-- 费用项 -->
		<result column="CUST_ID" property="custId" />
		<result column="CUST_NO" property="custNo" />
		<result column="CITY" property="city" />
		<result column="COUNTY" property="county" />
		<result column="APP_NO" property="appNo" />
		<result column="CALC_ID" property="calcId" />
		<result column="BILLING_TYPE" property="billingType" />
		<result column="LEASE" property="lease" />
		<result column="T_SETTLE_PQ" property="tSettlePq" />
		<result column="PRICING_AMT" property="pricingAmt" />
		<result column="RCVBL_ID" property="rcvblId" />
		<result column="CALC_DATE" property="calcDate" />
	</resultMap>

	<resultMap id="chargeDetBoBean" type="com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo">
		<!-- 费用明细 -->
		<result column="CALC_ID" property="calcId" />
		<result column="ITEM_CODE" property="itemCode" />
		<result column="ITEM_NAME" property="itemName" />
		<result column="ITEM_UNITS" property="itemUnits" />
		<result column="PRICING_AMT" property="pricingAmt" />
		<result column="AMOUNT" property="amount" />
		<result column="PRICE" property="price" />
		<result column="CALC_PROCESS" property="calcProcess" />
		<result column="ITEM_DESC" property="itemDesc" />

	</resultMap>

	<select id="getPrcCharge" resultMap="chargeBoBean" parameterType="java.util.Map">
		SELECT e.CUST_ID, e.CUST_NO, e.CITY, e.COUNTY, e.APP_NO, e.CALC_ID, e.BILLING_TYPE, 
		e.LEASE, e.T_SETTLE_PQ, e.PRICING_AMT, e.RCVBL_ID, date_format(e.CALC_DATE,'%Y-%m-%d %H:%i:%s') CALC_DATE
		FROM e_prc_charge e
		WHERE e.billing_type=#{billingType}
		and e.app_no=#{appNo}
	</select>
	<select id="getPrcChargeDet" resultMap="chargeDetBoBean" parameterType="java.util.Map">
		SELECT a.CALC_ID, a.ITEM_CODE, a.ITEM_NAME, a.ITEM_UNITS,
		a.PRICING_AMT, a.AMOUNT, a.PRICE, a.CALC_PROCESS, a.ITEM_DESC
		FROM e_prc_charge e, e_prc_charge_det a
		WHERE e.CALC_ID = a.CALC_ID
		and e.billing_type=#{billingType}
		and e.app_no=#{appNo}
		<if test="itemCode!=null">
			and a.item_code like concat(#{itemCode},'%')
		</if>
	</select>

	<insert id="insertEPrcCharge" parameterType="java.util.List">
		INSERT INTO e_prc_charge
		<trim prefix="(" suffix=")">
			<foreach collection="list" item="item" index="index"
				separator=",">
				<if test=" item.custId !=null and item.custId != '' ">CUST_ID,</if>
				<if test=" item.custNo !=null and item.custNo != '' ">CUST_NO,</if>
				<if test=" item.city !=null and item.city != '' ">CITY,</if>
				<if test=" item.county !=null and item.county != '' ">COUNTY,</if>
				<if test=" item.appNo !=null and item.appNo != '' ">APP_NO,</if>
				<if test=" item.calcId !=null and item.calcId != '' ">CALC_ID,</if>
				<if test=" item.billingType !=null and item.billingType != '' ">BILLING_TYPE,</if>
				<if test=" item.lease !=null and item.lease != '' ">LEASE,</if>
				<if test=" item.tSettlePq !=null and item.tSettlePq != '' ">T_SETTLE_PQ,</if>
				<if test=" item.pricingAmt !=null and item.pricingAmt != '' ">PRICING_AMT,</if>
				<if test=" item.rcvblId !=null and item.rcvblId != '' ">RCVBL_ID,</if>
				CALC_DATE,DATA_OPER_TIME,DATA_OPER_TYPE
			</foreach>
		</trim>
		<trim prefix="values (" suffix=")">
			<foreach collection="list" item="item" index="index"
				separator=",">
				<if test=" item.custId !=null and item.custId != '' ">#{item.custId},</if> 
				<if test=" item.custNo !=null and item.custNo != '' ">#{item.custNo},</if> 
				<if test=" item.city !=null and item.city != '' ">#{item.city},</if> 
				<if test=" item.county !=null and item.county != '' ">#{item.county},</if> 
				<if test=" item.appNo !=null and item.appNo != '' ">#{item.appNo},</if> 
				<if test=" item.calcId !=null and item.calcId != '' ">#{item.calcId},</if> 
				<if test=" item.billingType !=null and item.billingType != '' ">#{item.billingType},</if> 
				<if test=" item.lease !=null and item.lease != '' ">#{item.lease},</if> 
				<if test=" item.tSettlePq !=null and item.tSettlePq != '' ">#{item.tSettlePq},</if> 
				<if test=" item.pricingAmt !=null and item.pricingAmt != '' ">#{item.pricingAmt},</if> 
				<if test=" item.rcvblId !=null and item.rcvblId != '' ">#{item.rcvblId},</if> 
				now(),now(),'I'
			</foreach>
		</trim>
	</insert>
	<insert id="insertEPrcChargeDet" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index"
				separator=";">
		INSERT INTO e_prc_charge_det(
				<if test=" item.calcId !=null and item.calcId != '' ">CALC_ID,</if>
				<if test=" item.itemCode !=null and item.itemCode != '' ">ITEM_CODE,</if>
				<if test=" item.itemName !=null and item.itemName != '' ">ITEM_NAME,</if>
				<if test=" item.itemUnits !=null and item.itemUnits != '' ">ITEM_UNITS,</if>
				<if test=" item.pricingAmt !=null and item.pricingAmt != '' ">PRICING_AMT,</if>
				<if test=" item.amount !=null and item.amount != '' ">AMOUNT,</if>
				<if test=" item.price !=null and item.price != '' ">PRICE,</if>
				<if test=" item.calcProcess !=null and item.calcProcess != '' ">CALC_PROCESS,</if>
				<if test=" item.itemDesc !=null and item.itemDesc != '' ">ITEM_DESC,</if>
				DATA_OPER_TIME,DATA_OPER_TYPE)
		values (
				<if test=" item.calcId !=null and item.calcId != '' ">#{item.calcId},</if>
				<if test=" item.itemCode !=null and item.itemCode != '' ">#{item.itemCode},</if>
				<if test=" item.itemName !=null and item.itemName != '' ">#{item.itemName},</if>
				<if test=" item.itemUnits !=null and item.itemUnits != '' ">#{item.itemUnits},</if>
				<if test=" item.pricingAmt !=null and item.pricingAmt != '' ">#{item.pricingAmt},</if>
				<if test=" item.amount !=null and item.amount != '' ">#{item.amount},</if>
				<if test=" item.price !=null and item.price != '' ">#{item.price},</if>
				<if test=" item.calcProcess !=null and item.calcProcess != '' ">#{item.calcProcess},</if>
				<if test=" item.itemDesc !=null and item.itemDesc != '' ">#{item.itemDesc},</if>
				now(),'I'
		)
		</foreach>
	</insert>
	<update id="updateEprcCharge" parameterType="java.util.Map">
		update e_prc_charge
		<set>
			<if test="pricingAmt!=null">
				PRICING_AMT = #{pricingAmt},
			</if>
		</set>
		<where>
			<if test="appNo!=null">
				and app_no = #{appNo}
			</if>
			<if test="billingType!=null">
				and BILLING_TYPE = #{billingType}
			</if>
		</where>
	</update>
</mapper>