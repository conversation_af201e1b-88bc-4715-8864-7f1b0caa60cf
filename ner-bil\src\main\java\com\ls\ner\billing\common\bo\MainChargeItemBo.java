package com.ls.ner.billing.common.bo;

import java.util.Date;

public class MainChargeItemBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String mainNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String planNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.CHARGE_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Integer chargeNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_main_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String dataOperType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.SYSTEM_ID
     *
     * @return the value of e_main_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.SYSTEM_ID
     *
     * @param systemId the value for e_main_charge_item.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.MAIN_NO
     *
     * @return the value of e_main_charge_item.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getMainNo() {
        return mainNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.MAIN_NO
     *
     * @param mainNo the value for e_main_charge_item.MAIN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setMainNo(String mainNo) {
        this.mainNo = mainNo == null ? null : mainNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.PLAN_NO
     *
     * @return the value of e_main_charge_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getPlanNo() {
        return planNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.PLAN_NO
     *
     * @param planNo the value for e_main_charge_item.PLAN_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPlanNo(String planNo) {
        this.planNo = planNo == null ? null : planNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.CHARGE_MODE
     *
     * @return the value of e_main_charge_item.CHARGE_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeMode() {
        return chargeMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.CHARGE_MODE
     *
     * @param chargeMode the value for e_main_charge_item.CHARGE_MODE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeMode(String chargeMode) {
        this.chargeMode = chargeMode == null ? null : chargeMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.CHARGE_TYPE
     *
     * @return the value of e_main_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeType() {
        return chargeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.CHARGE_TYPE
     *
     * @param chargeType the value for e_main_charge_item.CHARGE_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.CHARGE_UNIT
     *
     * @return the value of e_main_charge_item.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeUnit() {
        return chargeUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.CHARGE_UNIT
     *
     * @param chargeUnit the value for e_main_charge_item.CHARGE_UNIT
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeUnit(String chargeUnit) {
        this.chargeUnit = chargeUnit == null ? null : chargeUnit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.CHARGE_NUM
     *
     * @return the value of e_main_charge_item.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Integer getChargeNum() {
        return chargeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.CHARGE_NUM
     *
     * @param chargeNum the value for e_main_charge_item.CHARGE_NUM
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeNum(Integer chargeNum) {
        this.chargeNum = chargeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.DATA_OPER_TIME
     *
     * @return the value of e_main_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_main_charge_item.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_main_charge_item.DATA_OPER_TYPE
     *
     * @return the value of e_main_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_main_charge_item.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_main_charge_item.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }
}