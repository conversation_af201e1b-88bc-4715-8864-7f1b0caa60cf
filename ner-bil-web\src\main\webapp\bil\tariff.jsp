<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费标准管理" />
<ls:body>
		<ls:form id="tariffForm" name="tariffForm">
				<ls:title text="查询条件 "></ls:title>
				<table class="tab_search">
						<tr>
								<td>
										<ls:label text="资费标准" />
								</td>
								<td>
										<ls:select name="subBe">
												<ls:options property="subBeList" scope="request" text="codeName" value="codeValue" />
										</ls:select>
								</td>
								<td colspan="2">
										<div class="pull-right">
												<ls:button text="清空" onclick="doClear" />
												<ls:button text="查询" onclick="doSearch" />
										</div>
								</td>
						</tr>
				</table>
		</ls:form>
		<table align="center" class="tab_search">
				<tr>
						<td>
								<ls:grid height="340px" width="100%" url="" name="tariffGrid" treeGrid="true" expandColumn="value" showCheckBox="true" expand="true" primaryKey="id" treeGridModel="adjacency">
										<ls:gridToolbar name="code_grid_bar">
												<ls:gridToolbarItem name="addTariff" text="新增" imageKey="add" onclick="addTariff" />
												<ls:gridToolbarItem name="editTariff" text="修改" imageKey="edit" onclick="editTariff" />
												<ls:gridToolbarItem name="deleteTariff" text="删除" imageKey="delete" onclick="deleteTariff" />
												<ls:gridToolbarItem name="detailTariff" text="详情" imageKey="look" onclick="detailTariff" />
												<%-- <ls:gridToolbarItem name="startTariff" text="启用" imageKey="start" onclick="startTariff" />
												<ls:gridToolbarItem name="stopTariff" text="停用" imageKey="stop" onclick="stopTariff" /> --%>
										</ls:gridToolbar>
										<ls:column caption="资费标准名称" name="planName" />
										<ls:column caption="资费说明" name="planRemark" align="left"/>
										<ls:pager pageSize="20"/>
								</ls:grid>
						</td>
				</tr> 
		</table>
		<ls:script>
		window.doSearch = doSearch;
		doSearch();
	    
		function doSearch(){
			var params = tariffForm.getFormData();
			tariffGrid.query('~/billing/tariff/queryTariffList',params);
		}
		
	  function doClear(){
	    tariffForm.clear();
	  }
    
    function addTariff(){
       window.location.href=rootUrl+"billing/tariff/addTariff";
    }
    
    function editTariff(){
    	var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          window.location.href=rootUrl+"billing/tariff/editTariff?id="+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function deleteTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      LS.confirm('确认删除吗?', function(data) {
          if (data) {
              LS.ajax("~/billing/tariff/deleteTariff?id="+item[0], {}, function(e) {
                if (e.items[0]=="Y") {                  
                  LS.message("info", "删除成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    
    function detailTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          window.location.href=rootUrl+"billing/tariff/detailTariff?id="+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function startTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var items=tariffGridGrid.getCheckedItems();
      for(var i=0;i< items.length;i++){
        if(items[i].tariffGridStatus!="03"){
          LS.message("info","只能对已发布的内容进行撤销");
          return;
        }
      }
      LS.confirm('确认启用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = item;
              LS.ajax("~/billing/tariff/startTariff", params, function(e) {
                if (e.items[0]=="Y") {                  
                  LS.message("info", "启用成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    function stopTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var items=tariffGridGrid.getCheckedItems();
      for(var i=0;i< items.length;i++){
        if(items[i].tariffGridStatus!="03"){
          LS.message("info","只能对已发布的内容进行撤销");
          return;
        }
      }
      LS.confirm('确认停用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = item;
              LS.ajax("~/billing/tariff/stopTariff", params, function(e) {
                if (e.items[0]=="Y") {                  
                  LS.message("info", "停用成功！");
                  query();
                } else {
                  LS.message("error", e.items[0]);
                }
              });
          }
      });
    }
    </ls:script>
</ls:body>
</html>