package com.ls.ner.billing.mktact.bo;

import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.io.Serializable;

/**
 * 描述:活动类型
 * MarketTypeBo.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/29 15:57
 **/
public class MarketTypeBo extends QueryCondition implements Serializable {


    private static final long serialVersionUID = 954089699387318093L;

    private String actId; // 活动ID
    private String actType;// 活动类别
    private String dctCondFlag; // 优惠条件类型
    private String dctLvl; //优惠类型
    private String actCondId; // 优惠条件ID
    private String dctCondType; // 优惠条件类别
    private String prodId; // 优惠条件对象
    private String dctCondUnit; // 优惠条件对象的数值单位

    //==============活动条件明细
    private String actCondDetId; // 优惠条件明细ID
    private String dctCondValue; // 优惠条件数值
    private String dctType; // 优惠类别
    @DicAttribute(dicName = "codeDict", key = "dctType", subType = "dctType")
    private String dctTypeName;

    private String dctCalcMethod;//优惠计算方法

    //==============活动内容
    private String actDctId;//优惠内容ID
    private String actDctType; //优惠类别
    private String actDctProdId; // 优惠对象
    private String actDctProdName;// 优惠对象名称
    private String allFree; // 是否全免
    private String dctValue; // 优惠数值
    private String actDctCondUnit; // 优惠数值单位
    /**
     * 适用用户 0-个人，1-企业
     */
    private String appUser;
    /**
     * 是否享受企业计费
     */
    private String pBillFlag;

    /**
     * 是否全部站点
     */
    private String allStationFlag;
    private String busiType;
    /**
     * 优惠券Id
     */
    private String cpnId;
    private String stationId;
    private String stationName;
    private String actStationId;

    //是否来自新版详情 1是 0或者空不是
    private String isFromNewDetail;

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getActDctProdName() {
        return actDctProdName;
    }

    public void setActDctProdName(String actDctProdName) {
        this.actDctProdName = actDctProdName;
    }

    public String getDctTypeName() {
        return dctTypeName;
    }

    public void setDctTypeName(String dctTypeName) {
        this.dctTypeName = dctTypeName;
    }

    public String getActDctType() {
        return actDctType;
    }

    public void setActDctType(String actDctType) {
        this.actDctType = actDctType;
    }

    public String getActDctProdId() {
        return actDctProdId;
    }

    public void setActDctProdId(String actDctProdId) {
        this.actDctProdId = actDctProdId;
    }

    public String getActId() {
        return actId;
    }

    public void setActId(String actId) {
        this.actId = actId;
    }

    public String getActType() {
        return actType;
    }

    public void setActType(String actType) {
        this.actType = actType;
    }

    public String getDctCondFlag() {
        return dctCondFlag;
    }

    public void setDctCondFlag(String dctCondFlag) {
        this.dctCondFlag = dctCondFlag;
    }

    public String getDctLvl() {
        return dctLvl;
    }

    public void setDctLvl(String dctLvl) {
        this.dctLvl = dctLvl;
    }

    public String getActCondId() {
        return actCondId;
    }

    public void setActCondId(String actCondId) {
        this.actCondId = actCondId;
    }

    public String getDctCondType() {
        return dctCondType;
    }

    public void setDctCondType(String dctCondType) {
        this.dctCondType = dctCondType;
    }

    public String getProdId() {
        return prodId;
    }

    public void setProdId(String prodId) {
        this.prodId = prodId;
    }

    public String getDctCondUnit() {
        return dctCondUnit;
    }

    public void setDctCondUnit(String dctCondUnit) {
        this.dctCondUnit = dctCondUnit;
    }

    public String getActCondDetId() {
        return actCondDetId;
    }

    public void setActCondDetId(String actCondDetId) {
        this.actCondDetId = actCondDetId;
    }

    public String getDctCondValue() {
        return dctCondValue;
    }

    public void setDctCondValue(String dctCondValue) {
        this.dctCondValue = dctCondValue;
    }

    public String getDctType() {
        return dctType;
    }

    public void setDctType(String dctType) {
        this.dctType = dctType;
    }

    public String getDctCalcMethod() {
        return dctCalcMethod;
    }

    public void setDctCalcMethod(String dctCalcMethod) {
        this.dctCalcMethod = dctCalcMethod;
    }

    public String getActDctId() {
        return actDctId;
    }

    public void setActDctId(String actDctId) {
        this.actDctId = actDctId;
    }

    public String getAllFree() {
        return allFree;
    }

    public void setAllFree(String allFree) {
        this.allFree = allFree;
    }

    public String getDctValue() {
        return dctValue;
    }

    public void setDctValue(String dctValue) {
        this.dctValue = dctValue;
    }

    public String getActDctCondUnit() {
        return actDctCondUnit;
    }

    public void setActDctCondUnit(String actDctCondUnit) {
        this.actDctCondUnit = actDctCondUnit;
    }

    public String getAppUser() {
        return appUser;
    }

    public void setAppUser(String appUser) {
        this.appUser = appUser;
    }

    public String getpBillFlag() {
        return pBillFlag;
    }

    public void setpBillFlag(String pBillFlag) {
        this.pBillFlag = pBillFlag;
    }

    public String getAllStationFlag() {
        return allStationFlag;
    }

    public void setAllStationFlag(String allStationFlag) {
        this.allStationFlag = allStationFlag;
    }

    public String getActStationId() {
        return actStationId;
    }

    public void setActStationId(String actStationId) {
        this.actStationId = actStationId;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getIsFromNewDetail() {
        return isFromNewDetail;
    }

    public void setIsFromNewDetail(String isFromNewDetail) {
        this.isFromNewDetail = isFromNewDetail;
    }
}
