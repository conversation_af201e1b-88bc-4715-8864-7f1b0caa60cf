package com.ls.ner.billing.mktact.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.code.CodeMethod;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.api.BillConstants.BalType;
import com.ls.ner.billing.api.BillConstants.ActSubType;
import com.ls.ner.billing.api.BillConstants.PresentCycleType;
import com.ls.ner.billing.mktact.bo.PreSectionBo;
import com.ls.ner.billing.mktact.dao.IPreSectionDao;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;

/**
 * 
 *<pre><b><font color="blue">PreSectionServiceImpl</font></b></pre>
 *
 *<pre><b>&nbsp;--定义余额预存的赠送段落/规则--</b></pre>
 * <pre></pre>
 * <pre>
 * <b>--样例--</b>
 * 
 * </pre>
 * JDK版本：JDK1.7
 * <AUTHOR>
 */
@Service(target = { ServiceType.APPLICATION }, value = "preSectionService")
public class PreSectionServiceImpl implements IPreSectionService {

	private static final Logger logger = LoggerFactory.getLogger(PreSectionServiceImpl.class);

	@Autowired(required=true)
	private IPreSectionDao preSectionDao;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	/**
	 * 获取段落列表
	 * @return
	 * <AUTHOR>
	 */
	public List<PreSectionBo> qryPreSections(PreSectionBo condition) {
		List<PreSectionBo> dataList = preSectionDao.qryPreSections(condition);
		if (CollectionUtils.isNotEmpty(dataList)){
			for (PreSectionBo sectionBo : dataList) {
				if ("-1".equals(String.valueOf(sectionBo.getMaxValue()))){
					sectionBo.setMaxValueStr("无上限");
				}else {
					sectionBo.setMaxValueStr(StringUtil.getString(sectionBo.getMaxValue()));
				}
				logger.debug(">>>>>codeValue:{},codeName:{}",sectionBo.getPresentBalType(),CodeMethod.getCodeName(BillConstants.ACT_TYPE,sectionBo.getPresentBalType()));
				sectionBo.setPresentBalTypeName(CodeMethod.getCodeName(BillConstants.ACT_TYPE,sectionBo.getPresentBalType()));
				Map<String, Object> limitMap = preSectionDao.qryTimeLimitById(sectionBo.getTimeLimitId());
				if (limitMap != null){
					if (BillConstants.TimeType.ABSOLUTELY_TIME.equals(StringUtil.getString(limitMap.get("beginTimeType")))){
						sectionBo.setBeginTimeType(BillConstants.TimeType.ABSOLUTELY_TIME);
						sectionBo.setBeginCalcObject(StringUtil.getString(limitMap.get("beginCalcObject")).split(" ")[0]);
					}else{
						sectionBo.setBeginTimeType(BillConstants.TimeType.RELATIVE_TIME);
						sectionBo.setBeginTimeDuration(new BigDecimal(StringUtil.getString(limitMap.get("beginTimeDuration"))).intValue());
						sectionBo.setBeginTimeUnit(StringUtil.getString(limitMap.get("beginTimeUnit")));
//						sectionBo.setBeginCalcObject("领取之后"+limitMap.get("beginTimeDuration")+CodeMethod.getCodeName(BillConstants.TIME_UNIT,StringUtil.getString(limitMap.get("beginTimeUnit"))));
					}
					if (BillConstants.TimeType.ABSOLUTELY_TIME.equals(StringUtil.getString(limitMap.get("endTimeType")))){
						sectionBo.setEndTimeType(BillConstants.TimeType.ABSOLUTELY_TIME);
						sectionBo.setEndCalcObject(StringUtil.getString(limitMap.get("endCalcObject")).split(" ")[0]);
					}else{
						sectionBo.setEndTimeType(BillConstants.TimeType.RELATIVE_TIME);
						sectionBo.setEndTimeDuration(new BigDecimal(StringUtil.getString(limitMap.get("endTimeDuration"))).intValue());
						sectionBo.setEndTimeUnit(StringUtil.getString(limitMap.get("endTimeUnit")));
//						sectionBo.setEndCalcObject("领取之后"+limitMap.get("endTimeDuration")+CodeMethod.getCodeName(BillConstants.TIME_UNIT,StringUtil.getString(limitMap.get("endTimeUnit"))));
					}
				}


			}

		}
		return dataList;
	}
	
	/**
	 * 获取段落listMap
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryPreSectionsMap(Map<String, Object> map) {
		return preSectionDao.qryPreSectionsMap(map);
	}
	
	/**
	 * 保存 -- 段落规则
	 * @param model
	 * @return
	 * <AUTHOR>
	 */
	public long savePreSection(PreSectionBo model) {
		logger.debug("保存段落信息入参：" + JsonUtil.obj2Json(model));
		if (model.getPresentSectionId()==0) {
			model.setPresentSectionId(seqRpcService.getDefId());
			model.setPayBalType(BalType.PAY_BAL);
			if(!ActSubType.ACT_SUB_TYPE_0702.equals(model.getPresentBalType())){
				if (PresentCycleType.ONE.equals(model.getPresentCycleType())) {
					model.setTimeLimitId(seqRpcService.getDefId());
					preSectionDao.insertTimeLimitType(model);
				} else if (PresentCycleType.CYCLE.equals(model.getPresentCycleType())){
					model.setCycleTypeId(seqRpcService.getDefId());
					preSectionDao.insertCycleType(model);
				}
			}
			preSectionDao.insertPreSection(model);
			String presentSectionDets = model.getPresentSectionDets();
			List<Map<String, Object>> detlist = new ArrayList<Map<String, Object>>();
			for (String sectionDet: presentSectionDets.split(";")) {
				Map<String, Object> detmap = IJsonUtil.parseJsonToMap(sectionDet);
				detmap.put("sectionDetId", seqRpcService.getDefId());
				detmap.put("presentSectionId", model.getPresentSectionId());
				detlist.add(detmap);
			}
			preSectionDao.insertPreSectionDet(detlist);
		}else{
			logger.info("更新周期---------");
			PreSectionBo preSectionBo = new PreSectionBo();
			preSectionBo.setActId(model.getActId());
			preSectionBo.setPresentSectionId(model.getPresentSectionId());
			List<PreSectionBo> preSectionBoList = preSectionDao.qryPreSections(preSectionBo);
			if (CollectionUtils.isNotEmpty(preSectionBoList)){
				/*if ("2".equals(model.getBeginTimeType())){
					model.setBeginCalcObject("");
				}
				if ("2".equals(model.getEndTimeType())){
					model.setEndCalcObject("");
				}
				if ("1".equals(model.getBeginTimeType())){
					model.setBeginTimeUnit("");
					model.setBeginTimeDuration(0);
				}
				if ("1".equals(model.getEndTimeType())){
					model.setEndTimeUnit("");
					model.setEndTimeDuration(0);
				}*/
				preSectionDao.deleteTimeLimitType(model.getPresentSectionId());
				model.setTimeLimitId(preSectionBoList.get(0).getTimeLimitId());
				preSectionDao.insertTimeLimitType(model);
			}
			preSectionDao.updatePreSection(model);
			String presentSectionDets = model.getPresentSectionDets();
			logger.debug("====presentSectionDets=====" + presentSectionDets);
			for (String sectionDet: presentSectionDets.split(";")) {
				Map<String, Object> detMap = IJsonUtil.parseJsonToMap(sectionDet);
				detMap.put("presentSectionId", model.getPresentSectionId());
				logger.debug("====段落明细===" + detMap);
				if(StringUtil.isNotBlank(detMap.get("sectionDetId"))){
					preSectionDao.updatePreSectionDet(detMap);
				}else{
					List<Map<String, Object>> detList = new ArrayList<Map<String, Object>>();
					detMap.put("sectionDetId", seqRpcService.getDefId());
					detList.add(detMap);
					preSectionDao.insertPreSectionDet(detList);
				}
			}
		}
		return model.getPresentSectionId();
	}
	
	/**
	 * 删除 段落规则
	 * 
	 * @param presentSectionId
	 * @return
	 * <AUTHOR>
	 */
	public void operatePreSection(Long presentSectionId) {
		preSectionDao.deleteTimeLimitType(presentSectionId);
		preSectionDao.deleteCycleType(presentSectionId);
		preSectionDao.deletePreSectionDet(presentSectionId);
		preSectionDao.deletePreSection(presentSectionId);
	}
	
	/**
	 * 根据活动ID、预存金额 获取赠送金额
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public Map<String, Object> qryPreSectionValue(Map<String, Object> map) {
		return preSectionDao.qryPreSectionValue(map);
	}

	public Map<String, Object> qryTimeLimitById(long timeLimitId) {
		return preSectionDao.qryTimeLimitById(timeLimitId);
	}

	public Map<String, Object> qryCycleById(long cycleTypeId) {
		return preSectionDao.qryCycleById(cycleTypeId);
	}

	public List<Map<String,Object>> qryPreSectionsDetInfo(Map<String, Object> map) {
		return preSectionDao.qryPreSectionDetInfo(map);
	}

	/**
	 * @param inMap
	 * @description 删除充值段落明细
	 * <AUTHOR>
	 * @create 2018-07-03 17:53:02
	 */
	@Override
	public void preSectionDetDel(Map<String, Object> inMap) {
		preSectionDao.preSectionDetDel(inMap);
	}
	/**
	 *@param
	 *@description 获取段落列表 ---新
	 *<AUTHOR>
	 *@create 2019/5/30 17:13
	 */
	public List<PreSectionBo> qryPreSectionsNew(PreSectionBo condition) {
		List<PreSectionBo> dataList = preSectionDao.qryPreSections(condition);
		if (CollectionUtils.isNotEmpty(dataList)) {
			for (PreSectionBo sectionBo : dataList) {
				if ("-1".equals(String.valueOf(sectionBo.getMaxValue()))) {
					sectionBo.setMaxValueStr("无上限");
				} else {
					sectionBo.setMaxValueStr(StringUtil.getString(sectionBo.getMaxValue()));
				}
				logger.debug(">>>>>codeValue:{},codeName:{}", sectionBo.getPresentBalType(), CodeMethod.getCodeName(BillConstants.ACT_TYPE, sectionBo.getPresentBalType()));
				sectionBo.setPresentBalTypeName(CodeMethod.getCodeName(BillConstants.ACT_TYPE, sectionBo.getPresentBalType()));
				Map<String, Object> limitMap = preSectionDao.qryTimeLimitById(sectionBo.getTimeLimitId());
				if (limitMap != null) {
					sectionBo.setBeginTimeType(limitMap.get("beginTimeType").toString());
					if (BillConstants.TimeType.ABSOLUTELY_TIME.equals(StringUtil.getString(limitMap.get("beginTimeType")))) {
						sectionBo.setEndCalcObject(StringUtil.getString(limitMap.get("endCalcObject")).split(" ")[0]);
						sectionBo.setBeginCalcObject(StringUtil.getString(limitMap.get("beginCalcObject")).split(" ")[0]);
					} else {
						sectionBo.setEndTimeDuration(Integer.parseInt(String.valueOf(limitMap.get("endTimeDuration"))));
						sectionBo.setEndTimeUnit(String.valueOf(limitMap.get("endTimeUnit")));
						sectionBo.setBeginTimeDuration(Integer.parseInt(limitMap.get("beginTimeDuration").toString()));
                        sectionBo.setBeginTimeUnit(limitMap.get("beginTimeUnit").toString());
					}
				}


			}

		}
		return dataList;
	}

}
