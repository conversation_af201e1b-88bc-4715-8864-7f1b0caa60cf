<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%
    String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="限时折扣">
    <style>
        .file-img {
            display: block;
            width: 200px;
            height: 120px;
            position: relative;
        }

        .file-img img {
            display: block;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: gray;
        }

        .file-img .span_add {
            position: absolute;
            left: 0;
            bottom: 0;
            display: none;
            width: 50%;
            height: 30px;
            line-height: 30px;
            background-repeat: no-repeat;
            background-position: center;
            background-color: rgba(0, 0, 0, .5);
            text-align: center;
            background-image: url(<%=request.getContextPath() %>/bil/mktact/img/add.png);
        }

        .file-img .span_del {
            position: absolute;
            right: 0;
            bottom: 0;
            display: none;
            width: 50%;
            height: 30px;
            line-height: 30px;
            background-repeat: no-repeat;
            background-position: center;
            background-color: rgba(0, 0, 0, .5);
            text-align: center;
            background-image: url(<%=request.getContextPath() %>/bil/mktact/img/delete.png);
        }

        .file-img:hover span {
            display: block;
        }

        .file-img input {
            position: absolute;
            font-size: 100px;
            right: 0;
            top: 0;
            opacity: 0;
        }
    </style>
    <link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/themes/default/default.css" />
    <link rel="stylesheet" href="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.css" />
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/kindeditor-all.js"></script>
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/lang/zh-CN.js"></script>
    <script charset="utf-8" src="<%=pubPath %>/pub/kindeditor/plugins/code/prettify.js"></script>
</ls:head>
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
    <ls:form id="marketactForm" name="marketactForm" enctype="multipart/form-data">
        <ls:text name="release" property="release" type="hidden"/>
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="presentSection" type="hidden"/>
        <ls:text name="buildId" property="buildId" value="0" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden" value="07"/>
        <ls:text name="actSubType" value="0701" type="hidden"/>
        <ls:text name="info" type="hidden"/>
        <table class="tab_search">
            <tr>
                <td width="10%"><ls:label text="活动名称" ref="actName"/></td>
                <td><ls:text name="actName" property="actName" required="true"/></td>
                <td rowspan="4">
                    <div>
                        <a class="file-img" href="javascript:void(0);" style="left: 200px;top: 10px;">
                            <img src="" id="headImg"></img>
                            <span id="span_add" class="span_add">
                                    <input id="fileData" accept="image/*" class="text" name="fileData"
                                           type="file" onchange="ShowImage(this)" multiple/>
                                </span>
                            <span id="span_del" class="span_del" onclick="doDelPic()"></span>
                        </a>
                    </div>
                </td>
            </tr>
            <tr>
                <td><ls:label text="生效时间" ref="effTime"/></td>
                <td width="50%">
                    <div style="float: left;width: 30%">
                        <ls:checklist name="isImmediate" type="checkbox" property="isImmediate" required="true"
                                      value="1"
                                      onchanged="getImmediate">
                            <ls:checkitem text="立即生效" value="1"/>

                        </ls:checklist>
                    </div>
                    <div style="float: left;width: 70%">
                        <span id="efftimeSpan" style="display: none">
                            <ls:date name="effTime" property="effTime" format="yyyy-MM-dd HH:mm" required="true"/>
                            </span>
                    </div>
                </td>
            </tr>
            <tr>
                <td><ls:label text="失效时间" ref="expTime"/></td>
                <td>
                    <ls:date name="expTime" property="expTime" format="yyyy-MM-dd HH:mm" required="true"/>
                </td>
            </tr>
            <tr>
                <td><ls:label text="适用对象" ref="custType"/></td>
                <td>
                    <ls:checklist name="custType" property="custType" type="checkbox" required="true" value="01">
                        <ls:checkitem text="个人" value="01"/>
                        <ls:checkitem text="企业" value="02"/>
                    </ls:checklist>
                </td>

            </tr>
        </table>
    </ls:form>
    <ls:form id="preSectionForm" name="preSectionForm">
        <ls:text name="presentSectionId" property="presentSectionId" type="hidden"/>
        <ls:text name="presentBalType" property="presentBalType" type="hidden"/>
        <ls:text name="beginTimeType" property="beginTimeType" type="hidden"/>


        <table class="tab_search">
            <tr>
                <td>
                    <ls:label text="赠送最大值" ref="maxValue"/>
                </td>
                <td>
                    <ls:text name="maxValue" property="maxValue" required="true"
                             onValidate="VD.positiveInteger(maxValue)"/>
                </td>

            </tr>
            <tr id="sectionDetTr">
                <td width="100px">
                    <ls:label text="段落明细" ref="presentSectionDets"/>
                </td>
                <td colspan="6">
                    <ls:grid url="" name="sectionDetGrid" primaryKey="sectionDetSn" cellEdit="true" treeGrid="true"
                             expandColumn="value" expand="true" treeGridModel="sectionDet" height="100px" width="100%">
                        <ls:gridToolbar name="sectionDetGridBar">
                            <ls:gridToolbarItem imageKey="add" onclick="doAddSectionDet" text="新增"/>
                            <ls:gridToolbarItem imageKey="delete" onclick="doDelSectionDet" text="删除"/>
                        </ls:gridToolbar>
                        <ls:column caption="预存段落ID" name="sectionDetId" editableEdit="false" hidden="true"/>
                        <ls:column caption="排序号" name="sectionDetSn" editableEdit="false"/>
                        <ls:column caption="起始值(>=)" name="refCeil" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="终止值(<)" name="refFloor" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="赠送值" name="baseValue" editableEdit="true">
                            <ls:textEditor required="true" type="number"/>
                        </ls:column>
                        <ls:column caption="赠送积分" name="integralNum" editableEdit="true">
                            <ls:textEditor required="false" type="number"/>
                        </ls:column>
                    </ls:grid>
                    <ls:text name="presentSectionDets" property="presentSectionDets" required="true" type="hidden"/>
                </td>
            </tr>
            <tr>
                <td>
                    <ls:label text="有效期限" ref="youxiao"/>
                    <ls:text type="hidden" name="youxiao" required="true"/></td>
                </td>
                <td width="10%">
                    <ls:checklist name="beginTimeType2" property="beginTimeType" type="radio" required="true"
                                  value="2" onchanged="changeEffetTime2">
                        <ls:checkitem text="至参与后" value="2"/>
                    </ls:checklist>
                </td>
                <td colspan="2" id="bgnTd1" width="8%">
                    <ls:text name="endTimeDuration" property="endTimeDuration" required="true"/>
                </td>
                <td id="bgnTd2" width="15%">
                    <ls:select name="endTimeUnit" property="endTimeUnit" required="true" value="1">
                        <ls:options property="timeUnitList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>
                <td>
                    <ls:label text="有效" id="youxiao1"/>
                </td>
            </tr>
            <tr>
                <td width="10%">
                    <ls:label text=""/>
                </td>
                <td>
                    <ls:checklist name="beginTimeType1" property="beginTimeType" type="radio" required="true"
                                  onchanged="changeEffetTime1">
                        <ls:checkitem text="具体日期" value="1"/>
                    </ls:checklist>
                </td>

                <td id="bgnTd3" width="15%">
                    <ls:date name="beginCalcObject" property="beginCalcObject" format="yyyy-MM-dd"/>
                </td>
                <td id="bgnTd4" width="3%">
                    <ls:label text="至" textAlign="center"/>
                </td>
                <td id="bgnTd5" width="15%">
                    <ls:date name="endCalcObject" property="endCalcObject" format="yyyy-MM-dd"/>
                </td>
                <td align="right">
                    <ls:label text="有效" id="youxiao2"/>
                </td>
            </tr>

            <tr>
                <td>
                    <ls:label text="简介"/>
                </td>
                <td colspan="6">
                    <ls:text name="presentRuleDesc" property="presentRuleDesc" type="textarea" required="true"/>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:form id="actinfoForm" name="actinfoForm">
        <ls:text name="actId" property="actId" type="hidden"/>
        <ls:text name="actType" property="actType" type="hidden"/>
        <ls:text name="actInfoId" property="actInfoId" type="hidden"/>
        <ls:text name="relaId" property="relaId" type="hidden"/>
        <ls:text name="attachId" property="attachId" type="hidden"/>
        <ls:text name="contentUrl" property="contentUrl" type="hidden"/>
        <ls:text name="picFlag" property="picFlag" type="hidden"/>
        <table align="center" class="tab_search">
            <colgroup>
                <col width="10%"/>
                <col width="90%"/>
            </colgroup>
            <tr>
                <td><ls:label text="资讯内容" ref="infoType"/></td>
                <td>
                    <ls:checklist type="radio" name="infoType" property="infoType" required="true"
                                  onchanged="doInfoType" value="1">
                        <ls:checkitems property="infoTypeList" scope="request" text="text"
                                       value="value"></ls:checkitems>
                    </ls:checklist>
                </td>
            </tr>
            <tr id="tr_content">
                <td><ls:label text="活动资讯" ref="actInfoContent"/><ls:text type="hidden" name="actInfoContent" required="true"/></td>
                <td height="180px">
                    <div id="metacontent">
                    </div>
                    <ls:text name="content" property="content" visible="false"></ls:text>
                </td>
            </tr>
            <tr id="tr_linkUrl">
                <td><ls:label text="链接网址"/></td>
                <td><ls:text name="linkUrl" property="linkUrl"/></td>
            </tr>
            <tr>
                <td colspan="2">
                    <div class="pull-right">
                        <ls:button text="保存" name="save" onclick="doSave"/>
                        <ls:button text="发布" name="submit" onclick="doSubmit"/>
                    </div>
                </td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        doSearch();

        function doSearch() {
            var actIdValue = actId.getValue();
            var balTypeValue = presentBalType.getValue();
            var sectionIdValue = presentSectionId.getValue();
            if (!LS.isEmpty(actIdValue)) {
                sectionDetGrid.query('~/cpn/sections?actId=' + actIdValue + "&presentSectionId=" + sectionIdValue + "&presentBalType=" + balTypeValue, null);
            }
        }


        var editor;
        var pubPath = '${pubPath}';

        KindEditor.ready(function(K) {
        editor = K.create('#metacontent', {
        cssPath : pubPath + '/pub/kindeditor/plugins/code/prettify.css',
        uploadJson : pubPath + '/pub/picture/upload',
        items : [
        'undo', 'redo', '|', 'preview', 'template', 'cut', 'copy', 'paste',
        'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
        'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
        'superscript', 'clearhtml', 'quickformat', 'selectall', '|', 'fullscreen', '/',
        'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
        'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image',
        'table', 'hr', 'emoticons', 'pagebreak',
        'link', 'unlink'
        ],
        extraFileUploadParams : {
        relaTable: 'mktActInfo',
        relaId: actId.getValue()
        },
        afterUpload : function(data) {

        },
        <%--            fileManagerJson : pref + '/jsp/file_manager_json.jsp',--%>
        allowFileManager : true,
        afterCreate : function() {
        }
        });
        prettyPrint();
        });


        window.onload = function () {
            if (!LS.isEmpty(isImmediate.getValue()) && isImmediate.getValue() == '0') {
                $('#efftimeSpan').show();
            }
            if (!LS.isEmpty(content.getValue())) {
                editor.html(content.getValue());
                contentUrl.setValue(content.getValue())
            }
            var _actInfoId = actInfoId.getValue();
            var _attachId = attachId.getValue();
            var _relaId = relaId.getValue();
            headImg.src = pubPath + '/pub/image/js/add.png';
            if (LS.isEmpty(_actInfoId) || LS.isEmpty(_attachId)) {
                headImg.src = pubPath + '/pub/image/js/add.png';
            } else {
                headImg.src = pubPath + '/attach/attachs/mkt_act_info/02/' + _relaId;
            }
            doInfoType();
        }

        function addAct() {
            actDctGrid.addRow();
        }

        // 描述:删除  创建人:biaoxiangd  创建时间:2017/6/30 11:18
        function delAct() {
            var checkedItems = actDctGrid.getCheckedItems();
            if (checkedItems == null || checkedItems.length != 1) {
                LS.message("info", "请选择一条记录!");
                return;
            }
            var allItems = actDctGrid.getItems();
            if (allItems && allItems.length > 0) {
                for (var i = 0; i < allItems.length; i++) {
                    if (checkedItems[0].id == allItems[i].id) {
                        var _actDctProdId = allItems[i].actDctProdId;
                        var _actDctProdObj = actDctProdObj.getValue();
                        if (!LS.isEmpty(_actDctProdId) && !LS.isEmpty(_actDctProdObj)) {
                            var bk = _actDctProdObj.replace(_actDctProdId, '');
                            actDctProdObj.setValue(bk);
                        }
                        actDctGrid.removeItem(allItems[i]);
                    }
                }
            }
        }

        //描述:资讯内容变更  创建人:biaoxiangd  创建时间:2017/6/28 10:52
        function doInfoType() {
            var _infoType = infoType.getValue();
            if ('2' == _infoType) {
                $('#tr_content').hide();
                $('#tr_linkUrl').show();
            } else {
                $('#tr_content').show();
                $('#tr_linkUrl').hide();
            }
        }

        // 描述:添加图片  创建人:biaoxiangd  创建时间:2017/6/28 14:24
        window.ShowImage = function (file) {
            if ($.browser.msie) {
                $("#headImg").attr("src", $(this).val())
            } else {
                if (file.files || file.files[0] != undefined) {
                    var objUrl = getObjectURL(file.files[0]);
                    if (objUrl) {
                        $("#headImg").attr("src", objUrl);
                        picFlag.setValue("0");
                    }
                }
            }
        }

        // 描述:删除图片  创建人:biaoxiangd  创建时间:2017/6/28 14:21
        window.doDelPic = function () {
            var _picFlag = picFlag.getValue();
            if ('0' == _picFlag) {
                headImg.src = pubPath + '/pub/image/js/add.png';
                picFlag.setValue('1');
            }
        }

        function getObjectURL(file) {
            var url = null;
            if (file != undefined) {
                if (window.createObjectURL != undefined) {
                    url = window.createObjectURL(file);
                } else if (window.URL != undefined) {
                    url = window.URL.createObjectURL(file);
                } else if (window.webkitURL != undefined) {
                    url = window.webkitURL.createObjectURL(file);
                }
            }
            return url;
        }

        function getImmediate() {
            if (isImmediate.getValue() != 1) {
                isImmediate.setValue("0");
                $('#efftimeSpan').show();
            } else {
                $('#efftimeSpan').hide();
            }
        }


        function doSave() {
            release.setValue("false");
            doSaveBase(1);
        }

        function doSubmit() {
            release.setValue("true");

            var imme = isImmediate.getValue();
            var eff = effTime.getValue();
            var exp = expTime.getValue();
            var nowDate = new Date();
            var current = (nowDate).Format("yyyy-MM-dd hh:mm");

            //1、立即生效：生效时间为当前，失效时间大于当前时间1分钟，状态进行中。
            if (imme == 1) {
                effTime.setValue(current)
            }
            if (imme == 1 && new Date(exp) - nowDate < 60 * 1000) {
                effTime.setValue(current)
                LS.message("info", "立即生效时，失效时间必须大于当前时间1分钟！");
                return;
            }
            //2、无立即生效：生效时间大于当前时间30分钟，失效时间大于生效时间时间1分钟，状态未开始
            if (imme == 0 && LS.isEmpty(eff)) {
                LS.message("info", "请选择生效时间！");
                return;
            }
            if (imme == 0 && new Date(eff) - nowDate < 30 * 60 * 1000) {
                LS.message("info", "生效时间必须大于当前时间30分钟！");
                return;
            }

            if (imme == 0 && new Date(exp) - new Date(eff) < 60 * 1000) {
                LS.message("info", "失效时间必须大于生效时间时1分钟！");
                return;
            }
            if (!LS.isEmpty(exp) && current > exp) {
                LS.message("info", "失效时间不能小于当前时间。");
                return;
            }
            if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff > exp) {
                LS.message("info", "生效时间不能大于失效时间。");
                return;
            }
            doSaveBase(2);
        }

        function doSaveBase(opType) {
            //将富文本内容设置回文本框
            var html = editor.html();
            content.setValue(html);
            contentUrl.setValue(html);

            var eff = effTime.getValue();
            var exp = expTime.getValue();
            var actNameValue = actName.getValue();

            if (LS.isEmpty(actNameValue)) {
                LS.message("info", "请输入活动名称！");
                return;
            }
            if (!LS.isEmpty(actNameValue) && actNameValue.length > 32) {
                LS.message("info", "活动名称过长，请重新输入！");
                return;
            }
            if (isImmediate.getValue() == 0 && LS.isEmpty(eff)) {
                LS.message("info", "请选择生效时间！");
                return;
            }
            if (LS.isEmpty(exp)) {
                LS.message("info", "请选择失效时间！");
                return;
            }
            if (LS.isEmpty(custType.getValue())) {
                LS.message("info", "请选择适用对象！");
                return;
            }
            if (!sectionDetGrid) {
                LS.message("error", "出错了，请联系管理员");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                LS.message("info", "段落明细请至少设置一条记录！");
                return;
            }
            //段落明细
            var _beginTimeType1 = beginTimeType1.getValue();
            var _beginTimeType2 = beginTimeType2.getValue();
            if (!LS.isEmpty(_beginTimeType1) && LS.isEmpty(_beginTimeType2)) {
                beginTimeType.setValue(_beginTimeType1);
                if (LS.isEmpty(beginCalcObject.getValue())) {
                    LS.message("info", "有效期限不能为空，请填写完整");
                    return;
                }
                if (LS.isEmpty(endCalcObject.getValue())) {
                    LS.message("info", "有效期限不能为空，请填写完整");
                    return;
                }
            }
            if (!LS.isEmpty(_beginTimeType2) && LS.isEmpty(_beginTimeType1)) {
                beginTimeType.setValue(_beginTimeType2);
                if (LS.isEmpty(endTimeDuration.getValue())) {
                    LS.message("info", "有效期限不能为空，请填写完整");
                    return;
                }
                if (endTimeDuration.getValue()==0) {
                    LS.message("info", "至参与后时限必须大于0");
                    return;
                }
                if (LS.isEmpty(endTimeUnit.getValue())) {
                    LS.message("info", "有效期限不能为空，请填写完整");
                    return;
                }
            }


            var newItems = [];
            var baseValueTotal = Number('0');
            var _maxValue = Number(maxValue.getValue());
            if (_maxValue <= 0) {
                LS.message("info", "赠送最大值必须大于0！");
                return;
            }
            for (var m = 0; m < items.length; m++) {
                var _baseValue = Number(items[m].baseValue);
                var _refCeil = Number(items[m].refCeil);
                var _refFloor = Number(items[m].refFloor);
                if (_baseValue <= 0) {
                    LS.message("info", "赠送值必须大于0！");
                    return;
                }
                if (_refFloor <= 0) {
                    LS.message("info", "终止值必须大于0！");
                    return;
                }

                baseValueTotal +=
                    newItems.push(JSON.stringify(items[m]));
            }

            var items = sectionDetGrid.getItems();
            for (var i = 0; i < items.length; i++) {
                if (parseInt(items[0].refCeil) < 0) {
                    LS.message("info", "排序号1起始值不能小于0！");
                    return;
                }
                if (parseInt(items[i].refFloor) < parseInt(items[i].refCeil)) {
                    LS.message("info", "排序号" + items[i].sectionDetSn + "终止值不能小于起始值！");
                    return;
                }
                for (var j = 0; j < items.length; j++) {
                    if ((parseInt(items[i].refCeil) < parseInt(items[j].refCeil)) && (parseInt(items[i].sectionDetSn) != parseInt(items[j].sectionDetSn))) {
                        if (parseInt(items[j].refCeil) < parseInt(items[i].refFloor)) {
                            LS.message("info", "排序号" + items[j].sectionDetSn + "起始值必须大于等于" + "排序号" + items[i].sectionDetSn + "的终止值！");
                            return;
                        }
                    } else if ((parseInt(items[i].refCeil) == parseInt(items[j].refCeil)) && (parseInt(items[i].sectionDetSn) != parseInt(items[j].sectionDetSn))) {
                        LS.message("info", "排序号" + items[i].sectionDetSn + "和" + items[j].sectionDetSn + "起始值不能一样！");
                        return;
                    }
                }
            }
            presentSectionDets.setValue(newItems.join(";"));
            var presentSectionDate = preSectionForm.getFormData()
            presentSection.setValue(JSON.stringify(presentSectionDate));


            //资讯内容
            var _infoType = infoType.getValue();
            if ('1' == _infoType) {
                if (LS.isEmpty(content.getValue())) {
                    LS.message("error", "发布活动资讯信息中的内容为空");
                    return;
                }
                linkUrl.setValue("");
            } else {
                if (LS.isEmpty(linkUrl.getValue())) {
                    LS.message("error", "发布活动资讯信息中的外部链接为空");
                    return;
                }
                content.setValue("");
                contentUrl.setValue("");
            }
            var infoDate = actinfoForm.getFormData()
            info.setValue(JSON.stringify(infoDate));
            marketactForm.submit("~/markertAct/marketacts/saving", function (e) {
                var result = e.items[0];
                if (result == "T") {
                    if(opType==1){
                        LS.message("info", "保存成功！");
                    }
                    if(opType==2){
                        LS.message("info", "发布成功！");
                    }
                    LS.parent().doSearch();
                    LS.window.close();
                }else if(result.indexOf("时间冲突")>0){
                    LS.message("info", e.items[0]);
                }else {
                    LS.message("info", result);
                }
            });


        }


        //新增
        function doAddSectionDet() {
            var items = sectionDetGrid.getItems();
            if (items.length == 0) {
                var data = {sectionDetSn: '1', refCeil: '', refFloor: '', integralNum: 0};
                sectionDetGrid.appendItem(data);
            } else {
                if (LS.isEmpty(items[items.length - 1].refFloor)) {
                    LS.message("error", "上一条终止值不能为空！");
                    return;
                }
                var data = {
                    sectionDetSn: items.length + 1,
                    refCeil: parseInt(items[items.length - 1].refFloor),
                    refFloor: '',
                    integralNum: 0
                };
                sectionDetGrid.appendItem(data);
            }
        }

        //删除
        function doDelSectionDet() {
            var item = sectionDetGrid.getSelectedItem();
            if (LS.isEmpty(item)) {
                LS.message("error", "请选择要删除的记录");
                return;
            }
            var items = sectionDetGrid.getItems();
            if (items.length == 1 || item.sectionDetSn == items[0].sectionDetSn) {
                LS.message("error", "第一条记录不能删除");
                return;
            }
            var newItems = [];
            sectionDetGrid.setCell(parseInt(item.sectionDetSn) + 1, "refCeil", items[parseInt(item.sectionDetSn) - 2].refFloor);
            items = sectionDetGrid.getItems();
            for (var i = 0; i < items.length; i++) {
                if (item.sectionDetSn != items[i].sectionDetSn) {
                    newItems.push(items[i]);
                } else {
                    if (!LS.isEmpty(item.sectionDetId)) {
                        LS.ajax("~/sections/cpn/delete", {sectionDetId: item.sectionDetId}, function (e) {
                        });
                    }
                }
            }
            sectionDetGrid.removeAllItems();
            for (var i = 0; i < newItems.length; i++) {
                newItems[i].sectionDetSn = i + 1;
            }
            sectionDetGrid.appendItem(newItems);
        }

        function choiceCoupon(rowid, rowdata, rowEditor) {
            rowId = rowid;
            rowData = rowdata;
            LS.dialog("~/billing/coupon/couponManage/06?singleSelect=false", "选择优惠券", 950, 600, true);
        }



        function changeEffetTime2() {
            var _beginTimeType1 = beginTimeType1.getValue();
            var _beginTimeType2 = beginTimeType2.getValue();

            if (LS.isEmpty(_beginTimeType1) && LS.isEmpty(_beginTimeType2)) {
                LS.message("info", "有效期限必须选择一种模式！");
                beginTimeType2.setValue("2");
                return;
            }
            $('#bgnTd1').show();
            $('#bgnTd2').show();
            $('#bgnTd3').hide();
            $('#bgnTd4').hide();
            $('#bgnTd5').hide();

            document.getElementById('youxiao2').setAttribute("style","display: none");
            document.getElementById('youxiao1').setAttribute("style","width:10%");

            if (_beginTimeType2 == 2) {
                beginTimeType1.setValue("");
            } else {
                beginTimeType1.setValue("1");
            }
        }

        function changeEffetTime1() {
            var _beginTimeType1 = beginTimeType1.getValue();
            var _beginTimeType2 = beginTimeType2.getValue();

            if (LS.isEmpty(_beginTimeType1) && LS.isEmpty(_beginTimeType2)) {
                LS.message("info", "有效期限必须选择一种模式！");
                beginTimeType1.setValue("1");
                return;
            }
            $('#bgnTd1').hide();
            $('#bgnTd2').hide();
            $('#bgnTd3').show();
            $('#bgnTd4').show();
            $('#bgnTd5').show();

            document.getElementById('youxiao1').setAttribute("style","display: none");
            document.getElementById('youxiao2').setAttribute("style","width:10%");
            if (_beginTimeType1 == 1) {
                beginTimeType2.setValue("");
            } else {
                beginTimeType2.setValue("2");
            }
        }

        init();

        function init() {
            changeEffetTime2();
            changeEffetTime1();

            var _beginTimeType1 = beginTimeType1.getValue();
            var _beginTimeType2 = beginTimeType2.getValue();

            if (!LS.isEmpty(_beginTimeType2) && _beginTimeType2=="2"){
                $('#bgnTd1').show();
                $('#bgnTd2').show();
                $('#bgnTd3').hide();
                $('#bgnTd4').hide();
                $('#bgnTd5').hide();
                document.getElementById('youxiao2').setAttribute("style","display: none");
                document.getElementById('youxiao1').setAttribute("style","width:10%");
            }

            if (!LS.isEmpty(_beginTimeType1) && _beginTimeType1=="1"){
                $('#bgnTd1').hide();
                $('#bgnTd2').hide();
                $('#bgnTd3').show();
                $('#bgnTd4').show();
                $('#bgnTd5').show();
                document.getElementById('youxiao1').setAttribute("style","display: none");
                document.getElementById('youxiao2').setAttribute("style","width:10%");
            }
        }
    </ls:script>
</ls:body>
</html>