package com.ls.ner.billing.market.service.impl;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.dao.IntegralExchangeDao;
import com.ls.ner.billing.market.service.IIntegralExchangeService;
import com.ls.ner.billing.market.vo.IntegralExchangeVo;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service(target = {ServiceType.LOCAL}, value = "integralExchangeService")
public class IIntegralExchangeServiceImpl implements IIntegralExchangeService {
    private static final Logger log = LoggerFactory.getLogger(IIntegralExchangeServiceImpl.class);


    @Autowired
    private IntegralExchangeDao integralExchangeDao;

    @Override
    public List<IntegralExchangeVo> getIntegralExchangeList(IntegralExchangeVo integralExchangeVo) {

        return integralExchangeDao.getIntegralExchangeList(integralExchangeVo);
    }

    @Override
    public int getIntegralExchangeListCount(IntegralExchangeVo integralExchangeVo) {

        return integralExchangeDao.getIntegralExchangeListCount(integralExchangeVo);
    }

    /**
     * 保存
     */
    public void save(IntegralExchangeVo integralExchangeVo) {
        IntegralExchangeVo newIntegralExchangeVo = new IntegralExchangeVo();
        newIntegralExchangeVo.setIntegralId(integralExchangeVo.getIntegralId());
        newIntegralExchangeVo.setLogisticsNo(integralExchangeVo.getLogisticsNo());
        newIntegralExchangeVo.setLogisticsName(integralExchangeVo.getLogisticsName());
        newIntegralExchangeVo.setStatus("02");//已发货
        integralExchangeDao.update(newIntegralExchangeVo);
    }

}
