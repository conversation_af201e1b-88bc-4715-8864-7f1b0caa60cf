<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ls.ner.billing.common.dao.DepositItemMapper" >
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.DepositItem" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" property="systemId" jdbcType="BIGINT" />
    <result column="PLAN_NO" property="planNo" jdbcType="VARCHAR" />
    <result column="DEPOSIT_NO" property="depositNo" jdbcType="VARCHAR" />
    <result column="ITEM_NAME" property="itemName" jdbcType="VARCHAR" />
    <result column="ITEM_NO" property="itemNo" jdbcType="VARCHAR" />
    <result column="DATA_OPER_TIME" property="dataOperTime" jdbcType="TIMESTAMP" />
    <result column="DATA_OPER_TYPE" property="dataOperType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, PLAN_NO, DEPOSIT_NO, ITEM_NAME, ITEM_NO, DATA_OPER_TIME, DATA_OPER_TYPE
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.ls.ner.billing.common.bo.DepositItemExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_deposit_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_deposit_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_deposit_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.DepositItemExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_deposit_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.DepositItem" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_deposit_item (SYSTEM_ID, PLAN_NO, DEPOSIT_NO, 
      ITEM_NAME, ITEM_NO, DATA_OPER_TIME, 
      DATA_OPER_TYPE)
    values (#{systemId,jdbcType=BIGINT}, #{planNo,jdbcType=VARCHAR}, #{depositNo,jdbcType=VARCHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{itemNo,jdbcType=VARCHAR}, #{dataOperTime,jdbcType=TIMESTAMP}, 
      #{dataOperType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.DepositItem" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_deposit_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="systemId != null" >
        SYSTEM_ID,
      </if>
      <if test="planNo != null" >
        PLAN_NO,
      </if>
      <if test="depositNo != null" >
        DEPOSIT_NO,
      </if>
      <if test="itemName != null" >
        ITEM_NAME,
      </if>
      <if test="itemNo != null" >
        ITEM_NO,
      </if>
      <if test="dataOperTime != null" >
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null" >
        DATA_OPER_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="systemId != null" >
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="planNo != null" >
        #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="depositNo != null" >
        #{depositNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null" >
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null" >
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null" >
        #{dataOperType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.DepositItemExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_deposit_item
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_deposit_item
    <set >
      <if test="record.systemId != null" >
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.planNo != null" >
        PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      </if>
      <if test="record.depositNo != null" >
        DEPOSIT_NO = #{record.depositNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null" >
        ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemNo != null" >
        ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null" >
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null" >
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_deposit_item
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      PLAN_NO = #{record.planNo,jdbcType=VARCHAR},
      DEPOSIT_NO = #{record.depositNo,jdbcType=VARCHAR},
      ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.DepositItem" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_deposit_item
    <set >
      <if test="planNo != null" >
        PLAN_NO = #{planNo,jdbcType=VARCHAR},
      </if>
      <if test="depositNo != null" >
        DEPOSIT_NO = #{depositNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null" >
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null" >
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null" >
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null" >
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.DepositItem" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_deposit_item
    set PLAN_NO = #{planNo,jdbcType=VARCHAR},
      DEPOSIT_NO = #{depositNo,jdbcType=VARCHAR},
      ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>