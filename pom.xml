<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ls.ner</groupId>
        <artifactId>ner-boot-parent</artifactId>
        <version>0.0.1</version>
		<relativePath>../ner-boot-parent</relativePath>
	</parent>
	<groupId>com.ls.ner.bil</groupId>
	<artifactId>ner-bil-boot</artifactId>
	<version>0.0.1</version>
	<name>ner-bil-boot</name>
	<description>ner-bil-boot-parent</description>
    <packaging>pom</packaging>
	<properties>
		<java.version>1.8</java.version>
	</properties>

    <modules>
        <module>ner-bil-web</module>
        <module>ner-bil-api</module>
        <module>ner-bil</module>
    </modules>

	<dependencies>
        <dependency>
            <groupId>com.ls.ner</groupId>
    		<artifactId>ner-bootstrap</artifactId>
            <version>0.0.1</version>
        </dependency>
	</dependencies>



</project>
