package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BillingOrderRelaBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public BillingOrderRelaBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andAppNoIsNull() {
            addCriterion("APP_NO is null");
            return (Criteria) this;
        }

        public Criteria andAppNoIsNotNull() {
            addCriterion("APP_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAppNoEqualTo(String value) {
            addCriterion("APP_NO =", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotEqualTo(String value) {
            addCriterion("APP_NO <>", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoGreaterThan(String value) {
            addCriterion("APP_NO >", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoGreaterThanOrEqualTo(String value) {
            addCriterion("APP_NO >=", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLessThan(String value) {
            addCriterion("APP_NO <", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLessThanOrEqualTo(String value) {
            addCriterion("APP_NO <=", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLike(String value) {
            addCriterion("APP_NO like", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotLike(String value) {
            addCriterion("APP_NO not like", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoIn(List<String> values) {
            addCriterion("APP_NO in", values, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotIn(List<String> values) {
            addCriterion("APP_NO not in", values, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoBetween(String value1, String value2) {
            addCriterion("APP_NO between", value1, value2, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotBetween(String value1, String value2) {
            addCriterion("APP_NO not between", value1, value2, "appNo");
            return (Criteria) this;
        }
        
        public Criteria andBillTypeIsNull() {
            addCriterion("BILL_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNotNull() {
            addCriterion("BILL_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andBillTypeEqualTo(String value) {
            addCriterion("BILL_TYPE =", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotEqualTo(String value) {
            addCriterion("BILL_TYPE <>", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThan(String value) {
            addCriterion("BILL_TYPE >", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanOrEqualTo(String value) {
            addCriterion("BILL_TYPE >=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThan(String value) {
            addCriterion("BILL_TYPE <", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanOrEqualTo(String value) {
            addCriterion("BILL_TYPE <=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLike(String value) {
            addCriterion("BILL_TYPE like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotLike(String value) {
            addCriterion("BILL_TYPE not like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeIn(List<String> values) {
            addCriterion("BILL_TYPE in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotIn(List<String> values) {
            addCriterion("BILL_TYPE not in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeBetween(String value1, String value2) {
            addCriterion("BILL_TYPE between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotBetween(String value1, String value2) {
            addCriterion("BILL_TYPE not between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNull() {
            addCriterion("SUB_BE is null");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNotNull() {
            addCriterion("SUB_BE is not null");
            return (Criteria) this;
        }

        public Criteria andSubBeEqualTo(String value) {
            addCriterion("SUB_BE =", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotEqualTo(String value) {
            addCriterion("SUB_BE <>", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThan(String value) {
            addCriterion("SUB_BE >", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThanOrEqualTo(String value) {
            addCriterion("SUB_BE >=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThan(String value) {
            addCriterion("SUB_BE <", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThanOrEqualTo(String value) {
            addCriterion("SUB_BE <=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLike(String value) {
            addCriterion("SUB_BE like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotLike(String value) {
            addCriterion("SUB_BE not like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIn(List<String> values) {
            addCriterion("SUB_BE in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotIn(List<String> values) {
            addCriterion("SUB_BE not in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeBetween(String value1, String value2) {
            addCriterion("SUB_BE between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotBetween(String value1, String value2) {
            addCriterion("SUB_BE not between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNotNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is not null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY =", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <>", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY not like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY not in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY not between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andVersionLimitIsNull() {
            addCriterion("VERSION_LIMIT is null");
            return (Criteria) this;
        }

        public Criteria andVersionLimitIsNotNull() {
            addCriterion("VERSION_LIMIT is not null");
            return (Criteria) this;
        }

        public Criteria andVersionLimitEqualTo(String value) {
            addCriterion("VERSION_LIMIT =", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitNotEqualTo(String value) {
            addCriterion("VERSION_LIMIT <>", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitGreaterThan(String value) {
            addCriterion("VERSION_LIMIT >", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitGreaterThanOrEqualTo(String value) {
            addCriterion("VERSION_LIMIT >=", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitLessThan(String value) {
            addCriterion("VERSION_LIMIT <", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitLessThanOrEqualTo(String value) {
            addCriterion("VERSION_LIMIT <=", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitLike(String value) {
            addCriterion("VERSION_LIMIT like", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitNotLike(String value) {
            addCriterion("VERSION_LIMIT not like", value, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitIn(List<String> values) {
            addCriterion("VERSION_LIMIT in", values, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitNotIn(List<String> values) {
            addCriterion("VERSION_LIMIT not in", values, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitBetween(String value1, String value2) {
            addCriterion("VERSION_LIMIT between", value1, value2, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andVersionLimitNotBetween(String value1, String value2) {
            addCriterion("VERSION_LIMIT not between", value1, value2, "versionLimit");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoIsNull() {
            addCriterion("DEFAULT_BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoIsNotNull() {
            addCriterion("DEFAULT_BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoEqualTo(String value) {
            addCriterion("DEFAULT_BILLING_NO =", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoNotEqualTo(String value) {
            addCriterion("DEFAULT_BILLING_NO <>", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoGreaterThan(String value) {
            addCriterion("DEFAULT_BILLING_NO >", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoGreaterThanOrEqualTo(String value) {
            addCriterion("DEFAULT_BILLING_NO >=", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoLessThan(String value) {
            addCriterion("DEFAULT_BILLING_NO <", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoLessThanOrEqualTo(String value) {
            addCriterion("DEFAULT_BILLING_NO <=", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoLike(String value) {
            addCriterion("DEFAULT_BILLING_NO like", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoNotLike(String value) {
            addCriterion("DEFAULT_BILLING_NO not like", value, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoIn(List<String> values) {
            addCriterion("DEFAULT_BILLING_NO in", values, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoNotIn(List<String> values) {
            addCriterion("DEFAULT_BILLING_NO not in", values, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoBetween(String value1, String value2) {
            addCriterion("DEFAULT_BILLING_NO between", value1, value2, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andDefaultBillingNoNotBetween(String value1, String value2) {
            addCriterion("DEFAULT_BILLING_NO not between", value1, value2, "defaultBillingNo");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosIsNull() {
            addCriterion("SELECTED_ATTACH_ITEM_NOS is null");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosIsNotNull() {
            addCriterion("SELECTED_ATTACH_ITEM_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosEqualTo(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS =", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosNotEqualTo(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS <>", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosGreaterThan(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS >", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosGreaterThanOrEqualTo(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS >=", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosLessThan(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS <", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosLessThanOrEqualTo(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS <=", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosLike(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS like", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosNotLike(String value) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS not like", value, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosIn(List<String> values) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS in", values, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosNotIn(List<String> values) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS not in", values, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosBetween(String value1, String value2) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS between", value1, value2, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andSelectedAttachItemNosNotBetween(String value1, String value2) {
            addCriterion("SELECTED_ATTACH_ITEM_NOS not between", value1, value2, "selectedAttachItemNos");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIsNull() {
            addCriterion("DATE_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIsNotNull() {
            addCriterion("DATE_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE =", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE <>", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeGreaterThan(String value) {
            addCriterion("DATE_OPER_TYPE >", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE >=", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLessThan(String value) {
            addCriterion("DATE_OPER_TYPE <", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE <=", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLike(String value) {
            addCriterion("DATE_OPER_TYPE like", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotLike(String value) {
            addCriterion("DATE_OPER_TYPE not like", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIn(List<String> values) {
            addCriterion("DATE_OPER_TYPE in", values, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotIn(List<String> values) {
            addCriterion("DATE_OPER_TYPE not in", values, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeBetween(String value1, String value2) {
            addCriterion("DATE_OPER_TYPE between", value1, value2, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATE_OPER_TYPE not between", value1, value2, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIsNull() {
            addCriterion("PRICING_AMT is null");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIsNotNull() {
            addCriterion("PRICING_AMT is not null");
            return (Criteria) this;
        }

        public Criteria andPricingAmtEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT =", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT <>", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtGreaterThan(BigDecimal value) {
            addCriterion("PRICING_AMT >", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT >=", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtLessThan(BigDecimal value) {
            addCriterion("PRICING_AMT <", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT <=", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIn(List<BigDecimal> values) {
            addCriterion("PRICING_AMT in", values, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotIn(List<BigDecimal> values) {
            addCriterion("PRICING_AMT not in", values, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICING_AMT between", value1, value2, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICING_AMT not between", value1, value2, "pricingAmt");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated do_not_delete_during_merge Wed Mar 16 15:59:45 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}