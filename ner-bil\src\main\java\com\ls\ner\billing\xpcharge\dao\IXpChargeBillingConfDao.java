package com.ls.ner.billing.xpcharge.dao;

import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;

import java.util.List;


/**
 * <AUTHOR>
 * @dateTime 2018-03-16
 * @description 充电计费配置  E_CHARGE_BILLING_CONF
 */
public interface IXpChargeBillingConfDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-16
	 * @description 查询充电计费配置E_CHARGE_BILLING_CONF
	 */
	public List<ChargeBillingConfBo> queryXpChargeBillingConfs(XpChargeBillingConfQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-16
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
	 */
	public int queryXpChargeBillingConfsNum(XpChargeBillingConfQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电分时设置  E_CHARGE_PERIODS
	 */
	public List<ChargePeriodsBo> queryChargePeriods(XpChargeBillingConfQueryCondition bo);

	/**
	 * 通过站点查询计费配置信息
	 * @param bo
	 * @return
	 */
	public List<ChargeBillingConfBo> queryXpChargeBillingConfInfo(XpChargeBillingConfQueryCondition bo);

	/**
	 * 通过站点查询计费配置信息
	 * @param bo
	 * @return
	 */
	public List<ChargeBillingConfBo> queryEffectChargeBillingConfs(XpChargeBillingConfQueryCondition bo);


}
