package com.ls.ner.billing.tariff2.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.owasp.esapi.ESAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.base.constants.AssistConstants;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.tariff2.service.ITariffPlanService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.IdRequestBody;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.IDRequestObject;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 资费标准管理Controller
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/billing/tariffplans")
public class TariffPlanController extends QueryController<TariffPlanBo> {

	private static final String TARIFF_PLAN_FORM = "tariffPlanForm";
	private static final String VIEW_TARIFF_PLAN_GRID = "/bil/tariff/tariffPlanGrid";
	private static final String VIEW_TARIFF_PLAN_FORM = "/bil/tariff/tariffPlanForm";

	@Autowired
	private ControllerSupport controllerSupport;
	
	@ServiceAutowired
	Authentication authentication;
	
	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	
	@ServiceAutowired("tariffPlanService")
	private ITariffPlanService tariffPlanService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ICodeService codeService;
	
	/**
	 * 资费标准管理页面初始化  （包含选择资费标准弹出窗口）
	 * @param m
	 * @return
	 */
	@RequestMapping(value="/init",method = RequestMethod.GET)
	public String init(Model m,HttpServletRequest request) {
		//flag用于选界面
		String flag=ESAPI.encoder().encodeForJavaScript(request.getParameter("flag"));
		if(!StringUtils.nullOrBlank(flag)){
			m.addAttribute("subBe", ESAPI.encoder().encodeForJavaScript(request.getParameter("subBe")));
		}
		request.setAttribute("flag", flag);
		controllerSupport.addStCodes(m, BillingConstants.ValidFlag.CODE_TYPE);
		TariffPlanBo bo = new TariffPlanBo();
		 AccountBo currentAccount = authentication.getCurrentAccount();
		 OrgBo orgBo = orgService.getRootOrgs().get(0);
			if(orgBo != null){
				bo.setOrgCode(orgBo.getOrgCode());
				bo.setOrgCodeName(orgBo.getOrgShortName());			
			}
		m.addAttribute("tariffPlanQueryForm", bo);
		m.addAttribute("rentTypeList", code2ItemList(codeService.getAllStandardCodes(SubBe.CODE_TYPE, null)));
		return VIEW_TARIFF_PLAN_GRID;
	}
	
	/**
	 * 资费标准详情页面
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/showTariff/{planNo}", method = RequestMethod.GET)
	public String showTariff(Model m, @PathVariable("planNo")
	String planNo) {
		setStCodes(m);
		m.addAttribute("operFlag","show");
		m.addAttribute("readOnly","true");
		setTariffBoEntity(m, planNo);
//		setPlanNo(m, planNo);
		return VIEW_TARIFF_PLAN_FORM;
	}

	protected void setTariffBoEntity(Model m, String planNo) {
		TariffPlanBo tariffPlanBo = tariffPlanService.getByPlanNo(planNo);
		m.addAttribute(TARIFF_PLAN_FORM, tariffPlanBo);
	}

	/**
	 * 资费标准表格数据 查询
	 * @param params
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value="/getTariffPlanList")
	public @ItemResponseBody
	QueryResultObject getTariffPlanList(@QueryRequestParam("params")
	RequestCondition params) throws Exception {
		TariffPlanBo bo = this.rCondition2QCondition(params);
		bo.setPageBegin(bo.getPageBegin()-1);
		List<TariffPlanBo> list = tariffPlanService.getTariffPlanList(bo);
		return RestUtils.wrappQueryResult(list, bo.getRows());
	}

	/**
	 * 资费标准新增 表单页面初始化
	 * @param m
	 * @return
	 */
	@RequestMapping(value = "/initAdd", method = RequestMethod.GET)
	public String initAdd(Model m) {
		setStCodes(m);
		m.addAttribute("operFlag","add");
		m.addAttribute("readOnly","false");
		TariffPlanBo bo = new TariffPlanBo();
		bo.setChargeWay(ChargeWay.BY_TIME);
		bo.setChargeWay(ChargeMode.STANDARD);
		 AccountBo currentAccount = authentication.getCurrentAccount();
		OrgBo orgBo = orgService.getRootOrgs().get(0);
		if(orgBo != null){
			bo.setOrgCode(orgBo.getOrgCode());
			bo.setOrgCodeName(orgBo.getOrgShortName());			
		}
		m.addAttribute(TARIFF_PLAN_FORM, bo);
		return VIEW_TARIFF_PLAN_FORM;
	}

	protected void setStCodes(Model m) {
		//单选按钮组用
		controllerSupport.addCheckListMaps(m,ChargeWay.CODE_TYPE,ChargeMode.CODE_TYPE);
		//普通的标准代码用
		controllerSupport.addStCodes(m,PriceUnit.CODE_TYPE);
		m.addAttribute("rentTypeList", code2ItemList(codeService.getAllStandardCodes(SubBe.CODE_TYPE, null)));
	}
	/**
	 * 资费标准新增 保存 
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/saveTariffPlan", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveTariffPlan(Model m, @RequestBody Map<String, Object> dataParams) {
		TariffPlanBo bo = getBean(dataParams);
		String planNo = tariffPlanService.create(bo);
		return WrappedResult.successWrapedResult(planNo);
	}
	protected TariffPlanBo getBean(Map<String, Object> dataParams) {
		TariffPlanBo bo = new TariffPlanBo();
		controllerSupport.populateSubmitParams(dataParams, bo);
		
		return bo;
	}
	/**
	 * 资费标准修改页面初始化
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/initEdit/{planNo}", method = RequestMethod.GET)
	public String initEdit(Model m, @PathVariable("planNo")
	String planNo) {
		setStCodes(m);
		m.addAttribute("operFlag","edit");
		m.addAttribute("readOnly","false");
//		setPlanNo(m, planNo);
		setTariffBoEntity(m,planNo);
		return VIEW_TARIFF_PLAN_FORM;
	}

	protected void setPlanNo(Model m, String planNo) {
		m.addAttribute("planNo", ESAPI.encoder().encodeForJavaScript(planNo));
	}
	/**
	 * 资费标准更新 保存
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping(value = "/saveTariffPlan/{planNo}", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveTariffPlan(Model m, @PathVariable("planNo")
	String planNo, @RequestBody Map<String, Object> dataParams) {
		TariffPlanBo bo = getBean(dataParams);
		bo.setPlanNo(planNo);
		tariffPlanService.update(bo);
		return WrappedResult.successWrapedResult(planNo);
	}
	/**
	 * 资费标准批量删除
	 * @param m
	 * @param planNo
	 * @return
	 */
	@RequestMapping("/delete")
	public @ItemResponseBody
	QueryResultObject delete(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		tariffPlanService.deletePlans(ids);
		return RestUtils.wrappQueryResult("Y");
	}
	/**
	 * 
	 * 方法说明：资费标准启用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:25:08
	 * History:  2016年4月11日 下午9:25:08   lipf   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/startTariff")
	public @ItemResponseBody
	QueryResultObject startTariff(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		tariffPlanService.startTariff(ids);
		return RestUtils.wrappQueryResult("Y");
	}
	/**
	 * 
	 * 方法说明：资费标准停用
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:25:34
	 * History:  2016年4月11日 下午9:25:34   lipf   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/stopTariff")
	public @ItemResponseBody
	QueryResultObject stopTariff(@IdRequestBody
	IDRequestObject idObject) {
		String[] ids = idObject.getIds();
		tariffPlanService.stopTariff(ids);
		return RestUtils.wrappQueryResult("Y");
	}
	/**
	 * 
	 * 方法说明：复制资费模版
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 下午9:43:37
	 * History:  2016年4月11日 下午9:43:37   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	@RequestMapping("/copyTariff")
	public @ItemResponseBody
	QueryResultObject copyTariff(@RequestBody TariffPlanBo bo) {
		tariffPlanService.copyTariff(bo);
		return RestUtils.wrappQueryResult("Y");
	}
	
	public List<Map<String, Object>> code2ItemList(List<CodeBO> codeList) {
		if (codeList != null && codeList.size() > 0) {
			List<Map<String, Object>> tempMapList = new ArrayList<Map<String, Object>>();
			for (CodeBO codeBO : codeList) {
				if (StringUtils.nullOrBlank(codeBO.getpCodeValue())) {
					Map<String, Object> tempMap = new HashMap<String, Object>();
					tempMap.put("name", codeBO.getCodeName());
					tempMap.put("value", codeBO.getCodeValue());
					tempMapList.add(tempMap);
				}
			}
			for (Map<String, Object> map : tempMapList) {
				List<Map<String, String>> tempList = new ArrayList<Map<String, String>>();
				for (CodeBO codeBO : codeList) {
					if (String.valueOf(codeBO.getpCodeValue()).equals(map.get("value"))) {
						Map<String, String> _tempMap = new HashMap<String, String>();
						_tempMap.put("name", codeBO.getCodeName());
						_tempMap.put("value", codeBO.getCodeValue());
						tempList.add(_tempMap);
					}
				}
				map.put("items", tempList);
			}
			return tempMapList;
		} else {
			return null;
		}
	}

	@Override
	protected TariffPlanBo initCondition() {
		return new TariffPlanBo();
	}
}
