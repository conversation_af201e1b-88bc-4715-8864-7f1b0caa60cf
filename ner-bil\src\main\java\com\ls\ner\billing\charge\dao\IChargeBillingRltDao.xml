<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChargeBillingRltDao">
	
	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChargeBillingRltBo">
		<result column="SYSTEM_ID" property="systemId" />
		<result column="APP_NO" property="appNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="BILL_CTL_MODE" property="billCtlMode" />
		<result column="CHARGE_MODE" property="chargeMode" />
		<result column="T_PQ" property="tPq" />
		<result column="T_AMT" property="tAmt" />
		<result column="ITEM_T_AMT" property="itemTAmt" />
		<result column="AMT" property="amt" />
		<result column="ATTACH_ITEM_NOS" property="attachItemNos" />
		<result column="ATTACH_ITEM_PRICES" property="attachItemPrices" />
		<result column="ATTACH_ITEM_UNITS" property="attachItemUnits" />
		<result column="ATTACH_ITEM_NUMS" property="attachItemNums" />
		<result column="ATTACH_ITEM_AMTS" property="attachItemAmts" />
		<result column="BGN_TIME" property="bgnTime" />
		<result column="END_TIME" property="endTime" />
		<result column="T_TIME" property="tTime" />
		<result column="APPLY_BGN_TIME" property="applyBgnTime" />
		<result column="APPLY_END_TIME" property="applyEndTime" />
		<result column="CHARGE_PRICE" property="chargePrice" />
		<result column="BGN_MR_NUM" property="bgnMrNum" />
		<result column="END_MR_NUM" property="endMrNum" />
		<result column="FREE_NUM" property="freeNum" />
		 <result column="ITEM_CHARGE_MODE" property="itemChargeMode" />
	 </resultMap>
	 
	 <sql id="chargeBillingRltItems">
	 	a.APP_NO,a.CHC_NO,a.BILL_CTL_MODE,a.CHARGE_MODE,a.T_PQ,a.T_AMT,a.ITEM_T_AMT,a.AMT,a.ATTACH_ITEM_NOS,a.ATTACH_ITEM_PRICES,a.ATTACH_ITEM_UNITS,
	 	a.ATTACH_ITEM_NUMS,a.ATTACH_ITEM_AMTS,a.BGN_TIME,a.END_TIME,a.T_TIME,a.APPLY_BGN_TIME,a.APPLY_END_TIME,a.CHARGE_PRICE,a.BGN_MR_NUM,a.FREE_NUM,a.END_MR_NUM,
		a.ITEM_CHARGE_MODE
	 </sql>
	 
	<!-- 查询充电订单计费结果E_CHARGE_BILLING_RLT-->
	<select id="queryChargeBillingRlt" parameterType="string" resultMap="BaseResultMap">
		select  
			<include refid="chargeBillingRltItems"></include>
		from E_CHARGE_BILLING_RLT a
		<where>
			a.APP_NO = #{appNo}
		</where>
	</select>
	
	<!-- 查询充电订单计费结果条数E_CHARGE_BILLING_RLT-->
	<select id="queryChargeBillingRltNum" parameterType="string" resultType="int">
		select  
			count(1)
		from E_CHARGE_BILLING_RLT a
		<where>
			a.APP_NO = #{appNo}
		</where>
	</select>

	<delete id="deleteChargeBillingRlt" parameterType="String">
    delete from E_CHARGE_BILLING_RLT
    where APP_NO =#{appNo}
  </delete>
	<!-- 新增充电订单计费结果  E_CHARGE_BILLING_RLT-->
	<insert id="insertChargeBillingRlt" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHARGE_BILLING_RLT
		<trim prefix="(" suffix=")">
			<if test="appNo !=null and appNo !=''">APP_NO,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE,</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE,</if>
			<if test="tPq !=null and tPq !=''">T_PQ,</if>
			<if test="tAmt !=null and tAmt !=''">T_AMT,</if>
			<if test="itemTAmt !=null and itemTAmt !=''">ITEM_T_AMT,</if>
			<if test="amt !=null and amt !=''">AMT,</if>
			<if test="attachItemNos !=null and attachItemNos !=''">ATTACH_ITEM_NOS,</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">ATTACH_ITEM_PRICES,</if>
			<if test="attachItemUnits !=null and attachItemUnits !=''">ATTACH_ITEM_UNITS,</if>
			<if test="attachItemNums !=null and attachItemNums !=''">ATTACH_ITEM_NUMS,</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">ATTACH_ITEM_AMTS,</if>
			<if test="bgnTime !=null and bgnTime !=''">BGN_TIME,</if>
			<if test="endTime !=null and endTime !=''">END_TIME,</if>
			<if test="tTime !=null and tTime !=''">T_TIME,</if>
			<if test="applyEndTime !=null and applyEndTime !=''">APPLY_END_TIME,</if>
			<if test="chargePrice !=null and chargePrice !=''">CHARGE_PRICE,</if>
			<if test="bgnMrNum !=null and bgnMrNum !=''">BGN_MR_NUM,</if>
			<if test="endMrNum !=null and endMrNum !=''">END_MR_NUM,</if>
			<if test="freeNum !=null and freeNum !=''">FREE_NUM,</if>
			<if test="itemChargeMode !=null and itemChargeMode !=''">ITEM_CHARGE_MODE,</if>
			APPLY_BGN_TIME,DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="appNo !=null and appNo !=''">#{appNo},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">#{chargeMode},</if>
			<if test="tPq !=null and tPq !=''">#{tPq},</if>
			<if test="tAmt !=null and tAmt !=''">#{tAmt},</if>
			<if test="itemTAmt !=null and itemTAmt !=''">#{itemTAmt},</if>
			<if test="amt !=null and amt !=''">#{amt},</if>
			<if test="attachItemNos !=null and attachItemNos !=''">#{attachItemNos},</if>
			<if test="attachItemPrices !=null and attachItemPrices !=''">#{attachItemPrices},</if>
			<if test="attachItemUnits !=null and attachItemUnits !=''">#{attachItemUnits},</if>
			<if test="attachItemNums !=null and attachItemNums !=''">#{attachItemNums},</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">#{attachItemAmts},</if>
			<if test="bgnTime !=null and bgnTime !=''">#{bgnTime},</if>
			<if test="endTime !=null and endTime !=''">#{endTime},</if>
			<if test="tTime !=null and tTime !=''">#{tTime},</if>
			<if test="applyEndTime !=null and applyEndTime !=''">#{applyEndTime},</if>
			<if test="chargePrice !=null and chargePrice !=''">#{chargePrice},</if>
			<if test="bgnMrNum !=null and bgnMrNum !=''">#{bgnMrNum},</if>
			<if test="endMrNum !=null and endMrNum !=''">#{endMrNum},</if>
			<if test="freeNum !=null and freeNum !=''">#{freeNum},</if>
			<if test="itemChargeMode !=null and itemChargeMode !=''">#{itemChargeMode},</if>
			now(),now(),'I'
	    </trim>
	</insert>
	
	<!-- 更新充电订单计费结果  E_CHARGE_BILLING_RLT -->
	<update id="updateChargeBillingRlt" parameterType="com.ls.ner.billing.charge.bo.ChargeBillingRltBo">
		update E_CHARGE_BILLING_RLT
		<set>
			<if test="chcNo !=null and chcNo !=''">CHC_NO =#{chcNo},</if>
			<if test="billCtlMode !=null and billCtlMode !=''">BILL_CTL_MODE =#{billCtlMode},</if>
			<if test="chargeMode !=null and chargeMode !=''">CHARGE_MODE =#{chargeMode},</if>
			<if test="tPq !=null and tPq !=''">T_PQ =#{tPq},</if>
			<if test="tAmt !=null and tAmt !=''">T_AMT =#{tAmt},</if>
			<if test="itemTAmt !=null and itemTAmt !=''">ITEM_T_AMT =#{itemTAmt},</if>
			<if test="amt !=null and amt !=''">AMT =#{amt},</if>
			<if test="attachItemNos !=null">ATTACH_ITEM_NOS =#{attachItemNos},</if>
			<if test="attachItemPrices !=null">ATTACH_ITEM_PRICES =#{attachItemPrices},</if>
			<if test="attachItemUnits !=null">ATTACH_ITEM_UNITS =#{attachItemUnits},</if>
			<if test="attachItemNums !=null and attachItemNums !=''">ATTACH_ITEM_NUMS =#{attachItemNums},</if>
			<if test="attachItemAmts !=null and attachItemAmts !=''">ATTACH_ITEM_AMTS =#{attachItemAmts},</if>
			<if test="bgnTime !=null and bgnTime !=''">BGN_TIME =#{bgnTime},</if>
			<if test="endTime !=null and endTime !=''">END_TIME =#{endTime},</if>
			<if test="tTime !=null and tTime !=''">T_TIME =#{tTime},</if>
			<if test="applyBgnTime !=null and applyBgnTime !=''">APPLY_BGN_TIME =#{applyBgnTime},</if>
			<if test="applyEndTime !=null and applyEndTime !=''">APPLY_END_TIME =now(),</if>
			<if test="chargePrice !=null and chargePrice !=''">CHARGE_PRICE =#{chargePrice},</if>
			<if test="bgnMrNum !=null and bgnMrNum !=''">BGN_MR_NUM =#{bgnMrNum},</if>
			<if test="endMrNum !=null and endMrNum !=''">END_MR_NUM =#{endMrNum},</if>
				DATA_OPER_TIME =now(),
				DATA_OPER_TYPE ='U'
		</set>
		<where>
			APP_NO =#{appNo}
		</where>
	</update>

   <select  id="qryOrderBillConfFlag" parameterType="java.util.Map" resultType="int">
		SELECT
			count(1)
		FROM
			e_charge_billing_rlt a,
			e_charge_billing_conf b
		WHERE
			a.CHC_NO = b.CHC_NO
		AND a.APP_NO = #{orderNo}
		AND b.CUST_ID IS NOT NULL
   </select>

	<update id="updateChargeBillingOrgCode" parameterType="java.util.Map">
		UPDATE e_charge_billing_conf SET
			ORG_CODE = #{orgCode}
		WHERE
			STATION_ID = #{stationId}
	</update>

	<select id="getChargePeriodsItemsPq" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
		a.APP_NO appNo,
		ifnull(SUM(IF((a.CHARGE_MODE = '0202' AND b.TIME_FLAG = '1' and b.ITEM_NO = '1000000000'),b.PQ,0)),0) poPq,
		ifnull(SUM(IF((a.CHARGE_MODE = '0202' AND b.TIME_FLAG = '2' and b.ITEM_NO = '1000000000'),b.PQ,0)),0) pePq,
		ifnull(SUM(IF((a.CHARGE_MODE = '0202' AND b.TIME_FLAG = '3' and b.ITEM_NO = '1000000000'),b.PQ,0)),0) vaPq,
		ifnull(SUM(IF((a.CHARGE_MODE = '0202' AND b.TIME_FLAG = '4' and b.ITEM_NO = '1000000000'),b.PQ,0)),0) avPq
		FROM e_charge_billing_rlt a
		left join e_charge_billing_rlt_periods b on b.APP_NO = a.APP_NO
		WHERE b.ITEM_NO = '1000000000'
		<if test="orderNoList != '' and  orderNoList.size() > 0 ">
			AND a.APP_NO in (
			<foreach collection="orderNoList" item="item" index="index" separator="," >
				#{item}
			</foreach>
			)
		</if>
		GROUP BY a.APP_NO;
	</select>
</mapper>