/**
 *
 * @(#) IChargeItemDao.java
 * @Package com.ls.ner.billing.item.dao
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.item.dao;

import java.util.List;

import com.ls.ner.billing.item.bo.ChargeItemBo;

/**
 * Description :
 * 
 * @author: lipf
 * 
 *          History: 2015年7月7日 下午7:28:23 lipf Created.
 * 
 */
public interface IChargeItemDao {
	/**
	 * 
	 * Method description : 费用项查询
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:29:16 History: 2015年7月7日
	 * 下午7:29:16 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	List<ChargeItemBo> getChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用项查询 记录数
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:29:34 History: 2015年7月7日
	 * 下午7:29:34 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	int getChargeItemCount(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用项 保存
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:29:49 History: 2015年7月7日
	 * 下午7:29:49 lipf Created.
	 * 
	 * @param bo
	 * 
	 */
	void saveChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用项 更新
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:29:56 History: 2015年7月7日
	 * 下午7:29:56 lipf Created.
	 * 
	 * @param bo
	 * 
	 */
	void updateChargeItem(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用项 删除
	 * 
	 * Author： lipf Create Date： 2015年7月7日 下午7:30:04 History: 2015年7月7日
	 * 下午7:30:04 lipf Created.
	 * 
	 * @param id
	 * 
	 */
	void deleteChargeItem(String id);

	/**
	 * 
	 * Method description : 费用明细查询
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午9:01:05 History: 2015年7月22日
	 * 下午9:01:05 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	List<ChargeItemBo> getItemDetail(ChargeItemBo bo);

	/**
	 * 
	 * Method description : 费用明细查询记录数
	 * 
	 * Author： lipf Create Date： 2015年7月22日 下午9:01:00 History: 2015年7月22日
	 * 下午9:01:00 lipf Created.
	 * 
	 * @param bo
	 * @return
	 * 
	 */
	int getItemDetailCount(ChargeItemBo bo);

}
