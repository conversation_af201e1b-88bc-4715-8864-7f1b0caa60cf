<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型租赁设置" />
<ls:body>
		<%
			Object flag = request.getAttribute("flag");
				String subBe = request.getAttribute("subBe") == null ? "" : request.getAttribute("subBe").toString();
		%>
		<ls:layout-use>
				<ls:layout-put into="container">
						<ls:form id="searchForm" name="searchForm">
								<table class="tab_VSplit" cellpadding="0" cellspacing="0">
										<tr>
												<td width="25%" style="vertical-align: top;">
														<table class="tab_search">
																<tr>
																		<td colspan="2">
																				<div id="companyTree" style="height:500px;overflow: auto;">
																						<ls:tree name="companyDataTree" url="~/billing/models/getModelRentalTree"></ls:tree>
																				</div>
																		</td>
																</tr>
														</table>
												</td>
												<td style="vertical-align: top">
														<%-- <ls:title text="查询条件"></ls:title>
														<table class="tab_search"> --%>
																<%
																	if (flag != null && !flag.equals("")) { //flag用于 标记
																						//定价新增选择车型不需要选择已经定价的车型 注释掉
																%>
																<ls:text name="billconfFlag" value="1" visible="false"></ls:text>
																<ls:text name="subBeFlag" value="1" visible="false"></ls:text>
																<%
																	}
																%>
																<ls:text name="subBe" value="<%=subBe%>" visible="false"></ls:text>
																<ls:text name="orgCode"  visible="false"></ls:text>
																<%-- <tr>
																		<td>
																				<ls:label text="车型名称" ref="modelName" />
																		</td>
																		<td>
																				<ls:text name="modelName" property="modelName"></ls:text>
																		</td>
																		<td>
																				<ls:label text="车辆级别" ref="autoLevel" />
																		</td>
																		<td>
																				<ls:select name="autoLevel">
																						<ls:options property="autoLevelList" scope="request" text="codeName" value="codeValue" />
																				</ls:select>
																		</td>
																		<td>
																				<ls:label text="品牌" ref="autoBrand" />
																		</td>
																		<td>
																				<ls:select name="autoBrand">
																						<ls:options property="autoBrandList" scope="request" text="codeName" value="codeValue" />
																				</ls:select>
																		</td>
																		<td>
																				<ls:button text="清空" onclick="doClear" />
																				<ls:button text="查询" onclick="query" />
																		</td>
																</tr>
														</table> --%>
														<ls:title text="车型租赁列表"></ls:title>
														<ls:grid url="" name="companyModelGrid" height="350px" showCheckBox="true" singleSelect="true">
																<ls:gridToolbar name="companyModelGridBar">
																		<%
																			if (flag != null && !flag.equals("")) { //flag用于 标记
																		%>
																		<ls:gridToolbarItem name="doSure" text="确认" imageKey="set" onclick="doSure"></ls:gridToolbarItem>
																		<ls:gridToolbarItem name="closeWin" text="返回" imageKey="back" onclick="closeWin"></ls:gridToolbarItem>
																		<%
																			} else {
																		%>
																		<ls:gridToolbarItem imageKey="add" onclick="doAdd" text="添加"></ls:gridToolbarItem>
																		<ls:gridToolbarItem imageKey="delete" onclick="doDelete" text="删除"></ls:gridToolbarItem>
																		<%
																			}
																		%>
																</ls:gridToolbar>
																<ls:column name="modelId" hidden="true"></ls:column>
																<ls:column name="orgCodeName" caption="管理单位" />
																<ls:column caption="车型名称" name="modelName" />
																<ls:column caption="品牌" name="brandName" />
																<%-- <ls:column caption="车辆级别" name="autoLevelName" /> --%>
																<ls:column caption="规格" name="spec" />
																<ls:column caption="租赁方式" name="subBeName" />
																<ls:column caption="是否定价" name="billconfFlagName" />
																<ls:pager pageSize="15,20" pageIndex="1"></ls:pager>
														</ls:grid>
												</td>
										</tr>
								</table>
						</ls:form>
				</ls:layout-put>
		</ls:layout-use>
		<ls:script>
	window.companyModelGrid = companyModelGrid;
	window.expandParentNode = expandParentNode;
	//companyDataTree.expandNode(companyDataTree.getRootNode());
	window.query = query;
	//query();
	function query(){
	  var node = companyDataTree.getSelectedNode();
    if(node!=null&&!LS.isEmpty(node.subBe)){
      var params = searchForm.getFormData();
      companyModelGrid.query('~/billing/models/getModelRentalList',params,function(){});
    } else {
      LS.message("info","请先选择一种租赁方式！");
      return;
    }
	}
	companyDataTree.onNodeClick = function(node){
    if(!node.hasChildren){
      subBe.setValue(node.subBe);
      orgCode.setValue(node.parentCode);
      window.currentSelectedNode = node;
    } else {
      return;
    }
    query();
  }
	function doClear(){
		searchForm.clear();
	}
	
	function doAdd(){
	  	var node = companyDataTree.getSelectedNode();
	    if(node!=null&&!LS.isEmpty(node.subBe)){
	      var param = {};
	      param.pageIndex="1";
	      param.pageSize="";
	      param.filter = "orgCode="+searchForm.getFormData().orgCode+"&subBe="+searchForm.getFormData().subBe;
      	  LS.ajax('~/billing/models/getModelRentalList?params='+encodeURIComponent(JSON.stringify(param)),{},function(data){
      	  	var ret = data.items;
      	  	var modelIds = [];
      	  	for(var i=0;i < ret.length ; i++){
      	  		modelIds.push(ret[i].modelId);
      	  	}
 	        LS.dialog("<%=astPath %>/ast/model/select/car/model?excludeModelId="+modelIds.join(","), "添加车型", 800, 400, true, null);
      	  });
	    } else {
	      LS.message("info","请先选择一种租赁方式！");
	      return;
	    }
	    return;
	}
	
	function doDelete(){
		var items = companyModelGrid.getCheckedItems();
    if(items.length == 0){
      LS.message('info','请选择一条记录');
      return;
    }
    for(var i=0;i < items.length;i++){
      if(items[i].billconfFlag=="1"){
        LS.message('info','该车型已经定价，不能删除');
        return;
      }
    }
    var autoModelNos=[];
    for(var i=0;i < items.length;i++){
       autoModelNos.push(items[i].prodNo);
    }
	  LS.confirm('确认删除吗?', function(data) {
        if (data) {
            var params = {};
            params.ids = autoModelNos;
            LS.ajax("~/billing/models/delComModelRental", params, function(e) {
              if (e.items[0]=="Y") {                  
                LS.message("info", "删除成功！");
                //query();
                expandParentNode();
              } else {
                LS.message("error", e.items[0]);
              }
            });
        }
    });
	}
	
	function expandParentNode(){
	   var node =  currentSelectedNode;
	   var rootNode = companyDataTree.getRootNode();
	   var nodeIds = [];
	   nodeIds.push(node.id);
	   while(node.id!=rootNode.id){
	     var node = companyDataTree.getNode(node.pId);
	     if(node.id==rootNode.id){
	       continue;
	     }
	     nodeIds.push(node.id);
	   }
	   companyDataTree.load(null,function(){
	      companyDataTree.expandNode(companyDataTree.getRootNode(),function(){
	       expandRecursion(nodeIds.length-1,nodeIds);
	      });
	   });
	}
	function expandRecursion(n, nodeIds) {
    if(n == 0) {
      companyDataTree.selectNode(companyDataTree.getNode(nodeIds[n]));
      return;
    } else {
        companyDataTree.expandNode(companyDataTree.getNode(nodeIds[n]), function() {
          expandRecursion(n - 1, nodeIds);
        });
    }
  }
	function doSure(){      
    var items = companyModelGrid.getCheckedItems();
    if(items.length==0){
      LS.message("info","请选择一条记录!");
      return;
    }else if(items.length > 1){
      LS.message("info","只能选择一条记录!");
      return;
    }else{
      LS.parent().setModelRental(items[0]);
      LS.window.close();
    }
  }    
  
  function closeWin(){
    LS.window.close();
  }
  function getOrg(){
    var initValue=null;
    if(!LS.isEmpty(orgCode.getValue())){
      initValue=orgCode.getValue().split(",");
    }
    js_util.selectOrgTree(false, null, true, initValue, false, setOrg);
  }
  function setOrg(node){
    if(node==null){
      return;
    }
    var orgCodes="";
    var orgCodeNames="";
    if(node.length==undefined){
      orgCodes=node.id;
      orgCodeNames=node.text;
    }else{
      for(var i=0;i< node.length;i++){
        if(node.length==i+1){
          orgCodes+=node[i].id;
          orgCodeNames+=node[i].text;
        }else{
          orgCodes+=node[i].id+',';
          orgCodeNames+=node[i].text+',';
        }
      }
    }
    orgCode.setValue(orgCodes);
    orgCodeName.setValue(orgCodeNames);
  }
  function changeOrgName(){
    if(LS.isEmpty(orgCodeName.getValue())){
       orgCode.setValue();
    }
  }
  //高度适应
  document.getElementById("companyTree").style.height=(getHeights())+"px";
  function getHeights(tag) {
    var retVal=0;
    var myWidth = 0, myHeight = 0;
    if( typeof( window.innerWidth ) == 'number' ) {
      //Non-IE
      myWidth = window.innerWidth;
      myHeight = window.innerHeight;
    } else if( document.documentElement && ( document.documentElement.clientWidth || document.documentElement.clientHeight ) ) {
      //IE 6+ in 'standards compliant mode'
      myWidth = document.documentElement.clientWidth;
      myHeight = document.documentElement.clientHeight;
      if(navigator.appVersion.match(/6./i)=="6."){
          myHeight=myHeight;
       }else if(navigator.appVersion.match(/7./i)=="7."){
         myHeight=myHeight;
       }
    } else if( document.body && ( document.body.clientWidth || document.body.clientHeight ) ) {
      //IE 4 compatible
      //alert('IE 4');
      myWidth = document.body.clientWidth;
      myHeight = document.body.clientHeight;
    }
    if(tag==1){
      retVal=myWidth;
    }else{
      retVal= myHeight-20;
    }
      return retVal;
    }
    function resizewindow() {
     var y=getHeights();
     var heightaa=document.getElementById("companyTree").style.height;
     var ysize=y+"px";
     if(heightaa!=ysize){
       document.getElementById("companyTree").style.height = ysize;
      }
    }  
    setInterval(resizewindow,50);
    
    window.setModel = setModel;
    function setModel(data){
    	var params = {};
	    params.modelId = data.join(",");
	    params.orgCode = orgCode.getValue();
	    params.subBe = subBe.getValue();
	    LS.ajax("~/billing/models/saveAutoModelRental", params, function(e) {
	      if (e.items[0]=="Y") {                  
	        LS.message("info", "添加成功！");
	        expandParentNode();
	       // LS.parent().query();
	       // LS.window.close(); 
	      } else {
	        LS.message("error", e.items[0]);
	      }
	    });
    }
  </ls:script>
</ls:body>
</html>