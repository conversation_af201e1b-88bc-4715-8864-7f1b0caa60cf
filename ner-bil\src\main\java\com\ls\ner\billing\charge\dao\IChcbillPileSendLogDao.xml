<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChcbillPileSendLogDao">

	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo">
		<result column="PILE_NO" property="pileNo" />
		<result column="CHC_NO" property="chcNo" />
		<result column="PILE_SEND_ID" property="pileSendId" />
		<result column="SEND_TIME" property="sendTime" />
		<result column="FAIL_REASON" property="failReason" />
		<result column="PILE_SEND_STATUS" property="pileSendStatus" />
		<result column="PILE_NAME" property="pileName" />
		 <result column="OPERATOR" property="operator" />
		 <result column="INTRANET_IP" property="intranetIp" />
		 <result column="NETWORK_IP" property="networkIp" />
	 </resultMap>
	 
	 <sql id="chcbillPileSendLogItem">
		a.PILE_NO,a.CHC_NO,a.PILE_SEND_ID,a.SEND_TIME,a.FAIL_REASON,a.PILE_SEND_STATUS,a.OPERATOR,a.INTRANET_IP,a.NETWORK_IP
	</sql>
	<sql id="queryChcbillPileSendLogWhere">
		<if test="chcNo !=null and chcNo !=''">
		    and a.CHC_NO =#{chcNo}
		</if>
		<if test="pileNo !=null and pileNo !=''">
		    and a.PILE_NO =#{pileNo}
		</if>
	</sql> 
	
	<!-- 查询桩电价下发日志  E_CHCBILL_PILESEND_LOG-->
	<select id="queryChcbillPileSendLogs" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultMap="BaseResultMap">
		select  
			<include refid="chcbillPileSendLogItem"></include>
		from E_CHCBILL_PILESEND_LOG a
		<where>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
		order by a.SEND_TIME desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	
	<!-- 查询桩电价下发日志条数  E_CHCBILL_PILESEND_LOG-->
	<select id="queryChcbillPileSendLogsNum" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="int">
		select  
			count(1)
		from E_CHCBILL_PILESEND_LOG a
		<where>
			<include refid="queryChcbillPileSendLogWhere"></include>
		</where>
	</select>
	
	<!-- 新增桩电价下发日志   E_CHCBILL_PILESEND_LOG -->
	<insert id="insertChcbillPileSendLog" parameterType="com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHCBILL_PILESEND_LOG
		<trim prefix="(" suffix=")">
			<if test="pileSendId !=null and pileSendId !=''">PILE_SEND_ID,</if>
			<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="pileNo !=null and pileNo !=''">PILE_NO,</if>
			<if test="pileId !=null and pileId !=''">PILE_ID,</if>
			<if test="sendTime !=null and sendTime !=''">SEND_TIME,</if>
			<if test="failReason !=null and failReason !=''">FAIL_REASON,</if>
			<if test="pileSendStatus !=null and pileSendStatus !=''">PILE_SEND_STATUS,</if>
			<if test="operator != null and operator != ''">OPERATOR,</if>
			<if test="intranetIp != null and intranetIp != ''" >INTRANET_IP,</if>
			<if test="networkIp != null and networkIp != ''">NETWORK_IP,</if>
	      	DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="pileSendId !=null and pileSendId !=''">#{pileSendId},</if>
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="pileNo !=null and pileNo !=''">#{pileNo},</if>
			<if test="pileId !=null and pileId !=''">#{pileId},</if>
			<if test="sendTime !=null and sendTime !=''">now(),</if>
			<if test="failReason !=null and failReason !=''">#{failReason},</if>
			<if test="pileSendStatus !=null and pileSendStatus !=''">#{pileSendStatus},</if>
			<if test="operator != null and operator != ''">#{operator},</if>
			<if test="intranetIp != null and intranetIp != ''" >#{intranetIp},</if>
			<if test="networkIp != null and networkIp != ''">#{networkIp},</if>
	      	now(),'I'
	    </trim>
	</insert>
	
	<!-- 删除桩电价下发日志   E_CHCBILL_PILESEND_LOG -->
	<delete id="deleteChcbillPileSendLog" parameterType="string">
		delete from E_CHCBILL_PILESEND_LOG where CHC_NO = #{chcNo}
	</delete>
	
	<!-- 更新下发状态 -->
	<update id="updateChcbillPileSendLogStatus" parameterType="com.ls.ner.billing.charge.bo.ChcbillPileSendLogBo">
		update E_CHCBILL_PILESEND_LOG 
		<set>
			<if test="pileSendStatus !=null and pileSendStatus !=''">PILE_SEND_STATUS = #{pileSendStatus},</if>
			<if test="failReason !=null and failReason !=''">FAIL_REASON = #{failReason},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			PILE_SEND_ID = (
				select PILE_SEND_ID from E_CHCBILL_PILESEND where CHC_NO = #{chcNo} and PILE_NO = #{pileNo}
			)
		</where>
	</update>
</mapper>