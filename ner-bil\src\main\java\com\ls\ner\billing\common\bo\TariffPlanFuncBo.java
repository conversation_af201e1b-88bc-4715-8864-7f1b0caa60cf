package com.ls.ner.billing.common.bo;

import java.util.Date;

public class TariffPlanFuncBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan_func.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan_func.E_TARIFF_PLAN_NO
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    private String eTariffPlanNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan_func.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    private String dataOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan_func.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_tariff_plan_func.CONTENT
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan_func.SYSTEM_ID
     *
     * @return the value of e_tariff_plan_func.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan_func.SYSTEM_ID
     *
     * @param systemId the value for e_tariff_plan_func.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan_func.E_TARIFF_PLAN_NO
     *
     * @return the value of e_tariff_plan_func.E_TARIFF_PLAN_NO
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public String geteTariffPlanNo() {
        return eTariffPlanNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan_func.E_TARIFF_PLAN_NO
     *
     * @param eTariffPlanNo the value for e_tariff_plan_func.E_TARIFF_PLAN_NO
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public void seteTariffPlanNo(String eTariffPlanNo) {
        this.eTariffPlanNo = eTariffPlanNo == null ? null : eTariffPlanNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan_func.DATA_OPER_TYPE
     *
     * @return the value of e_tariff_plan_func.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan_func.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_tariff_plan_func.DATA_OPER_TYPE
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan_func.DATA_OPER_TIME
     *
     * @return the value of e_tariff_plan_func.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan_func.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_tariff_plan_func.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_tariff_plan_func.CONTENT
     *
     * @return the value of e_tariff_plan_func.CONTENT
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_tariff_plan_func.CONTENT
     *
     * @param content the value for e_tariff_plan_func.CONTENT
     *
     * @mbggenerated Wed Mar 09 16:40:02 CST 2016
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}