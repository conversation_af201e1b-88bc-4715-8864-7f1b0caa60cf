package com.ls.ner.billing.charge.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;

/**
 * 充电计费数据适配器--只做数据转换
 * <AUTHOR>
 */
@Component
public class ChargeBillingRltAdapter{
	private static final Logger LOGGER = LoggerFactory.getLogger(ChargeBillingRltAdapter.class);

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ISysParamService sysParamService;
	
	/**
	 * @descrption 从将3.3.1.2.4	刷卡结算new 传入的dataMap 获取本地计费模型cbrBo需要的数据
	 * @param dataMap(
	 * 		当  DATA_TYPE = 1，boss计费，推送示数
	 * 			传入：DATA_TIME 数据时间 、END_TIME 充电结束时间、BGN_TIME 充电开始时间、T_TIME 充电时间 、MR_NUM 示数、TIME_SHAR_FLAG分时标志   
	 * 			当 chargeMode = 01标准，
	 * 				传入：无
	 * 			当 chargeMode = 02分时，
	 * 				传入：无
	 * 		当 DATA_TYPE = 2，boss计费，推送电量
	 * 			传入：DATA_TIME 数据时间 、END_TIME 充电结束时间、BGN_TIME 充电开始时间、T_TIME 充电时间 、T_PQ 总电量、TIME_SHAR_FLAG分时标志
	 * 			当 chargeMode = 01标准，
	 * 				传入：无
	 * 			当 chargeMode = 02分时，
	 * 				传入：PERIODSLIST 分时明细
	 * 		当DATA_TYPE = 3，桩计费，
	 * 			传入：DATA_TIME 数据时间 、END_TIME 充电结束时间、BGN_TIME 充电开始时间、T_TIME 充电时间 、T_PQ 总电量、、TIME_SHAR_FLAG分时标志、T_AMT充电总金额、ITEM_T_AMT服务费用项总金额、AMT总金额、ATTACHLIST服务项记录
	 * 			当 chargeMode = 01标准，
	 * 				传入：无
	 * 			当 chargeMode = 02分时，
	 * 				传入：PERIODSLIST 分时明细
	 * )
	 * */
	public void adapter(Map<String,Object> dataMap,ChargeBillingRltBo cbrBo){
		//如果结算或实时计费过程中有传入充电开始时间，则使用该时间
		String bgnTime = StringUtils.isEmpty(dataMap.get("BGN_TIME"))?cbrBo.getBgnTime():String.valueOf(dataMap.get("BGN_TIME"));//用于计算充电时间的开始时间
		String endTime = StringUtils.isEmpty(dataMap.get("END_TIME"))?String.valueOf(dataMap.get("DATA_TIME")):String.valueOf(dataMap.get("END_TIME"));//用于计算充电时间的结束时间
		//停车场计费（协鑫演示）备注：后期要删除该条件
		String parkingAmt= sysParamService==null?null:sysParamService.getSysParamsValues("parkingAmt");
		if(StringUtils.isEmpty(parkingAmt) || "0".equals(parkingAmt))
			cbrBo.settTime(StringUtils.isEmpty(dataMap.get("T_TIME"))?startToEndTime(bgnTime, endTime):String.valueOf(dataMap.get("T_TIME")));//充电时间，单位：分钟
		else
			cbrBo.settTime(startToEndTime(bgnTime, endTime));//充电时间，单位：分钟
		cbrBo.setEndTime(startTimeAddLong(cbrBo.getBgnTime(), Long.parseLong(cbrBo.gettTime())));//充电结束时间
		cbrBo.setApplyEndTime("true");//请求结束时间
		String chargeMode = transferTimeSharFlag(dataMap.get("TIME_SHAR_FLAG"));//计费方式
		if(ChargeConstant.dateType.BOSS_ONE.equals(dataMap.get("DATA_TYPE"))){//boss计费，推送数据为示数
			cbrBo.setEndMrNum(isNotEmpty(dataMap.get("MR_NUM"), "缺少充电结束示数"));//充电结束示数
			BigDecimal bgnMrNum = new BigDecimal(cbrBo.getBgnMrNum());
			BigDecimal endMrNum = new BigDecimal(cbrBo.getEndMrNum());
			cbrBo.settPq(endMrNum.subtract(bgnMrNum).toString());//总电量 = 结束示数 - 开始示数
			
			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
				
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
				refreshPeriods(cbrBo);
			}else
				throw new ChargeBillingRltException("请查看充电参数配置信息，缺少了计费方式");
		}else if(ChargeConstant.dateType.BOSS_TWO.equals(dataMap.get("DATA_TYPE"))){//boss计费，推送电量
			cbrBo.settPq(isNotEmpty(dataMap.get("T_PQ"), "缺少充电总电量"));

			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(chargeMode)){//dataMap传入的是标准计费
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
					
				}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
					cbrBo.setChargeMode(chargeMode);
					List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();
					if(rltPeriods == null || rltPeriods.size() == 0)
						throw new ChargeBillingRltException("充电计费配置分时计费方式，没有分时记录");
					for(ChargeBillingRltPeriodsBo rltPeriod:rltPeriods){
						if(compareTime(toTime(cbrBo.getBgnTime()), rltPeriod.getBeginTime(), rltPeriod.getEndTime())){
							cbrBo.setChargePrice(rltPeriod.getPrice());
							break;
						}
					}
					cbrBo.setRltPeriods(null);
				}
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(chargeMode)){//dataMap传入的是分时计费
				List<ChargeBillingRltPeriodsBo> periods = rltPeriods((List<Map<String,Object>>)dataMap.get("PERIODSLIST"));
				chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
					cbrBo.setChargeMode(chargeMode);
					for(ChargeBillingRltPeriodsBo period:periods){
						period.setPrice(cbrBo.getChargePrice());
					}
					cbrBo.setChargePrice(null);
					cbrBo.setRltPeriods(periods);
				}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
					cbrBo.setRltPeriods(priceForRltPeriods(periods, cbrBo.getRltPeriods()));
				}
			}
			
		}else if(ChargeConstant.dateType.REMOTE_ONE.equals(dataMap.get("DATA_TYPE"))){//桩计费
			cbrBo.settPq(isNotEmpty(dataMap.get("T_PQ"), "缺少充电总电量"));
			cbrBo.settAmt(isNotEmpty(dataMap.get("T_AMT"), "缺少充电总金额"));
			cbrBo.setItemTAmt(StringUtils.isEmpty(dataMap.get("ITEM_T_AMT"))?"0.00":String.valueOf(dataMap.get("ITEM_T_AMT")));
			cbrBo.setAmt(isNotEmpty(dataMap.get("AMT"), "缺少总金额"));
			serItemAdapter((List<Map<String,Object>>)dataMap.get("ATTACHLIST"), cbrBo);//服务项记录
			cbrBo.setChargeMode(chargeMode);//计费方式
			
			if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
				
			}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
				List<ChargeBillingRltPeriodsBo> periods = rltPeriods((List<Map<String,Object>>)dataMap.get("PERIODSLIST"));
				chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());
				cbrBo.setRltPeriods(periods);//分时记录
			}else
				throw new ChargeBillingRltException("传入的分时标志有误");
		}else
			throw new ChargeBillingRltException("没有该计费方式");
		
	}

	/**
	 * @description 时间跨度
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 返回间隔分钟数
	 * */
	private String startToEndTime(String startTime,String EndTime){
		SimpleDateFormat formatter = new SimpleDateFormat(EndTime!=null&&EndTime.length()==16?"yyyy-MM-dd HH:mm":"yyyy-MM-dd HH:mm:ss");
		long i = 0;
		try {
			Date currentTime = formatter.parse(startTime);
			Date currentTime2 = formatter.parse(EndTime);
			i = (currentTime2.getTime() - currentTime.getTime())/(1000*60);
		} catch (ParseException e) {
			e.printStackTrace();
			LOGGER.error("请检查充电开始时间、充电结束时间，可能格式不正确或没有输入");
			return "0";
		}
		if(i < 0)
			throw new ChargeBillingRltException("充电时间出现负数，请排查是否充电时间或者开始时间与结束时间输入有误");
		return String.valueOf(i);
	}

	/**
	 * @description 时间跨度
	 * @param startTime 开始时间
	 * @param time 分钟数
	 * @return endTime 结束时间
	 * */
	private String startTimeAddLong(String startTime,long time){
		SimpleDateFormat formatter = new SimpleDateFormat(startTime!=null&&startTime.length()==16?"yyyy-MM-dd HH:mm":"yyyy-MM-dd HH:mm:ss");
		String returnDate = "";
		try {
			Date currentTime = formatter.parse(startTime);
			currentTime.setTime(currentTime.getTime()+(time*1000*60));
			returnDate = formatter.format(currentTime);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return returnDate;
	}
	
	/**
	 * @description 将传入的TIME_SHAR_FLAG分时标志转为计费方式       1分时、0不分时；如为空值，默认为0
	 * */
	private String transferTimeSharFlag(Object timeSharFlag){
		String chargeMode = "";
		if(StringUtils.isEmpty(timeSharFlag) || "0".equals(timeSharFlag))//不分时
			chargeMode = ChargeConstant.chcBillingChargeMode.STANDARD;
		else
			chargeMode = ChargeConstant.chcBillingChargeMode.TIME_SHARE;
		return chargeMode;
	}
	
	/**
	 * @description boss计费，传递示数，分时计费
	 * */
	private void refreshPeriods(ChargeBillingRltBo cbrBo){
		List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();
		List<ChargeBillingRltPeriodsBo> newPeriods = cbrBo.getNewPeriods();
		if(rltPeriods == null || rltPeriods.size() == 0){
			throw new ChargeBillingRltException("分时的时候，没有分时记录");
		}
		String endTime =toTime(cbrBo.getEndTime());
		//1、如果是第一次实时计费，根据结束时间判断落在哪个时段区间内
		if(newPeriods == null || newPeriods.size() == 0){
			newPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
			for(ChargeBillingRltPeriodsBo period:rltPeriods){
				if(endTime.compareTo(period.getBeginTime())>=0 && endTime.compareTo(period.getEndTime())<=0){
					period.setSn("1");
					period.setPq(cbrBo.gettPq());
					newPeriods.add(period);
					break;
				}
			}
		}else{
		//2、如果不是第一次实时计费，先根据结束时间判断是否落在已存的时段区间内
			BigDecimal pq = obtainPqAmt(newPeriods);//已录入的分时总电量
			BigDecimal newPq = new BigDecimal(cbrBo.gettPq());//新传入的电量
			BigDecimal subPd = subtract(newPq, pq);//新增长的电量
			ChargeBillingRltPeriodsBo period = newPeriods.get(newPeriods.size()-1);
			//如果落在已存在的时段区间内，则累加原来该区间的电量
			if(endTime.compareTo(period.getBeginTime())>=0 && endTime.compareTo(period.getEndTime())<=0){
				period.setPq(BigDecimalToString(addition(strToBigDecimal(period.getPq(), ""), subPd)));
			}else{
			//如果没有落在已存在的时段区间内，则新增区间记录
				for(ChargeBillingRltPeriodsBo cbrPeriodsBo:rltPeriods){
					if(endTime.compareTo(cbrPeriodsBo.getBeginTime())>=0 && endTime.compareTo(cbrPeriodsBo.getEndTime())<=0){
						cbrPeriodsBo.setSn(String.valueOf(newPeriods.size()+1));
						cbrPeriodsBo.setPq(BigDecimalToString(subPd));
						newPeriods.add(cbrPeriodsBo);
						break;
					}
				}
			}
		}
		cbrBo.setRltPeriods(newPeriods);
		
	}

	/**
	 * @description 根据分时记录信息List<ChargeBillingRltPeriodsBo>，获取总电量
	 * */
	private BigDecimal obtainPqAmt(List<ChargeBillingRltPeriodsBo> newPeriods){
		BigDecimal returnDecimal = new BigDecimal("0.00");
		if(newPeriods != null && newPeriods.size() > 0)
			for(ChargeBillingRltPeriodsBo periods:newPeriods){
				BigDecimal pq = new BigDecimal(periods.getPq());
				returnDecimal = addition(returnDecimal, pq);
			}
		return returnDecimal;
	}
	
	/**
	 * @description 分时的时候，将传入的List<Map>记录转为List<ChargeBillingRltPeriodsBo>，
	 * */
	private List<ChargeBillingRltPeriodsBo> rltPeriods(List<Map<String,Object>> rltPeriodList){
		if(rltPeriodList == null || rltPeriodList.size() == 0){
			throw new ChargeBillingRltException("分时的时候，没有分时记录");
		}
		List<ChargeBillingRltPeriodsBo> rltPeriods = new ArrayList<ChargeBillingRltPeriodsBo>();
		int i =1;
		for(Map<String,Object> rltMap : rltPeriodList){
			ChargeBillingRltPeriodsBo period =  new ChargeBillingRltPeriodsBo();
			period.setSn(StringUtils.isEmpty(rltMap.get("sn"))?String.valueOf(i):String.valueOf(rltMap.get("sn")));
			period.setBeginTime(isNotEmpty(rltMap.get("BEGIN_TIME"),"分时的起始时间不能为空"));
			period.setEndTime(isNotEmpty(rltMap.get("END_TIME"),"分时的结束时间不能为空"));
			period.setPrice(StringUtils.isEmpty(rltMap.get("periodRate"))?null:String.valueOf(rltMap.get("periodRate")));
			period.setPq(StringUtils.isEmpty(rltMap.get("PQ"))?null:String.valueOf(rltMap.get("PQ")));
			period.setAmt(StringUtils.isEmpty(rltMap.get("AMT"))?null:String.valueOf(rltMap.get("AMT")));
			rltPeriods.add(period);
			i++;
		}
		return rltPeriods;
	}
	
	/**
	 * @description 根据传入的分时信息，回填时段单价
	 * @param newRltPeriods 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	private List<ChargeBillingRltPeriodsBo> priceForRltPeriods(List<ChargeBillingRltPeriodsBo> newRltPeriods,List<ChargeBillingRltPeriodsBo> oldRltPeriods){
		for(ChargeBillingRltPeriodsBo newPeriod:newRltPeriods){
			priceForRltPeriod(newPeriod, oldRltPeriods);
		}
		return newRltPeriods;
	}

	/**
	 * @description 根据传入的分时信息，回填时段单价
	 * @param newRltPeriod 接口传入的分时记录信息
	 * @param oldRltPeriods 本地的分时时段记录信息
	 * */
	private void priceForRltPeriod(ChargeBillingRltPeriodsBo newRltPeriod,List<ChargeBillingRltPeriodsBo> oldRltPeriods){
		for(ChargeBillingRltPeriodsBo oldRltPeriod:oldRltPeriods){
			if(compareTime(newRltPeriod.getBeginTime(), oldRltPeriod.getBeginTime(), oldRltPeriod.getEndTime())){
				newRltPeriod.setPrice(oldRltPeriod.getPrice());
				return;
			}
		}
		throw new ChargeBillingRltException("接口传入的分时记录有错误，无法与本地分时时段对应");
	}
	
	/**
	 * @description 根据传入的分时信息，回填订单编号appNo,充电计费编号chcNo
	 * */
	private void chcNoForRltPeriods(List<ChargeBillingRltPeriodsBo> periods,String appNo,String chcNo){
		if(periods != null)
			for(ChargeBillingRltPeriodsBo p:periods){
				p.setAppNo(appNo);
				p.setChcNo(chcNo);
			}
	}
	/**
	 * @description 桩计费时，组装服务费用项
	 * */
	private void serItemAdapter(List<Map<String,Object>> serItems,ChargeBillingRltBo cbrBo){
		if(serItems == null || serItems.size() == 0){
			LOGGER.debug("桩计费时，没有传入服务费信息");
			return;
		}
		StringBuffer attachItemNos = new StringBuffer();//服务项目编号，逗号隔开
		StringBuffer attachItemPrices = new StringBuffer();//服务项目单价，逗号隔开
		StringBuffer attachItemUnits = new StringBuffer();//服务项目单位，逗号隔开
		StringBuffer attachItemNums = new StringBuffer();//服务项目数量，逗号隔开
		StringBuffer attachItemAmts = new StringBuffer();//服务项目金额，逗号隔开
		for(int i=0;i< serItems.size();i++){
			attachItemNos.append(serItems.get(i).get("ITEM_NO"));
			attachItemPrices.append(serItems.get(i).get("ITEM_PRICE"));
			attachItemUnits.append(serItems.get(i).get("ITEM_UNIT"));
			attachItemNums.append(serItems.get(i).get("ITEM_NUM"));
			attachItemAmts.append(serItems.get(i).get("ITEM_AMT"));
			if(i != serItems.size() -1){
				attachItemNos.append(",");
				attachItemPrices.append(",");
				attachItemUnits.append(",");
				attachItemNums.append(",");
				attachItemAmts.append(",");
			}
		}
		cbrBo.setAttachItemNos(attachItemNos.toString());
		cbrBo.setAttachItemPrices(attachItemPrices.toString());
		cbrBo.setAttachItemUnits(attachItemUnits.toString());
		cbrBo.setAttachItemNums(attachItemNums.toString());
		cbrBo.setAttachItemAmts(attachItemAmts.toString());
	}
	
	/**
	 * 时间格式00:00的比较   ： maxBgnStr <= minBgnStr <= minEndStr < = maxEndStr
	 * */
	private boolean compareTime(String minBgnStr,String maxBgnStr,String maxEndStr){
		return (minBgnStr.compareTo(maxBgnStr)>=0 && maxEndStr.compareTo(minBgnStr)>0);
	}
	
	/**
	 * 验证不为空
	 * */
	private String isNotEmpty(Object str,String message){
		if(StringUtils.isEmpty(str))
			throw new ChargeBillingRltException(message);
		return String.valueOf(str);
	}

	/**
	 * 将时间格式yyyy-MM-dd HH:mm:ss转化为HH:mm
	 * */
	private String toTime(String str){
		String[] time = str.split(" ")[1].split(":");
		return time[0]+":"+time[1];
	}
	
	/**
	 * String类型转为BigDecimal
	 * */
	private BigDecimal strToBigDecimal(String str,String message) {
	    try{
	    	return new BigDecimal(str);
	    }catch(Exception e){
	    	throw new ChargeBillingRltException(message);
	    }
	 }
	
	/**
	 *BigDecimal转为 String
	 * */
	private String BigDecimalToString(BigDecimal preStr){
		return preStr.toString();
	}
	
	/**
	 * 加法计算
	 * */
	private BigDecimal addition(BigDecimal preStr,BigDecimal nextStr){
		return preStr.add(nextStr);
	}

	/**
	 * 减法计算
	 * */
	private BigDecimal subtract(BigDecimal preStr,BigDecimal nextStr){
		return preStr.subtract(nextStr);
	}
	
}
