<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.gift.dao.ICarGiftDao">

    <resultMap id="BaseResultMap" type="com.ls.ner.billing.gift.bo.CarGiftBo">
        <result column="gift_id" jdbcType="VARCHAR" property="giftId" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
        <result column="alipay_no" jdbcType="VARCHAR" property="alipayNo"/>
        <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
        <result column="car_type" jdbcType="VARCHAR" property="carType" />
        <result column="vin" jdbcType="VARCHAR" property="vin" />
        <result column="gift_amt" jdbcType="VARCHAR" property="giftAmt" />
        <result column="gift_status" jdbcType="VARCHAR" property="giftStatus" />
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
        <result column="apply_time" jdbcType="VARCHAR" property="applyTime" />
        <result column="audit_time" jdbcType="VARCHAR" property="auditTime" />
        <result column="get_time" jdbcType="VARCHAR" property="getTime" />
        <result column="buy_time" jdbcType="VARCHAR" property="buyTime" />
        <result column="buy_addr" jdbcType="VARCHAR" property="buyAddr" />
        <result column="buy_model" jdbcType="VARCHAR" property="buyModel" />
        <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    </resultMap>

    <sql id="Base_Column_List">
        gift_id, mobile, cert_no, alipay_no, license_no, car_type, vin,
        gift_amt, gift_status, fail_reason, DATE_FORMAT(apply_time,"%Y-%m-%d %H:%i:%s") apply_time,
        DATE_FORMAT(audit_time,"%Y-%m-%d %H:%i:%s") audit_time, DATE_FORMAT(get_time,"%Y-%m-%d %H:%i:%s") get_time,
        DATE_FORMAT(buy_time,"%Y-%m-%d %H:%i:%s") buy_time,buy_addr,buy_model,buyer_name
    </sql>


    <select id="listCarGift" parameterType="com.ls.ner.billing.gift.condition.CarGiftCondition" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List" />
        from t_car_gift
        <where>
            <if test="licenseNo !=null and licenseNo !=''">
                and license_no = #{licenseNo}
            </if>
            <if test="vin !=null and vin !=''">
                and vin = #{vin}
            </if>
            <if test="certNo !=null and certNo !=''">
                and cert_no = #{certNo}
            </if>
            <if test="applyTimeBgn !=null and applyTimeBgn !=''">
                AND DATE_FORMAT(apply_time,"%Y-%m-%d %H:%i:%s") >= #{applyTimeBgn}
            </if>
            <if test="applyTimeEnd !=null and applyTimeEnd  !=''">
                AND <![CDATA[ DATE_FORMAT(apply_time,"%Y-%m-%d %H:%i:%s") <= #{applyTimeEnd}]]>
            </if>
            <if test="auditTimeBegin !=null and auditTimeBegin !=''">
                AND DATE_FORMAT(audit_time,"%Y-%m-%d %H:%i:%s") >= #{auditTimeBegin}
            </if>
            <if test="auditTimeEnd !=null and auditTimeEnd  !=''">
                AND <![CDATA[ DATE_FORMAT(audit_time,"%Y-%m-%d %H:%i:%s") <= #{auditTimeEnd}]]>
            </if>
            <if test="giftStatus !=null and giftStatus  !=''">
                AND gift_status = #{giftStatus}
            </if>
            <if test="carType !=null and carType  !=''">
                AND car_type = #{carType}
            </if>
            <if test="giftId !=null and giftId  !=''">
                AND gift_id = #{giftId}
            </if>
        </where>
        order by apply_time desc
        <if test="end!=null and end!= 0">
            limit #{begin},#{end}
        </if>
    </select>

    <select id="countCarGift" parameterType="com.ls.ner.billing.gift.condition.CarGiftCondition" resultType="int">
        SELECT
          count(1)
        FROM
          t_car_gift
        <where>
            <if test="licenseNo !=null and licenseNo !=''">
                and license_no = #{licenseNo}
            </if>
            <if test="vin !=null and vin !=''">
                and vin = #{vin}
            </if>
            <if test="certNo !=null and certNo !=''">
                and cert_no = #{certNo}
            </if>
            <if test="applyTimeBgn !=null and applyTimeBgn !=''">
                AND DATE_FORMAT(apply_time,"%Y-%m-%d %H:%i:%s") >= #{applyTimeBgn}
            </if>
            <if test="applyTimeEnd !=null and applyTimeEnd !=''">
                AND <![CDATA[ DATE_FORMAT(apply_time,"%Y-%m-%d %H:%i:%s") <= #{applyTimeEnd}]]>
            </if>
            <if test="auditTimeBegin !=null and auditTimeBegin !=''">
                AND DATE_FORMAT(audit_time,"%Y-%m-%d %H:%i:%s") >= #{auditTimeBegin}
            </if>
            <if test="auditTimeEnd !=null and auditTimeEnd !=''">
                AND <![CDATA[ DATE_FORMAT(audit_time,"%Y-%m-%d %H:%i:%s") <= #{auditTimeEnd}]]>
            </if>
            <if test="giftStatus !=null and giftStatus !=''">
                AND gift_status = #{giftStatus}
            </if>
            <if test="carType !=null and carType !=''">
                AND car_type = #{carType}
            </if>
        </where>
    </select>

    <update id="updateCarGift" parameterType="com.ls.ner.billing.gift.bo.CarGiftBo">
        update t_car_gift
        <set>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="certNo != null and certNo != ''">cert_no = #{certNo},</if>
            <if test="alipayNo != null and alipayNo != ''">alipay_no = #{alipayNo},</if>
            <if test="licenseNo != null and licenseNo != ''">license_no = #{licenseNo},</if>
            <if test="carType != null and carType != ''">car_type = #{carType},</if>
            <if test="vin != null and vin != ''">vin = #{vin},</if>
            <if test="giftAmt != null and giftAmt != ''">gift_amt = #{giftAmt},</if>
            <if test="giftStatus != null and giftStatus != ''">gift_status = #{giftStatus},</if>
            <if test="failReason != null and failReason != ''">fail_reason = #{failReason},</if>
            <if test="applyTime != null and applyTime != ''">apply_time = #{applyTime},</if>
            <if test="auditTime != null and auditTime != ''">audit_time = #{auditTime},</if>
            <if test="getTime != null and getTime != ''">get_time = #{getTime},</if>
            <if test="buyTime != null and buyTime != ''">buy_time = #{buyTime},</if>
            <if test="buyAddr != null and buyAddr != ''">buy_addr = #{buyAddr},</if>
            <if test="buyModel != null and buyModel != ''">buy_model = #{buyModel},</if>
            <if test="buyerName != null and buyerName != ''">buyer_name = #{buyerName},</if>
        </set>
        <where>
            <if test="giftId != null and giftId !='' ">
                and gift_id = #{giftId}
            </if>
        </where>
    </update>

    <insert id="insertImportData" parameterType="java.util.List">
        insert into t_car_gift
        (
        license_no,
        vin,
        cert_no,
        gift_status,
        buyer_name
        ) values
        <foreach collection="carGiftBoList" item="item" index="index" separator="," >
            (
            #{item.licenseNo},
            #{item.vin},
            #{item.certNo},
            #{item.giftStatus},
            #{item.buyerName}
            )
        </foreach>
    </insert>

    <select id="listCarGiftByCondition" parameterType="com.ls.ner.billing.gift.bo.CarGiftBo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_car_gift
        <where>
            <if test="licenseNo !=null and licenseNo !=''">
                and license_no = #{licenseNo}
            </if>
            <if test="vin !=null and vin !=''">
                and vin = #{vin}
            </if>
            <if test="certNo !=null and certNo !=''">
                and cert_no = #{certNo}
            </if>
            <if test="mobile != null and mobile !='' ">
                and mobile = #{mobile}
            </if>
            <if test="giftStatus !=null and giftStatus !=''">
                AND gift_status = #{giftStatus}
            </if>
        </where>
    </select>
    <select id="queryRecentGiftInfo" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT gift_amt giftAmt FROM t_car_gift WHERE mobile = #{mobile}
        AND gift_status = "04"
        AND DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[<=]]> get_time
        ORDER BY get_time DESC LIMIT 1
    </select>



</mapper>