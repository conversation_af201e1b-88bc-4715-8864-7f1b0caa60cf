package com.ls.ner.billing.api.integral;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */

public class MemberBenefitsDTO implements Serializable {

    /** 加倍积分 **/
    private String isDouble;
    /** 加倍积分倍数 **/
    private BigDecimal multiple;

    public String getIsDouble() {
        return isDouble;
    }

    public void setIsDouble(String isDouble) {
        this.isDouble = isDouble;
    }

    public BigDecimal getMultiple() {
        return multiple;
    }

    public void setMultiple(BigDecimal multiple) {
        this.multiple = multiple;
    }
}
