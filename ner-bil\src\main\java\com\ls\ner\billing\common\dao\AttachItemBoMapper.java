package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.AttachItemBo;
import com.ls.ner.billing.common.bo.AttachItemBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AttachItemBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(AttachItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(AttachItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(AttachItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(AttachItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<AttachItemBo> selectByExample(AttachItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    AttachItemBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") AttachItemBo record, @Param("example") AttachItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") AttachItemBo record, @Param("example") AttachItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(AttachItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_attach_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(AttachItemBo record);
}