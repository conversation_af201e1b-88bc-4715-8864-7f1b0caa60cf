package com.ls.ner.billing.market.bo;

public class ThreadPageBo {

    private Long pageSize;
    private Long maxCustId;
    private Long pageIndex;

    public Long getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Long pageIndex) {
        this.pageIndex = pageIndex;
    }

    public ThreadPageBo() {
    }

    public ThreadPageBo(Long pageSize, Long pageIndex) {
        this.pageSize = pageSize;
        this.pageIndex = pageIndex;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long getMaxCustId() {
        return maxCustId;
    }

    public void setMaxCustId(Long maxCustId) {
        this.maxCustId = maxCustId;
    }
}
