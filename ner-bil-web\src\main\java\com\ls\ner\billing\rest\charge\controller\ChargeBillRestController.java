package com.ls.ner.billing.rest.charge.controller;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.service.IChargeBillingRltService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Maps;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargePriceCondition;
import com.ls.ner.billing.api.charge.service.IChargeBillRpcService;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.service.impl.ChargeBillingRltUtil;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;

/**
 * object description: 充电   app接口
 * <AUTHOR>
 * @time 2016-11-01 14:30
 */
@Controller
@RequestMapping("/api")
public class ChargeBillRestController {
	private static final Logger logger = LoggerFactory.getLogger(ChargeBillRestController.class);

	@ServiceAutowired(serviceTypes =ServiceType.LOCAL, value = "chargeBillingRltService")
	private IChargeBillingRltService chargeBillingRltService;

	@ServiceAutowired(serviceTypes =ServiceType.RPC, value = "chargeBillRpcService")
	private IChargeBillRpcService chargeBillRpcService;

	@Autowired
	private ChargeBillingRltUtil cbRltUtil;

	/**
	 * Method description:  3.1.2	REST05-02查询充电定价
	 * <AUTHOR>
	 * @time 2016-11-24 14:30
	 */
	@RequestMapping(value = "/v0.1/charging-pricing", method = RequestMethod.GET)  
    public @ResponseBody Object queryModels(ChargePriceCondition condition) {

		Map<String,Object> returnMap = new HashMap<String,Object>();
		try {
			if(StringUtils.nullOrBlank(condition.getStationId())){
				returnMap.put("ret", 406);
				returnMap.put("msg", "站点id不为空");
				return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
			}
			Map<String,Object> searchMap = new HashMap<String,Object>();
			searchMap.put("prodType", "0");//全部
			List<Map> stationList = new LinkedList<Map>();
			searchMap.put("stationList", stationList);
			Map<String,Object> stationMap = new HashMap<String, Object>();
			stationList.add(stationMap);
			stationMap.put("stationId", condition.getStationId());
			List<Map<String, Object>> prodModeList = chargeBillRpcService.getChargePrice(searchMap);
			returnMap.put("prodModeList", prodModeList);
			returnMap.put("ret", 200);
			return new ResponseEntity<Map>((Map) StringUtil.notMapNull(returnMap), HttpStatus.OK);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			returnMap.put("ret", 400);
			returnMap.put("msg", e.getMessage());
			return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
		}
	} 
	
//	/**
//	 * Method description:  生产计费版本
//	 * <AUTHOR>
//	 * @time 2016-11-24 14:30
//	 */
//	@RequestMapping(value = "/v0.1/payInAdvance", method = RequestMethod.GET)  
//    public @ResponseBody Object payInAdvance(@RequestParam("orderNo") String orderNo,@RequestParam("stationId") String stationId) {
//
//		Map<String,Object> returnMap = new HashMap<String,Object>();
//		try {
//			returnMap.put("orderNo", orderNo);
//			returnMap.put("stationId", stationId);
//			return chargeBillRpcService.payInAdvance(returnMap);
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(), e);
//			returnMap.put("ret", 400);
//			returnMap.put("msg", e.getMessage());
//			return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
//		}
//	}
//
//	/**
//	 * Method description:  预估-性能测试
//	 * <AUTHOR>
//	 * @time 2016-11-24 14:30
//	 */
//	@RequestMapping(value = "/v0.1/estimate", method = RequestMethod.GET)  
//    public @ResponseBody Object estimate(@RequestParam("param") String param) {
//
//		Map<String,Object> returnMap = new HashMap<String,Object>();
//		try {
//			long current = System.currentTimeMillis();
//			returnMap = (Map<String,Object>) JsonUtils.json2Object(param, Map.class);
//			for(int i = 0;i < 100;i++){
//				chargeBillRpcService.estimate(returnMap);
//			}
//			long end =  System.currentTimeMillis();
//			System.out.print("使用redis之后，实时计费100次花费时间：" + (end - current)/1000 + "秒");
//			return true;
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(), e);
//			returnMap.put("ret", 400);
//			returnMap.put("msg", e.getMessage());
//			return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
//		}
//	}
//
//	/**
//	 * Method description:  预估
//	 * <AUTHOR>
//	 * @time 2016-11-24 14:30
//	 */
//	@RequestMapping(value = "/v0.1/estimate", method = RequestMethod.GET)  
//    public @ResponseBody Object estimate(@RequestParam("param") String param) {
//
//		Map<String,Object> returnMap = new HashMap<String,Object>();
//		try {
//			returnMap = (Map<String,Object>) JsonUtils.json2Object(param, Map.class);
//			return chargeBillRpcService.estimate(returnMap);
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(), e);
//			returnMap.put("ret", 400);
//			returnMap.put("msg", e.getMessage());
//			return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
//		}
//	}
//
//	/**
//	 * Method description:   结算
//	 * <AUTHOR>
//	 * @time 2016-11-24 14:30
//	 */
//	@RequestMapping(value = "/v0.1/settlement", method = RequestMethod.GET)  
//    public @ResponseBody Object settlement(@RequestParam("param") String param) {
//
//		Map<String,Object> returnMap = new HashMap<String,Object>();
//		try {
//			returnMap = (Map<String,Object>) JsonUtils.json2Object(param, Map.class);
//			return chargeBillRpcService.settlement(returnMap);
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error(e.getMessage(), e);
//			returnMap.put("ret", 400);
//			returnMap.put("msg", e.getMessage());
//			return new ResponseEntity<Map>(returnMap, HttpStatus.OK);
//		}
//	}
}
