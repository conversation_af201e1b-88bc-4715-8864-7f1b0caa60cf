/**
 *功能：VD validate的缩写
 *		用于所有公用的onValidate验证
 **/
var VD  = {

		/**
		 *功能：身份证号码校验
		 *参数说明： 
		 *      strText        输入控件的name
		 **/
		validateIdCard: function (strText) {
			var str = strText.getValue();
			var isValid = false;
			if (/^(\d{15}$|^\d{18}$|^\d{17}(\d|X|x))$/.test(str)) {
				isValid = true;
			}
			return {
	            isValid : isValid,
	            message : "身份证号码格式错误"
	        };
		},
		
		/**
		 *功能：用于校验输入日期必须大于此时此刻   输入格式：yyyy-MM-dd HH:mm
		 *参数说明： 
		 *      timeText        输入日期控件的name
		 **/
		dateTimeOfTodayValidate:function(timeText){
			var v = timeText.getValue();
			var isValid = true;
			if(!LS.isEmpty(v)){
				var current = (new Date()).Format("yyyy-MM-dd hh:mm");
		        isValid = (v > current);
			}
	        return {
	            isValid : isValid,
	            message : "时间选择必须大于当前时间"
	        }
	    },
	    
		/**
		 *功能：用于校验输入日期不大于今日
		 *参数说明： 
		 *      timeText        输入日期控件的name
		 **/
		timeOfTodayValidate:function(timeText){
			var v = timeText.getValue();
			var isValid = true;
			if(!LS.isEmpty(v)){
		        var value = v.split("-");
		        var time = new Date(value[0],value[1]-1,value[2]);
		        var current = new Date();
		        isValid = (time <= current);
			}
	        return {
	            isValid : isValid,
	            message : "日期选择不允许大于当前日期"
	        }
	    },
	    
		/**
		 *功能：用于小数点后两位校验
		 *参数说明： 
		 *      twoDecimalText        输入控件的name
		 **/
		towDecimalPlaceValidate:function(twoDecimalText){
	    	var bMoney = twoDecimalText.getValue();
			var isValid = true;
	    	if(!LS.isEmpty(bMoney)){
		    	var i = bMoney.indexOf(".");
		    	var str = "";
		    	if(i>=0){
		    		str = bMoney.substring(i+1);
		    	}
		    	isValid = (str.length <= 2);
	    	}
	    	return {
	            isValid : isValid,
	            message : "小数位不能超过两位"
	        }
	    },

		/**
		 *功能：用于小数点后一位校验
		 *参数说明： 
		 *      twoDecimalText        输入控件的name
		 **/
		oneDecimalPlaceValidate:function(twoDecimalText){
	    	var bMoney = twoDecimalText.getValue();
			var isValid = true;
	    	if(!LS.isEmpty(bMoney)){
		    	var i = bMoney.indexOf(".");
		    	var str = "";
		    	if(i>=0){
		    		str = bMoney.substring(i+1);
		    	}
		    	isValid = (str.length <= 1);
	    	}
	    	return {
	            isValid : isValid,
	            message : "小数位不能超过一位"
	        }
	    },
	    
		/**
		 *功能：用于车牌号规则校验
		 *参数说明： 
		 *      licenseNoText        输入控件的name
		 **/
		licenseNoValidate:function(licenseNoText){
	        var value = licenseNoText.getValue();
	        var isValid = true;
	        if(!LS.isEmpty(value)){
		    //	isValid = /^[\u4e00-\u9fa5]{1}[a-zA-Z]{1}[a-zA-Z0-9]{5}$/.test(value);
				isValid = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/.test(value);	    	}
	        return {
	            isValid : isValid,
	            message : "请输入正确的车牌号,格式如：闽D88888或者闽A-01839D"
	        }
	    },
		
		/**
		 *功能：用于校验输入的是否为正整数
		 *参数说明： 
		 *      loadNum        输入控件的name
		 **/
		positiveInteger:function(loadNum){
	        var value = loadNum.getValue();
	        var isValid = true;
	        if(!LS.isEmpty(value)){
		    	isValid = /^\d{1,}$/.test(value);
	    	}
	        return {
	            isValid : isValid,
	            message : "请输入整数"
	        }
	    },
	    
		/**
		 *功能：用于校验dataStart <= dataStop,否则提示 "日期起始时间必须小于或等于日期终止时间"
		 *参数说明： 
		 *      dateStart        日期控件的name  格式：yyy-MM-dd
		 *      dateStop         日期控件的name  格式：yyy-MM-dd
		 **/
		dataStartValidate : function(dateStart,dateStop){
			var start = dateStart.getValue();
			var stop = dateStop.getValue();
			var isValid = true;
			if(LS.isEmpty(start) || LS.isEmpty(stop))
				isValid = true;
			else{
				var begin = start.split('-');
				var end = stop.split('-');
				var startTime = new Date(begin[0],begin[1]-1,begin[2]);
				var stopTime = new Date(end[0],end[1]-1,end[2]);
				isValid = (startTime <=stopTime);
			}
			return {
		            isValid : isValid,
		            message : "日期起始时间必须小于或等于日期终止时间"
		        }
		},
		
		/**
		 *功能：用于校验dataStart <= dataStop,否则提示 "日期终止时间必须大于等于日期起始时间"
		 *参数说明： 
		 *      dateStart        日期控件的name  格式：yyy-MM-dd
		 *      dateStop         日期控件的name  格式：yyy-MM-dd
		 **/
		dateStopValidate : function(dateStart,dateStop){
			var start = dateStart.getValue();
			var stop = dateStop.getValue();
			var isValid = true;
			if(LS.isEmpty(start) || LS.isEmpty(stop))
				isValid = true;
			else{
				var begin = start.split('-');
				var end = stop.split('-');
				var startTime = new Date(begin[0],begin[1]-1,begin[2]);
				var stopTime = new Date(end[0],end[1]-1,end[2]);
				isValid = (startTime <=stopTime);
			}
			return {
		            isValid : isValid,
		            message : "日期终止时间必须大于等于日期起始时间"
		        }
		},
		
		/**
		 *功能：用于校验numberStart <= numberStop,否则提示 "起始数值必须小于或等于终止数值"
		 *参数说明： 
		 *      numberStart        起始数值
		 *      numberStop         终止数值
		 **/
		numberStartValidate:function(numberStart,numberStop){
			var start = numberStart.getValue();
			var stop = numberStop.getValue();
			var isValid = true;
			if(LS.isEmpty(start) || LS.isEmpty(stop))
				isValid = true;
			else{
				var begin = new Number(start);
				var end = new Number(stop);
				isValid = (begin <= end);
			}
			return {
			            isValid : isValid,
			            message : "起始数值必须小于或等于终止数值"
			        }
		},
		
		/**
		 *功能：用于校验numberStart <= numberStop,否则提示 "终止数值必须大于或等于起始数值"
		 *参数说明： 
		 *      numberStart        起始数值
		 *      numberStop         终止数值
		 **/
		numberStopValidate:function(numberStart,numberStop){
			var start = numberStart.getValue();
			var stop = numberStop.getValue();
			var isValid = true;
			if(LS.isEmpty(start) || LS.isEmpty(stop))
				isValid = true;
			else{
				var begin = new Number(start);
				var end = new Number(stop);
				isValid = (begin <= end);
			}
			return {
			            isValid : isValid,
			            message : "终止数值必须大于或等于起始数值"
			        }
		},
		validTelphone: function (strText) {
			var str = strText.getValue();
			var isValid = false;
			//var isMob=/^((\+?86)|(\(\+86\)))?(13[0123456789][0-9]{8}|15[012356789][0-9]{8}|18[012356789][0-9]{8}|147[0-9]{8}|1349[0-9]{7})$/;
		    var isMob = /^1(3|4|5|7|8)\d{9}$/;
			if(isMob.test(str)){
				isValid = true;
			}
			return {
	            isValid : isValid,
	            message : "手机号码格式错误"
	        };
		}
}

//对Date的扩展，将 Date 转化为指定格式的String   
//月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，   
//年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)   
//例子：   
//(new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423   
//(new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18   
Date.prototype.Format = function(fmt)   
{ //author: meizz   
	var o = {   
	 "M+" : this.getMonth()+1,                 //月份   
	 "d+" : this.getDate(),                    //日   
	 "h+" : this.getHours(),                   //小时   
	 "m+" : this.getMinutes(),                 //分   
	 "s+" : this.getSeconds(),                 //秒   
	 "q+" : Math.floor((this.getMonth()+3)/3), //季度   
	 "S"  : this.getMilliseconds()             //毫秒   
	};   
	if(/(y+)/.test(fmt))   
	 fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));   
	for(var k in o)   
	 if(new RegExp("("+ k +")").test(fmt))   
	fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));   
	return fmt;   
}
