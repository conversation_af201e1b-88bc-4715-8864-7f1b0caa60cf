package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.BillingFuncBo;
import com.ls.ner.billing.common.bo.BillingFuncBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface BillingFuncBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int countByExample(BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int deleteByExample(BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int insert(BillingFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int insertSelective(BillingFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    List<BillingFuncBo> selectByExampleWithBLOBs(BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    List<BillingFuncBo> selectByExample(BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    BillingFuncBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByExampleSelective(@Param("record") BillingFuncBo record, @Param("example") BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByExampleWithBLOBs(@Param("record") BillingFuncBo record, @Param("example") BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByExample(@Param("record") BillingFuncBo record, @Param("example") BillingFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByPrimaryKeySelective(BillingFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByPrimaryKeyWithBLOBs(BillingFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_func
     *
     * @mbggenerated Tue Mar 15 14:42:10 CST 2016
     */
    int updateByPrimaryKey(BillingFuncBo record);
}