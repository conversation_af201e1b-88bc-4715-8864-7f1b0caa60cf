<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="车型定价" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
		<ls:form id="billingConfigPageForm" name="billingConfigPageForm">
		    <ls:text name="orgCode" property="orgCode" type="hidden"></ls:text>
		    <ls:text name="autoModelNo" property="autoModelNo" type="hidden"></ls:text>
		    <ls:text name="autoModelName" property="autoModelName" type="hidden"></ls:text>
		    <ls:text name="subBeName" property="subBeName" type="hidden"></ls:text>
		    <ls:text name="subBe" property="subBe" type="hidden"></ls:text>
		    <ls:text name="chargeWayName" property="chargeWayName" type="hidden"></ls:text>
        <ls:text name="chargeWay" property="chargeWay" type="hidden"></ls:text>
        <ls:text name="chargeModeName" property="chargeModeName" type="hidden"></ls:text>
        <ls:text name="chargeMode" property="chargeMode" type="hidden"></ls:text>
        <ls:text name="planNo" property="planNo" type="hidden"></ls:text>
        <ls:text name="billingConfigName" property="billingConfigName" type="hidden"></ls:text>
        <ls:text name="eftDate" property="eftDate" type="hidden"></ls:text>
				<ls:title text="租赁费用"></ls:title>
				<table class="tab_search">
						<tr style="display: none;">
								<td>
										<ls:label text="付费时点" ref="billingFunc_mainChargeItem_chargeTimePoint" />
								</td>
								<td>
										<ls:select name="billingFunc_mainChargeItem_chargeTimePoint" property="billingFunc.mainChargeItem.chargeTimePoint" required="true" visible="false" value="00">
												<ls:option value="01" text="预付费"></ls:option>
												<ls:option value="02" text="后付费"></ls:option>
										</ls:select>
								</td>
						</tr>
						<tr>
						<td>
						<table>
						<%--按时间 --%>
						<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeWay eq '0101'||billingConfigPageForm.billingFunc.mainChargeItem.chargeWay eq '0103' }">
								<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '01'}">
										<tr>
												<td>
														<ls:label text="时间单价" ref="billingFunc_mainChargeItem_timeChargeItem_price" />
												</td>
												<td colspan="2">
														<ls:text name="billingFunc_mainChargeItem_timeChargeItem_price" property="billingFunc.mainChargeItem.timeChargeItem.price" required="true" type="number"></ls:text>
												</td>
												<td>
														元/
														<c:out value="${billingConfigPageForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc}" />
												</td>
										</tr>
								</c:if>
								<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'|| billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
										<tr>
												<td width="80px">
														<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
																<ls:label text="时间分时价格" />
														</c:if>
														<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
																<ls:label text="时间阶梯价格" />
														</c:if>
												</td>
												<td colspan="3">
												    <c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
													    <ls:grid url=""  allowScroll="false" primaryKey="sn" name="timeGrid" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="500px">
									              <ls:column caption="排序号" name="sn" hidden="true"/>
									              <ls:column width="80px" caption="开始时段" name="beginTime" editableEdit="false"/>
									              <ls:column width="80px" caption="结束时段" name="endTime" editableEdit="false">
									              </ls:column>
									              <ls:column width="120px" caption="单价（元/${billingConfigPageForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc}）" name="priceTime">
									                <ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
									              </ls:column>
									              <ls:column width="80px" caption="是否包段" name="isPackTime" >
									                <ls:checkListEditor type="radio" > 
                                   <ls:checkListItemEditor value="1" text="是"></ls:checkListItemEditor>
                                   <ls:checkListItemEditor value="0" text="否"></ls:checkListItemEditor>
                                  </ls:checkListEditor>
                                </ls:column>
                                <ls:column width="100px" caption="包段价格（元）" name="priceAmtTime">
                                  <ls:textEditor maxlength="11" type="number" max="9999" onValidate="priceValidate()"/>
                                </ls:column>
									            </ls:grid>
								            </c:if>
                            <c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
                              <ls:grid url=""  allowScroll="false" name="timeGrid" primaryKey="sn" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="500px">
                                <ls:column caption="排序号" name="sn" hidden="true"/>
                                <ls:column width="150px" caption="开始(${billingConfigPageForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.unitName})" name="beginTime" editableEdit="false"/>
                                <ls:column width="150px" caption="结束(${billingConfigPageForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.unitName})" name="endTime" editableEdit="false">
                                </ls:column>
                                <ls:column width="200px" caption="单价（元/${billingConfigPageForm.billingFunc.mainChargeItem.timeChargeItem.priceUnit.desc}）" name="priceTime">
                                  <ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
                                </ls:column>
                              </ls:grid>
                            </c:if>
												</td>
										</tr>
								</c:if>
								<tr>
		                <td>
		                    <ls:label text="时间最低消费"/>
		                </td>
		                <td>
		                    <ls:select name="billingFunc_mainChargeItem_timeChargeItem_min_limitType" property="billingFunc.mainChargeItem.timeChargeItem.min.limitType" onchanged="change_timeChargeItem_min_limitType">
		                        <ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
		                    </ls:select>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_timeChargeItem_min_limitQuantity" property="billingFunc.mainChargeItem.timeChargeItem.min.limitQuantity" onValidate="positiveIntegerTimeMin(billingFunc_mainChargeItem_timeChargeItem_min_limitQuantity)" type="number"></ls:text>
		                </td>
		                <td><span class="timeChargeItem_min_limitType">元</span></td>
		            </tr>
		            <tr>
		                <td>
		                    <ls:label text="时间最高消费"/>
		                </td>
		                <td>
		                    <ls:select name="billingFunc_mainChargeItem_timeChargeItem_max_limitType" property="billingFunc.mainChargeItem.timeChargeItem.max.limitType" onchanged="change_timeChargeItem_max_limitType">
		                        <ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
		                    </ls:select>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_timeChargeItem_max_limitQuantity" property="billingFunc.mainChargeItem.timeChargeItem.max.limitQuantity" onValidate="positiveIntegerTimeMax(billingFunc_mainChargeItem_timeChargeItem_max_limitQuantity)" type="number"></ls:text>
		                </td>
		                <td><span class="timeChargeItem_max_limitType">元</span></td>
		            </tr>
		            <tr style="display: none;">
		                <td>
		                    <ls:label text="APP显示价格" ref="billingFunc_mainChargeItem_timeChargeItem_displayPrice"/>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_timeChargeItem_displayPrice" property="billingFunc.mainChargeItem.timeChargeItem.displayPrice" required="true" value="2元/分钟"></ls:text>
		                </td>
		                <td colspan="2">(示例:2元/分钟)</td>
		            </tr>
		            <tr>
		                <td>
		                    <ls:label text="描述"/>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_timeChargeItem_priceRemark" property="billingFunc.mainChargeItem.timeChargeItem.priceRemark" type="textarea"></ls:text>
		                </td>
		                <td colspan="2"></td>
		            </tr>
						</c:if>
						</table>
						</td>
						<td>
						<table>
						<%--按里程 --%>
						<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeWay=='0102'||billingConfigPageForm.billingFunc.mainChargeItem.chargeWay=='0103' }">
								<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '01'}">
										<tr>
												<td>
														<ls:label text="里程单价" ref="billingFunc_mainChargeItem_millChargeItem_price" />
												</td>
												<td colspan="2">
														<ls:text name="billingFunc_mainChargeItem_millChargeItem_price" property="billingFunc.mainChargeItem.millChargeItem.price" required="true" type="number"></ls:text>
												</td>
												<td>
														元/
														<c:out value="${billingConfigPageForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.desc}" />
												</td>
										</tr>
								</c:if>
								<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'|| billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
										<tr>
												<td width="80px">
														<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
																<ls:label text="里程分时价格" />
														</c:if>
														<c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
																<ls:label text="里程阶梯价格" />
														</c:if>
												</td>
												<td colspan="3">
                            <c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '02'}">
                              <ls:grid url=""  allowScroll="false" primaryKey="sn" name="millGrid" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="500px">
                                <ls:column caption="排序号" name="sn" hidden="true"/>
                                <ls:column width="80px"  caption="开始时段" name="beginTime" editableEdit="false"/>
                                <ls:column width="80px"  caption="结束时段" name="endTime" editableEdit="false">
                                </ls:column>
                                <ls:column width="120px" caption="单价（元/${billingConfigPageForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.desc}）" name="priceMill">
                                  <ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
                                </ls:column>
                                <ls:column width="80px" caption="是否包段" name="isPackMill" >
                                  <ls:checkListEditor type="radio" > 
                                   <ls:checkListItemEditor value="1" text="是"></ls:checkListItemEditor>
                                   <ls:checkListItemEditor value="0" text="否"></ls:checkListItemEditor>
                                  </ls:checkListEditor>
                                </ls:column>
                                <ls:column width="100px" caption="包段价格（元）" name="priceAmtMill">
                                  <ls:textEditor maxlength="11" type="number" max="9999" onValidate="priceValidate()"/>
                                </ls:column>
                              </ls:grid>
                            </c:if>
                            <c:if test="${billingConfigPageForm.billingFunc.mainChargeItem.chargeMode eq '03'}">
                              <ls:grid url=""  allowScroll="false" name="millGrid" primaryKey="sn" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="500px">
                                <ls:column caption="排序号" name="sn" hidden="true"/>
                                <ls:column width="150px" caption="开始(${billingConfigPageForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.unitName})" name="beginTime" editableEdit="false"/>
                                <ls:column width="150px" caption="结束(${billingConfigPageForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.unitName})" name="endTime" editableEdit="false">
                                </ls:column>
                                <ls:column width="200px" caption="单价（元/${billingConfigPageForm.billingFunc.mainChargeItem.millChargeItem.priceUnit.desc}）" name="priceMill">
                                  <ls:textEditor maxlength="11" required="true" type="number" max="9999" onValidate="priceValidate()"/>
                                </ls:column>
                              </ls:grid>
                            </c:if>
                        </td>
										</tr>
								</c:if>
								<tr>
                    <td>
                        <ls:label text="里程最低消费"/>
                    </td>
                    <td>
                        <ls:select name="billingFunc_mainChargeItem_millChargeItem_min_limitType" property="billingFunc.mainChargeItem.millChargeItem.min.limitType" onchanged="change_millChargeItem_min_limitType">
                            <ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
                        </ls:select>
                    </td>
                    <td>
                        <ls:text name="billingFunc_mainChargeItem_millChargeItem_min_limitQuantity" property="billingFunc.mainChargeItem.millChargeItem.min.limitQuantity" onValidate="positiveIntegerMillMin(billingFunc_mainChargeItem_millChargeItem_min_limitQuantity)" type="number"></ls:text>
                    </td>
                    <td><span class="millChargeItem_min_limitType">元</span></td>
                </tr>
                <tr>
                    <td>
                        <ls:label text="里程最高消费" />
                    </td>
                    <td>
                        <ls:select name="billingFunc_mainChargeItem_millChargeItem_max_limitType" property="billingFunc.mainChargeItem.millChargeItem.max.limitType" onchanged="change_millChargeItem_max_limitType">
                            <ls:options property="billingLimitTypeList" scope="request" text="codeName" value="codeValue" />
                        </ls:select>
                    </td>
                    <td>
                        <ls:text name="billingFunc_mainChargeItem_millChargeItem_max_limitQuantity" property="billingFunc.mainChargeItem.millChargeItem.max.limitQuantity" onValidate="positiveIntegerMillMax(billingFunc_mainChargeItem_millChargeItem_max_limitQuantity)" type="number"></ls:text>
                    </td>
                    <td><span class="millChargeItem_max_limitType">元</span></td>
                </tr>
                <tr style="display: none;">
		                <td>
		                    <ls:label text="APP显示价格" ref="billingFunc_mainChargeItem_millChargeItem_displayPrice"/>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_millChargeItem_displayPrice" property="billingFunc.mainChargeItem.millChargeItem.displayPrice" required="true" value="3元/2公里"></ls:text>
		                </td>
		                <td  colspan="2">(示例:3元/2公里)</td>
		            </tr>
		            <tr>
		                <td>
		                    <ls:label text="描述"/>
		                </td>
		                <td>
		                    <ls:text name="billingFunc_mainChargeItem_millChargeItem_priceRemark" property="billingFunc.mainChargeItem.millChargeItem.priceRemark" type="textarea"></ls:text>
		                </td>
		                <td colspan="2"></td>
		            </tr>
						</c:if>
						</table>
						</td>
						
						</tr>
				</table>
				<ls:title text="押金项目"></ls:title>
				<table class="tab_search">
				  <tr>
						<c:forEach var="aci" items="${billingConfigPageForm.billingFunc.depositChargeItems}" varStatus="status">
										<td width="80px">
										    <ls:label text="${aci.itemName}" ref="depositItem_price_${status.index}"></ls:label>
										</td>
										<td>
												<ls:select name="depositItem_chargeTimePoint_${status.index}" id="depositItem_chargeTimePoint_${status.index}" property="billingFunc.depositChargeItems[${status.index}].chargeTimePoint" visible="false" value="00">
														<ls:option value="01" text="预付费"></ls:option>
														<ls:option value="02" text="后付费"></ls:option>
												</ls:select>
										</td>
										<td>
												<ls:text name="depositItem_price_${status.index}" id="depositItem_price_${status.index}" property="billingFunc.depositChargeItems[${status.index}].price" required="true" type="number"></ls:text>
										</td>
										<td>
												元/
												<c:out value="${aci.priceUnit.desc}" />
										</td>
										<td>
										    	<ls:label text="描述"></ls:label>
										</td>
										<td>
												<ls:text name="depositItem_priceRemarks_${status.index}" id="depositItem_priceRemarks_${status.index}" property="billingFunc.depositChargeItems[${status.index}].priceRemarks" ></ls:text>
										</td>
						</c:forEach>
					</tr>
				</table>
				<ls:title text="其他服务项目"></ls:title>
				<table class="tab_search">
				<tr>
						<c:forEach var="aci" items="${billingConfigPageForm.billingFunc.attachChargeItems}" varStatus="status">
										<td>
										    <ls:label text="${aci.itemName}(${aci.buyTypeName})" ref="attachItem_price_${status.index}"></ls:label>
										</td>
										<td>
												<ls:select name="attachItem_chargeTimePoint_${status.index}" id="attachItem_chargeTimePoint_${status.index}" property="billingFunc.attachChargeItems[${status.index}].chargeTimePoint" visible="false" value="00">
														<ls:option value="01" text="预付费"></ls:option>
														<ls:option value="02" text="后付费"></ls:option>
												</ls:select>
										</td>
										<td>
												<ls:text name="attachItem_price_${status.index}" id="attachItem_price_${status.index}" property="billingFunc.attachChargeItems[${status.index}].price" required="true" type="number"></ls:text>
										</td>
										<td>
												元/
												<c:out value="${aci.priceUnit.desc}" />
										</td>
										<td>
										    <ls:label text="描述"></ls:label>
										</td>
										<td>
												<ls:text name="attachItem_priceRemarks_${status.index}" id="attachItem_priceRemarks_${status.index}" property="billingFunc.attachChargeItems[${status.index}].priceRemarks" ></ls:text>
										</td>
						</c:forEach>
					</tr>
				</table>
		</ls:form>
		<c:if test="${operFlag=='add'|| operFlag=='edit' }">
				<table>
						<tr>
								<td>
										<ls:button text="保存" onclick="saveBillingConfigs"></ls:button>
								</td>
						</tr>
				</table>
		</c:if>
		<ls:script>
	var baseUrl = "~/billing/configs";
	var billingNo = '<c:out value="${requestScope.billingNo}" />';
	var billingConfigFormObj = <c:out value="${requestScope.billingConfigFormJson}" escapeXml="false" />;

	var chargeWayTemp = billingConfigFormObj.billingFunc.mainChargeItem.chargeWay;
	var chargeModeTemp = billingConfigFormObj.billingFunc.mainChargeItem.chargeMode;
  if(chargeWayTemp == '0101' || chargeWayTemp == '0103'){
    var timeChargeItemTemp = billingConfigFormObj.billingFunc.mainChargeItem.timeChargeItem;
    if(chargeModeTemp == '02' || chargeModeTemp == '03'){
      var rangeChargeItems = timeChargeItemTemp.rangeChargeItems;
      if(rangeChargeItems){
        var item = [];
        for(var i=0;i< rangeChargeItems.length ;i++){
          var itemRow = {
		        sn:i,
		        beginTime:rangeChargeItems[i].range.from,
		        endTime:rangeChargeItems[i].range.to?rangeChargeItems[i].range.to:'∞',
		        priceTime:'',
		        isPackTime:'',
		        priceAmtTime:''
	        };
	        item.push(itemRow);
        }
        timeGrid.appendItem(item);
        window.timeGrid=timeGrid;
      }
    }
  }

  if(chargeWayTemp == '0102' || chargeWayTemp == '0103'){
    var millChargeItemTemp = billingConfigFormObj.billingFunc.mainChargeItem.millChargeItem;
    if(chargeModeTemp == '02' || chargeModeTemp == '03'){
      var rangeChargeItems = millChargeItemTemp.rangeChargeItems;
      if(rangeChargeItems){
        var item = [];
        for(var i=0;i< rangeChargeItems.length ;i++){
          var itemRow = {
            sn:i,
            beginTime:rangeChargeItems[i].range.from,
            endTime:rangeChargeItems[i].range.to?rangeChargeItems[i].range.to:'∞',
            priceMill:'',
            isPackMill:'',
            priceAmtMill:''
          };
          item.push(itemRow);
        }
        millGrid.appendItem(item);
        window.millGrid=millGrid;
      }
    }
  }
    function millGridCellEditAfter(rowid,cellname,value,bbb){

      if(cellname=="isPackMill"){
        if(value!="包段"){
          millGrid.setCell(rowid,'priceAmtMill','');
        }
      }
    }
    function timeGridCellEditAfter(rowid,cellname,value,bbb){

      if(cellname=="isPackTime"){
        if(value!="包段"){
          timeGrid.setCell(rowid,'priceAmtTime','');
        }
      }
    }
    function saveBillingConfigs(){
    billingConfigPageForm.fields.orgCode.setValue(document.forms[0].orgCode.value);
    billingConfigPageForm.fields.autoModelNo.setValue(document.forms[0].autoModelNo.value);
    billingConfigPageForm.fields.autoModelName.setValue(document.forms[0].autoModelName.value);
    billingConfigPageForm.fields.chargeModeName.setValue(document.forms[0].chargeModeName.value);
    billingConfigPageForm.fields.chargeMode.setValue(document.forms[0].chargeMode.value);
    billingConfigPageForm.fields.chargeWayName.setValue(document.forms[0].chargeWayName.value);
    billingConfigPageForm.fields.chargeWay.setValue(document.forms[0].chargeWay.value);
    billingConfigPageForm.fields.subBeName.setValue(document.forms[0].subBeName.value);
    billingConfigPageForm.fields.subBe.setValue(document.forms[0].subBe.value);
    billingConfigPageForm.fields.billingConfigName.setValue(document.forms[0].billingConfigName.value);
    billingConfigPageForm.fields.eftDate.setValue(document.forms[0].eftDate.value);
    if(LS.isEmpty(billingConfigName.getValue())){
      LS.message("info","请填写定价方案名称");
      return;
    }
    if(LS.isEmpty(eftDate.getValue())){
      LS.message("info","请选择生效日期");
      return;
    }
    if(eftDate.getValue()< nowDateFormat("yyyy-MM-dd hh:mm")){
      LS.message("info","生效日期必须大于当前时间");
      return;
    }
    var url = rootUrl+'billing/configs';
    url += '/post';
    if(billingNo){
    	url += '/'+billingNo;
    }
    	
    	<%-- 收集需要提交的数据 --%>
    	var formData = billingConfigPageForm.getFormData();
    	billingConfigFormObj.orgCode = orgCode.getValue();
    	billingConfigFormObj.autoModelNo = autoModelNo.getValue();
    	billingConfigFormObj.autoModelName = autoModelName.getValue();
    	billingConfigFormObj.eftDate = eftDate.getValue();
    	billingConfigFormObj.billingConfigName = billingConfigName.getValue();
    	//billingConfigFormObj.unionPrice = unionPrice.getValue();
    	var billingFunc = billingConfigFormObj.billingFunc;
    	var mainChargeItem = billingFunc.mainChargeItem;
    	var chargeWay = mainChargeItem.chargeWay;
    	var chargeMode = mainChargeItem.chargeMode;
    	mainChargeItem.chargeTimePoint = billingFunc_mainChargeItem_chargeTimePoint.getValue();

    	if(chargeWay == '0101' || chargeWay == '0103'){
    		var timeChargeItem = mainChargeItem.timeChargeItem;
    		timeChargeItem.displayPrice = billingFunc_mainChargeItem_timeChargeItem_displayPrice.getValue();
    		timeChargeItem.priceRemark = billingFunc_mainChargeItem_timeChargeItem_priceRemark.getValue();
    		if(chargeMode == '01'){
    			timeChargeItem.price = billingFunc_mainChargeItem_timeChargeItem_price.getValue();
    		}
    		else if(chargeMode == '02' || chargeMode == '03'){
    			var rangeChargeItems = timeChargeItem.rangeChargeItems;
    			if(rangeChargeItems){
	    			for(var i=0;i< rangeChargeItems.length ;i++){
	    			  var gridItem = timeGrid.getItems()[i];
	    				var rci=rangeChargeItems[i]; 
	    				rci.price=gridItem.priceTime;
	    				if(LS.isEmpty(gridItem.priceTime)){
	    				  LS.message("error","时间价格中"+rangeChargeItems[i].range.desc+"的单价不能为空");
                return;
	    				}
	    				if(gridItem.isPackTime=='是'||gridItem.isPackTime=='1'){
               rci.isPack = '1';
               rci.priceAmt = gridItem.priceAmtTime;
               if(LS.isEmpty(gridItem.priceAmtTime)){
	                LS.message("error","时间价格中"+rangeChargeItems[i].range.desc+"的包段价格不能为空");
                  return;
	             }
              }
	    			}
    			}    		
    		}
    	}

    	if(chargeWay== '0102' || chargeWay== '0103'){
    		var millChargeItem=mainChargeItem.millChargeItem;
    		millChargeItem.displayPrice = billingFunc_mainChargeItem_millChargeItem_displayPrice.getValue();
    		millChargeItem.priceRemark = billingFunc_mainChargeItem_millChargeItem_priceRemark.getValue();
    		if(chargeMode== '01'){
    			millChargeItem.price=billingFunc_mainChargeItem_millChargeItem_price.getValue(); 
    		}
    		else if(chargeMode== '02' || chargeMode== '03'){
    			var rangeChargeItems=millChargeItem.rangeChargeItems; 
    			if(rangeChargeItems){
	    			for(var i=0;i < rangeChargeItems.length;i++){
	    		    var gridItem = millGrid.getItems()[i];
	    				var rci = rangeChargeItems[i];
	    				rci.price = gridItem.priceMill;
	    				if(LS.isEmpty(gridItem.priceMill)){
                LS.message("error","里程价格中"+rangeChargeItems[i].range.desc+"的单价不能为空");
                return;
              }
	    				if(gridItem.isPackMill=='是'||gridItem.isPackMill=='1'){
	    				 rci.isPack = '1';
	    				 rci.priceAmt = gridItem.priceAmtMill;
	    				 if(LS.isEmpty(gridItem.priceAmtMill)){
                  LS.message("error","里程价格中"+rangeChargeItems[i].range.desc+"的包段价格不能为空");
                  return;
               }
	    				}
	    			}
    			}
    		}
    	}
    	
      if(mainChargeItem.millChargeItem){
        var min = mainChargeItem.millChargeItem.min;
	      min.limitType = billingFunc_mainChargeItem_millChargeItem_min_limitType.getValue();
	      min.limitQuantity = billingFunc_mainChargeItem_millChargeItem_min_limitQuantity.getValue();
	      var max = mainChargeItem.millChargeItem.max;
	      max.limitType = billingFunc_mainChargeItem_millChargeItem_max_limitType.getValue();
	      max.limitQuantity = billingFunc_mainChargeItem_millChargeItem_max_limitQuantity.getValue();   
      }
      if(mainChargeItem.timeChargeItem){
        var min = mainChargeItem.timeChargeItem.min;
        min.limitType = billingFunc_mainChargeItem_timeChargeItem_min_limitType.getValue();
        min.limitQuantity = billingFunc_mainChargeItem_timeChargeItem_min_limitQuantity.getValue();
        var max = mainChargeItem.timeChargeItem.max;
        max.limitType = billingFunc_mainChargeItem_timeChargeItem_max_limitType.getValue();
        max.limitQuantity = billingFunc_mainChargeItem_timeChargeItem_max_limitQuantity.getValue();   
      }
    	    	
    	var depositChargeItems = billingFunc.depositChargeItems;
    	if(depositChargeItems){
	    	for(var i=0;i< billingFunc.depositChargeItems.length;i++){
	    		depositChargeItems[i].chargeTimePoint = formData['depositItem_chargeTimePoint_'+i];
	    		depositChargeItems[i].price = formData['depositItem_price_'+i];
	    		depositChargeItems[i].priceRemarks = formData['depositItem_priceRemarks_'+i];
	    	}
    	}
		var attachChargeItems = billingFunc.attachChargeItems;
		if(attachChargeItems){
	    	for(var i=0;i< billingFunc.attachChargeItems.length;i++){
	    		attachChargeItems[i].chargeTimePoint = formData['attachItem_chargeTimePoint_'+i];
	    		attachChargeItems[i].price = formData['attachItem_price_'+i];
	    		attachChargeItems[i].priceRemarks = formData['attachItem_priceRemarks_'+i];
	    	}
    	}
    	if(!billingConfigPageForm.valid()){
    	 return;
    	}
      	$.ajax({
      	method:'POST',
      	url:url, 
      	contentType:'application/json',
      	data:JSON.stringify(billingConfigFormObj),
      	dataType:'json'
      	}).done(function(result){
     		
       		LS.message("info","保存成功，车型定价编号为"+result.resultValue);
       		LS.parent().doSearch();
          LS.window.close();
       	
      });
      
    }
    function nowDateFormat(fmt){
      var d = new Date();
      var o = {
          "M+": d.getMonth() + 1, //月份 
          "d+": d.getDate(), //日 
          "h+": d.getHours(), //小时 
          "m+": d.getMinutes(), //分 
          "s+": d.getSeconds(), //秒 
          "q+": Math.floor((d.getMonth() + 3) / 3), //季度 
          "S": d.getMilliseconds() //毫秒 
      };
      if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
       if (new RegExp("(" + k + ")").test(fmt)) 
       fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    }
    function priceValidate(value){
      var bMoney = value;
    var isValid = true;
      if(!LS.isEmpty(bMoney)){
        var i = bMoney.indexOf(".");
        var str = "";
        if(i>=0){
          str = bMoney.substring(i+1);
        }
        isValid = (str.length <= 4);
      }
      return {
            isValid : isValid,
            message : "小数位不能超过四位"
      }
    }
    function change_timeChargeItem_min_limitType(){
      var limitType = billingFunc_mainChargeItem_timeChargeItem_min_limitType.getValue();
      if(limitType=="01"){//按金额
        $('.timeChargeItem_min_limitType').html("元");
      } else if (limitType=="02"){//单位
        var priceUnit = billingConfigFormObj.billingFunc.mainChargeItem.timeChargeItem.priceUnit;
        $('.timeChargeItem_min_limitType').html("*"+priceUnit.value+priceUnit.unitName);
      }
    }
    function change_timeChargeItem_max_limitType(){
      var limitType = billingFunc_mainChargeItem_timeChargeItem_max_limitType.getValue();
      if(limitType=="01"){//按金额
        $('.timeChargeItem_max_limitType').html("元");
      } else if (limitType=="02"){//单位
        var priceUnit = billingConfigFormObj.billingFunc.mainChargeItem.timeChargeItem.priceUnit;
        $('.timeChargeItem_max_limitType').html("*"+priceUnit.value+priceUnit.unitName);
      }
    }
    function change_millChargeItem_min_limitType(){
      var limitType = billingFunc_mainChargeItem_millChargeItem_min_limitType.getValue();
      if(limitType=="01"){//按金额
        $('.millChargeItem_min_limitType').html("元");
      } else if (limitType=="02"){//单位
        var priceUnit = billingConfigFormObj.billingFunc.mainChargeItem.millChargeItem.priceUnit;
        $('.millChargeItem_min_limitType').html("*"+priceUnit.value+priceUnit.unitName);
      }
    }
    function change_millChargeItem_max_limitType(){
      var limitType = billingFunc_mainChargeItem_millChargeItem_max_limitType.getValue();
      if(limitType=="01"){//按金额
        $('.millChargeItem_max_limitType').html("元");
      } else if (limitType=="02"){//单位
        var priceUnit = billingConfigFormObj.billingFunc.mainChargeItem.millChargeItem.priceUnit;
        $('.millChargeItem_max_limitType').html("*"+priceUnit.value+priceUnit.unitName);
      }
    }
    function positiveIntegerTimeMin(value){
      var start = billingFunc_mainChargeItem_timeChargeItem_min_limitQuantity.getValue();
      var stop = billingFunc_mainChargeItem_timeChargeItem_max_limitQuantity.getValue();
      return startEndValidate(value,start,stop);
    }
    function positiveIntegerTimeMax(value){
      var start = billingFunc_mainChargeItem_timeChargeItem_min_limitQuantity.getValue();
      var stop = billingFunc_mainChargeItem_timeChargeItem_max_limitQuantity.getValue();
      return startEndValidate(value,start,stop);
    }
    function startEndValidate(value,start,stop){
      var ret = VD.positiveInteger(value);
      if(!ret.isValid){
        return ret;
      }
      var isValid = true;
      if(LS.isEmpty(start) || LS.isEmpty(stop))
        isValid = true;
      else{
        var begin = parseFloat(start);
        var end = parseFloat(stop);
        isValid = (begin <= end);
      }
      return {
                  isValid : isValid,
                  message : "最低消费必须小于或等于最高消费"
              }
    }
    function positiveIntegerMillMin(value){
      var start = billingFunc_mainChargeItem_millChargeItem_min_limitQuantity.getValue();
      var stop = billingFunc_mainChargeItem_millChargeItem_max_limitQuantity.getValue();
      return startEndValidate(value,start,stop);
    }
    function positiveIntegerMillMax(value){
      var start = billingFunc_mainChargeItem_millChargeItem_min_limitQuantity.getValue();
      var stop = billingFunc_mainChargeItem_millChargeItem_max_limitQuantity.getValue();
      return startEndValidate(value,start,stop);
    }
    </ls:script>
</ls:body>
</html>