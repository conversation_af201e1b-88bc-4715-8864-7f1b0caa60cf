package com.ls.ner.billing.vip.bo;

import com.pt.poseidon.api.framework.DicAttribute;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description 会员权益
 */
public class MemberBenefitsBo implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键id **/
    private String vipId;
    /** 会员类型 01普通会员 02付费会员 **/
    private String vipType;
    @DicAttribute(dicName = "codeDict", key = "vipType", subType =
            "memberBenefitsType")
    private String vipTypeName;
    /** 会员等级 **/
    private String vipLevel;
    @DicAttribute(dicName = "codeDict", key = "vipLevel", subType =
            "memberBenefitsLevel")
    private String vipLevelName;
    /** 积分兑换 **/
    private String isExchange;
    /** 加倍积分 **/
    private String isDouble;
    /** 专享优惠 **/
    private String isExclusiveOffer;
    /** 专属客服 **/
    private String isExclusiveCustomer;
    /** 升级礼包 **/
    private String isUpgradePackage;
    /** 会员场站 **/
    private String isMemberStation;
    /** 开卡礼 **/
    private String isWelcomeGift;
    /** 会员日折上折 **/
    private String isMemberDayAdditionalDiscount;
    /** 会员权益数量 **/
    private Integer benefitsCount;
    /** 加倍积分倍数 **/
    private BigDecimal multiple;
    /** 专享优惠优惠券 **/
    private String exclusiveOffer;
    private List<VipCouponBo> exclusiveOfferList;
    /** 升级礼包等送积分数 **/
    private Integer upgradePackage;
    /** 会员场站折扣 **/
    private BigDecimal memberStation;
    /** 会员日 **/
    private String memberDay;
    /** 开卡礼优惠券 **/
    private String welcomeGift;
    private List<VipCouponBo> welcomeGiftList;
    /** 会员日折上折折扣 **/
    private BigDecimal memberDayAdditionalDiscount;

    private String custId;

    private String mobile;

    /**
     * 积分兑换说明
     */
    private String isExchangeExplain;

    /**
     * 加倍积分说明
     */
    private String isDoubleExplain;

    /**
     * 专属客服说明
     */
    private String isExclusiveCustomerExplain;

    /**
     * 升级礼包说明
     */
    private String isUpgradePackageExplain;

    /**
     * 会员场站说明
     */
    private String isMemberStationExplain;

    /**
     * 会员日折上折说明
     */
    private String isMemberDayAdditionalDiscountExplain;

    /**
     * 最高月省度数配置
     */
    private String monthDegreeConfig;

    /**
     * 省钱攻略充电度数配置
     */
    private String chargeDegree1;

    /**
     * 省钱攻略充电度数配置
     */
    private String chargeDegree2;

    /**
     * 省钱攻略充电度数配置
     */
    private String chargeDegree3;

    /**
     * 省钱攻略充电度数配置
     */
    private String chargeDegree4;

    /**
     * 省钱攻略充电度数配置
     */
    private String chargeDegree5;

    /** 优惠限制 **/
    private String discountLimit;
    /** 会员场站月限定次数 **/
    private Integer limitNum;
    /** 会员场站单次充电最大额度 **/
    private BigDecimal limitAmt;

    public String getDiscountLimit() {
        return discountLimit;
    }

    public void setDiscountLimit(String discountLimit) {
        this.discountLimit = discountLimit;
    }

    public Integer getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(Integer limitNum) {
        this.limitNum = limitNum;
    }

    public BigDecimal getLimitAmt() {
        return limitAmt;
    }

    public void setLimitAmt(BigDecimal limitAmt) {
        this.limitAmt = limitAmt;
    }

    public String getIsExchangeExplain() {
        return isExchangeExplain;
    }

    public void setIsExchangeExplain(String isExchangeExplain) {
        this.isExchangeExplain = isExchangeExplain;
    }

    public String getIsDoubleExplain() {
        return isDoubleExplain;
    }

    public void setIsDoubleExplain(String isDoubleExplain) {
        this.isDoubleExplain = isDoubleExplain;
    }

    public String getIsExclusiveCustomerExplain() {
        return isExclusiveCustomerExplain;
    }

    public void setIsExclusiveCustomerExplain(String isExclusiveCustomerExplain) {
        this.isExclusiveCustomerExplain = isExclusiveCustomerExplain;
    }

    public String getIsUpgradePackageExplain() {
        return isUpgradePackageExplain;
    }

    public void setIsUpgradePackageExplain(String isUpgradePackageExplain) {
        this.isUpgradePackageExplain = isUpgradePackageExplain;
    }

    public String getMemberDay() {
        return memberDay;
    }

    public void setMemberDay(String memberDay) {
        this.memberDay = memberDay;
    }

    public String getIsMemberStationExplain() {
        return isMemberStationExplain;
    }

    public void setIsMemberStationExplain(String isMemberStationExplain) {
        this.isMemberStationExplain = isMemberStationExplain;
    }

    public String getIsMemberDayAdditionalDiscountExplain() {
        return isMemberDayAdditionalDiscountExplain;
    }

    public void setIsMemberDayAdditionalDiscountExplain(String isMemberDayAdditionalDiscountExplain) {
        this.isMemberDayAdditionalDiscountExplain = isMemberDayAdditionalDiscountExplain;
    }

    public String getMonthDegreeConfig() {
        return monthDegreeConfig;
    }

    public void setMonthDegreeConfig(String monthDegreeConfig) {
        this.monthDegreeConfig = monthDegreeConfig;
    }

    public String getChargeDegree1() {
        return chargeDegree1;
    }

    public void setChargeDegree1(String chargeDegree1) {
        this.chargeDegree1 = chargeDegree1;
    }

    public String getChargeDegree2() {
        return chargeDegree2;
    }

    public void setChargeDegree2(String chargeDegree2) {
        this.chargeDegree2 = chargeDegree2;
    }

    public String getChargeDegree3() {
        return chargeDegree3;
    }

    public void setChargeDegree3(String chargeDegree3) {
        this.chargeDegree3 = chargeDegree3;
    }

    public String getChargeDegree4() {
        return chargeDegree4;
    }

    public void setChargeDegree4(String chargeDegree4) {
        this.chargeDegree4 = chargeDegree4;
    }

    public String getChargeDegree5() {
        return chargeDegree5;
    }

    public void setChargeDegree5(String chargeDegree5) {
        this.chargeDegree5 = chargeDegree5;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public BigDecimal getMultiple() {
        return multiple;
    }

    public void setMultiple(BigDecimal multiple) {
        this.multiple = multiple;
    }

    public String getExclusiveOffer() {
        return exclusiveOffer;
    }

    public void setExclusiveOffer(String exclusiveOffer) {
        this.exclusiveOffer = exclusiveOffer;
    }

    public Integer getUpgradePackage() {
        return upgradePackage;
    }

    public void setUpgradePackage(Integer upgradePackage) {
        this.upgradePackage = upgradePackage;
    }

    public BigDecimal getMemberStation() {
        return memberStation;
    }

    public void setMemberStation(BigDecimal memberStation) {
        this.memberStation = memberStation;
    }

    public String getWelcomeGift() {
        return welcomeGift;
    }

    public void setWelcomeGift(String welcomeGift) {
        this.welcomeGift = welcomeGift;
    }

    public BigDecimal getMemberDayAdditionalDiscount() {
        return memberDayAdditionalDiscount;
    }

    public void setMemberDayAdditionalDiscount(BigDecimal memberDayAdditionalDiscount) {
        this.memberDayAdditionalDiscount = memberDayAdditionalDiscount;
    }

    public String getVipTypeName() {
        return vipTypeName;
    }

    public void setVipTypeName(String vipTypeName) {
        this.vipTypeName = vipTypeName;
    }

    public String getVipLevelName() {
        return vipLevelName;
    }

    public void setVipLevelName(String vipLevelName) {
        this.vipLevelName = vipLevelName;
    }

    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getVipType() {
        return vipType;
    }

    public void setVipType(String vipType) {
        this.vipType = vipType;
    }

    public String getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(String vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getIsExchange() {
        return isExchange;
    }

    public void setIsExchange(String isExchange) {
        this.isExchange = isExchange;
    }

    public String getIsDouble() {
        return isDouble;
    }

    public void setIsDouble(String isDouble) {
        this.isDouble = isDouble;
    }

    public String getIsExclusiveOffer() {
        return isExclusiveOffer;
    }

    public void setIsExclusiveOffer(String isExclusiveOffer) {
        this.isExclusiveOffer = isExclusiveOffer;
    }

    public String getIsExclusiveCustomer() {
        return isExclusiveCustomer;
    }

    public void setIsExclusiveCustomer(String isExclusiveCustomer) {
        this.isExclusiveCustomer = isExclusiveCustomer;
    }

    public String getIsUpgradePackage() {
        return isUpgradePackage;
    }

    public void setIsUpgradePackage(String isUpgradePackage) {
        this.isUpgradePackage = isUpgradePackage;
    }

    public String getIsMemberStation() {
        return isMemberStation;
    }

    public void setIsMemberStation(String isMemberStation) {
        this.isMemberStation = isMemberStation;
    }

    public String getIsWelcomeGift() {
        return isWelcomeGift;
    }

    public void setIsWelcomeGift(String isWelcomeGift) {
        this.isWelcomeGift = isWelcomeGift;
    }

    public String getIsMemberDayAdditionalDiscount() {
        return isMemberDayAdditionalDiscount;
    }

    public void setIsMemberDayAdditionalDiscount(String isMemberDayAdditionalDiscount) {
        this.isMemberDayAdditionalDiscount = isMemberDayAdditionalDiscount;
    }

    public Integer getBenefitsCount() {
        return benefitsCount;
    }

    public void setBenefitsCount(Integer benefitsCount) {
        this.benefitsCount = benefitsCount;
    }

    public List<VipCouponBo> getExclusiveOfferList() {
        return exclusiveOfferList;
    }

    public void setExclusiveOfferList(List<VipCouponBo> exclusiveOfferList) {
        this.exclusiveOfferList = exclusiveOfferList;
    }

    public List<VipCouponBo> getWelcomeGiftList() {
        return welcomeGiftList;
    }

    public void setWelcomeGiftList(List<VipCouponBo> welcomeGiftList) {
        this.welcomeGiftList = welcomeGiftList;
    }

}
