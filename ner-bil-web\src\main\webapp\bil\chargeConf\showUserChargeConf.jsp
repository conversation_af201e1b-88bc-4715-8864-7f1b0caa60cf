<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费基本信息" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<ls:body>
	<ls:form id="chargeForm" name="chargeForm">
		<ls:title text="资费基本信息"></ls:title>
			<ls:text type="hidden" name="systemId" property="systemId"/>
			<ls:text type="hidden" name="custId" property="custId"/>
			<ls:text type="hidden" name="stationId" property="stationId"></ls:text>
			<ls:text type="hidden" name="attachItemNos" property="attachItemNos"/>
			<ls:text type="hidden" name="attachItemPrices" property="attachItemPrices"/>
			<ls:text type="hidden" name="chargePeriodsStr"/>
			<table class="tab_search">
				<tr>
					<td width="130px;;"><ls:label text="充电计费编号" ref="chcNo" /></td>
					<td colspan="2"><ls:text name="chcNo" property="chcNo" required="true" readOnly="true"/></td>
					<td><ls:label text="企业名称" ref="custName"/></td>
					<td colspan="2">
                    	<ls:text  name="custName" property="custName"  required="true" readOnly="true" onchanged="changeStationName"></ls:text>
                	</td>
                </tr>
                <tr>
					<td><ls:label text="充电计费名称" ref="chcName" /></td>
					<td colspan="2"><ls:text name="chcName" property="chcName" required="true" maxlength="50" enabled="false"/></td>
					<td><ls:label text="生效日期" ref="eftDate" /></td>
					<td colspan="2"><ls:date name="eftDate" property="eftDate" required="true" format="yyyy-MM-dd HH:mm" readOnly="true"/></td>
				</tr>
				<tr>
					<td><ls:label text="终端显示的充电服务费" ref="chcRemark"/></td>
					<td colspan="5"><ls:text  name="chcRemark" property="chcRemark" type="textarea" required="true" maxlength="100" enabled="false"></ls:text></td>
				</tr>
			</table>
		<ls:title text="充电计费规则"></ls:title>
			<table class="tab_search">
				<tr>
					<td width="130px;;"><ls:label text="费控模式" ref="billCtlMode" /></td>
					<td colspan="2">
						<ls:checklist type="radio" name="billCtlMode" property="billCtlMode" onchanged="changeBillCtlMode" required="true" readOnly="true">
							<%--<ls:checkitems property="billCtlModeList" scope="request" text="text" value="value"></ls:checkitems>--%>
							<ls:checkitem value="2" text="远程"></ls:checkitem>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeShowOne" width="100px;"><ls:label text="桩推送数据类型" ref="gatherDataType" /></td>
					<td id="gatherDataTypeShowTwo" colspan="2">
						<ls:checklist type="radio" name="gatherDataType" property="gatherDataType" required="true" onchanged="changePriceTrShow"  readOnly="true">
							<ls:checkitem value="1" text="计量表示数"></ls:checkitem>
							<ls:checkitem value="2" text="电量"></ls:checkitem>
						</ls:checklist>
					</td>
					<td id="gatherDataTypeHide" colspan="3" style="display: none;"></td>
				</tr>
				<tr>
					<td width="100px;"><ls:label text="计费模式" ref="chargeMode" /></td>
					<td colspan="5">
						<ls:checklist type="radio" name="chargeMode" property="chargeMode" required="true" readOnly="true" onchanged="changePriceTrShow">
							<ls:checkitems property="chcBillingChargeModeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
				</tr>
				<tr id="priceTr">
					<td><ls:label text="计费单价" ref="chargePrice" /></td>
					<td colspan="4"><ls:text name="chargePrice" property="chargePrice" enabled="false" required="true" type="number" maxlength="10"/></td>
					<td>元/kWh</td>
				</tr>
				<tr id="timeTr">
					<td><ls:label text="分时时段设置"/></td>
					<td colspan="5">
						<ls:grid url="" name="timeShareGrid" primaryKey="sn" cellEdit="true" treeGrid="true" expandColumn="value"  expand="true"  treeGridModel="adjacency" height="100px;" width="100%">
							<ls:column caption="排序号" name="sn" hidden="true"/>
							<ls:column caption="开始时间" name="beginTime" editableEdit="false" readOnly="true"/>
							<ls:column caption="结束时间" name="endTime"  editableEdit="false" readOnly="true"/>
							<ls:column caption="时段标识" name="timeFlag"  editableEdit="false" readOnly="true">
								<ls:selectEditor property="timeFlagList" name="timeFlag" valueMember="codeValue" required="true" displayMember="codeName">
								</ls:selectEditor>
							</ls:column>
							<ls:column caption="计费单价（元）" name="price" editableEdit="false" readOnly="true">
							</ls:column>
						</ls:grid>
					</td>
				</tr>
			</table>
			<ls:title text="服务项目"></ls:title>
			<table align="center" class="tab_search">
				<tr>
					<td><ls:grid url="" name="serviceGrid" primaryKey="itemNo"   height="100px;" width="99%">
							<ls:column caption="费用项no" name="itemNo"  hidden="true" />
							<ls:column caption="费用项名称" name="itemName"  readOnly="true" />
							<ls:column caption="费用单价（元）" name="servicePrice"/>
							<ls:column caption="费用项单位" name="itemUnitName" readOnly="true"/>
							<ls:column caption="是否必购" name="buyTypeName"  hidden="true"  />
						</ls:grid></td>
				</tr>
			</table>
			<ls:title text="站点列表"></ls:title>
			<table class="tab_search">
				<tr>
					<td width="100px"><ls:label text="站点使用范围"/></td>
					<td colspan="5">
						<ls:checklist type="radio" name="rangeFlag" property="rangeFlag" enabled="false">
							<ls:checkitems property="stationRangeList" scope="request" text="text" value="value"></ls:checkitems>
						</ls:checklist>
					</td>
				</tr>
				<tr id="stationList">
					<td><ls:label text="站点列表"/></td>
					<td colspan="5">
						<ls:grid url="" name="stationGrid" primaryKey="stationId" showCheckBox="true" singleSelect="false" height="100px;" width="100%">
							<ls:column caption="站点编号" name="stationId"  readOnly="true" hidden="true"/>
							<ls:column caption="站点编号" name="stationNo"  readOnly="true"/>
							<ls:column caption="站点名称" name="stationName" readOnly="true" align="left"/>
						</ls:grid>
					</td>
				</tr>
			</table>
	</ls:form>
	<ls:script>
	init();
	function init(){
		if(systemId.getValue() == null || systemId.getValue() == ''){
			var item = {
				sn:1,
				beginTime:"00:00",
				endTime:"24:00"
			};
			timeShareGrid.appendItem(item);
			chargeMode.setValue('0201');//标准
		}else{
			var mode = chargeMode.getValue();
			if(mode != '0201'){
				timeShareGridSearch();
			}
			serviceGridSearch();
		}
		changePriceTrShow();
		changeBillCtlMode();
		changeStationRange();
		$("#pager_id_timeShareGrid").remove();
		$("#pager_id_serviceGrid").remove();
		$("#pager_id_stationGrid").remove();
	}
	//========================================费控模式===========================================================
	function changeBillCtlMode(){
    	var value = billCtlMode.getValue();
    	if(value == '1'){//本地
    		$('#gatherDataTypeHide').show();
    		$('#gatherDataTypeShowOne').hide();
    		$('#gatherDataTypeShowTwo').hide();
    	}else{//远程
    		$('#gatherDataTypeHide').hide();
    		$('#gatherDataTypeShowOne').show();
    		$('#gatherDataTypeShowTwo').show();
    	}
	}
	//========================================分时时段设置=========================================================
	function timeShareGridSearch(){
		var params = {
			chcNo:chcNo.getValue()
		};
		LS.ajax("~/billing/chargebillingconf/queryChargePeriods",params,function(data){
			timeShareGrid.removeAllItems();
			if(data != null){
				for(var i = 0 ; i < data.length ; i++){
					timeShareGrid.addRow(data[i],"last");
				}
			}
		});
	}
	
	function doAddTimeShareGrid(){
		var data = timeShareGrid.getItems();
		
		var item = {
			sn:data[data.length-1].sn+1,
			endTime:"24:00"
		};
		timeShareGrid.appendItem(item);
		timeShareGrid.setCell(data[data.length-1].sn, "endTime", "");
	}
	
	function endTimeValidate(value,eValidator){
		var returnValid = {
			isValid : true,
			message : ""
		}
		if(LS.isEmpty(value)){
			returnValid.message = "必选填字段";
			returnValid.isValid = false;
			return returnValid;
		}
		if(!/((([0-1]\d)|(2[0-3])):[0-5]\d)|24:00/.test(value)){
			returnValid.message = "请输入正确的时间格式，如:03:00";
			returnValid.isValid = false;
			return returnValid;
		}
		var data = timeShareGrid.getItems();
		var j = 0;
		for(var i = 0;i < data.length;i++){
			if(data[i].sn == eValidator.rowid ){
				j = i;
				break;
			}
		}
		if(j+1 == data.length && value !='24:00'){
				returnValid.message = "分时时段最终的结束时间必须为24:00,不能更改";
				returnValid.isValid = false;
				return returnValid;
		}
		if(j!=0){
			var preEndTime = timeShareGrid.getCell(data[j-1].sn, "endTime");
			if(!LS.isEmpty(preEndTime) && !tranTime(preEndTime,value)){
					returnValid.message = "当前的结束时间必须大于开始时间";
					returnValid.isValid = false;
					return returnValid;
			}
		}
		if(j+1!=data.length){
			var nextEndTime = timeShareGrid.getCell(data[j+1].sn, "endTime");
			if(!LS.isEmpty(nextEndTime) && !tranTime(value,nextEndTime)){
					returnValid.message = "当前的结束时间必须小于下一个时间段的结束时间";
					returnValid.isValid = false;
					return returnValid;
			}
		}
		if(returnValid.isValid && j+1 != data.length){
			timeShareGrid.setCell(data[i+1].sn, "beginTime", value);
		}
		return returnValid;
	}
	
	function tranTime(minTime,maxTime){
		var min = minTime.split(":");
		var max = maxTime.split(":");
		var returnValue = true;
		if(Number(max[0]) < Number(min[0]))
			returnValue = false;
		if(Number(max[0]) == Number(min[0]) && Number(max[1]) <= Number(min[1]))
			returnValue = false;
		return returnValue;
	}
	
	function doDelTimeShareGrid(){
	   var item = timeShareGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要删除的分时时段记录");
    		return;
	   }
	   var items = timeShareGrid.getItems();
	   if(items.length == 1){
    		LS.message("error","至少需要有一条记录");
    		return;
	   }
	   var newItems = [];
	   for(var i=0;i < items.length;i++){
	   		if(item.sn == items[i].sn){
	   			if(i==0){
	   				items[1].beginTime = '00:00';
	   			}else if(i==items.length-1){
	   				newItems[i-1].endTime = '24:00';
	   			}else{
	   				items[i+1].beginTime = items[i-1].endTime;
	   			}
	   		}else{
	   			newItems.push(items[i]);
	   		}
	   }
	   timeShareGrid.removeAllItems();
	   timeShareGrid.appendItem(newItems);
	}
	
	//========================================服务项目=========================================================
	function serviceGridSearch(){
		if(!LS.isEmpty(attachItemNos.getValue())){
			var params = {
				itemNo:attachItemNos.getValue(),
				price:attachItemPrices.getValue()
			};
			serviceGrid.query('~/billing/chargeServiceItem/querySerItemsByNo',params,function(){});
		}
	}
	
	function doAddSerGrid(){
		var items = serviceGrid.getItems();
		var nos = "";
		for(var i =0;i < items.length;i++){
			if(i!=items.length-1){
				nos += items[i].itemNo+",";
			}else
				nos += items[i].itemNo;
		}
		LS.dialog("~/billing/chargeServiceItem/select?itemStatus=1&unUseItemNos="+nos,"选择服务项目",1000, 400,true, null);
	}
	
	window.setChargeSer = setChargeSer;
	function setChargeSer(item){
		serviceGrid.appendItem(item);
	}
	
	function doDelSerGrid(){
		var item = serviceGrid.getSelectedItem();
	   if(LS.isEmpty(item)){
    		LS.message("error","请选择要删除的服务项目");
    		return;
	   }
	   serviceGrid.removeItem(item);
	}
	
	//====================================其他=============================================================
    function changeStationName(){
    	if(stationName.getValue()==null || stationName.getValue()==''){
    		stationId.setValue();
    	}
    }
    
    window.setPile = setPile;
    function setPile(item) {
       stationId.setValue(item.stationId);  
       stationName.setValue(item.stationName);
    }
    
    function changePriceTrShow(){
    	var mode = chargeMode.getValue();
    	if(mode == '0201'){//标准、显示priceTr
    		$('#priceTr').show();
    		$('#timeTr').hide();
    	}else{//隐藏priceTr
    		$('#priceTr').hide();
    		$('#timeTr').show();
    	}
    }

	function changeStationRange(){
		var sRange = rangeFlag.getValue();
		if(sRange == "1"){
			stationGrid.removeAllItems();
			$("#stationList").hide();
		}else{
			$("#stationList").show();
		}
	}

	stationSeach();
	function stationSeach(){
		var sRange = rangeFlag.getValue();
		if(sRange == "0"){
			var params = {
				chcNo:chcNo.getValue(),
				custId:custId.getValue()
			};
			LS.ajax("~/billing/chargeUserbilconf/queryBillingStation",params,function(data){
				stationGrid.removeAllItems();
				if(data != null){
					for(var i = 0 ; i < data.length ; i++){
						stationGrid.addRow(data[i],"last");
					}
				}
			});
		}
	}
    </ls:script>
</ls:body>
</html>