package com.ls.ner.billing.gift.dao;

import com.ls.ner.billing.gift.bo.RechargeCardBo;
import com.ls.ner.billing.gift.bo.RechargeCardDetail;
import com.ls.ner.billing.gift.bo.RechargeCardOrder;
import com.ls.ner.billing.gift.condition.RechargeCardCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.dao
 * @ClassName: IRechargeCardDao
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/4/27 15:03
 * @Version: 1.0
 */
public interface IRechargeCardDao {

    List<RechargeCardBo> listRechargeCard(RechargeCardCondition rechargeCardCondition);

    int countRechargeCard(RechargeCardCondition rechargeCardCondition);

    RechargeCardBo queryRechargeCard(RechargeCardBo rechargeCardBo);

    void addRechargeCard(RechargeCardBo rechargeCardBo);

    void batchAddRechargeCardDetail(@Param("list")List<RechargeCardDetail> list);

    void batchAddRechargeCardDetailIntegral(@Param("list")List<RechargeCardDetail> list);
    void updateRechargeCard(RechargeCardBo rechargeCardBo);

    void updateCardDetailStatus(RechargeCardDetail rechargeCardDetail);

    void updateCardDetailCustId(RechargeCardDetail rechargeCardDetail);

    void updateCardDetailAmt(RechargeCardDetail rechargeCardDetail);

    List<RechargeCardDetail> queryDetails(RechargeCardCondition rechargeCardCondition);

    /**
     * 实际余额正序
     * @param rechargeCardCondition
     * @return
     */
    List<RechargeCardDetail> queryDetailsOrderByActualBalance(RechargeCardCondition rechargeCardCondition);
    int queryDetailsCount(RechargeCardCondition rechargeCardCondition);

    List<RechargeCardOrder> qryCardOrderDetail(RechargeCardCondition rechargeCardCondition);

    int queryCardOrderDetailCount(RechargeCardCondition rechargeCardCondition);

    RechargeCardDetail queryCardDetailsByOne(RechargeCardDetail rechargeCardDetail);


    RechargeCardDetail queryRechargeCardCodeByOne(RechargeCardDetail rechargeCardDetail);

    List<RechargeCardDetail> myRechargeCardInfos(RechargeCardDetail rechargeCardDetail);

    List<Map<String, String>> getRechargeCardList();

    void insertRechargeCardOrder(RechargeCardOrder rechargeCardOrder);

    List<RechargeCardOrder> queryCardOrderGroupByCardId(RechargeCardOrder rechargeCardOrder);

    List<RechargeCardOrder> queryCardOrderGroupByCardDetailId(RechargeCardOrder rechargeCardOrder);

    List<Map<String, Object>> queryCardOrderByOrderNo(Map<String, Object> map);

    List<String> cardOrderNoList(@Param("cardId") String cardId, @Param("cardDetailId") String cardDetailId);

    List<Map<String,Object>> queryCardInfosByChargeOrderList(Map<String, Object> map);

    List<String> getOrderListByCardNo(Map<String, Object> map);
}
