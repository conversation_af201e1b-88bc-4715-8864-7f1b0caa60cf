package com.ls.ner.billing.xpcharge.service;

import com.ls.ner.billing.charge.bo.ChcbillPileSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;

import java.util.List;
import java.util.Map;

/**
 * 充电计费下发
 * <AUTHOR>
 */
public interface IXpPileSendService {

    /**
     * @description 查询最新计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheNewChc(ChcbillSendQueryCondition m);

    /**
     * @description 查询当前计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheCurrentChc(Map m);

    /**
     * @description 充电计费配置下发到充电站
     * @return String 下发完成状态  02下发失败   03部分成功  04下发成功
     * */
    public void issuredStation(String chcNo, String pileNo, String stationId) throws Exception;

    /**
     * 查询桩是否有单独计费
     * @param sercher
     * @return
     */
    int queryPileBillCount(Map sercher);

    /**
     * 桩计费下发
     * @param chcNo
     * @param pileNo
     * @param stationId
     */
    void issuredPile(String chcNo, String pileNo, String stationId)throws Exception;

    int queryTheNewChcNum(ChcbillSendQueryCondition condition);

    List<Map<String,Object>> queryChcbillPileSendsByNo(ChcbillSendQueryCondition condition);

    Map<String,Object> queryChcbillPileSendByNo(Map<String,Object> inMap);

    /**
     * @description 查询桩计费信息
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryPileBillInfo(ChcbillSendQueryCondition m);
    public int queryPileBillInfoCount(ChcbillSendQueryCondition m);

    List<Map<String, Object>> queryStationBillConfs();

    List<Map<String, Object>> queryPileBillConfs();

}
