package com.ls.ner.billing.mktact.condition;

import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * <AUTHOR>
 * @description 新版限时折扣表单提交
 * @create 2019/5/28 22:35
 *
 */
public class MarketActFormCondition extends QueryCondition {
	private String actId;
	private String actName;
	private String isImmediate;
	private String effTime;
	private String expTime;
	private String custType;
	private String release;
	private String actMarks;
	private String buildType;
	private String buildId;
	private String city;
	private String cityCodes;
	private String stationId;
	private String stationIds;
	private String info;
	private String youhui;
	private String coberPic;
	private String presentSectionDets;//预存赠送 段落明细
	private String presentSection;
	private String buildName;
	private String actType;
	private String actSubType;
	// 宁德新增字段控制本次活动充值金是否允许提现， 0 否，1 是
	private String allowWithdraw;


	public String getActId() {
		return actId;
	}

	public void setActId(String actId) {
		this.actId = actId;
	}

	public String getActName() {
		return actName;
	}

	public void setActName(String actName) {
		this.actName = actName;
	}

	public String getIsImmediate() {
		return isImmediate;
	}

	public void setIsImmediate(String isImmediate) {
		this.isImmediate = isImmediate;
	}

	public String getEffTime() {
		return effTime;
	}

	public void setEffTime(String effTime) {
		this.effTime = effTime;
	}

	public String getExpTime() {
		return expTime;
	}

	public void setExpTime(String expTime) {
		this.expTime = expTime;
	}

	public String getCustType() {
		return custType;
	}

	public void setCustType(String custType) {
		this.custType = custType;
	}

	public String getRelease() {
		return release;
	}

	public void setRelease(String release) {
		this.release = release;
	}

	public String getActMarks() {
		return actMarks;
	}

	public void setActMarks(String actMarks) {
		this.actMarks = actMarks;
	}

	public String getBuildType() {
		return buildType;
	}

	public void setBuildType(String buildType) {
		this.buildType = buildType;
	}

	public String getBuildId() {
		return buildId;
	}

	public void setBuildId(String buildId) {
		this.buildId = buildId;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCityCodes() {
		return cityCodes;
	}

	public void setCityCodes(String cityCodes) {
		this.cityCodes = cityCodes;
	}

	public String getStationId() {
		return stationId;
	}

	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getStationIds() {
		return stationIds;
	}

	public void setStationIds(String stationIds) {
		this.stationIds = stationIds;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public String getYouhui() {
		return youhui;
	}

	public void setYouhui(String youhui) {
		this.youhui = youhui;
	}

	public String getCoberPic() {
		return coberPic;
	}

	public void setCoberPic(String coberPic) {
		this.coberPic = coberPic;
	}

	public String getBuildName() {
		return buildName;
	}

	public void setBuildName(String buildName) {
		this.buildName = buildName;
	}

	public String getActType() {
		return actType;
	}

	public void setActType(String actType) {
		this.actType = actType;
	}

	public String getPresentSectionDets() {
		return presentSectionDets;
	}

	public void setPresentSectionDets(String presentSectionDets) {
		this.presentSectionDets = presentSectionDets;
	}

	public String getPresentSection() {
		return presentSection;
	}

	public void setPresentSection(String presentSection) {
		this.presentSection = presentSection;
	}

	public String getActSubType() {
		return actSubType;
	}

	public void setActSubType(String actSubType) {
		this.actSubType = actSubType;
	}

	public String getAllowWithdraw() {
		return allowWithdraw;
	}

	public void setAllowWithdraw(String allowWithdraw) {
		this.allowWithdraw = allowWithdraw;
	}
}
