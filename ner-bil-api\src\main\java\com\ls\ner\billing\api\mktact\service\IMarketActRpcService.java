package com.ls.ner.billing.api.mktact.service;

import java.util.List;
import java.util.Map;

public interface IMarketActRpcService {

	/**
	 * 查询‘预存赠送’活动
	 * @param map {
	 * 		custType用户分类
	 * 		custLvl用户等级
	 * 		channel渠道:01APP 02现场03微信04支付宝
	 * }
	 * @return actList{
	 * 	actId活动ID,actLbl活动标签,
	 * 	actName活动名称,actMarks活动描述,
	 * 	effTime生效时间,expTime失效时间,
	 *  presentRuleList[
	 * 		presentBalanceType赠送余额类型,
	 * 		presentRuleDesc赠送规则描述
	 *  ]}
	 * <AUTHOR>
	 */
	public List<Map<String,Object>> qryMarketActives(Map<String, Object> map);

	/**
	 * 执行‘预存赠送’活动
	 * @param map {
	 * 	actId活动ID
	 * 	custType用户分类
	 * 	custLvl用户等级
	 * 	channel渠道:01APP 02现场03微信04支付宝
	 * 	payBalanceValue预存金额
	 * 	payBalanceTime预存时间
	 * }
	 * @return
	 * <AUTHOR>
	 */
	public Map<String, Object> doPresentSection(Map<String, Object> map);

	/**
	 * 描述 RPC05-11-05订单可参加的活动（还车后）
	 * 针对活动为：02	限时打折；03	满减满送；04	首单免
	 * @param: [map]
	 * @return: java.util.Map<java.lang.String,java.lang.Object>
	 * 创建人:biaoxiangd
	 * 创建时间:2017/7/10 3:15
	 */
	Map<String, Object> actByOrder(Map<String, Object> map) throws Exception;

	/**
	 * @Description: 3.5.5	RPC05-11-05订单可参加的活动（还车后）
	 * @method: getOrderAvailAct
	 * @param map
	 * @return: java.util.Map<java.lang.String,java.lang.Object>
	 * @Author: cailianL
	 * @Time: 2017/7/12 11:36
	 */
	public Map<String, Object> getOrderAvailAct(Map<String, Object> map) throws Exception;

	/**
	 * @param map 暂无
	 * @return actList{
	 * 	dctGiveType 赠送类别 ,
	 *  cpns 赠送的优惠券列表[
	 * 		cpnId 优惠券ID,
	 * 		prodBusiType 产品业务类，
	 * 		limGetNum 每人限领，
	 * 		eftDate 生效日期，
	 * 		invDate 失效日期，
	 * 		putTime 发放时间
	 *  ]}
	 * @description RPC05-12-03 获取注册送活动
	 * @author: biaoxiangd
	 * @create:2017-09-27
	 */
	public Map<String,Object> getRegisterSendAct(Map<String, Object> map) throws Exception;
	/**
	 * @param map
	 * @description 获取各种进行中的活动详情---统一接口
	 * <AUTHOR> @create 2018-05-16 09:57:09
	 */
	List<Map<String, Object>> getActiveActInfo(Map<String, Object> map) throws  Exception;
	/**
	 * @param map
	 * @description 记录参与活动（预存赠送记录参与活动次数）
	 * <AUTHOR>
	 * @create 2018-07-05 15:32:29
	 */
	void joinActRecord(Map<String, Object> map);

	/**
	 * @param inMap
	 * @description 满减计算，减免金额
	 * <AUTHOR>
	 * @create 2018-09-25 10:40:45
	 */
	Map<String, Object> fullSaleCalc(Map<String, Object> inMap);

	/**
	 * @param inMap
	 * @description 查询活动
	 * <AUTHOR>
	 * @create 2018-12-17 10:28:07
	 */
	List<Map> qryMktActList(Map<String, Object> inMap);

	/**
	 * @param inMap
	 * @description 查询充值活动
	 * <AUTHOR>
	 * @create 2018-12-17 16:53:05
	 */
	List<Map> qryRecMktActList(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 判断站点是否有活动 没有具体站点传new HashMap()<>;
	 * @create 2019/5/28 14:41
	 *
	 * @return
	 */
	public List<Map> queryChargingStationsAct(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @description 查询所有以站点的活动适用范围
	 * @create 2019/5/27 19:16
	 *
	 */
	Map qryAllMktActforStation(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description  查询优惠内容（限时折扣）
	 * @create 2019/5/28 15:00
	 *
	 */
	List<Map> qryActContDct(Map<String, Object> inMap);

	/**
	 * <AUTHOR>
	 * @description 查询活动和资讯信息
	 * @create 2019/5/28 17:20
	 *
	 */
	public List<Map> getActAndInfo(Map<String, Object> map) throws Exception;

	/**
	 * @param
	 * @description  根据订单查询活动参与
	 * <AUTHOR>
	 * @create 2019/6/4 18:25
	 */
	public List<Map> qeyActJoin(Map<String, Object> inMap);


	/**
	 * @param actId
	 * @description 根据Id获取活动信息
	 * <AUTHOR>
	 * @create 2020-11-12 16:55
	 */
	Map<String,Object> getActivityInfoById(String actId);

	/**
	 * @Description: 关闭活动
	 * @param: actId: 活动id
	 * <AUTHOR>
	 * @date 2023/5/12 15:10
	 */
	void stopAct(Long actId);

	Map<String, Object> queryActInProgress(Map<String, Object> map);

	void aysncCustIntegral(Map<String, Object> map);
}
