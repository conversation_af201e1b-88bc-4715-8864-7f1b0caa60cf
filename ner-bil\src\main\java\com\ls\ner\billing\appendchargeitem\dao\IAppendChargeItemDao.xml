<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.appendchargeitem.dao.IAppendChargeItemDao">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.AppendChargeItemBo">

    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType" />
    <result column="ITEM_UNIT" jdbcType="VARCHAR" property="itemUnit" />
    <result column="SN" jdbcType="INTEGER" property="sn" />
  </resultMap>
 
  <sql id="Base_Column_List">
    SYSTEM_ID, DATA_OPER_TIME, DATA_OPER_TYPE, ITEM_NO, ITEM_NAME, ITEM_TYPE,ITEM_UNIT, SN
  </sql>
  
  <sql id="Base_Where">
  	where ITEM_TYPE = #{itemType,jdbcType=VARCHAR} and ITEM_NO = #{itemNo,jdbcType=VARCHAR}
  </sql>


  <select id="selectByItemNoAndItemType" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo" resultMap="BaseResultMap">
   
    select 
    <include refid="Base_Column_List" />
    from e_append_charge_item
  	<include refid="Base_Where" />
  </select>  
  
  <delete id="deleteByItemNoAndItemType" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
  
    delete from e_append_charge_item
   
   <include refid="Base_Where" />
  </delete>

  


  
  <update id="updateByItemNoAndItemType" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Mar 05 16:36:49 CST 2016.
    -->
    update e_append_charge_item
    set DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
     
      ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      ITEM_UNIT = #{itemUnit,jdbcType=VARCHAR},
      SN = #{sn,jdbcType=INTEGER},
      REMARKS = #{remarks,jdbcType=INTEGER},
      BUY_TYPE = #{buyType,jdbcType=INTEGER}
   <include refid="Base_Where" />
  </update>
</mapper>