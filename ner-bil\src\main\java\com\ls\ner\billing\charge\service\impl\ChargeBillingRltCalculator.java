package com.ls.ner.billing.charge.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeBillingRltPeriodsBo;

/**
 * 充电计费方法类---只做算钱
 * <AUTHOR>
 */
@Component
public class ChargeBillingRltCalculator{
	private static final Logger LOGGER = LoggerFactory.getLogger(ChargeBillingRltCalculator.class);
	@ServiceAutowired(serviceTypes= ServiceType.RPC)
	private ISysParamService sysParamService;
	@ServiceAutowired("servicePriceService")
	private ITimeShareServicePriceService servicePriceService;
	/**
	 * @description 充电计费---唯一入口
	 * @param cbrBo(
	 * 		chargeMode  计费模式 01标准 02分时
	 * 		tPq			 总电量。如果是分时，则为分时电量总和
	 * 		tTime       充电时间，单位：分钟
	 * 		attachItemPrices 服务项目单价，逗号隔开
	 * 		attachItemUnits	服务项目单位，逗号隔开
	 * 		chargePrice	计费单价 (计费模式01标准，必须有)
	 * 		rltPeriods	充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	 * )
	 * @return cbrBo(
	 * 		tAmt		充电金额。如果是分时，则为分时金额总和
	 * 		itemTAmt	服务项目总金额
	 * 		amt			总金额
	 * 		attachItemNums	服务项目数量，逗号隔开
	 * 		attachItemAmts	服务项目金额，逗号隔开
	 * )
	 * */
	public void calculator(ChargeBillingRltBo cbrBo){
		//1、计算充电金额
		tAmtCalculator(cbrBo);
		//2、计算费用项
		serItemsCalculator(cbrBo);

		//3、计算总金额
		amtCalculator(cbrBo);
	}


	public void calculatorNew(ChargeBillingRltBo cbrBo,Map<String, Object> inMap){
		//1、计算充电金额
		tAmtCalculator(cbrBo);
		//2、计算费用项
		serItemsCalculatorNew(cbrBo,inMap);

		//3、计算总金额
		amtCalculator(cbrBo);
	}

	/**
	 * @description 计算总充电金额
	 *   ---不分时，tAmt = tPq * chargePrice
	 * @param 'tPq' 总电量
	 * @param 'chargePrice' 单价
	 * @return 总充电金额
	 * */
	private void tAmtCalculator(ChargeBillingRltBo cbrBo){
		//1、标准计费方式
		if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){
			cbrBo.settAmt(BigDecimalToString(tAmtCalculator(cbrBo.gettPq(), cbrBo.getChargePrice())));
			//2、分时计费方式
		}else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){
			cbrBo.settAmt(BigDecimalToString(tAmtCalculator(cbrBo.getRltPeriods())));
		}else
			throw new RuntimeException("没有该计费方式");
	}

	/**
	 * @description 计算不分时总充电金额
	 *   ---不分时，tAmt = tPq * chargePrice
	 * @param tPq 总电量
	 * @param chargePrice 单价
	 * @return 总充电金额
	 * */
	private BigDecimal tAmtCalculator(String tPq,String chargePrice){
		return round(multiply(strToBigDecimal(tPq, "不分时金额计算时，总电量获取出错"), strToBigDecimal(chargePrice, "不分时金额计算时，计费单价获取出错")), 2);
	}

	/**
	 * @description 计算分时总充电金额
	 *   分时，取本地分时电价,取接口参数分时电量
	 *   tAmt =（电价 * 电量）+（电价 * 电量）。。。。
	 * @param rltPeriods 分时时段数据（pq时段电量，price计费单价）
	 * @return 总充电金额         分时时段充电金额
	 * 备注：分时算金额时，按找平算，即最后一个分时金额   = 总金额  - 其余分时金额
	 * */
	private BigDecimal tAmtCalculator(List<ChargeBillingRltPeriodsBo> rltPeriods){
		BigDecimal amt = strToBigDecimal("0.00", "");//分时总金额
		BigDecimal amtTmp = strToBigDecimal("0.00", "");
		if(rltPeriods == null || rltPeriods.size() == 0){
			LOGGER.debug("分时时段无数据");
			return amt;
		}
		//20220607统一口径，总金额=各个费用之和
		for(int i = 0;i<rltPeriods.size();i++){
			ChargeBillingRltPeriodsBo crpBo = rltPeriods.get(i);
			BigDecimal timeAmt = multiply(strToBigDecimal(crpBo.getPq(), "分时金额计算时，时段电量获取出错"), strToBigDecimal(crpBo.getPrice(), "分时金额计算时，计费单价获取出错"));
			BigDecimal roundTimeAmt = round(timeAmt, 2);
			amt = addition(amt, roundTimeAmt);
			crpBo.setAmt(BigDecimalToString(roundTimeAmt));
		}
		/*for(int i = 0;i<rltPeriods.size();i++){
			ChargeBillingRltPeriodsBo crpBo = rltPeriods.get(i);
			BigDecimal timeAmt = multiply(strToBigDecimal(crpBo.getPq(), "分时金额计算时，时段电量获取出错"), strToBigDecimal(crpBo.getPrice(), "分时金额计算时，计费单价获取出错"));
			amt = addition(amt, timeAmt);
			//如果是最后一个分时金额，则分时金额 = 总金额 - 其余分时金额
			if(i == rltPeriods.size() -1){
				amt = round(amt, 2);
				crpBo.setAmt(BigDecimalToString(subtract(amt, amtTmp)));
			}else{
				//如果不是最后一个分时金额，则分时金额四舍五入、保留两位小数
				BigDecimal roundTimeAmt = round(timeAmt, 2);
				crpBo.setAmt(BigDecimalToString(roundTimeAmt));
				amtTmp = addition(amtTmp, roundTimeAmt);
			}
		}*/
		return amt;
	}

	/**
	 * @description 计算服务项目总费用
	 *	 根据总电量，计算服务费itemTAmt
	 *  ---如果(attachItemUnit)服务项目单位为次，
	 *      itemTAmt + = attachItemPrice
	 *  ---如果(attachItemUnit)服务项目单位为kma，
	 *	 itemTAmt + = attachItemPrice * tPq
	 *@return itemTAmt服务项目总费用     attachItemNums服务项目数量，逗号隔开     attachItemAmts服务项目金额，逗号隔开
	 * */
	private void serItemsCalculator(ChargeBillingRltBo cbrBo){
		if(StringUtils.isEmpty(cbrBo.getAttachItemPrices()) || StringUtils.isEmpty(cbrBo.getAttachItemUnits())){
			cbrBo.setItemTAmt("0.00");
			LOGGER.debug("无服务费可计算");
			return;
		}
		//1、初始化总服务费、服务项目单价、服务项目单位、总电量
		BigDecimal itemTAmt = strToBigDecimal("0.00", "");//总服务费
		String[] itemPrices = cbrBo.getAttachItemPrices().split(",");
		String[] itemUnits = cbrBo.getAttachItemUnits().split(",");
		String[] itemNos = cbrBo.getAttachItemNos().split(",");
		BigDecimal tPq = strToBigDecimal(cbrBo.gettPq(), "计费服务费时，获取总电量错误");
		StringBuffer attachItemNums = new StringBuffer();//服务项目数量，逗号隔开
		StringBuffer attachItemAmts = new StringBuffer();//服务项目金额，逗号隔开
		//2、计算总服务费
		for(int i = 0; i < itemPrices.length; i ++){
			BigDecimal price = strToBigDecimal(itemPrices[i], "服务费项目单价错误");
			if("1000006000".equals(itemNos[i])) {//补电出车费
				attachItemNums.append("1");
				attachItemAmts.append(BigDecimalToString(price));
				itemTAmt = addition(itemTAmt, price);
			}else{
				//按次数计价,直接累加
				if(ChargeConstant.chcBillingPriceUnit.COUNT.equals(itemUnits[i])){
					attachItemNums.append("1");
					attachItemAmts.append(BigDecimalToString(price));
					itemTAmt = addition(itemTAmt, price);
					//按总电量计价
				}else if(ChargeConstant.chcBillingPriceUnit.KWH.equals(itemUnits[i])){
					BigDecimal oneAmt = serItemCalculator(price,tPq);
					attachItemNums.append(tPq);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
					//按小时计价,不足一小时，按一小时算
				}else if(ChargeConstant.chcBillingPriceUnit.HOUR.equals(itemUnits[i])){
					long tTime = minuteToHour(cbrBo.gettTime(), "60");
					BigDecimal oneAmt = serItemCalculator(price,new BigDecimal(tTime));
					attachItemNums.append(tTime);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
					//按半小时计价，不足半小时，按半小时算
				}else if(ChargeConstant.chcBillingPriceUnit.HALF_HOUR.equals(itemUnits[i])){
					long tTime = minuteToHour(cbrBo.gettTime(), "30");
					BigDecimal oneAmt = serItemCalculator(price,new BigDecimal(tTime));
					attachItemNums.append(tTime);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
				}
			}
			if(i != itemPrices.length -1){
				attachItemNums.append(",");
				attachItemAmts.append(",");
			}
		}
		cbrBo.setAttachItemNums(attachItemNums.toString());
		cbrBo.setAttachItemAmts(attachItemAmts.toString());
		cbrBo.setItemTAmt(BigDecimalToString(round(itemTAmt,2)));
	}


	/**
	 * @description 计算费用项
	 *@return itemTAmt费用项总费用     attachItemNums费用项目数量，逗号隔开     attachItemAmts费用项目金额，逗号隔开
	 * */
	private void serItemsCalculatorNew(ChargeBillingRltBo cbrBo,Map<String, Object> inMap){
		if(StringUtils.isEmpty(cbrBo.getAttachItemPrices()) || StringUtils.isEmpty(cbrBo.getAttachItemUnits())){
			cbrBo.setItemTAmt("0.00");
			LOGGER.debug("费用项可计算");
			return;
		}
		//1、初始化总服务费、服务项目单价、服务项目单位、总电量
		BigDecimal itemTAmt = strToBigDecimal("0.00", "");//总费用
		String[] itemPrices = cbrBo.getAttachItemPrices().split(",");
		String[] itemUnits = cbrBo.getAttachItemUnits().split(",");
		String[] itemNos = cbrBo.getAttachItemNos().split(",");
		String[] freeNums ;
		StringBuilder freeNumSb = new StringBuilder();
		if(StringUtil.isEmpty(cbrBo.getFreeNum())){
			for(int i =0;i<itemPrices.length;i++){
				freeNumSb.append("0").append(",");
			}
			freeNums = freeNumSb.substring(0, freeNumSb.length() - 1).split(",");
		}else{
			freeNums = cbrBo.getFreeNum().split(",");
		}

		String[] itemChargeModes = cbrBo.getItemChargeMode().split(",");

		BigDecimal tPq = strToBigDecimal(cbrBo.gettPq(), "计费服务费时，获取总电量错误");
		StringBuffer attachItemNums = new StringBuffer();//服务项目数量，逗号隔开
		StringBuffer attachItemAmts = new StringBuffer();//服务项目金额，逗号隔开

		Boolean servicePriceFlag = servicePriceService.getServicePriceSwitch();
		//2、计算费用项
		for(int i = 0; i < itemPrices.length; i ++){
			BigDecimal price = strToBigDecimal("0", "费用项目单价错误");
			if (ChargeConstant.chcBillingChargeMode.STANDARD.equals(itemChargeModes[i])){
				price = strToBigDecimal(itemPrices[i], "费用项目单价错误");
			}
			BigDecimal free = strToBigDecimal(freeNums[i], "免费量错误");
			if(BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(itemNos[i])){//服务费
				//按次数计价,直接累加
				if(ChargeConstant.chcBillingPriceUnit.COUNT.equals(itemUnits[i])){
					attachItemNums.append("1");
					attachItemAmts.append(BigDecimalToString(round(price, 2)));
					itemTAmt = addition(itemTAmt, round(price, 2));
					//按总电量计价
				}else if(ChargeConstant.chcBillingPriceUnit.KWH.equals(itemUnits[i])){
					if (!servicePriceFlag){
						BigDecimal freeNum = subtract(tPq,free);
						if(freeNum.compareTo(strToBigDecimal("0",""))<0){//如果免费量大于收费量则 此费用为0；
							freeNum=strToBigDecimal("0","");
						}
						BigDecimal oneAmt = round(serItemCalculator(price,freeNum), 2);
						attachItemNums.append(freeNum);
						attachItemAmts.append(BigDecimalToString(oneAmt));
						itemTAmt = addition(itemTAmt, oneAmt);
					}else {
						BigDecimal oneAmt = strToBigDecimal("0","");
						BigDecimal freeNum = subtract(tPq,free);
						//如果免费量大于收费量则 此费用为0；
						if(freeNum.compareTo(strToBigDecimal("0",""))<0){
							freeNum=strToBigDecimal("0","");
						}
						//标准
						if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(itemChargeModes[i])) {
							oneAmt = round(serItemCalculator(price, freeNum), 2);
						}
						//分时
						if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(itemChargeModes[i])) {
							oneAmt = getItemCal(cbrBo.getSerRltPeriods(),free);
						}
						attachItemNums.append(freeNum);
						attachItemAmts.append(BigDecimalToString(oneAmt));
						itemTAmt = addition(itemTAmt, oneAmt);
					}
					//按小时计价,不足一小时，按一小时算
				}else if(ChargeConstant.chcBillingPriceUnit.HOUR.equals(itemUnits[i])){
					long tTime = minuteToHour(cbrBo.gettTime(), "60");
					BigDecimal freeNum = subtract(new BigDecimal(tTime),free);
					if(freeNum.compareTo(strToBigDecimal("0",""))<0){
						freeNum=strToBigDecimal("0","");
					}
					BigDecimal oneAmt = round(serItemCalculator(price,freeNum), 2);
					attachItemNums.append(freeNum);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
					//按半小时计价，不足半小时，按半小时算
				}else if(ChargeConstant.chcBillingPriceUnit.HALF_HOUR.equals(itemUnits[i])){
					long tTime = minuteToHour(cbrBo.gettTime(), "30");
					BigDecimal freeNum = subtract(new BigDecimal(tTime),free);
					if(freeNum.compareTo(strToBigDecimal("0",""))<0){
						freeNum=strToBigDecimal("0","");
					}
					BigDecimal oneAmt = round(serItemCalculator(price,freeNum), 2);
					attachItemNums.append(freeNum);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
				}
				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
			if("1000002001".equals(itemNos[i])){//充电前占位费：(插枪时间-降地锁时间-免费时间量)*单价---时间是分钟
				String plugGunTime = StringUtil.nullToString(inMap.get("plugGunTime"));//插枪时间
				String downGroundLockTime = StringUtil.nullToString(inMap.get("downGroundLockTime"));//降地锁时间
				if(StringUtil.isNotEmpty(plugGunTime) && StringUtil.isNotEmpty(downGroundLockTime)){
					BigDecimal difTime=subtract(timeDiff(downGroundLockTime,plugGunTime),free);
					if(difTime.compareTo(strToBigDecimal("0",""))<0){
						difTime=strToBigDecimal("0","");
					}
					attachItemNums.append(difTime);
					attachItemAmts.append(round(serItemCalculator(price,difTime), 2));
					itemTAmt = addition(itemTAmt, round(serItemCalculator(price,difTime), 2));
				}else{
					attachItemNums.append(0);
					attachItemAmts.append(0);
				}

				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
			if("1000002002".equals(itemNos[i])){//充电后占位费：(拔枪时间-结束充电时间-免费时间量)*单价
				String chargingEndTime = StringUtil.nullToString(inMap.get("chargingEndTime"));//充电完成时间
				String pullGunTime = StringUtil.nullToString(inMap.get("pullGunTime"));//拔枪时间
				if(StringUtil.isNotEmpty(chargingEndTime) && StringUtil.isNotEmpty(pullGunTime)){
					BigDecimal difTime=subtract(timeDiff(chargingEndTime,pullGunTime),free);
					if(difTime.compareTo(strToBigDecimal("0",""))<0){
						difTime=strToBigDecimal("0","");
					}
					attachItemNums.append(difTime);
					attachItemAmts.append(round(serItemCalculator(price,difTime), 2));
					itemTAmt = addition(itemTAmt, round(serItemCalculator(price,difTime), 2));
				}else{
					attachItemNums.append(0);
					attachItemAmts.append(0);
				}

				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
			if("1000002003".equals(itemNos[i])){//充电前插枪占位费：(充电开始时间-插枪时间-免费时间量)*单价
				String plugGunTime = StringUtil.nullToString(inMap.get("plugGunTime"));//插枪时间
				String beginTime = StringUtil.nullToString(inMap.get("chargingBgnTime"));//充电开始时间
				if(StringUtil.isNotEmpty(plugGunTime) && StringUtil.isNotEmpty(beginTime)) {
					BigDecimal difTime=subtract(timeDiff(plugGunTime,beginTime),free);
					if(difTime.compareTo(strToBigDecimal("0",""))<0){
						difTime=strToBigDecimal("0","");
					}
					attachItemNums.append(difTime);
					attachItemAmts.append(round(serItemCalculator(price,difTime), 2));
					itemTAmt = addition(itemTAmt, round(serItemCalculator(price,difTime), 2));
				}else{
					attachItemNums.append(0);
					attachItemAmts.append(0);
				}
				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
			if("1000002004".equals(itemNos[i])){//充电后插枪占位费：(升地锁时间-拔枪时间-免费时间量)*单价
				String upGroundLockTime = StringUtil.nullToString(inMap.get("upGroundLockTime"));//升地锁时间
				String pullGunTime = StringUtil.nullToString(inMap.get("pullGunTime"));//拔枪时间
				if(StringUtil.isNotEmpty(upGroundLockTime) && StringUtil.isNotEmpty(pullGunTime)) {
					BigDecimal difTime=subtract(timeDiff(pullGunTime,upGroundLockTime),free);
					if(difTime.compareTo(strToBigDecimal("0",""))<0){
						difTime=strToBigDecimal("0","");
					}
					attachItemNums.append(difTime);
					attachItemAmts.append(round(serItemCalculator(price,difTime), 2));
					itemTAmt = addition(itemTAmt, round(serItemCalculator(price,difTime), 2));
				}else{
					attachItemNums.append(0);
					attachItemAmts.append(0);
				}

				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
			if("1000003000".equals(itemNos[i])){//低速附加费：（电量-免费量）*单价
				String sec90 = StringUtil.nullToString(inMap.get("secEnergy"));
				if(StringUtil.isNotEmpty(sec90)) {
					BigDecimal secEnergy = strToBigDecimal(StringUtil.nullToString(inMap.get("secEnergy")), ">90%电量错误");
					BigDecimal freeNum = subtract(secEnergy,free);
					if(freeNum.compareTo(strToBigDecimal("0",""))<0){
						freeNum=strToBigDecimal("0","");
					}
					BigDecimal oneAmt = round(serItemCalculator(price,freeNum), 2);
					attachItemNums.append(freeNum);
					attachItemAmts.append(BigDecimalToString(oneAmt));
					itemTAmt = addition(itemTAmt, oneAmt);
				}else{
					attachItemNums.append(0);
					attachItemAmts.append(0);
				}

				if(i != itemPrices.length -1){
					attachItemNums.append(",");
					attachItemAmts.append(",");
				}
			}
		}
		cbrBo.setAttachItemNums(attachItemNums.toString());
		cbrBo.setAttachItemAmts(attachItemAmts.toString());
		cbrBo.setItemTAmt(BigDecimalToString(itemTAmt));
	}

	private BigDecimal timeDiff(String bgnTime,String endTime){
		LOGGER.debug("=========计算费用项：bgnTime："+bgnTime+",endTime:"+endTime);
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		BigDecimal minute=null;
		try {
			Date begin =   df.parse(bgnTime);
			Date end   =   df.parse(endTime);
			long between=(end.getTime()-begin.getTime())/1000;//除以1000是为了转换成秒
			minute=strToBigDecimal(MathUtils.divide(between+"","60"),"");
		} catch (ParseException e) {
			e.printStackTrace();
			return new BigDecimal(0);
		}
		return minute;
	}
	/**
	 * @description 将输入的分钟数，转换成输入
	 * @param minute 分钟数
	 * @param unit 被除数
	 * @return 返回数值（如果有余数，则加+1）
	 * */
	private long minuteToHour(String minute,String unit){
		if(StringUtils.isEmpty(minute))
			throw new ChargeBillingRltException("计费服务费时，获取充电时间为空");
		long minuteLong = Long.parseLong(minute);
		long unitLong = Long.parseLong(unit);
		long returnLong = minuteLong / unitLong;
		long remainder = minuteLong % unitLong;
		if(remainder != 0)
			returnLong ++;
		return returnLong;
	}

	/**
	 * @description 如果服务项目单位为KWA，获取乘积
	 * @param itemTAmt 总服务费
	 * @param itemPrice 单项服务费
	 * */
	private BigDecimal serItemCalculator(BigDecimal itemTAmt,BigDecimal itemPrice){
		return multiply(itemTAmt, itemPrice);
	}

	/**
	 * @description 根据服务费与充电总金额，算出总金额     amt = itemTAmt + tAmt
	 * @reutn 总金额     amt
	 * */
	private void amtCalculator(ChargeBillingRltBo cbrBo){
		cbrBo.setAmt(BigDecimalToString(addition(strToBigDecimal(cbrBo.getItemTAmt(),"总服务费错误"), strToBigDecimal(cbrBo.gettAmt(),"充电总金额错误"))));
	}

	/**
	 * String类型转为BigDecimal
	 * */
	private BigDecimal strToBigDecimal(String str,String message) {
		try{
			return new BigDecimal(str);
		}catch(Exception e){
			throw new ChargeBillingRltException(message);
		}
	}

	/**
	 *BigDecimal转为 String
	 * */
	private String BigDecimalToString(BigDecimal preStr){
		return preStr.toString();
	}

	/**
	 * 加法计算
	 * */
	private BigDecimal addition(BigDecimal preStr,BigDecimal nextStr){
		return preStr.add(nextStr);
	}

	/**
	 * 减法计算
	 * */
	private BigDecimal subtract(BigDecimal preStr,BigDecimal nextStr){
		return preStr.subtract(nextStr);
	}

	/**
	 * 乘法计算
	 * */
	private BigDecimal multiply(BigDecimal preStr,BigDecimal nextStr){
		return preStr.multiply(nextStr);
	}

	/**
	 * 进行四舍五入操作
	 * @param len 小数点后面位数
	 * */
	public static BigDecimal round(BigDecimal b1,int len) {
		BigDecimal b2 = new BigDecimal(1);
		return b1.divide(b2, len,BigDecimal.ROUND_HALF_UP);
	}

	/**
	 * @param
	 * @param free
	 * @description 计算服务费分时电价
	 * <AUTHOR>
	 * @create 2019-03-18 17:06:43
	 */
	private BigDecimal getItemCal(List<ChargeBillingRltPeriodsBo> newRltPeriods, BigDecimal free) {
		Map calMap = new HashMap();

		//开始计算
		BigDecimal amt = strToBigDecimal("0.00", "");//分时总金额
		BigDecimal amtTmp = strToBigDecimal("0.00", "");
		if(newRltPeriods == null || newRltPeriods.size() == 0){
			LOGGER.debug("分时时段无数据");
			return amt;
		}
		BigDecimal freeNum = free;

		for(int i = 0;i<newRltPeriods.size();i++){
			ChargeBillingRltPeriodsBo crpBo = newRltPeriods.get(i);
			BigDecimal tPq = strToBigDecimal(crpBo.getPq(),"分时金额计算时，时段电量获取出错");
			BigDecimal freePq = strToBigDecimal(crpBo.getPq(),"分时金额计算时，时段电量获取出错");

			if(freeNum.compareTo(strToBigDecimal("0", "")) > 0){
				tPq = subtract(tPq,freeNum);//免费量
				freeNum = subtract(freeNum,freePq);//剩余免费量
				if(freeNum.compareTo(strToBigDecimal("0", "")) < 0){
					freeNum = strToBigDecimal("0", "");
				}
			}

			if(tPq.compareTo(strToBigDecimal("0", "")) < 0){
				tPq = strToBigDecimal("0", "");
			}
			//20220607统一口径，总金额=各个费用之和
			BigDecimal timeAmt = multiply(tPq, strToBigDecimal(crpBo.getPrice(), "分时金额计算时，计费单价获取出错"));
			BigDecimal roundTimeAmt = round(timeAmt, 2);
			amt = addition(amt, roundTimeAmt);
			crpBo.setAmt(BigDecimalToString(roundTimeAmt));
			crpBo.setPq(BigDecimalToString(tPq));

			//如果是最后一个分时金额，则分时金额 = 总金额 - 其余分时金额
			/*if(i == newRltPeriods.size() -1){
				amt = round(amt, 2);
				crpBo.setAmt(BigDecimalToString(subtract(amt, amtTmp)));
				crpBo.setPq(BigDecimalToString(tPq));
			}else{
				//如果不是最后一个分时金额，则分时金额四舍五入、保留两位小数
				BigDecimal roundTimeAmt = round(timeAmt, 2);
				crpBo.setAmt(BigDecimalToString(roundTimeAmt));
				crpBo.setPq(BigDecimalToString(tPq));
				amtTmp = addition(amtTmp, roundTimeAmt);
				LOGGER.debug("设置分时段金额"+roundTimeAmt);
			}*/
		}
		calMap.put("amt",amt);
		return amt;
	}
}
