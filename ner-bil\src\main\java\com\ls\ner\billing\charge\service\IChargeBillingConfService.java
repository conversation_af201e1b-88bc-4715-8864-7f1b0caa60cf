package com.ls.ner.billing.charge.service;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;

/**
 * 充电计费配置
 * <AUTHOR>
 */
public interface IChargeBillingConfService {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置E_CHARGE_BILLING_CONF
	 */
	public List<ChargeBillingConfBo> queryChargeBillingConfs(ChargeBillingConfQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
	 */
	public int queryChargeBillingConfsNum(ChargeBillingConfQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public String insertChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void updateChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 *
	 * @param bo
	 */
	public void addChargeBillingConfHistory(ChargeBillingConfHistoryBo bo);

	List<ChargeBillingConfHistoryBo> queryChargeBillingConfHistory(ChargeBillingConfHisQueryCondition bo);

	int queryChargeBillingConfHistoryCount(ChargeBillingConfHisQueryCondition condition);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 删除充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void delChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 发布充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void releaseChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 复制充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public ChargeBillingConfBo copyChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * @Description 停用计费配置
	 * <AUTHOR>
	 * @Date 2022/6/14 17:46
	 * @param bo
	 */
	List<ChargeBillingConfBo> deactivateChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 调价充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public ChargeBillingConfBo modifyPriceChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电分时设置  E_CHARGE_PERIODS
	 */
	public List<ChargePeriodsBo> queryChargePeriods(ChargeBillingConfQueryCondition bo);

	/**
	 * 查询计费配置信息
	 * @param condition
	 * @return
	 */
	List<ChargeBillingConfBo> queryChargeBillingMap(ChargeBillingConfQueryCondition condition);

	/**
	 * @param bo
	 * @description 下发分时信息
	 * <AUTHOR>
	 * @create 2018-08-13 18:40:33
	 */
	void sendChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * @param bo
	 * @description 注销计费
	 * <AUTHOR>
	 * @create 2018-11-05 10:52:40
	 */
	void cancellChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * @Description 计费生效
	 * <AUTHOR>
	 * @Date 2022/6/14 19:27
	 * @param bo
	 */
	void effectiveChargeBillingConf(ChargeBillingConfBo bo, Map<String,Object> map) throws Exception;

	/**
	 * @param bo
	 * @description 企业计费--查询可使用站点信息
	 * <AUTHOR>
	 * @create 2019-05-08 17:48:39
	 */
	public List<ChargeBillingConfBo> queryBillingStation(ChargeBillingConfQueryCondition bo);
}
