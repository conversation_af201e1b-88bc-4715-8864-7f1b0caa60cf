package com.ls.ner.billing.charge.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.base.cache.lua.RedisLuaScriptUtil;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.exception.BaseBusinessException;
import com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.*;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.dao.*;
import com.ls.ner.billing.charge.service.IChargeBillingRltService;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import com.ls.ner.billing.utils.RedisDistributedLock;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.util.*;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 分时服务费实现类
 * @date 2022/5/17 16:06
 */
@Service(target = {ServiceType.APPLICATION}, value = "servicePriceService")
public class TimeShareServicePriceServiceImpl implements ITimeShareServicePriceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TimeShareServicePriceServiceImpl.class);

    @Autowired
    private ChargeBillingRltUtil cbRltUtil;
    @Autowired
    private IChargeBillRpcDao chargeBillRpcDao;
    @Autowired
    private IChargeBillingRltDao chargeBillingRltDao;
    @Autowired
    private ChargeBillingRltCalculator chargeBillingRltCalculator;
    @Autowired
    private IChargeBillingPeriodsDao chargeBillingPeriodsDao;
    @Autowired
    private IChargeBillingConfDao chargeBillingConfDao;
    @Autowired
    private IChargePeriodsDao chargePeriodsDao;
    @Autowired
    private IChargeBillingRltPeriodsDao chargeBillingRltPeriodsDao;
    @Autowired
    private IChargeBillingDao chargeBillingDao;
    @Autowired
    private ChargeBillingRltAdapter chargeBillingRltAdapter;
    @ServiceAutowired("chargeSerItemService")
    private IChargeSerItemService chargeSerItemService;
    @ServiceAutowired(serviceTypes= ServiceType.RPC)
    private ICodeService codeService;
    @ServiceAutowired(serviceTypes= ServiceType.RPC)
    private ISysParamService sysParamService;
    private RedisCluster redisService = RedisCluster.getInstance();
    @Autowired
    private IChargeSerItemDao chargeSerItemDao;
    @ServiceAutowired(serviceTypes =ServiceType.LOCAL, value = "chargeBillingRltService")
    private IChargeBillingRltService chargeBillingRltService;
    @ServiceAutowired("peakService")
    private IXpPeakService peakService;

    private static final String RLT_PER = "RLT_";//实际计费结果redis的前缀
    private static final String PERIODS_PER = "PERIODS_";//实际计费分时明细redis的前缀
    private static final String SER_ITEM = "ITEMS_";//实际计费服务费redis的前缀
    private static final int EXPIRE = 3600;//超时时间   一小时
    private static final String SER_PERIODS_PER = "SER_PERIODS_";//服务费实时计费分时明细redis的前缀
    private final RedisDistributedLock redisLock = new RedisDistributedLock(RedisCluster.getInstance());

    @Override
    public Boolean getServicePriceSwitch() {
        //目前不支持桩计费，所以开启桩计费直接返回false
        String isAssistPileBilling = sysParamService.getSysParamsValues("isAssistPileBilling");
        if (PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
            return false;
        }
        String servicePriceSwitch = sysParamService.getSysParamsValues("servicePriceSwitch");
        return StringUtils.isEmpty(servicePriceSwitch) ? false : "1".equals(servicePriceSwitch);
    }

    @Override
    public void initItemChargeModeConfBo(ChargeBillingConfBo cbcBo) {
        if(!StringUtils.isEmpty(cbcBo.getAttachItemNos()) && StringUtils.isEmpty(cbcBo.getItemChargeMode())){
            String[] itemNos = cbcBo.getAttachItemNos().split(",");
            String itemChargeMode = "0201";
            for (int i = 0; i < itemNos.length; i++) {
                if ("1000001000".equals(itemNos[i]) && i != 0){
                    itemChargeMode += ",0201";
                }
            }
            cbcBo.setItemChargeMode(itemChargeMode);
        }
    }

    @Override
    public void initItemChargeModeRltBo(ChargeBillingRltBo cbrBo){
        if(!StringUtils.isEmpty(cbrBo.getAttachItemNos()) && StringUtils.isEmpty(cbrBo.getItemChargeMode())){
            String[] itemNos = cbrBo.getAttachItemNos().split(",");
            String itemChargeMode = "0201";
            for (int i = 0; i < itemNos.length; i++) {
                if ("1000001000".equals(itemNos[i]) && i != 0){
                    itemChargeMode += ",0201";
                }
            }
            cbrBo.setItemChargeMode(itemChargeMode);
        }
    }

    /**
     * <AUTHOR>
     * @throws Exception
     * @dateTime 2016-11-30
     * @description *******	RPC05-02-02查询充电定价
     */
    @Override
    public List<Map<String, Object>> getChargePrice(Map<String,Object> inMap) throws Exception{
        List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
        List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
        if(stationList != null){
            inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
            inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
            String stationIds = StringUtil.nullForString(inMap.get("stationId"));
            if(!StringUtil.isBlank(stationIds)){
                inMap.put("stationIds", stationIds.split(","));
            }
            //1、查询计费配置
            List<Map> confs = getChargeConfs(inMap);
            if(confs != null && confs.size() > 0){
                String currentStationId = null;
                StringBuilder chcNoSb = new StringBuilder();
                StringBuilder itemNoSb = new StringBuilder();
                List<Map<String,Object>> stationConfs = new LinkedList<Map<String,Object>>();
                for(Map conf : confs){
                    String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
                    String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
                    String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
                    if(currentStationId == null || !currentStationId.equals(stationId)){//第一个stationId
                        currentStationId = stationId;
                        stationConfs.add(conf);
                        chcNoSb.append(chcNo).append(",");
                        itemNoSb.append(itemNo).append(",");
                    }else{//如果不是第一个，将多余的充电计费配置状态改为无效
                        if(!"".equals(chcNo)){
                            ChargeBillingConfBo cbc = new ChargeBillingConfBo();
                            cbc.setChcNo(chcNo);
                            cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
                            chargeBillingConfDao.updateChargeBillingConf(cbc);
                        }
                    }
                }
                //2、查询充电分时设置
                String chcNos = chcNoSb.toString();
                if(!StringUtils.isEmpty(chcNos)){
                    chcNos = chcNos.substring(0, chcNos.length() - 1);
                    List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
                    List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
                    MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
                }
                //3、查询服务费用
                Map<String,Object> itemMap = null;
                if("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))){//所有产品
                    String itemNos = itemNoSb.toString();
                    if(!StringUtils.isEmpty(itemNos)){
                        itemNos = itemNos.substring(0, itemNos.length() - 1);
                        ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
                        bo.setItemNo(itemNos);
                        List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
                        if(csItems != null && csItems.size() > 0){
                            itemMap = new HashMap<String, Object>();
                            for(ChargeSerItemBo csBo : csItems)
                                itemMap.put(csBo.getItemNo(), csBo);
                        }
                    }
                }
                LOGGER.debug("getChargePrice-itemMap:"+itemMap);

                //4、组合数据
                for(Map sconf : stationConfs){
                    Map<String,Object> stationCharge = Maps.newHashMap();
                    stationCharges.add(stationCharge);
                    stationCharge.put("stationId", sconf.get("STATION_ID"));
                    List<Map> prodList = new LinkedList<Map>();
                    stationCharge.put("prodList", prodList);
                    //充电费组合
                    Map<String,Object> prodOne = new HashMap<String, Object>();
                    prodList.add(prodOne);
                    prodOne.put("prodId", sconf.get("CHC_NO"));
                    prodOne.put("prodName", "充电费");
                    prodOne.put("mandatoryType", "02");
                    List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
                    prodOne.put("pricingCombine", pricingCombine);
                    String details = "";
                    String beginStr = null;
                    String endStr = null;
                    if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
                        details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
                        if(beginStr == null || cbRltUtil.isGt(beginStr, String.valueOf(sconf.get("CHARGE_PRICE")))){
                            beginStr = FormatUtil.formatNumber(String.valueOf(sconf.get("CHARGE_PRICE")),4,2);
                        }
                        if(endStr == null || !cbRltUtil.isGt(endStr, String.valueOf(sconf.get("CHARGE_PRICE")))){
                            endStr = FormatUtil.formatNumber(String.valueOf(sconf.get("CHARGE_PRICE")),4,2);
                        }
                    }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
                        List<Map> periods = (List<Map>)sconf.get("periods");
                        if(periods != null)
                            for(Map prerod : periods){
                                if(ChargeConstant.itemTypeNo.CHARGE.equals(prerod.get("ITEM_NO"))) {
                                    details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") + " " + FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度;";
                                    if (beginStr == null || cbRltUtil.isGt(beginStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
                                        beginStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
                                    }
                                    if (endStr == null || !cbRltUtil.isGt(endStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
                                        endStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
                                    }
                                }
                            }
                    }
                    Map<String,Object> pricing = Maps.newHashMap();
                    pricingCombine.add(pricing);
                    if(!cbRltUtil.isEq(beginStr, endStr))
                        pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4,4) + "-" + FormatUtil.formatNumber(endStr,4,4) + "元/度");
                    else
                        pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4,4) +  "元/度");
                    pricing.put("pricingSectDesc", details);
                    //复制备注
                    if(sconf.get("CHC_REMARK") != null) {
                        pricing.put("chcRemark",sconf.get("CHC_REMARK").toString());
                    }else{
                        pricing.put("chcRemark","");
                    }
                    //服务费组合
                    if(itemMap != null){
                        String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
                        String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
                        String itemChargeMode = cbRltUtil.returnNotNull(sconf.get("ITEM_CHARGE_MODE"));
                        if(!StringUtils.isEmpty(itemNos)){
                            String[] iNos = itemNos.split(",");
                            String[] iPrices = itemPrices.split(",");
                            String[] iChargeMode = itemChargeMode.split(",");
                            List<Map> periods = (List<Map>) sconf.get("periods");
                            for(int i = 0;i < iNos.length ; i ++){
                                ChargeSerItemBo csBo = (ChargeSerItemBo)itemMap.get(iNos[i]);
                                Map<String,Object> prodTwo = Maps.newHashMap();
                                prodList.add(prodTwo);
                                prodTwo.put("prodId", csBo.getItemNo());
                                prodTwo.put("prodName", csBo.getItemName());
                                List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
                                prodTwo.put("pricingCombine", pricingCombine2);
                                Map<String,Object> pricing2 = Maps.newHashMap();
                                pricingCombine2.add(pricing2);

                                if (ChargeConstant.itemTypeNo.SERVICE.equals(iNos[i]) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(iChargeMode[i])){
                                    String serDetails = "";
                                    String serBeginStr = null;
                                    String serEndStr = null;
                                    for(Map prerod : periods){
                                        if(ChargeConstant.itemTypeNo.SERVICE.equals(prerod.get("ITEM_NO"))) {
                                            serDetails += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") + " " + FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度;";
                                            if (serBeginStr == null || cbRltUtil.isGt(serBeginStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
                                                serBeginStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
                                            }
                                            if (serEndStr == null || !cbRltUtil.isGt(serEndStr, cbRltUtil.returnNotNull(prerod.get("PRICE")))) {
                                                serEndStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(prerod.get("PRICE")), 4, 2);
                                            }
                                        }
                                    }

                                    if(!cbRltUtil.isEq(serBeginStr, serEndStr))
                                        pricing2.put("pricingSectName",  FormatUtil.formatNumber(serBeginStr,4,4) + "-" + FormatUtil.formatNumber(serEndStr,4,4) + "元/度");
                                    else
                                        pricing2.put("pricingSectName",  FormatUtil.formatNumber(serBeginStr,4,4) +  "元/度");

                                    pricing2.put("pricingSectDesc", serDetails);
                                    LOGGER.debug("服务费分时pricingSectName出参:{}", JSON.toJSONString(pricing2));


                                }else{
                                    double iprice = Double.parseDouble(getItemInfo(csBo, periods,iPrices[i],iChargeMode[i]));
                                    pricing2.put("pricingSectName", FormatUtil.formatNumber(iprice, 4) + "元/" + csBo.getItemUnitValueName());
                                    pricing2.put("pricingSectDesc", StringUtil.isBlank(csBo.getRemarks()) ? pricing2.get("pricingSectName") : csBo.getRemarks());
                                    LOGGER.debug("=========pricingSectValue:"+iPrices[i]);
                                    pricing2.put("pricingSectValue", FormatUtil.formatNumber(iPrices[i], 2));
                                }

                            }
                        }
                    }
                }
            }
        }
        List<Map<String,Object>> prodModeList = new LinkedList<Map<String,Object>>();
        Map<String,Object> prodMode = Maps.newHashMap();
        prodMode.put("prodModeId", "1");
        prodMode.put("stationList", stationCharges);
        prodModeList.add(prodMode);
        return prodModeList;
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-11-23
     * @description *******	RPC05-02-25充电计费 – 预估
     */
    @Override
    public Map<String, Object> estimate(Map<String, Object> inMap)throws Exception{
        //1、判断传参是否正确
        String orderNo = cbRltUtil.isNotEmpty(inMap.get("orderNo"), "订单编号不能为空");
        //2、查询该订单的充电计费信息
        Object obj = redisService.get(RLT_PER + orderNo);
        ChargeBillingRltBo cbrBo = null;
        List<ChargeBillingRltPeriodsBo> cbrList = null;
        if(obj != null){
            cbrBo = IJsonUtil.json2Obj((String)obj, ChargeBillingRltBo.class);
            cbrList = cbrBo.getRltPeriods();
        }else{
            cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
            if(cbrBo == null)
                throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");
//			cbrList = chargeBillingRltPeriodsDao.queryChargeBillingRltPeriod(orderNo);
            Map periodMap = new HashMap();
            periodMap.put("appNo",orderNo);
            periodMap.put("itemType","01");//01:电费  02 服务费  03：其他费用
            cbrList = chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap);
            cbrBo.setRltPeriods(cbrList);
            redisService.setex(RLT_PER + orderNo, cbrBo, EXPIRE);
        }

        //初始化itemchargemode
        initItemChargeModeRltBo(cbrBo);

        Map periodMap = new HashMap();
        periodMap.put("appNo",orderNo);
        periodMap.put("itemType","02");//01:电费  02 服务费  03：其他费用
        cbrBo.setSerPeriods(chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap));

        periodMap.put("itemType","03");//01:电费  02 服务费  03：其他费用
        cbrBo.setItemRltPeriods(chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap));//用于计算附加费用-分时

        //3、装配数据
        cbrBo.setBgnTime(cbRltUtil.isNotEmpty(inMap.get("chargingBgnTime"), "充电开始时间不能为空"));
        cbrBo.setEndTime(cbRltUtil.isNotEmpty(inMap.get("chargingEndTime"), "充电结束时间不能为空"));
        cbrBo.settTime(cbRltUtil.startToEndTime(cbrBo.getBgnTime(), cbrBo.getEndTime()));//充电时间，单位：分钟
        cbrBo.settPq(cbRltUtil.isNotEmpty(inMap.get("tPq"), "缺少充电总电量"));
        if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
            String edElecAmt = StringUtil.nullToString(inMap.get("edElecAmt"));//CCTIA表示充电费 其他协议表示总金额
            String edServiceAmt = StringUtil.nullToString(inMap.get("edServiceAmt"));//CCTIA表示服务费
            String edAmt = StringUtil.nullToString(inMap.get("edAmt"));//CCTIA表示总金额
            if(StringUtil.isEmpty(edAmt)){//如果没上报edAmt，则不是CCTIA协议，edElecAmt表示总金额
                cbrBo.setAmt(cbRltUtil.isNotEmpty(edElecAmt, "缺少总金额"));
            }else{
                cbrBo.settAmt(cbRltUtil.isNotEmpty(edElecAmt, "缺少充电费"));
                cbrBo.setItemTAmt(StringUtils.isEmpty(edServiceAmt)?"0.00":String.valueOf(edServiceAmt));
                cbrBo.setAmt(cbRltUtil.isNotEmpty(edAmt, "缺少总金额"));
            }
        }else{//远程计费
            if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
                Map orderMap = new HashMap();
                orderMap.put("appNo",orderNo);
                Object pObj = redisService.get(PERIODS_PER + orderNo);
                if(pObj != null){
                    Gson gson=new Gson();
                    List<ChargeBillingRltPeriodsBo> newPeriods =gson.fromJson((String)pObj, new TypeToken<List<ChargeBillingRltPeriodsBo>>(){}.getType());
                    cbrBo.setNewPeriods(newPeriods);
                }else{
                    orderMap.put("itemNo","1000000000");
                    List<ChargeBillingRltPeriodsBo> newPeriods = chargeBillingPeriodsDao.queryChargeBillingPeriod(orderMap);
                    cbrBo.setNewPeriods(newPeriods);
                }
                cbRltUtil.refreshPeriods(cbrBo,"1000000000");//电费分时组装

                //服务费
                Object serPobj = redisService.get(SER_PERIODS_PER + orderNo);
                if (serPobj != null) {
                    Gson gson = new Gson();
                    List<ChargeBillingRltPeriodsBo> newPeriods = gson.fromJson((String) serPobj, new TypeToken<List<ChargeBillingRltPeriodsBo>>() {}.getType());
                    cbrBo.setNewPeriods(newPeriods);
                } else {
                    orderMap.put("itemNo","1000001000");
                    List<ChargeBillingRltPeriodsBo> newPeriods = chargeBillingPeriodsDao.queryChargeBillingPeriod(orderMap);
                    cbrBo.setNewPeriods(newPeriods);
                }
                cbRltUtil.refreshPeriods(cbrBo,"1000001000");//服务费分时组装
            }
            //4、计算费用
            String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
            inMap.put("calType","02");//计算类型 01：实时 02 结算
            if("toMoreStation".equals(chargeVesion)){//新版计费
                chargeBillingRltCalculator.calculatorNew(cbrBo,inMap);
            }else{
                chargeBillingRltCalculator.calculator(cbrBo);
            }
        }
        //5、存储充电计费信息
        List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
        if(ChargeConstant.billCtlMode.REMOTE.equals(cbrBo.getBillCtlMode()) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//远程计费、分时计费
            if(periods!=null){
                ChargeBillingRltPeriodsBo p = periods.get(periods.size()-1);
                if(StringUtils.isEmpty(p.getSystemId())){
                    chargeBillingPeriodsDao.insertChargeBillingPeriods(p);
                }else{
                    // 分布式操作
                    executeWithDistributedLock(p);
                }
                redisService.setex(PERIODS_PER + orderNo, periods, EXPIRE);
            }
        }

        //5、存储服务费计费信息
        List<ChargeBillingRltPeriodsBo> serPeriods = cbrBo.getSerRltPeriods();
        if (ChargeConstant.billCtlMode.REMOTE.equals(cbrBo.getBillCtlMode()) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())) {//远程计费、分时计费
            if (serPeriods != null && serPeriods.size() >0) {
                ChargeBillingRltPeriodsBo p = serPeriods.get(serPeriods.size() - 1);
                if (StringUtils.isEmpty(p.getSystemId())) {
                    chargeBillingPeriodsDao.insertChargeBillingPeriods(p);
                } else {
                    // 分布式操作
                    executeWithDistributedLock(p);
                }
                redisService.setex(SER_PERIODS_PER + orderNo, serPeriods, EXPIRE);
            }
        }

        //6、返回数据
        Map<String,Object> returnMap = Maps.newHashMap();
        returnMap.put("ctlMode",cbrBo.getBillCtlMode());//计费方式：1：本地、2：远程
        returnMap.put("prodBillId", cbrBo.getAppNo());//产品订单实例ID
        returnMap.put("billAmt", cbrBo.getAmt());//总金额
        List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
        returnMap.put("prodBillDet", prodBillDet);
        if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
            Map<String,Object> prodOne = Maps.newHashMap();
            prodBillDet.add(prodOne);
            prodOne.put("prodId", cbrBo.getChcNo());
            prodOne.put("prodName", "充电费");
            prodOne.put("billDetNum", cbrBo.gettPq());
            prodOne.put("billDetAmt", cbrBo.gettAmt());
            Map<String,Object> prodTwo = Maps.newHashMap();
            prodBillDet.add(prodTwo);
            prodTwo.put("prodId", cbrBo.getChcNo());
            prodTwo.put("prodName", "服务费");
            prodTwo.put("billDetAmt", cbrBo.getItemTAmt());
        }else{//远程计费
            Map<String,Object> prodOne = Maps.newHashMap();
            prodBillDet.add(prodOne);
            prodOne.put("prodId", cbrBo.getChcNo());
            prodOne.put("prodName", "充电费");
            prodOne.put("billDetNum", cbrBo.gettPq());
            prodOne.put("billDetAmt", cbrBo.gettAmt());
            List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
            prodOne.put("pricingCombine", pricingCombine);
            String details = "";
            String beginStr = null;
            String endStr = null;
            if(cbrList != null && cbrList.size() > 0){
                for(ChargeBillingRltPeriodsBo prerod : cbrList){
                    details += prerod.getBeginTime() + "~" + prerod.getEndTime() +" "+ FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
                    if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
                        beginStr = prerod.getPrice();
                    }
                    if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
                        endStr = prerod.getPrice();
                    }
                }
            }else{
                details += "00:00~24:00 "  + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
                if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
                    beginStr = cbrBo.getChargePrice();
                }
                if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
                    endStr = cbrBo.getChargePrice();
                }
            }
            Map<String,Object> pricing = Maps.newHashMap();
            pricingCombine.add(pricing);
            if(!cbRltUtil.isEq(beginStr, endStr))
                pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
            else
                pricing.put("pricingSectName",  FormatUtil.formatNumber(beginStr,4) +  "元/度");
            pricing.put("pricingSectDesc", details);
            //获取服务费用项
            if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
                ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
                csCondition.setItemNo(cbrBo.getAttachItemNos());
                Object pObj = redisService.get(SER_ITEM + orderNo);
                List<ChargeSerItemBo> csList = null;
                if(pObj != null){
                    Gson gson=new Gson();
                    csList =gson.fromJson((String)pObj, new TypeToken<List<ChargeSerItemBo>>(){}.getType());
                }else{
                    csList = chargeSerItemDao.queryChargeSerItems(csCondition);
                    redisService.setex(SER_ITEM + orderNo, csList, EXPIRE);
                }
                if(csList != null){
                    Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
                    for(ChargeSerItemBo csi:csList){
                        m.put(csi.getItemNo(), csi);
                    }
                    String[] itemNo = cbrBo.getAttachItemNos().split(",");
                    String[] price = cbrBo.getAttachItemPrices().split(",");
                    String[] nums = cbrBo.getAttachItemNums().split(",");
                    String[] amts = cbrBo.getAttachItemAmts().split(",");
                    String[] itemChargeMode = cbrBo.getItemChargeMode().split(",");
                    String serPrice = "0";
                    for(int i = 0;i < itemNo.length;i ++){
                        ChargeSerItemBo bo = m.get(itemNo[i]);
                        if(bo != null){
                            serPrice = getSerPrice(itemChargeMode[i],price[i]);
                            Map<String,Object> prodTwo = Maps.newHashMap();
                            prodBillDet.add(prodTwo);
                            prodTwo.put("prodId", bo.getItemNo());
                            prodTwo.put("prodName", bo.getItemName());
                            prodTwo.put("billDetNum", nums[i]);
                            prodTwo.put("billDetAmt", amts[i]);
                            prodTwo.put("price", FormatUtil.formatNumber(serPrice, 2));
                            List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
                            prodTwo.put("pricingCombine", pricingCombine2);
                            Map<String,Object> pricing2 = Maps.newHashMap();
                            pricingCombine2.add(pricing2);
                            pricing2.put("pricingSectName", FormatUtil.formatNumber(serPrice, 4) + "元/" + bo.getItemUnitValueName());
                        }
                    }
                }
            }
        }
        return returnMap;
    }

    /**
     * 在分布式锁保护下执行缓存检查与数据库更新操作
     */
    private void executeWithDistributedLock(ChargeBillingRltPeriodsBo p) throws InterruptedException {
        String systemId = p.getSystemId();
        String uuid = null;
        try {
            // 获取 Redis 锁的过期时间（默认 5 秒）
            String timeShareServiceRedisTime = sysParamService.getSysParamsValues("timeShareServiceRedisTime");
            if (StringUtils.isEmpty(timeShareServiceRedisTime)) {
                timeShareServiceRedisTime = "5";
            }
            int expireTime = Integer.parseInt(timeShareServiceRedisTime);

            // 获取分布式锁（最多重试 3 次）
            for (int i = 0; i < RedisLuaScriptUtil.DEFAULT_RETRY_TIMES; i++) {
                uuid = RedisLuaScriptUtil.acquireLock("lock:charge:" + systemId, expireTime);
                if (uuid != null) {
                    LOGGER.info("获取锁成功，systemId={}", systemId);
                    break;
                }
                LOGGER.warn("第 {} 次尝试获取锁失败，等待 {} ms 后重试", i + 1, RedisLuaScriptUtil.SLEEP_INTERVAL);
                Thread.sleep(RedisLuaScriptUtil.SLEEP_INTERVAL);
            }

            if (uuid == null) {
                LOGGER.warn("多次尝试获取锁失败，跳过处理 systemId={}", systemId);
                throw new BaseBusinessException("获取锁失败，跳过处理 systemId=" + systemId);
            }

            // ✅ 加锁成功，执行业务操作
            String cacheKey = "timeShare_d_" + systemId;
            String queuingNumber = (String) redisService.get(cacheKey);

            if (StringUtils.isEmpty(queuingNumber)) {
                chargeBillingPeriodsDao.updateChargeBillingPeriods(p);
            }

            redisService.setex(cacheKey, systemId, expireTime);

        } finally {
            // 安全释放锁
            if (uuid != null) {
                RedisLuaScriptUtil.releaseLock("lock:charge:" + systemId, uuid);
            }
        }
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-11-25
     * @description *******	RPC05-02-24充电计费 – 预收/计费版本
     */
    @Override
    public Map<String, Object> payInAdvance(Map<String, Object> inMap)throws Exception{
        Map<String, Object> cbMap = Maps.newHashMap();
        Map<String, Object> param = Maps.newHashMap();
        param.put("appNo", inMap.get("orderNo"));
        param.put("stationId", inMap.get("stationId"));
        param.put("pileId", inMap.get("pileId"));
        param.put("custId", inMap.get("custId"));
        param.put("groupId", inMap.get("groupId"));
        param.put("pCustId",inMap.get("pCustId"));
        chargeBillingRltService.createChargeOrderVersionForOrder(param);
        ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(String.valueOf(inMap.get("orderNo")));

        //初始化itemchargemode
        initItemChargeModeRltBo(cbrBo);

        cbMap.put("prodBillId", cbrBo.getSystemId());
        List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
        cbMap.put("prodBillDet", prodBillDet);
        Map<String,Object> prodOne = Maps.newHashMap();
        prodBillDet.add(prodOne);
        prodOne.put("prodId", cbrBo.getChcNo());
        prodOne.put("prodName", "充电费");
        List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
        prodOne.put("pricingCombine", pricingCombine);
        String details = "";
        String beginStr = null;
        String endStr = null;
        List<ChargePeriodsBo> cperiodsList = new ArrayList<ChargePeriodsBo>();
        //分时明细
        if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//如果是标准计费方式
            details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
            if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
                beginStr = cbrBo.getChargePrice();
            }
            if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
                endStr = cbrBo.getChargePrice();
            }
        }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//如果是分时计费方式
            ChargeBillingConfQueryCondition cperiodsCondition = new ChargeBillingConfQueryCondition();
            cperiodsCondition.setChcNo(cbrBo.getChcNo());
            cperiodsList = peakService.queryPeriodListCheckPeak(cperiodsCondition);
            if(cperiodsList!=null) {
                for (ChargePeriodsBo cpBo : cperiodsList) {
                    if (ChargeConstant.itemTypeNo.CHARGE.equals(cpBo.getItemNo())) {
                        details += cpBo.getBeginTime() + "~" + cpBo.getEndTime() + "  " + FormatUtil.formatNumber(cpBo.getPrice(), 4) + "元/度;";
                        if (beginStr == null || cbRltUtil.isGt(beginStr, cpBo.getPrice())) {
                            beginStr = cpBo.getPrice();
                        }
                        if (endStr == null || !cbRltUtil.isGt(endStr, cpBo.getPrice())) {
                            endStr = cpBo.getPrice();
                        }
                    }
                }
            }
        }
        Map<String,Object> pricing = Maps.newHashMap();
        pricingCombine.add(pricing);
        if(!cbRltUtil.isEq(beginStr, endStr))
            pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
        else
            pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
        pricing.put("pricingSectDesc", details);
        //获取服务费用项
        if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
            ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
            csCondition.setItemNo(cbrBo.getAttachItemNos());
            csCondition.setPrice(cbrBo.getAttachItemPrices());
            csCondition.setItemChargeMode(cbrBo.getItemChargeMode());
            List<ChargeSerItemBo> csList = chargeSerItemService.querySerItemsByNo(csCondition);
            if(csList != null)
                for(ChargeSerItemBo bo : csList){
                    Map<String,Object> prodTwo = Maps.newHashMap();
                    prodBillDet.add(prodTwo);
                    prodTwo.put("prodId", bo.getItemNo());
                    prodTwo.put("prodName", bo.getItemName());
                    List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
                    prodTwo.put("pricingCombine", pricingCombine2);
                    Map<String,Object> pricing2 = Maps.newHashMap();
                    pricingCombine2.add(pricing2);
//					pricing2.put("pricingSectName",  FormatUtil.formatNumber(bo.getServicePrice(),2) + "元/" + bo.getItemUnitValueName());
                    pricing2.put("pricingSectName", getItemPrice(bo, cperiodsList) + "元/" + bo.getItemUnitValueName());
                }
        }
        return cbMap;
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-11-25
     * @description *******	RPC05-02-26充电计费 – 结算
     */
    @Override
    public  Map<String, Object> settlement(Map<String, Object> inMap)throws Exception{
        LOGGER.debug("结算计费入参："+JSON.toJSONString(inMap));
        //1、判断传参是否正确
        String orderNo = cbRltUtil.isNotEmpty(inMap.get("orderNo"), "订单编号不能为空");
        //2、查询该订单的充电计费信息
        ChargeBillingRltBo cbrBo = chargeBillingRltDao.queryChargeBillingRlt(orderNo);
        if(cbrBo == null)
            throw new ChargeBillingRltException("找不到该订单的充电计费结果明细");

        //初始化itemchargemode
        initItemChargeModeRltBo(cbrBo);

        Map periodMap = new HashMap();
        periodMap.put("appNo",orderNo);
        periodMap.put("itemType","01");//01:电费  02 服务费  03：其他费用
        List<ChargeBillingRltPeriodsBo> cbrList = chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap);
        cbrBo.setRltPeriods(cbrList);

        periodMap.put("itemType","02");//01:电费  02 服务费  03：其他费用
        cbrBo.setSerPeriods(chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap));

        periodMap.put("itemType","03");//01:电费  02 服务费  03：其他费用
        cbrBo.setItemRltPeriods(chargeBillingRltPeriodsDao.queryBillingRltPeriod(periodMap));//用于计算附加费用-分时
        //3、装配数据
        cbrBo.setBgnTime(cbRltUtil.isNotEmpty(inMap.get("chargingBgnTime"), "充电开始时间不能为空"));
        cbrBo.setEndTime(cbRltUtil.isNotEmpty(inMap.get("chargingEndTime"), "充电结束时间不能为空"));
        cbrBo.settTime(cbRltUtil.startToEndTime(cbrBo.getBgnTime(), cbrBo.getEndTime()));//充电时间，单位：分钟
        cbrBo.setApplyEndTime("true");//请求结束时间
        cbrBo.settPq(cbRltUtil.isNotEmpty(inMap.get("tPq"), "缺少充电总电量"));
        List<Map<String,Object>> periodsList = (List<Map<String,Object>>)inMap.get("periodsList");
        String chargeMode = ChargeConstant.chcBillingChargeMode.TIME_SHARE;//计费方式
        if(periodsList == null || periodsList.size() == 0){
            chargeMode = ChargeConstant.chcBillingChargeMode.STANDARD;
        }
        if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
            cbrBo.settAmt(cbRltUtil.isNotEmpty(inMap.get("elecAmt"), "缺少充电费"));
            cbrBo.setItemTAmt(StringUtils.isEmpty(inMap.get("serviceAmt"))?"0.00":String.valueOf(inMap.get("serviceAmt")));
            cbrBo.setAmt(cbRltUtil.isNotEmpty(inMap.get("tAmt"), "缺少总金额"));
            cbrBo.setChargeMode(chargeMode);//计费方式
            if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
                List<ChargeBillingRltPeriodsBo> periods = cbRltUtil.rltPeriods((List<Map<String,Object>>)inMap.get("periodsList"));
                cbRltUtil.chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());

                if (cbrBo.getRltPeriods() != null && cbrBo.getRltPeriods().size() > 0){
                    LOGGER.debug("billRpc-本地电费分时组装前:"+ JsonUtil.obj2Json(cbrBo.getRltPeriods()));
                    cbrBo.setRltPeriods(cbRltUtil.priceForRltPeriods(periods, cbrBo.getRltPeriods(),"1000000000"));//电费分时组装
                    LOGGER.debug("billRpc-本地电费分时组装后:" + JsonUtil.obj2Json(cbrBo.getRltPeriods()));
                }

                cbrBo.setRltPeriods(periods);//分时记录

                if(cbrBo.getSerPeriods() !=null && cbrBo.getSerPeriods().size()>0) {
                    List<ChargeBillingRltPeriodsBo> periodsSer = cbRltUtil.rltPeriods((List<Map<String, Object>>) inMap.get("periodsList"));
                    cbRltUtil.chcNoForRltPeriods(periodsSer, cbrBo.getAppNo(), cbrBo.getChcNo());

                    for (ChargeBillingRltPeriodsBo crb: periodsSer) {
                        crb.setAmt(null);
                    }
                    LOGGER.debug("billRpc-本地服务费分时组装前:" + JsonUtil.obj2Json(cbrBo.getSerPeriods()));
                    cbrBo.setSerRltPeriods(cbRltUtil.priceForRltPeriods(periodsSer, cbrBo.getSerPeriods(), "1000001000"));//服务费分时组装
                    LOGGER.debug("billRpc-本地服务费分时组装后:" + JsonUtil.obj2Json(cbrBo.getSerRltPeriods()));
                }
            }
        }else{//远程计费
            if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbrBo.getChargeMode())){//标准计费
                if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(chargeMode)){//inMap传入的是标准计费

                }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(chargeMode)){//inMap传入的是分时计费

                }
            }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbrBo.getChargeMode())){//分时计费
                if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(chargeMode)){//inMap传入的是标准计费
                    cbrBo.setChargeMode(chargeMode);
                    List<ChargeBillingRltPeriodsBo> rltPeriods = cbrBo.getRltPeriods();
                    if(rltPeriods == null || rltPeriods.size() == 0)
                        throw new ChargeBillingRltException("充电计费配置分时计费方式，没有分时记录");
                    for(ChargeBillingRltPeriodsBo rltPeriod:rltPeriods){
                        if(cbRltUtil.compareTime(cbRltUtil.toTime(cbrBo.getBgnTime()), rltPeriod.getBeginTime(), rltPeriod.getEndTime())){
                            cbrBo.setChargePrice(rltPeriod.getPrice());
                            break;
                        }
                    }
                    cbrBo.setRltPeriods(null);
                }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(chargeMode)){//inMap传入的是分时计费
                    List<ChargeBillingRltPeriodsBo> periods = cbRltUtil.rltPeriods((List<Map<String,Object>>)inMap.get("periodsList"));
                    cbRltUtil.chcNoForRltPeriods(periods, cbrBo.getAppNo(), cbrBo.getChcNo());
                    LOGGER.debug("billRpc-分时组装前:"+ JsonUtil.obj2Json(periods));
                    cbrBo.setRltPeriods(cbRltUtil.priceForRltPeriods(periods, cbrBo.getRltPeriods(),"1000000000"));//电费分时组装
                    LOGGER.debug("billRpc-分时组装后:"+ JsonUtil.obj2Json(cbrBo.getRltPeriods()));

                    if(cbrBo.getSerPeriods() !=null && cbrBo.getSerPeriods().size()>0) {
                        List<ChargeBillingRltPeriodsBo> periodsSer = cbRltUtil.rltPeriods((List<Map<String, Object>>) inMap.get("periodsList"));
                        cbRltUtil.chcNoForRltPeriods(periodsSer, cbrBo.getAppNo(), cbrBo.getChcNo());
                        LOGGER.debug("billRpc-服务费分时组装前:" + JsonUtil.obj2Json(cbrBo.getSerPeriods()));
                        cbrBo.setSerRltPeriods(cbRltUtil.priceForRltPeriods(periodsSer, cbrBo.getSerPeriods(), "1000001000"));//服务费分时组装
                        LOGGER.debug("billRpc-服务费分时组装后:" + JsonUtil.obj2Json(cbrBo.getSerRltPeriods()));
                    }
                }
            }

            //4、计算费用
            String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
            if("toMoreStation".equals(chargeVesion)){//新版计费
                chargeBillingRltCalculator.calculatorNew(cbrBo,inMap);
            }else{
                chargeBillingRltCalculator.calculator(cbrBo);
            }

        }
        //5、存储充电计费信息,互联互通过来的结算只计算金额不保存
        if (!"hlhtSettle".equals(StringUtil.getString(inMap.get("from")))){
            //5、存储充电计费信息
            chargeBillingRltDao.updateChargeBillingRlt(cbrBo);
            chargeBillingRltPeriodsDao.deleteChargeBillingRltPeriod(orderNo);
            List<ChargeBillingRltPeriodsBo> periods = cbrBo.getRltPeriods();
            LOGGER.debug("billRpc-分时计算后:"+ JsonUtil.obj2Json(periods));
            if(periods !=null)
                for(ChargeBillingRltPeriodsBo period:periods)
                    chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(period);

            //存储服务费分时
            List<ChargeBillingRltPeriodsBo> serPeriods = cbrBo.getSerRltPeriods();
            LOGGER.debug("billRpc-服务费分时计算后:" + JsonUtil.obj2Json(serPeriods));
            if (serPeriods != null)
                for (ChargeBillingRltPeriodsBo period : serPeriods)
                    chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(period);

            //存储其他费用分时
            List<ChargeBillingRltPeriodsBo> oItemPeriods = cbrBo.getItemRltPeriods();
            LOGGER.debug("billRpc-其他费分时计算后:" + JsonUtil.obj2Json(oItemPeriods));
            if (oItemPeriods != null)
                for (ChargeBillingRltPeriodsBo period : oItemPeriods)
                    chargeBillingRltPeriodsDao.insertChargeBillingRltPeriods(period);
        }
        //6、返回数据
        Map<String,Object> returnMap = Maps.newHashMap();
        returnMap.put("prodBillId", cbrBo.getAppNo());//产品订单实例ID
        returnMap.put("billAmt", cbrBo.getAmt());//总金额
        List<Map<String,Object>> prodBillDet = new LinkedList<Map<String,Object>>();
        returnMap.put("prodBillDet", prodBillDet);
        if(ChargeConstant.billCtlMode.LOCAL.equals(cbrBo.getBillCtlMode())){//本地计费
            Map<String,Object> prodOne = Maps.newHashMap();
            prodBillDet.add(prodOne);
            prodOne.put("prodId", cbrBo.getChcNo());
            prodOne.put("prodName", "充电费");
            prodOne.put("billDetNum", cbrBo.gettPq());
            prodOne.put("billDetAmt", cbrBo.gettAmt());
            List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
            prodOne.put("pricingCombine", pricingCombine);
            String details = "";
            String beginStr = null;
            String endStr = null;
            if(cbrList != null && cbrList.size() > 0){
                for(ChargeBillingRltPeriodsBo prerod : cbrList){
                    details += prerod.getBeginTime() + "~" + prerod.getEndTime() + "  " + FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
                    if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
                        beginStr = prerod.getPrice();
                    }
                    if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
                        endStr = prerod.getPrice();
                    }
                }
            }else{
                details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
                if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
                    beginStr = cbrBo.getChargePrice();
                }
                if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
                    endStr = cbrBo.getChargePrice();
                }
            }
            Map<String,Object> pricing = Maps.newHashMap();
            pricingCombine.add(pricing);
            if(!cbRltUtil.isEq(beginStr, endStr))
                pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
            else
                pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
            pricing.put("pricingSectDesc", details);

            //服务费
            Map<String,Object> prodTwo = Maps.newHashMap();
            prodBillDet.add(prodTwo);
            prodTwo.put("prodId", cbrBo.getChcNo());
            prodTwo.put("prodName", "服务费");
            prodTwo.put("billDetNum", cbrBo.gettPq());
            prodTwo.put("billDetAmt", cbrBo.getItemTAmt());
            if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
                ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
                csCondition.setItemNo(cbrBo.getAttachItemNos());
                List<ChargeSerItemBo> csList = chargeSerItemDao.queryChargeSerItems(csCondition);
                if(csList != null){
                    Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
                    for(ChargeSerItemBo csi:csList){
                        m.put(csi.getItemNo(), csi);
                    }
                    String[] itemNo = cbrBo.getAttachItemNos().split(",");
                    String[] price = cbrBo.getAttachItemPrices().split(",");
                    String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
                    String[] itemChargeMode = cbrBo.getItemChargeMode().split(",");
                    String localSerPrice = "0";
                    if("toMoreStation".equals(chargeVesion)){//新版计费
                        for(int i = 0;i < itemNo.length;i ++){
                            if("1000001000".equals(itemNo[i])) {
                                ChargeSerItemBo bo = m.get(itemNo[i]);
                                if (bo != null) {
                                    List<Map<String, Object>> pricingCombine2 = new LinkedList<Map<String, Object>>();
                                    prodTwo.put("pricingCombine", pricingCombine2);
                                    Map<String, Object> pricing2 = Maps.newHashMap();
                                    pricingCombine2.add(pricing2);
                                    localSerPrice = getSerPriceMinToMax(itemChargeMode[i],price[i]);
                                    pricing2.put("pricingSectName", "" + localSerPrice + "元/" + bo.getItemUnitValueName());
                                }
                                break;
                            }
                        }
                    }else{
                        ChargeSerItemBo bo = m.get(itemNo[0]);
                        if(bo != null){
                            List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
                            prodTwo.put("pricingCombine", pricingCombine2);
                            Map<String,Object> pricing2 = Maps.newHashMap();
                            pricingCombine2.add(pricing2);
                            pricing2.put("pricingSectName", "" + FormatUtil.formatNumber(price[0],4) + "元/" + bo.getItemUnitValueName());
                        }
                    }
                }
            }
        }else{//远程计费
            Map<String,Object> prodOne = Maps.newHashMap();
            prodBillDet.add(prodOne);
            prodOne.put("prodId", cbrBo.getChcNo());
            prodOne.put("prodName", "充电费");
            prodOne.put("billDetNum", cbrBo.gettPq());
            prodOne.put("billDetAmt", cbrBo.gettAmt());
            List<Map<String,Object>> pricingCombine = new LinkedList<Map<String,Object>>();
            prodOne.put("pricingCombine", pricingCombine);
            String details = "";
            String beginStr = null;
            String endStr = null;
            if(cbrList != null && cbrList.size() > 0){
                for(ChargeBillingRltPeriodsBo prerod : cbrList){
                    details += prerod.getBeginTime() + "~" + prerod.getEndTime() + "  " + FormatUtil.formatNumber(prerod.getPrice(),4) + "元/度;";
                    if(beginStr == null || cbRltUtil.isGt(beginStr, prerod.getPrice())){
                        beginStr = prerod.getPrice();
                    }
                    if(endStr == null || !cbRltUtil.isGt(endStr, prerod.getPrice())){
                        endStr = prerod.getPrice();
                    }
                }
            }else{
                details += "00:00~24:00" + "  " + FormatUtil.formatNumber(cbrBo.getChargePrice(),4) + "元/度;";
                if(beginStr == null || cbRltUtil.isGt(beginStr, cbrBo.getChargePrice())){
                    beginStr = cbrBo.getChargePrice();
                }
                if(endStr == null || !cbRltUtil.isGt(endStr, cbrBo.getChargePrice())){
                    endStr = cbrBo.getChargePrice();
                }
            }
            Map<String,Object> pricing = Maps.newHashMap();
            pricingCombine.add(pricing);
            LOGGER.debug(">>>>>>>>>>>>>>>>>计费充电费：beginStr:"+beginStr+",endStr:"+endStr);
            if(!cbRltUtil.isEq(beginStr, endStr))
                pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) + "-" + FormatUtil.formatNumber(endStr,4) + "元/度");
            else
                pricing.put("pricingSectName", "  " + FormatUtil.formatNumber(beginStr,4) +  "元/度");
            pricing.put("pricingSectDesc", details);
            //获取服务费用项
            if(!StringUtils.isEmpty(cbrBo.getAttachItemNos())){
                ChargeSerItemQueryCondition csCondition = new ChargeSerItemQueryCondition();
                csCondition.setItemNo(cbrBo.getAttachItemNos());
                List<ChargeSerItemBo> csList = chargeSerItemDao.queryChargeSerItems(csCondition);
                if(csList != null){
                    Map<String,ChargeSerItemBo> m = new HashMap<String,ChargeSerItemBo>();
                    for(ChargeSerItemBo csi:csList){
                        m.put(csi.getItemNo(), csi);
                    }
                    String[] itemNo = cbrBo.getAttachItemNos().split(",");
                    String[] price = cbrBo.getAttachItemPrices().split(",");
                    String[] nums = cbrBo.getAttachItemNums().split(",");
                    String[] amts = cbrBo.getAttachItemAmts().split(",");
                    String[] itemChargeMode = cbrBo.getItemChargeMode().split(",");
                    String serPrice = "0";
                    for(int i = 0;i < itemNo.length;i ++){
                        ChargeSerItemBo bo = m.get(itemNo[i]);
                        if(bo != null){
                            serPrice = getSerPriceMinToMax(itemChargeMode[i],price[i]);
                            Map<String,Object> prodTwo = Maps.newHashMap();
                            prodBillDet.add(prodTwo);
                            prodTwo.put("prodId", bo.getItemNo());
                            prodTwo.put("prodName", bo.getItemName());
                            prodTwo.put("billDetNum", nums[i]);
                            prodTwo.put("billDetAmt", amts[i]);
                            prodTwo.put("price", serPrice);
                            List<Map<String,Object>> pricingCombine2 = new LinkedList<Map<String,Object>>();
                            prodTwo.put("pricingCombine", pricingCombine2);
                            Map<String,Object> pricing2 = Maps.newHashMap();
                            pricingCombine2.add(pricing2);
                            pricing2.put("pricingSectName", "" + serPrice + "元/" + bo.getItemUnitValueName());
                        }
                    }
                }
            }
        }
        return returnMap;
    }

    @Override
    public List<BillRltPeriodsBo> getChargeBillRltPeriod(Map map) {
        //20220513返回数据过滤电量为0的数据
        List<BillRltPeriodsBo> result = new ArrayList<>();
        //查询订单分时结果
        List<BillRltPeriodsBo> chargeBillRltPeriodList = chargeBillingRltPeriodsDao.getChargeBillRltPeriod(map);
        if(chargeBillRltPeriodList != null && chargeBillRltPeriodList.size()>0){
            //查询订单对应计费模板
            map.put("itemNo","1000000000");
            List<Map> chargeBillRltModelList = peakService.getChargeBillRltModel(map);
            if (chargeBillRltModelList != null && chargeBillRltModelList.size() > 0) {
                //电费
                chargeBillRltPeriodList = chargeBillingRltPeriodsDao.getChargeBillRltPeriod(map);
                //0201标准 0202分时
                if(chargeBillRltPeriodList != null && chargeBillRltPeriodList.size()>0){
                    if ("0202".equals(chargeBillRltModelList.get(0).get("chargeMode"))) {
                        for (BillRltPeriodsBo billRltPeriodsBo : chargeBillRltPeriodList) {
                            for (Map chargeBillRltModel : chargeBillRltModelList) {
                                //金额为空
                                if (billRltPeriodsBo.getTimeFlag().equals(chargeBillRltModel.get("timeFlag"))
                                        && MathUtils.compareTo(BigDecimal.ZERO.toPlainString(), billRltPeriodsBo.getPq()) < 0) {
                                    String periodPrice = StringUtil.nullToString(chargeBillRltModel.get("periodPrice"));
                                    String times = StringUtil.nullToString(chargeBillRltModel.get("times"));
                                    if (!StringUtil.isNotBlank(billRltPeriodsBo.getPrice())) {
                                        billRltPeriodsBo.setPrice(periodPrice);
                                    }
                                    if (!StringUtil.isNotBlank(billRltPeriodsBo.getAmt())) {
                                        if (billRltPeriodsBo.getPq() != null) {
                                            billRltPeriodsBo.setAmt(MathUtils.multiply(periodPrice, billRltPeriodsBo.getPq()));
                                        }
                                    }
                                    billRltPeriodsBo.setTimes(times);
                                    //如果电量不为空则添加
                                    result.add(billRltPeriodsBo);
                                    break;
                                }
                            }
                        }
                        //如果服务费分时，匹配服务费
                        String attachItemNoStr = StringUtil.nullToString(chargeBillRltModelList.get(0).get("attachItemNos"));
                        String itemChargeModeStr = StringUtil.nullToString(chargeBillRltModelList.get(0).get("itemChargeMode"));
                        String[] attachItemNos = attachItemNoStr.split(",");
                        String[] itemChargeModes = itemChargeModeStr.split(",");
                        for(int i = 0;i < attachItemNos.length;i++){
                            if ("1000001000".equals(attachItemNos[i]) && ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(itemChargeModes[i])){
                                //有服务费并且是分时，匹配服务费
                                map.put("itemNo","1000001000");
                                List<BillRltPeriodsBo> serviceBillRltPeriodList = chargeBillingRltPeriodsDao.getChargeBillRltPeriod(map);
                                for (BillRltPeriodsBo billRltPeriodsBo : result) {
                                    for (BillRltPeriodsBo serBilRltPeriodsBo : serviceBillRltPeriodList){
                                        if (billRltPeriodsBo.getTimeFlag().equals(serBilRltPeriodsBo.getTimeFlag())
                                                && MathUtils.compareTo(BigDecimal.ZERO.toPlainString(), serBilRltPeriodsBo.getPq()) < 0){
                                            billRltPeriodsBo.setSerUnitPrice(serBilRltPeriodsBo.getPrice());
                                            String amt = serBilRltPeriodsBo.getAmt();
                                            billRltPeriodsBo.setSerPrice(amt);
                                            if (billRltPeriodsBo.getPq() != null && amt == null) {
                                                billRltPeriodsBo.setSerPrice(FormatUtil.formatNumber(MathUtils.multiply(serBilRltPeriodsBo.getPrice(), billRltPeriodsBo.getPq()),2));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        for (BillRltPeriodsBo billRltPeriodsBo : chargeBillRltPeriodList) {
                            String price = StringUtil.nullToString(chargeBillRltModelList.get(0).get("chargePrice"));
                            if (!StringUtil.isNotBlank(billRltPeriodsBo.getPrice())) {
                                billRltPeriodsBo.setPrice(price);
                            }
                            if (billRltPeriodsBo.getPq() != null) {
                                billRltPeriodsBo.setAmt(MathUtils.multiply(price,billRltPeriodsBo.getPq()));
                            }
                        }
                        return chargeBillRltPeriodList;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 查询计费描述
     * @param inMap
     * @return
     * @throws Exception
     */
    @Override
    public List<Map<String, Object>> getNewElecAmtRemark(Map<String, Object> inMap) throws Exception {
        List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
        String  chcNoCd = StringUtil.nullToString(inMap.get("chcNo"));
        inMap.put("chcNo", chcNoCd);
        String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
        //1、查询计费配置
        List<Map> confs = chargeBillRpcDao.queryChargeBillingConfs(inMap);
        if(confs != null && confs.size() > 0) {
            StringBuilder chcNoSb = new StringBuilder();
            StringBuilder itemNoSb = new StringBuilder();
            List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
            Map conf = confs.get(0);
            String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
            String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
            stationConfs.add(conf);
            chcNoSb.append(chcNo).append(",");
            itemNoSb.append(itemNo).append(",");

            //2、查询充电分时设置
            String chcNos = chcNoSb.toString();
            if (!StringUtils.isEmpty(chcNos)) {
                chcNos = chcNos.substring(0, chcNos.length() - 1);
                List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
                List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
                MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
            }
            //3、查询服务费用
            Map<String, Object> itemMap = null;
            if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
                String itemNos = itemNoSb.toString();
                if (!StringUtils.isEmpty(itemNos)) {
                    itemNos = itemNos.substring(0, itemNos.length() - 1);
                    ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
                    bo.setItemNo(itemNos);
                    List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
                    if (csItems != null && csItems.size() > 0) {
                        itemMap = new HashMap<String, Object>();
                        for (ChargeSerItemBo csBo : csItems)
                            itemMap.put(csBo.getItemNo(), csBo);
                    }
                }
            }
            //4、组合数据
            for(Map sconf :confs){
                Map<String,Object> stationCharge = Maps.newHashMap();
                stationCharge.put("eftDate",sconf.get("EFT_DATE"));
                stationCharges.add(stationCharge);
                Double price = null;
                String details = "";
                //充电费
                if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
                    price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
                    details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE")!=null?String.valueOf(sconf.get("CHARGE_PRICE")):"",4,2)  + "元/度;";
                }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
                    List<Map> periods = (List<Map>)sconf.get("periods");
                    if(periods != null){
                        double periodPrice = 0.0;
                        for(Map prerod : periods) {
                            if (ChargeConstant.itemTypeNo.CHARGE.equals(prerod.get("ITEM_NO"))) {
                                details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") + " " + FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度;";
                                double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
                                if ("toMoreStation".equals(chargeVesion)) {
                                    String beginTime = StringUtil.nullToString(prerod.get("BEGIN_TIME"));
                                    String endTime = StringUtil.nullToString(prerod.get("END_TIME"));
                                    if (isBetweenDate(beginTime, endTime)) {
                                        periodPrice = p;
                                    }
                                } else {
                                    if (p > periodPrice) {
                                        periodPrice = p;
                                    }
                                }
                            }
                            price = periodPrice;
                        }
                    }
                }
                stationCharge.put("elecAmt", FormatUtil.formatNumber(String.valueOf(price),4,2) +  "元/度");
                stationCharge.put("elecAmtRemark", details);
//					LOGGER.debug("充电费 price：" + price);
                //服务费
                if(itemMap != null){
                    double servicePrice = 0.0;
                    String unit = "度";
                    String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
                    String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
                    String itemChargeMode = cbRltUtil.returnNotNull(sconf.get("ITEM_CHARGE_MODE"));
                    LOGGER.debug("ITEM_CHARGE_MODE:{}",IJsonUtil.obj2Json(sconf));
                    if(!StringUtils.isEmpty(itemNos)){
                        String[] iNos = itemNos.split(",");
                        String[] iPrices = itemPrices.split(",");
                        String[] iChargeMode = itemChargeMode.split(",");
                        List<Map> periods = (List<Map>) sconf.get("periods");
                        for(int i = 0;i < iNos.length ; i ++){
                            ChargeSerItemBo csBo = (ChargeSerItemBo)itemMap.get(iNos[i]);
                            double iprice = Double.parseDouble(getItemInfo(csBo, periods,iPrices[i],iChargeMode[i]));
                            if("1000001000".equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)){
//									double iprice = Double.parseDouble(iPrices[i]);
                                servicePrice = iprice;
                                unit=csBo.getItemUnitValueName();
                            }
                            if("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)){
//									double iprice = Double.parseDouble(iPrices[i]);
                                if(iprice > servicePrice){
                                    servicePrice = iprice;
                                    unit=csBo.getItemUnitValueName();
                                }
                            }
                        }
                    }
                    stationCharge.put("servicePrice", FormatUtil.formatNumber(String.valueOf(servicePrice),4,2) +"元/"+unit);
                }
            }
        }
        return stationCharges;
    }

    /**
     * @param inMap
     * @description 查询当前时间 充电费+服务费/充电费分时列表+其他费列表
     * <AUTHOR>
     * @create 2018-05-21 17:35:04
     */
    @Override
    public List<Map<String, Object>> getCurChargePriceRemark(Map<String,Object> inMap) throws Exception{
        List<Map<String, Object>> stationList = (List<Map<String, Object>>) inMap.get("stationList");
        List<Map<String, Object>> stationCharges = new LinkedList<Map<String, Object>>();
        if (stationList != null || inMap.get("stationId") != null) {
            inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
            inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
            String stationIds = StringUtil.nullForString(inMap.get("stationId"));
            if (!StringUtil.isBlank(stationIds)) {
                inMap.put("stationIds", stationIds.split(","));
            }
            //1、查询计费配置

            inMap.put("groupByStation", "true");
            List<Map> confs = getChargeConfs(inMap);
            if (confs != null && confs.size() > 0) {
                String chargeVesion = sysParamService.getSysParamsValues("chargeVesion");
                String currentStationId = null;
                StringBuilder chcNoSb = new StringBuilder();
                StringBuilder itemNoSb = new StringBuilder();
                List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
                for (Map conf : confs) {
                    String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
                    String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
                    String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
                    if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
                        currentStationId = stationId;
                        stationConfs.add(conf);
                        chcNoSb.append(chcNo).append(",");
                        itemNoSb.append(itemNo).append(",");
                    } else {//如果不是第一个，将多余的充电计费配置状态改为无效
                        if (!"".equals(chcNo)) {
                            ChargeBillingConfBo cbc = new ChargeBillingConfBo();
                            cbc.setChcNo(chcNo);
                            cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
                            chargeBillingConfDao.updateChargeBillingConf(cbc);
                        }
                    }
                }
                //2、查询充电分时设置
                String chcNos = chcNoSb.toString();
                if (!StringUtils.isEmpty(chcNos)) {
                    chcNos = chcNos.substring(0, chcNos.length() - 1);
                    List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
                    List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
                    MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
                }
                //3、查询服务费用
                Map<String, Object> itemMap = null;
                if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
                    String itemNos = itemNoSb.toString();
                    if (!StringUtils.isEmpty(itemNos)) {
                        itemNos = itemNos.substring(0, itemNos.length() - 1);
                        ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
                        bo.setItemNo(itemNos);
                        List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
                        if (csItems != null && csItems.size() > 0) {
                            itemMap = new HashMap<String, Object>();
                            for (ChargeSerItemBo csBo : csItems)
                                itemMap.put(csBo.getItemNo(), csBo);
                        }
                    }
                }
                //4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
                for (Map sconf : stationConfs) {
                    List<Map> chargeIteamList = new ArrayList();
                    List inceChargeItem = new ArrayList();
                    Map<String, Object> stationCharge = Maps.newHashMap();
                    stationCharge.put("stationId", sconf.get("STATION_ID"));
                    stationCharge.put("billCtlMode", sconf.get("BILL_CTL_MODE"));
                    stationCharge.put("chargeMode", sconf.get("CHARGE_MODE"));
                    stationCharge.put("gathErDataType", sconf.get("GATHER_DATA_TYPE"));
                    Double price = null;
                    String details = "";
                    //充电费
                    if (ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))) {//标准计费
                        price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
                        details += "00:00~24:00" + "  " + FormatUtil.formatNumber(sconf.get("CHARGE_PRICE") != null ? String.valueOf(sconf.get("CHARGE_PRICE")) : "", 4, 2) + "元/度;";
                    } else if (ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))) {//分时计费
                        List<Map> periods = (List<Map>) sconf.get("periods");
                        if (periods != null) {
                            double periodPrice = 0.0;
                            for (Map prerod : periods) {
                                if (ChargeConstant.itemTypeNo.CHARGE.equals(prerod.get("ITEM_NO"))) {
                                    details += prerod.get("BEGIN_TIME") + "~" + prerod.get("END_TIME") + " " + FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度;";
                                    double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
                                    String beginTime = StringUtil.nullToString(prerod.get("BEGIN_TIME"));
                                    String endTime = StringUtil.nullToString(prerod.get("END_TIME"));
                                    if (isBetweenDate(beginTime, endTime)) {
                                        periodPrice = p;
                                    }

                                    Map chargeIteam = new HashMap();
                                    chargeIteam.put("startValue", prerod.get("BEGIN_TIME"));
                                    chargeIteam.put("endValue", prerod.get("END_TIME"));
                                    chargeIteam.put("timeFlag", prerod.get("TIME_FLAG"));
                                    chargeIteam.put("sn", prerod.get("SN"));
                                    chargeIteam.put("chargePrice", FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度");
                                    chargeIteamList.add(chargeIteam);
                                }
                                price = periodPrice;
                            }

                            //如果有服务费匹配服务费
                            for (Map prerod : periods) {
                                if (ChargeConstant.itemTypeNo.SERVICE.equals(prerod.get("ITEM_NO"))) {
                                    for (Map item : chargeIteamList) {
                                        if (item.get("sn").equals(prerod.get("SN"))) {
                                            item.put("servicePrice", FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2) + "元/度");
                                        }
                                    }
                                }
                            }

                        }
                    }
                    stationCharge.put("chargeAmt", FormatUtil.formatNumber(String.valueOf(price), 4, 2) + "元/度");
                    stationCharge.put("chargeAmtNum", FormatUtil.formatNumber(String.valueOf(price), 4, 2));
                    stationCharge.put("elecAmtRemark", details);

//					LOGGER.debug("充电费 price：" + price);
                    //服务费
                    if (itemMap != null) {
                        double servicePrice = 0.0;
                        String unit = "度";
                        String itemNos = cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
                        String itemPrices = cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
                        String itemChargeMode = cbRltUtil.returnNotNull(sconf.get("ITEM_CHARGE_MODE"));
                        if (!StringUtils.isEmpty(itemNos)) {
                            String[] iNos = itemNos.split(",");
                            String[] iPrices = itemPrices.split(",");
                            String[] itemModes = itemChargeMode.split(",");
                            List<Map> periods = (List<Map>) sconf.get("periods");
                            for (int i = 0; i < iNos.length; i++) {
                                ChargeSerItemBo csBo = (ChargeSerItemBo) itemMap.get(iNos[i]);
                                double iprice = Double.parseDouble(getItemInfo(csBo, periods, iPrices[i], itemModes[i]));
                                if ("1000001000".equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)) {
                                    servicePrice = iprice;
                                    unit = csBo.getItemUnitValueName();
                                }
                                if ("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)) {
                                    if (iprice > servicePrice) {
                                        servicePrice = iprice;
                                        unit = csBo.getItemUnitValueName();
                                    }
                                }

                                Map<String, Object> prodTwo = Maps.newHashMap();
                                prodTwo.put("itemCode", csBo.getItemNo());
                                prodTwo.put("itemName", csBo.getItemName());
                                prodTwo.put("itemPrice", iprice);
                                prodTwo.put("itemUnitName", csBo.getItemUnitValueName());
                                prodTwo.put("itemDesc", csBo.getRemarks());
                                inceChargeItem.add(prodTwo);
                            }
                        }
                        price += servicePrice;
                        stationCharge.put("serviceAmt", FormatUtil.formatNumber(String.valueOf(servicePrice), 4, 2) + "元/" + unit);
                        stationCharge.put("serviceAmtNum", FormatUtil.formatNumber(String.valueOf(servicePrice), 4, 2));


                        for (Map item : chargeIteamList) {
                            if (!item.containsKey("servicePrice")) {
                                item.put("servicePrice", FormatUtil.formatNumber(String.valueOf(servicePrice), 4, 2) + "元/度");
                            }
                        }

                    }
                    stationCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(price), 4, 4) + "元/度");
                    stationCharge.put("priceRemarkNum", FormatUtil.formatNumber(String.valueOf(price), 4, 4));
                    stationCharge.put("charge-item-list", chargeIteamList);
                    stationCharge.put("increment-item-list", inceChargeItem);
                    stationCharges.add(stationCharge);
                }
            }
        }
        return stationCharges;
    }

    @Override
    public List<Map<String, Object>> getXpChargePriceRemark(Map<String, Object> inMap) throws Exception {
        List<Map<String,Object>> stationList = (List<Map<String,Object>>)inMap.get("stationList");
        List<Map<String,Object>> stationCharges = new LinkedList<Map<String,Object>>();
        if(stationList != null || inMap.get("stationId") != null){
            inMap.put("chcStatus", ChargeConstant.validFlag.ENABLE);
            inMap.put("endEftDate", JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm"));
            String stationIds = StringUtil.nullForString(inMap.get("stationId"));
            if(!StringUtil.isBlank(stationIds)){
                inMap.put("stationIds", stationIds.split(","));
            }
            //1、查询计费配置
            List<Map> confs = getChargeConfs(inMap);
            if(confs != null && confs.size() > 0) {
                String currentStationId = null;
                StringBuilder chcNoSb = new StringBuilder();
                StringBuilder itemNoSb = new StringBuilder();
                List<Map<String, Object>> stationConfs = new LinkedList<Map<String, Object>>();
                for (Map conf : confs) {
                    String stationId = cbRltUtil.returnNotNull(conf.get("STATION_ID"));
                    String chcNo = cbRltUtil.returnNotNull(conf.get("CHC_NO"));
                    String itemNo = cbRltUtil.returnNotNull(conf.get("ATTACH_ITEM_NOS"));
                    if (currentStationId == null || !currentStationId.equals(stationId)) {//第一个stationId
                        currentStationId = stationId;
                        stationConfs.add(conf);
                        chcNoSb.append(chcNo).append(",");
                        itemNoSb.append(itemNo).append(",");
                    } else {//如果不是第一个，将多余的充电计费配置状态改为无效
                        if (!"".equals(chcNo)) {
                            ChargeBillingConfBo cbc = new ChargeBillingConfBo();
                            cbc.setChcNo(chcNo);
                            cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
                            chargeBillingConfDao.updateChargeBillingConf(cbc);
                        }
                    }
                }
                //2、查询充电分时设置
                String chcNos = chcNoSb.toString();
                if (!StringUtils.isEmpty(chcNos)) {
                    chcNos = chcNos.substring(0, chcNos.length() - 1);
                    List<Map> periods = peakService.queryChargePeriods(null, chcNos.split(","));
                    List pds = MergeUtil.mergeForSelf(periods, "CHC_NO");
                    MergeUtil.mergeList(stationConfs, pds, "CHC_NO", new String[]{"periods"}, new String[]{"list"});
                }
                //3、查询服务费用
                Map<String, Object> itemMap = null;
                if ("0".equals(cbRltUtil.returnNotNull(inMap.get("prodType")))) {//所有产品
                    String itemNos = itemNoSb.toString();
                    if (!StringUtils.isEmpty(itemNos)) {
                        itemNos = itemNos.substring(0, itemNos.length() - 1);
                        ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
                        bo.setItemNo(itemNos);
                        List<ChargeSerItemBo> csItems = chargeSerItemService.queryChargeSerItems(bo);
                        if (csItems != null && csItems.size() > 0) {
                            itemMap = new HashMap<String, Object>();
                            for (ChargeSerItemBo csBo : csItems)
                                itemMap.put(csBo.getItemNo(), csBo);
                        }
                    }
                }
                //4、组合数据
//				LOGGER.debug("组合数据量：" + stationConfs.size());
                for(Map sconf : stationConfs){
                    List chargeIteamList = new ArrayList();
                    List inceChargeItem = new ArrayList();
                    Map<String,Object> stationCharge = Maps.newHashMap();
                    stationCharge.put("stationId", sconf.get("STATION_ID"));
                    Double price = null;
                    //充电费
                    if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//标准计费
                        price = Double.parseDouble(String.valueOf(sconf.get("CHARGE_PRICE")));
                        stationCharge.put("chargeAmt", price);
                    }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(String.valueOf(sconf.get("CHARGE_MODE")))){//分时计费
                        List<Map> periods = (List<Map>)sconf.get("periods");
                        if(periods != null){
                            double periodPrice = 0.0;
                            for(Map prerod : periods){
                                if (ChargeConstant.itemTypeNo.CHARGE.equals(prerod.get("ITEM_NO"))) {
                                    double p = Double.parseDouble(String.valueOf(prerod.get("PRICE")));
                                    String beginTime = StringUtil.nullToString(prerod.get("BEGIN_TIME"));
                                    String endTime = StringUtil.nullToString(prerod.get("END_TIME"));
                                    if (isBetweenDate(beginTime, endTime)) {
                                        periodPrice = p;
                                    }

                                    Map chargeIteam = new HashMap();
                                    chargeIteam.put("startValue", prerod.get("BEGIN_TIME"));
                                    chargeIteam.put("endValue", prerod.get("END_TIME"));
                                    chargeIteam.put("timeFlag", prerod.get("TIME_FLAG"));
                                    chargeIteam.put("chargePrice", FormatUtil.formatNumber(prerod.get("PRICE") != null ? String.valueOf(prerod.get("PRICE")) : "", 4, 2));
                                    chargeIteamList.add(chargeIteam);
                                }
                                price = periodPrice;
                                stationCharge.put("chargeAmt", FormatUtil.formatNumber(String.valueOf(periodPrice),4,2));
                                stationCharge.put("chargeAmtUnit", "元/度");
                            }
                        }
                    }
//					LOGGER.debug("充电费 price：" + price);
                    //服务费
                    if(itemMap != null){
                        double servicePrice = 0.0;
                        String unit ="";
                        String itemNos =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_NOS"));
                        String itemPrices =cbRltUtil.returnNotNull(sconf.get("ATTACH_ITEM_PRICES"));
                        String itemChargeMode = cbRltUtil.returnNotNull(sconf.get("ITEM_CHARGE_MODE"));
                        if(!StringUtils.isEmpty(itemNos)){
                            String[] iNos = itemNos.split(",");
                            String[] iPrices = itemPrices.split(",");
                            String[] iChargeMode = itemChargeMode.split(",");
                            String chargeVesion= sysParamService.getSysParamsValues("chargeVesion");
                            List<Map> periods = (List<Map>) sconf.get("periods");
                            for(int i = 0;i < iNos.length ; i ++){
                                ChargeSerItemBo csBo = (ChargeSerItemBo)itemMap.get(iNos[i]);
                                double iprice = Double.parseDouble(getItemInfo(csBo, periods,iPrices[i],iChargeMode[i]));
                                if("1000001000".equals(csBo.getItemNo()) && "toMoreStation".equals(chargeVesion)){
                                    servicePrice = iprice;
                                    unit=csBo.getItemUnitValueName();
                                }
                                if("服务费".equals(csBo.getItemName()) && !"toMoreStation".equals(chargeVesion)){
//									double iprice = Double.parseDouble(iPrices[i]);
                                    if(iprice > servicePrice){
                                        servicePrice = iprice;
                                        unit=csBo.getItemUnitValueName();
                                    }
                                }

                                Map<String,Object> prodTwo = Maps.newHashMap();
                                prodTwo.put("itemCode", csBo.getItemNo());
                                prodTwo.put("itemName", csBo.getItemName());
                                prodTwo.put("itemPrice", iprice);
                                prodTwo.put("itemUnitName", csBo.getItemUnitValueName());
                                prodTwo.put("itemDesc", csBo.getRemarks());
                                inceChargeItem.add(prodTwo);
                            }
                        }
                        price += servicePrice;
                        stationCharge.put("serviceAmt", FormatUtil.formatNumber(String.valueOf(servicePrice),4,2));
                        stationCharge.put("serviceAmtUnit", unit);

                    }
                    stationCharge.put("priceRemark", FormatUtil.formatNumber(String.valueOf(price),4,4) +  "元/度");
                    stationCharge.put("charge-item-list", chargeIteamList);
                    stationCharge.put("increment-item-list", inceChargeItem);
                    stationCharges.add(stationCharge);
                }
            }
        }
        return stationCharges;
    }

    /**
     * 将数据组合成下发到车联网的格式(分时服务费)
     * */
    @Override
    public List<String> combinateFormat(List<Map<String,String>> piles,ChargeBillingConfBo cbcBo,String dataSource){
        List<String> combinates = new LinkedList<String>();
        //服务费分时标准
        boolean serShareFlag = false;
        if(piles!=null && piles.size() > 0){
            for(Map<String,String> pile : piles){
                Map<String,Object> combinate = new HashMap<String,Object>();
                if("1".equals(dataSource)){//没有gun的下发
                    LOGGER.error("--------------------------没有枪的电价下发----------------------------------");
                }else if("2".equals(dataSource)){//dataSource=2，则必填。equipNo为桩编号
                    combinate.put("operatorId", pile.get("operatorId"));//运营商标识
                    combinate.put("equipNo", pile.get("pileNo"));//桩编号
                }else if("3".equals(dataSource)){//桩id
                    combinate.put("equipId", String.valueOf(pile.get("pileId")));//桩id
                }
                combinate.put("equipType", "0202");//充电枪
                combinate.put("dataSource", dataSource);//传递桩编号
                combinate.put("pricingId", cbcBo.getChcNo());//定价ID/计费模型ID
                combinate.put("effTime", cbcBo.getEftDate());//桩编号
                if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbcBo.getChargeMode())){//如果是标准计费方式
                    combinate.put("timeSharFlag", "0");//分时标志,不分时
                    combinate.put("rate", cbcBo.getChargePrice());//非分时的费率
                }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbcBo.getChargeMode())){//如果是分时计费方式
                    combinate.put("timeSharFlag", "1");//分时标志,分时

                    List<Map<String,String>> cperiodList = new LinkedList<Map<String,String>>();

                    Map<String,String> serviceMap = new HashedMap();
                    for(ChargePeriodsBo cperiod : cbcBo.getCperiods()){
                        Map<String,String> cperiodMap = Maps.newHashMap();
                        if (ChargeConstant.itemTypeNo.SERVICE.equals(cperiod.getItemNo())){
                            //服务费分时
                            serShareFlag = true;
                            serviceMap.put(cperiod.getSn(),cperiod.getPrice());
                        }
                    }

                    int periodsNum = 0;
                    for(ChargePeriodsBo cperiod : cbcBo.getCperiods()){
                        Map<String,String> cperiodMap = Maps.newHashMap();
                        if (StringUtil.isBlank(cperiod.getItemNo()) || ChargeConstant.itemTypeNo.CHARGE.equals(cperiod.getItemNo())){
                            periodsNum++;
                            cperiodMap.put("sn", cperiod.getSn());//时段序号
                            cperiodMap.put("beginTime", cperiod.getBeginTime());//起始时间
                            cperiodMap.put("endTime", cperiod.getEndTime());//结束时间
                            cperiodMap.put("periodFlag", cperiod.getTimeFlag());//时段标志
                            cperiodMap.put("periodRate", cperiod.getPrice());//时段费率
                            cperiodList.add(cperiodMap);
                        }
                    }

                    combinate.put("periodsNum", periodsNum);//分时标志,分时

                    if (serShareFlag){
                        for (Map<String,String> map : cperiodList){
                            map.put("periodServiceRate",StringUtil.zeroForString(serviceMap.get(map.get("sn"))));
                        }
                    }

                    //组合服务费分时
                    combinate.put("periodsList", cperiodList);
                }
                if (serShareFlag){
                    //服务费分时，分时价格区间在
                    combinate.put("serPrice", "0.000000");
                }else{
                    //服务费
                    String attachItemPrices = cbcBo.getAttachItemPrices();
                    if(attachItemPrices != null && !"".equals(attachItemPrices)){
                        String [] prices = attachItemPrices.split(",");
                        combinate.put("serPrice", prices[0]);
                    }

                }
                combinates.add(IJsonUtil.obj2Json(combinate));
            }
        }
        LOGGER.debug("车桩联网计费下发出参：{}"+IJsonUtil.obj2Json(combinates));
        return combinates;
    }

    private String getSerPriceMinToMax(String itemChargeMode,String priceStr){
        if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(itemChargeMode)){
            String priceAry[] = priceStr.split("#");
            String serBeginStr = null;
            String serEndStr = null;
            for(String price : priceAry){
                if (serBeginStr == null || cbRltUtil.isGt(serBeginStr, price)) {
                    serBeginStr = FormatUtil.formatNumber(cbRltUtil.returnNotNull(price), 4, 2);
                }
                if (serEndStr == null || !cbRltUtil.isGt(serEndStr, cbRltUtil.returnNotNull(price))) {
                    serEndStr =	FormatUtil.formatNumber(cbRltUtil.returnNotNull(price), 4, 2);
                }
            }
            if(!cbRltUtil.isEq(serBeginStr, serEndStr)){
                return FormatUtil.formatNumber(serBeginStr,4,2) + "-" + FormatUtil.formatNumber(serEndStr,4,2);
            }else{
                return FormatUtil.formatNumber(serBeginStr,4,2);
            }

        }
        return priceStr;
    }

    /**
     * @description 获取费用项价格
     * <AUTHOR>
     * @create 2019-03-11 21:19:36
     */
    private String getItemPrice(ChargeSerItemBo bo, List<ChargePeriodsBo> cperiodsList) {
        String price = "";
        if (ChargeConstant.chcBillingChargeMode.STANDARD.equals(bo.getItemChargeMode())) {//标准
            price = FormatUtil.formatNumber(bo.getServicePrice(), 4);
        } else {
            for (ChargePeriodsBo cpBo : cperiodsList) {
                if (bo.getItemNo().equals(cpBo.getItemNo())) {
                    String itemPrice = FormatUtil.formatNumber(cpBo.getPrice(), 4);
                    String beginTime = StringUtil.nullToString(cpBo.getBeginTime());
                    String endTime = StringUtil.nullToString(cpBo.getEndTime());
                    if (isBetweenDate(beginTime, endTime)) {
                        price = itemPrice;
                    }
                }
            }
        }
        return price;
    }

    private String getSerPrice(String itemChargeMode, String priceStr) {
        if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(itemChargeMode)){
            String priceAry[] = priceStr.split("#");
            priceStr = MathUtils.parseDoubleStr(MathUtils.divide(MathUtils.add_obj(priceAry),priceAry.length+""),2);
        }
        return priceStr;
    }

    /**
     * 获取计费配置
     * @param inMap
     * @return
     */
    private List<Map> getChargeConfs(Map<String, Object> inMap) {
        List<Map> confs = new ArrayList<>();
        String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
        String isAssistPileBilling=sysParamService.getSysParamsValues("isAssistPileBilling"); // 是否支持桩计费
        if("toMoreStation".equals(chargeVesion)){//新版计费
            if(inMap.containsKey("groupId") && !CollectionUtils.isNotEmpty(confs)){//大客户计费
                confs = chargeBillRpcDao.queryCustGroupChargeBilling(inMap);
            }
            if ((inMap.containsKey("pileId") || inMap.containsKey("pileIds")) && PublicConstants.YN.TRUE.equals(isAssistPileBilling) && !CollectionUtils.isNotEmpty(confs)){
                confs = chargeBillRpcDao.queryNewChargePileBillingConfs(inMap); // 桩计费
            }else if(!CollectionUtils.isNotEmpty(confs)){
                confs=chargeBillRpcDao.queryNewChargeBillingConfs(inMap);//站点计费
            }
        }else{
            if(!CollectionUtils.isNotEmpty(confs)){
                confs=chargeBillRpcDao.queryChargeBillingConfs(inMap);
            }
        }
        return  confs;
    }

    private String getItemInfo(ChargeSerItemBo bo, List<Map> periods,String price,String itemMode) {
        if (ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(itemMode)) {//分时
            for (Map cpBo : periods) {
                if (bo.getItemNo().equals(cpBo.get("ITEM_NO"))) {
                    String itemPrice = FormatUtil.formatNumber(StringUtil.nullToString(cpBo.get("PRICE")), 4,4);
                    String beginTime = StringUtil.nullToString(cpBo.get("BEGIN_TIME"));
                    String endTime = StringUtil.nullToString(cpBo.get("END_TIME"));
                    price = itemPrice;
                    if (isBetweenDate(beginTime, endTime)) {
                        price = itemPrice;
                        break;
                    }
                }
            }
        } else {
            price = FormatUtil.formatNumber(price,4, 4);
        }
        return price;
    }

    /**
     * @param begin 10:00
     * @param end	12:00
     * @description 判断当前时间是否在时间段范围内
     * <AUTHOR>
     * @create 2018-05-16 16:11:28
     */
    public static boolean isBetweenDate(String beginT, String endT) {
        boolean flag = false;
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");//设置日期格式
        Date nowTime = null;
        Date beginTime = null;
        Date endTime = null;
        try {
            //格式化当前时间格式
            nowTime = df.parse(df.format(new Date()));
            //定义开始时间
            beginTime = df.parse(beginT);
            //定义结束时间
            endTime = df.parse(endT);

            //设置当前时间
            Calendar date = Calendar.getInstance();
            date.setTime(nowTime);
            //设置开始时间
            Calendar begin = Calendar.getInstance();
            begin.setTime(beginTime);
            //设置结束时间
            Calendar end = Calendar.getInstance();
            end.setTime(endTime);
            //处于开始时间之后，和结束时间之前的判断
            if ((date.after(begin) || date.equals(begin)) && date.before(end)) {
                flag = true;
            } else {
                flag = false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }
}
