package com.ls.ner.billing.api.rent.model;

import java.io.Serializable;
import java.util.List;

import com.ls.ner.billing.api.rent.model.RangeChargeItem;

public class SubChargeItem extends AbstractChargeItem implements Serializable{
/**
	 * 
	 */
	private static final long serialVersionUID = -4809204708174056396L;
//	/**
//	 * 分时和阶梯 区分区间的点
//	 */
//	protected String[] splitPoints;
	protected List<RangeChargeItem> rangeChargeItems;
	/**
	 * 总价上下限用 可以通过金额和租赁单位等方式来限定
	 * 里程限定 单位是公里
	 * 时间限定 单位是分钟
	 * <AUTHOR>
	 *
	 */
	public static class Limit implements Serializable{
		private static final long serialVersionUID = -7193868765709396340L;
		public static final String CODE_TYPE = "billingLimitType";
		/**
		 * 限制方式 - 总金额
		 */
		public static final String LIMIT_TYPE_BY_AMOUNT = "01";
		/**
		 * 限制方式 - 单位
		 */
		public static final String LIMIT_TYPE_BY_UNIT = "02";
		
		private String limitType;
		
		private long limitQuantity;

		public Limit(){
			
		}
		
		public Limit(String limitType,long limitQuantity){
			this.limitType = limitType;
			this.limitQuantity = limitQuantity;
		}
		
		public String getLimitType() {
			return limitType;
		}

		public void setLimitType(String limitType) {
			this.limitType = limitType;
		}

		public long getLimitQuantity() {
			return limitQuantity;
		}

		public void setLimitQuantity(long limitQuatity) {
			this.limitQuantity = limitQuatity;
		}

		@Override
		public String toString() {
			return "Limit [limitType=" + limitType + ", limitQuatity="
					+ limitQuantity + "]";
		} 
		
		
	}
	/**
	 * 收费下限
	 */
	private Limit min;
	/**
	 * 收费上限
	 */
	private Limit max;
	
	private String displayPrice;
	
	private String priceRemark;
//	public String[] getSplitPoints() {
//		return splitPoints;
//	}
//
//	public void setSplitPoints(String[] splitPoints) {
//		this.splitPoints = splitPoints;
//	}

	public List<RangeChargeItem> getRangeChargeItems() {
		return rangeChargeItems;
	}

	public void setRangeChargeItems(List<RangeChargeItem> rangeChargeItems) {
		this.rangeChargeItems = rangeChargeItems;
	}
	
	public Limit getMin() {
		return min;
	}

	public void setMin(Limit min) {
		this.min = min;
	}

	public Limit getMax() {
		return max;
	}

	public void setMax(Limit max) {
		this.max = max;
	}

	public String getDisplayPrice() {
		return displayPrice;
	}

	public void setDisplayPrice(String displayPrice) {
		this.displayPrice = displayPrice;
	}

	public String getPriceRemark() {
		return priceRemark;
	}

	public void setPriceRemark(String priceRemark) {
		this.priceRemark = priceRemark;
	}

	public void clearPrice(){
		super.clearPrice();
		if(rangeChargeItems != null){
			for(RangeChargeItem rangeChargeItem:rangeChargeItems){
				rangeChargeItem.clearPrice();
			}
		}
		
	}
	
	public void clearResult(){
		super.clearResult();
		if(rangeChargeItems != null){
			for(RangeChargeItem rangeChargeItem:rangeChargeItems){
				rangeChargeItem.clearResult();
			}
		}
	}
}
