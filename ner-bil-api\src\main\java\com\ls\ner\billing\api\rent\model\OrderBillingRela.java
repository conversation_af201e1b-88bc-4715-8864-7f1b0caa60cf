package com.ls.ner.billing.api.rent.model;

public class OrderBillingRela implements IBillingLocateCondition{
	
	//订单相关的
	private String appNo;//订单编号
	private String rentType;//用户选择的租赁类型
	private String selectedAttachItemNos;//用户选择的附加项编码
	
	//车相关的
	private String autoModelNo;
	private String orgCode;
	private String rtId;//租赁点内部标识
	private String rtNo;//租赁点外部标识
	
	private String planQcTime;//计划取车时间
	private String planHcTime;//计划还车时间
	private long meters;//默认里程数
	
	/**
	 * 是否保存计价结果数据（到数据库）的标志
	 */
	private boolean saveLogFlag;
	
	public String getAppNo() {
		return appNo;
	}
	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}
	public String getRentType() {
		return rentType;
	}
	public void setRentType(String rentType) {
		this.rentType = rentType;
	}
	public String getSelectedAttachItemNos() {
		return selectedAttachItemNos;
	}
	public void setSelectedAttachItemNos(String selectedAttachItemNos) {
		this.selectedAttachItemNos = selectedAttachItemNos;
	}
	@Override
	public String getAutoModelNo() {
		return autoModelNo;
	}
	@Override
	public void setAutoModelNo(String autoModelNo) {
		this.autoModelNo = autoModelNo;
	}
	@Override
	public String getOrgCode() {
		return orgCode;
	}
	@Override
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	@Override
	public String getRtId() {
		return rtId;
	}
	@Override
	public void setRtId(String rtId) {
		this.rtId = rtId;
	}
	@Override
	public String getRtNo() {
		return rtNo;
	}
	@Override
	public void setRtNo(String rtNo) {
		this.rtNo = rtNo;
	}
	public String getPlanQcTime() {
		return planQcTime;
	}
	public void setPlanQcTime(String planQcTime) {
		this.planQcTime = planQcTime;
	}
	public String getPlanHcTime() {
		return planHcTime;
	}
	public void setPlanHcTime(String planHcTime) {
		this.planHcTime = planHcTime;
	}
	public long getMeters() {
		return meters;
	}
	public void setMeters(long meters) {
		this.meters = meters;
	}
	public boolean isSaveLogFlag() {
		return saveLogFlag;
	}
	public void setSaveLogFlag(boolean saveLogFlag) {
		this.saveLogFlag = saveLogFlag;
	}
	
}
