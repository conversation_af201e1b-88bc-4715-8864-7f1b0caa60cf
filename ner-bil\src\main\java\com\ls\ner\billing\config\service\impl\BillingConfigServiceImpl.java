package com.ls.ner.billing.config.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.base.constants.BizConstants.DataOperType;
import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.code.CodeMethod;
import com.ls.ner.util.json.JsonMapper;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.BuyType;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeTimePoint;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.ItemStatus;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.rent.model.AbstractChargeItem;
import com.ls.ner.billing.api.rent.model.AppendChargeItem;
import com.ls.ner.billing.api.rent.model.BillingFunc;
import com.ls.ner.billing.api.rent.model.DepositChargeItem;
import com.ls.ner.billing.api.rent.model.MainChargeItem;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.api.rent.model.Range;
import com.ls.ner.billing.api.rent.model.RangeChargeItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem.Limit;
import com.ls.ner.billing.appendchargeitem.service.impl.AppendChargeItemServiceImpl;
import com.ls.ner.billing.common.bo.AppendChargeItemBo;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.common.bo.BillingConfigBoExample;
import com.ls.ner.billing.common.bo.BillingFuncBo;
import com.ls.ner.billing.common.bo.BillingFuncBoExample;
import com.ls.ner.billing.common.bo.BillingOrderPricingBo;
import com.ls.ner.billing.common.bo.BillingOrderPricingBoExample;
import com.ls.ner.billing.api.common.bo.BillingOrderRelaBo;
import com.ls.ner.billing.common.bo.BillingOrderRelaBoExample;
import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.common.dao.BillingConfigBoMapper;
import com.ls.ner.billing.common.dao.BillingFuncBoMapper;
import com.ls.ner.billing.common.dao.BillingOrderPricingBoMapper;
import com.ls.ner.billing.common.dao.BillingOrderRelaBoMapper;
import com.ls.ner.billing.config.bo.BillingConfigForm;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.dao.IBillingConfigDao;
import com.ls.ner.billing.config.service.IBillingConfigService;
import com.ls.ner.billing.tariff2.service.impl.TariffPlanServiceImpl;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;

@Component
public class BillingConfigServiceImpl implements IBillingConfigService {
	private static final Logger LOGGER = LoggerFactory.getLogger(BillingConfigServiceImpl.class);
	
	@Autowired
	TariffPlanServiceImpl tariffPlanServiceImpl;

	@Autowired
	AppendChargeItemServiceImpl appendChargeItemServiceImpl;
	
	@Autowired
	BillingConfigBoMapper billingConfigBoMapper;
	
	@Autowired
	BillingFuncBoMapper billingFuncBoMapper;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	ICodeService codeService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private Authentication authentication;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC,value="orgRpcService")
	private IOrgRpcService orgRpcService;
	
	@Autowired
	private BillingOrderRelaBoMapper billingOrderRelaBoMapper;
	
	@Autowired
	private BillingOrderPricingBoMapper billingOrderPricingBoMapper;
	
	@Autowired
	IBillingConfigDao billingConfigDao;
	
	/**
	 * 根据资费标准编号初始化定价表单
	 * 
	 * @param planNo
	 * @return
	 */
	public BillingConfigForm initFormByPlanNo(String planNo) {
		LOGGER.debug("initFormByPlanNo:{}", planNo);
		TariffPlanBo tariffPlanBo = tariffPlanServiceImpl.getByPlanNo(planNo);
		BillingFunc billingFunc = tariffPlanToBillingFunc(tariffPlanBo);
		BillingConfigForm billingConfigForm = new BillingConfigForm();
		// 其实不需要
		// billingConfigForm.setPlanNo(planNo);
		// billingConfigForm.setPlanName(tariffPlanBo.getPlanName());
		billingConfigForm.setSubBe(tariffPlanBo.getSubBe());
		billingConfigForm.setSubBeName(codeService.getStandardCode( SubBe.CODE_TYPE,tariffPlanBo.getSubBe(),null).getCodeName());
		billingConfigForm.setChargeWay(tariffPlanBo.getChargeWay());
		billingConfigForm.setChargeWayName(codeService.getStandardCode( ChargeWay.CODE_TYPE,tariffPlanBo.getChargeWay(),null).getCodeName());
		billingConfigForm.setChargeMode(tariffPlanBo.getChargeMode());
		billingConfigForm.setChargeModeName(codeService.getStandardCode( ChargeMode.CODE_TYPE,tariffPlanBo.getChargeMode(),null).getCodeName());
		billingConfigForm.setPlanName(tariffPlanBo.getPlanName());
		billingConfigForm.setBillingFunc(billingFunc);
		billingConfigForm.setPlanNo(planNo);
		return billingConfigForm;
	}

	public BillingFunc tariffPlanToBillingFunc(TariffPlanBo tariffPlanBo) {
		BillingFunc billingFunc = new BillingFunc();
		String chargeWay = tariffPlanBo.getChargeWay();
		String chargeMode = tariffPlanBo.getChargeMode();

		MainChargeItem mainChargeItem = new MainChargeItem();
		billingFunc.setMainChargeItem(mainChargeItem);
		mainChargeItem.setChargeWay(chargeWay);
		mainChargeItem.setChargeMode(chargeMode);
		mainChargeItem.setChargeTimePoint(ChargeTimePoint.POST);
		if (ChargeWay.BY_MILL.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			if (ChargeMode.STANDARD.equals(chargeMode)) {
				setStandardMillChargeItem(tariffPlanBo, mainChargeItem);
			} else if (ChargeMode.PERIOD.equals(chargeMode)) {
				setPeriodMillChargeItem(tariffPlanBo, mainChargeItem);
			} else if (ChargeMode.STEP.equals(chargeMode)) {
				setStepMillChargeItem(tariffPlanBo, mainChargeItem);
			}

		}
		if (ChargeWay.BY_TIME.equals(chargeWay)
				|| ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)) {
			if (ChargeMode.STANDARD.equals(chargeMode)) {
				setStandardTimeChargeItem(tariffPlanBo, mainChargeItem);
			} else if (ChargeMode.PERIOD.equals(chargeMode)) {
				setPeriodTimeChargeItem(tariffPlanBo, mainChargeItem);
			} else if (ChargeMode.STEP.equals(chargeMode)) {
				setStepTimeChargeItem(tariffPlanBo, mainChargeItem);
			}
		}
		
		// 押金和其他费用项
		// TODO 这里以后上缓存。。。就不用老在数据库冗余什么了。
		AppendChargeItemBo tempBo = new AppendChargeItemBo();
		tempBo.setItemStatus(ItemStatus.ENABLE);
		String[] depositItemNos = StringUtils.split(
				StringUtils.trimToEmpty(tariffPlanBo.getDepositItemNos()), ',');
		List<AppendChargeItemBo> depositChargeItemList = appendChargeItemServiceImpl
				.listDepositChargeItem(tempBo);
		billingFunc.setDepositChargeItems(trans2DepositChargeItems(
				depositItemNos, depositChargeItemList));
		List<AppendChargeItemBo> attachChargeItemList = appendChargeItemServiceImpl
				.listAttachChargeItem(tempBo);
		String[] attachItemNos = StringUtils.split(
				StringUtils.trimToEmpty(tariffPlanBo.getAttachItemNos()), ',');
		billingFunc.setAttachChargeItems(trans2AppendChargeItems(attachItemNos,
				attachChargeItemList));

		if (LOGGER.isDebugEnabled()) {
			LOGGER.debug("billingFunc json:{}", JsonMapper.nonEmptyMapper()
					.toJson(billingFunc));
		}

		return billingFunc;
	}

	protected List<DepositChargeItem> trans2DepositChargeItems(
			String[] itemNos, List<AppendChargeItemBo> allItemList) {
		Map<String, AppendChargeItemBo> depositChargeItemMap = appendChargeItemBoList2Map(allItemList);
		List<DepositChargeItem> list = Lists.newArrayList();
		for (String depositItemNo : itemNos) {
			AppendChargeItemBo bo = depositChargeItemMap.get(depositItemNo);
			String itemNo = bo.getItemNo();
			String itemName = bo.getItemName();
			DepositChargeItem chargeItem = new DepositChargeItem();
			chargeItem.setChargeTimePoint(ChargeTimePoint.POST);
			chargeItem.setItemNo(itemNo);
			chargeItem.setItemName(itemName);
//			chargeItem.setPrice("1.00");
			PriceUnit priceUnit = new PriceUnit();
			priceUnit.setUnit(bo.getItemUnit());
			setPriceUnitName(priceUnit);
			chargeItem.setPriceUnit(priceUnit);
			chargeItem.setBuyType(bo.getBuyType());
			chargeItem.setBuyTypeName(codeService.getStandardCode( BuyType.CODE_TYPE,bo.getBuyType(),null).getCodeName());
			chargeItem.setRemarks(bo.getRemarks());
			list.add(chargeItem);

		}
		return list;
	}

	protected List<AppendChargeItem> trans2AppendChargeItems(String[] itemNos,
			List<AppendChargeItemBo> allItemList) {
		Map<String, AppendChargeItemBo> depositChargeItemMap = appendChargeItemBoList2Map(allItemList);
		List<AppendChargeItem> list = Lists.newArrayList();
		for (String depositItemNo : itemNos) {
			AppendChargeItemBo bo = depositChargeItemMap.get(depositItemNo);
			String itemNo = bo.getItemNo();
			String itemName = bo.getItemName();
			AppendChargeItem chargeItem = new AppendChargeItem();
			chargeItem.setChargeTimePoint(ChargeTimePoint.POST);
			chargeItem.setItemNo(itemNo);
			chargeItem.setItemName(itemName);
//			chargeItem.setPrice("1.00");
			PriceUnit priceUnit = new PriceUnit();
			priceUnit.setUnit(bo.getItemUnit());
			setPriceUnitName(priceUnit);
			chargeItem.setPriceUnit(priceUnit);
			chargeItem.setBuyType(bo.getBuyType());
			chargeItem.setBuyTypeName(codeService.getStandardCode( BuyType.CODE_TYPE,bo.getBuyType(),null).getCodeName());
			chargeItem.setRemarks(bo.getRemarks());
			list.add(chargeItem);

		}
		return list;
	}

	protected void setPriceUnitName(PriceUnit priceUnit) {
		priceUnit.setUnitName(codeService.getStandardCode( PriceUnit.CODE_TYPE,priceUnit.getUnit(),null).getCodeName());
	}

	protected Map<String, AppendChargeItemBo> appendChargeItemBoList2Map(
			List<AppendChargeItemBo> chargeItemList) {
		Map<String, AppendChargeItemBo> map = Maps.newHashMap();
		for (AppendChargeItemBo appendChargeItemBo : chargeItemList) {
			map.put(appendChargeItemBo.getItemNo(), appendChargeItemBo);

		}
		return map;
	}

	protected void setStandardMillChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		SubChargeItem millChargeItem = new SubChargeItem();
		mainChargeItem.setMillChargeItem(millChargeItem);
		// 限制的值由页面设置，单位和主单位保持一致，所以这里不需要设置
		Limit min = new Limit();
		Limit max = new Limit();
		// min.setLimitType(tariffPlanBo.get);
	
		min.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
		//造数据用的。。。先看看设置完整之后的json长啥样
//				min.setLimitQuantity(20);
		max.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
//				max.setLimitQuantity(200);
		millChargeItem.setMin(min);
		millChargeItem.setMax(max);
		millChargeItem.setPrice("");
		millChargeItem.setPriceUnit(new PriceUnit(tariffPlanBo
				.getMillPerPriceValue(), tariffPlanBo.getMillPerPriceUnit()));
		setPriceUnitName(millChargeItem.getPriceUnit());
	}

	protected void setPeriodMillChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		// SubChargeItem millChargeItem = new SubChargeItem();
		// mainChargeItem.setMillChargeItem(millChargeItem);
		// String periodSplitPoints = tariffPlanBo.getPeriodSplitPoints();
		// PriceUnit priceUnit = new
		// PriceUnit(tariffPlanBo.getMillPerPriceValue(),tariffPlanBo.getMillPerPriceUnit());
		setStandardMillChargeItem(tariffPlanBo, mainChargeItem);
		String periodSplitPoints = tariffPlanBo.getPeriodSplitPoints();
		setPeriodRangeChargeItems(periodSplitPoints,
				mainChargeItem.getMillChargeItem());
	}

	protected void setPeriodRangeChargeItems(String periodSplitPoints,
			SubChargeItem millOrTimeChargeItem) {
		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
		millOrTimeChargeItem.setRangeChargeItems(rangeChargeItems);
		PriceUnit priceUnit = millOrTimeChargeItem.getPriceUnit();
		
		millOrTimeChargeItem.setPriceUnit(priceUnit);

		String[] periodSplitPointArray = periodSplitPoints.split("[\\s]+");
		
		if("00:00".equals(periodSplitPointArray[0])){
			for (int i = 0; i < periodSplitPointArray.length-1; i++) {
				String from = periodSplitPointArray[i];
				String to = periodSplitPointArray[i+1];
				Range range = new Range(from, to);

				RangeChargeItem rangeChargeItem = new RangeChargeItem();
				rangeChargeItem.setRange(range);
				rangeChargeItem.setPrice("");
				rangeChargeItem.setPriceUnit(priceUnit);
				rangeChargeItems.add(rangeChargeItem);

			}
		} else 
			for (int i = 0; i < periodSplitPointArray.length; i++) {
			String from = periodSplitPointArray[i];
			String to = (i == periodSplitPointArray.length-1) ? periodSplitPointArray[0]
					: periodSplitPointArray[i+1];
			Range range = new Range(from, to);

			RangeChargeItem rangeChargeItem = new RangeChargeItem();
			rangeChargeItem.setRange(range);
			rangeChargeItem.setPrice("");
			rangeChargeItem.setPriceUnit(priceUnit);
			rangeChargeItems.add(rangeChargeItem);

		}
	}

	protected void setStepMillChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		setStandardMillChargeItem(tariffPlanBo, mainChargeItem);
		String millStepSplitPoints = tariffPlanBo.getMillStepSplitPoints();
		setStepRangeChargeItems(millStepSplitPoints,
				mainChargeItem.getMillChargeItem());
	}

	protected void setStepRangeChargeItems(String stepSplitPoints,
			SubChargeItem millOrTimeChargeItem) {
		List<RangeChargeItem> rangeChargeItems = Lists.newArrayList();
		millOrTimeChargeItem.setRangeChargeItems(rangeChargeItems);
		PriceUnit priceUnit = millOrTimeChargeItem.getPriceUnit();

		millOrTimeChargeItem.setPriceUnit(priceUnit);

		String[] stepSplitPointArray = stepSplitPoints.split("[\\s]+");

		for (int i = 0; i <= stepSplitPointArray.length; i++) {
			String from = (i == 0) ? "0" : stepSplitPointArray[i - 1];
			String to = (i == stepSplitPointArray.length) ? ""
					: stepSplitPointArray[i];
			Range range = new Range(from, to);

			RangeChargeItem rangeChargeItem = new RangeChargeItem();
			rangeChargeItem.setRange(range);
			rangeChargeItem.setPrice("");
			rangeChargeItem.setPriceUnit(priceUnit);
			rangeChargeItems.add(rangeChargeItem);

		}
	}

	protected void setStandardTimeChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		SubChargeItem timeChargeItem = new SubChargeItem();
		mainChargeItem.setTimeChargeItem(timeChargeItem);
		// 限制的值由页面设置，单位和主单位保持一致，所以这里不需要设置
		Limit min = new Limit();
		Limit max = new Limit();
		// min.setLimitType(tariffPlanBo.get);
	
		min.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
		//造数据用的。。。先看看设置完整之后的json长啥样
//						min.setLimitQuantity(20);
		max.setLimitType(Limit.LIMIT_TYPE_BY_AMOUNT);
//						max.setLimitQuantity(200);
		timeChargeItem.setMin(min);
		timeChargeItem.setMax(max);
		timeChargeItem.setPrice("");
		timeChargeItem.setPriceUnit(new PriceUnit(tariffPlanBo
				.getTimePerPriceValue(), tariffPlanBo.getTimePerPriceUnit()));
		setPriceUnitName(timeChargeItem.getPriceUnit());
	}

	protected void setPeriodTimeChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		setStandardTimeChargeItem(tariffPlanBo, mainChargeItem);
		String periodSplitPoints = tariffPlanBo.getPeriodSplitPoints();
		setPeriodRangeChargeItems(periodSplitPoints,
				mainChargeItem.getTimeChargeItem());
	}

	protected void setStepTimeChargeItem(TariffPlanBo tariffPlanBo,
			MainChargeItem mainChargeItem) {
		setStandardTimeChargeItem(tariffPlanBo, mainChargeItem);
		String timeStepSplitPoints = tariffPlanBo.getTimeStepSplitPoints();
		setStepRangeChargeItems(timeStepSplitPoints,
				mainChargeItem.getTimeChargeItem());
	}

	@Override
	public BillingConfigForm getFormByBillingNo(String billingNo) {
		BillingConfigBo billingConfigBo = getBillingConfigBoByBillingNo(billingNo);
		BillingFunc billingFunc = getBillingFunc(billingNo);
		SubChargeItem subChargeItem = null;
		
		subChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
		setSubChargeItemPriceUnitName(subChargeItem);
		
		subChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
		setSubChargeItemPriceUnitName(subChargeItem);
		
		List<DepositChargeItem> depositChargeItems =billingFunc.getDepositChargeItems();
		if(depositChargeItems!=null&&depositChargeItems.size()>0){
			for (DepositChargeItem depositChargeItem : depositChargeItems) {
				setPriceUnitName(depositChargeItem.getPriceUnit());
			}
		}
		
		List<AppendChargeItem> attachChargeItems =billingFunc.getAttachChargeItems();
		if(attachChargeItems!=null&&attachChargeItems.size()>0){
			for (AppendChargeItem attachChargeItem : attachChargeItems) {
				setPriceUnitName(attachChargeItem.getPriceUnit());
			}
		}
		
		
		
		BillingConfigForm billingConfigForm = new BillingConfigForm();
		billingConfigForm.setEftDate(billingConfigBo.getEftDate());
		billingConfigForm.setBillingNo(billingConfigBo.getBillingNo());
		billingConfigForm.setBillingConfigName(billingConfigBo.getBillingConfigName());
		billingConfigForm.setSubBe(billingConfigBo.getSubBe());
		billingConfigForm.setSubBeName(CodeMethod.getCodeName(SubBe.CODE_TYPE, billingConfigForm.getSubBe()));
		billingConfigForm.setAutoModelNo(billingConfigBo.getAutoModelNo());
		billingConfigForm.setAutoModelName(billingConfigBo.getAutoModelName());
		billingConfigForm.setUnionPrice(billingConfigBo.getUniformPrice());
		billingConfigForm.setBillingFunc(billingFunc);
		billingConfigForm.setPlanNo(billingConfigBo.getPlanNo());
		billingConfigForm.setPlanName(billingConfigBo.getPlanName());
		billingConfigForm.setChargeWay(billingConfigBo.getChargeWay());
		billingConfigForm.setChargeWayName(CodeMethod.getCodeName(ChargeWay.CODE_TYPE, billingConfigBo.getChargeWay()));
		billingConfigForm.setChargeMode(billingConfigBo.getChargeMode());
		billingConfigForm.setChargeModeName(CodeMethod.getCodeName(ChargeMode.CODE_TYPE, billingConfigBo.getChargeMode()));
		String temp = billingConfigBo.getOrgAutoModelKey();
		String orgCode = temp.substring(temp.indexOf("C")+1, temp.indexOf("#"));
		billingConfigForm.setOrgCode(orgCode);
		billingConfigForm.setOrgCodeName(orgService.getOrgByNo(orgCode).getOrgShortName());
		return billingConfigForm;
	}

	protected void setSubChargeItemPriceUnitName(SubChargeItem subChargeItem) {
		if(subChargeItem !=null){
			setPriceUnitName(subChargeItem.getPriceUnit());
			if(subChargeItem.getRangeChargeItems()!=null&&subChargeItem.getRangeChargeItems().size()>0){
				for (RangeChargeItem rangeChargeItem : subChargeItem.getRangeChargeItems()) {
					setPriceUnitName(rangeChargeItem.getPriceUnit());
				}
			}
		}
	}

	public BillingConfigBo getBillingConfigBoByBillingNo(String billingNo) {
		BillingConfigBoExample example = new BillingConfigBoExample();
		example.createCriteria().andBillingNoEqualTo(billingNo);
		BillingConfigBo billingConfigBo = billingConfigBoMapper.selectByExample(example).get(0);
		return billingConfigBo;
	}

	@Override
	public List<BillingConfigBo> pagi(BillingConfigQueryCondition condition) {
		//页面有传orgCode进来则直接用页面的  没传则是当前登录用户下的所有
		if(!StringUtils.isEmpty(condition.getOrgCode())){
			String[] orgCode=condition.getOrgCode().split(",");
			List<String> orgCodes=new ArrayList<String>();
			for (String string : orgCode) {
				orgCodes.add(string);
			}
			condition.setOrgCodes(orgCodes);
			condition.setOrgCode(null);
		}else{
//			condition.setOrgCode("4600001");
			try {
				condition.setOrgCodes(orgRpcService.getSubOrgCodes());
			} catch (Exception e) {
				Validate.isTrue(false,e.getMessage());
			}
		}
		condition.setIsAdjusted("0");
		int count = billingConfigDao.countBillingConfig(condition);
		condition.setRows(count);
		List<BillingConfigBo> list = billingConfigDao.selectBillingConfig(condition);
		if(list!=null&&list.size()>0){
			for (BillingConfigBo billingConfigBo : list) {
				String temp = billingConfigBo.getOrgAutoModelKey();
				if(StringUtils.isBlank(temp)){
					continue;
				}
				String orgCode = temp.substring(temp.indexOf("C")+1, temp.indexOf("#"));
				OrgBo org=orgService.getOrgByNo(orgCode);
				if(org!=null){
					billingConfigBo.setOperOrgCode(org.getOrgShortName());
				}
			}
		}
		return list;
	}

	@Override
	public String create(BillingConfigForm form) {
		// TODO Auto-generated method stub
		BillingFunc billingFunc = form.getBillingFunc();
		billingFunc.clearResult();
		
		// create和update应该相通 saveOrUpdate（lipf 0412注：只有未生效的定价才能修改 更新的时候也就直接把当前数据更新掉 因此这里create和update不相通 分开写）
		// billingNo对应业务上的意义应该是 org_code rt_no 和 auto_model_no的组合 也就是 locate方法中拼出来的orgAutoModelKey
		//格式是 公司 C 开头 租赁点 R 开头 然后公司编码或者租赁点编码 然后# 然后车型编号 最终是 C0101#0001 之类的
		//要判断这个是否重复了。如果是重复了，就要更新同一公司同一车型的数据。IS_LATEST_VERSION设成0，新插的数据是1
		//其他好几个字段是为了不需要多表关联、或者运维人员查数据的冗余字段，按照规则更新
		//min 和 max 的price 要从 billingFunc中取出对应的数据 （时间 里程）（标准 分时 阶梯） 中 最小的和最大的来设置
		//这个是为了查看定价信息，和调整定价之类的地方可以大致看到价格范围用的
		
		//无论如果，查找和更新现有的 都是必要的
		BillingConfigBoExample presentExample = new BillingConfigBoExample();
		//更新的时候，是完整对应公司和租赁点来的，所以需要默认值
		BillingConfigBoExample.Criteria c = presentExample.createCriteria()
				.andSubBeEqualTo(form.getSubBe())
				.andAutoModelNoEqualTo(form.getAutoModelNo());
		//先不考虑租赁点,单纯考虑公司
		AccountBo currentAccount = authentication.getCurrentAccount();
//		String orgCode = "4600001";
		OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
//		if(orgBo == null || StringUtils.isEmpty( orgBo.getOrgCode() ) ){
//			//TODO 平台的异常处理机制
//			Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
//		}
		String orgCode = 	"000000";
		c.andOperOrgCodeEqualTo(orgCode);
		BillingConfigBo updateLatestVersionEtc = new BillingConfigBo();
		updateLatestVersionEtc.setIsLatestVersion("0");
		updateLatestVersionEtc.setDataOperType("U");
		updateLatestVersionEtc.setDataOperTime(new DateTime().toDate());
		if("1".equals(form.getIsAdjusted())){
			c.andApplyDateEqualTo(form.getApplyDate());
			c.andIsAdjustedEqualTo("1");
		}/*else{
			c.andApplyDateLessThanOrEqualTo(form.getApplyDate());
			c.andIsLatestVersionEqualTo("1");
		}*/
		/*int rows = billingConfigBoMapper.updateByExampleSelective(updateLatestVersionEtc, presentExample);
		if("1".equals(form.getIsAdjusted())){
			LOGGER.info("orgCode={},subBe={},autoModelNo={},applyDate={},存在{}条调价历史版本记录，已更新{}条",orgCode,form.getSubBe(),form.getAutoModelNo(),form.getApplyDate(),rows,rows);
		}else{
			LOGGER.info("orgCode={},subBe={},autoModelNo={},applyDate={},存在{}条已生效版本记录，已更新{}条",orgCode,form.getSubBe(),form.getAutoModelNo(),form.getApplyDate(),rows,rows);
		}*/
		BillingConfigBo newRecord = new BillingConfigBo();
		String billingNo = seqRpcService.getDefNo();
	
		newRecord.setBillingNo(billingNo);
		newRecord.setOperOrgCode(orgCode);
		//TODO 租赁点定价还未确认
//		newRecord.setRtNo("");
		newRecord.setPeBe(BillingConstants.PeBe.RENT);
		newRecord.setSubBe(form.getSubBe());
		newRecord.setVersion(getNowVersion());//当前时间版本
		newRecord.setIsLatestVersion("1");
		String autoModelNo = form.getAutoModelNo();
		newRecord.setAutoModelNo(autoModelNo);
		newRecord.setAutoModelName(form.getAutoModelName());
		newRecord.setOrgAutoModelKey(getCompanyKey(form.getOrgCode(), autoModelNo));
		//公司统一定价
		newRecord.setUniformPrice(form.getUnionPrice());
		
		//TODO 之后做调整价格的分歧点
		if("1".equals(form.getIsAdjusted())){
			newRecord.setApplyDate(form.getApplyDate());
			newRecord.setIsAdjusted("1");
		}else{
			newRecord.setIsAdjusted("0");
			newRecord.setApplyDate(BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE);
		}
		
		newRecord.setBillingConfigName(form.getBillingConfigName());
		String chargeWay = billingFunc.getMainChargeItem().getChargeWay();
		String chargeMode = billingFunc.getMainChargeItem().getChargeMode();
		newRecord.setChargeWay(chargeWay);
		newRecord.setChargeMode(chargeMode);
		
		// 价格展示用的冗余字段 根据是不同的 标准的最小和最大是一样的 分时和阶梯要找。然后单位1要忽略来着
		String minPriceUnitDesc = "";
		String maxPriceUnitDesc = "";

		SubChargeItem subChargeItem = null;
		if (ChargeWay.BY_MILL.equals(chargeWay)) {
			subChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
			this.calMinMaxPrice(chargeMode,subChargeItem,newRecord);
			newRecord.setMinPriceUnitDesc(subChargeItem.getDisplayPrice());
		} else if (ChargeWay.BY_TIME.equals(chargeWay)) {
			subChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
			this.calMinMaxPrice(chargeMode,subChargeItem,newRecord);
			newRecord.setMinPriceUnitDesc(subChargeItem.getDisplayPrice());
		} else {
			subChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();//时间
			this.calMinMaxPrice(chargeMode,subChargeItem,newRecord);
			minPriceUnitDesc = subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = newRecord.getMaxPriceUnitDesc();
			subChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();//里程
			this.calMinMaxPrice(chargeMode,subChargeItem,newRecord);
			minPriceUnitDesc = minPriceUnitDesc+","+ subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = maxPriceUnitDesc+","+ newRecord.getMaxPriceUnitDesc();
			newRecord.setMinPriceUnitDesc(minPriceUnitDesc);
			newRecord.setMaxPriceUnitDesc(maxPriceUnitDesc);
		}

		//TODO 备注要靠那之前的自动生成的加强，先放空，整体验证
//		newRecord.setRemark("");
		
		
		newRecord.setDataOperType("I");
		newRecord.setDataOperTime(new DateTime().toDate());
		if(StringUtils.isBlank(form.getEftDate())){
			newRecord.setEftDate(DateTools.getStringDateShort("yyyy-MM-dd HH:mm"));
		}else{
			newRecord.setEftDate(form.getEftDate());
		}
		newRecord.setPlanNo(form.getPlanNo());
		billingConfigBoMapper.insertSelective(newRecord);
		
		BillingFuncBo newBillingFuncRecord = new BillingFuncBo();
		newBillingFuncRecord.setBillingNo(billingNo);
		newBillingFuncRecord.setContent(JsonMapper.nonEmptyMapper().toJson(billingFunc));
		LOGGER.info("billing func json:{}",newBillingFuncRecord.getContent());
		newBillingFuncRecord.setDataOperType("I");
		newBillingFuncRecord.setDataOperTime(new DateTime().toDate());
		billingFuncBoMapper.insertSelective(newBillingFuncRecord);
		if(!"1".equals(form.getIsAdjusted())){//新增车型定价的时候更新e_auto_model_rental 标识此车型已经定价
			Map<String,String> inMap = new HashMap<String, String>();
			inMap.put("orgCode", form.getOrgCode());
			inMap.put("autoModelNo", autoModelNo);
			inMap.put("subBe", form.getSubBe());
			billingConfigDao.updateAutoModelRental(inMap);
		}
		return billingNo;
	}
	
	private void calMinMaxPrice(String chargeMode, SubChargeItem subChargeItem, BillingConfigBo newRecord) {
		String minPriceUnitDesc = "";
		String maxPriceUnitDesc = "";
		if (ChargeMode.STANDARD.equals(chargeMode)) {

			minPriceUnitDesc = maxPriceUnitDesc = getPerPriceUnitDesc(subChargeItem);
		} else {
			List<RangeChargeItem> rangeChargeItemCopy = Lists
					.newArrayList(subChargeItem.getRangeChargeItems());
			Collections.sort(rangeChargeItemCopy,
					new Comparator<RangeChargeItem>() {

						@Override
						public int compare(RangeChargeItem o1,
								RangeChargeItem o2) {
							// 按价格从小到大排序一次
							return new BigDecimal(o1.getPrice())
									.compareTo(new BigDecimal(o2.getPrice()));
						}
					});
			minPriceUnitDesc = getPerPriceUnitDesc(rangeChargeItemCopy.get(0));
			maxPriceUnitDesc = getPerPriceUnitDesc(rangeChargeItemCopy
					.get(rangeChargeItemCopy.size() - 1));
		}

		newRecord.setMinPriceUnitDesc(minPriceUnitDesc);
		newRecord.setMaxPriceUnitDesc(maxPriceUnitDesc);
	}

	public String getTodayString() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String today = sdf.format(new Date());
		return today;
	}

	protected String getCompanyKey(String orgCode,String autoModelNo) {
		//前7位就是总公司编码
		//return "C"+getRootOrgCode(orgCode)+"#"+autoModelNo;
		return "C"+orgCode+"#"+autoModelNo;
	}

	protected String getRootOrgCode(String orgCode) {
		if(orgCode!=null&&orgCode.length()>=7){
		return orgCode.substring(0,7);
		}
		//正常的数据其实不该走到这里的。考虑到对异常数据的健壮，判断一下
		return orgCode;
	}

	protected String getRentPointKey(String rtNo,String autoModelNo) {
		return "R"+rtNo+"#"+autoModelNo;
	}

	public String getPerPriceUnitDesc(AbstractChargeItem chargeItem) {
		return chargeItem.getPrice()+getPriceUnitDesc(chargeItem.getPriceUnit());
	}
	/**
	 * 单位描述
	 * @return
	 */
	protected String getPriceUnitDesc(PriceUnit priceUnit){
		String valueDesc = priceUnit.getValue()==1?"":String.valueOf(priceUnit.getValue());
		String unitDesc = CodeMethod.getCodeName(PriceUnit.CODE_TYPE, priceUnit.getUnit());
		return "元/"+valueDesc+unitDesc;
	}
	
	public String getNowVersion() {
		return new DateTime().toString("yyyyMMddHHmmss");
	}

	@Override
	public void update(BillingConfigForm form) {
		BillingFunc billingFunc = form.getBillingFunc();
		billingFunc.clearResult();
		BillingConfigBo record = new BillingConfigBo();
		BillingConfigBoExample example = new BillingConfigBoExample();
		example.createCriteria().andBillingNoEqualTo(form.getBillingNo());
		record.setVersion(getNowVersion());
		record.setBillingConfigName(form.getBillingConfigName());
		String chargeWay = billingFunc.getMainChargeItem().getChargeWay();
		String chargeMode = billingFunc.getMainChargeItem().getChargeMode();
		// 价格展示用的冗余字段 根据是不同的 标准的最小和最大是一样的 分时和阶梯要找。然后单位1要忽略来着
		String minPriceUnitDesc = "";
		String maxPriceUnitDesc = "";

		SubChargeItem subChargeItem = null;
		if (ChargeWay.BY_MILL.equals(chargeWay)) {
			subChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
			this.calMinMaxPrice(chargeMode,subChargeItem,record);
			minPriceUnitDesc = subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = record.getMaxPriceUnitDesc();
		} else if (ChargeWay.BY_TIME.equals(chargeWay)) {
			subChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
			this.calMinMaxPrice(chargeMode,subChargeItem,record);
			minPriceUnitDesc = subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = record.getMaxPriceUnitDesc();
		} else {
			subChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();//时间
			this.calMinMaxPrice(chargeMode,subChargeItem,record);
			minPriceUnitDesc = subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = record.getMaxPriceUnitDesc();
			subChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();//里程
			this.calMinMaxPrice(chargeMode,subChargeItem,record);
			minPriceUnitDesc = minPriceUnitDesc+","+ subChargeItem.getDisplayPrice();
			maxPriceUnitDesc = maxPriceUnitDesc+","+ record.getMaxPriceUnitDesc();
		}

		record.setMinPriceUnitDesc(minPriceUnitDesc);
		record.setMaxPriceUnitDesc(maxPriceUnitDesc);
		record.setDataOperType("U");
		record.setDataOperTime(new DateTime().toDate());
		record.setEftDate(form.getEftDate());
		billingConfigBoMapper.updateByExampleSelective(record , example );
		BillingFuncBo newBillingFuncRecord = new BillingFuncBo();
		newBillingFuncRecord.setContent(JsonMapper.nonEmptyMapper().toJson(billingFunc));
		LOGGER.info("billing func json:{}",newBillingFuncRecord.getContent());
		newBillingFuncRecord.setDataOperType("U");
		newBillingFuncRecord.setDataOperTime(new DateTime().toDate());
		BillingFuncBoExample newExample = new BillingFuncBoExample();
		newExample.createCriteria().andBillingNoEqualTo(form.getBillingNo());
		billingFuncBoMapper.updateByExampleSelective(newBillingFuncRecord, newExample );
	}
	
	public BillingFunc getBillingFunc(String billingNo) {
		BillingFuncBoExample billingFuncBoExample = new BillingFuncBoExample();
		billingFuncBoExample.createCriteria().andBillingNoEqualTo(billingNo);
		List<BillingFuncBo> billingFuncBoList = billingFuncBoMapper.selectByExampleWithBLOBs(billingFuncBoExample);
		String billingFuncJson = billingFuncBoList.get(0).getContent();
		BillingFunc billingFunc = JsonMapper.DEFAULT.fromJson(billingFuncJson, BillingFunc.class);
		billingFunc.setBillingNo(billingFuncBoList.get(0).getBillingNo());
		return billingFunc;
	}

	public static void main(String[] args) {
		String periodSplitPoints = "00:00 06:00 09:00 12:00 22:00 23:00 24:00";
		String[] periodSplitPointArray = periodSplitPoints.split("[\\s]+");
		// String[] periodSplitPointArray = {"06:00","12:00","21:00"};
		for (int i = 0; i < periodSplitPointArray.length-1; i++) {
			String from = periodSplitPointArray[i];
			String to = periodSplitPointArray[i+1];
//			System.out.println(from + "-" + to);
		}
	}

	/**
	 * 
	 * 方法说明：根据appNo、billType查询订单计费信息(billType-01预收、02结算)
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月7日 下午3:59:52
	 * History:  2016年4月7日 下午3:59:52   lipf   Created.
	 *
	 * @param billingOrderRelaBo
	 * @return
	 *
	 */
	public BillingOrderRelaBo getBillingOrderRela(BillingOrderRelaBo billingOrderRelaBo) {
		BillingOrderRelaBoExample example = new BillingOrderRelaBoExample();
		example.createCriteria().andAppNoEqualTo(billingOrderRelaBo.getAppNo()).andBillTypeEqualTo(billingOrderRelaBo.getBillType());
		List<BillingOrderRelaBo> list = billingOrderRelaBoMapper.selectByExample(example);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

	/**
	 * 
	 * 方法说明：根据appNo、billType查询订单计费详细信息(billType-01预收、02结算)
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月7日 下午4:00:04
	 * History:  2016年4月7日 下午4:00:04   lipf   Created.
	 *
	 * @param billingOrderRelaBo
	 * @return
	 *
	 */
	public List<BillingOrderPricingBo> getBillingOrderPricingList(BillingOrderRelaBo billingOrderRelaBo) {
		BillingOrderPricingBoExample example = new BillingOrderPricingBoExample();
		example.createCriteria().andRelaNoEqualTo(billingOrderRelaBo.getRelaNo());
		List<BillingOrderPricingBo> list = billingOrderPricingBoMapper.selectByExampleWithBLOBs(example);
		if(list!=null&&list.size()>0){
			return list;
		}
		return null;
	}

	@Override
	public void deleteBillings(String[] ids) {
		for (String billingNo : ids) {
			BillingConfigBoExample example = new BillingConfigBoExample();
			example.createCriteria().andBillingNoEqualTo(billingNo);
			billingConfigBoMapper.deleteByExample(example );
			BillingFuncBoExample newExample = new BillingFuncBoExample();
			newExample.createCriteria().andBillingNoEqualTo(billingNo);
			billingFuncBoMapper.deleteByExample(newExample );
		}
	}

	@Override
	public String createAdjust(BillingConfigForm form) {
		String[] applyDates = form.getApplyDate().split(",");
		for (int i = 0; i < applyDates.length; i++) {
			BillingConfigForm tempForm = new BillingConfigForm();
			BeanUtils.copyProperties(form, tempForm);
			tempForm.setApplyDate(applyDates[i].replace("-", ""));
			this.create(tempForm);
		}
		return null;
	}

	@Override
	public String copyConfig(BillingConfigBo bo) {
		BillingConfigBoExample example = new BillingConfigBoExample();
		example.createCriteria().andBillingNoEqualTo(bo.getBillingNo());
		List<BillingConfigBo> list = billingConfigBoMapper.selectByExample(example );
		if(list!=null&&list.size()>0){
			if(bo.getEftDate().equals(list.get(0).getEftDate())){
				return "isExist";//存在相同的生效日期 直接返回
			}
			AccountBo currentAccount = authentication.getCurrentAccount();
			if(currentAccount==null){
				Validate.isTrue(false,"当前人员没有登录，或是会话已失效");
			}
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo == null || StringUtils.isEmpty( orgBo.getOrgCode() ) ){
				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
			}
			BillingConfigBo newConfigBo = new BillingConfigBo();;
			BeanUtils.copyProperties(list.get(0), newConfigBo);
			newConfigBo.setVersion(getNowVersion());
			String billingNo = seqRpcService.getDefNo();
			newConfigBo.setBillingNo(billingNo);
			newConfigBo.setDataOperType(DataOperType.I);
			newConfigBo.setDataOperTime(new DateTime().toDate());
			newConfigBo.setOperOrgCode(orgBo.getOrgCode());
			newConfigBo.setEftDate(bo.getEftDate());
			newConfigBo.setIsLatestVersion("1");
			newConfigBo.setSystemId(null);
			billingConfigBoMapper.insertSelective(newConfigBo);
			BillingFuncBoExample newExample = new BillingFuncBoExample();
			newExample.createCriteria().andBillingNoEqualTo(bo.getBillingNo());
			List<BillingFuncBo> funcList = billingFuncBoMapper.selectByExampleWithBLOBs(newExample );
			BillingFuncBo newFuncBo = new BillingFuncBo();
			newFuncBo.setBillingNo(billingNo);
			newFuncBo.setContent(funcList.get(0).getContent());
			newFuncBo.setDataOperType(DataOperType.I);
			newFuncBo.setDataOperTime(new DateTime().toDate());
			billingFuncBoMapper.insertSelective(newFuncBo);
		}
		return "";
	}

	@Override
	public List<BillingConfigBo> getAutoBillingList(BillingConfigQueryCondition condition) {
		return billingConfigDao.getAutoBillingList(condition);
	}
}
