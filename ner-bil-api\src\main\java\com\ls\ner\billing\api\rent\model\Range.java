package com.ls.ner.billing.api.rent.model;

import java.io.Serializable;

import org.apache.commons.lang3.StringUtils;

public class Range implements Serializable{
	private static final long serialVersionUID = -7941577592476342671L;
	private String from;
	private String to;
	private String desc;
	public Range(){
		
	}
	public Range(String from,String to){
		this.from = from;
		this.to = to;
	}
	
	
	public String getFrom() {
		return from;
	}
	public void setFrom(String from) {
		this.from = from;
	}
	public String getTo() {
		return to;
	}
	public void setTo(String to) {
		this.to = to;
	}
	public String toString(){
		return StringUtils.trimToEmpty(from)+"-"+StringUtils.trimToEmpty(to);
	}
	
	public String getDesc(){
		return this.toString();
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
}
