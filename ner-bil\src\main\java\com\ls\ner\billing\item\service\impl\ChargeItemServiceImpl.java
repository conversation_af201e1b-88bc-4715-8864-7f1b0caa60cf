/**
 *
 * @(#) ChargeItemServiceImpl.java
 * @Package com.ls.ner.billing.item.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.item.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.billing.item.bo.ChargeItemBo;
import com.ls.ner.billing.item.dao.IChargeItemDao;
import com.ls.ner.billing.item.service.IChargeItemService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;

/**
 * Description : 费用项 实现类
 * 
 * @author: lipf
 * 
 *          History: 2015年7月3日 下午7:23:43 lipf Created.
 * 
 */
@Service(target = { ServiceType.APPLICATION }, value = "chargeItemService")
public class ChargeItemServiceImpl implements IChargeItemService {
	@Autowired(required = true)
	private IChargeItemDao dao;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	/**
	 * 费用项 查询
	 */
	public List<ChargeItemBo> getChargeItem(ChargeItemBo bo) {
		int pageBegin = bo.getPageBegin();
		bo.setPageBegin(pageBegin - 1);
		return dao.getChargeItem(bo);
	}

	/**
	 * 费用项 查询 记录数
	 */
	public int getChargeItemCount(ChargeItemBo bo) {
		return dao.getChargeItemCount(bo);
	}

	/**
	 * 费用项 保存
	 */
	public void saveChargeItem(ChargeItemBo bo) {
		String itemId = seqRpcService.getDefId().toString();
		bo.setItemId(itemId);
		dao.saveChargeItem(bo);
	}

	/**
	 * 费用项 更新
	 */
	public void updateChargeItem(ChargeItemBo bo) {
		dao.updateChargeItem(bo);
	}

	/**
	 * 费用项 删除
	 */
	public void deleteChargeItem(String id) {
		dao.deleteChargeItem(id);
	}

	/**
	 * 费用明细查询
	 */
	public List<ChargeItemBo> getItemDetail(ChargeItemBo bo) {
		int pageBegin = bo.getPageBegin();
		bo.setPageBegin(pageBegin - 1);
		return dao.getItemDetail(bo);
	}

	/**
	 * 费用明细查询记录数
	 */
	public int getItemDetailCount(ChargeItemBo bo) {
		return dao.getItemDetailCount(bo);
	}

}
