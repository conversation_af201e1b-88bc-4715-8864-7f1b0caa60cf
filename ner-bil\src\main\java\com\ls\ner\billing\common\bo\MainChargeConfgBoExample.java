package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MainChargeConfgBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public MainChargeConfgBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andMainNoIsNull() {
            addCriterion("MAIN_NO is null");
            return (Criteria) this;
        }

        public Criteria andMainNoIsNotNull() {
            addCriterion("MAIN_NO is not null");
            return (Criteria) this;
        }

        public Criteria andMainNoEqualTo(String value) {
            addCriterion("MAIN_NO =", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoNotEqualTo(String value) {
            addCriterion("MAIN_NO <>", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoGreaterThan(String value) {
            addCriterion("MAIN_NO >", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoGreaterThanOrEqualTo(String value) {
            addCriterion("MAIN_NO >=", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoLessThan(String value) {
            addCriterion("MAIN_NO <", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoLessThanOrEqualTo(String value) {
            addCriterion("MAIN_NO <=", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoLike(String value) {
            addCriterion("MAIN_NO like", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoNotLike(String value) {
            addCriterion("MAIN_NO not like", value, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoIn(List<String> values) {
            addCriterion("MAIN_NO in", values, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoNotIn(List<String> values) {
            addCriterion("MAIN_NO not in", values, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoBetween(String value1, String value2) {
            addCriterion("MAIN_NO between", value1, value2, "mainNo");
            return (Criteria) this;
        }

        public Criteria andMainNoNotBetween(String value1, String value2) {
            addCriterion("MAIN_NO not between", value1, value2, "mainNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNull() {
            addCriterion("BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNotNull() {
            addCriterion("BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBillingNoEqualTo(Long value) {
            addCriterion("BILLING_NO =", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotEqualTo(Long value) {
            addCriterion("BILLING_NO <>", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThan(Long value) {
            addCriterion("BILLING_NO >", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThanOrEqualTo(Long value) {
            addCriterion("BILLING_NO >=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThan(Long value) {
            addCriterion("BILLING_NO <", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThanOrEqualTo(Long value) {
            addCriterion("BILLING_NO <=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIn(List<Long> values) {
            addCriterion("BILLING_NO in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotIn(List<Long> values) {
            addCriterion("BILLING_NO not in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoBetween(Long value1, Long value2) {
            addCriterion("BILLING_NO between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotBetween(Long value1, Long value2) {
            addCriterion("BILLING_NO not between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("PRICE is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("PRICE is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(BigDecimal value) {
            addCriterion("PRICE =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(BigDecimal value) {
            addCriterion("PRICE <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(BigDecimal value) {
            addCriterion("PRICE >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICE >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(BigDecimal value) {
            addCriterion("PRICE <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICE <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<BigDecimal> values) {
            addCriterion("PRICE in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<BigDecimal> values) {
            addCriterion("PRICE not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICE between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICE not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andMinCostIsNull() {
            addCriterion("MIN_COST is null");
            return (Criteria) this;
        }

        public Criteria andMinCostIsNotNull() {
            addCriterion("MIN_COST is not null");
            return (Criteria) this;
        }

        public Criteria andMinCostEqualTo(BigDecimal value) {
            addCriterion("MIN_COST =", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostNotEqualTo(BigDecimal value) {
            addCriterion("MIN_COST <>", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostGreaterThan(BigDecimal value) {
            addCriterion("MIN_COST >", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("MIN_COST >=", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostLessThan(BigDecimal value) {
            addCriterion("MIN_COST <", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("MIN_COST <=", value, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostIn(List<BigDecimal> values) {
            addCriterion("MIN_COST in", values, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostNotIn(List<BigDecimal> values) {
            addCriterion("MIN_COST not in", values, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MIN_COST between", value1, value2, "minCost");
            return (Criteria) this;
        }

        public Criteria andMinCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MIN_COST not between", value1, value2, "minCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostIsNull() {
            addCriterion("MAX_COST is null");
            return (Criteria) this;
        }

        public Criteria andMaxCostIsNotNull() {
            addCriterion("MAX_COST is not null");
            return (Criteria) this;
        }

        public Criteria andMaxCostEqualTo(BigDecimal value) {
            addCriterion("MAX_COST =", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostNotEqualTo(BigDecimal value) {
            addCriterion("MAX_COST <>", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostGreaterThan(BigDecimal value) {
            addCriterion("MAX_COST >", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("MAX_COST >=", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostLessThan(BigDecimal value) {
            addCriterion("MAX_COST <", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostLessThanOrEqualTo(BigDecimal value) {
            addCriterion("MAX_COST <=", value, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostIn(List<BigDecimal> values) {
            addCriterion("MAX_COST in", values, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostNotIn(List<BigDecimal> values) {
            addCriterion("MAX_COST not in", values, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MAX_COST between", value1, value2, "maxCost");
            return (Criteria) this;
        }

        public Criteria andMaxCostNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("MAX_COST not between", value1, value2, "maxCost");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNull() {
            addCriterion("DATA_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIsNotNull() {
            addCriterion("DATA_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE =", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <>", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThan(String value) {
            addCriterion("DATA_OPER_TYPE >", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE >=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThan(String value) {
            addCriterion("DATA_OPER_TYPE <", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATA_OPER_TYPE <=", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeLike(String value) {
            addCriterion("DATA_OPER_TYPE like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotLike(String value) {
            addCriterion("DATA_OPER_TYPE not like", value, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotIn(List<String> values) {
            addCriterion("DATA_OPER_TYPE not in", values, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATA_OPER_TYPE not between", value1, value2, "dataOperType");
            return (Criteria) this;
        }

        public Criteria andChargModeIsNull() {
            addCriterion("CHARG_MODE is null");
            return (Criteria) this;
        }

        public Criteria andChargModeIsNotNull() {
            addCriterion("CHARG_MODE is not null");
            return (Criteria) this;
        }

        public Criteria andChargModeEqualTo(String value) {
            addCriterion("CHARG_MODE =", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeNotEqualTo(String value) {
            addCriterion("CHARG_MODE <>", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeGreaterThan(String value) {
            addCriterion("CHARG_MODE >", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARG_MODE >=", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeLessThan(String value) {
            addCriterion("CHARG_MODE <", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeLessThanOrEqualTo(String value) {
            addCriterion("CHARG_MODE <=", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeLike(String value) {
            addCriterion("CHARG_MODE like", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeNotLike(String value) {
            addCriterion("CHARG_MODE not like", value, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeIn(List<String> values) {
            addCriterion("CHARG_MODE in", values, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeNotIn(List<String> values) {
            addCriterion("CHARG_MODE not in", values, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeBetween(String value1, String value2) {
            addCriterion("CHARG_MODE between", value1, value2, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargModeNotBetween(String value1, String value2) {
            addCriterion("CHARG_MODE not between", value1, value2, "chargMode");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNull() {
            addCriterion("CHARGE_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNotNull() {
            addCriterion("CHARGE_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeEqualTo(String value) {
            addCriterion("CHARGE_TYPE =", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotEqualTo(String value) {
            addCriterion("CHARGE_TYPE <>", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThan(String value) {
            addCriterion("CHARGE_TYPE >", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_TYPE >=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThan(String value) {
            addCriterion("CHARGE_TYPE <", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_TYPE <=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLike(String value) {
            addCriterion("CHARGE_TYPE like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotLike(String value) {
            addCriterion("CHARGE_TYPE not like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIn(List<String> values) {
            addCriterion("CHARGE_TYPE in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotIn(List<String> values) {
            addCriterion("CHARGE_TYPE not in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeBetween(String value1, String value2) {
            addCriterion("CHARGE_TYPE between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotBetween(String value1, String value2) {
            addCriterion("CHARGE_TYPE not between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeUnitIsNull() {
            addCriterion("CHARGE_UNIT is null");
            return (Criteria) this;
        }

        public Criteria andChargeUnitIsNotNull() {
            addCriterion("CHARGE_UNIT is not null");
            return (Criteria) this;
        }

        public Criteria andChargeUnitEqualTo(String value) {
            addCriterion("CHARGE_UNIT =", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitNotEqualTo(String value) {
            addCriterion("CHARGE_UNIT <>", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitGreaterThan(String value) {
            addCriterion("CHARGE_UNIT >", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitGreaterThanOrEqualTo(String value) {
            addCriterion("CHARGE_UNIT >=", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitLessThan(String value) {
            addCriterion("CHARGE_UNIT <", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitLessThanOrEqualTo(String value) {
            addCriterion("CHARGE_UNIT <=", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitLike(String value) {
            addCriterion("CHARGE_UNIT like", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitNotLike(String value) {
            addCriterion("CHARGE_UNIT not like", value, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitIn(List<String> values) {
            addCriterion("CHARGE_UNIT in", values, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitNotIn(List<String> values) {
            addCriterion("CHARGE_UNIT not in", values, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitBetween(String value1, String value2) {
            addCriterion("CHARGE_UNIT between", value1, value2, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeUnitNotBetween(String value1, String value2) {
            addCriterion("CHARGE_UNIT not between", value1, value2, "chargeUnit");
            return (Criteria) this;
        }

        public Criteria andChargeNumIsNull() {
            addCriterion("CHARGE_NUM is null");
            return (Criteria) this;
        }

        public Criteria andChargeNumIsNotNull() {
            addCriterion("CHARGE_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andChargeNumEqualTo(Integer value) {
            addCriterion("CHARGE_NUM =", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumNotEqualTo(Integer value) {
            addCriterion("CHARGE_NUM <>", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumGreaterThan(Integer value) {
            addCriterion("CHARGE_NUM >", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHARGE_NUM >=", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumLessThan(Integer value) {
            addCriterion("CHARGE_NUM <", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumLessThanOrEqualTo(Integer value) {
            addCriterion("CHARGE_NUM <=", value, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumIn(List<Integer> values) {
            addCriterion("CHARGE_NUM in", values, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumNotIn(List<Integer> values) {
            addCriterion("CHARGE_NUM not in", values, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumBetween(Integer value1, Integer value2) {
            addCriterion("CHARGE_NUM between", value1, value2, "chargeNum");
            return (Criteria) this;
        }

        public Criteria andChargeNumNotBetween(Integer value1, Integer value2) {
            addCriterion("CHARGE_NUM not between", value1, value2, "chargeNum");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_main_charge_config
     *
     * @mbggenerated do_not_delete_during_merge Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_main_charge_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}