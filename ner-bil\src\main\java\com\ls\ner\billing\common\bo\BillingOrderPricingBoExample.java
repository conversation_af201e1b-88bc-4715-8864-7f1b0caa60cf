package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BillingOrderPricingBoExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public BillingOrderPricingBoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("SYSTEM_ID is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("SYSTEM_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Long value) {
            addCriterion("SYSTEM_ID =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Long value) {
            addCriterion("SYSTEM_ID <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Long value) {
            addCriterion("SYSTEM_ID >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Long value) {
            addCriterion("SYSTEM_ID <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Long value) {
            addCriterion("SYSTEM_ID <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Long> values) {
            addCriterion("SYSTEM_ID in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Long> values) {
            addCriterion("SYSTEM_ID not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Long value1, Long value2) {
            addCriterion("SYSTEM_ID not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andAppNoIsNull() {
            addCriterion("APP_NO is null");
            return (Criteria) this;
        }

        public Criteria andAppNoIsNotNull() {
            addCriterion("APP_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAppNoEqualTo(String value) {
            addCriterion("APP_NO =", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotEqualTo(String value) {
            addCriterion("APP_NO <>", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoGreaterThan(String value) {
            addCriterion("APP_NO >", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoGreaterThanOrEqualTo(String value) {
            addCriterion("APP_NO >=", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLessThan(String value) {
            addCriterion("APP_NO <", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLessThanOrEqualTo(String value) {
            addCriterion("APP_NO <=", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoLike(String value) {
            addCriterion("APP_NO like", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotLike(String value) {
            addCriterion("APP_NO not like", value, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoIn(List<String> values) {
            addCriterion("APP_NO in", values, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotIn(List<String> values) {
            addCriterion("APP_NO not in", values, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoBetween(String value1, String value2) {
            addCriterion("APP_NO between", value1, value2, "appNo");
            return (Criteria) this;
        }

        public Criteria andAppNoNotBetween(String value1, String value2) {
            addCriterion("APP_NO not between", value1, value2, "appNo");
            return (Criteria) this;
        }
        
        public Criteria andRelaNoIsNull() {
            addCriterion("RELA_NO is null");
            return (Criteria) this;
        }

        public Criteria andRelaNoIsNotNull() {
            addCriterion("RELA_NO is not null");
            return (Criteria) this;
        }

        public Criteria andRelaNoEqualTo(String value) {
            addCriterion("RELA_NO =", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoNotEqualTo(String value) {
            addCriterion("RELA_NO <>", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoGreaterThan(String value) {
            addCriterion("RELA_NO >", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoGreaterThanOrEqualTo(String value) {
            addCriterion("RELA_NO >=", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoLessThan(String value) {
            addCriterion("RELA_NO <", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoLessThanOrEqualTo(String value) {
            addCriterion("RELA_NO <=", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoLike(String value) {
            addCriterion("RELA_NO like", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoNotLike(String value) {
            addCriterion("RELA_NO not like", value, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoIn(List<String> values) {
            addCriterion("RELA_NO in", values, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoNotIn(List<String> values) {
            addCriterion("RELA_NO not in", values, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoBetween(String value1, String value2) {
            addCriterion("RELA_NO between", value1, value2, "relaNo");
            return (Criteria) this;
        }

        public Criteria andRelaNoNotBetween(String value1, String value2) {
            addCriterion("RELA_NO not between", value1, value2, "relaNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoIsNull() {
            addCriterion("PRICING_DETAIL_NO is null");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoIsNotNull() {
            addCriterion("PRICING_DETAIL_NO is not null");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoEqualTo(String value) {
            addCriterion("PRICING_DETAIL_NO =", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoNotEqualTo(String value) {
            addCriterion("PRICING_DETAIL_NO <>", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoGreaterThan(String value) {
            addCriterion("PRICING_DETAIL_NO >", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoGreaterThanOrEqualTo(String value) {
            addCriterion("PRICING_DETAIL_NO >=", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoLessThan(String value) {
            addCriterion("PRICING_DETAIL_NO <", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoLessThanOrEqualTo(String value) {
            addCriterion("PRICING_DETAIL_NO <=", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoLike(String value) {
            addCriterion("PRICING_DETAIL_NO like", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoNotLike(String value) {
            addCriterion("PRICING_DETAIL_NO not like", value, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoIn(List<String> values) {
            addCriterion("PRICING_DETAIL_NO in", values, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoNotIn(List<String> values) {
            addCriterion("PRICING_DETAIL_NO not in", values, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoBetween(String value1, String value2) {
            addCriterion("PRICING_DETAIL_NO between", value1, value2, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andPricingDetailNoNotBetween(String value1, String value2) {
            addCriterion("PRICING_DETAIL_NO not between", value1, value2, "pricingDetailNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNull() {
            addCriterion("BILLING_NO is null");
            return (Criteria) this;
        }

        public Criteria andBillingNoIsNotNull() {
            addCriterion("BILLING_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBillingNoEqualTo(String value) {
            addCriterion("BILLING_NO =", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotEqualTo(String value) {
            addCriterion("BILLING_NO <>", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThan(String value) {
            addCriterion("BILLING_NO >", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoGreaterThanOrEqualTo(String value) {
            addCriterion("BILLING_NO >=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThan(String value) {
            addCriterion("BILLING_NO <", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLessThanOrEqualTo(String value) {
            addCriterion("BILLING_NO <=", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoLike(String value) {
            addCriterion("BILLING_NO like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotLike(String value) {
            addCriterion("BILLING_NO not like", value, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoIn(List<String> values) {
            addCriterion("BILLING_NO in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotIn(List<String> values) {
            addCriterion("BILLING_NO not in", values, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoBetween(String value1, String value2) {
            addCriterion("BILLING_NO between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andBillingNoNotBetween(String value1, String value2) {
            addCriterion("BILLING_NO not between", value1, value2, "billingNo");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNull() {
            addCriterion("SUB_BE is null");
            return (Criteria) this;
        }

        public Criteria andSubBeIsNotNull() {
            addCriterion("SUB_BE is not null");
            return (Criteria) this;
        }

        public Criteria andSubBeEqualTo(String value) {
            addCriterion("SUB_BE =", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotEqualTo(String value) {
            addCriterion("SUB_BE <>", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThan(String value) {
            addCriterion("SUB_BE >", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeGreaterThanOrEqualTo(String value) {
            addCriterion("SUB_BE >=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThan(String value) {
            addCriterion("SUB_BE <", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLessThanOrEqualTo(String value) {
            addCriterion("SUB_BE <=", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeLike(String value) {
            addCriterion("SUB_BE like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotLike(String value) {
            addCriterion("SUB_BE not like", value, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeIn(List<String> values) {
            addCriterion("SUB_BE in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotIn(List<String> values) {
            addCriterion("SUB_BE not in", values, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeBetween(String value1, String value2) {
            addCriterion("SUB_BE between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andSubBeNotBetween(String value1, String value2) {
            addCriterion("SUB_BE not between", value1, value2, "subBe");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIsNotNull() {
            addCriterion("ORG_AUTO_MODEL_KEY is not null");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY =", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <>", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY >=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThan(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLessThanOrEqualTo(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY <=", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotLike(String value) {
            addCriterion("ORG_AUTO_MODEL_KEY not like", value, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotIn(List<String> values) {
            addCriterion("ORG_AUTO_MODEL_KEY not in", values, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andOrgAutoModelKeyNotBetween(String value1, String value2) {
            addCriterion("ORG_AUTO_MODEL_KEY not between", value1, value2, "orgAutoModelKey");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("VERSION is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("VERSION is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("VERSION =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("VERSION <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("VERSION >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("VERSION >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("VERSION <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("VERSION <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("VERSION like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("VERSION not like", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("VERSION in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("VERSION not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("VERSION between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("VERSION not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNull() {
            addCriterion("APPLY_DATE is null");
            return (Criteria) this;
        }

        public Criteria andApplyDateIsNotNull() {
            addCriterion("APPLY_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDateEqualTo(String value) {
            addCriterion("APPLY_DATE =", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotEqualTo(String value) {
            addCriterion("APPLY_DATE <>", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThan(String value) {
            addCriterion("APPLY_DATE >", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateGreaterThanOrEqualTo(String value) {
            addCriterion("APPLY_DATE >=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThan(String value) {
            addCriterion("APPLY_DATE <", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLessThanOrEqualTo(String value) {
            addCriterion("APPLY_DATE <=", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateLike(String value) {
            addCriterion("APPLY_DATE like", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotLike(String value) {
            addCriterion("APPLY_DATE not like", value, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateIn(List<String> values) {
            addCriterion("APPLY_DATE in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotIn(List<String> values) {
            addCriterion("APPLY_DATE not in", values, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateBetween(String value1, String value2) {
            addCriterion("APPLY_DATE between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andApplyDateNotBetween(String value1, String value2) {
            addCriterion("APPLY_DATE not between", value1, value2, "applyDate");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIsNull() {
            addCriterion("PRICING_AMT is null");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIsNotNull() {
            addCriterion("PRICING_AMT is not null");
            return (Criteria) this;
        }

        public Criteria andPricingAmtEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT =", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT <>", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtGreaterThan(BigDecimal value) {
            addCriterion("PRICING_AMT >", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT >=", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtLessThan(BigDecimal value) {
            addCriterion("PRICING_AMT <", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PRICING_AMT <=", value, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtIn(List<BigDecimal> values) {
            addCriterion("PRICING_AMT in", values, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotIn(List<BigDecimal> values) {
            addCriterion("PRICING_AMT not in", values, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICING_AMT between", value1, value2, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andPricingAmtNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PRICING_AMT not between", value1, value2, "pricingAmt");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIsNull() {
            addCriterion("DATE_OPER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIsNotNull() {
            addCriterion("DATE_OPER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE =", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE <>", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeGreaterThan(String value) {
            addCriterion("DATE_OPER_TYPE >", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE >=", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLessThan(String value) {
            addCriterion("DATE_OPER_TYPE <", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLessThanOrEqualTo(String value) {
            addCriterion("DATE_OPER_TYPE <=", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeLike(String value) {
            addCriterion("DATE_OPER_TYPE like", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotLike(String value) {
            addCriterion("DATE_OPER_TYPE not like", value, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeIn(List<String> values) {
            addCriterion("DATE_OPER_TYPE in", values, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotIn(List<String> values) {
            addCriterion("DATE_OPER_TYPE not in", values, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeBetween(String value1, String value2) {
            addCriterion("DATE_OPER_TYPE between", value1, value2, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDateOperTypeNotBetween(String value1, String value2) {
            addCriterion("DATE_OPER_TYPE not between", value1, value2, "dateOperType");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNull() {
            addCriterion("DATA_OPER_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIsNotNull() {
            addCriterion("DATA_OPER_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME =", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <>", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThan(Date value) {
            addCriterion("DATA_OPER_TIME >", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME >=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThan(Date value) {
            addCriterion("DATA_OPER_TIME <", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeLessThanOrEqualTo(Date value) {
            addCriterion("DATA_OPER_TIME <=", value, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotIn(List<Date> values) {
            addCriterion("DATA_OPER_TIME not in", values, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }

        public Criteria andDataOperTimeNotBetween(Date value1, Date value2) {
            addCriterion("DATA_OPER_TIME not between", value1, value2, "dataOperTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated do_not_delete_during_merge Wed Mar 16 15:34:41 CST 2016
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table e_billing_order_rela_pricing
     *
     * @mbggenerated Wed Mar 16 15:34:41 CST 2016
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}