/**
 *
 * @(#) BillingServiceImpl.java
 * @Package com.ls.ner.billing.tariff.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.bo.AttachConfigBo;
import com.ls.ner.billing.bo.ItemPeriods;
import com.ls.ner.billing.bo.MainChargeConfigBo;
import com.ls.ner.billing.bo.StepConfig;
import com.ls.ner.billing.bo.VehicleBillingBo;
import com.ls.ner.billing.tariff.dao.IBillingDao;
import com.ls.ner.billing.tariff.service.IBillingService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: BillingServiceImpl.java,v 1.2 2016/03/07 06:18:20 32495 Exp $
 * 
 *          History: 2016年2月24日 上午9:46:01 lipf Created.
 * 
 */
@Service(target = { ServiceType.APPLICATION }, value = "billingService")
public class BillingServiceImpl implements IBillingService {

	@Autowired(required = true)
	private IBillingDao dao;

	/**
	 * 获取车辆租赁收费项目信息 根据车型、租赁方式获取车辆租赁费用项目信息
	 */
	@Override
	public Map<String, Object> getCarTariff(Map<String, String> inMap) {
		Map<String, Object> retMap = new HashMap<String, Object>();
		VehicleBillingBo billingBo = dao.getVehicleBilling(inMap);
		
		if (billingBo != null) {
			retMap.put("chargeMode", billingBo.getChargeMode());
			retMap.put("minChargeMode", billingBo.getMinChargeMode());
			retMap.put("maxChargeMode", billingBo.getMaxChargeMode());
			retMap.put("chargeUnit", billingBo.getChargeUnit());
			retMap.put("remark", billingBo.getRemark());
			// 主收费配置
			Map<String, String> tempMap = new HashMap<String, String>();
			tempMap.put("billingNo", billingBo.getBillingNo());
			List<MainChargeConfigBo> mainConfigList = dao.getMainChargeConfig(tempMap);
			if (mainConfigList != null && mainConfigList.size() > 0) {
				List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
				Map<String, Object> tMap = null;
				for (MainChargeConfigBo mainChargeConfigBo : mainConfigList) {
					tMap = new HashMap<String, Object>();
					tMap.put("price", mainChargeConfigBo.getPrice());
					tMap.put("minCost", mainChargeConfigBo.getMinCost());
					tMap.put("maxCost", mainChargeConfigBo.getMaxCost());
					tMap.put("chargeMode", mainChargeConfigBo.getChargMode());
					tMap.put("chargeUnit", mainChargeConfigBo.getChargeUnit());
					tMap.put("chargeType", mainChargeConfigBo.getChargeType());
					// 分时信息查询
					Map<String, String> temp = new HashMap<String, String>();
					temp.put("mainNo", mainChargeConfigBo.getMainNo());
					List<ItemPeriods> itemPeriods = dao.getItemPeriods(temp);
					tMap.put("timeInfo", itemPeriods);
					// 阶梯配置查询
					temp = new HashMap<String, String>();
					temp.put("mainNo", mainChargeConfigBo.getMainNo());
					List<StepConfig> stepConfig = dao.getStepConfig(temp);
					tMap.put("stepInfo", stepConfig);
					list.add(tMap);
				}
				retMap.put("mainChargeList", list);
			} else {
				retMap.put("mainChargeList", null);
			}
			// 附加收费配置
			List<AttachConfigBo> attachConfigList = dao.getAttachConfig(tempMap);
			if (attachConfigList != null && attachConfigList.size() > 0) {
				List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
				Map<String, Object> tMap = null;
				for (AttachConfigBo attachConfigBo : attachConfigList) {
					tMap = new HashMap<String, Object>();
					tMap.put("itemNo", attachConfigBo.getItemNo());
					tMap.put("itemName", attachConfigBo.getItemName());
					tMap.put("chargeType", attachConfigBo.getChargeType());
					tMap.put("buyIdentity", attachConfigBo.getBuyIdentity());
					list.add(tMap);
				}
				retMap.put("attachList", list);
			} else {
				retMap.put("attachList", null);
			}
		}
		return retMap;
	}

}
