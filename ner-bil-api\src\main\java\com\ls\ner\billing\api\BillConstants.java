/**
 *
 * @(#) BillConstants.java
 * @Package com.ls.ner.billing.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: BillConstants.java,v 1.1 2016/02/26 02:34:00 34544 Exp $ 
 *		Edit by GUOXUYANG  ON 2016-10-22 12:31
 *  History:  2016年2月18日 上午9:57:54   lipf   Created.
 *           
 */
public class BillConstants {

	public static final String SUB_BE = "rentType";
	public static final String CHARGE_MODE = "chargeMode";
	public static final String CHARGE_TYPE = "chargeType";
	/**
	 * 是 否
	 */
	public static final String JUDGE_FLAG = "ynJudgeFlag";

	/**
	  * 活动类型：01优惠券推广、02限时打折、03满就送、04首单免、05注册送、06推荐新人送、07预存赠送
	  */
	public static final String ACT_TYPE = "actType";
	/**
	  * 活动渠道：01APP、02现场、03微信、04支付宝
	  */
	public static final String ACT_CHANNEL = "channel";
	/**
	  * 活动状态：0草稿、1未开始、2进行中、3已结束、4已关闭
	  */
	public static final class ActStatus {
		public static final String CODE_TYPE = "actState";
		/**
		 * 0草稿
		 */
		public static final String DRAFT = "0";
		/**
		 * 1未开始
		 */
		public static final String UNSTART = "1";
		/**
		 * 2进行中
		 */
		public static final String PROCESSING = "2";
		/**
		 * 3已结束
		 */
		public static final String OVER = "3";
		/**
		 * 4已关闭
		 */
		public static final String CLOSED = "4";
	}
	/**
	 * 赠送段落关系：1依赖、2互斥、3并存
	 */
	public static final String SECTION_RELA_TYPE = "presentSectRelaType";
	/**
	 * 赠送段落类型：1无段落、5单向单段、6循环
	 */
	public static final String SECTION_TYPE = "presentSectType";
	
	/**
	 * 金额类型：01可用余额、02积分、03红包、04优惠券、05押金、06虚拟钱包、07馈赠金
	 */
	public static final class BalType {
		public static final String CODE_TYPE = "accType";
		/**
		 * 01可用余额
		 */
		public static final String PAY_BAL = "01";
		/**
		 * 07馈赠金
		 */
		public static final String PRESENT_BAL = "07";
	}
	
	/**
	 * 赠送周期类型：1一次性、2周期性
	 */
	public static final class PresentCycleType {
		public static final String CODE_TYPE = "cycleType";
		/**
		 * 1一次性
		 */
		public static final String ONE = "1";
		/**
		 * 2周期性
		 */
		public static final String CYCLE = "2";
	}
	
	/**
	 * 计算公式的计算方法：加减乘除
	 */
	public static final String CALC_METHOD = "presentCalcMethod";
	/**
	 * 周期单位：1日、2周、3旬、4月、5年
	 */
	public static final String CYCLE_UNIT = "cycleUnit";
	/**
	 * 时间偏移单位：1日、4月、5年
	 */
	public static final String TIME_UNIT = "timeUnit";
	/**
	 * 时间偏移单位：日、月等
	 * <AUTHOR>
	 *
	 */
	public static final class TimeUnit {

		public static final String CODE_TYPE = "timeUnit";
		/**
		 * 1日
		 */
		public static final String DAY = "1";
		/**
		 * 2周
		 */
		public static final String WEEK = "2";
		/**
		 * 4月
		 */
		public static final String MONTH = "4";
		/**
		 * 5年
		 */
		public static final String YEAR = "5";
	}

	/**
	 * 时间类别，1绝对日期 2相对日期
	 */
	public static final class TimeType {

		public static final String CODE_TYPE = "timeType";
		/**
		 * 1绝对日期
		 */
		public static final String ABSOLUTELY_TIME = "1";
		/**
		 * 2相对日期
		 */
		public static final String RELATIVE_TIME = "2";
	}
	/**
	 * 计算精度：1向上取整、2向下取整、3四舍五入
	 */
	public static final String CALC_PRECISION = "calcPrecision";
	/**
	 * 时间类别：1绝对日期、2相对日期
	 */
	public static final String DATE_TYPE = "dateType";
	/**
	 * 资讯类别：1内容资讯 2链接资讯
	 */
	public static final String ACTINFO_TYPE = "actInfoType";

	/**
	 * '优惠条件类型dctCondFlag，1无条件 2条件',
	 */
	public static final class DctCondFlag {
		public static final String CODE_TYPE = "dctCondFlag";
		/**
		 * 1无条件
		 */
		public static final String ONE = "1";
		/**
		 * 2条件
		 */
		public static final String TWO = "2";
	}
	/**
	 *  '优惠条件类型=2时有效，优惠条件类别dctCondType\r\n            00整单金额 01计价数量 02计价金额',
	 */
	public static final class DctCondType {
		public static final String CODE_TYPE = "dctCondType";
		/**
		 * 00整单金额
		 */
		public static final String ALL_ORDER_AMT = "00";
		/**
		 * 01计价数量
		 */
		public static final String PROD_NUM = "01";
		/**
		 * 02计价金额
		 */
		public static final String PROD_AMT = "02";
	}

	/**
	 *  '优惠类别dctType\r\n            0100整单金额\r\n            0101计价数量 \r\n            0102计价金额\r\n            0201定价费率 	0401整单服务费',
	 */
	public static final class DctType {
		public static final String CODE_TYPE = "dctType";
		/**
		 * 0100整单金额
		 */
		public static final String ALL_ORDER_AMT = "0100";
		/**
		 * 0101计价数量
		 */
		public static final String PROD_NUM = "0101";
		/**
		 * 0102计价金额
		 */
		public static final String PROD_AMT = "0102";
		/**
		 * 0201定价费率
		 */
		public static final String PROD_RATE = "0201";
		/**
		 * 0401 整单服务费
		 */
		public static final String ALL_ORDER_SER_AMT = "0401";
	}


	/**
	 *   优惠内容 cpnDctType     0100整单金额  0200服务费 ,
	 */
	public static final class CpnDctType {
		public static final String CODE_TYPE = "cpnDctType";
		/**
		 * 0100整单金额
		 */
		public static final String ALL_ORDER_AMT = "0100";
		/**
		 * 0200服务费
		 */
		public static final String SERVICE_AMT = "0110";
	}


	/**
	 *  活动类型，actType           02限时打折、03满减满送、04首单免
	 */
	public static final class ActType {
		public static final String CODE_TYPE = "actType";
		/**
		 * 01优惠券推广
		 */
		public static final String ACT_TYPE_01 = "01";
		/**
		 * 02限时打折
		 */
		public static final String ACT_TYPE_02 = "02";
		/**
		 * 03满减满送
		 */
		public static final String ACT_TYPE_03 = "03";
		/**
		 * 04首单免
		 */
		public static final String ACT_TYPE_04 = "04";
		/**
		 * 05注册送
		 */
		public static final String ACT_TYPE_05 = "05";
		/**
		 * 06 推荐新人送
		 */
		public static final String ACT_TYPE_06 = "06";
		/**
		 * 07 预存赠送
		 */
		public static final String ACT_TYPE_07 = "07";
		/**
		 * 馈赠金赠送
		 */
		public static final String ACT_TYPE_0701 = "0701";
		/**
		 * 优惠券赠送
		 */
		public static final String ACT_TYPE_0702 = "0702";
		/**
		 * 08 积分兑换优惠券
		 */
		public static final String ACT_TYPE_08 = "08";
	}
	/**
	 *  活动子类型，actSubType
	 */
	public static final class ActSubType {
		public static final String CODE_TYPE = "actSubType";
		/**
		 * 0701赠送馈赠金
		 */
		public static final String ACT_SUB_TYPE_0701 = "0701";
		/**
		 * 0701赠送优惠券
		 */
		public static final String ACT_SUB_TYPE_0702 = "0702";
	}
	/**
	 *  '订单状态， 0草稿 1在用 2停用 3作废 billingEnableFlag',
	 */
	public static final class OrderState {
		public static final String CODE_TYPE = "orderState";
		/**
		 * 0草稿
		 */
		public static final String ORDER_STATE_0 = "0";
		/**
		 * 1在用
		 */
		public static final String ORDER_STATE_1 = "1";
		/**
		 * 2停用
		 */
		public static final String ORDER_STATE_2 = "2";
		/**
		 * 3作废
		 */
		public static final String ORDER_STATE_3 = "3";
	}
	/**
	 *  优惠计算方法，与优惠类别匹配使用，=优惠类别前2位。\r\n            01减免 02折扣
	 */
	public static final class DctCalcMethod {
		public static final String DCT_CALC_METHOD = "dctCalcMethod";
		/**
		 * 01减免
		 */
		public static final String REDUCTION = "01";
		/**
		 * 02折扣
		 */
		public static final String DISCOUNT = "02";
	}
	/**
	 *  `CPN_STATE` varchar(8) DEFAULT NULL COMMENT '优惠券状态cpnState，0草稿 1在用 2停用 3作废',
	 */
	public static final class CpnState {
		public static final String NAME = "cpnState";
		/**
		 * 0草稿
		 */
		public static final String DRAFT = "0";
		/**
		 * 1在用
		 */
		public static final String IN_USE = "1";
		/**
		 * 2停用
		 */
		public static final String OFF_USE = "2";
		/**
		 * 3作废
		 */
		public static final String DELETE = "3";
	}

	/**
	 * 企业计费--站点范围
	 */
	public static final class RANGE_FLAG {

		/**
		 * 全部
		 */
		public static final String ALL = "1";
		/**
		 * 部分
		 */
		public static final String PART = "0";

	}
}
