package com.ls.ner.billing.market.service.impl;

import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponPushBo;
import com.ls.ner.billing.market.dao.ICouponPushDao;
import com.ls.ner.billing.market.service.ICouponPushService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.ws.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 优惠券发放实现类
 * description
 * 创建时间 2016年5月31日下午2:29:41
 * 创建人 lise
 */
@Service(target = {ServiceType.LOCAL}, value = "couponPushService")
public class CouponPushServiceImpl implements ICouponPushService {

    @Autowired(required = true)
    private ICouponPushDao dao; // 发放主表

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    /**
     * 描述:获取已推广的优惠券
     *
     * @param: [condition]
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/6 14:50
     */
    @Override
    public List<CouponPushBo> getCouponsPush(MarketCondition condition) throws Exception {
        List<CouponPushBo> list = dao.getCouponsPush(condition);
        for (int i = 0; i < list.size(); i++) {
            CouponPushBo bo = list.get(i);
            String cpnType = bo.getCpnType();
            if (StringUtil.isNotBlank(cpnType)) {
                if ("01".equals(cpnType)) {
                    list.get(i).setCpnAmt(bo.getCpnAmt() + "元");
                } else {
                    list.get(i).setCpnAmt(bo.getCpnAmt() + "折");
                }
            }
            String cpnTimeType = bo.getCpnTimeType();
            if (StringUtil.isNotBlank(cpnTimeType)) {
                if ("2".equals(cpnTimeType)) {
                    List<CodeBO> timeUnitList = codeService.getStandardCodes("timeUnit", null);
                    if (timeUnitList != null && timeUnitList.size() > 0) {
                        String timeUit = bo.getTimeUnit();
                        for (CodeBO timeUitCode : timeUnitList) {
                            if (timeUit.equals(timeUitCode.getCodeValue())) {
                                String effectTime = bo.getEffectTime();
                                list.get(i).setEffectTime(effectTime + "" + timeUitCode.getCodeName());
                            }
                        }
                    }
                }
            }

            if (!com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(bo.getOrgCode())) {
                OrgBo org = orgService.getOrgByNo(bo.getOrgCode());
                if (org != null) {
                    list.get(i).setCrtOrgName(org.getOrgShortName());
                }
            }
        }
        return list;
    }

    public int getCouponsPushCount(MarketCondition condition) throws Exception {
        return dao.getCouponsPushCount(condition);
    }

    /**
     * 描述:保存推广的优惠券
     *
     * @param: [condition]
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/6 14:50
     */
    public int saveCouponPush(CouponPushBo bo) throws Exception{
        int flag = 0;
        flag = dao.saveCouponPush(bo);
        return flag;
    }

    /**
     * 描述:删除已推广的优惠券
     *
     * @param: [condition]
     * @return: java.util.List<com.ls.ner.billing.market.bo.CouponPushBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/6 14:50
     */
    @Override
    public int delCouponPush(MarketCondition condition) throws Exception {
        return dao.delCouponPush(condition);
    }
}
