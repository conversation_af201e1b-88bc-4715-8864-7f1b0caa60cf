package com.ls.ner.billing.market.dao;


import com.ls.ner.billing.api.market.vo.EvaVO;
import com.ls.ner.billing.api.market.vo.IntegralInvoiceUpdateBo;
import com.ls.ner.billing.market.vo.IntegralExchangeVo;

import java.util.List;
import java.util.Map;

public interface IntegralExchangeDao {

    /**
     * 获取积分兑换明细列表
     *
     * @param integralExchangeVo
     * @return
     */
    List<IntegralExchangeVo> getIntegralExchangeList(IntegralExchangeVo integralExchangeVo);

    /**
     * 获取积分兑换明细列表
     *
     * @param integralExchangeVo
     * @return
     */
    List<IntegralExchangeVo> getIntegralExchangeListByWX(IntegralExchangeVo integralExchangeVo);

    /**
     * 获取积分兑换明细总数
     *
     * @param integralExchangeVo
     * @return
     */
    int getIntegralExchangeListCount(IntegralExchangeVo integralExchangeVo);

    /**
     * 获取用户兑换商品数量
     *
     * @param integralExchangeVo
     * @return
     */
    int getExchangeCount(IntegralExchangeVo integralExchangeVo);


    void update(IntegralExchangeVo integralExchangeVo);

    void insert(IntegralExchangeVo integralExchangeVo);

    EvaVO queryEva(IntegralExchangeVo exchangeVo);

    List<Map<String, String>> queryEvaItems(Integer evaId);

    void updateInvoiceFlag(IntegralInvoiceUpdateBo bo);
}
