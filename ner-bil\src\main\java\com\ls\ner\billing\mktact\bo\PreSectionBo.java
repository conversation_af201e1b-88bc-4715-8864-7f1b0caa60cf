package com.ls.ner.billing.mktact.bo;

import com.ls.ner.billing.api.BillConstants;
import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

public class PreSectionBo extends QueryCondition {

	/*******************************e_bal_present_section********************/
	/**
	  * 赠送规则标识
	  */
	private long presentSectionId=0;
	/**
	  * 活动ID
	  */
	private long actId=0;
	/**
	  * 段落名称
	  */
	private String sectionName;
	/**
	  * 段落关系
	  */
	private String sectionRelaType;
	/**
	  * 计算优先级
	  */
	private int calcPriority;
	/**
	  * 段落类型
	  */
	private String sectionType;
	/**
	  * 预存余额类型
	  */
	private String payBalType;
	/**
	  * 赠送余额类型
	  */
	private String presentBalType;
	/**
	  * 赠送规则描述
	  */
	private String presentRuleDesc;
	/**
	  * 计算精度
	  */
	private String calcPrecision;
	/**
	  * 赠送最大值
	  */
	private long maxValue;
	/**
	  * 赠送周期类型
	  */
	private String presentCycleType;
	/**
	  * 周期类别标识
	  */
	private long cycleTypeId=0;
	/**
	  * 周期使用上限
	  */
	private long cycleUpper;
	/**
	  * 周期使用下限
	  */
	private long cycleLower;
	/**
	  * 时间限制标识
	  */
	private long timeLimitId=0;
	/**
	  * 段落关系名称
	  */
	@DicAttribute(dicName = "codeDict", key = "sectionRelaType", subType = BillConstants.SECTION_RELA_TYPE)
	private String sectionRelaTypeName;
	/**
	  * 段落类型名称
	  */
	@DicAttribute(dicName = "codeDict", key = "sectionType", subType = BillConstants.SECTION_TYPE)
	private String sectionTypeName;
	/**
	  * 赠送余额类型名称
	  */
//	@DicAttribute(dicName = "codeDict", key = "presentBalType", subType = BillConstants.BalType.CODE_TYPE)
	private String presentBalTypeName;
	/**
	  * 赠送周期类型
	  */
	@DicAttribute(dicName = "codeDict", key = "presentCycleType", subType = BillConstants.PresentCycleType.CODE_TYPE)
	private String presentCycleTypeName;
	/**
	 * 赠送段落明细
	 */
	private String presentSectionDets;

	/**
	 * 发布标识
	 */
	private String release;
	
	/**  
	 * 获取赠送规则标识  
	 * @return presentSectionId 赠送规则标识  
	 */
	public long getPresentSectionId() {
		return presentSectionId;
	}
	/**  
	 * 设置赠送规则标识  
	 * @param presentSectionId 赠送规则标识  
	 */
	public void setPresentSectionId(long presentSectionId) {
		this.presentSectionId = presentSectionId;
	}
	/**  
	 * 获取活动ID  
	 * @return actId 活动ID  
	 */
	public long getActId() {
		return actId;
	}
	/**  
	 * 设置活动ID  
	 * @param actId 活动ID  
	 */
	public void setActId(long actId) {
		this.actId = actId;
	}
	/**  
	 * 获取段落名称  
	 * @return sectionName 段落名称  
	 */
	public String getSectionName() {
		return sectionName;
	}
	/**  
	 * 设置段落名称  
	 * @param sectionName 段落名称  
	 */
	public void setSectionName(String sectionName) {
		this.sectionName = sectionName;
	}
	/**  
	 * 获取段落关系  
	 * @return sectionRelaType 段落关系  
	 */
	public String getSectionRelaType() {
		return sectionRelaType;
	}
	/**  
	 * 设置段落关系  
	 * @param sectionRelaType 段落关系  
	 */
	public void setSectionRelaType(String sectionRelaType) {
		this.sectionRelaType = sectionRelaType;
	}
	/**  
	 * 获取计算优先级  
	 * @return calcPriority 计算优先级  
	 */
	public int getCalcPriority() {
		return calcPriority;
	}
	/**  
	 * 设置计算优先级  
	 * @param calcPriority 计算优先级  
	 */
	public void setCalcPriority(int calcPriority) {
		this.calcPriority = calcPriority;
	}
	/**  
	 * 获取段落类型  
	 * @return sectionType 段落类型  
	 */
	public String getSectionType() {
		return sectionType;
	}
	/**  
	 * 设置段落类型  
	 * @param sectionType 段落类型  
	 */
	public void setSectionType(String sectionType) {
		this.sectionType = sectionType;
	}
	/**  
	 * 获取预存余额类型  
	 * @return payBalType 预存余额类型  
	 */
	public String getPayBalType() {
		return payBalType;
	}
	/**  
	 * 设置预存余额类型  
	 * @param payBalType 预存余额类型  
	 */
	public void setPayBalType(String payBalType) {
		this.payBalType = payBalType;
	}
	/**  
	 * 获取赠送余额类型  
	 * @return presentBalType 赠送余额类型  
	 */
	public String getPresentBalType() {
		return presentBalType;
	}
	/**  
	 * 设置赠送余额类型  
	 * @param presentBalType 赠送余额类型  
	 */
	public void setPresentBalType(String presentBalType) {
		this.presentBalType = presentBalType;
	}
	/**  
	 * 获取赠送规则描述  
	 * @return presentRuleDesc 赠送规则描述  
	 */
	public String getPresentRuleDesc() {
		return presentRuleDesc;
	}
	/**  
	 * 设置赠送规则描述  
	 * @param presentRuleDesc 赠送规则描述  
	 */
	public void setPresentRuleDesc(String presentRuleDesc) {
		this.presentRuleDesc = presentRuleDesc;
	}
	/**  
	 * 获取计算精度  
	 * @return calcPrecision 计算精度  
	 */
	public String getCalcPrecision() {
		return calcPrecision;
	}
	/**  
	 * 设置计算精度  
	 * @param calcPrecision 计算精度  
	 */
	public void setCalcPrecision(String calcPrecision) {
		this.calcPrecision = calcPrecision;
	}
	/**  
	 * 获取赠送最大值  
	 * @return maxValue 赠送最大值  
	 */
	public long getMaxValue() {
		return maxValue;
	}
	/**  
	 * 设置赠送最大值  
	 * @param maxValue 赠送最大值  
	 */
	public void setMaxValue(long maxValue) {
		this.maxValue = maxValue;
	}

	/**
	 * 设置赠送最大值 转String
	 */
	private String maxValueStr;

	public String getMaxValueStr() {
		return maxValueStr;
	}

	public void setMaxValueStr(String maxValueStr) {
		this.maxValueStr = maxValueStr;
	}

	/**
	 * 获取赠送周期类型  
	 * @return presentCycleType 赠送周期类型  
	 */
	public String getPresentCycleType() {
		return presentCycleType;
	}
	/**  
	 * 设置赠送周期类型  
	 * @param presentCycleType 赠送周期类型  
	 */
	public void setPresentCycleType(String presentCycleType) {
		this.presentCycleType = presentCycleType;
	}
	/**  
	 * 获取周期类别标识  
	 * @return cycleTypeId 周期类别标识  
	 */
	public long getCycleTypeId() {
		return cycleTypeId;
	}
	/**  
	 * 设置周期类别标识  
	 * @param cycleTypeId 周期类别标识  
	 */
	public void setCycleTypeId(long cycleTypeId) {
		this.cycleTypeId = cycleTypeId;
	}
	/**  
	 * 获取周期使用上限  
	 * @return cycleUpper 周期使用上限  
	 */
	public long getCycleUpper() {
		return cycleUpper;
	}
	/**  
	 * 设置周期使用上限  
	 * @param cycleUpper 周期使用上限  
	 */
	public void setCycleUpper(long cycleUpper) {
		this.cycleUpper = cycleUpper;
	}
	/**  
	 * 获取周期使用下限  
	 * @return cycleLower 周期使用下限  
	 */
	public long getCycleLower() {
		return cycleLower;
	}
	/**  
	 * 设置周期使用下限  
	 * @param cycleLower 周期使用下限  
	 */
	public void setCycleLower(long cycleLower) {
		this.cycleLower = cycleLower;
	}
	/**  
	 * 获取时间限制标识  
	 * @return timeLimitId 时间限制标识  
	 */
	public long getTimeLimitId() {
		return timeLimitId;
	}
	/**  
	 * 设置时间限制标识  
	 * @param timeLimitId 时间限制标识  
	 */
	public void setTimeLimitId(long timeLimitId) {
		this.timeLimitId = timeLimitId;
	}
	/**  
	 * 获取段落关系名称  
	 * @return sectionRelaTypeName 段落关系名称  
	 */
	public String getSectionRelaTypeName() {
		return sectionRelaTypeName;
	}
	/**  
	 * 设置段落关系名称  
	 * @param sectionRelaTypeName 段落关系名称  
	 */
	public void setSectionRelaTypeName(String sectionRelaTypeName) {
		this.sectionRelaTypeName = sectionRelaTypeName;
	}
	/**  
	 * 获取段落类型名称  
	 * @return sectionTypeName 段落类型名称  
	 */
	public String getSectionTypeName() {
		return sectionTypeName;
	}
	/**  
	 * 设置段落类型名称  
	 * @param sectionTypeName 段落类型名称  
	 */
	public void setSectionTypeName(String sectionTypeName) {
		this.sectionTypeName = sectionTypeName;
	}
	/**
	 * 获取赠送余额类型名称
	 * @return presentBalTypeName 赠送余额类型名称
	 */
	public String getPresentBalTypeName() {
		return presentBalTypeName;
	}
	/**
	 * 设置赠送余额类型名称
	 * @param presentBalTypeName 赠送余额类型名称
	 */
	public void setPresentBalTypeName(String presentBalTypeName) {
		this.presentBalTypeName = presentBalTypeName;
	}
	/**  
	 * 获取赠送周期类型  
	 * @return presentCycleTypeName 赠送周期类型  
	 */
	public String getPresentCycleTypeName() {
		return presentCycleTypeName;
	}
	/**  
	 * 设置赠送周期类型  
	 * @param presentCycleTypeName 赠送周期类型  
	 */
	public void setPresentCycleTypeName(String presentCycleTypeName) {
		this.presentCycleTypeName = presentCycleTypeName;
	}
	/**  
	 * 获取赠送段落明细  
	 * @return presentSectionDets 赠送段落明细  
	 */
	public String getPresentSectionDets() {
		return presentSectionDets;
	}
	/**  
	 * 设置赠送段落明细  
	 * @param presentSectionDets 赠送段落明细  
	 */
	public void setPresentSectionDets(String presentSectionDets) {
		this.presentSectionDets = presentSectionDets;
	}
	/**
	 * 获取发布标识
	 * @return release 发布标识
	 */
	public String getRelease() {
		return release;
	}
	/**
	 * 设置发布标识
	 * @param release 发布标识
	 */
	public void setRelease(String release) {
		this.release = release;
	}

	/******************************定义周期类型e_cycle_type***********************/
	
	/**
	  * 周期类别名称
	  */
	private String cycleTypeName;
	/**
	  * 周期单位
	  */
	private String cycleUnit;
	/**
	  * 周期数
	  */
	private int cycleUnitCount;
	/**
	  * 间隔偏移量
	  */
	private int cycleDuration;
	/**
	  * 偏移天数
	  */
	private int cycleDurationDays;
	/**  
	 * 获取周期类别名称  
	 * @return cycleTypeName 周期类别名称  
	 */
	public String getCycleTypeName() {
		return cycleTypeName;
	}
	/**  
	 * 设置周期类别名称  
	 * @param cycleTypeName 周期类别名称  
	 */
	public void setCycleTypeName(String cycleTypeName) {
		this.cycleTypeName = cycleTypeName;
	}
	/**  
	 * 获取周期单位  
	 * @return cycleUnit 周期单位  
	 */
	public String getCycleUnit() {
		return cycleUnit;
	}
	/**  
	 * 设置周期单位  
	 * @param cycleUnit 周期单位  
	 */
	public void setCycleUnit(String cycleUnit) {
		this.cycleUnit = cycleUnit;
	}
	/**  
	 * 获取周期数  
	 * @return cycleUnitCount 周期数  
	 */
	public int getCycleUnitCount() {
		return cycleUnitCount;
	}
	/**  
	 * 设置周期数  
	 * @param cycleUnitCount 周期数  
	 */
	public void setCycleUnitCount(int cycleUnitCount) {
		this.cycleUnitCount = cycleUnitCount;
	}
	/**  
	 * 获取间隔偏移量  
	 * @return cycleDuration 间隔偏移量  
	 */
	public int getCycleDuration() {
		return cycleDuration;
	}
	/**  
	 * 设置间隔偏移量  
	 * @param cycleDuration 间隔偏移量  
	 */
	public void setCycleDuration(int cycleDuration) {
		this.cycleDuration = cycleDuration;
	}
	/**  
	 * 获取偏移天数  
	 * @return cycleDurationDays 偏移天数  
	 */
	public int getCycleDurationDays() {
		return cycleDurationDays;
	}
	/**  
	 * 设置偏移天数  
	 * @param cycleDurationDays 偏移天数  
	 */
	public void setCycleDurationDays(int cycleDurationDays) {
		this.cycleDurationDays = cycleDurationDays;
	}

	
	/******************************e_time_limit_type**********************/
	
	/**
	  * 开始时间类型
	  */
	private String beginTimeType;
	/**
	  * 开始时间
	  */
	private String beginCalcObject;
	/**
	  * 开始时间偏移单位
	  */
	private String beginTimeUnit;
	/**
	  * 开始时间偏移量
	  */
	private int beginTimeDuration;
	/**
	  * 结束时间类型
	  */
	private String endTimeType;
	/**
	  * 结束时间
	  */
	private String endCalcObject;
	/**
	  * 结束时间偏移单位
	  */
	private String endTimeUnit;
	/**
	  * 结束时间偏移量
	  */
	private int endTimeDuration;
	/**  
	 * 获取开始时间类型  
	 * @return beginTimeType 开始时间类型  
	 */
	public String getBeginTimeType() {
		return beginTimeType;
	}
	/**  
	 * 设置开始时间类型  
	 * @param beginTimeType 开始时间类型  
	 */
	public void setBeginTimeType(String beginTimeType) {
		this.beginTimeType = beginTimeType;
	}
	/**  
	 * 获取开始时间  
	 * @return beginCalcObject 开始时间  
	 */
	public String getBeginCalcObject() {
		return beginCalcObject;
	}
	/**  
	 * 设置开始时间  
	 * @param beginCalcObject 开始时间  
	 */
	public void setBeginCalcObject(String beginCalcObject) {
		this.beginCalcObject = beginCalcObject;
	}
	/**  
	 * 获取开始时间偏移单位  
	 * @return beginTimeUnit 开始时间偏移单位  
	 */
	public String getBeginTimeUnit() {
		return beginTimeUnit;
	}
	/**  
	 * 设置开始时间偏移单位  
	 * @param beginTimeUnit 开始时间偏移单位  
	 */
	public void setBeginTimeUnit(String beginTimeUnit) {
		this.beginTimeUnit = beginTimeUnit;
	}
	/**  
	 * 获取开始时间偏移量  
	 * @return beginTimeDuration 开始时间偏移量  
	 */
	public int getBeginTimeDuration() {
		return beginTimeDuration;
	}
	/**  
	 * 设置开始时间偏移量  
	 * @param beginTimeDuration 开始时间偏移量  
	 */
	public void setBeginTimeDuration(int beginTimeDuration) {
		this.beginTimeDuration = beginTimeDuration;
	}
	/**  
	 * 获取结束时间类型  
	 * @return endTimeType 结束时间类型  
	 */
	public String getEndTimeType() {
		return endTimeType;
	}
	/**  
	 * 设置结束时间类型  
	 * @param endTimeType 结束时间类型  
	 */
	public void setEndTimeType(String endTimeType) {
		this.endTimeType = endTimeType;
	}
	/**  
	 * 获取结束时间  
	 * @return endCalcObject 结束时间  
	 */
	public String getEndCalcObject() {
		return endCalcObject;
	}
	/**  
	 * 设置结束时间  
	 * @param endCalcObject 结束时间  
	 */
	public void setEndCalcObject(String endCalcObject) {
		this.endCalcObject = endCalcObject;
	}
	/**  
	 * 获取结束时间偏移单位  
	 * @return endTimeUnit 结束时间偏移单位  
	 */
	public String getEndTimeUnit() {
		return endTimeUnit;
	}
	/**  
	 * 设置结束时间偏移单位  
	 * @param endTimeUnit 结束时间偏移单位  
	 */
	public void setEndTimeUnit(String endTimeUnit) {
		this.endTimeUnit = endTimeUnit;
	}
	/**  
	 * 获取结束时间偏移量  
	 * @return endTimeDuration 结束时间偏移量  
	 */
	public int getEndTimeDuration() {
		return endTimeDuration;
	}
	/**  
	 * 设置结束时间偏移量  
	 * @param endTimeDuration 结束时间偏移量  
	 */
	public void setEndTimeDuration(int endTimeDuration) {
		this.endTimeDuration = endTimeDuration;
	}
}
