<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.VehiclePriceBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.VehiclePriceBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="BILLING_NO" jdbcType="VARCHAR" property="billingNo" />
    <result column="BATCH" jdbcType="BIGINT" property="batch" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="RT_NO" jdbcType="VARCHAR" property="rtNo" />
    <result column="AUTO_MODEL_NO" jdbcType="VARCHAR" property="autoModelNo" />
    <result column="EFFECT_TIME" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="INVALID_TIME" jdbcType="TIMESTAMP" property="invalidTime" />
    <result column="IS_COVER" jdbcType="VARCHAR" property="isCover" />
    <result column="PRIORITY" jdbcType="DECIMAL" property="priority" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="VERSION" jdbcType="VARCHAR" property="version" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="UNIFIED_PRICE" jdbcType="VARCHAR" property="unifiedPrice" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    SYSTEM_ID, BILLING_NO, BATCH, ORG_CODE, RT_NO, AUTO_MODEL_NO, EFFECT_TIME, INVALID_TIME, 
    IS_COVER, PRIORITY, DATA_OPER_TIME, DATA_OPER_TYPE, VERSION, STATUS, UNIFIED_PRICE
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_vehicle_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_vehicle_price
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_vehicle_price
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    delete from e_vehicle_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_vehicle_price (SYSTEM_ID, BILLING_NO, BATCH, 
      ORG_CODE, RT_NO, AUTO_MODEL_NO, 
      EFFECT_TIME, INVALID_TIME, IS_COVER, 
      PRIORITY, DATA_OPER_TIME, DATA_OPER_TYPE, 
      VERSION, STATUS, UNIFIED_PRICE
      )
    values (#{systemId,jdbcType=BIGINT}, #{billingNo,jdbcType=VARCHAR}, #{batch,jdbcType=BIGINT}, 
      #{orgCode,jdbcType=VARCHAR}, #{rtNo,jdbcType=VARCHAR}, #{autoModelNo,jdbcType=VARCHAR}, 
      #{effectTime,jdbcType=TIMESTAMP}, #{invalidTime,jdbcType=TIMESTAMP}, #{isCover,jdbcType=VARCHAR}, 
      #{priority,jdbcType=DECIMAL}, #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{unifiedPrice,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    insert into e_vehicle_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="billingNo != null">
        BILLING_NO,
      </if>
      <if test="batch != null">
        BATCH,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="rtNo != null">
        RT_NO,
      </if>
      <if test="autoModelNo != null">
        AUTO_MODEL_NO,
      </if>
      <if test="effectTime != null">
        EFFECT_TIME,
      </if>
      <if test="invalidTime != null">
        INVALID_TIME,
      </if>
      <if test="isCover != null">
        IS_COVER,
      </if>
      <if test="priority != null">
        PRIORITY,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="unifiedPrice != null">
        UNIFIED_PRICE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="billingNo != null">
        #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=BIGINT},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="rtNo != null">
        #{rtNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelNo != null">
        #{autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="effectTime != null">
        #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidTime != null">
        #{invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCover != null">
        #{isCover,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="unifiedPrice != null">
        #{unifiedPrice,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    select count(*) from e_vehicle_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_vehicle_price
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.billingNo != null">
        BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batch != null">
        BATCH = #{record.batch,jdbcType=BIGINT},
      </if>
      <if test="record.orgCode != null">
        ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.rtNo != null">
        RT_NO = #{record.rtNo,jdbcType=VARCHAR},
      </if>
      <if test="record.autoModelNo != null">
        AUTO_MODEL_NO = #{record.autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="record.effectTime != null">
        EFFECT_TIME = #{record.effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.invalidTime != null">
        INVALID_TIME = #{record.invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCover != null">
        IS_COVER = #{record.isCover,jdbcType=VARCHAR},
      </if>
      <if test="record.priority != null">
        PRIORITY = #{record.priority,jdbcType=DECIMAL},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        VERSION = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        STATUS = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedPrice != null">
        UNIFIED_PRICE = #{record.unifiedPrice,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_vehicle_price
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      BILLING_NO = #{record.billingNo,jdbcType=VARCHAR},
      BATCH = #{record.batch,jdbcType=BIGINT},
      ORG_CODE = #{record.orgCode,jdbcType=VARCHAR},
      RT_NO = #{record.rtNo,jdbcType=VARCHAR},
      AUTO_MODEL_NO = #{record.autoModelNo,jdbcType=VARCHAR},
      EFFECT_TIME = #{record.effectTime,jdbcType=TIMESTAMP},
      INVALID_TIME = #{record.invalidTime,jdbcType=TIMESTAMP},
      IS_COVER = #{record.isCover,jdbcType=VARCHAR},
      PRIORITY = #{record.priority,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      VERSION = #{record.version,jdbcType=VARCHAR},
      STATUS = #{record.status,jdbcType=VARCHAR},
      UNIFIED_PRICE = #{record.unifiedPrice,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_vehicle_price
    <set>
      <if test="billingNo != null">
        BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        BATCH = #{batch,jdbcType=BIGINT},
      </if>
      <if test="orgCode != null">
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="rtNo != null">
        RT_NO = #{rtNo,jdbcType=VARCHAR},
      </if>
      <if test="autoModelNo != null">
        AUTO_MODEL_NO = #{autoModelNo,jdbcType=VARCHAR},
      </if>
      <if test="effectTime != null">
        EFFECT_TIME = #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalidTime != null">
        INVALID_TIME = #{invalidTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCover != null">
        IS_COVER = #{isCover,jdbcType=VARCHAR},
      </if>
      <if test="priority != null">
        PRIORITY = #{priority,jdbcType=DECIMAL},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="unifiedPrice != null">
        UNIFIED_PRICE = #{unifiedPrice,jdbcType=VARCHAR},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.VehiclePriceBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 04 21:08:25 CST 2016.
    -->
    update e_vehicle_price
    set BILLING_NO = #{billingNo,jdbcType=VARCHAR},
      BATCH = #{batch,jdbcType=BIGINT},
      ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      RT_NO = #{rtNo,jdbcType=VARCHAR},
      AUTO_MODEL_NO = #{autoModelNo,jdbcType=VARCHAR},
      EFFECT_TIME = #{effectTime,jdbcType=TIMESTAMP},
      INVALID_TIME = #{invalidTime,jdbcType=TIMESTAMP},
      IS_COVER = #{isCover,jdbcType=VARCHAR},
      PRIORITY = #{priority,jdbcType=DECIMAL},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      VERSION = #{version,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      UNIFIED_PRICE = #{unifiedPrice,jdbcType=VARCHAR}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>