package com.ls.ner.billing.xpcharge.dao;


import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @dateTime 2018-03-21
 * @description 充电计费下发
 */
public interface IXpPileSendDao {

    /**
     * @description 查询最新计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheNewChc(ChcbillSendQueryCondition m);

    /**
     * @description 查询当前计费版本
     * <AUTHOR>
     * @create 2018-03-26 14:53:27
     */
    public List<Map> queryTheCurrentChc(Map m);

    public void insertChcbillSend(ChcbillSendBo cSendBo);

    int  queryPileBillCount(Map sercher);

    int queryTheNewChcNum(ChcbillSendQueryCondition condition);

    public List<Map> queryPileBillInfo(ChcbillSendQueryCondition m);
    public int queryPileBillInfoCount(ChcbillSendQueryCondition m);
}
