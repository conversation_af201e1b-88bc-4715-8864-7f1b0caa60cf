package com.ls.ner.billing.market.bo;

import com.pt.poseidon.api.framework.DicAttribute;

import java.io.Serializable;
import java.util.Date;

/**
 * 优惠券
 * description
 * 创建时间 2016年4月7日下午3:39:11
 * 创建人 lise
 */
public class CouponBo implements Serializable {

    private static final long serialVersionUID = 2963444803161908118L;
    private String orgCode; // 管理单位
    private String CrtOrgName;
    private String cpnNo; // 优惠券编号
    private String cpnName; // 优惠券名称
    private String cpnType; // 优惠券类型，01现金券 02折扣券
    @DicAttribute(dicName = "codeDict", key = "cpnType", subType = "cpnType")
    private String cpnTypeName;
    private String cpnDctType; //优惠内容 0100 整单金额 0110 服务费
    private String fullReductionAmt; // 满减金额
    private String maximumDiscountAmt; // 最大折扣金额
    private String cpnAmt; // 面额，或者折扣
    private String eftDate; // 生效日期
    private String invDate; // 失效日期
    private String cpnNumOrigin; // 发行数量
    private String cpnNum; // 发行数量
    private String limGetNum; // 每人限领
    private String alrdyGetNum; // 已领数量
    private String cpnMarks; // 优惠券描述
    private String cpnStatus; // 优惠券状态 0草稿 1在用 2失效 3作废
    @DicAttribute(dicName = "codeDict", key = "cpnStatus", subType = "cpnState")
    private String cpnStatusName;
    private String creTime; // 创建时间
    private String creEmp; // 创建人员
    private String effectTime; // 查询生效范围
    private String invFlag;//是否失效
    private String dctType; // 优惠类别（优惠券）（01租赁 02充电）
    private String busiType; // 产品业务类，租车、充电
    @DicAttribute(dicName = "codeDict", key = "busiType", subType = "orderType")
    private String busiTypeName;
    private String timeUnit; // 生效时间偏移单位,日、月等
    @DicAttribute(dicName = "codeDict", key = "timeUnit", subType = "timeUnit")
    private String timeUnitName;
    private String cpnTimeType; // 优惠券生效时间类型，1绝对日期 2相对日期
    private String subBusiType; // 暂不考虑
    private String cpnCondType; // 优惠条件类型，00总计价金额 01计价数量 02计价金额
    private String dctCondTypeName; // 优惠券条件类型名称
    private String prodId; // 计费参数B_PROD表主键（优惠券内容）
    private String docCondProdName; // 优惠券条件内容名称(PROD_NAME)
    private String calcPrecisionDigits; //计算结果精度小数位数
    private String calcPrecisionDigitsName; //计算结果精度小数位数名称
    private String calcPrecision; // 计算结果精度
    private String calcPrecisionName; // 计算结果精度名称
    private String cpnId; // 优惠券主键
    private String timeDuration; // 生效时间偏移量，相对日期时使用
    private String handleType; // 操作类型，01管理界面-新增按钮；02管理界面-修改按钮；03展现管理界面；04展现查询界面；05发放功能中触发选择功能
    private String calcMaxValue; // 计算结果的上限值
    private String calcMinValue;// 计算结果的下限值
    private String stateTime; // 状态时间
    private String dctCalcMethod;// 优惠计算方法
    private String filePath; // 优惠券图片路径
    private String attachId; // 图片附件ID
    private String effectDate;// 生效时间
    private String isEffect; // 是否立即生效


    /************发放信息***********/
    private String putId; // 发放主键
    private String putNum; // 当前发放数量

    /************优惠条件***********/
    private String dctCondId; // 优惠条件主键DCT_COND_ID
    private String dctCondFlag; // 优惠条件标志；1无条件 2条件
    private String dctCondType; // 优惠券条件类型.00总计价金额 01计价数量 02计价金额
    private String dctTypeName; // 优惠类别名称（优惠券）
    private String dctCondValue; // 优惠券条件值
    private String dctProdId; //  计费参数B_PROD表主键（PROD_ID优惠条件内容）
    private String dctProdName; // 计费参数优惠内容标题
    private String ActId; // 活动ID
    private String dctBusiType; // 优惠条件业务类型
    private String dctCondAreaFlag; // 活动ID
    private String dctCondCity; // 优惠条件业务类型
    private String dctCondStation; // 优惠条件业务类型
    private String dctCondBuild;
    private String timeCondFlag;//优惠时段条件标识 1全天 2 区间
    private String timeCondBeg;//开始时间
    private String timeCondEnd;//结束时间
    /************优惠券使用***********/
    private String custNo; // 客户编号
    private String custName; // 客戶名称
    private String mobile; // 手机号码
    private String custSortCode; // 客户分类
    private String custSortCodeName; //客户分类
    private String getSource; // 来源
    private String getSourceName; // 来源
    private String getWay; // 获取方式
    private String getWayName; // 获取方式
    private String getChannel; // 获取渠道
    private String getChannelName; // 获取渠道
    private String getTime; // 获取时间
    private String useTime; // 使用时间
    private String appNo; // 订单编号
    private String useStatus; // 使用状态
    private String useStatusName; // 使用状态
    private String getNum;// 每人已领数量

    private String useNum;
    private String unUseNum;
    private String totalChargeAmt;
    private String totalServiceAmt;
    private String totalCpnTBal;
    private String chargeAmt;
    private String serviceAmt;
    private String cpnTBal;
    private String settleTBal;

    /**
     * cpnArr = cpnName + '$' + cpnId + ',';
     */
    private String cpnArr;

    /**
     *兑换优惠券所需积分
     */
    private String needPointNum;

    private String useCount;
    private String useMoney;
    private String cityCodes;
    private String stationIds;
    private String buildId;
    private String city;
    private String stationId;
    private String buildType;
    private String cityType;
    private String stationType;
    private String isDeleteImg;
    private String superpositionFlag; // 会员叠加 '1' 允许 '2'不允许

    private Boolean isDelay;//列表查询使用，是否存在延迟任务

    private String cpnPurpose;//优惠卷用途 1.首次添加爱车 2.其他

    public String getCpnPurpose() {
        return cpnPurpose;
    }

    public void setCpnPurpose(String cpnPurpose) {
        this.cpnPurpose = cpnPurpose;
    }

    public String getCpnDctType() {
        return cpnDctType;
    }

    public void setCpnDctType(String cpnDctType) {
        this.cpnDctType = cpnDctType;
    }

    public Boolean getDelay() {
        return isDelay;
    }

    public void setDelay(Boolean delay) {
        isDelay = delay;
    }

    public String getSuperpositionFlag() {
        return superpositionFlag;
    }

    public void setSuperpositionFlag(String superpositionFlag) {
        this.superpositionFlag = superpositionFlag;
    }


    public String getFullReductionAmt() {
        return fullReductionAmt;
    }

    public void setFullReductionAmt(String fullReductionAmt) {
        this.fullReductionAmt = fullReductionAmt;
    }

    public String getMaximumDiscountAmt() {
        return maximumDiscountAmt;
    }

    public void setMaximumDiscountAmt(String maximumDiscountAmt) {
        this.maximumDiscountAmt = maximumDiscountAmt;
    }

    public String getCpnNumOrigin() {
        return cpnNumOrigin;
    }

    public void setCpnNumOrigin(String cpnNumOrigin) {
        this.cpnNumOrigin = cpnNumOrigin;
    }

    public String getUseCount() {
        return useCount;
    }

    public void setUseCount(String useCount) {
        this.useCount = useCount;
    }

    public String getUseMoney() {
        return useMoney;
    }

    public void setUseMoney(String useMoney) {
        this.useMoney = useMoney;
    }

    public String getNeedPointNum() {
        return needPointNum;
    }

    public void setNeedPointNum(String needPointNum) {
        this.needPointNum = needPointNum;
    }

    public String getCpnArr() {
        return cpnArr;
    }

    public void setCpnArr(String cpnArr) {
        this.cpnArr = cpnArr;
    }

    public String getEffectDate() {
        return effectDate;
    }

    public void setEffectDate(String effectDate) {
        this.effectDate = effectDate;
    }

    public String getIsEffect() {
        return isEffect;
    }

    public void setIsEffect(String isEffect) {
        this.isEffect = isEffect;
    }

    public String getGetNum() {
        return getNum;
    }

    public void setGetNum(String getNum) {
        this.getNum = getNum;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCustSortCode() {
        return custSortCode;
    }

    public void setCustSortCode(String custSortCode) {
        this.custSortCode = custSortCode;
    }

    public String getCustSortCodeName() {
        return custSortCodeName;
    }

    public void setCustSortCodeName(String custSortCodeName) {
        this.custSortCodeName = custSortCodeName;
    }

    public String getGetSource() {
        return getSource;
    }

    public void setGetSource(String getSource) {
        this.getSource = getSource;
    }

    public String getGetSourceName() {
        return getSourceName;
    }

    public void setGetSourceName(String getSourceName) {
        this.getSourceName = getSourceName;
    }

    public String getGetWay() {
        return getWay;
    }

    public void setGetWay(String getWay) {
        this.getWay = getWay;
    }

    public String getGetWayName() {
        return getWayName;
    }

    public void setGetWayName(String getWayName) {
        this.getWayName = getWayName;
    }

    public String getGetChannel() {
        return getChannel;
    }

    public void setGetChannel(String getChannel) {
        this.getChannel = getChannel;
    }

    public String getGetChannelName() {
        return getChannelName;
    }

    public void setGetChannelName(String getChannelName) {
        this.getChannelName = getChannelName;
    }

    public String getGetTime() {
        return getTime;
    }

    public void setGetTime(String getTime) {
        this.getTime = getTime;
    }

    public String getUseTime() {
        return useTime;
    }

    public void setUseTime(String useTime) {
        this.useTime = useTime;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }

    public String getUseStatus() {
        return useStatus;
    }

    public void setUseStatus(String useStatus) {
        this.useStatus = useStatus;
    }

    public String getUseStatusName() {
        return useStatusName;
    }

    public void setUseStatusName(String useStatusName) {
        this.useStatusName = useStatusName;
    }

    public String getAttachId() {
        return attachId;
    }

    public void setAttachId(String attachId) {
        this.attachId = attachId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getCrtOrgName() {
        return CrtOrgName;
    }

    public void setCrtOrgName(String crtOrgName) {
        CrtOrgName = crtOrgName;
    }

    public String getDctCondId() {
        return dctCondId;
    }

    public void setDctCondId(String dctCondId) {
        this.dctCondId = dctCondId;
    }

    public String getDctBusiType() {
        return dctBusiType;
    }

    public void setDctBusiType(String dctBusiType) {
        this.dctBusiType = dctBusiType;
    }

    public String getPutId() {
        return putId;
    }

    public void setPutId(String putId) {
        this.putId = putId;
    }

    public String getDctProdName() {
        return dctProdName;
    }

    public void setDctProdName(String dctProdName) {
        this.dctProdName = dctProdName;
    }

    public String getDctCondValue() {
        return dctCondValue;
    }

    public void setDctCondValue(String dctCondValue) {
        this.dctCondValue = dctCondValue;
    }

    public String getDctProdId() {
        return dctProdId;
    }

    public void setDctProdId(String dctProdId) {
        this.dctProdId = dctProdId;
    }

    public String getDctTypeName() {
        return dctTypeName;
    }

    public void setDctTypeName(String dctTypeName) {
        this.dctTypeName = dctTypeName;
    }

    public String getDctCondTypeName() {
        return dctCondTypeName;
    }

    public void setDctCondTypeName(String dctCondTypeName) {
        this.dctCondTypeName = dctCondTypeName;
    }

    public String getDocCondProdName() {
        return docCondProdName;
    }

    public void setDocCondProdName(String docCondProdName) {
        this.docCondProdName = docCondProdName;
    }

    public String getCalcPrecisionDigitsName() {
        return calcPrecisionDigitsName;
    }

    public void setCalcPrecisionDigitsName(String calcPrecisionDigitsName) {
        this.calcPrecisionDigitsName = calcPrecisionDigitsName;
    }

    public String getCalcPrecisionName() {
        return calcPrecisionName;
    }

    public void setCalcPrecisionName(String calcPrecisionName) {
        this.calcPrecisionName = calcPrecisionName;
    }

    public String getActId() {
        return ActId;
    }

    public void setActId(String actId) {
        ActId = actId;
    }

    public String getDctCalcMethod() {
        return dctCalcMethod;
    }

    public void setDctCalcMethod(String dctCalcMethod) {
        this.dctCalcMethod = dctCalcMethod;
    }

    public String getCalcMinValue() {
        return calcMinValue;
    }

    public void setCalcMinValue(String calcMinValue) {
        this.calcMinValue = calcMinValue;
    }

    public String getCalcMaxValue() {
        return calcMaxValue;
    }

    public void setCalcMaxValue(String calcMaxValue) {
        this.calcMaxValue = calcMaxValue;
    }

    public String getDctCondFlag() {
        return dctCondFlag;
    }

    public void setDctCondFlag(String dctCondFlag) {
        this.dctCondFlag = dctCondFlag;
    }

    public String getStateTime() {
        return stateTime;
    }

    public void setStateTime(String stateTime) {
        this.stateTime = stateTime;
    }

    public String getProdId() {
        return prodId;
    }

    public void setProdId(String prodId) {
        this.prodId = prodId;
    }

    public String getHandleType() {
        return handleType;
    }

    public void setHandleType(String handleType) {
        this.handleType = handleType;
    }

    public String getCpnCondType() {
        return cpnCondType;
    }

    public void setCpnCondType(String cpnCondType) {
        this.cpnCondType = cpnCondType;
    }

    public String getTimeDuration() {
        return timeDuration;
    }

    public void setTimeDuration(String timeDuration) {
        this.timeDuration = timeDuration;
    }

    public String getCpnId() {
        return cpnId;
    }

    public void setCpnId(String cpnId) {
        this.cpnId = cpnId;
    }

    public String getDctCondType() {
        return dctCondType;
    }

    public void setDctCondType(String dctCondType) {
        this.dctCondType = dctCondType;
    }

    public String getCalcPrecisionDigits() {
        return calcPrecisionDigits;
    }

    public void setCalcPrecisionDigits(String calcPrecisionDigits) {
        this.calcPrecisionDigits = calcPrecisionDigits;
    }

    public String getCalcPrecision() {
        return calcPrecision;
    }

    public void setCalcPrecision(String calcPrecision) {
        this.calcPrecision = calcPrecision;
    }

    public String getSubBusiType() {
        return subBusiType;
    }

    public void setSubBusiType(String subBusiType) {
        this.subBusiType = subBusiType;
    }

    public String getCpnTimeType() {
        return cpnTimeType;
    }

    public void setCpnTimeType(String cpnTimeType) {
        this.cpnTimeType = cpnTimeType;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getBusiTypeName() {
        return busiTypeName;
    }

    public void setBusiTypeName(String busiTypeName) {
        this.busiTypeName = busiTypeName;
    }

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public String getTimeUnitName() {
        return timeUnitName;
    }

    public void setTimeUnitName(String timeUnitName) {
        this.timeUnitName = timeUnitName;
    }

    public String getPutNum() {
        return putNum;
    }

    public void setPutNum(String putNum) {
        this.putNum = putNum;
    }

    public String getDctType() {
        return dctType;
    }

    public void setDctType(String dctType) {
        this.dctType = dctType;
    }


    public String getInvFlag() {
        return invFlag;
    }

    public void setInvFlag(String invFlag) {
        this.invFlag = invFlag;
    }

    public String getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(String effectTime) {
        this.effectTime = effectTime;
    }

    public String getCpnTypeName() {
        return cpnTypeName;
    }

    public void setCpnTypeName(String cpnTypeName) {
        this.cpnTypeName = cpnTypeName;
    }

    public String getCpnStatusName() {
        return cpnStatusName;
    }

    public void setCpnStatusName(String cpnStatusName) {
        this.cpnStatusName = cpnStatusName;
    }


    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }


    public String getCpnNo() {
        return cpnNo;
    }

    public void setCpnNo(String cpnNo) {
        this.cpnNo = cpnNo;
    }

    public String getCpnName() {
        return cpnName;
    }

    public void setCpnName(String cpnName) {
        this.cpnName = cpnName;
    }

    public String getCpnType() {
        return cpnType;
    }

    public void setCpnType(String cpnType) {
        this.cpnType = cpnType;
    }

    public String getCpnAmt() {
        return cpnAmt;
    }

    public void setCpnAmt(String cpnAmt) {
        this.cpnAmt = cpnAmt;
    }


    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getCpnNum() {
        return cpnNum;
    }

    public void setCpnNum(String cpnNum) {
        this.cpnNum = cpnNum;
    }

    public String getLimGetNum() {
        return limGetNum;
    }

    public void setLimGetNum(String limGetNum) {
        this.limGetNum = limGetNum;
    }

    public String getAlrdyGetNum() {
        return alrdyGetNum;
    }

    public void setAlrdyGetNum(String alrdyGetNum) {
        this.alrdyGetNum = alrdyGetNum;
    }

    public String getCpnMarks() {
        return cpnMarks;
    }

    public void setCpnMarks(String cpnMarks) {
        this.cpnMarks = cpnMarks;
    }

    public String getCpnStatus() {
        return cpnStatus;
    }

    public void setCpnStatus(String cpnStatus) {
        this.cpnStatus = cpnStatus;
    }

    public String getCreTime() {
        return creTime;
    }

    public void setCreTime(String creTime) {
        this.creTime = creTime;
    }

    public String getCreEmp() {
        return creEmp;
    }

    public void setCreEmp(String creEmp) {
        this.creEmp = creEmp;
    }

    public String getDctCondAreaFlag() {
        return dctCondAreaFlag;
    }

    public void setDctCondAreaFlag(String dctCondAreaFlag) {
        this.dctCondAreaFlag = dctCondAreaFlag;
    }

    public String getDctCondCity() {
        return dctCondCity;
    }

    public void setDctCondCity(String dctCondCity) {
        this.dctCondCity = dctCondCity;
    }

    public String getDctCondStation() {
        return dctCondStation;
    }

    public void setDctCondStation(String dctCondStation) {
        this.dctCondStation = dctCondStation;
    }

    public String getTimeCondFlag() {
        return timeCondFlag;
    }

    public void setTimeCondFlag(String timeCondFlag) {
        this.timeCondFlag = timeCondFlag;
    }

    public String getTimeCondBeg() {
        return timeCondBeg;
    }

    public void setTimeCondBeg(String timeCondBeg) {
        this.timeCondBeg = timeCondBeg;
    }

    public String getTimeCondEnd() {
        return timeCondEnd;
    }

    public void setTimeCondEnd(String timeCondEnd) {
        this.timeCondEnd = timeCondEnd;
    }

    public String getDctCondBuild() {
        return dctCondBuild;
    }

    public void setDctCondBuild(String dctCondBuild) {
        this.dctCondBuild = dctCondBuild;
    }

    public String getCityCodes() {
        return cityCodes;
    }

    public void setCityCodes(String cityCodes) {
        this.cityCodes = cityCodes;
    }

    public String getStationIds() {
        return stationIds;
    }

    public void setStationIds(String stationIds) {
        this.stationIds = stationIds;
    }

    public String getBuildId() {
        return buildId;
    }

    public void setBuildId(String buildId) {
        this.buildId = buildId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getBuildType() {
        return buildType;
    }

    public void setBuildType(String buildType) {
        this.buildType = buildType;
    }

    public String getCityType() {
        return cityType;
    }

    public void setCityType(String cityType) {
        this.cityType = cityType;
    }

    public String getStationType() {
        return stationType;
    }

    public void setStationType(String stationType) {
        this.stationType = stationType;
    }

    public String getIsDeleteImg() {
        return isDeleteImg;
    }

    public void setIsDeleteImg(String isDeleteImg) {
        this.isDeleteImg = isDeleteImg;
    }

    public String getUseNum() {
        return useNum;
    }

    public void setUseNum(String useNum) {
        this.useNum = useNum;
    }

    public String getUnUseNum() {
        return unUseNum;
    }

    public void setUnUseNum(String unUseNum) {
        this.unUseNum = unUseNum;
    }

    public String getTotalChargeAmt() {
        return totalChargeAmt;
    }

    public void setTotalChargeAmt(String totalChargeAmt) {
        this.totalChargeAmt = totalChargeAmt;
    }

    public String getTotalServiceAmt() {
        return totalServiceAmt;
    }

    public void setTotalServiceAmt(String totalServiceAmt) {
        this.totalServiceAmt = totalServiceAmt;
    }

    public String getTotalCpnTBal() {
        return totalCpnTBal;
    }

    public void setTotalCpnTBal(String totalCpnTBal) {
        this.totalCpnTBal = totalCpnTBal;
    }

    public String getChargeAmt() {
        return chargeAmt;
    }

    public void setChargeAmt(String chargeAmt) {
        this.chargeAmt = chargeAmt;
    }

    public String getServiceAmt() {
        return serviceAmt;
    }

    public void setServiceAmt(String serviceAmt) {
        this.serviceAmt = serviceAmt;
    }

    public String getCpnTBal() {
        return cpnTBal;
    }

    public void setCpnTBal(String cpnTBal) {
        this.cpnTBal = cpnTBal;
    }

    public String getSettleTBal() {
        return settleTBal;
    }

    public void setSettleTBal(String settleTBal) {
        this.settleTBal = settleTBal;
    }

    // 赠送积分相关
    private String integralNumFlag; // 是否赠送积分标识，1为不赠送，0或null为赠送
    private Integer integralNum; // 赠送积分数量

    public String getIntegralNumFlag() {
        return integralNumFlag;
    }
    public void setIntegralNumFlag(String integralNumFlag) {
        this.integralNumFlag = integralNumFlag;
    }
    public Integer getIntegralNum() {
        return integralNum;
    }
    public void setIntegralNum(Integer integralNum) {
        this.integralNum = integralNum;
    }
}
