package com.ls.ner.billing.charge.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @dateTime 2016-11-30
 * @description 充电计费配置rpc
 */
public interface IChargeBillRpcDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-30
	 * @description 批量查询充电计费配置
	 */
	public List<Map> queryChargeBillingConfs(Map map);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-30
	 * @description 批量查询充电分时设置
	 */
	public List<Map> queryChargePeriods(@Param("chcNo") String chcNo, @Param("chcNos") String[] chcNos);



	/**
	 *@Description: 查询站点下发的计费模型
	 *@Author: laibihui
	 *@Time: 2017/9/26 10:09
	 * @param map : station
	 */
	List<Map<String,Object>> getStationChargePeriods(Map map);

	/**
	 * @param inMap
	 * @description
	 * <AUTHOR>
	 * @create 2017-10-23 15:11:43
	 */
	List<Map<String,Object>>  getAppendCharItem(Map<String, Object> inMap);

	/**
	 *删除站点计费配置
	 * @param inMap
	 */
	void delChargeBilling(Map<String, Object> inMap);

	/**
	 *删除桩计费配置
	 * @param inMap
	 */
	void delPileChargeBilling(Map<String, Object> inMap);

	/**
	 * 新增站点计费配置
	 * @param inMap
	 */
	void insertChargeBilling(Map<String, Object> inMap);

	/**
	 * 新增桩计费配置
	 * @param inMap
	 */
	void insertPileChargeBilling(Map<String, Object> inMap);

	/**
	 * 修改站点计费配置
	 * @param inMap
	 */
	void updateChargeBilling(Map<String, Object> inMap);

	/**
	 * 查询新版计费
	 * @param inMap
	 * @return
	 */
	List<Map> queryNewChargeBillingConfs(Map<String, Object> inMap);

	/**
	 * 查询桩计费
	 * @param inMap
	 * @return
	 */
	List<Map> queryNewChargePileBillingConfs(Map<String, Object> inMap);

	List<Map<String,Object>> getNewStationChargePeriods(Map map);

	List<Map<String,Object>> getPileStationChargePeriods(Map map);

	/**
	 * @param inMap
	 * @description 新增大客户计费配置
	 * <AUTHOR>
	 * @create 2018-10-30 10:34:16
	 */
  void insertCustGroupChargeBilling(Map<String, Object> inMap);

  /**
   * @param inMap
   * @description 删除大客户计费配置
   * <AUTHOR>
   * @create 2018-10-30 10:37:16
   */
	void delCustGroupChargeBilling(Map<String, Object> inMap);

	/**
	 * @param inMap
	 * @description 查询大客户计费
	 * <AUTHOR>
	 * @create 2018-10-30 14:15:34
	 */
	List<Map> queryCustGroupChargeBilling(Map<String, Object> inMap);

	/**
	 * @Description 查询企业计费
	 * <AUTHOR>
	 * @Date 2023/7/26 16:14
	 * @param: inMap
	 */
	Map queryUserChargeBilling(Map<String, Object> inMap);

	/**
	 * @Description 校验是否可用
	 * <AUTHOR>
	 * @Date 2023/7/27 17:15
	 * @param: stationId
	 * @param: chcNo
	 */
	int checkIsExistByStationIdAndChcNo(@Param("stationId") String stationId, @Param("chcNo") String chcNo);

    Map queryPileBillingList(String pileId);
}
