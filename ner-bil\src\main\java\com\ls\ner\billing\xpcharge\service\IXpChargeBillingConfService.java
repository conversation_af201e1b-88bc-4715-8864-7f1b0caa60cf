package com.ls.ner.billing.xpcharge.service;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;

import java.util.List;

/**
 * 充电计费配置
 * <AUTHOR>
 */
public interface IXpChargeBillingConfService {

    /**
     * <AUTHOR>
     * @dateTime 2018-03-16
     * @description 查询充电计费配置E_CHARGE_BILLING_CONF
     */
    public List<ChargeBillingConfBo> queryChargeBillingConfs(XpChargeBillingConfQueryCondition bo);

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF
     */
    public int queryChargeBillingConfsNum(XpChargeBillingConfQueryCondition bo);

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 新增充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void insertChargeBillingConf(ChargeBillingConfBo bo);

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 更新充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void updateChargeBillingConf(ChargeBillingConfBo bo);

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 发布充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
     */
    public void releaseChargeBillingConf(ChargeBillingConfBo bo);


    List<ChargeBillingConfBo> queryXpChargeBillingConfInfo(XpChargeBillingConfQueryCondition xpCondition);

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 计费生效任务调度
     */
    public void excuteBillingTask() throws Exception;

    /**
     * <AUTHOR>
     * @dateTime 2018-03-20
     * @description 计费下发任务调度
     */
    public void excuteBillingSendTask(String stationId,String pileNo) throws Exception;

    void executeBillSendTask();

}
