<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.IIntegeralCustRPCDao">


	<select id="ixExistsCustNum" resultType="int"
		parameterType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		select count(1) from e_integral_cust
		where cust_no = #{custNo}
	</select>

	<select id="getCustIntegeralNum" resultType="string"
		parameterType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		select integral_number integralNumber
		from e_integral_cust
		where cust_no = #{custNo}
	</select>

	<insert id="addIntegeralCust" parameterType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		insert into e_integral_cust
		(cust_no,cust_phone,integral_number)
		values(
			#{custNo},#{custPhone},#{integralNumber}
		)
	</insert>

	<update id="updateIntegeralCust" parameterType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		update e_integral_cust
		set integral_number = #{integralNumber},
		update_time = now()
		where cust_no = #{custNo}
	</update>

	<insert id="addIntegeralCustLog" parameterType="com.ls.ner.billing.market.bo.IntegeralCustLogBo">
		insert into e_integral_cust_log
		(integral_no,event_name,event_type,phone,charge_num,integral_number,charge_type,charge_time)
		values(
			#{integralNo},#{eventName},#{eventType},#{phone},#{chargeNum},#{integralNumber},#{chargeType},now()
		)
	</insert>
	<select id="getLogNum" parameterType="com.ls.ner.billing.market.bo.IntegeralCustLogBo" resultType="int">
		select count(1) num from e_integral_cust_log
		where event_name = #{eventName} and phone = #{phone} and charge_num > 0
	</select>

	<select id="getIntegeralCustLogCount" parameterType="com.ls.ner.billing.market.bo.IntegeralCustLogBo" resultType="int">
		select count(1) num
		from e_integral_cust_log
		<where>
			<if test="phone != null and phone !=''">
	            AND phone = #{phone}
	        </if>
	        <if test="beginTime != null and beginTime != ''">
	            AND charge_time >= #{beginTime}
	        </if>
	        <if test="endTime != null and endTime != ''">
	            AND charge_time <![CDATA[<=]]> CONCAT(#{endTime},' 23:59:59')
	        </if>
	        <if test="chargeType != null and chargeType !='' and chargeType !='all'">
	            AND charge_type = #{chargeType}
	        </if>
			<if test="eventType != null and eventType != ''">
				AND event_type = #{eventType}
			</if>
		</where>
	</select>

	<select id="getIntegeralCustLogList" parameterType="com.ls.ner.billing.market.bo.IntegeralCustLogBo"
		resultType="com.ls.ner.billing.market.bo.IntegeralCustLogBo">
		select
			 log.id,
			 log.integral_no integralNo,
			 log.integral_number integralNumber,
			 log.event_name eventName,
			 log.phone,
			 log.charge_num chargeNum,
			 log.charge_type chargeType,
			 log.charge_time chargeTime,
			 e.goods_name goodsName
		from e_integral_cust_log log
		left join e_integral_exchange e on e.INTEGRAL_ID = log.integral_no
		<where>
			log.charge_num > 0
			<if test="phone != null and phone !=''">
	            AND log.phone = #{phone}
	        </if>
	        <if test="beginTime != null and beginTime != ''">
	            AND log.charge_time >= #{beginTime}
	        </if>
	        <if test="endTime != null and endTime != ''">
	            AND log.charge_time <![CDATA[<=]]> CONCAT(#{endTime},' 23:59:59')
	        </if>
	        <if test="chargeType != null and chargeType !='' and chargeType !='all'">
	            AND log.charge_type = #{chargeType}
	        </if>
		</where>
		order by log.charge_time desc
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>
	<select id="getIntegeralCustLogListByWX" parameterType="com.ls.ner.billing.market.bo.IntegeralCustLogBo"
			resultType="com.ls.ner.billing.market.bo.IntegeralCustLogBo">
		select
		a.id,
		a.integral_no integralNo,
		a.integral_number integralNumber,
		a.event_name eventName,
		a.phone,
		a.charge_num chargeNum,
		a.charge_type chargeType,
		a.charge_time chargeTime,
		b.GOODS_NAME goodsName
		from e_integral_cust_log a
		left join e_integral_exchange b on b.INTEGRAL_ID = a.integral_no
		<where>
			a.charge_num > 0
			<if test="phone != null and phone !=''">
				AND a.phone = #{phone}
			</if>
			<if test="beginTime != null and beginTime != ''">
				AND a.charge_time >= #{beginTime}
			</if>
			<if test="endTime != null and endTime != ''">
				AND a.charge_time <![CDATA[<=]]> CONCAT(#{endTime},' 23:59:59')
			</if>
			<if test="chargeType != null and chargeType !='' and chargeType !='all'">
				AND a.charge_type = #{chargeType}
			</if>
		</where>
		order by a.charge_time desc
		<if test="limit!=null and limit!=0">
			limit #{start} ,#{limit}
		</if>
	</select>
	<update id="updateIntegeralCustByTask" parameterType="java.util.Map">
		update e_integral_cust
		set integral_number = #{integralNumber},
		update_time = now()
		where cust_no in
			<foreach item="item" index="index" collection="integralNoList" open="(" separator="," close=")">
				#{item}
			</foreach>
	</update>

	<select id="getIntegeralCustList" resultType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		select id,
			cust_no custNo,
			cust_phone custPhone,
			integral_number integralNumber,
			update_time updateTime
		from e_integral_cust
	</select>

	<select id="getCustIntegralNumber" resultType="com.ls.ner.billing.market.bo.IntegeralCustBo">
		select c.id,
		c.cust_no custNo,
		c.cust_phone custPhone,
		c.integral_number integralNumber,
		c.update_time updateTime,
		rc.rank_name vipLevel,
		l.charge_time chargeTime
		from e_integral_cust c LEFT JOIN e_vip_level l ON c.cust_no = l.cust_id
		LEFT JOIN e_regular_vip_rank_config rc ON l.vip_level = rc.rank_no
		where c.cust_no in
		<foreach item="item" index="index" collection="custIdList" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getCustIntegralNumberById" resultType="String">
		select
		integral_number
		from e_integral_cust
		where cust_no = #{custId}
	</select>

</mapper>
