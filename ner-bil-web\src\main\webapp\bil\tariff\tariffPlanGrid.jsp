<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="资费标准管理" />
<ls:body>
    <%
      Object flag = request.getAttribute("flag");
      String subBe = request.getAttribute("subBe")==null?"":request.getAttribute("subBe").toString();
    %>
		<ls:form id="tariffPlanQueryForm" name="tariffPlanQueryForm">
				<ls:title text="查询条件 "></ls:title>
				<table class="tab_search">
						<tr>
						    <td>
                    <ls:label text="管理单位" />
                </td>
                <td>
                    <ls:text type="hidden" name="orgCode" property="orgCode"/>
                    <ls:text name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" />
<%--                     <ls:text imageKey="search" onClickImage="getOrg" name="orgCodeName" property="orgCodeName" enabled="false" onchanged="changeOrgName" />
 --%>                </td>
								<td>
										<ls:label text="租赁方式" />
								</td>
								<td>
								    <%
                      if (subBe != null && !subBe.equals("")) { //flag用于选择标记
                    %> 
											<ls:select name="subBe" value="<%=subBe %>" readOnly="true">
												<ls:options property="rentTypeList" scope="request" text="name" value="value" groupName="items"/>
											</ls:select>
										<%
                      } else {
                    %>
                      <ls:select name="subBe" allowParentCheck="true">
							<ls:options property="rentTypeList" scope="request" text="name" value="value" groupName="items"/>
                      </ls:select>   
                    <%
                      }
                    %>
								</td>
								<td>
                    <ls:label text="是否有效" />
                </td>
                <td>
                    <%
                      if (subBe != null && !subBe.equals("")) { //flag用于选择标记
                    %> 
                      <ls:select name="validFlag" value="1" readOnly="true">
                          <ls:options property="validFlagList" scope="request" text="codeName" value="codeValue" />
                      </ls:select>
                    <%
                      } else {
                    %>
                      <ls:select name="validFlag">
                          <ls:options property="validFlagList" scope="request" text="codeName" value="codeValue" />
                      </ls:select>   
                    <%
                      }
                    %>
                </td>
								<td colspan="2">
										<div class="pull-right">
												<ls:button text="清空" onclick="doClear" />
												<ls:button text="查询" onclick="doSearch" />
										</div>
								</td>
						</tr>
				</table>
		</ls:form>
		<ls:title text="资费模板列表"></ls:title>
		<table align="center" class="tab_search">
				<tr>
						<td>
								<ls:grid height="350px" width="100%" url="" name="tariffGrid" primaryKey="planNo" showCheckBox="true" singleSelect="false">
										<ls:gridToolbar name="code_grid_bar">
										    <%
                          if (flag != null && !flag.equals("")) { //flag用于选择标记
                        %>                            
                        <ls:gridToolbarItem name="doSure" text="确认" imageKey="set" onclick="doSure"></ls:gridToolbarItem>
                        <ls:gridToolbarItem name="closeWin" text="返回" imageKey="back" onclick="closeWin"></ls:gridToolbarItem>
                        <%
                          } else {
                        %>
												<ls:gridToolbarItem name="addTariffBtn" text="新增" imageKey="add" onclick="addTariff" />
												<ls:gridToolbarItem name="editTariffBtn" text="修改" imageKey="edit" onclick="editTariff" />
												<ls:gridToolbarItem name="deleteTariffBtn" text="删除" imageKey="delete" onclick="deleteTariff" />
												<ls:gridToolbarItem name="showTariffBtn" text="详情" imageKey="look" onclick="showTariff" />
												<ls:gridToolbarItem name="startTariffBtn" text="启用" imageKey="start" onclick="startTariff" />
												<ls:gridToolbarItem name="stopTariffBtn" text="停用" imageKey="stop" onclick="stopTariff" />
												<ls:gridToolbarItem name="copyTariffBtn" text="复制" imageKey="copy" onclick="copyTariff" />
										    <%-- <ls:gridToolbarItem name="addCarConfig" text="车型定价" imageKey="add" onclick="addCarConfig" /> --%>
										    <%
                          }
                        %>
										</ls:gridToolbar>
										<ls:column caption="管理单位" name="orgCodeName" />
										<ls:column caption="模版编号" name="planNo" />
										<ls:column caption="模版名称" name="planName" />
										<ls:column caption="租赁方式" name="subBeName" />
										<ls:column caption="计费方式" name="chargeWayName" />
										<ls:column caption="计费模式" name="chargeModeName" />
										<ls:column caption="发布时间" name="createTime" />
										<ls:column caption="状态" name="validFlagName" />
										<%-- <ls:column caption="资费说明" name="planRemark" align="left"/> --%>
										<ls:pager pageSize="15,20"/>
								</ls:grid>
						</td>
				</tr> 
		</table>
		<ls:script>
		var baseUrl = '~/billing/tariffplans/getTariffPlanList';
		var operFlag = '<c:out value="${requestScope.flag}" />';
		window.doSearch = doSearch;
		doSearch();

		function doSearch(){
			var params = tariffPlanQueryForm.getFormData();
			tariffGrid.query(baseUrl,params);
		}
		
	  function doClear(){
	    tariffPlanQueryForm.clear();
	    orgCode.setValue();
	    orgCodeName.setValue();
	  }
    tariffGrid.itemclick = function(){
        if(!LS.isEmpty(operFlag)){
          return;
        }
        var item = tariffGrid.getSelectedItem();
        if(!item){
          return;
        }
        var flag = item.validFlag;
        if(flag=="0"){//无效
          tariffGrid.toolbarItems.editTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.deleteTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.stopTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.startTariffBtn.setEnabled(true);
        } else if(flag=="1"){//有效
          tariffGrid.toolbarItems.editTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.deleteTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.stopTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.startTariffBtn.setEnabled(false);
        } else if(flag=="2"){//草稿
          tariffGrid.toolbarItems.editTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.deleteTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.stopTariffBtn.setEnabled(false);
          tariffGrid.toolbarItems.startTariffBtn.setEnabled(true);
        } else {
          tariffGrid.toolbarItems.editTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.deleteTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.stopTariffBtn.setEnabled(true);
          tariffGrid.toolbarItems.startTariffBtn.setEnabled(true);
        }
      }
    function addTariff(){
       LS.dialog("~/billing/tariffplans/initAdd","新增资费模版",1000,600,true);
       //window.location.href=rootUrl+"billing/tariffplans/initAdd";
    }
    
    function editTariff(){
      var item = tariffGrid.getCheckedItems();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          if(item[0].validFlag=="1"){
	          LS.message('info','已经启用的资费模版不能修改，请重新选择');
	          return;
	        }
          LS.dialog("~/billing/tariffplans/initEdit/"+item[0].planNo,"修改资费模版",1000,600,true);
          //window.location.href=rootUrl+"billing/tariffplans/initEdit/"+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function deleteTariff(){
      var items = tariffGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var planNos = [];
	    for(var i=0;i < items.length;i++){
	      if(items[i].validFlag=="1"){
	        LS.message('info','选中的记录包含已经启用的资费模版，请重新选择');
	        return;
	      }else{
	        planNos.push(items[i].planNo);
	      }
	    }
      LS.confirm('确认删除吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = planNos;
              LS.ajax("~/billing/tariffplans/delete", params, function(result) {
              	LS.message("info","删除成功");
                doSearch();
              });
          }
      });
    }
    
    function showTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.dialog("~/billing/tariffplans/showTariff/"+item[0],"查看资费模版详细信息",1000,600,true);
          //window.location.href=rootUrl+"billing/tariffplans/showTariff/"+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    
    function startTariff(){
      var items = tariffGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var planNos = [];
      for(var i=0;i < items.length;i++){
        if(items[i].validFlag=="1"){
          LS.message('info','选中的记录包含已经启用的资费模版，请重新选择');
          return;
        }else{
          planNos.push(items[i].planNo);
        }
      }
      LS.confirm('确认启用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = planNos;
              LS.ajax("~/billing/tariffplans/startTariff", params, function(result) {
                LS.message("info","启用成功");
                doSearch();
              });
          }
      });
    }
    
    function stopTariff(){
      var items = tariffGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var planNos = [];
      for(var i=0;i < items.length;i++){
        if(items[i].validFlag!="1"){
          LS.message('info','只有已经启用的模版才能停用，请重新选择');
          return;
        }else{
          planNos.push(items[i].planNo);
        }
      }
      LS.confirm('确认停用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = planNos;
              LS.ajax("~/billing/tariffplans/stopTariff", params, function(result) {
                LS.message("info","停用成功");
                doSearch();
              });
          }
      });
    }
    
    function copyTariff(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          LS.ajax("~/billing/tariffplans/copyTariff", {planNo:item[0]}, function(result) {
            LS.message("info","复制成功");
            doSearch();
          });
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    function doSure(){      
	    var items = tariffGrid.getCheckedItems();
	    if(items.length==0){
	      LS.message("info","请选择一条记录!");
	      return;
	    }else if(items.length > 1){
	      LS.message("info","只能选择一条记录!");
	      return;
	    }else{
	      LS.parent().setPlan(items[0]);
	      LS.window.close();
	    }
	  }    
	  
	  function closeWin(){
	    LS.window.close();
	  }
    function getOrg(){
      var initValue=null;
      if(!LS.isEmpty(orgCode.getValue())){
        initValue=orgCode.getValue().split(",");
      }
      js_util.selectOrgTree(true, null, true, initValue, false, setOrg);
    }
    function setOrg(node){
      if(node==null){
        return;
      }
      var orgCodes="";
      var orgCodeNames="";
      if(node.length==undefined){
        orgCodes=node.id;
        orgCodeNames=node.text;
      }else{
        for(var i=0;i< node.length;i++){
          if(node.length==i+1){
            orgCodes+=node[i].id;
            orgCodeNames+=node[i].text;
          }else{
            orgCodes+=node[i].id+',';
            orgCodeNames+=node[i].text+',';
          }
        }
      }
      orgCode.setValue(orgCodes);
      orgCodeName.setValue(orgCodeNames);
    }
    function changeOrgName(){
      if(LS.isEmpty(orgCodeName.getValue())){
         orgCode.setValue();
      }
    }
    function addCarConfig(){
      var item = tariffGrid.getCheckedIDs();
      if(item.length>0){
        if(item.length>1){
          LS.message("info","只能选择一条记录");
          return;
        }else{
          window.location.href=rootUrl+"billing/configs/new/"+item[0];
        }
      }else{
        LS.message("info","请选择一条记录");
        return;
      }
    }
    </ls:script>
</ls:body>
</html>