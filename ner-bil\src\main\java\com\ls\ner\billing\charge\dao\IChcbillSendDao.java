package com.ls.ner.billing.charge.dao;

import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @dateTime 2016-04-08
 * @description 充电计费配置下发信息 E_CHCBILL_SEND
 */
public interface IChcbillSendDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询充电计费配置下发信息  E_CHCBILL_SEND
	 */
	public List<ChcbillSendBo> queryChcbillSends(ChcbillSendQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description  查询充电计费配置下发信息条数  E_CHCBILL_SEND
	 */
	public int queryChcbillSendsNum(ChcbillSendQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description  查询所有下发成功的充电桩
	 */
	public List<Map> queryPilesByMap(Map map);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 新增充电计费配置下发信息 E_CHCBILL_SEND  
	 */
	public void insertChcbillSend(ChcbillSendBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 更新充电计费配置下发信息 E_CHCBILL_SEND 
	 */
	public void updateChcbillSend(ChcbillSendBo bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 删除充电计费配置下发信息 E_CHCBILL_SEND 
	 */
	public void deleteChcbillSend(String chcNo);

	/**
	 * @Description 通过站点id和计费编号查询计费
	 * <AUTHOR>
	 * @Date 2023/7/27 15:40
	 * @param: stationId
	 * @param: chcNo
	 */
	ChcbillSendBo selectByStationIdAndChcNo(@Param("stationId") String stationId, @Param("chcNo")String chcNo);
}
