package com.ls.ner.billing.market.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.billing.api.vip.bo.VipRPCBo;
import com.ls.ner.billing.gift.constant.IntegralEnum;
import com.ls.ner.billing.market.bo.*;
import com.ls.ner.billing.market.dao.ICouponPutDao;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.market.vo.ExclusiveOfferVo;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.billing.vip.bo.VipExclusivePutBo;
import com.ls.ner.billing.vip.bo.VipPayRecordBo;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.dao.IVipExclusivePutDao;
import com.ls.ner.billing.vip.service.IVipLevelService;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.market.dao.IIntegeralCustRPCDao;
import com.ls.ner.billing.market.dao.IIntegeralRuleDao;
import com.ls.ner.billing.market.service.IIntegeralCustService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.param.api.ISysParamService;

@Service(target = {ServiceType.LOCAL}, value = "IntegeralCustService")
public class IntegeralCustServiceImpl implements IIntegeralCustService {

    private static final Logger log = LoggerFactory.getLogger(IntegeralCustServiceImpl.class);

    @Autowired(required = true)
    private IIntegeralRuleDao ruleDao;

    @Autowired(required = true)
    private IIntegeralCustRPCDao dao;

    @Autowired(required = true)
    private IMemberBenefitsDao memberBenefitsDao;

    @Autowired(required = true)
    private IVipExclusivePutDao exclusivePutDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "accuCouponRpcService")
    private ICouponRpcService accuCouponRpcService; // 支付中心-优惠券接口

    @Autowired(required = true)
    private ICouponPutDao putDao; // 发放主表

    @ServiceAutowired(value = "codeService", serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "seqRpcService")
    private ISeqRpcService seqRpcService;

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
    private IDefrayAccountRpcService defrayAccountRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "vipLevelService")
    private IVipLevelService vipLevelService;

    private RedisCluster redisCluster = RedisCluster.getInstance();

    private static final String INTEGRAL_CLEAR_TIME_KEY = "integral:clear:time";

    @Override
    public List<IntegeralCustLogBo> getIntegeralCustLogList(IntegeralCustLogBo logBo) {

        return dao.getIntegeralCustLogList(logBo);
    }

    @Override
    public int getIntegeralCustLogCount(IntegeralCustLogBo logBo) {

        return dao.getIntegeralCustLogCount(logBo);
    }

    @Override
    public String getCustVipType(String custId) {
        VipPayRecordBo bo = memberBenefitsDao.getCustVipType(custId);
        if (bo != null) {
            return bo.getVipType();
        } else {
            return "";
        }
    }

    @Override
    public void excuteIntegeralCustTask() {
        //用户积分清零
        List<String> integralNoList = new ArrayList<>();
        String clearTime = (String) redisCluster.get(INTEGRAL_CLEAR_TIME_KEY);
        if (StringUtils.isBlank(clearTime)) {
            log.info("未设置积分清零时间，跳过积分清零任务");
            return;
        }

        //判断是否到积分清零时间
        String nowDate = DateTools.dateToStr(new Date(), "yyyy-MM-dd HH:mm:ss");
        int compare = DateTools.compareDate(clearTime, nowDate);
        if (compare <= 0) {
            log.info("到达积分清零时间：{}，开始执行积分清零任务", clearTime);

            //获取所有用户积分
            List<IntegeralCustBo> list = dao.getIntegeralCustList();
            if (CollectionUtils.isNotEmpty(list)) {
                for (IntegeralCustBo integeralCustBo : list) {
                    integralNoList.add(integeralCustBo.getCustNo());
                    IntegeralCustLogBo logBo = new IntegeralCustLogBo();
                    logBo.setIntegralNo(seqRpcService.getAppNo());
                    logBo.setPhone(integeralCustBo.getCustPhone());
                    logBo.setEventName(IntegralEnum.POINTS_EXPIRATION.getCode());
                    logBo.setEventType(IntegralEnum.POINTS_EXPIRATION.getName());
                    logBo.setChargeType("02");
                    logBo.setChargeNum(integeralCustBo.getIntegralNumber());
                    logBo.setIntegralNumber(integeralCustBo.getIntegralNumber());
                    dao.addIntegeralCustLog(logBo);
                }
            }
            //积分清零
            Map<String, Object> iMap = new HashMap<String, Object>();
            iMap.put("integralNoList", integralNoList);
            iMap.put("integralNumber", "0");
            dao.updateIntegeralCustByTask(iMap);

            log.info("积分清零任务执行完成，清零用户数：{}", integralNoList.size());
        } else {
            log.info("未到积分清零时间：{}，当前时间：{}", clearTime, nowDate);
        }
    }

    /**
     * 会员专享优惠 每月发券
     *
     * @return
     */
    @Override
    public void excuteExclusiveOfferTask() {
        List<String> typeList = new ArrayList<>();
        typeList.add("5");
        typeList.add("6");

        for (String vipType : typeList) {
            //获取专享优惠券
            MemberBenefitsBo req = new MemberBenefitsBo();
            req.setVipType("02");
            req.setVipLevel(vipType);
            MemberBenefitsBo benefitsBo = memberBenefitsDao.getMemberBenefits(req);
            if (benefitsBo==null||benefitsBo.getIsExclusiveOffer().equals("0")||StringUtil.isEmpty(benefitsBo.getExclusiveOffer())||benefitsBo.getExclusiveOffer().equals("[]")){
                log.error("未获取到会员专享优惠权益");
                return;
            }
            //查询专享优惠的用户
            String putDay = DateTools.getStringDateShort("yyyy-MM-dd");
            List<VipExclusivePutBo> bos = exclusivePutDao.getExclusiveByPutDay(putDay);
            if (CollectionUtils.isEmpty(bos)){
                log.error("未获取到发放日期在:{}的用户");
                return;
            }
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            for (VipExclusivePutBo bo : bos) {
                VipRPCBo rpcBo = new VipRPCBo();
                rpcBo.setMobile(bo.getMobile());
                rpcBo.setCustId(bo.getCustId());
                vipLevelService.exclusiveOffer(rpcBo);
                bo.setPutTimes(bo.getPutTimes()-1);
                LocalDateTime futureDate = expireTimeAdd(1, LocalDateTime.now());
                bo.setPutDay(futureDate.format(dateFormatter));
                exclusivePutDao.update(bo);
            }
        }
    }

    private static LocalDateTime expireTimeAdd(int monthsToAdd, LocalDateTime dateTime){

        // 获取下几个月后的同一天
        LocalDateTime futureDateTime;

        // 检查是否是该月的最后一天
        if (dateTime.toLocalDate().getDayOfMonth() == dateTime.toLocalDate().lengthOfMonth()) {
            // 如果是最后一天，则调整为新月份的最后一天
            futureDateTime = dateTime.with(TemporalAdjusters.lastDayOfMonth())
                    .plusMonths(monthsToAdd);
        } else {
            // 尝试直接增加指定的月份数
            try {
                futureDateTime = dateTime.plusMonths(monthsToAdd);
            } catch (DateTimeException e) {
                // 如果该日期在目标月份不存在（如2月31日），则调整为新月份的最后一天
                futureDateTime = dateTime.with(TemporalAdjusters.lastDayOfMonth())
                        .plusMonths(monthsToAdd);
            }
        }
        return futureDateTime;
    }

}
