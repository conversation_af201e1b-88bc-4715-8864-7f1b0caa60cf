/**
 *
 * @(#) RentBillRestController.java
 * @Package com.ls.ner.billing.rest.rent.controller
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.rest.rent.controller;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.base.constants.OrderConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.rent.model.MainChargeItem;
import com.ls.ner.billing.api.rent.model.PriceUnit;
import com.ls.ner.billing.api.rent.model.RentRun.DateItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem;
import com.ls.ner.billing.api.rent.model.SubChargeItem.Limit;
import com.ls.ner.billing.api.rent.service.IRentBillRpcService;
import com.ls.ner.billing.rent.condition.RentBillRestCondition;
import com.ls.ner.billing.rent.service.impl.RentBillingCalculator;
import com.ls.ner.util.AssertUtil;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.http.HttpClientUtil;
import com.ls.ner.util.json.JsonMapper;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 *  Description : 租车定价REST接口
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月27日 下午5:42:44   qixiyu   Created.
 *           
 */
@Controller
@RequestMapping("/api")
public class RentBillRestController {
	private static final Logger logger = LoggerFactory.getLogger(RentBillRestController.class);
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="rentBillRpcService")
	private IRentBillRpcService rentBillRpcService;
	
	@Autowired
	private RentBillingCalculator billingCalculator;
	/**
	 * 
	 * Method description : 查询租车定价
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月27日 下午5:45:57
	 * History:  2016年10月27日 下午5:45:57   qixiyu   Created.
	 *
	 * @param compId
	 * @param channel
	 * @param pickStationId
	 * @return
	 *
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "/v0.1/rent-pricing", method = RequestMethod.GET)  
	public @ResponseBody Object getRentPrice(RentBillRestCondition condition) {
		logger.debug("查询租车定价入参rent-pricing："+JsonMapper.nonEmptyMapper().toJson(condition));
		Map<String,Object> returnMap = new HashMap<String,Object>();
		try {
			AssertUtil.notEmptyForString(condition.getProdBusiType(), "产品业务类不为空");
			AssertUtil.equalsForString(condition.getProdBusiType(), "01", "产品业务类参数值错误");
			AssertUtil.notEmptyForString(condition.getProdType(), "产品类别不为空");
			AssertUtil.equalsForString(condition.getProdType(), "0", "产品类别参数值错误");
			AssertUtil.notEmptyForString(condition.getCity(), "城市不为空");
			AssertUtil.notEmptyForString(condition.getStationId(), "站点ID不为空");
			AssertUtil.notEmptyForString(condition.getModelId(), "型号ID不为空");
			Map<String,Object> inMap = BeanUtils.describe(condition);
			List<Map<String,Object>> modelList = Lists.newArrayList();
			Map<String,Object> modelMap = Maps.newHashMap();
			modelMap.put("modelId", condition.getModelId());
			modelList.add(modelMap);
			inMap.put("modelList", modelList);
			returnMap = rentBillRpcService.getRentPrice(inMap);
			returnMap.put("ret", 200);
			logger.debug("查询租车定价出参rent-pricing："+returnMap);
			return new ResponseEntity<Map>((Map) StringUtil.notNull(returnMap), HttpStatus.OK);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			returnMap.put("ret", 400);
			returnMap.put("msg", e.getMessage());
			return new ResponseEntity<Map>(returnMap, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	/**
	 * 
	 * Method description : 查询物流定价
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年10月30日 下午2:18:06
	 * History:  2016年10月30日 下午2:18:06   qixiyu   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "/v0.1/log-pricing", method = RequestMethod.GET)  
	public @ResponseBody Object getLogPrice(RentBillRestCondition condition) {
		logger.debug("查询物流定价入参log-pricing："+JsonMapper.nonEmptyMapper().toJson(condition));
		Map<String,Object> returnMap = new HashMap<String,Object>();
		try {
			AssertUtil.notEmptyForString(condition.getProdBusiType(), "产品业务类不为空");
			AssertUtil.equalsForString(condition.getProdBusiType(), "04", "产品业务类参数值错误");
			AssertUtil.notEmptyForString(condition.getProdType(), "产品类别不为空");
			AssertUtil.equalsForString(condition.getProdType(), "0", "产品类别参数值错误");
			AssertUtil.notEmptyForString(condition.getCity(), "城市不为空");
			AssertUtil.notEmptyForString(condition.getStationId(), "站点ID不为空");
			AssertUtil.notEmptyForString(condition.getModelId(), "型号ID不为空");
			Map<String,Object> inMap = BeanUtils.describe(condition);
			List<Map<String,Object>> modelList = Lists.newArrayList();
			Map<String,Object> modelMap = Maps.newHashMap();
			modelMap.put("modelId", condition.getModelId());
			modelList.add(modelMap);
			inMap.put("modelList", modelList);
			returnMap = rentBillRpcService.getDeliverPrice(inMap);
			returnMap.put("ret", 200);
			logger.debug("查询物流定价出参log-pricing："+returnMap);
			return new ResponseEntity<Map>((Map) StringUtil.notNull(returnMap), HttpStatus.OK);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			returnMap.put("ret", 400);
			returnMap.put("msg", e.getMessage());
			return new ResponseEntity<Map>(returnMap, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	/**
	 * 
	 * Method description : 查询物流车辆预收费用
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月2日 下午2:31:08
	 * History:  2016年11月2日 下午2:31:08   qixiyu   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "/v0.1/log-pricing-estimate", method = RequestMethod.POST)
	public @ResponseBody Object getLogPriceEst(RentBillRestCondition condition) {
		logger.debug("查询物流车辆预收费用入参log-pricing-estimate："+JsonMapper.nonEmptyMapper().toJson(condition));
		Map<String,Object> returnMap = new HashMap<String,Object>();
		try {
//			AssertUtil.notEmptyForString(condition.getRentMileage(), "租赁里程不为空");
			AssertUtil.notEmptyForString(condition.getModelId(), "型号ID不为空");
			Map<String,Object> inMap = BeanUtils.describe(condition);
			List<Map<String,Object>> modelList = Lists.newArrayList();
			Map<String,Object> modelMap = Maps.newHashMap();
			modelMap.put("modelId", condition.getModelId());
			modelList.add(modelMap);
			inMap.put("modelList", modelList);
			inMap.put("isNeedItem", "true");//临时取mainchargeitem
			Map<String,Object> ret = rentBillRpcService.getDeliverPrice(inMap);
			List<Map<String,Object>> prodModeList = (List<Map<String,Object>>)ret.get("prodModeList");
			Map<String,Object> map = prodModeList.get(0);
			prodModeList = (List<Map<String,Object>>)map.get("modelList");
			prodModeList = (List<Map<String,Object>>)prodModeList.get(0).get("prodList");
			BigDecimal billAmt = new BigDecimal("0.00");
			long planMile = 0;
			if(StringUtils.nullOrBlank(condition.getLineList())){
				planMile = new BigDecimal(condition.getRentMileage()).multiply(new BigDecimal(1000)).longValue();
			} else {
				planMile = caclRoute(condition.getLineList()).longValue();
			}
			logger.debug("物流里程数："+planMile);
			if(prodModeList!=null&&prodModeList.size()>0){
				for (Map<String, Object> tMap : prodModeList) {
					if(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO.equals(tMap.get("prodId"))){
						MainChargeItem mainChargeItem = (MainChargeItem)tMap.get("mainChargeItem");
						DateItem dateItem = new DateItem();
						dateItem.setMeters(planMile);
						billAmt = billingCalculator.calculate(mainChargeItem, dateItem );
//						List<Map<String,Object>> tempList = (List<Map<String,Object>>)tMap.get("pricingCombine");
//						SubChargeItem chargeItem = (SubChargeItem)tempList.get(0).get("chargeItem");
//						int priceUnitValue = chargeItem.getPriceUnit().getValue();
//						if (PriceUnit.KM.equals(chargeItem.getPriceUnit().getUnit())) {
//							priceUnitValue = priceUnitValue * 1000;
//						}
//						long quantity = transQuantity(planMile, priceUnitValue);
//						Limit min = chargeItem.getMin();
//						Limit max = chargeItem.getMax();
//						long limitedQuantity = billingCalculator.limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), quantity);
//						billAmt = getResult(chargeItem.getPrice(), limitedQuantity);
//						billAmt = billingCalculator.limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max),billAmt);
						break;
					}
				}
			}
			returnMap.put("ret", 200);
			returnMap.put("billAmt", billAmt);
			logger.debug("查询物流车辆预收费用入参log-pricing-estimate："+returnMap);
			return new ResponseEntity<Map>((Map) StringUtil.notNull(returnMap), HttpStatus.OK);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			returnMap.put("ret", 400);
			returnMap.put("msg", e.getMessage());
			return new ResponseEntity<Map>(returnMap, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	private long transQuantity(long amount, int priceUnitValue) {
		long a = amount / priceUnitValue;
		long b = amount % priceUnitValue;
		long quantity = a;
		if (b != 0) {
			quantity += 1;
		}
		return quantity;
	}
	
	private BigDecimal getResult(String price, long quantity) {
		return scale(new BigDecimal(price).multiply(new BigDecimal(quantity)));
	}
	
	private BigDecimal scale(BigDecimal bigDecimal) {
		return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	private String bigDecimal2String(BigDecimal bigDecimal) {
		return scale(bigDecimal).toPlainString();
	}
	
	@SuppressWarnings("unchecked")
	private BigDecimal caclRoute(String lineList) {
		List<Map<String,Object>> list = JsonUtil.json2Obj(lineList, List.class);
		// 对线路进行排序
		Collections.sort(list, new Comparator<Map<String,Object>>() {
			@Override
			public int compare(Map<String, Object> o1, Map<String, Object> o2) {
				BigDecimal a1 = new BigDecimal(String.valueOf(o1.get("sort")));
				BigDecimal b1 = new BigDecimal(String.valueOf(o2.get("sort")));
				if (a1.compareTo(b1) > 0)
					return 1;
				else if (a1.compareTo(b1) < 0)
					return -1;
				else
					return 0;
			}
		});
		BigDecimal planMile = new BigDecimal(0);
		for (int i = 0; i < list.size() - 1; i++) {
			String url = "&origins=" + list.get(i).get("lat") + ","
					+ list.get(i).get("lon") + "&destinations="
					+ list.get(i + 1).get("lat") + ","
					+ list.get(i + 1).get("lon") + "&ak="
					+ OrderConstants.BaiDuPath.ROUTE_MATRIX_AK;
			url = OrderConstants.BaiDuPath.ROUTE_MATRIX_URL + url;
			HttpClientUtil clientUtil = new HttpClientUtil();
			//logger.debug("url=={}" + url);
			String result = clientUtil.getResponseFromHttp(url);
		//	logger.debug("计算线路距离" + result + "dd");
			if (!StringUtils.nullOrBlank(result)) {
				JSONObject retObject = JSONObject.fromObject(result);
				logger.debug("计算线路距离{}", JsonUtil.obj2Json(retObject));
				if (OrderConstants.BaiDuPath.ROUTE_MATRIX_SUCCESS
						.equals(retObject.get("status").toString())) {
					JSONArray jsonArray1 = JSONArray.fromObject(retObject
							.get("result"));
					List<Map<String, Object>> mapList = (List<Map<String, Object>>) JSONArray
							.toCollection(jsonArray1, Map.class);
					if (mapList != null && mapList.size() > 0) {
						for (Map<String, Object> map : mapList) {
							JSONObject obj = JSONObject.fromObject(map
									.get("distance"));
							obj.get("value").toString();
							planMile = planMile.add(new BigDecimal(obj.get(
									"value").toString()));
						}

					}
				}
			}

		}
		return planMile;
	}
}
