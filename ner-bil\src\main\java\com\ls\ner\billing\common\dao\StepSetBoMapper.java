package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.StepSetBo;
import com.ls.ner.billing.common.bo.StepSetBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface StepSetBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(StepSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(StepSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(StepSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(StepSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<StepSetBo> selectByExample(StepSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    StepSetBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") StepSetBo record, @Param("example") StepSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") StepSetBo record, @Param("example") StepSetBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(StepSetBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_set
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(StepSetBo record);
}