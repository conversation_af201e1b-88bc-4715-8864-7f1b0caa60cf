<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="活动查询" />
<script type="text/javascript" src="../comm/validate/validation.js"></script>
<script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
<ls:body>
	<ls:form id="searchForm" name="searchForm">
		<ls:title text="查询条件"></ls:title>
		<table class="tab_search">
			<tr>
				<td><ls:label text="活动名称" /></td>
				<td><ls:text name="actName" /></td>
				<td><ls:label text="活动类型" /></td>
				<td>
					<ls:select name="actType" property="actType" >
						<ls:options property="actTypeList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td><ls:label text="活动状态" /></td>
				<td>
					<ls:select name="actState" property="actState" >
						<ls:options property="actStateList" scope="request" text="codeName" value="codeValue" />
					</ls:select>
				</td>
				<td></td>
			</tr>
			<tr>
				<td><ls:label text="活动时间" /></td>
				<td>
					<ls:date name="effTime" format="yyyy-MM-dd HH:mm" />
				</td>
				<td><ls:label text="至" /></td>
				<td>
					<ls:date name="expTime" format="yyyy-MM-dd HH:mm" />
				</td>
				<td colspan="2">
					<div class="pull-right">
						<ls:button text="查询" onclick="doSearch" />
						<ls:button text="清空" onclick="doClear" />
					</div>
				</td>
				<td></td>
			</tr>
		</table>

	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="mktactGrid" height="330px;" width="100%"
					showCheckBox="false" singleSelect="true" caption="活动列表" primaryKey="actId">
					<ls:column caption="" name="actId" hidden="true"/>
					<ls:column caption="活动名称" name="actName" formatFunc="operation" />
					<ls:column caption="活动类型" name="actTypeName" />
					<ls:column caption="活动标签" name="actLbl" />
					<ls:column caption="活动状态" name="actStateName" />
					<ls:column caption="生效时间" name="effTime" />
					<ls:column caption="失效时间" name="expTime" />
					<ls:column caption="参与次数" name="totalTimes" />
                    <ls:column caption="参与人数" name="totalNums" />
                    <ls:column caption="参与情况" name="detailRemark" />
					<ls:column caption="创建单位" name="orgCodeName" />
					<ls:column caption="创建时间" name="creTime" />
					<ls:pager pageSize="15" ></ls:pager>
				</ls:grid></td>
		</tr>
	</table>
	</ls:form>
	<ls:script>
	window.getMarketDetail=getMarketDetail;
	function operation(rowdata){
	  const safeActName = escapeHtml(rowdata.actName);
      return "<a style='text-decoration: underline;' href='javascript:void(0);' onclick='getMarketDetail(\"" + rowdata.actId +"\")'>"+safeActName+"</a>";
    }
		function escapeHtml(unsafe) {
			if (typeof unsafe !== 'string') {
				return unsafe;
			}
			const map = {
				'&': '&amp;',
				'<': '&lt;',
				'>': '&gt;',
				'"': '&quot;',
				"'": '&#039;'
			};
			const reg = /[&<>"']/g;
			return unsafe.replace(reg, match => map[match]);
		}
    function getMarketDetail(actId){
      LS.dialog("~/marketqry/"+actId, "活动详情", 800, 450, true, null);
    }

	window.doSearch=doSearch;
<%--    doSearch();--%>
	function doSearch(){
		var eff = effTime.getValue();
		var exp = expTime.getValue();
		if (!LS.isEmpty(eff) && !LS.isEmpty(exp) && eff>exp) {
			LS.message("info", "开始时间不能大于截止时间。");
      		return;
		}
		var params = searchForm.getFormData();
		mktactGrid.query('~/marketqry', params, function(){});
	}
	function doClear(){
		searchForm.clear();
	}

		window.onload = function() {
		initGridHeight('searchForm','mktactGrid');
		}
    </ls:script>
</ls:body>
</html>
