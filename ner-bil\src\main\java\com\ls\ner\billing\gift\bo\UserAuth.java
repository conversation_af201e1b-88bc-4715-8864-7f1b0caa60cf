package com.ls.ner.billing.gift.bo;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.billing.gift.service.impl.UserOAuthServiceImpl;
import com.ls.ner.billing.gift.util.BusinessException;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.io.Serializable;

@JsonSerialize
public class UserAuth implements Serializable {

    public static final long serialVersionUID = 2032878612063832118L;

    private String getAccessTokenUrl = null;

    private String getUserInfoUrl = null;

    private String clientId = null;

    private String secretKey = null;

    private String redirectUri = null;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public UserAuth(String clientId, String secretKey, String getAccessTokenUrl, String getUserInfoUrl, String redirectUri) {
        this.getAccessTokenUrl = getAccessTokenUrl;
        this.getUserInfoUrl = getUserInfoUrl;
        this.clientId = clientId;
        this.secretKey = secretKey;
        this.redirectUri = redirectUri;
    }

    public String getAccessToken(String code, String authorizationCode) {
        long startTime = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        GetAccessTokenRequest input = new GetAccessTokenRequest();
        input.setCode(code);
        input.setClientId(clientId);
        input.setGrantType(authorizationCode);
        input.setRedirectUri(redirectUri);
        String result = "{}";
        try {
            AccessTokenResult tokenInfo = UserOAuthServiceImpl.getAccessToken(input, secretKey, getAccessTokenUrl);
            return JSONObject.toJSONString(tokenInfo);
        } catch (BusinessException e) {
            jsonObject.put("code", e.getCode());
            jsonObject.put("message", e.getMessage());
            String error = jsonObject.toJSONString();
            return error;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;

    }
    public String getUserInfo(String accessToken) {
        long startTime = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        int accessTokenLength = 36;
        if (accessToken == null) {
            jsonObject.put("code", com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_CODE);
            jsonObject.put("message", "accessToken error");
            String error = jsonObject.toJSONString();
            return error;
        }
        if (accessToken.length() > accessTokenLength) {
            try {
                AccessTokenResult tokenInfo = JSONObject.parseObject(accessToken, AccessTokenResult.class);
                accessToken = tokenInfo.getAccessToken();
            } catch (Exception e) {
                jsonObject.put("code", com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_CODE);
                jsonObject.put("message", "accessToken error");
                String error = jsonObject.toJSONString();
                return error;
            }
        } else if (accessToken.length() < accessTokenLength) {
            jsonObject.put("code", com.ls.ner.billing.gift.constant.CoreConstants.REQUEST_ERROR_CODE);
            jsonObject.put("message", "accessToken error");
            String error = jsonObject.toJSONString();
            return error;
        }
        String result = "{}";
        try {
            CertUserInfoResult userInfo = UserOAuthServiceImpl.getUserInfo(accessToken, getUserInfoUrl);
            long endTime = System.currentTimeMillis();
            return JSONObject.toJSONString(userInfo);
        } catch (BusinessException e) {
            jsonObject.put("code", e.getCode());
            jsonObject.put("message", e.getMessage());
            String error = jsonObject.toJSONString();
            return error;
        }
    }
    public static void main(String[] args) {

        String getAccessTokenUrl = "https://uias.evstyle.cn/auth/token";
        String getUserInfoUrl = "https://uias.evstyle.cn/open/info/getUserInfo";

        String clientId = "20190911274136";//appid
        String secretKey = "0dd4437d28943ca6a7dac29d1eca7ac5";//密钥
        String redirectUri1 = "http://iningde.cn";//三方的回调地址
        String code = "aff44c35-f573-31a3-b15d-56e0395acc5d";//访问签名地址后返回

        UserAuth auth_demo = new UserAuth(clientId, secretKey, getAccessTokenUrl, getUserInfoUrl, redirectUri1);
        String accessToken = auth_demo.getAccessToken(code, "authorizationCode");//6.2.2接口
        System.out.println("accessToken1 result:" + accessToken);

        AccessTokenResult accessToken1 = JSONObject.parseObject(accessToken, AccessTokenResult.class);
        accessToken = accessToken1.getAccessToken();
        String userInfo = auth_demo.getUserInfo(accessToken);//6.2.3接口
        System.out.println("userInfo1 result:" + userInfo);


    }
}
