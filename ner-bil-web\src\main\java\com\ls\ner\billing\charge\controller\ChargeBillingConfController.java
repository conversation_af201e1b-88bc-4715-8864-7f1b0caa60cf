package com.ls.ner.billing.charge.controller;

import com.ls.ner.util.StringUtil;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.constants.PublicConstants;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.util.FormatUtil;
import com.ls.ner.util.ListMapUtil;
import com.ls.ner.util.MergeUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * 充电计费配置
 * <AUTHOR>
 */
@Controller
@RequestMapping("/billing/chargebillingconf")
public class ChargeBillingConfController extends QueryController<ChargeBillingConfQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(ChargeBillingConfController.class);

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;
	
	@ServiceAutowired
	Authentication authentication;

	@Override
	protected ChargeBillingConfQueryCondition initCondition() {
		return new ChargeBillingConfQueryCondition();
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-25
	 * @description 初始化充电计费配置
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
		 AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null){
				condition.setOrgCode(orgBo.getOrgCode());
				condition.setOrgCodeName(orgBo.getOrgShortName());			
			}
		m.addAttribute("searchForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/chargeConf/chargeConfIndex";
	}
	
	/**
	 * <AUTHOR>
	 * @throws IllegalAccessException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchFieldException 
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置
	 */
	@RequestMapping(value = "/queryChargeBillingConfs")
	public @ItemResponseBody QueryResultObject queryChargeSerItems(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChargeBillingConfQueryCondition condition = this.rCondition2QCondition(params);
		if(StringUtils.nullOrBlank(condition.getOrgCode())){
			AccountBo currentAccount = authentication.getCurrentAccount();
			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
			if(orgBo != null)
				condition.setOrgCode(orgBo.getOrgCode());
		}
		condition.setOrgList(getSubOrg(condition.getOrgCode()));
		condition.setBillType("1");
		List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
		if(list!=null && list.size()>0){
			StringBuilder stationIdSb = new StringBuilder();
			for(ChargeBillingConfBo cbcBo:list){
				OrgBo org = orgService.getOrgByNo(cbcBo!=null?cbcBo.getOrgCode():"");
				if(org != null)
					cbcBo.setOrgCodeName(org.getOrgShortName());
				if(ChargeConstant.validFlag.DRAFT.equals(cbcBo.getChcStatus())){
					cbcBo.setChcStatusName("草稿");
				}else
					cbcBo.setChcStatusName("-");
				if(!StringUtils.nullOrBlank(cbcBo.getStationId())){
					stationIdSb.append(cbcBo.getStationId()).append(",");
				}
			}
			String stationIds = stationIdSb.toString();
			if(!StringUtils.nullOrBlank(stationIds)){
				stationIds = stationIds.substring(0, stationIds.length() - 1);
				List<Map<String,Object>> stationList = archivesRpcService.queryStations(stationIds);
				MergeUtil.mergeList(list, stationList, "stationId",  new String[]{"stationName"});
			}
		}
		int recordCount = chargeBillingConfService.queryChargeBillingConfsNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增/修改充电计费配置
	 */
	@RequestMapping(value = "/addChargeBillingConf", method = RequestMethod.GET)
	public String addChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			m.addAttribute("chargeForm", cbc);
		} else {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);
				if(!StringUtils.nullOrBlank(bo.getStationId())){
					Map<String, Object> searchMap = new HashMap<String, Object>();
					searchMap.put("stationId", bo.getStationId());
					Map<String,Object> stationMap = archivesRpcService.queryStation(searchMap);
					if(stationMap!=null && stationMap.get("stationName")!=null)
						bo.setStationName(String.valueOf(stationMap.get("stationName")));
				}
				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				m.addAttribute("chargeForm", bo);
			}else
				m.addAttribute("chargeForm", new ChargeBillingConfBo());
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/chargeConf/addChargeConf";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 查看充电计费配置
	 */
	@RequestMapping(value = "/showChargeBillingConf", method = RequestMethod.GET)
	public String showChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
		condition.setChcNo(chcNo);
		List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
		if(list != null && list.size() > 0){
			ChargeBillingConfBo bo =list.get(0);
			bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
			Map<String, Object> searchMap = new HashMap<String, Object>();
			searchMap.put("stationId", bo.getStationId());
			Map<String,Object> stationMap = archivesRpcService.queryStation(searchMap);
			if(stationMap!=null && stationMap.get("stationName")!=null)
				bo.setStationName(String.valueOf(stationMap.get("stationName")));
			m.addAttribute("chargeForm", bo);
		}else
			m.addAttribute("chargeForm", new ChargeBillingConfBo());
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> l= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", l);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		return "/bil/chargeConf/showChargeConf";
	}
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 保存充电计费配置
	 */
	@RequestMapping(value = "/saveChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveChargeBillingConf(@RequestBody Map<String, Object> dataParams) throws IllegalAccessException, InvocationTargetException{
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try{
			String systemId = bo.getSystemId();
			if (systemId != null && !"".equals(systemId)) {
				chargeBillingConfService.updateChargeBillingConf(bo);
			} else {
				chargeBillingConfService.insertChargeBillingConf(bo);
			}
			return WrappedResult.successWrapedResult(bo);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 删除充电计费配置
	 */
	@RequestMapping(value = "/delChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult delChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		chargeBillingConfService.delChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 发布充电计费配置
	 */
	@RequestMapping(value = "/releaseChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult releaseChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		try{
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			bo.setChcNo(chcNo);
			chargeBillingConfService.releaseChargeBillingConf(bo);
			return WrappedResult.successWrapedResult(true);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 复制充电计费配置
	 */
	@RequestMapping(value = "/copyChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult copyChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		bo = chargeBillingConfService.copyChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询分时时段设置
	 */
	@RequestMapping(value = "/queryChargePeriods")
	public @ResponseBody WrappedResult queryChargePeriods(@RequestBody ChargeBillingConfQueryCondition condition) {
//		condition.setPageEnd(0);
		List<ChargePeriodsBo> list =chargeBillingConfService.queryChargePeriods(condition);
		if(list!= null){
			for(ChargePeriodsBo cpBo:list){
				cpBo.setPrice(FormatUtil.formatNumber(cpBo.getPrice(),4));
//				if(!StringUtils.nullOrBlank(cpBo.getTimeFlag())){
//					CodeBO code = codeService.getStandardCode(BizConstants.ProductType.TIME_FLAG, cpBo.getTimeFlag(), null);
//					if(code!=null){
//						cpBo.setTimeFlag(code.getCodeName());
//					}
//				}
			}
		}
		return WrappedResult.successWrapedResult(list);
	}

	private List<String> getSubOrg(String orgCode) {
		List<String> orgList = new ArrayList<String>();
		if(StringUtil.isNotBlank(orgCode)) {
			String []orgArr = orgCode.split(",");
			for(String orgCodeTemp : orgArr) {
				List<OrgBo> orgBoList = orgService.getSubOrg(orgCodeTemp, null, null);
				if(!CollectionUtils.isEmpty(orgBoList)) {
					for(OrgBo bo : orgBoList) {
						orgList.add(bo.getOrgCode());
					}
				}
			}
		}else {
			return null;
		}
		return orgList;
	}

	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}
	
}
