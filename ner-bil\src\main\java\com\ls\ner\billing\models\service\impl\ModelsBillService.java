/**
 *
 * @(#) ModelsBillService.java
 * @Package com.ls.ner.billing.models.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.models.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.models.bo.ModelsBillBo;
import com.ls.ner.billing.models.service.IModelsBillService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

/**
 *  Description : 
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月28日 下午4:36:45   qixiyu   Created.
 *           
 */
@Service(target = { ServiceType.APPLICATION }, value = "modelsBillService")
public class ModelsBillService implements IModelsBillService {
	@Autowired
	ModelsBillServiceImpl modelsBillServiceImpl;
	
	@Override
	public List<ModelsBillBo> getModelRentalList(ModelsBillBo condition) {
		return modelsBillServiceImpl.getModelRentalList(condition);
	}

	@Override
	public int getModelRentalListNum(ModelsBillBo condition) {
		return modelsBillServiceImpl.getModelRentalListNum(condition);
	}

	@Override
	public void saveModelRental(ModelsBillBo bo) {
		modelsBillServiceImpl.saveModelRental(bo);
	}

	@Override
	public void delModelRentals(String[] ids) {
		modelsBillServiceImpl.delModelRentals(ids);
	}

}
