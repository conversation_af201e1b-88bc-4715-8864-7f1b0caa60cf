package com.ls.ner.billing.mktact.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.dao.ICouponDao;
import com.ls.ner.billing.mktact.bo.*;
import com.ls.ner.billing.mktact.condition.MarketActFormCondition;
import com.ls.ner.billing.mktact.dao.*;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.ls.ner.def.api.account.dto.AccountCouponListDTO;
import com.ls.ner.def.api.account.dto.AccountGiftListDTO;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.operators.service.IDefrayOperRpcService;
import com.ls.ner.order.api.dto.CpnAmtDTO;
import com.ls.ner.order.api.service.IOrderRpcService;
import com.ls.ner.pub.api.area.bo.AreaCondition;
import com.ls.ner.pub.api.area.service.IAreaRpcService;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MergeUtil;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.JodaDateTime;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.org.api.IOrgService;
import org.springframework.web.multipart.MultipartFile;

/**
 *
 *<pre><b><font color="blue">MarketActServiceImpl</font></b></pre>
 *
 *<pre><b>&nbsp;--促销活动service实现类--</b></pre>
 * <pre></pre>
 * <pre>
 * <b>--样例--</b>
 * </pre>
 * JDK版本：JDK1.7
 * <AUTHOR>
 */
@Service(target = { ServiceType.APPLICATION }, value = "marketActService")
public class MarketActServiceImpl implements IMarketActService {
	private static final Logger logger = LoggerFactory.getLogger(MarketActServiceImpl.class);

	@Autowired(required=true)
	private IMarketActDao marketActDao;

	@Autowired(required=true)
	private ICouponDao couponDao;

	@Autowired
	private IPreSectionDao preSectionDao;

	@Autowired
	private IActCondDao actCondDao;

	@Autowired
	private IMarketActScopeDao marketActScopeDao;

	@Autowired
    private IActInfoDao actInfoDao;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defrayOperRpcService")
	private IDefrayOperRpcService defrayOperRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="areaRpcService")
	private IAreaRpcService areaRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC,value = "orderRpcService")
	private IOrderRpcService orderRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
	private IDefrayAccountRpcService defrayAccountRpcService;
	@ServiceAutowired("preSectionService")
	private IPreSectionService preSectionService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "attachRpcService")
	private IAttachRpcService attachService;

	/**
	 * 获取活动 by PrimaryKey
	 *
	 * @param actId
	 * @return
	 * <AUTHOR>
	 */
	public MarketActBo queryByPrimaryKey(Long actId) {
		MarketActBo condition = new MarketActBo();
		condition.setActId(actId);
		List<MarketActBo> list = marketActDao.qryMarketActives(condition);
		if (list != null && list.size()>0){
			MarketActBo bo = new MarketActBo();
			bo = list.get(0);
			if (bo.getActType().contains(BillConstants.ActType.ACT_TYPE_07)){
				PreSectionBo sectionBo = new PreSectionBo();
				sectionBo.setActId(bo.getActId());
				List<PreSectionBo> sectionList = preSectionDao.qryPreSections(sectionBo);
				if (CollectionUtils.isNotEmpty(sectionList)){
					bo.setPresentSectionId(String.valueOf(sectionList.get(0).getPresentSectionId()));
					bo.setPresentBalType(sectionList.get(0).getPresentBalType());
				}
			}else if (bo.getActType().equals(BillConstants.ActType.ACT_TYPE_03)){
				MarketTypeBo marketTypeBo = new MarketTypeBo();
				marketTypeBo.setActId(String.valueOf(bo.getActId()));
				List<MarketTypeBo> actCondList = null;
				try {
					actCondList = actCondDao.queryActCond(marketTypeBo);
				} catch (Exception e) {
					e.printStackTrace();
				}
				if (CollectionUtils.isNotEmpty(actCondList)){
					MarketTypeBo typeBo = actCondList.get(0);
					bo.setActCondId(String.valueOf(typeBo.getActCondId()));
					bo.setDctLvl(typeBo.getDctLvl());
					bo.setDctCondType(typeBo.getDctCondType());
					bo.setAppUser(typeBo.getAppUser());
					bo.setpBillFlag(typeBo.getpBillFlag());
					bo.setAllStationFlag(typeBo.getAllStationFlag());
				}
			}
			return bo;
		}
		return condition;
	}

	/**
	 * 获取活动列表 by condition
	 */
	public List<MarketActBo> qryMarketActives(MarketActBo condition) {
		condition.setNums(marketActDao.qryMarketActivesNums(condition));
		List<MarketActBo> marketActBos = marketActDao.qryMarketActives(condition);
		if (StringUtil.isNotBlank(condition.getMainFlag())) {
			return marketActBos;
		}
		List<AccountGiftListDTO> accountGiftListDTOS = defrayAccountRpcService.selectGiftList();
		Map<String, AccountGiftListDTO> giftMap = new HashMap<>();
		if(accountGiftListDTOS != null && accountGiftListDTOS.size() >0){
			giftMap = accountGiftListDTOS.stream().collect(Collectors.toMap(AccountGiftListDTO::getActivityNo, Function.identity()));
		}

		if(marketActBos != null && marketActBos.size() >0){
			for(MarketActBo marketActBo : marketActBos){
				String actType = marketActBo.getActType();
				if(!StringUtils.nullOrBlank(actType) && "07".equals(actType)){
					AccountGiftListDTO accountGiftListDTO = giftMap.get(String.valueOf(marketActBo.getActId()));
					if(accountGiftListDTO != null){
						marketActBo.setTotalNums(accountGiftListDTO.getTotalNums());
						marketActBo.setTotalTimes(accountGiftListDTO.getTotalTimes());
						String remark = "累计充值金额："+accountGiftListDTO.getPrePayAmt()+"元；累计馈赠金："+accountGiftListDTO.getGiftAmt()+"元";
						marketActBo.setDetailRemark(remark);
					}
				}
				if(!StringUtils.nullOrBlank(actType) && ("01".equals(actType) || "09".equals(actType))){
					List<Long> cpnIDs = couponDao.selectCouponId(Long.valueOf(marketActBo.getActId()));
					if(cpnIDs != null && cpnIDs.size() >0){
						AccountCouponListDTO accountCouponListDTOS = defrayAccountRpcService.selectCouponList(cpnIDs);
						if(accountCouponListDTOS != null){
							marketActBo.setTotalTimes(accountCouponListDTOS.getCpnIds());
							marketActBo.setTotalNums(accountCouponListDTOS.getAccIds());
							CpnAmtDTO cpnAmtDTO = orderRpcService.selectCpnAmt(cpnIDs);
							String remark = "累计发放/领取优惠券数："+accountCouponListDTOS.getCpnIds()+"；累计优惠："+cpnAmtDTO.getCpnAmt()+"元";
							marketActBo.setDetailRemark(remark);
						}
					}
				}
			}
		}
		return marketActBos;
	}

	/**
	 * 获取活动 listMap
	 * @param map
	 * @return
	 * <AUTHOR>
	 */
	public List<Map<String, Object>> qryMarketActivesMap(Map<String, Object> map) {
		return marketActDao.qryMarketActivesMap(map);
	}

	/**
	 * 关闭/删除活动
	 *
	 * @param oprType
	 * @param actId
	 * <AUTHOR>
	 */
	public void operateMarketAct(String oprType, Long actId) {
		if ("delete".equals(oprType)) {
			PreSectionBo condition = new PreSectionBo();
			condition.setActId(actId);
			List<PreSectionBo> preBos = preSectionDao.qryPreSections(condition);
			if(preBos != null && preBos.size() > 0){
				PreSectionBo pre = preBos.get(0);
				preSectionDao.deleteTimeLimitType(pre.getPresentSectionId());
				preSectionDao.deleteCycleType(pre.getPresentSectionId());
				preSectionDao.deletePreSectionDet(pre.getPresentSectionId());
				preSectionDao.deletePreSection(pre.getPresentSectionId());
			}

			marketActDao.deleteByPrimaryKey(actId);
		}
		else {
			MarketActBo marketActBo = new MarketActBo();
			marketActBo.setActId(actId);
			marketActBo.setActState("4");//活动状态 0草稿 1未开始、2进行中、3已结束、4已关闭
			marketActBo.setCloseTime(DateTools.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
			marketActDao.updateMarketActive(marketActBo);
		}
	}

    /**
     * 关闭/删除活动
     *
     * @param oprType
     * @param actId
     * <AUTHOR>
     */
    public void operateMarketActNew(String oprType, Long actId) {
        if ("delete".equals(oprType)) {
            logger.info("删除---------------");
            PreSectionBo condition = new PreSectionBo();
            condition.setActId(actId);
            List<PreSectionBo> preBos = preSectionDao.qryPreSections(condition);
            if(preBos != null && preBos.size() > 0){
                PreSectionBo pre = preBos.get(0);
                preSectionDao.deleteTimeLimitType(pre.getPresentSectionId());
                preSectionDao.deleteCycleType(pre.getPresentSectionId());
                preSectionDao.deletePreSectionDet(pre.getPresentSectionId());
                preSectionDao.deletePreSection(pre.getPresentSectionId());
            }
            actInfoDao.deleteActInfoByActId(actId);
            marketActDao.deleteByPrimaryKey(actId);
        }
        else {
            MarketActBo marketActBo = new MarketActBo();
            marketActBo.setActId(actId);
            marketActBo.setActState("4");//活动状态 0草稿 1未开始、2进行中、3已结束、4已关闭
            marketActBo.setCloseTime(DateTools.dateToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
            marketActDao.updateMarketActive(marketActBo);
        }
    }

	/**
	 * 保存营销活动
	 *
	 * @param model
	 * <AUTHOR>
	 */
	public Long saveMarketAct(MarketActBo model, MultipartFile file) throws Exception {
		if (model.getActId()>0) {
			logger.info("活动更新");
			//release为发布标识，只有发布时才能变更活动状态为非草稿状态
			if ("true".equals(model.getRelease())) {
				MarketActBo market = queryByPrimaryKey(model.getActId());
				int eff = JodaDateTime.getMinutesTonow(market.getEffTime()+":00");
				int exp = JodaDateTime.getMinutesTonow(market.getExpTime()+":00");
				//生效时间小于当前，失效时间大于当前
				if (eff > 0 && exp < 0) {
					model.setActState(ActStatus.PROCESSING);
				}
				//生效时间大于当前
				else if (eff < 0) {
					model.setActState(ActStatus.UNSTART);
				}
			}
			marketActDao.updateMarketActive(model);
		}
		else {
			logger.info("活动新增");
			model.setActId(seqRpcService.getDefId());
			model.setActNo(seqRpcService.getDefNo());
			model.setActState(ActStatus.DRAFT);
			marketActDao.insertMarketActive(model);
		}

		if (file != null) {
			logger.info("保存文件:{}", file.getOriginalFilename());
			String actId = String.valueOf(model.getActId());
			AttachBo attach = new AttachBo();
			attach.setRelaTable("actImg");
			attach.setRelaId(actId);
			AttachBo oldAttach = attachService.qryAttachInfo(attach);
			if (oldAttach != null && StringUtil.isNotEmpty(oldAttach.getAttachId())) {
				attachService.deleteById(oldAttach.getAttachId());
			}

			// 取得当前上传文件的文件名称
			String fileName = file.getOriginalFilename();
			// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
			if (!"".equals(fileName.trim())) {
				AttachBo attachBo = new AttachBo();
				attachBo.setRelaTable("actImg");
				attachBo.setRelaId(actId);
				attachBo.setFileName(fileName);
				attachBo.setFileSize(String.valueOf(file.getSize()));
				attachBo.setContentType("02");// 图片
				attachService.uploadOneAttach(file.getBytes(), attachBo);
			}

		}

		return model.getActId();
	}

	/**
	 * 活动状态轮询job
	 *
	 * <AUTHOR>
	 */
	public void marketStateJob() {
		//1、更新 未开始的活动为进行中
		String actState = ActStatus.PROCESSING;
		int num = marketActDao.updateMarketActiveState(actState);
		if (num > 0) {
			logger.debug(">>>>> 活动状态轮询job，更新{}个活动为进行中。", num);
		}
		//2、更新 进行中的活动为已结束
		actState = ActStatus.OVER;
		num = marketActDao.updateMarketActiveState(actState);
		if (num > 0) {
			logger.debug(">>>>> 活动状态轮询job，更新{}个活动为已结束。", num);
		}
	}

	/**
	 * @param bo
	 * @description 查询注册送/邀请送，送的优惠券
	 * <AUTHOR>
	 * @create 2017-08-09 10:31:15
	 */
	@Override
	public List<CouponBo> queryActCpn(MarketActBo bo) {
		List<CouponBo> list = couponDao.queryActCpn(bo);
		return list;
	}

	/**
	 * @param
	 * @description  根据活动查询优惠卷
	 * <AUTHOR>
	 * @create 2018-06-04 14:47:41
	 */
	public List<Map<String, Object>> queryActCoupon(String actId){
		List<Map<String, Object>> dataList = new ArrayList<>();
		MarketActBo condition = new MarketActBo();
		condition.setActId(Long.parseLong(actId));
		List<MarketActBo> list = marketActDao.qryMarketActives(condition);
		if (CollectionUtils.isNotEmpty(list)) {
			MarketActBo bo = list.get(0);
			if (BillConstants.ActType.ACT_TYPE_01.equals(bo.getActType())
					||BillConstants.ActType.ACT_TYPE_08.equals(bo.getActType())){
				Map<String, Object> inMap = new HashMap<>();
				inMap.put("actId",actId);
				dataList = marketActDao.queryActCoupon(inMap);
			}else{
				List<CouponBo> cpnList = couponDao.queryActCpn(bo);
				if (CollectionUtils.isNotEmpty(cpnList)){
					for (CouponBo cpnBo : cpnList) {
						Map<String, Object> map = new HashMap<>();
						map.put("cpnId", cpnBo.getCpnId());
						dataList.add(map);
					}
				}
			}
		}
		return dataList;
	}

    /**
     * <AUTHOR>
     * @description 判断活动是否交叉
     * @create 2019/5/27 10:37
     *
     * @return
     */
	@Override
	public JSONObject checkActIsCross(MarketActFormCondition marketActFormCondition) {
	    //新建活动和已有活动按照最小维度单位 不能有时间交叉
		JSONObject jsonObject =  new JSONObject();
	    int resCode=200;

		String buildType = marketActFormCondition.getBuildType();
		String cityType = marketActFormCondition.getCity();
		String stationType = marketActFormCondition.getStationId();
		logger.info("查询交叉buildType-----------------"+buildType);
		logger.info("查询交叉cityType-----------------"+cityType);
		logger.info("查询交叉stationType-----------------"+stationType);

		String effTime = marketActFormCondition.getEffTime();
		String expTime = marketActFormCondition.getExpTime();

		logger.info("查询交叉effTime-----------------"+effTime);
		logger.info("查询交叉expTime-----------------"+expTime);

		Map<String,Object> paramsMap = new HashMap<>();
        List<String> cityList = null;
        List<String> stationList = null;
        if (marketActFormCondition.getCustType().contains(",")){
            String[] custTypeStr = marketActFormCondition.getCustType().split(",");
            paramsMap.put("custType1",custTypeStr[0]);
            paramsMap.put("custType2",custTypeStr[1]);
        }else {
            paramsMap.put("custType1",marketActFormCondition.getCustType());
            paramsMap.put("custType2",null);
        }
        paramsMap.put("actType",marketActFormCondition.getActType());
		if ("0".equals(buildType)){
			//运营商为全部时 都是全部 SCOPE_TYPE=1
			paramsMap.put("scopeType","1");
			paramsMap.put("buildId","0");
		}else{
			if (StringUtil.isNotBlank(marketActFormCondition.getBuildId())){
				paramsMap.put("buildId", marketActFormCondition.getBuildId());
			}
			if ("0".equals(cityType) && "0".equals(stationType)){
				//运营商为部分时 其他都是全部 SCOPE_TYPE=2
				paramsMap.put("scopeType","2");

			}
            if ("1".equals(cityType) && "0".equals(stationType)){
                //运营商为部分 城市部分 站点全部 SCOPE_TYPE=3
                paramsMap.put("scopeType","3");
                if (StringUtil.isNotBlank(marketActFormCondition.getCityCodes())){
					cityList = new ArrayList<>();
					String cityCodes = marketActFormCondition.getCityCodes();
					if (StringUtil.isNotBlank(cityCodes)) {
						if (cityCodes.contains(",")) {
							String[] cityStr = cityCodes.split(",");
							cityList = Arrays.asList(cityStr);
							logger.info("cityList----------"+cityList);
						}
					}
					paramsMap.put("cityList",cityList);
				}
            }
            if ("1".equals(cityType) && "1".equals(stationType)){
                //运营商为部分 城市部分 站点部分 SCOPE_TYPE=4
                paramsMap.put("scopeType","4");
				if (StringUtil.isNotBlank(marketActFormCondition.getCityCodes())){
					cityList = new ArrayList<>();
					String cityCodes = marketActFormCondition.getCityCodes();
					if (StringUtil.isNotBlank(cityCodes)) {
						if (cityCodes.contains(",")) {
							String[] cityStr = cityCodes.split(",");
							cityList = Arrays.asList(cityStr);
							logger.info("cityList----------"+cityList);
						}
					}
					paramsMap.put("cityList",cityList);
				}
				if (StringUtil.isNotBlank(marketActFormCondition.getStationIds())){
					stationList = new ArrayList<>();
					String stationIdsValue = marketActFormCondition.getStationIds();
					if (stationIdsValue.contains(",")) {
						String[] stationStr = stationIdsValue.split(",");
                        for (int i=0 ; i< stationStr.length;i++){
                            stationList.add(stationStr[i].split("_")[0]);
                        }
						logger.info("stationList----------"+stationList);
					}else{
                        stationList.add(stationIdsValue.split("_")[0]);
                    }
					paramsMap.put("stationList",stationList);
				}
            }
		}
		logger.info("paramsMap----------"+paramsMap);
		List<Map<String,Object>> resultList = marketActDao.queryActScope(paramsMap);
		String oldActName = "";
		for (Map<String, Object> map: resultList){
			String effDateStr = String.valueOf(map.get("effTime"));
			String expDateStr= String.valueOf(map.get("expTime"));
			//-1 ：小于 ， 0：  等于 ，1：大于

			if (DateTools.compareDate(expTime,effDateStr+":00")!=-1 && DateTools.compareDate(expTime,expDateStr+":00")!=1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}

			if (DateTools.compareDate(effTime,effDateStr+":00")!=-1 && DateTools.compareDate(effTime,expDateStr+":00")!=1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}
			/*if (DateTools.compareDate(effTime,effDateStr+":00")!=1 && DateTools.compareDate(expTime,effDateStr+":00")!=1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}
			if (DateTools.compareDate(effTime,effDateStr+":00")!=1 && DateTools.compareDate(expTime,expDateStr+":00")!=1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}


			if (DateTools.compareDate(effTime,effDateStr+":00")==1 && DateTools.compareDate(expTime,expDateStr+":00")!=1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}
			if (DateTools.compareDate(effTime,effDateStr+":00")==1 && DateTools.compareDate(expTime,expDateStr+":00")==1){
				resCode = -1;
				oldActName = String.valueOf(map.get("actName"));
			}*/
		}
		jsonObject.put("resCode",resCode);
		jsonObject.put("oldActName",oldActName);
		return jsonObject;
	}

    public long saveMarketActNew(MarketActBo model) throws Exception {

		if ("true".equals(model.getRelease())) {//发布
			logger.info("//发布================");
			//1、立即生效：生效时间为当前，失效时间大于当前时间1分钟，状态进行中。
            //0、无立即生效：生效时间大于当前时间30分钟，失效时间大于生效时间时间1分钟，状态未开始
			if ("1".equals(model.getIsImmediate())){
				model.setActState(ActStatus.PROCESSING);
			}else{
                model.setIsImmediate("0");
                model.setActState(ActStatus.UNSTART);
			}

		}else { //保存
			logger.info("//保存================");
			if ("1".equals(model.getIsImmediate())){
				model.setEffTime("");
				//草稿
				model.setActState(ActStatus.DRAFT);
			}else{
				model.setIsImmediate("0");
				logger.info("保存----------------efftime:"+model.getEffTime());
				logger.info("保存----------------exptime:"+model.getExpTime());
				int eff = JodaDateTime.getMinutesTonow(model.getEffTime()+":00");
				int exp = JodaDateTime.getMinutesTonow(model.getExpTime()+":00");
				model.setActState(ActStatus.DRAFT);
			}
		}
        if (model.getActId()>0){
            //更新
			logger.info("//更新================");
			marketActDao.updateMarketActive(model);
        }else {
            //新增
			logger.info("//新增================");
			model.setActId(seqRpcService.getDefId());
			model.setActNo(seqRpcService.getDefNo());
			logger.info("获取id================"+model.getActId());
			marketActDao.insertMarketActive(model);
        }

		return model.getActId();
    }

	@Override
	public void saveActScope(MarketActFormCondition marketActFormCondition ,String actId,String actType)throws Exception {
		List<MarketActScopeBo> marketActScopeBoList = new ArrayList<>();
		Map<String,Object> imap = new HashMap<>();
		imap.put("actId",actId);
		if (CollectionUtils.isNotEmpty(marketActScopeDao.queryScopeList(imap))){
			marketActScopeDao.deleteByActId(actId);
		}
		/*String buildType = StringUtil.trimToEmpty(inMap.get("buildType"));
		String cityType = StringUtil.trimToEmpty(inMap.get("city"));
		String stationType = StringUtil.trimToEmpty(inMap.get("stationId"));*/

		String buildType = marketActFormCondition.getBuildType();
		String cityType = marketActFormCondition.getCity();
		String stationType = marketActFormCondition.getStationId();
		logger.info("buildType==========="+buildType);
		logger.info("cityType==========="+cityType);
		logger.info("cityType==========="+stationType);
		if ("0".equals(buildType)){
			MarketActScopeBo marketActScopeBo = new MarketActScopeBo();
			marketActScopeBo.setActId(String.valueOf(actId));
			marketActScopeBo.setActType(actType);
			marketActScopeBo.setBuildId("0");
			marketActScopeBo.setScopeType("1");
			marketActScopeBoList.add(marketActScopeBo);
		}else {
			if ("0".equals(cityType) && "0".equals(stationType)){
				MarketActScopeBo marketActScopeBo = new MarketActScopeBo();
				marketActScopeBo.setActId(String.valueOf(actId));
				marketActScopeBo.setActType(actType);
				marketActScopeBo.setBuildId(marketActFormCondition.getBuildId());
				marketActScopeBo.setScopeType("2");
				marketActScopeBoList.add(marketActScopeBo);
			}
			if ("1".equals(cityType) && "0".equals(stationType)){
				String cityCodes =marketActFormCondition.getCityCodes();
				if (StringUtil.isNotBlank(cityCodes) && cityCodes.contains(",")){
					String[] cityStr = cityCodes.split(",");
					for (int i=0 ; i< cityStr.length;i++){
						MarketActScopeBo marketActScopeBo = new MarketActScopeBo();
						marketActScopeBo.setActId(String.valueOf(actId));
						marketActScopeBo.setActType(actType);
						marketActScopeBo.setBuildId(marketActFormCondition.getBuildId());
						marketActScopeBo.setCity(cityStr[i]);
						marketActScopeBo.setScopeType("3");
						marketActScopeBoList.add(marketActScopeBo);
					}
				}
			}
			if ("0".equals(cityType) && "1".equals(stationType)){
				String stationIds = marketActFormCondition.getStationIds();
				if (StringUtil.isNotBlank(stationIds) && stationIds.contains(",")){
					String[] stationStr = stationIds.split(",");
					for (int i=0 ; i< stationStr.length;i++){
						MarketActScopeBo marketActScopeBo = new MarketActScopeBo();
						marketActScopeBo.setActId(String.valueOf(actId));
						marketActScopeBo.setActType(actType);
						marketActScopeBo.setBuildId(marketActFormCondition.getBuildId());
						marketActScopeBo.setStationId(stationStr[i].split("_")[0]);
						marketActScopeBo.setScopeType("4");
						marketActScopeBoList.add(marketActScopeBo);
					}
				}
			}

			if ("1".equals(cityType) && "1".equals(stationType)){
				String stationIds = marketActFormCondition.getStationIds();
				logger.info("stationIds============"+stationIds);
				if (StringUtil.isNotBlank(stationIds) && stationIds.contains(",")){
					String[] stationStr = stationIds.split(",");
					for (int i=0 ; i< stationStr.length;i++){
						MarketActScopeBo marketActScopeBo = new MarketActScopeBo();
						marketActScopeBo.setActId(String.valueOf(actId));
						marketActScopeBo.setActType(actType);
						marketActScopeBo.setBuildId(marketActFormCondition.getBuildId());
						marketActScopeBo.setStationId(stationStr[i].split("_")[0]);
						marketActScopeBo.setCity(stationStr[i].split("_")[1]);
						marketActScopeBo.setScopeType("4");
						marketActScopeBoList.add(marketActScopeBo);
					}
				}
			}
		}
		if (CollectionUtils.isNotEmpty(marketActScopeBoList)){
			marketActScopeDao.insertMarketActScopeBatch(marketActScopeBoList);
		}
	}

	/**
	 * 获取活动列表 by condition
	 */
	public Map<String,Object> qryMarketActivesNew(MarketActBo condition)throws Exception {
		logger.info("执行新的查询------------------");
		logger.info("actState-------------------"+condition.getActState());
		Map<String,Object> reMap =  new HashMap<>();
		if ("1".equals(condition.getBuildId())){
			condition.setBuildId("");
		}
		reMap.put("num",marketActDao.qryMarketActivesNumNew(condition));
		List<Map<String,Object>> resultList = marketActDao.qryMarketActivesNew(condition);
		List<String> actIdList = new ArrayList<>();
		Map<String,Object> orderMap = new HashMap<>();
		for (Map<String,Object> map : resultList){
			//初始化 状态名字
			String actState = StringUtil.trimToEmpty(map.get("actState"));
			if (ActStatus.DRAFT.equals(actState)){
				map.put("actStateName","草稿");
			}else if (ActStatus.CLOSED.equals(actState)){
				//已关闭：状态为已关闭状态
				map.put("actStateName","已关闭");
			}else{
				logger.info("effTime------------------"+StringUtil.trimToEmpty(map.get("effTime")));
				logger.info("expTime------------------"+StringUtil.trimToEmpty(map.get("expTime")));
				int eff = JodaDateTime.getMinutesTonow(StringUtil.trimToEmpty(map.get("effTime")));
				int exp = JodaDateTime.getMinutesTonow(StringUtil.trimToEmpty(map.get("expTime")));
				logger.info("eff------------------"+eff);
				logger.info("exp------------------"+exp);
				//未开始 当前小于生效时间，且状态为未开始状态；
				if (eff < 0 && ActStatus.UNSTART.equals(actState)){
					map.put("actStateName","未开始");
				}
				//进行中 当前时间在生效与失效时间期间，且状态为未开始或进行中状态
				if (eff >= 0 && exp <= 0 && (ActStatus.PROCESSING.equals(actState) || ActStatus.UNSTART.equals(actState))){
					map.put("actStateName","进行中");
				}
				//已结束：当前时间大于失效时间，且状态为未开始、进行中、已结束；
				if (exp > 0 && (ActStatus.PROCESSING.equals(actState) || ActStatus.UNSTART.equals(actState) || ActStatus.OVER.equals(actState))){
					map.put("actStateName","已结束");
				}
			}

			//初始化运营商
			String buildId = StringUtil.trimToEmpty(map.get("buildId"));
			String cityCodes = StringUtil.trimToEmpty(map.get("cityCodes"));
			if ("0".equals(buildId)){
				map.put("buildName","全部");
				map.put("cityName","全部");
			}else {
				if (StringUtil.isNotBlank(buildId)){
					Map<String,Object> paramMap = new HashMap<>();
					paramMap.put("operNo",buildId);
					List<Map<String, Object>> operInfoList = defrayOperRpcService.qryOperInfoList(paramMap);
					if (CollectionUtils.isNotEmpty(operInfoList)){
						map.put("buildName",operInfoList.get(0).get("operName"));
					}
				}

			}
			//初始化城市名称
			if (StringUtil.isNotBlank(cityCodes)){
				String cityName = "";
				if(!StringUtils.nullOrBlank(cityCodes)){
					AreaCondition areaCondition = new AreaCondition();
					areaCondition.setAreaCode(cityCodes);
					List<Map> cityList = areaRpcService.queryCityByCodes(areaCondition);
					for (Map map1 : cityList){
						cityName += StringUtil.trimToEmpty(map1.get("AREA_NAME"))+",";
					}

					if (cityName.length()>0){
						map.put("cityName",cityName.substring(0,cityName.length()-1));
					}
				}
			}else{
				map.put("cityName","全部");
			}
			actIdList.add(String.valueOf(map.get("actId")));
			/*if("07".equals(map.get("actType"))){
				Map<String,Object> map1 = new HashMap<>();
				map1.put("actId",String.valueOf(map.get("actId")));
				Map<String, Object> sectionmap = marketActDao.qryGiftBalanceValue(map1);
				if(sectionmap!=null){
					Long maxValue = ((BigDecimal) sectionmap.get("presentMaxValue")).longValue();
					Long baseValue = ((BigDecimal) sectionmap.get("baseValue")).longValue();
					if (baseValue > maxValue) {
						map.put("presentValue", maxValue.toString());
					} else {
						map.put("presentValue", baseValue.toString());
					}
				}

			}*/
		}
		//初始化参与人数
		logger.info("初始化参与人数==============");
		List<Map<String,Object>> joinNumList = new ArrayList<>();
		if(actIdList!=null && actIdList.size()>0){
			joinNumList = marketActDao.qryJoinActNumAndOrder(actIdList);
			MergeUtil.mergeList(resultList, joinNumList, "actId", "actId", new String[]{"joinNum"}, new String[]{"joinNum"});
		}
        if("02".equals(condition.getActType())){
            //初始化优惠金额
            logger.info("限时折扣--初始化优惠金额==============");
			for (Map<String,Object> oMap : joinNumList){
				logger.info("oMap-----------------"+oMap);
				List<String> orderNoList = new ArrayList<>();
				if (StringUtil.isNotBlank(String.valueOf(oMap.get("orderNoStr")))){
					if (String.valueOf(oMap.get("orderNoStr")).contains(",")){
						String[] orderArray = String.valueOf(oMap.get("orderNoStr")).split(",");
						orderNoList = Arrays.asList(orderArray);
					}else {
						orderNoList.add(String.valueOf(oMap.get("orderNoStr")));
					}
				}
				orderMap.put(String.valueOf(oMap.get("actId")),orderNoList);
			}
            if (orderMap.size()>0){
                List<Map<String,Object>> discountList = orderRpcService.queryDiscountBalForAct(orderMap);
                logger.info("discountList-----------"+discountList.size());
                MergeUtil.mergeList(resultList, discountList, "actId", "actId", new String[]{"discountTotal"}, new String[]{"discountBal"});
            }
        }
		if("07".equals(condition.getActType())){
			//初始化优惠金额
			logger.info("预存赠送--初始化优惠金额==============");

			if (actIdList.size()>0){
				List<Map<String,Object>> giftList = defrayAccountRpcService.queryGiftAmtForAct(actIdList);
				logger.info("giftList-----------"+giftList);
				MergeUtil.mergeList(resultList, giftList, "actId", "actId", new String[]{"presentValue"}, new String[]{"presentValue"});
			}
		}
		reMap.put("dataList",resultList);
		return reMap;
	}
}
