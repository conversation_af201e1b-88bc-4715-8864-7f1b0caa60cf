package com.ls.ner.billing.charge.bo;

import java.util.List;



/**
 * <AUTHOR>
 * @dateTime 2016-03-28
 * @description 充电订单计费结果  E_CHARGE_BILLING_RLT
 */
public class ChargeBillingRltBo {
	private String systemId;//主键列
	private String appNo;//订单编号
	private String chcNo;//充电计费编号
	private String billCtlMode;//费控模式，1本地 2远程
	private String chargeMode;//计费模式 01标准 02分时
	private String tPq;//总电量。如果是分时，则为分时电量总和
	private String tAmt;//充电金额。如果是分时，则为分时金额总和
	private String itemTAmt;//服务项目总金额
	private String amt;//总金额
	private String attachItemNos;//服务项目编号，逗号隔开
	private String attachItemPrices;//服务项目单价，逗号隔开
	private String attachItemUnits;//服务项目单位，逗号隔开
	private String attachItemNums;//服务项目数量，逗号隔开
	private String attachItemAmts;//服务项目金额，逗号隔开
	private String bgnTime;//充电开始时间
	private String endTime;//充电结束时间
	private String tTime;//充电时间，单位：分钟
	private String applyBgnTime;//请求生成时间
	private String applyEndTime;//请求结算时间
	private String chargePrice;//计费单价
	private String bgnMrNum;//充电开始示数
	private String endMrNum;//充电结束示数
	private String freeNum;//免费量

	private String itemChargeMode;//

	private List<ChargeBillingRltPeriodsBo> rltPeriods;//充电计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	private List<ChargeBillingRltPeriodsBo> newPeriods;//充电实时计费结果分时明细  E_CHARGE_BILLING_RLT_PERIODS
	private List<ChargeBillingRltPeriodsBo> itemRltPeriods;//充电计费结果分时明细-费用项  E_CHARGE_BILLING_RLT_PERIODS
	private List<ChargeBillingRltPeriodsBo> serPeriods;//充电计费入参分时明细-费用项  E_CHARGE_BILLING_RLT_PERIODS
	private List<ChargeBillingRltPeriodsBo> serRltPeriods;//服务费分时明显

	public List<ChargeBillingRltPeriodsBo> getItemRltPeriods() {
		return itemRltPeriods;
	}

	public void setItemRltPeriods(List<ChargeBillingRltPeriodsBo> itemRltPeriods) {
		this.itemRltPeriods = itemRltPeriods;
	}

	public List<ChargeBillingRltPeriodsBo> getSerPeriods() {
		return serPeriods;
	}

	public void setSerPeriods(List<ChargeBillingRltPeriodsBo> serPeriods) {
		this.serPeriods = serPeriods;
	}

	public List<ChargeBillingRltPeriodsBo> getSerRltPeriods() {
		return serRltPeriods;
	}

	public void setSerRltPeriods(List<ChargeBillingRltPeriodsBo> serRltPeriods) {
		this.serRltPeriods = serRltPeriods;
	}

	public String getItemChargeMode() {
		return itemChargeMode;
	}

	public void setItemChargeMode(String itemChargeMode) {
		this.itemChargeMode = itemChargeMode;
	}

	public String getFreeNum() {
		return freeNum;
	}
	public void setFreeNum(String freeNum) {
		this.freeNum = freeNum;
	}
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public String getAppNo() {
		return appNo;
	}
	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getBillCtlMode() {
		return billCtlMode;
	}
	public void setBillCtlMode(String billCtlMode) {
		this.billCtlMode = billCtlMode;
	}
	public String getChargeMode() {
		return chargeMode;
	}
	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}
	public String gettPq() {
		return tPq;
	}
	public void settPq(String tPq) {
		this.tPq = tPq;
	}
	public String gettAmt() {
		return tAmt;
	}
	public void settAmt(String tAmt) {
		this.tAmt = tAmt;
	}
	public String getItemTAmt() {
		return itemTAmt;
	}
	public void setItemTAmt(String itemTAmt) {
		this.itemTAmt = itemTAmt;
	}
	public String getAmt() {
		return amt;
	}
	public void setAmt(String amt) {
		this.amt = amt;
	}
	public String getAttachItemNos() {
		return attachItemNos;
	}
	public void setAttachItemNos(String attachItemNos) {
		this.attachItemNos = attachItemNos;
	}
	public String getAttachItemPrices() {
		return attachItemPrices;
	}
	public void setAttachItemPrices(String attachItemPrices) {
		this.attachItemPrices = attachItemPrices;
	}
	public String getAttachItemUnits() {
		return attachItemUnits;
	}
	public void setAttachItemUnits(String attachItemUnits) {
		this.attachItemUnits = attachItemUnits;
	}
	public String getAttachItemNums() {
		return attachItemNums;
	}
	public void setAttachItemNums(String attachItemNums) {
		this.attachItemNums = attachItemNums;
	}
	public String getAttachItemAmts() {
		return attachItemAmts;
	}
	public void setAttachItemAmts(String attachItemAmts) {
		this.attachItemAmts = attachItemAmts;
	}
	public String getBgnTime() {
		return bgnTime;
	}
	public void setBgnTime(String bgnTime) {
		this.bgnTime = bgnTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String gettTime() {
		return tTime;
	}
	public void settTime(String tTime) {
		this.tTime = tTime;
	}
	public String getApplyBgnTime() {
		return applyBgnTime;
	}
	public void setApplyBgnTime(String applyBgnTime) {
		this.applyBgnTime = applyBgnTime;
	}
	public String getApplyEndTime() {
		return applyEndTime;
	}
	public void setApplyEndTime(String applyEndTime) {
		this.applyEndTime = applyEndTime;
	}
	public String getChargePrice() {
		return chargePrice;
	}
	public void setChargePrice(String chargePrice) {
		this.chargePrice = chargePrice;
	}
	public String getBgnMrNum() {
		return bgnMrNum;
	}
	public void setBgnMrNum(String bgnMrNum) {
		this.bgnMrNum = bgnMrNum;
	}
	public String getEndMrNum() {
		return endMrNum;
	}
	public void setEndMrNum(String endMrNum) {
		this.endMrNum = endMrNum;
	}
	public List<ChargeBillingRltPeriodsBo> getRltPeriods() {
		return rltPeriods;
	}
	public void setRltPeriods(List<ChargeBillingRltPeriodsBo> rltPeriods) {
		this.rltPeriods = rltPeriods;
	}
	public List<ChargeBillingRltPeriodsBo> getNewPeriods() {
		return newPeriods;
	}
	public void setNewPeriods(List<ChargeBillingRltPeriodsBo> newPeriods) {
		this.newPeriods = newPeriods;
	}
}