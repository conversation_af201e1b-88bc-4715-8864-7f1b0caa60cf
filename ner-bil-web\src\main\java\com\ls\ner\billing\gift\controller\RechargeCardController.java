package com.ls.ner.billing.gift.controller;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.gift.bo.RechargeCardBo;
import com.ls.ner.billing.gift.bo.RechargeCardDetail;
import com.ls.ner.billing.gift.bo.RechargeCardOrder;
import com.ls.ner.billing.gift.condition.RechargeCardCondition;
import com.ls.ner.billing.gift.service.IRechargeCardService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.controller
 * @ClassName: RechargeCardController
 * @Author: bdBoWenYang
 * @Description: 充值卡配置
 * @Date: 2025/4/27 9:02
 * @Version: 1.0
 */
@Controller
@RequestMapping("/bil/rechargeCard")
public class RechargeCardController extends QueryController<RechargeCardCondition> {

    private static final Logger logger = LoggerFactory.getLogger(RechargeCardController.class);

    @ServiceAutowired(value="rechargeCardService")
    private IRechargeCardService iRechargeCardService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "codeService")
    private ICodeService codeService;


    @RequestMapping(value = "/init")
    public String init(Model m) {
        m.addAttribute("searchForm",new RechargeCardBo());
        return "/bil/card/rechargeCard";
    }

    /**
     * @param params
     * @description 充值卡列表数据查询
     * <AUTHOR>
     * @create 2020-05-04 11:24:48
     */
    @RequestMapping(value = "/querys")
    public @ItemResponseBody
    QueryResultObject querys(@QueryRequestParam("params") RequestCondition params){
        RechargeCardCondition condition = this.rCondition2QCondition(params);
        logger.debug("充值卡查询入参{}", JsonUtil.obj2Json(condition));
        List<RechargeCardBo> dataList = iRechargeCardService.listRechargeCard(condition);
        int count = iRechargeCardService.countRechargeCard(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 详情
     * @param params
     * @return
     */
    @RequestMapping("/qryDetail")
    public @ItemResponseBody QueryResultObject qryDetail(
            @QueryRequestParam("params") RequestCondition params) {
        RechargeCardCondition condition = this.rCondition2QCondition(params);
        List<RechargeCardDetail> dataList = new ArrayList<RechargeCardDetail>();
        int count = iRechargeCardService.queryDetailsCount(condition);
        if(count>0) {
            dataList = iRechargeCardService.queryDetails(condition);
        }
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 详情
     * @param params
     * @return
     */
    @RequestMapping("/qryCardOrderDetail")
    public @ItemResponseBody QueryResultObject qryCardOrderDetail(
            @QueryRequestParam("params") RequestCondition params) {
        RechargeCardCondition condition = this.rCondition2QCondition(params);
        List<RechargeCardOrder> dataList = new ArrayList<RechargeCardOrder>();
        int count = iRechargeCardService.queryCardOrderDetailCount(condition);
        if(count>0) {
            dataList = iRechargeCardService.qryCardOrderDetail(condition);
        }
        return RestUtils.wrappQueryResult(dataList, count);
    }


    @RequestMapping(value = "/addCard", method = RequestMethod.GET)
    public String addCard(Model m, @RequestParam("cardId") String cardId) {
        if (StringUtils.nullOrBlank(cardId)) {
            RechargeCardBo bo = new RechargeCardBo();
            m.addAttribute("addCardForm", bo);
        } else {
            RechargeCardBo condition = new RechargeCardBo();
            condition.setCardId(Long.valueOf(cardId));
            RechargeCardBo vo = iRechargeCardService.queryDetail(condition);
            if(vo != null){
                m.addAttribute("addCardForm", vo);
            }else{
                m.addAttribute("addCardForm", new RechargeCardBo());
            }
        }
        return "/bil/card/addCard";
    }

    @RequestMapping(value = "/updateCard", method = RequestMethod.GET)
    public String updateCard(Model m, @RequestParam("cardId") String cardId) {
        if (StringUtils.nullOrBlank(cardId)) {
            RechargeCardBo bo = new RechargeCardBo();
            m.addAttribute("addCardForm", bo);
        } else {
            RechargeCardBo condition = new RechargeCardBo();
            condition.setCardId(Long.valueOf(cardId));
            RechargeCardBo vo = iRechargeCardService.queryDetail(condition);
            if(vo != null){
                m.addAttribute("addCardForm", vo);
            }else{
                m.addAttribute("addCardForm", new RechargeCardBo());
            }
        }
        return "/bil/card/updateCard";
    }

    @RequestMapping(value = "/addBatchCardInit", method = RequestMethod.GET)
    public String addBatchCardInit(Model m, @RequestParam("cardId") String cardId) {
        if (StringUtils.nullOrBlank(cardId)) {
            RechargeCardBo bo = new RechargeCardBo();
            m.addAttribute("addBatchCardForm", bo);
        } else {
            RechargeCardBo condition = new RechargeCardBo();
            condition.setCardId(Long.valueOf(cardId));
            RechargeCardBo vo = iRechargeCardService.queryDetail(condition);
            if(vo != null){
                m.addAttribute("addBatchCardForm", vo);
            }else{
                m.addAttribute("addBatchCardForm", new RechargeCardBo());
            }
        }
        return "/bil/card/addBatchCard";
    }


    @RequestMapping(value = "/cardDetails/{cardId}")
    public String getChkDetail(Model m, @PathVariable String cardId) {
        m.addAttribute("cardId", cardId);
        List<CodeBO> rechargeCardStatus = codeService.getStandardCodes("rechargeCardStatus",null);
        m.addAttribute("rechargeCardStatusList", rechargeCardStatus);
        m.addAttribute("cardDetailForm",new RechargeCardDetail());
        return "/bil/card/cardDetail";
    }

    @RequestMapping(value = "/cardOrderDetails/{cardDetailId}")
    public String cardOrderDetails(Model m, @PathVariable String cardDetailId) {
        m.addAttribute("cardDetailId", cardDetailId);
        String ordUrl = PublicConstants.ApplicationPath.getOrdPath();
        m.addAttribute("ordUrl", ordUrl);
        return "/bil/card/cardOrderDetails";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public @ResponseBody WrappedResult save(@RequestBody Map<String, Object> dataParams, Model m) {
        List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
        RechargeCardBo bo = new RechargeCardBo();
        try {
            for (Map<String, Object> map : saveList) {
                BeanUtils.populate(bo, map);
            }
            if (StringUtils.nullOrBlank(StringUtil.nullToString(bo.getCardId()))) {//新增
                iRechargeCardService.addRechargeCard(bo);
            } else {//修改
                iRechargeCardService.updateRechargeCard(bo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return WrappedResult.failedWrappedResult("保存失败！");
        }
        return WrappedResult.successWrapedResult(bo);
    }

    @RequestMapping(value = "/addBatchCard", method = RequestMethod.POST)
    public @ResponseBody WrappedResult addBatchCard(@RequestBody Map<String, Object> dataParams, Model m) {
        List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
        RechargeCardBo bo = new RechargeCardBo();
        try {
            for (Map<String, Object> map : saveList) {
                BeanUtils.populate(bo, map);
            }
            iRechargeCardService.addBatchNoCard(bo);
        } catch (Exception e) {
            e.printStackTrace();
            return WrappedResult.failedWrappedResult("保存失败！");
        }
        return WrappedResult.successWrapedResult(bo);
    }


    /**
     * <AUTHOR>
     * @dateTime 2016-03-24
     * @description 启用服务费用项目
     */
    @RequestMapping(value = "/enableCard", method = RequestMethod.POST)
    public @ResponseBody WrappedResult enableChargeSerItem(Model m, @RequestParam("cardId") Long cardId) {
        RechargeCardBo bo = new RechargeCardBo();
        bo.setCardId(cardId);
        bo.setCardStatus("1");//启用
        iRechargeCardService.updateCardStatus(bo);
        return WrappedResult.successWrapedResult(true);
    }

    /**
     * <AUTHOR>
     * @dateTime 2016-03-24
     * @description 停用服务费用项目
     */
    @RequestMapping(value = "/disableCard")
    public @ResponseBody WrappedResult disableChargeSerItem(Model m, @RequestParam("cardId") Long cardId) {
        RechargeCardBo bo = new RechargeCardBo();
        bo.setCardId(cardId);
        bo.setCardStatus("0");//停用
        iRechargeCardService.updateCardStatus(bo);
        return WrappedResult.successWrapedResult(true);
    }

    @RequestMapping(value = "/updateCardDetailStatus")
    public @ResponseBody WrappedResult updateCardDetailStatus(Model m, @RequestParam("cardDetailId") Long cardDetailId, @RequestParam("detailStatus") String detailStatus) {
        RechargeCardDetail bo = new RechargeCardDetail();
        bo.setCardDetailId(cardDetailId);
        //0 待激活 1 已激活 2已冻结 3已退款 4使用完毕
        bo.setCardDetailStatus(detailStatus);
        iRechargeCardService.updateCardDetailStatus(bo);
        return WrappedResult.successWrapedResult(true);
    }


    @Override
    protected RechargeCardCondition initCondition() {
        return new RechargeCardCondition();
    }
}
