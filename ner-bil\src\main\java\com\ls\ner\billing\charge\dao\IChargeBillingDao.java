package com.ls.ner.billing.charge.dao;

import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;

/**
 * <AUTHOR>
 * @dateTime 2016-03-31
 * @description 充电实时计费 E_CHARGE_BILLING
 */
public interface IChargeBillingDao {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 查询充电实时计费 E_CHARGE_BILLING
	 */
	public ChargeBillingRltBo queryChargeBilling(String appNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 查询充电实时计费条数 E_CHARGE_BILLING
	 */
	public int queryChargeBillingNum(String appNo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 新增充电实时计费 E_CHARGE_BILLING
	 */
	public void insertChargeBilling(ChargeBillingRltBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-31
	 * @description 更新充电实时计费 E_CHARGE_BILLING
	 */
	public void updateChargeBilling(ChargeBillingRltBo bo);
	

}
