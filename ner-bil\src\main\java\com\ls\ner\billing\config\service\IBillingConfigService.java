package com.ls.ner.billing.config.service;

import java.util.List;

import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.config.bo.BillingConfigForm;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;

public interface IBillingConfigService {
	
	/**
	 * 根据资费标准编号初始化定价表单
	 * @param planNo
	 * @return
	 */
	public BillingConfigForm initFormByPlanNo(String planNo);
	
	public BillingConfigForm getFormByBillingNo(String billingNo);
	
	public List<BillingConfigBo> pagi(BillingConfigQueryCondition q);
	
	public String create(BillingConfigForm form);
	
	public void update(BillingConfigForm form);

	/**
	 * 
	 * 方法说明：租赁定价批量 删除
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午12:56:55
	 * History:  2016年4月12日 下午12:56:55   lipf   Created.
	 *
	 * @param ids
	 *
	 */
	public void deleteBillings(String[] ids);

	/**
	 * 
	 * 方法说明：调整定价批量保存
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午1:41:24
	 * History:  2016年4月12日 下午1:41:24   lipf   Created.
	 *
	 * @param form
	 * @return
	 *
	 */
	public String createAdjust(BillingConfigForm form);

	/**
	 * 
	 * 方法说明：定价配置复制保存
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月12日 下午3:53:49
	 * History:  2016年4月12日 下午3:53:49   lipf   Created.
	 *
	 * @param bo
	 * @return 
	 *
	 */
	public String copyConfig(BillingConfigBo bo);

	/**
	 * 
	 * 方法说明：
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月24日 下午10:21:19
	 * History:  2016年5月24日 下午10:21:19   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public List<BillingConfigBo> getAutoBillingList(BillingConfigQueryCondition condition);
}
