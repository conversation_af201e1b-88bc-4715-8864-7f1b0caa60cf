package com.ls.ner.billing.tariff2.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.tariff2.service.ITariffPlanService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

@Service(target = { ServiceType.APPLICATION }, value = "tariffPlanService")
public class TariffPlanService implements ITariffPlanService{

	@Autowired
	TariffPlanServiceImpl tariffPlanServiceImpl;

	public TariffPlanBo getByPlanNo(String planNo) {
		return tariffPlanServiceImpl.getByPlanNo(planNo);
	}

	public List<TariffPlanBo> getTariffPlanList(TariffPlanBo bo) {
		return tariffPlanServiceImpl.getTariffPlanList(bo);
	}

	public void update(TariffPlanBo bo) {
		tariffPlanServiceImpl.update(bo);
	}

	public String create(TariffPlanBo bo) {
		return tariffPlanServiceImpl.create(bo);
	}

	public void deletePlans(String[] ids) {
		tariffPlanServiceImpl.deletePlans(ids);
	}

	public void startTariff(String[] ids) {
		tariffPlanServiceImpl.startTariff(ids);
	}

	public void stopTariff(String[] ids) {
		tariffPlanServiceImpl.stopTariff(ids);
	}

	public void copyTariff(TariffPlanBo bo) {
		tariffPlanServiceImpl.copyTariff(bo);
	}
	
	
}
