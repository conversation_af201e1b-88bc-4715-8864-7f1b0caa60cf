package com.ls.ner.billing.charge.service.impl;

import java.util.AbstractList;
import java.util.ArrayList;
import java.util.List;

import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.dao.IChargeBillingConfDao;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.util.JodaDateTime;
import com.pt.poseidon.api.framework.ServiceAutowired;

@Component
public class ChargingPriingRequestHandler {
	
	private static final Logger logger = LoggerFactory.getLogger(ChargingPriingRequestHandler.class);

	@Autowired
	private IChargeBillingConfDao chargeBillingConfDao;

	@ServiceAutowired("xpStationSendService")
	private IXpStationSendService xpStationSendService;

	@ServiceAutowired("chcbillSendService")
	private IChcbillSendService chcbillSendService;

	@ServiceAutowired(serviceTypes= ServiceType.RPC)
	private ISysParamService sysParamService;
	
	public void handle(String stationId,String pileNo){
		try{
			ChargeBillingConfBo conf = obtainChargeBillingConf(stationId, null);
			chcbillSendService.issuredPile(conf.getChcNo(), pileNo,null);

		}catch(Exception e){
			logger.error("", e);
		}
	}

	public void newHandle(String stationId,String pileNo){
		try{
			ChargeBillingConfBo conf = obtainChargeBillingConf(stationId, null);
			xpStationSendService.issuredPile(conf.getChcNo(), pileNo,stationId,null);
		}catch(Exception e){
			logger.error("", e);
		}
	}
	
	/**
	 * 获取充电计费配置，判断是否充电计费配置有效,如果已失效，修改状态
	 * @param stationNo 充电站编号
	 * @param pileNo 充电桩编号
	 * @param endEftDate 生效日期 小于的时间点   格式：yyyy-MM-dd HH:mm
	 * @return ChargeBillingConfBo 当前充电计费版本
	 * */
	public ChargeBillingConfBo obtainChargeBillingConf(String stationId,String endEftDate){
		if(StringUtils.isEmpty(stationId))
			throw new ChargeBillingRltException("没有传入充电站id，无法获取充电计费配置");
		ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
		if(StringUtils.isEmpty(endEftDate))
			endEftDate=JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
		cbcCondition.setEndEftDate(endEftDate);
		cbcCondition.setChcStatus(ChargeConstant.validFlag.ENABLE);
		if(!StringUtils.isEmpty(stationId))
			cbcCondition.setStationId(stationId);
		//按照生效时间排序，取与当前时间节点最接近的一条返回
		List<ChargeBillingConfBo> cbcList=new ArrayList<ChargeBillingConfBo>();
		String chargeVesion=sysParamService.getSysParamsValues("chargeVesion");
		if("toMoreStation".equals(chargeVesion)){//新版计费
			cbcList = chargeBillingConfDao.queryNewChargeBillingConfs(cbcCondition);
		}else{
			cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
		}
		if(cbcList == null || cbcList.size() == 0)
			throw new ChargeBillingRltException("该充电站没有有效的充电计费配置");
		//将多余的充电计费配置状态改为无效
		if(cbcList.size()>1){
			for(int i = 1;i<cbcList.size();i++){
				ChargeBillingConfBo cbc = new ChargeBillingConfBo();
				cbc.setChcNo(cbcList.get(i).getChcNo());
				cbc.setChcStatus(ChargeConstant.validFlag.UNENABLE);
				chargeBillingConfDao.updateChargeBillingConf(cbc);
			}
		}
		return cbcList.get(0);
	}
}
