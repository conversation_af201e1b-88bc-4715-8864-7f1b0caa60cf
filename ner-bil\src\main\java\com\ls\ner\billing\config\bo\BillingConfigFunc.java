/**
 *
 * @(#) BillingConfigFunc.java
 * @Package com.ls.ner.billing.api.config.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.bo;


/**
 * 类描述：定价配置详细
 * 
 * @author: lipf
 * @version $Id: Exp$
 * 
 *          History: 2016年4月10日 下午1:51:01 lipf Created.
 * 
 */
public class BillingConfigFunc {
	private String billingNo;
	private String billingConfigName;
	private String subBe;
	private String subBeName;
	private String chargeWay;
	private String chargeWayName;
	private String chargeMode;
	private String chargeModeName;
	private String planNo;
	private String planName;
	private String autoModelNo;
	private String autoModelName;
	private String unionPrice;
	private String eftDate;
	private String applyDate;
	private String minLimitPrice;
	private String maxLimitPrice;
	private String createTime;
	private String content;
	private String isAdjusted;
	private String details;
	public String getBillingNo() {
		return billingNo;
	}

	public void setBillingNo(String billingNo) {
		this.billingNo = billingNo;
	}

	public String getBillingConfigName() {
		return billingConfigName;
	}

	public void setBillingConfigName(String billingConfigName) {
		this.billingConfigName = billingConfigName;
	}

	public String getSubBe() {
		return subBe;
	}

	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}

	public String getSubBeName() {
		return subBeName;
	}

	public void setSubBeName(String subBeName) {
		this.subBeName = subBeName;
	}

	public String getChargeWay() {
		return chargeWay;
	}

	public void setChargeWay(String chargeWay) {
		this.chargeWay = chargeWay;
	}

	public String getChargeWayName() {
		return chargeWayName;
	}

	public void setChargeWayName(String chargeWayName) {
		this.chargeWayName = chargeWayName;
	}

	public String getChargeMode() {
		return chargeMode;
	}

	public void setChargeMode(String chargeMode) {
		this.chargeMode = chargeMode;
	}

	public String getChargeModeName() {
		return chargeModeName;
	}

	public void setChargeModeName(String chargeModeName) {
		this.chargeModeName = chargeModeName;
	}

	public String getPlanNo() {
		return planNo;
	}

	public void setPlanNo(String planNo) {
		this.planNo = planNo;
	}

	public String getPlanName() {
		return planName;
	}

	public void setPlanName(String planName) {
		this.planName = planName;
	}

	public String getAutoModelNo() {
		return autoModelNo;
	}

	public void setAutoModelNo(String autoModelNo) {
		this.autoModelNo = autoModelNo;
	}

	public String getAutoModelName() {
		return autoModelName;
	}

	public void setAutoModelName(String autoModelName) {
		this.autoModelName = autoModelName;
	}

	public String getUnionPrice() {
		return unionPrice;
	}

	public void setUnionPrice(String unionPrice) {
		this.unionPrice = unionPrice;
	}

	public String getEftDate() {
		return eftDate;
	}

	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}

	public String getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(String applyDate) {
		this.applyDate = applyDate;
	}

	public String getMinLimitPrice() {
		return minLimitPrice;
	}

	public void setMinLimitPrice(String minLimitPrice) {
		this.minLimitPrice = minLimitPrice;
	}

	public String getMaxLimitPrice() {
		return maxLimitPrice;
	}

	public void setMaxLimitPrice(String maxLimitPrice) {
		this.maxLimitPrice = maxLimitPrice;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getIsAdjusted() {
		return isAdjusted;
	}

	public void setIsAdjusted(String isAdjusted) {
		this.isAdjusted = isAdjusted;
	}

	public String getDetails() {
		return details;
	}

	public void setDetails(String details) {
		this.details = details;
	}
}
