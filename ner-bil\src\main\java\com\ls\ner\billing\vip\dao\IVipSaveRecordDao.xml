<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.vip.dao.IVipSaveRecordDao">

    <resultMap id="VipSaveRecordResultMap" type="com.ls.ner.billing.vip.bo.VipSaveRecordBo">
        <id column="record_id" property="recordId"/>
        <result column="order_no" property="orderNo"/>
        <result column="mobile" property="mobile"/>
        <result column="cash_amt" property="cashAmt"/>
        <result column="discount_amt" property="discountAmt"/>
        <result column="vip_discount" property="vipDiscount"/>
        <result column="vip_discount_up" property="vipDiscountUp"/>
        <result column="pay_time" property="payTime" javaType="java.util.Date"/>
        <result column="pay_month" property="payMonth"/>
    </resultMap>

    <sql id="Base_Column_List">
        record_id
        ,
        order_no,
        mobile,
        cash_amt,
        discount_amt,
        vip_discount,
        vip_discount_up,
        pay_time,
        pay_month
    </sql>

    <select id="getSaveRecordByMobile" resultType="com.ls.ner.billing.api.vip.bo.VipSaveRecordRPCBo">
        SELECT
        pay_month payMonth,ifnull(sum(cash_amt),0) cashAmt,
        ifnull(sum(discount_amt),0) discountAmt,
        ifnull(sum(vip_discount),0) vipDiscount,
        ifnull(sum(vip_discount_up),0) vipDiscountUp,
        ifnull(sum(cash_amt),0) + ifnull(sum(discount_amt),0) + ifnull(sum(vip_discount),0)
        + ifnull(sum(vip_discount_up),0) totalSave
        FROM e_vip_save_record
        <where>
            mobile = #{mobile}
            <if test="startTime != null and startTime!=''">
                AND pay_month >= #{startTime}
            </if>
            <if test="endTime != null and endTime!=''">
                <![CDATA[AND pay_month <= #{endTime}]]>
            </if>
        </where>
        GROUP BY pay_month order by pay_month
    </select>

    <select id="getUserSaveRecord" resultType="com.ls.ner.billing.api.vip.bo.VipSaveRecordRPCBo">
        SELECT
        ifnull(sum(cash_amt),0) cashAmt,
        ifnull(sum(discount_amt),0) discountAmt,
        ifnull(sum(vip_discount),0) vipDiscount,
        ifnull(sum(vip_discount_up),0) vipDiscountUp
        FROM e_vip_save_record
        <where>
            mobile = #{mobile}
            <if test="startTime != null and startTime!=''">
                AND pay_month >= #{startTime}
            </if>
            <if test="endTime != null and endTime!=''">
                <![CDATA[AND pay_month <= #{endTime}]]>
            </if>
        </where>
    </select>

    <!-- SQL 插入：添加新记录 -->
    <insert id="insertVipSaveRecord" parameterType="com.ls.ner.billing.vip.bo.VipSaveRecordBo">
        INSERT INTO e_vip_save_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null and recordId != ''">
                record_id,
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no,
            </if>
            <if test="mobile != null and mobile != ''">
                mobile,
            </if>
            <if test="cashAmt != null">
                cash_amt,
            </if>
            <if test="discountAmt != null">
                discount_amt,
            </if>
            <if test="vipDiscount != null">
                vip_discount,
            </if>
            <if test="vipDiscountUp != null">
                vip_discount_up,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="payMonth != null and payMonth != ''">
                pay_month
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null and recordId != ''">
                #{recordId},
            </if>
            <if test="orderNo != null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="mobile != null and mobile != ''">
                #{mobile},
            </if>
            <if test="cashAmt != null">
                #{cashAmt},
            </if>
            <if test="discountAmt != null">
                #{discountAmt},
            </if>
            <if test="vipDiscount != null">
                #{vipDiscount},
            </if>
            <if test="vipDiscountUp != null">
                #{vipDiscountUp},
            </if>
            <if test="payTime != null">
                #{payTime},
            </if>
            <if test="payMonth != null and payMonth != ''">
                #{payMonth}
            </if>
        </trim>
    </insert>

    <!-- SQL 更新：根据主键更新记录 -->
    <update id="updateByOrder" parameterType="com.ls.ner.billing.vip.bo.VipSaveRecordBo">
        UPDATE e_vip_save_record
        <set>
            <if test="orderNo != null and orderNo != ''">
                order_no = #{orderNo},
            </if>
            <if test="mobile != null and mobile != ''">
                mobile = #{mobile},
            </if>
            <if test="cashAmt != null">
                cash_amt = #{cashAmt},
            </if>
            <if test="discountAmt != null">
                discount_amt = #{discountAmt},
            </if>
            <if test="vipDiscount != null">
                vip_discount = #{vipDiscount},
            </if>
            <if test="vipDiscountUp != null">
                vip_discount_up = #{vipDiscountUp},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="payMonth != null and payMonth != ''">
                pay_month = #{payMonth}
            </if>
        </set>
        WHERE order_no = #{orderNo}
    </update>

</mapper>