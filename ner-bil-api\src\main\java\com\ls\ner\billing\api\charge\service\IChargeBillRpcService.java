package com.ls.ner.billing.api.charge.service;

import com.ls.ner.billing.api.charge.bo.BillRltPeriodsBo;
import com.ls.ner.billing.api.charge.bo.ChargeBillConfigServiceModeBO;
import com.ls.ner.billing.api.charge.bo.ChargeBillStaionInfoBO;

import java.util.List;
import java.util.Map;

public interface IChargeBillRpcService {

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-23
	 * @description *******	RPC05-02-02查询充电定价
	 */
	public List<Map<String, Object>> getChargePrice(Map<String, Object> inMap) throws Exception;

	/**
	 * @description 充电价格描述，取服务费(最大值)+充电费(最大值)之和
	 * <AUTHOR>
	 * @create 2018-01-22 15:35:11
	 */
	public List<Map<String, Object>> getChargePriceRemark(Map<String, Object> inMap) throws Exception;

	/**
	 * @description 充电费、服务费、充电费描述 取尖峰谷平的时段的价格进行描述，如“00:00-06:00 1.0元/度”
	 * <AUTHOR>
	 * @create 2018-01-22 15:35:11
	 */
	public List<Map<String, Object>> getElecAmtRemark(Map<String, Object> inMap) throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-23
	 * @description *******	RPC05-02-25充电计费 – 预估
	 */
	public Map<String, Object> estimate(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-25
	 * @description *******	RPC05-02-24充电计费 – 预收/计费版本
	 */
	public Map<String, Object> payInAdvance(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2019-01-23
	 * @description 删除充电计费
	 */
	public Map<String, Object> deleteChargeBillingRlt(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2016-11-25
	 * @description *******	RPC05-02-26充电计费 – 结算
	 */
	public Map<String, Object> settlement(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2017-05-09
	 * @description 4充电计费 – 预收/计费版本 + 结算
	 */
	public Map<String, Object> payInAdvanceAndSettlement(Map<String, Object> inMap)throws Exception;

	/**
	 * <AUTHOR>
	 * @dateTime 2017-05-03
	 * @description 下发充电计费模型
	 */
	public void issuredStationOrPile(String stationId, String pileNo, String chcNo)throws Exception;

	/**
	 * @Description 下单时给单桩下发计费(站点计费)
	 * <AUTHOR>
	 * @Date 2022/7/16 15:20
	 * @param stationId
	 * @param pileNo
	 */
	void issuredStationBillSend(String stationId, String pileNo);

	/**
	  *@Description: 查询结算分段计费明细
	  *@Author: qianghuang
	  *@Time: 2017/8/18 15:09
	  */
	List<BillRltPeriodsBo> getChargeBillRltPeriod(Map map);

	/**
	 *@Description: 查询结算分段计费明细(简单单表查询)
	 *@Author: hdf
	 *@Time: 2022/07/11 15:09
	 */
	List<BillRltPeriodsBo> getChargeBillRltPeriodByOrder(Map map);

	/**
	 *@Description: 查询station下发的计费模型
	 *@Author: laibihui
	 *@Time: 2017/9/26 10:09
	 */
	public Map<String, Object> getStationChargePeriods(Map map);

	/**
	 * @param inMap
	 * @description 获取服务费项维护
	 * <AUTHOR>
	 * @create 2017-10-23 15:04:36
	 */
	public List<Map<String,Object>> getAppendCharItem(Map<String, Object> inMap);

	/**
	 *获取计费配置
	 * @param inMap ====={stationIds:[]}
	 * @return  list
	 */
	public List<Map> queryChargeBillingConfs(Map<String, Object> inMap);

	/**
	 * 新增站点计费配置
	 * @param inMap==={stationId:"12344",chcNo:"12345",orgCode:"112344"}
	 */
	public  void insertChargeBilling(Map<String, Object> inMap);

	/**
	 * 修改站点计费配置
	 * @param inMap==={stationId:"12344",chcNo:"12345",orgCode:"112344"}
	 */
	public  void updateChargeBilling(Map<String, Object> inMap);

	/**
	 * 删除站点计费配置
	 * @param inMap==={stationId:"12344"}
	 */
	public  void delChargeBilling(Map<String, Object> inMap);

	/**
	 * @description 充电费、服务费、充电费描述 取尖峰谷平的时段的价格进行描述，如“00:00-06:00 1.0元/度”
	 * <AUTHOR>
	 * @create 2018-01-22 15:35:11  inMap=={chcNo:"xxxxx"}
	 */
	public List<Map<String, Object>> getNewElecAmtRemark(Map<String, Object> inMap) throws Exception;

	/**
	 *获取所有计费配置
	 * @param inMap
	 * @return  list
	 */
	public List<Map>  queryChargeBillingList(Map<String, Object> inMap);

	/**
	 * 单个桩的计费配置
	 */
	Map queryPileBillingList(String pileId);

	/**
	 * @param searchMap
	 * @description 查询当前充电价格描述 充电费+服务费
	 * <AUTHOR>
	 * @create 2018-05-21 17:32:28
	 */
	List<Map<String,Object>> getCurChargePriceRemark(Map<String, Object> searchMap) throws Exception;

	/**
	 * @param inMap
	 * @description 个性化计费查询-小鹏使用
	 * <AUTHOR>
	 * @create 2018-07-25 17:15:22
	 */
	List<Map<String,Object>> getXpChargePriceRemark(Map<String, Object> inMap) throws Exception;

	/**
	 * @param inMap {orderNo:xxx}
	 * @description 查询订单充电费用项
	 * <AUTHOR>
	 * @create 2018-08-02 23:19:23
	 * return {"1000002002":{"price":"1.2","unit":"分钟","iteamNo":"1000002002","freeNum","0.2"},{},{}}
	 */
	public Map<String, Object> qryOrderChargeIteams(Map<String, Object> inMap)throws Exception;


	/**
	 * 新增大客户计费配置
	 * @param inMap==={groupId:"12344",chcNo:"12345"}
	 */
	public  void insertCustGroupChargeBilling(Map<String, Object> inMap);

	/**
	 * 删除大客户计费配置
	 * @param inMap==={groupId:"12344"}
	 */
	public  void delCustGroupChargeBilling(Map<String, Object> inMap);

	/**
	 *获取大客户计费配置
	 * @param inMap
	 * @return  list
	 */
	public List<Map>  queryCustGroupChargeBilling(Map<String, Object> inMap);


	/**
	 * @param inMap {orderNo:xxxx,bilType:1}  bilType 0-实时计费  1-结算计费
	 * @description 查询订单计费信息
	 * <AUTHOR>
	 * @create 2019-04-24 21:14:40
	 */
	public Map<String, Object> qryOrderBillInfo(Map<String, Object> inMap)throws Exception;

	/**
	 * @param inMap
	 * @description 通过订单查询是否是企业计费 0-否 1-是
	 * <AUTHOR>
	 * @create 2019-05-16 16:48:30
	 */
	public Boolean qryOrderBillConfFlag(Map<String, Object> inMap);
	/**
	 * @param iMap
	 * @description
	 * <AUTHOR> 更新站点下面计费的管理单位
	 * @create 2019-05-27 12:20:44
	 */
	public void updateChargeBillingOrgCode(Map<String, Object> iMap);

	/**
	 * @param inMap
	 * @description 查询有效计费  按照 custId>pCustId>groupId>stationId 顺序
	 * <AUTHOR> (songyuhang搬运)
	 * @create 2019-08-22 17:16:41
	 */
    Map getChargConfig(Map inMap);

	/**
	 * @param iMap
	 * @description  查询服务项是否分时
	 * <AUTHOR>
	 * @create 2019-10-06 17:47:46
	 */
	String queryItemMode(Map<String,Object> iMap);

	/**
	 * @param inMap
	 * @description 查询订单分时电量
	 * <AUTHOR>
	 * @create 2019-07-08 19:13:29
	 */
	List<Map<String, Object>> getChargePeriodsItemsPq(Map inMap);

	/**
	 * @Description 通过订单编号查询计费分时明细
	 * <AUTHOR>
	 * @Date 2022/7/4 17:46
	 * @param orderNo
	 */
	List<BillRltPeriodsBo> getChargePeriodsByOrderNo(String orderNo);

	/**
	 * @Description 通过用户id查询企业计费
	 * <AUTHOR>
	 * @Date 2023/7/26 15:18
	 * @param: searchMap
	 */
	List<Map<String,Object>> getUserChargePriceRemarkByCustId(String custId, String stationId) throws Exception;

	ChargeBillConfigServiceModeBO queryServiceMode(String appNo);

	ChargeBillStaionInfoBO queryChargePriceHistory(String stationId,String sendTime);
}
