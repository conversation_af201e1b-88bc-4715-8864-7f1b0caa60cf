package com.ls.ner.billing.gift.bo;

import com.pt.poseidon.api.framework.DicAttribute;

/**
 * <AUTHOR>
 * @description CarGiftBo 宁德购车礼券相关信
 * @create 2020-04-29 17:21
 */
public class CarGiftBo {
    /** 主键id **/
    private String giftId;
    /** 手机号 **/
    private String mobile;
    /** 身份证号 **/
    private String certNo;
    /** 支付宝账号 **/
    private String alipayNo;
    /** 车牌号 **/
    private String licenseNo;
    /** 是否营运车辆0否1是 **/
    private String carType;
    /** 车架号 **/
    private String vin;
    /** 礼金金额，营运5000，非营运车辆4000 **/
    private String giftAmt;
    /** 领取状态：01未申请02待审核03审核通过04发放失败05已发放06已核销（审核失败变成未申请状态） **/
    private String giftStatus;
    @DicAttribute(dicName = "codeDict", key = "giftStatus", subType =
            "carGiftGetStatus")
    private String giftStatusName;
    /** 失败原因，审核失败或者发放失败都写入此字段 **/
    private String failReason;
    /** 申请时间 **/
    private String applyTime;
    /** 审核时间 **/
    private String auditTime;
    /** 领取时间 **/
    private String getTime;
    /** 购车时间 **/
    private String buyTime;
    /** 购车地点4s **/
    private String buyAddr;
    /** 购买车型 **/
    private String buyModel;
    /** 购车人支付宝账号姓名 **/
    private String buyerName;

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getGiftStatusName() {
        return giftStatusName;
    }

    public void setGiftStatusName(String giftStatusName) {
        this.giftStatusName = giftStatusName;
    }

    public String getGiftId() {
        return giftId;
    }

    public void setGiftId(String giftId) {
        this.giftId = giftId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getAlipayNo() {
        return alipayNo;
    }

    public void setAlipayNo(String alipayNo) {
        this.alipayNo = alipayNo;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getGiftAmt() {
        return giftAmt;
    }

    public void setGiftAmt(String giftAmt) {
        this.giftAmt = giftAmt;
    }

    public String getGiftStatus() {
        return giftStatus;
    }

    public void setGiftStatus(String giftStatus) {
        this.giftStatus = giftStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getGetTime() {
        return getTime;
    }

    public void setGetTime(String getTime) {
        this.getTime = getTime;
    }

    public String getBuyTime() {
        return buyTime;
    }

    public void setBuyTime(String buyTime) {
        this.buyTime = buyTime;
    }

    public String getBuyAddr() {
        return buyAddr;
    }

    public void setBuyAddr(String buyAddr) {
        this.buyAddr = buyAddr;
    }

    public String getBuyModel() {
        return buyModel;
    }

    public void setBuyModel(String buyModel) {
        this.buyModel = buyModel;
    }
}
