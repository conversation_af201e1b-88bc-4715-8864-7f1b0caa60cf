<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ls.ner.bil</groupId>
		<artifactId>ner-bil-boot</artifactId>
		<version>0.0.1</version>
	</parent>
	<artifactId>ner-bil-web</artifactId>
	<name>ner-bil-web</name>
	<url>http://maven.apache.org</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.ls.ner.bil</groupId>
			<artifactId>ner-bil</artifactId>
    		<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.ls.ner.ast</groupId>
			<artifactId>ner-ast-api</artifactId>
			<version>0.0.1</version>
		</dependency>
        <dependency>
            <groupId>com.ls.ner.cust</groupId>
            <artifactId>ner-cust-api</artifactId>
            <version>0.0.1</version>
        </dependency>
    </dependencies>

	<build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/webapp/thymeleaf</directory>
            </resource>
        </resources>
        <plugins>
             <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>-->
			<!--<plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>-->

			<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
				<!--<version>2.6</version>-->
				<configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.ls.ner.billing.NerBootstrapApplication</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>nerlib/</classpathPrefix>
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
				<!--<version>3.0.0</version>-->
				<!--<version>2.2</version>-->
				<executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/nerlib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
                    <!-- 跳过单元测试 -->
					<skipTests>true</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
        </plugins>
    </build>
</project>