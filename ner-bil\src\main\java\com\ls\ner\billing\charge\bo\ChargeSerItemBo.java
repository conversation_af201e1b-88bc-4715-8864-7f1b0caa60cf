package com.ls.ner.billing.charge.bo;

import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.DicAttribute;

/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 追加收费项目 E_APPEND_CHARGE_ITEM
 */
public class ChargeSerItemBo {
	private String itemNo;//收费项目编号
	private String itemName;//收费项目名称
	private String pBe;//业务大类：01租车 02 充电
	private String itemType;//项目类型：01服务02押金
	private String itemUnit;//项目单位 0100 次 0101 分钟 0102 小时 0103 日 0104 米 0105 公里 0200 次 0201 kWh
	private String remarks;//描述
	private String buyType;//是否必购，01选购、02必购
	private String sn;//排序SN
	private String itemStatus;//状态，1启用、0停用
	private String servicePrice;//收费项目单价
	private String freeNum;//免费段，逗号隔开，空或是0表示没有免费段
	private String systemId;//主键
	private String itemChargeMode;//费用分时标识
	private String itemRate;//费率

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	@DicAttribute(dicName = "codeDict", key = "itemUnit", subType = BizConstants.CodeType.CHC_BILLING_PRICE_UNIT)
	private String itemUnitName;//项目单位名称
	@DicAttribute(dicName = "codeDict", key = "buyType", subType = BizConstants.CodeType.BILLING_APPEND_ITEM_BUY_TYPE)
	private String buyTypeName;//是否必购名称

	@DicAttribute(dicName = "codeDict", key = "itemStatus", subType ="itemStatus")
	private String itemStatusName;//服务费状态

	public String getFreeNum() {
		return freeNum;
	}

	public void setFreeNum(String freeNum) {
		this.freeNum = freeNum;
	}

	public String getItemNo() {
		return itemNo;
	}
	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}
	public String getItemName() {
		return itemName;
	}
	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getpBe() {
		return pBe;
	}
	public void setpBe(String pBe) {
		this.pBe = pBe;
	}
	public String getItemType() {
		return itemType;
	}
	public void setItemType(String itemType) {
		this.itemType = itemType;
	}
	public String getItemUnit() {
		return itemUnit;
	}
	public void setItemUnit(String itemUnit) {
		this.itemUnit = itemUnit;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getBuyType() {
		return buyType;
	}
	public void setBuyType(String buyType) {
		this.buyType = buyType;
	}
	public String getSn() {
		return sn;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}
	public String getItemStatus() {
		return itemStatus;
	}
	public void setItemStatus(String itemStatus) {
		this.itemStatus = itemStatus;
	}
	public String getServicePrice() {
		return servicePrice;
	}
	public void setServicePrice(String servicePrice) {
		this.servicePrice = servicePrice;
	}
	public String getItemUnitName() {
		return itemUnitName;
	}
	public void setItemUnitName(String itemUnitName) {
		this.itemUnitName = itemUnitName;
	}
	public String getBuyTypeName() {
		return buyTypeName;
	}
	public void setBuyTypeName(String buyTypeName) {
		this.buyTypeName = buyTypeName;
	}
	public String getItemStatusName() {
		return itemStatusName;
	}
	public void setItemStatusName(String itemStatusName) {
		this.itemStatusName = itemStatusName;
	}
	public void notNull(){
		notNull(itemNo,itemName,pBe,itemType,itemUnit,remarks,buyType,sn,itemStatus,servicePrice,itemUnitName,buyTypeName,itemStatusName);
	}
	private void notNull(String ... str){
		for(String s:str){
			if(s == null)
				s = "";
		}
	}
	
	public String getItemUnitValueName(){
		String value = "";
		switch (itemUnit) {
		case "0100":
			value = "次";
			break;
		case "0101":
			value = "分钟";
			break;
		case "0102":
			value = "小时";
			break;
		case "0103":
			value = "日";
			break;
		case "0104":
			value = "米";
			break;
		case "0105":
			value = "公里";
			break;
		case "0200":
			value = "次";
			break;
		case "0201":
			value = "度";
			break;
		case "0202":
			value = "小时";
			break;
		case "0203":
			value = "半小时";
			break;
		case "0204":
			value = "分钟";
			break;
		default:
			break;
		}
		return value;
	}

	public String getItemChargeMode() {
		return itemChargeMode;
	}

	public void setItemChargeMode(String itemChargeMode) {
		this.itemChargeMode = itemChargeMode;
	}

	public String getItemRate() {
		return itemRate;
	}

	public void setItemRate(String itemRate) {
		this.itemRate = itemRate;
	}
}