package com.ls.ner.billing.market.bo;/**
 * Created by Administrator on 2017-06-04.
 */

import java.util.Date;

/**
*描述:发放
*CouponPutBo.java
*作者：biaoxiangd
*创建日期：2017-06-07 18:15
**/
public class CouponPutBo {
    private String putId; // 发放ID
    private String cpnNo;
    private String cpnId; // 优惠券ID
    private String putEmpType;// 发放人员类型，01系统人员 02客户
    private String putEmp;// 发放人员
    private String custId; // 可发放客户标识
    private String putTime; // 发放时间
    private String putNum; // 发放数量
    private String cpnNum; // 发行数量
    private String alrdyGetNum; // 已领数量
    private String noticeType;// 通知方式noticeType，01无 02短信
    private String noticeTypeName;
    private String noticeCont;// 通知内容

    private String custInfoList;// 客户集合
    private String custName;// 客户名称
    private String mobile; // 手机号码
    private String dateType;
    private String eftDate; // 生效日期
    private String invDate;// 失效日期
    private String eftLen;
    private String limGetNum; // 每人限领数量
    private String putChannel; // 发放渠道，channel
    private String putChannelName;// 发放渠道名称

    private String custSortCode;
    private String custSortCodeName;

    private String timeUnit; // 生效时间偏移单位,日、月等
    private String cpnTimeType;// 优惠券生效时间类型，1绝对日期 2相对日期
    private String timeDuration;//  // 生效时间偏移量，相对日期时使用

    private String putFlag; // 发放详情成功标志
    private String failReason; // 发送详情错误描述
    private String getChannel; // 获取来源（putChannel）

    private String busiType; // 优惠内容 01租车02充电

    private Boolean isAllCust=false;//是否全选用户

    private Date delayPutTime;//延迟执行时间

    private Integer isDelay =0; //1 是延迟，0 是立刻执行

    public Integer getIsDelay() {
        return isDelay;
    }

    public void setIsDelay(Integer isDelay) {
        this.isDelay = isDelay;
    }

    public Date getDelayPutTime() {
        return delayPutTime;
    }

    public void setDelayPutTime(Date delayPutTime) {
        this.delayPutTime = delayPutTime;
    }

    public Boolean getIsAllCust() {
        return isAllCust;
    }

    public void setIsAllCust(Boolean allCust) {
        isAllCust = allCust;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getGetChannel() {
        return getChannel;
    }

    public void setGetChannel(String getChannel) {
        this.getChannel = getChannel;
    }

    public String getPutFlag() {
        return putFlag;
    }

    public void setPutFlag(String putFlag) {
        this.putFlag = putFlag;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getNoticeTypeName() {
        return noticeTypeName;
    }

    public void setNoticeTypeName(String noticeTypeName) {
        this.noticeTypeName = noticeTypeName;
    }

    public String getCustSortCode() {
        return custSortCode;
    }

    public void setCustSortCode(String custSortCode) {
        this.custSortCode = custSortCode;
    }

    public String getCustSortCodeName() {
        return custSortCodeName;
    }

    public void setCustSortCodeName(String custSortCodeName) {
        this.custSortCodeName = custSortCodeName;
    }

    public String getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(String timeUnit) {
        this.timeUnit = timeUnit;
    }

    public String getCpnTimeType() {
        return cpnTimeType;
    }

    public void setCpnTimeType(String cpnTimeType) {
        this.cpnTimeType = cpnTimeType;
    }

    public String getTimeDuration() {
        return timeDuration;
    }

    public void setTimeDuration(String timeDuration) {
        this.timeDuration = timeDuration;
    }

    public String getPutId() {
        return putId;
    }

    public void setPutId(String putId) {
        this.putId = putId;
    }

    public String getCpnNo() {
        return cpnNo;
    }

    public void setCpnNo(String cpnNo) {
        this.cpnNo = cpnNo;
    }

    public String getCpnId() {
        return cpnId;
    }

    public void setCpnId(String cpnId) {
        this.cpnId = cpnId;
    }

    public String getPutEmpType() {
        return putEmpType;
    }

    public void setPutEmpType(String putEmpType) {
        this.putEmpType = putEmpType;
    }

    public String getPutEmp() {
        return putEmp;
    }

    public void setPutEmp(String putEmp) {
        this.putEmp = putEmp;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getPutTime() {
        return putTime;
    }

    public void setPutTime(String putTime) {
        this.putTime = putTime;
    }

    public String getPutNum() {
        return putNum;
    }

    public void setPutNum(String putNum) {
        this.putNum = putNum;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeCont() {
        return noticeCont;
    }

    public void setNoticeCont(String noticeCont) {
        this.noticeCont = noticeCont;
    }

    public String getCustInfoList() {
        return custInfoList;
    }

    public void setCustInfoList(String custInfoList) {
        this.custInfoList = custInfoList;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getEftLen() {
        return eftLen;
    }

    public void setEftLen(String eftLen) {
        this.eftLen = eftLen;
    }

    public String getLimGetNum() {
        return limGetNum;
    }

    public void setLimGetNum(String limGetNum) {
        this.limGetNum = limGetNum;
    }

    public String getPutChannel() {
        return putChannel;
    }

    public void setPutChannel(String putChannel) {
        this.putChannel = putChannel;
    }

    public String getPutChannelName() {
        return putChannelName;
    }

    public void setPutChannelName(String putChannelName) {
        this.putChannelName = putChannelName;
    }

    public String getCpnNum() {
        return cpnNum;
    }

    public void setCpnNum(String cpnNum) {
        this.cpnNum = cpnNum;
    }

    public String getAlrdyGetNum() {
        return alrdyGetNum;
    }

    public void setAlrdyGetNum(String alrdyGetNum) {
        this.alrdyGetNum = alrdyGetNum;
    }
}
