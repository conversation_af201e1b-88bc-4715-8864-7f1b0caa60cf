package com.ls.ner.billing.api.rent.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ls.ner.billing.api.rent.model.IChargeItem;
import com.ls.ner.billing.api.rent.model.PriceUnit;

/**
 * 带价格的抽象计费项
 * <AUTHOR>
 *
 */
public abstract class AbstractChargeItem implements IChargeItem,Cloneable,Serializable {


    private static final long serialVersionUID = 1715472018181520638L;
    /**
	 * 计价单位
	 */
    private PriceUnit priceUnit;
	
	/**
	 * 定价之后才存在 价格
	 */
	private String price;
	
	/**
	 * 结合实际运行数据之后才存在 计价结果
	 */
	private BigDecimal result;

	/**
	 * 结合实际运行数据之后才存在 实际计费数量（因为存在最大最小计费数量，可能实际计算费用的数量跟原先数量不等）
	 */
	private long actAmount;
	
	/**
	 * 结合实际运行数据之后才存在 原本计费数量 （因为存在最大最小计费数量）
	 */
	private long originalAmount;
	
	public PriceUnit getPriceUnit() {
		return priceUnit;
	}

	public void setPriceUnit(PriceUnit priceUnit) {
		this.priceUnit = priceUnit;
	}

	

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public BigDecimal getResult() {
		return result;
	}

	public void setResult(BigDecimal result) {
		this.result = result;
	}
	

	public long getActAmount() {
		return actAmount;
	}

	public void setActAmount(long actAmount) {
		this.actAmount = actAmount;
	}

	public long getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(long originalAmount) {
		this.originalAmount = originalAmount;
	}

	@Override
	public void clearPrice() {
		this.setPrice(null);
		
	}

	@Override
	public void clearResult() {
		this.setResult(null);
		
	}

	@Override
	protected Object clone() throws CloneNotSupportedException {
		// TODO Auto-generated method stub
		return super.clone();
	}

	public String toString(){
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	
}
