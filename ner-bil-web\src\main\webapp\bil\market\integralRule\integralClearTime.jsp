<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<%
    String pubPath = (String) request.getAttribute("pubPath");
    if (pubPath == null) {
        pubPath = request.getContextPath(); // 给pubPath一个默认值，防止未设置时报错
    }
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="设置积分清零时间" />
<script type="text/javascript" src="<%=pubPath %>/pub/validate/validation.js"></script>
<script src="<%=request.getContextPath()%>/bil/comm/js/DateUtils.js" type="text/javascript"></script>

<ls:body>
    <ls:form name="clearTimeForm" id="clearTimeForm">
        <ls:text name="oldClearTime" property="oldClearTime" visible="false"></ls:text>
        <ls:layout-use>
            <ls:layout-put into="container">
                <table align="center" class="tab_search">
                    <colgroup>
                        <col width="20%" />
                        <col width="80%" />
                    </colgroup>
                    <tr>
                        <td>
                            <ls:label text="当前清零时间" />
                        </td>
                        <td>
                            <ls:text name="currentClearTime" property="currentClearTime" readOnly="true" style="color: #666;"></ls:text>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <ls:label text="新清零时间" ref="clearTime"/>
                        </td>
                        <td>
                            <ls:date name="clearTime" property="clearTime" format="yyyy-MM-dd HH:mm:ss"
                                     displayTime="true" required="true"></ls:date>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div style="color: #999; font-size: 12px; margin-top: 10px;">
                                说明：设置积分清零时间后，系统将在指定时间自动清零所有用户积分。
                            </div>
                        </td>
                    </tr>
                </table>
            </ls:layout-put>
            <ls:layout-put into="footer">
                <div class="pull-right">
                    <ls:button text="保存" onclick="save"></ls:button>
                    <ls:button text="取消" onclick="cancel"></ls:button>
                </div>
            </ls:layout-put>
        </ls:layout-use>
    </ls:form>

    <ls:script>
        window.onload = function(){
            console.info(2222, oldClearTime.getValue())
            currentClearTime.setValue(oldClearTime.getValue());
        }

        function save(){
            console.log("save called"); // 调试保存按钮

            var clearTimeValue = clearTime.getValue();
            if(!clearTimeValue){
                LS.message("error", "请选择清零时间!");
                return;
            }

            // 验证时间格式和有效性
            var now = new Date();
            var selectedTime = new Date(clearTimeValue);

            if(selectedTime <= now){
                LS.message("error", "清零时间必须大于当前时间!");
                return;
            }

            var params = {
                clearTime: clearTimeValue
            };

            LS.ajax("~/billing/integralRule/saveClearTime", params, function(){
                LS.parent().query();
                LS.window.close();
            });
        }

        function cancel(){
            LS.window.close();
        }
    </ls:script>
</ls:body>
</html>
