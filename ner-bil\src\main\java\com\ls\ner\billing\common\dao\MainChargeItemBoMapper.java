package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.MainChargeItemBo;
import com.ls.ner.billing.common.bo.MainChargeItemBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface MainChargeItemBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(MainChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(MainChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(MainChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(MainChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<MainChargeItemBo> selectByExample(MainChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    MainChargeItemBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") MainChargeItemBo record, @Param("example") MainChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") MainChargeItemBo record, @Param("example") MainChargeItemBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(MainChargeItemBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_main_charge_item
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(MainChargeItemBo record);
}