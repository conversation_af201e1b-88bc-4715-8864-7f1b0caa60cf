package com.ls.ner.billing.api.common.bo;

import java.math.BigDecimal;
import java.util.Date;

public class BillingOrderRelaBo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String appNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String subBe;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String orgAutoModelKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.VERSION_LIMIT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String versionLimit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.DEFAULT_BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String defaultBillingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.SELECTED_ATTACH_ITEM_NOS
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String selectedAttachItemNos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private String dateOperType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_billing_order_rela.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    private BigDecimal pricingAmt;
    
    private String billType;
    
    private String relaNo;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.SYSTEM_ID
     *
     * @return the value of e_billing_order_rela.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.SYSTEM_ID
     *
     * @param systemId the value for e_billing_order_rela.SYSTEM_ID
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.APP_NO
     *
     * @return the value of e_billing_order_rela.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getAppNo() {
        return appNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.APP_NO
     *
     * @param appNo the value for e_billing_order_rela.APP_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setAppNo(String appNo) {
        this.appNo = appNo == null ? null : appNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.SUB_BE
     *
     * @return the value of e_billing_order_rela.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getSubBe() {
        return subBe;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.SUB_BE
     *
     * @param subBe the value for e_billing_order_rela.SUB_BE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setSubBe(String subBe) {
        this.subBe = subBe == null ? null : subBe.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.ORG_AUTO_MODEL_KEY
     *
     * @return the value of e_billing_order_rela.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getOrgAutoModelKey() {
        return orgAutoModelKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.ORG_AUTO_MODEL_KEY
     *
     * @param orgAutoModelKey the value for e_billing_order_rela.ORG_AUTO_MODEL_KEY
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setOrgAutoModelKey(String orgAutoModelKey) {
        this.orgAutoModelKey = orgAutoModelKey == null ? null : orgAutoModelKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.VERSION_LIMIT
     *
     * @return the value of e_billing_order_rela.VERSION_LIMIT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getVersionLimit() {
        return versionLimit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.VERSION_LIMIT
     *
     * @param versionLimit the value for e_billing_order_rela.VERSION_LIMIT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setVersionLimit(String versionLimit) {
        this.versionLimit = versionLimit == null ? null : versionLimit.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.DEFAULT_BILLING_NO
     *
     * @return the value of e_billing_order_rela.DEFAULT_BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getDefaultBillingNo() {
        return defaultBillingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.DEFAULT_BILLING_NO
     *
     * @param defaultBillingNo the value for e_billing_order_rela.DEFAULT_BILLING_NO
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setDefaultBillingNo(String defaultBillingNo) {
        this.defaultBillingNo = defaultBillingNo == null ? null : defaultBillingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.SELECTED_ATTACH_ITEM_NOS
     *
     * @return the value of e_billing_order_rela.SELECTED_ATTACH_ITEM_NOS
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getSelectedAttachItemNos() {
        return selectedAttachItemNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.SELECTED_ATTACH_ITEM_NOS
     *
     * @param selectedAttachItemNos the value for e_billing_order_rela.SELECTED_ATTACH_ITEM_NOS
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setSelectedAttachItemNos(String selectedAttachItemNos) {
        this.selectedAttachItemNos = selectedAttachItemNos == null ? null : selectedAttachItemNos.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.DATE_OPER_TYPE
     *
     * @return the value of e_billing_order_rela.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public String getDateOperType() {
        return dateOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.DATE_OPER_TYPE
     *
     * @param dateOperType the value for e_billing_order_rela.DATE_OPER_TYPE
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setDateOperType(String dateOperType) {
        this.dateOperType = dateOperType == null ? null : dateOperType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.DATA_OPER_TIME
     *
     * @return the value of e_billing_order_rela.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_billing_order_rela.DATA_OPER_TIME
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_billing_order_rela.PRICING_AMT
     *
     * @return the value of e_billing_order_rela.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public BigDecimal getPricingAmt() {
        return pricingAmt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_billing_order_rela.PRICING_AMT
     *
     * @param pricingAmt the value for e_billing_order_rela.PRICING_AMT
     *
     * @mbggenerated Wed Mar 16 15:59:45 CST 2016
     */
    public void setPricingAmt(BigDecimal pricingAmt) {
        this.pricingAmt = pricingAmt;
    }

	public String getBillType() {
		return billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	public String getRelaNo() {
		return relaNo;
	}

	public void setRelaNo(String relaNo) {
		this.relaNo = relaNo;
	}
}