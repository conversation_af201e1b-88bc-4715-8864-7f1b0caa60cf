package com.ls.ner.billing.charge.dao;

import java.util.List;

import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;

/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 追加收费项目dao
 */
public interface IChargeSerItemDao {
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public List<ChargeSerItemBo> queryChargeSerItems(ChargeSerItemQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询刷卡费用条数C_CAR_PAY
	 */
	public int queryChargeSerItemsNum(ChargeSerItemQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 根据itemName查询追加收费项目条数E_APPEND_CHARGE_ITEM
	 */
	public int queryChargeSerItemsNumByName(ChargeSerItemQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目最大的sn值 E_APPEND_CHARGE_ITEM 
	 */
	public int queryChargeSerMaxSn(ChargeSerItemQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增追加收费项目E_APPEND_CHARGE_ITEM 
	 */
	public void insertChargeSerItem(ChargeSerItemBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public void updateChargeSerItem(ChargeSerItemBo bo);
	
}
