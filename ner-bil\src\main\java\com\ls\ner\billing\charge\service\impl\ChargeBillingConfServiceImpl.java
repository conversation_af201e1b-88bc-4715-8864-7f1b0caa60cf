package com.ls.ner.billing.charge.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.mq.MqConstants;
import com.ls.ner.base.mq.ProducerHelper;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.xpcharge.dao.IXpChargeBillingConfDao;
import com.ls.ner.billing.xpcharge.service.IXpChargeBillingConfService;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.billing.xpcharge.service.IXpPileSendService;
import com.ls.ner.billing.xpcharge.service.IXpStationSendService;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.MergeUtil;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.bo.ChcbillSendBo;
import com.ls.ner.billing.charge.dao.IChargeBillingConfDao;
import com.ls.ner.billing.charge.dao.IChargePeriodsDao;
import com.ls.ner.billing.charge.dao.IChcbillPileSendDao;
import com.ls.ner.billing.charge.dao.IChcbillPileSendLogDao;
import com.ls.ner.billing.charge.dao.IChcbillSendDao;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.ls.ner.billing.charge.ChargeConstant.itemTypeNo;
/**
 * 充电计费配置
 * <AUTHOR>
 */
@Service(target = {ServiceType.APPLICATION}, value = "chargeBillingConfService")
public class ChargeBillingConfServiceImpl implements IChargeBillingConfService {
	private static final Logger logger = LoggerFactory.getLogger(ChargeBillingConfServiceImpl.class);

	@Autowired
	private IChargeBillingConfDao chargeBillingConfDao;

	@Autowired
	private IChargePeriodsDao chargePeriodsDao;
	
	@Autowired
	private IChcbillSendDao chcbillSendDao;

	@Autowired
	private IChcbillPileSendLogDao chcbillPileSendLogDao;

	@Autowired
	private IChcbillPileSendDao chcbillPileSendDao;

	@Autowired
	private IXpChargeBillingConfDao xpChargeBillingConfDao;
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	@ServiceAutowired
	Authentication authentication;

	@ServiceAutowired("xpChargeBillingConfService")
	private IXpChargeBillingConfService xpChargeBillingConfService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired("xpPileSendService")
	private IXpPileSendService xpPileSendService;

	@ServiceAutowired("xpStationSendService")
	private IXpStationSendService xpStationSendService;

	@ServiceAutowired("peakService")
	private IXpPeakService peakService;


	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置E_CHARGE_BILLING_CONF
	 */
	public List<ChargeBillingConfBo> queryChargeBillingConfs(ChargeBillingConfQueryCondition bo){
		return chargeBillingConfDao.queryChargeBillingConfs(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF 
	 */
	public int queryChargeBillingConfsNum(ChargeBillingConfQueryCondition bo){
		return chargeBillingConfDao.queryChargeBillingConfsNum(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public String insertChargeBillingConf(ChargeBillingConfBo bo){
		String chcNo = seqRpcService.getDefNo();
		String custId = bo.getCustId();
		bo.setChcNo(chcNo);
		//如果“费控模式=本地”，gatherDataType=03量价费
		if(ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode())){
			bo.setGatherDataType(ChargeConstant.gatherDataType.PRICE);
		}
		if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(bo.getChargeMode())){//如果分时
			String chargePeriodsStr = bo.getChargePeriodsStr();//分时数据
			Gson gson=new Gson();
			List<ChargePeriodsBo> cpBos =gson.fromJson(chargePeriodsStr, new TypeToken<List<ChargePeriodsBo>>(){}.getType());
			bo.setPeriodSplitNum(String.valueOf(cpBos.size()));//分时阶段数
			//电费分时
			for(int i = 0;i < cpBos.size(); i++){ //电费
				ChargePeriodsBo cpBo = cpBos.get(i);
				cpBo.setSn(i+1+"");
				cpBo.setChcNo(bo.getChcNo());
				cpBo.setItemNo(itemTypeNo.CHARGE);
				chargePeriodsDao.insertChargePeriods(cpBo);
			}

			//其他费用项分时
			StringBuilder attchIteamPriceSb = new StringBuilder();
			String attachItemNos = StringUtil.isBlank(bo.getAttachItemNos())?null:bo.getAttachItemNos();
			String itemChargeModes = StringUtil.isBlank(bo.getItemChargeMode())?null:bo.getItemChargeMode();
			String itemPrices [] = bo.getAttachItemPrices().split(",");

			String attachNoAry[] = new String[]{};
			if (attachItemNos != null){
				attachNoAry = attachItemNos.split(",");
			}

			String[] iteamChargeModeArr = new String[]{};
			if(itemChargeModes == null && attachNoAry.length > 0){
				//兼容旧版计费
				StringBuffer iteamChargeModeStr = new StringBuffer();
				iteamChargeModeArr = new String[attachNoAry.length];
				for (int i = 0 ;i < attachNoAry.length;i++){
					iteamChargeModeArr[i] = "0201";
					iteamChargeModeStr.append("0201,");
				}
				//截取最后一个逗号
				bo.setItemChargeMode(iteamChargeModeStr.substring(0,iteamChargeModeStr.length() - 1).toString());
			}else if (itemChargeModes != null){
				logger.debug("itemChargeModes:"+itemChargeModes);
				iteamChargeModeArr = itemChargeModes.split(",");
			}

			for(int k = 0;k < attachNoAry.length; k++){
				if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(iteamChargeModeArr[k])){
					for(int i = 0;i < cpBos.size(); i++){//
						ChargePeriodsBo cpBo = cpBos.get(i);
						cpBo.setSn(i+1+"");
						cpBo.setChcNo(bo.getChcNo());
						cpBo.setItemNo(attachNoAry[k]);
						cpBo.setPrice(itemPrices[k].split("#")[i]);
						chargePeriodsDao.insertChargePeriods(cpBo);
					}
				}
			}
			bo.setChargePrice(null);
		}else{
			//充电费为标准
			if (StringUtil.isNotBlank(bo.getAttachItemNos()) && StringUtil.isBlank(bo.getItemChargeMode())){
				String attachNoAry[] = new String[]{};
				String attachItemNos = StringUtil.isBlank(bo.getAttachItemNos())?null:bo.getAttachItemNos();
				if (attachItemNos != null){
					attachNoAry = attachItemNos.split(",");
				}
				StringBuffer iteamChargeModeStr = new StringBuffer();
				for (int i = 0 ;i < attachNoAry.length;i++){
					iteamChargeModeStr.append("0201,");
				}
				//截取最后一个逗号
				bo.setItemChargeMode(String.valueOf(iteamChargeModeStr.substring(0,iteamChargeModeStr.length() - 1)));
			}
			bo.setPeriodSplitNum("0");//分时阶段数
		}
		bo.setChargeNum("1");//最小计费数
		bo.setChargeUnit(ChargeConstant.chcBillingPriceUnit.KWH);//计费单位：01kWh
		bo.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
		chargeBillingConfDao.insertChargeBillingConf(bo);

		// 如果是企业计费,且使用范围是部分站点，则需要存储到e_charge_billing_station
		if(StringUtil.isNotBlank(bo.getCustId()) && BillConstants.RANGE_FLAG.PART.equals(bo.getRangeFlag())){
			try {
				List<Map<String,Object>> sList = new ArrayList<Map<String,Object>>();
				List<ChargeBillingConfBo> dataList = JSONObject.parseArray(bo.getStationIdStr(), ChargeBillingConfBo.class);
				for (ChargeBillingConfBo chargeBillingConfBo : dataList) {
					Map<String,Object> sMap = new HashMap<String, Object>();
					sMap.put("chcNo",chcNo);
					sMap.put("stationId",chargeBillingConfBo.getStationId());
					sMap.put("stationName",chargeBillingConfBo.getStationName());
					sMap.put("custId",custId);
					sList.add(sMap);
				}
				chargeBillingConfDao.insertBillingStation(sList);
			}catch (Exception e){
				logger.debug("可使用站点信息转换异常！");
			}
		}
		return chcNo;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void updateChargeBillingConf(ChargeBillingConfBo bo){
		//如果“费控模式=本地”，gatherDataType=03量价费
		if(ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode())){
			bo.setGatherDataType(ChargeConstant.gatherDataType.PRICE);
		}
		//如果对“状态=有效”进行修改，则直接更新原记录； 
		//同时（删除表【E_CHCBILL_SEND】，删除【E_CHCBILL_PILESEND】【E_CHCBILL_PILESEND_LOG】）。
		if(ChargeConstant.validFlag.ENABLE.equals(bo.getChcStatus())){
			//判断是否同一个生效时间、同一个 充电站/用户 有多条记录
			int count = chargeBillingConfDao.queryCbcByEftDateAndStationId(bo);
			if(count > 0)
				throw new RuntimeException("同一个充电站的多条有效计费配置不能有相同的生效时间");
			//删除【E_CHCBILL_SEND】
			chcbillSendDao.deleteChcbillSend(bo.getChcNo());
			//，删除【E_CHCBILL_PILESEND】
			chcbillPileSendDao.deleteChcbillSend(bo.getChcNo());
			//删除【E_CHCBILL_PILESEND_LOG】
			chcbillPileSendLogDao.deleteChcbillPileSendLog(bo.getChcNo());
			// 如果（“费控模式=本地”、或者“费控模式=远程  and 桩推送数据类型=电量”），增加【E_CHCBILL_SEND】（其中SEND_STATUS=01未下发）
			if(ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode()) ||
					(ChargeConstant.billCtlMode.REMOTE.equals(bo.getBillCtlMode()) && ChargeConstant.gatherDataType.ELEC.equals(bo.getGatherDataType()))){
				ChcbillSendBo chcbillSendBo = new ChcbillSendBo();
				chcbillSendBo.setChcNo(bo.getChcNo());
				chcbillSendBo.setSendTime("");
				chcbillSendBo.setSendStatus(ChargeConstant.chcBillSendStatus.NOT);//未下发
				chcbillSendDao.insertChcbillSend(chcbillSendBo);
			}
		}
		//如果对“状态=草稿”进行修改，则直接更新原记录；

		//1、先删除分时表数据
		ChargePeriodsBo delCpBo = new ChargePeriodsBo();
		delCpBo.setChcNo(bo.getChcNo());
		chargePeriodsDao.deleteChargePeriods(delCpBo);
		//2、如果分时，插入分时数据
		if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(bo.getChargeMode())){
			String chargePeriodsStr = bo.getChargePeriodsStr();//分时数据
			Gson gson=new Gson();
			List<ChargePeriodsBo> cpBos =gson.fromJson(chargePeriodsStr, new TypeToken<List<ChargePeriodsBo>>(){}.getType());
			bo.setPeriodSplitNum(String.valueOf(cpBos.size()));//分时阶段数
			for(int i = 0;i < cpBos.size(); i++){
				ChargePeriodsBo cpBo = cpBos.get(i);
				cpBo.setSn(i+1+"");
				cpBo.setChcNo(bo.getChcNo());
				cpBo.setItemNo(ChargeConstant.itemTypeNo.CHARGE);
				chargePeriodsDao.insertChargePeriods(cpBo);
			}


			//其他费用项分时
			StringBuilder attchIteamPriceSb = new StringBuilder();
			String attachItemNos = StringUtil.isBlank(bo.getAttachItemNos())?null:bo.getAttachItemNos();
			String itemChargeModes = StringUtil.isBlank(bo.getItemChargeMode())?null:bo.getItemChargeMode();
			String itemPrices [] = bo.getAttachItemPrices().split(",");
			logger.debug("attachItemNos:"+attachItemNos);

			String attachNoAry[] = new String[]{};
			if (attachItemNos != null){
				attachNoAry = attachItemNos.split(",");
			}

			String[] iteamChargeModeArr = new String[]{};
			if(itemChargeModes == null && attachNoAry.length > 0){
				//兼容旧版计费
				StringBuffer iteamChargeModeStr = new StringBuffer();
				iteamChargeModeArr = new String[attachNoAry.length];
				for (int i = 0 ;i < attachNoAry.length;i++){
					iteamChargeModeArr[i] = "0201";
					iteamChargeModeStr.append("0201,");
				}
				//截取最后一个逗号
				bo.setItemChargeMode(iteamChargeModeStr.substring(0,iteamChargeModeStr.length() - 1).toString());
			}else if (itemChargeModes != null){
				logger.debug("itemChargeModes:"+itemChargeModes);
				iteamChargeModeArr = itemChargeModes.split(",");
			}

			for(int k = 0;k < attachNoAry.length; k++){
				if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(iteamChargeModeArr[k])){
					for(int i = 0;i < cpBos.size(); i++){//
						ChargePeriodsBo cpBo = cpBos.get(i);
						cpBo.setSn(i+1+"");
						cpBo.setChcNo(bo.getChcNo());
						cpBo.setItemNo(attachNoAry[k]);
						cpBo.setPrice(itemPrices[k].split("#")[i]);
						chargePeriodsDao.insertChargePeriods(cpBo);
					}
				}
			}

			bo.setChargePrice(null);
		}else{
			//充电费为标准
			if (StringUtil.isNotBlank(bo.getAttachItemNos()) && StringUtil.isBlank(bo.getItemChargeMode())){
				String attachNoAry[] = new String[]{};
				String attachItemNos = StringUtil.isBlank(bo.getAttachItemNos())?null:bo.getAttachItemNos();
				if (attachItemNos != null){
					attachNoAry = attachItemNos.split(",");
				}
				StringBuffer iteamChargeModeStr = new StringBuffer();
				for (int i = 0 ;i < attachNoAry.length;i++){
					iteamChargeModeStr.append("0201,");
				}
				//截取最后一个逗号
				bo.setItemChargeMode(String.valueOf(iteamChargeModeStr.substring(0,iteamChargeModeStr.length() - 1)));
			}else if (StringUtil.isBlank(bo.getAttachItemNos())){
				bo.setItemChargeMode("");
			}
			bo.setPeriodSplitNum("0");//分时阶段数
		}
//		bo.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
		chargeBillingConfDao.updateChargeBillingConf(bo);

		// 企业计费--删除可使用站点
		chargeBillingConfDao.delBillingStation(bo);

		// 如果是企业计费,且使用范围是部分站点，则需要存储到e_charge_billing_station
		if(StringUtil.isNotBlank(bo.getCustId()) && BillConstants.RANGE_FLAG.PART.equals(bo.getRangeFlag())){
			try {
				List<Map<String,Object>> sList = new ArrayList<Map<String,Object>>();
				List<ChargeBillingConfBo> dataList = JSONObject.parseArray(bo.getStationIdStr(), ChargeBillingConfBo.class);
				for (ChargeBillingConfBo chargeBillingConfBo : dataList) {
					Map<String,Object> sMap = new HashMap<String, Object>();
					sMap.put("chcNo",bo.getChcNo());
					sMap.put("stationId",chargeBillingConfBo.getStationId());
					sMap.put("stationName",chargeBillingConfBo.getStationName());
					sMap.put("custId",bo.getCustId());
					sList.add(sMap);
				}
				chargeBillingConfDao.insertBillingStation(sList);
			}catch (Exception e){
				logger.debug("可使用站点信息转换异常！");
			}
		}
	}

	public void addChargeBillingConfHistory(ChargeBillingConfHistoryBo bo){
		chargeBillingConfDao.addChargeBillingConfHistory(bo);
	}

	@Override
	public List<ChargeBillingConfHistoryBo> queryChargeBillingConfHistory(ChargeBillingConfHisQueryCondition condition) {
		return chargeBillingConfDao.queryChargeBillingConfHistory(condition);
	}

	@Override
	public int queryChargeBillingConfHistoryCount(ChargeBillingConfHisQueryCondition condition) {
		return chargeBillingConfDao.queryChargeBillingConfHistoryCount(condition);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 删除充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void delChargeBillingConf(ChargeBillingConfBo bo){
		//1、先删除分时表数据
		ChargePeriodsBo delCpBo = new ChargePeriodsBo();
		delCpBo.setChcNo(bo.getChcNo());
		chargePeriodsDao.deleteChargePeriods(delCpBo);
		//2、删除充电计费配置E_CHARGE_BILLING_CONF
		chargeBillingConfDao.deleteChargeBillingConf(bo);
		// 企业计费--删除可使用站点
		chargeBillingConfDao.delBillingStation(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 发布充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public void releaseChargeBillingConf(ChargeBillingConfBo bo){
		ChargeBillingConfQueryCondition searchBo = new ChargeBillingConfQueryCondition();
		searchBo.setChcNo(bo.getChcNo());
		List<ChargeBillingConfBo> cbList = chargeBillingConfDao.queryChargeBillingConfs(searchBo);
		if(cbList!=null && cbList.size() == 1){
			bo = cbList.get(0);
			//判断是否同一个生效时间、同一个充电站有多条记录
			int count = chargeBillingConfDao.queryCbcByEftDateAndStationId(bo);
			if(count > 0)
				throw new RuntimeException("同一个充电站的多条有效计费配置不能有相同的生效时间");
			//1、把当前充电计费配置设为有效
			ChargeBillingConfBo updateBo = new ChargeBillingConfBo();
			AccountBo currentAccount = authentication.getCurrentAccount();
			updateBo.setChcNo(bo.getChcNo());
			updateBo.setChcStatus(ChargeConstant.validFlag.ENABLE);
			updateBo.setChcStatusName(null);
			updateBo.setChargePrice("");
			updateBo.setOperNo(currentAccount.getAccountName());
			updateBo.setCreateTime("true");
			chargeBillingConfDao.updateChargeBillingConf(updateBo);
			//2、如果（“费控模式=本地”、或者“费控模式=远程  and 桩推送数据类型=电量”），同时写表【E_CHCBILL_SEND】，其中SEND_STATUS=01未下发
			if((ChargeConstant.billCtlMode.LOCAL.equals(bo.getBillCtlMode()) ||
							(ChargeConstant.billCtlMode.REMOTE.equals(bo.getBillCtlMode()) && ChargeConstant.gatherDataType.ELEC.equals(bo.getGatherDataType())))){
				//更新表【E_CHCBILL_SEND】（其中SEND_STATUS=01未下发）
				ChcbillSendBo chcbillSendBo = new ChcbillSendBo();
				chcbillSendBo.setChcNo(bo.getChcNo());
				chcbillSendBo.setSendTime("");
				chcbillSendBo.setSendStatus(ChargeConstant.chcBillSendStatus.NOT);//未下发
				chcbillSendDao.insertChcbillSend(chcbillSendBo);
			}
			
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 复制充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public ChargeBillingConfBo copyChargeBillingConf(ChargeBillingConfBo bo){
		ChargeBillingConfBo cbc = null;
		ChargeBillingConfQueryCondition searchBo = new ChargeBillingConfQueryCondition();
		searchBo.setChcNo(bo.getChcNo());
		List<ChargeBillingConfBo> cbList = chargeBillingConfDao.queryChargeBillingConfs(searchBo);
		if(cbList!=null && cbList.size() == 1){
			cbc = cbList.get(0);
			cbc.setChcNo(String.valueOf(seqRpcService.getDefNo()));
			//1、如果分时，复制分时数据
			if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbc.getChargeMode())){
				ChargeBillingConfQueryCondition cq = new ChargeBillingConfQueryCondition();
				cq.setChcNo(bo.getChcNo());
				List<ChargePeriodsBo> cpList = chargePeriodsDao.queryChargePeriods(cq);
				for(ChargePeriodsBo cpBo:cpList){
					cpBo.setChcNo(cbc.getChcNo());
					chargePeriodsDao.insertChargePeriods(cpBo);
				}
			}
			//2、复制充电计费配置E_CHARGE_BILLING_CONF
			cbc.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
			chargeBillingConfDao.insertChargeBillingConf(cbc);

			//3、如果站点适用范围为部分，且存在限制站点时，复制限制站点
			if(BillConstants.RANGE_FLAG.PART.equals(cbc.getRangeFlag())){
				ChargeBillingConfQueryCondition condtion = new ChargeBillingConfQueryCondition();
				condtion.setChcNo(bo.getChcNo());
				List<ChargeBillingConfBo> dataList = chargeBillingConfDao.queryBillingStation(condtion);
				if(CollectionUtils.isNotEmpty(dataList) && dataList.size() > 0){
					List<Map<String,Object>> sList = new ArrayList<Map<String,Object>>();
					for (ChargeBillingConfBo chargeBillingConfBo : dataList) {
						Map<String,Object> sMap = new HashMap<String, Object>();
						sMap.put("chcNo",cbc.getChcNo());
						sMap.put("stationId",chargeBillingConfBo.getStationId());
						sMap.put("stationName",chargeBillingConfBo.getStationName());
						sMap.put("custId",cbc.getCustId());
						sList.add(sMap);
					}
					chargeBillingConfDao.insertBillingStation(sList);
				}
			}
		}
		return cbc;
	}

	@Override
	public List<ChargeBillingConfBo> deactivateChargeBillingConf(ChargeBillingConfBo bo) {
		//查询该计费是否有关联站点-桩，如果有则无法停用
		List<ChargeBillingConfBo> chargeBillingConfBos = chargeBillingConfDao.selectBillRels(bo.getChcNo());
		if (chargeBillingConfBos.size() > 0){
			String stationIds = "";
			String pileIds = "";
			for (int i = 0; i < chargeBillingConfBos.size(); i++) {
				if (!StringUtil.isEmpty(chargeBillingConfBos.get(i).getStationId())){
					stationIds += "," + chargeBillingConfBos.get(i).getStationId();
				}
				if (!StringUtil.isEmpty(chargeBillingConfBos.get(i).getPileId())){
					pileIds += "," + chargeBillingConfBos.get(i).getPileId();
				}
			}
			logger.debug("statonIds={}", stationIds);
			if (StringUtil.isNotEmpty(stationIds)){
				List<Map<String,Object>> stations = archivesRpcService.
						queryStations(stationIds.substring(1));
				logger.debug("stations={}", IJsonUtil.obj2Json(stations));
				for (int i = 0; i < chargeBillingConfBos.size(); i++) {
					ChargeBillingConfBo confBo = chargeBillingConfBos.get(i);
					if (StringUtil.isNotEmpty(confBo.getStationId())){
						for (int j = 0; j < stations.size(); j++) {
							Map<String,Object> station = stations.get(j);
							if (confBo.getStationId().equals(String.valueOf(station.get("stationId")))){
								confBo.setStationName(String.valueOf(station.get("stationName")));
							}
						}
					}
				}
			}
			//todo   桩关联
			return chargeBillingConfBos;
		}

		//停用计费
		bo.setChcStatus(ChargeConstant.validFlag.UNENABLE);
		chargeBillingConfDao.updateChargeBillingConf(bo);
		return null;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2018-03-20
	 * @description 调价充电计费配置E_CHARGE_BILLING_CONF、E_CHARGE_PERIODS
	 */
	public ChargeBillingConfBo modifyPriceChargeBillingConf(ChargeBillingConfBo bo){
		ChargeBillingConfBo cbc = null;
		ChargeBillingConfQueryCondition searchBo = new ChargeBillingConfQueryCondition();
		searchBo.setChcNo(bo.getChcNo());
		List<ChargeBillingConfBo> cbList = chargeBillingConfDao.queryChargeBillingConfs(searchBo);
		if(cbList!=null && cbList.size() == 1){
			cbc = cbList.get(0);
			cbc.setUpChcNo(cbc.getChcNo());
			cbc.setChcNo(String.valueOf(seqRpcService.getDefNo()));
			//1、如果分时，复制分时数据
			if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbc.getChargeMode())){
				ChargeBillingConfQueryCondition cq = new ChargeBillingConfQueryCondition();
				cq.setChcNo(bo.getChcNo());
				List<ChargePeriodsBo> cpList = chargePeriodsDao.queryChargePeriods(cq);
				for(ChargePeriodsBo cpBo:cpList){
					cpBo.setChcNo(cbc.getChcNo());
					chargePeriodsDao.insertChargePeriods(cpBo);
				}
			}
			//2、复制充电计费配置E_CHARGE_BILLING_CONF
			cbc.setChcStatus(ChargeConstant.validFlag.DRAFT);//状态，01有效、02无效、03草稿
			chargeBillingConfDao.insertChargeBillingConf(cbc);
		}
		return cbc;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电分时设置  E_CHARGE_PERIODS
	 */
	public List<ChargePeriodsBo> queryChargePeriods(ChargeBillingConfQueryCondition bo){
		return peakService.queryPeriodListCheckPeak(bo);
	}

	/**
	 * 查询计费配置信息
	 * @param condition
	 * @return
	 */
	public List<ChargeBillingConfBo> queryChargeBillingMap(ChargeBillingConfQueryCondition condition) {
		return chargeBillingConfDao.queryChargeBillingMap(condition);
	}

	/**
	 * @description 注销计费
	 * <AUTHOR>
	 * @create 2018-11-05 10:52:40
	 */
	public void cancellChargeBillingConf(ChargeBillingConfBo bo) {
		ChargeBillingConfQueryCondition searchBo = new ChargeBillingConfQueryCondition();
		searchBo.setChcNo(bo.getChcNo());
		List<ChargeBillingConfBo> cbList = chargeBillingConfDao.queryChargeBillingConfs(searchBo);
		if(cbList!=null && cbList.size() == 1){
			bo = cbList.get(0);
			//判断是否同一个生效时间、同一个充电站有多条记录
			int count = chargeBillingConfDao.queryCbcByEftDateAndStationId(bo);
			if(count > 0)
				throw new RuntimeException("同一个充电站的多条有效计费配置不能有相同的生效时间");
			//1、把当前充电计费配置设为无效
			ChargeBillingConfBo updateBo = new ChargeBillingConfBo();
			AccountBo currentAccount = authentication.getCurrentAccount();
			updateBo.setChcNo(bo.getChcNo());
			updateBo.setChcStatus(ChargeConstant.validFlag.UNENABLE);
			updateBo.setChcStatusName(null);
			updateBo.setChargePrice("");
			updateBo.setOperNo(currentAccount.getAccountName());
			chargeBillingConfDao.updateChargeBillingConf(updateBo);
		}
	}

	@Override
	public void effectiveChargeBillingConf(ChargeBillingConfBo bo,Map<String,Object> requestMap)  throws Exception{
		if (StringUtil.isNotBlank(bo.getUpChcNo())){
			ChargeBillingConfBo updateBo = new ChargeBillingConfBo();
			// 将上一个计费版本改为失效
			updateBo.setChcStatus(ChargeConstant.validFlag.UNENABLE);
			updateBo.setChcNo(bo.getUpChcNo());
			chargeBillingConfDao.updateChargeBillingConf(updateBo);

			//调价时需要计费关联站点一致修改
			Map map = new HashMap();
			map.put("newChcNo", bo.getChcNo());//新的计费编号
			map.put("upChcNo", bo.getUpChcNo());//旧的计费编号
			chargeBillingConfDao.updateStationBilling(map);
			chargeBillingConfDao.updatePileBilling(map);

			//删除尖峰计费关联
			if (peakService.peakBillingSwitch()){
				peakService.delPeakRel(bo.getUpChcNo(),requestMap);
			}
		}

		bo.setChcStatus(ChargeConstant.validFlag.ENABLE);
		chargeBillingConfDao.updateChargeBillingConf(bo);

		// 计费主动下发
		if (StringUtil.isNotBlank(bo.getUpChcNo())){
			ChcbillSendQueryCondition condition = new ChcbillSendQueryCondition();
			condition.setChcNo(bo.getUpChcNo());
			autoBillingSend(condition,bo.getChcNo(),requestMap);
		}
	}


	private void autoBillingSend(ChcbillSendQueryCondition bo,String chcNo,Map<String,Object> requestMap) throws Exception{
		bo.setGroupType("station");
		List<Map<String,Object>> sendList = xpPileSendService.queryChcbillPileSendsByNo(bo);
		if (CollectionUtils.isNotEmpty(sendList)){
			List<String> stationIdList = new ArrayList<>();
			for (Map<String, Object> sendMap : sendList) {
				if (StringUtil.isNotBlank(MapUtils.getValue(sendMap,"stationId"))){
					stationIdList.add(MapUtils.getValue(sendMap,"stationId"));
				}
			}
			Map<String, Object> m = new HashMap<>();
			m.put("stationIdList",stationIdList);
			List<Map<String,Object>> list = archivesRpcService.queryStationsList(m);
			MergeUtil.mergeList(sendList, list, "stationId",  new String[]{"billingConfig"},  new String[]{"billingConfig"});
			for (Map<String, Object> sendMap : sendList) {
				if (!AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(sendMap, "billingConfig"))){
					String newChcNo = "";
					if (StringUtil.isNotBlank(chcNo)){
						newChcNo = chcNo;
					}else{
						newChcNo = MapUtils.getValue(sendMap,"chcNo");
					}
					try {
						if (StringUtil.isNotBlank(newChcNo)){
							logger.debug("==========计费:{},站:{}", newChcNo,MapUtils.getValue(sendMap,"stationId"));
							xpStationSendService.issuredStation(newChcNo, MapUtils.getValue(sendMap, "stationId"), requestMap);
						}
					}catch (Exception e){
						logger.debug("==========站点计费下发失败:", e);
					}
				}
			}
		}
		bo.setGroupType("pile");
		List<Map<String,Object>> pileSendList = xpPileSendService.queryChcbillPileSendsByNo(bo);
		if (CollectionUtils.isNotEmpty(pileSendList)){
			List<String> stationIdList = new ArrayList<>();
			for (Map<String, Object> sendMap : pileSendList) {
				if (StringUtil.isNotBlank(MapUtils.getValue(sendMap,"stationId"))){
					stationIdList.add(MapUtils.getValue(sendMap,"stationId"));
				}
			}
			Map<String, Object> m = new HashMap<>();
			m.put("stationIdList",stationIdList);
			List<Map<String,Object>> list = archivesRpcService.queryStationsList(m);
			MergeUtil.mergeList(pileSendList, list, "stationId",  new String[]{"billingConfig"},  new String[]{"billingConfig"});

			for (Map<String, Object> sendMap : pileSendList) {
				if (AssetConstants.billingConfig.BILLING_BY_PILE.equals(MapUtils.getValue(sendMap, "billingConfig"))){
					String newChcNo = "";
					if (StringUtil.isNotBlank(chcNo)){
						newChcNo = chcNo;
					}else{
						newChcNo = MapUtils.getValue(sendMap,"chcNo");
					}
					try {
						if (StringUtil.isNotBlank(newChcNo)){
							logger.debug("==========计费:{},站:{},桩:{}", newChcNo,MapUtils.getValue(sendMap,"stationId"),MapUtils.getValue(sendMap,"pileNo"));
							List<String> pileList = new ArrayList<>();
							pileList.add(MapUtils.getValue(sendMap, "pileId"));
							Map<String,Object> searchMap = new HashedMap();
							searchMap.put("pileIdList",pileList);
							List<Map> pileInfoList = archivesRpcService.queryPilesByMap(searchMap);
							if (CollectionUtils.isNotEmpty(pileInfoList)){
								String runStatus = StringUtil.nullForString(pileInfoList.get(0).get("runStatus"));
								if (StringUtil.isNotBlank(runStatus) && runStatus.indexOf(AssetConstants.GunCouplerRunStatus.Gun_Coupler_Run_Status_5) < 0){
									xpPileSendService.issuredPile(newChcNo, MapUtils.getValue(sendMap, "pileNo"), MapUtils.getValue(sendMap, "stationId"));
								}
							}
						}
					}catch (Exception e){
						logger.debug("==========桩计费下发失败:", e);
					}
				}
			}
		}
	}

	/**
	 * @param bo
	 * @description 企业计费--查询可使用站点信息
	 * <AUTHOR>
	 * @create 2019-05-08 17:48:39
	 */
	@Override
	public List<ChargeBillingConfBo> queryBillingStation(ChargeBillingConfQueryCondition bo) {
		return chargeBillingConfDao.queryBillingStation(bo);
	}

	/**
	 * @param bo
	 * @description 下发分时信息
	 * <AUTHOR>
	 * @create 2018-08-13 18:40:33
	 */
	public void sendChargeBillingConf(ChargeBillingConfBo bo) {
		//获取该计费下的所有站
		XpChargeBillingConfQueryCondition xpCondition = new XpChargeBillingConfQueryCondition();
		xpCondition.setChcNo(bo.getChcNo());
		List<ChargeBillingConfBo> chargeBillingList = xpChargeBillingConfService.queryXpChargeBillingConfInfo(xpCondition);
		List periodsList = getPeriodsList(bo);
		Map sendMap = new HashMap();
		for(ChargeBillingConfBo cBillBo:chargeBillingList){
			logger.debug("--------------开始下发站点分时配置："+cBillBo.getStationId());
			//获取站点下面的枪
			Map qryMap = new HashMap();
			qryMap.put("stationId",cBillBo.getStationId());
			List<Map<String,Object>> equipList =  archivesRpcService.queryPileGunListByMap(qryMap);
			for(Map gunMap:equipList){//循环下发
				String pileId = StringUtil.nullToString(gunMap.get("pileId"));
				String gunNo = StringUtil.nullToString(gunMap.get("gunNo"));
				sendMap.put("pileId",pileId);
				sendMap.put("gunNo",gunNo);
				sendMap.put("periodsList",periodsList);
				logger.debug(">>>>>>>>>>>>>>>>下发分时配置："+sendMap);
				try {
					ProducerHelper.singleProduce(MqConstants.PILE_CHARGEIN_PERIODS, IJsonUtil.obj2Json(sendMap));
				}catch (Exception e){
					e.printStackTrace();
				}
			}
		}
	}

	private List getPeriodsList(ChargeBillingConfBo bo) {
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
		condition.setChcNo(bo.getChcNo());
		List<ChargePeriodsBo> chargePeriods = peakService.queryPeriodListCheckPeak(condition);
		List periodsList = new ArrayList();

		for(ChargePeriodsBo periodsBo:chargePeriods){
			Map periodsMap = new HashMap();
			periodsMap.put("sn",periodsBo.getSn());
			periodsMap.put("beginTime",periodsBo.getBeginTime());
			periodsMap.put("endTime",periodsBo.getEndTime());
			periodsMap.put("periodFlag",periodsBo.getTimeFlag());
			periodsMap.put("periodRate",periodsBo.getPrice());
			periodsList.add(periodsMap);
		}
		return periodsList;
	}
}
