<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<%
	String astPath = (String) request.getAttribute("astPath");
	String pubPath = (String) request.getAttribute("pubPath");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="">
		<style type="text/css">
.textEditor textarea {
	resize: none
}
</style>
</ls:head>
<ls:body>
		<ls:form name="billingAdjustCalendarForm" id="billingAdjustCalendarForm">
				<table class="tab_search">
						<tr>
								<td>
                    <ls:label text="车型" ref="autoModelName"/>
                </td>
                <td>
                    <ls:text name="autoModelNo" type="hidden"></ls:text>
                    <ls:text imageKey="search" onClickImage="getModelRental" name="autoModelName" required="true" readOnly="true" onchanged="changeAutoModelName"></ls:text>
                </td>
                <td>
                    <ls:label text="适用日期" ref="applyDate" />
                </td>
                <td>
                    <ls:date name="applyDate" required="true" format="yyyy-MM"></ls:date>
                </td>
                <td>
                    <ls:button text="清空" onclick="doClear" />
                    <ls:button text="查询" onclick="doSearch" />
                </td>
						</tr>
				</table>
				<ls:calendar url="" name="testCalend" calendarView="month" refresh="false" refreshTime="5000">
				</ls:calendar>
		</ls:form>
		<ls:script>	
	
		window.billingAdjustCalendarForm = billingAdjustCalendarForm;
		<%--日期上单击，添加日程
		dayClick : function(date, allDay, jsEvent, view ){
					if (LS.isFunction(_this.dayClick)) {
						_this.dayClick(date, allDay, jsEvent, view);
					}
					return false;
				} --%>
		testCalend.dayClick = function tt(p1,p2,p3,p4){
			LS.dialog("<%=pubPath %>"+"/calendar/openAddSchedule/"+ p1,I18nUtil.getMessage('CALENDAR_ADD'),400,550,true,{
				close : function(){
					testCalend.reLoadEvent(); 
				}
			});
		}
		<%--事件上单击，修改日程--%>
		testCalend.eventClick = function tt(p1,p2,p3){
			var scheduleId = p1.id;
			LS.dialog("<%=pubPath %>"+"/calendar/openUpdateSchedule/"+scheduleId,I18nUtil.getMessage('CALENDAR_EDIT'),400,500,false,{
				close : function(){
					testCalend.reLoadEvent(); 
				}
			});
		}
		<%--事件上右击，删除日程 --%>
		  testCalend.eventRightClick = function(event, jsEvent, view){
		  
		  document.oncontextmenu = function() {  
       			return false;  
    		}
			var scheduleId = event.id;
			if(scheduleId != null || "" != scheduleId){
			LS.confirm(I18nUtil.getMessage('CALENDAR_DEL_CONFIRM'),function(result){
			if(result){
				LS.ajax("<%=pubPath %>"+"/calendar/deleteScheduleInfor/"+scheduleId,null,function(){
						testCalend.reLoadEvent(); 
					}); 
			}
			document.oncontextmenu = function() {  
       			return true;  
    		}
 			});
		}
			
		} 
		
		
		<%-- 	
		testCalend.eventRightClick = function(event, jsEvent, view){
		}
		--%>
		window.doSearch = doSearch;
		var dataSource = null;
    function doSearch(){
      if(!billingAdjustInitForm.valid()){
        return;
      }
      var params = billingAdjustCalendarForm.getFormData();
      LS.ajax("~/billing/adjust/getBillingAdjust2Calendar",params,function(data){
        if(dataSource){
          testCalend.removeEventSource(dataSource);
        }
        dataSource=data;
        testCalend.addEventSource(dataSource);
      }); 
    }
    
    LS.node("#span_prev").on("click",function(){
      var date = testCalend.getView().title.replace("年","-").replace("月","").replace(/\s+/g,"");
      applyDate.setValue(date);
      doSearch();
    });
    
    LS.node("#span_next").on("click",function(){
      var date = testCalend.getView().title.replace("年","-").replace("月","").replace(/\s+/g,"");
      applyDate.setValue(date);
      doSearch();
    });
    
    LS.node("#span_today").on("click",function(e){
      var classCss = LS.node("#span_today").attr("class");
      if(classCss.indexOf("ui-state-disabled")>-1){
        return;
      }
      var date = testCalend.getView().title.replace("年","-").replace("月","").replace(/\s+/g,"");
      applyDate.setValue(date);
      doSearch();
    });

    function doClear(){
      billingAdjustInitForm.clear();
      autoModelNo.setValue();
    }
    
    function doAdjust(){
      billingAdjustInitForm.clear();
      autoModelNo.setValue();
    }
	function getModelRental(){
      LS.dialog("<%=astPath %>"+"/ast/brand/init?flag=1","选择车型",1000,500,true);
    }
    function changeAutoModelName(){
      if(LS.isEmpty(autoModelName.getValue())){
        autoModelNo.setValue();
      }
    }
    window.setModelRental = setModelRental;
    function setModelRental(item) {
       autoModelNo.setValue(item.modelId);  
       autoModelName.setValue(item.modelName);
      // subBe.setValue(item.subBe);
      // subBeName.setValue(item.subBeName);
    }
		</ls:script>
</ls:body>
</html>