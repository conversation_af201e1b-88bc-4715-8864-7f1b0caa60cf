package com.ls.ner.billing.api.rent.model;


/**
 * 押金和其他计费项目
 * <AUTHOR>
 *
 */
public class AppendChargeItem extends AbstractChargeItem implements IChargeTimePoint {
	
	private String itemNo;
	
	private String itemName;
	
	private String buyType;//是否必购
	
	private String buyTypeName;
	
	private String remarks;
	
	private String priceRemarks;//定价描述
	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}
	//app需要使用
	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	private String chargeTimePoint;
	
	public String getChargeTimePoint() {
		return chargeTimePoint;
	}

	public void setChargeTimePoint(String chargeTimePoint) {
		this.chargeTimePoint = chargeTimePoint;
	}

	public String getBuyType() {
		return buyType;
	}

	public void setBuyType(String buyType) {
		this.buyType = buyType;
	}

	public String getBuyTypeName() {
		return buyTypeName;
	}

	public void setBuyTypeName(String buyTypeName) {
		this.buyTypeName = buyTypeName;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getPriceRemarks() {
		return priceRemarks;
	}

	public void setPriceRemarks(String priceRemarks) {
		this.priceRemarks = priceRemarks;
	}

}
