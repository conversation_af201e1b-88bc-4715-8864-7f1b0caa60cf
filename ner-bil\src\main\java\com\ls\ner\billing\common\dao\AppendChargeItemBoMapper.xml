<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.AppendChargeItemBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="DATA_OPER_TYPE" jdbcType="VARCHAR" property="dataOperType" />
    <result column="ITEM_NO" jdbcType="VARCHAR" property="itemNo" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="ITEM_UNIT" jdbcType="VARCHAR" property="itemUnit" />
    <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType" />
    <result column="SN" jdbcType="INTEGER" property="sn" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="BUY_TYPE" jdbcType="VARCHAR" property="buyType" />
    <result column="ITEM_STATUS" jdbcType="VARCHAR" property="itemStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    SYSTEM_ID, DATA_OPER_TIME, DATA_OPER_TYPE, ITEM_NO, ITEM_NAME, ITEM_UNIT, ITEM_TYPE, 
    SN,REMARKS,BUY_TYPE,ITEM_STATUS
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_append_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_append_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    delete from e_append_charge_item
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    delete from e_append_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    insert into e_append_charge_item (SYSTEM_ID, DATA_OPER_TIME, DATA_OPER_TYPE, 
      ITEM_NO, ITEM_NAME, ITEM_UNIT, 
      ITEM_TYPE, SN)
    values (#{systemId,jdbcType=BIGINT}, #{dataOperTime,jdbcType=TIMESTAMP}, #{dataOperType,jdbcType=VARCHAR}, 
      #{itemNo,jdbcType=VARCHAR}, #{itemName,jdbcType=VARCHAR}, #{itemUnit,jdbcType=VARCHAR}, 
      #{itemType,jdbcType=VARCHAR}, #{sn,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    insert into e_append_charge_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE,
      </if>
      <if test="itemNo != null">
        ITEM_NO,
      </if>
      <if test="itemName != null">
        ITEM_NAME,
      </if>
      <if test="itemUnit != null">
        ITEM_UNIT,
      </if>
      <if test="itemType != null">
        ITEM_TYPE,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="buyType != null">
        BUY_TYPE,
      </if>
      <if test="itemStatus != null">
        ITEM_STATUS,
      </if>
      <if test="remarks != null">
        REMARKS,
      </if>
      <if test="pBe != null">
        p_be,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemUnit != null">
        #{itemUnit,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=INTEGER},
      </if>
      <if test="buyType != null">
        #{buyType,jdbcType=INTEGER},
      </if>
      <if test="itemStatus != null">
        #{itemStatus,jdbcType=INTEGER},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=INTEGER},
      </if>
      <if test="pBe != null">
        #{pBe,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    select count(*) from e_append_charge_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    update e_append_charge_item
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOperType != null">
        DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.itemNo != null">
        ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      </if>
      <if test="record.itemName != null">
        ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      </if>
      <if test="record.itemUnit != null">
        ITEM_UNIT = #{record.itemUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        ITEM_TYPE = #{record.itemType,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        SN = #{record.sn,jdbcType=INTEGER},
      </if>
      <if test="record.itemStatus != null">
        ITEM_STATUS = #{record.itemStatus,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    update e_append_charge_item
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{record.dataOperType,jdbcType=VARCHAR},
      ITEM_NO = #{record.itemNo,jdbcType=VARCHAR},
      ITEM_NAME = #{record.itemName,jdbcType=VARCHAR},
      ITEM_UNIT = #{record.itemUnit,jdbcType=VARCHAR},
      ITEM_TYPE = #{record.itemType,jdbcType=VARCHAR},
      SN = #{record.sn,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    update e_append_charge_item
    <set>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOperType != null">
        DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null">
        ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="itemName != null">
        ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemUnit != null">
        ITEM_UNIT = #{itemUnit,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        SN = #{sn,jdbcType=INTEGER},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.common.bo.AppendChargeItemBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 09 16:23:35 CST 2016.
    -->
    update e_append_charge_item
    set DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      DATA_OPER_TYPE = #{dataOperType,jdbcType=VARCHAR},
      ITEM_NO = #{itemNo,jdbcType=VARCHAR},
      ITEM_NAME = #{itemName,jdbcType=VARCHAR},
      ITEM_UNIT = #{itemUnit,jdbcType=VARCHAR},
      ITEM_TYPE = #{itemType,jdbcType=VARCHAR},
      SN = #{sn,jdbcType=INTEGER}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>