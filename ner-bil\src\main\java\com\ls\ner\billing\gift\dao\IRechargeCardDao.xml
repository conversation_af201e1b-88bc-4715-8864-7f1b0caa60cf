<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.gift.dao.IRechargeCardDao">

    <select id="listRechargeCard" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="com.ls.ner.billing.gift.bo.RechargeCardBo">
        select
            a.CARD_ID AS cardId,
            a.CARD_NAME AS cardName,
            a.CARD_BATCH_NO AS cardBatchNo,
            SUM(COALESCE(d.ACTUAL_AMT, 0) + COALESCE(d.GIFT_AMT, 0) ) as totalAmt,
            a.ACTUAL_AMT AS actualAmt,
            a.GIFT_AMT AS giftAmt,
            a.REMARK AS remark,
            a.CARD_STATUS AS cardStatus,
            CASE WHEN a.CARD_STATUS = '1' THEN '启用' ELSE '禁用' END cardStatusName,
            a.CARD_TIME_TYPE AS cardTimeType,
            DATE_FORMAT(a.EFT_DATE,"%Y-%m-%d %H:%i:%s") AS eftDate,
            DATE_FORMAT(a.INV_DATE,"%Y-%m-%d %H:%i:%s") AS invDate,
            a.CREATE_TIME AS createTime,
            a.CREATE_USER AS createUser,
            a.UPDATE_TIME AS updateTime,
            a.UPDATE_USER AS updateUser,
            count(d.CARD_ID) as totalCardCount,
            SUM(CASE WHEN COALESCE(d.ACTUAL_BALANCE, 0) + COALESCE(d.GIFT_BALANCE, 0) > 0 THEN 1 ELSE 0 END) as remainingCardCount,
            SUM(CASE WHEN COALESCE(d.ACTUAL_BALANCE, 0) + COALESCE(d.GIFT_BALANCE, 0) <![CDATA[ <= 0 ]]> THEN 1 ELSE 0 END) as usedUpCardCount,
            SUM(COALESCE(d.ACTUAL_BALANCE, 0) + COALESCE(d.GIFT_BALANCE, 0) ) as totalBalance,
            SUM(COALESCE(d.ACTUAL_BALANCE, 0)) as actualBalance,
            SUM(COALESCE(d.GIFT_BALANCE, 0) ) as giftBalance,
            SUM(COALESCE(d.ACTUAL_AMT, 0) + COALESCE(d.GIFT_AMT, 0) - COALESCE(d.ACTUAL_BALANCE, 0) - COALESCE(d.GIFT_BALANCE, 0)  ) as totalClearCount,
            SUM(COALESCE(d.ACTUAL_AMT, 0) - COALESCE(d.ACTUAL_BALANCE, 0)  ) as realClearCount,
            SUM(COALESCE(d.GIFT_AMT, 0) - COALESCE(d.GIFT_BALANCE, 0) ) as giftClearCount,
            a.INTEGRAL_NUM as integralNum
        from b_recharge_card a
        left JOIN b_recharge_card_detail d on a.CARD_ID = d.CARD_ID
        <where>
            <if test="cardName !=null and cardName !=''">
                and a.CARD_NAME like concat('%',#{cardName},'%')
            </if>
            <if test="cardBatchNo !=null and cardBatchNo !=''">
                and a.CARD_BATCH_NO like concat('%',#{cardBatchNo},'%')
            </if>
            <if test="createTimeBegin !=null and createTimeBegin !=''">
                AND a.CREATE_TIME >= #{createTimeBegin}
            </if>
            <if test="createTimeEnd !=null and createTimeEnd  !=''">
                AND <![CDATA[ a.CREATE_TIME <= #{createTimeEnd}]]>
            </if>
            <if test="cardStatus !=null and cardStatus  !=''">
                AND a.CARD_STATUS = #{cardStatus}
            </if>
        </where>
        GROUP BY a.CARD_ID
        order by a.CREATE_TIME desc
        <if test="end!=null and end!= 0">
            limit #{begin},#{end}
        </if>
    </select>

    <select id="countRechargeCard" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="int">
        select
            count(1)
        from b_recharge_card
        <where>
            <if test="cardName !=null and cardName !=''">
                and CARD_NAME like concat('%',#{cardName},'%')
            </if>
            <if test="cardBatchNo !=null and cardBatchNo !=''">
                and CARD_BATCH_NO like concat('%',#{cardBatchNo},'%')
            </if>
            <if test="createTimeBegin !=null and createTimeBegin !=''">
                AND CREATE_TIME >= #{createTimeBegin}
            </if>
            <if test="createTimeEnd !=null and createTimeEnd  !=''">
                AND <![CDATA[ CREATE_TIME <= #{createTimeEnd}]]>
            </if>
            <if test="cardStatus !=null and cardStatus  !=''">
                AND CARD_STATUS = #{cardStatus}
            </if>
        </where>
    </select>


    <select id="queryDetails" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        SELECT
        CARD_DETAIL_ID  as cardDetailId,
        CARD_ID as cardId,
        CARD_BATCH_NO as cardBatchNo,
        CARD_NO as cardNo,
        CARD_SECRET as cardSecret,
        CUST_ID as custId,
        MOBILE as mobile,
        (COALESCE(ACTUAL_AMT, 0) + COALESCE(GIFT_AMT, 0)) as totalAmt,
        ACTUAL_AMT as actualAmt,
        GIFT_AMT as giftAmt,
        (COALESCE(ACTUAL_BALANCE, 0) + COALESCE(GIFT_BALANCE, 0)) as totalBalance,
        ACTUAL_BALANCE as actualBalance,
        GIFT_BALANCE as giftBalance,
        (COALESCE(ACTUAL_AMT, 0) + COALESCE(GIFT_AMT, 0)) - (COALESCE(ACTUAL_BALANCE, 0) + COALESCE(GIFT_BALANCE, 0)) as totalClearCount,
        (COALESCE(ACTUAL_AMT, 0) - COALESCE(ACTUAL_BALANCE, 0))  as realClearCount,
        (COALESCE(GIFT_AMT, 0) - COALESCE(GIFT_BALANCE, 0))  as giftClearCount,
        DATE_FORMAT(ACTIVATE_TIME,"%Y-%m-%d %H:%i:%s") as activateTime,
        CARD_DETAIL_STATUS as cardDetailStatus,
        CASE
            WHEN CARD_DETAIL_STATUS = '0' THEN '待激活'
            WHEN CARD_DETAIL_STATUS = '1' THEN '已激活'
            WHEN CARD_DETAIL_STATUS = '2' THEN '已冻结'
            WHEN CARD_DETAIL_STATUS = '3' THEN '已退款'
            ELSE '使用完毕' END cardDetailStatusName,
        CARD_SOURCE as cardSource,
        CREATE_TIME as createTime,
        CREATE_USER as createUser,
        UPDATE_TIME as updateTime,
        UPDATE_USER as updateUser
        FROM b_recharge_card_detail
        <where>
            <if test="mobile !=null and mobile !=''">
                and MOBILE like concat('%',#{mobile},'%')
            </if>
            <if test="cardNo !=null and cardNo !=''">
                and CARD_NO like concat('%',#{cardNo},'%')
            </if>
            <if test="cardSecret !=null and cardSecret !=''">
                and CARD_SECRET like concat('%',#{cardSecret},'%')
            </if>
            <if test="cardDetailStatus !=null and cardDetailStatus  !=''">
                AND CARD_DETAIL_STATUS = #{cardDetailStatus}
            </if>
            <if test="custId !=null and custId  !=''">
                AND CUST_ID = #{custId}
            </if>
            <if test="cardId !=null">
                AND CARD_ID = #{cardId}
            </if>
        </where>
        order by UPDATE_TIME desc
        <if test="end!=null and end!= 0">
            limit #{begin},#{end}
        </if>
    </select>


    <select id="queryDetailsOrderByActualBalance" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        SELECT
        CARD_DETAIL_ID  as cardDetailId,
        CARD_ID as cardId,
        CARD_BATCH_NO as cardBatchNo,
        CARD_NO as cardNo,
        CARD_SECRET as cardSecret,
        CUST_ID as custId,
        MOBILE as mobile,
        (COALESCE(ACTUAL_AMT, 0) + COALESCE(GIFT_AMT, 0)) as totalAmt,
        ACTUAL_AMT as actualAmt,
        GIFT_AMT as giftAmt,
        (COALESCE(ACTUAL_BALANCE, 0) + COALESCE(GIFT_BALANCE, 0)) as totalBalance,
        ACTUAL_BALANCE as actualBalance,
        GIFT_BALANCE as giftBalance,
        ACTIVATE_TIME as activateTime,
        CARD_DETAIL_STATUS as cardDetailStatus,
        CASE
        WHEN CARD_DETAIL_STATUS = '0' THEN '待激活'
        WHEN CARD_DETAIL_STATUS = '1' THEN '已激活'
        WHEN CARD_DETAIL_STATUS = '2' THEN '已冻结'
        WHEN CARD_DETAIL_STATUS = '3' THEN '已退款'
        ELSE '使用完毕' END cardDetailStatusName,
        CARD_SOURCE as cardSource,
        CREATE_TIME as createTime,
        CREATE_USER as createUser,
        UPDATE_TIME as updateTime,
        UPDATE_USER as updateUser
        FROM b_recharge_card_detail
        <where>
            <if test="mobile !=null and mobile !=''">
                and MOBILE like concat('%',#{mobile},'%')
            </if>
            <if test="cardNo !=null and cardNo !=''">
                and CARD_NO like concat('%',#{cardNo},'%')
            </if>
            <if test="cardSecret !=null and cardSecret !=''">
                and CARD_SECRET like concat('%',#{cardSecret},'%')
            </if>
            <if test="cardDetailStatus !=null and cardDetailStatus  !=''">
                AND CARD_DETAIL_STATUS = #{cardDetailStatus}
            </if>
            <if test="custId !=null and custId  !=''">
                AND CUST_ID = #{custId}
            </if>
        </where>
        order by ACTUAL_BALANCE asc
    </select>

    <select id="queryDetailsCount" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="int">
        SELECT
          count(1)
        FROM b_recharge_card_detail
        <where>
            <if test="mobile !=null and mobile !=''">
                and MOBILE like concat('%',#{mobile},'%')
            </if>
            <if test="cardNo !=null and cardNo !=''">
                and CARD_NO like concat('%',#{cardNo},'%')
            </if>
            <if test="cardSecret !=null and cardSecret !=''">
                and CARD_SECRET like concat('%',#{cardSecret},'%')
            </if>
            <if test="cardDetailStatus !=null and cardDetailStatus  !=''">
                AND CARD_DETAIL_STATUS = #{cardDetailStatus}
            </if>
            <if test="cardId !=null">
                AND CARD_ID = #{cardId}
            </if>
        </where>
    </select>

    <select id="qryCardOrderDetail" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="com.ls.ner.billing.gift.bo.RechargeCardOrder">
        SELECT
        CARD_DETAIL_ID as cardDetailId,
        ORDER_NO as orderNo,
        CARD_SOURCE as cardSource,
        IFNULL(ACTUAL_AMT,0)   as actualAmt,
        IFNULL(GIFT_AMT, 0)  as giftAmt
        FROM b_recharge_card_order
        <where>
            <if test="settleTimeBegin !=null and settleTimeBegin !=''">
                AND UPDATE_TIME >= #{settleTimeBegin}
            </if>
            <if test="settleTimeEnd !=null and settleTimeEnd  !=''">
                AND UPDATE_TIME <![CDATA[ <= ]]> concat(#{settleTimeEnd}, ' 23:59:59')
            </if>
            <if test="cardDetailId !=null and cardDetailId !=''">
                AND CARD_DETAIL_ID = #{cardDetailId}
            </if>
        </where>
        <if test="end!=null and end!= 0">
            limit #{begin},#{end}
        </if>
    </select>

    <select id="queryCardOrderDetailCount" parameterType="com.ls.ner.billing.gift.condition.RechargeCardCondition" resultType="int">
        SELECT
        count(1)
        FROM b_recharge_card_order
        <where>
            <if test="settleTimeBegin !=null and settleTimeBegin !=''">
                AND UPDATE_TIME >= #{settleTimeBegin}
            </if>
            <if test="settleTimeEnd !=null and settleTimeEnd  !=''">
                AND UPDATE_TIME <![CDATA[ <= ]]> concat(#{settleTimeEnd}, ' 23:59:59')
            </if>
            <if test="cardDetailId !=null and cardDetailId !=''">
                AND CARD_DETAIL_ID = #{cardDetailId}
            </if>
        </where>

    </select>

    <select id="queryCardDetailsByOne" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail" resultType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        SELECT
        CARD_DETAIL_ID  as cardDetailId,
        CARD_ID as cardId,
        CARD_BATCH_NO as cardBatchNo,
        CARD_NO as cardNo,
        CARD_SECRET as cardSecret,
        CUST_ID as custId,
        MOBILE as mobile,
        ACTUAL_AMT as actualAmt,
        GIFT_AMT as giftAmt,
        TOTAL_BALANCE as totalBalance,
        ACTUAL_BALANCE as actualBalance,
        GIFT_BALANCE as giftBalance,
        ACTIVATE_TIME as activateTime,
        CARD_DETAIL_STATUS as cardDetailStatus,
        CARD_SOURCE as cardSource,
        CREATE_TIME as createTime,
        CREATE_USER as createUser,
        UPDATE_TIME as updateTime,
        UPDATE_USER as updateUser
        FROM b_recharge_card_detail
        <where>
            <if test="cardDetailId !=null and cardDetailId  !=''">
                AND CARD_DETAIL_ID = #{cardDetailId}
            </if>
        </where>
        limit 1
    </select>


    <select id="queryRechargeCardCodeByOne" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail" resultType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        SELECT
        a.CARD_DETAIL_ID  as cardDetailId,
        a.CARD_ID as cardId,
        a.CARD_BATCH_NO as cardBatchNo,
        a.CARD_NO as cardNo,
        a.CARD_SECRET as cardSecret,
        a.CUST_ID as custId,
        a.MOBILE as mobile,
        a.ACTUAL_AMT as actualAmt,
        a.GIFT_AMT as giftAmt,
        a.TOTAL_BALANCE as totalBalance,
        a.ACTUAL_BALANCE as actualBalance,
        a.GIFT_BALANCE as giftBalance,
        a.ACTIVATE_TIME as activateTime,
        a.CARD_DETAIL_STATUS as cardDetailStatus,
        a.CARD_SOURCE as cardSource,
        a.CREATE_TIME as createTime,
        a.CREATE_USER as createUser,
        a.UPDATE_TIME as updateTime,
        a.UPDATE_USER as updateUser,
        DATE_FORMAT(b.EFT_DATE,"%Y-%m-%d %H:%i:%s") AS eftDate,
        DATE_FORMAT(b.INV_DATE,"%Y-%m-%d %H:%i:%s") AS invDate,
        b.CARD_TIME_TYPE AS cardTimeType
        FROM b_recharge_card_detail a
        LEFT JOIN b_recharge_card b on a.CARD_ID = b.CARD_ID
        <where>
            a.CARD_DETAIL_STATUS = '0'
            AND b.CARD_STATUS = '1'
            AND a.CUST_ID is null
            <if test="cardDetailId !=null and cardDetailId  !=''">
                AND a.CARD_DETAIL_ID = #{cardDetailId}
            </if>
            <if test="cardSecret !=null and cardSecret  !=''">
                AND a.CARD_SECRET = #{cardSecret}
            </if>
            <if test="cardId !=null and cardId  !=''">
                AND a.CARD_ID = #{cardId}
            </if>

        </where>
        limit 1
    </select>



    <select id="queryRechargeCard" parameterType="com.ls.ner.billing.gift.bo.RechargeCardBo" resultType="com.ls.ner.billing.gift.bo.RechargeCardBo">
        select
        CARD_ID AS cardId,
        CARD_NAME AS cardName,
        CARD_BATCH_NO AS cardBatchNo,
        IFNULL(ACTUAL_AMT,0)  AS actualAmt,
        IFNULL(GIFT_AMT,0) AS giftAmt,
        REMARK AS remark,
        CARD_STATUS AS cardStatus,
        CARD_TIME_TYPE AS cardTimeType,
        DATE_FORMAT(EFT_DATE,"%Y-%m-%d %H:%i:%s") AS eftDate,
        DATE_FORMAT(INV_DATE,"%Y-%m-%d %H:%i:%s") AS invDate,
        CREATE_TIME AS createTime,
        CREATE_USER AS createUser,
        UPDATE_TIME AS updateTime,
        UPDATE_USER AS updateUser,
        INTEGRAL_NUM as integralNum
        from b_recharge_card
        <where>
            <if test="cardName !=null and cardName !=''">
                and CARD_NAME = #{cardName}
            </if>
            <if test="cardBatchNo !=null and cardBatchNo !=''">
                and CARD_BATCH_NO = #{cardBatchNo}
            </if>
            <if test="cardStatus !=null and cardStatus  !=''">
                AND CARD_STATUS = #{cardStatus}
            </if>
            <if test="upCardId !=null and upCardId  !=''">
                AND <![CDATA[ CARD_ID <> #{upCardId} ]]>
            </if>
            <if test="cardId !=null and cardId  !=''">
                AND CARD_ID = #{cardId}
            </if>
        </where>
    </select>

    <insert id="addRechargeCard" parameterType="com.ls.ner.billing.gift.bo.RechargeCardBo" useGeneratedKeys="true" keyProperty="cardId">
        INSERT INTO b_recharge_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardName !=null and cardName !=''">CARD_NAME,</if>
            <if test="cardBatchNo !=null and cardBatchNo !=''">CARD_BATCH_NO,</if>
            <if test="actualAmt !=null">ACTUAL_AMT,</if>
            <if test="giftAmt !=null">GIFT_AMT,</if>
            <if test="remark !=null and remark !=''">REMARK,</if>
            <if test="cardStatus !=null and cardStatus !=''">CARD_STATUS,</if>
            <if test="cardTimeType !=null and cardTimeType !=''">CARD_TIME_TYPE,</if>
            <if test="eftDate !=null and eftDate !=''">EFT_DATE,</if>
            <if test="invDate !=null and invDate !=''">INV_DATE,</if>
            <if test="createUser !=null and createUser !=''">CREATE_USER,</if>
            CREATE_TIME,
            <if test="integralNum != null">INTEGRAL_NUM,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cardName !=null and cardName !=''">#{cardName},</if>
            <if test="cardBatchNo !=null and cardBatchNo !=''">#{cardBatchNo},</if>
            <if test="actualAmt !=null">#{actualAmt},</if>
            <if test="giftAmt !=null">#{giftAmt},</if>
            <if test="remark !=null and remark !=''">#{remark},</if>
            <if test="cardStatus !=null and cardStatus !=''">#{cardStatus},</if>
            <if test="cardTimeType !=null and cardTimeType !=''">#{cardTimeType},</if>
            <if test="eftDate !=null and eftDate !=''">#{eftDate},</if>
            <if test="invDate !=null and invDate !=''">#{invDate},</if>
            <if test="createUser !=null and createUser !=''">#{createUser},</if>
            now(),
            <if test="integralNum != null">#{integralNum},</if>
        </trim>
    </insert>

    <insert id="batchAddRechargeCardDetail" >
        INSERT INTO b_recharge_card_detail (
            CARD_BATCH_NO,
            CARD_ID,
            CARD_NO,
            CARD_SECRET,
            ACTUAL_AMT,
            GIFT_AMT,
            TOTAL_BALANCE,
            ACTUAL_BALANCE,
            GIFT_BALANCE,
            CARD_DETAIL_STATUS,
            CARD_SOURCE,
            CREATE_TIME,
            CREATE_USER
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.cardBatchNo},
            #{item.cardId},
            #{item.cardNo},
            #{item.cardSecret},
            #{item.actualAmt},
            #{item.giftAmt},
            #{item.totalBalance},
            #{item.actualBalance},
            #{item.giftBalance},
            #{item.cardDetailStatus},
            #{item.cardSource},
            now(),
            #{item.createUser}
        )
        </foreach>
    </insert>

    <insert id="batchAddRechargeCardDetailIntegral" >
        INSERT INTO b_recharge_card_detail (
        CARD_BATCH_NO,
        CARD_ID,
        CARD_NO,
        CARD_SECRET,
        ACTUAL_AMT,
        GIFT_AMT,
        TOTAL_BALANCE,
        ACTUAL_BALANCE,
        GIFT_BALANCE,
        CARD_DETAIL_STATUS,
        CARD_SOURCE,
        CREATE_TIME,
        CREATE_USER,
        CUST_ID,
        MOBILE,
        ACTIVATE_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cardBatchNo},
            #{item.cardId},
            #{item.cardNo},
            #{item.cardSecret},
            #{item.actualAmt},
            #{item.giftAmt},
            #{item.totalBalance},
            #{item.actualBalance},
            #{item.giftBalance},
            #{item.cardDetailStatus},
            #{item.cardSource},
            now(),
            #{item.createUser},
            #{item.custId},
            #{item.mobile},
             now()
            )
        </foreach>
    </insert>

    <update id="updateRechargeCard" parameterType="com.ls.ner.billing.gift.bo.RechargeCardBo">
        UPDATE b_recharge_card
        <set>
            <if test="cardName != null and cardName != ''">
                CARD_NAME = #{cardName},
            </if>
            <if test="remark != null and remark != ''">
                REMARK = #{remark},
            </if>
            <if test="cardStatus != null and cardStatus != ''">
                CARD_STATUS = #{cardStatus},
            </if>
            <if test="cardTimeType != null and cardTimeType != ''">
                CARD_TIME_TYPE = #{cardTimeType},
            </if>
            <if test="eftDate != null and eftDate !=''">
                EFT_DATE = #{eftDate},
            </if>
            <if test="invDate != null and invDate !='' ">
                INV_DATE = #{invDate},
            </if>
            UPDATE_USER = #{updateUser},
            <if test="integralNum != null">
                INTEGRAL_NUM = #{integralNum},
            </if>
        </set>
        WHERE CARD_ID = #{cardId}
    </update>

    <update id="updateCardDetailStatus" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        UPDATE b_recharge_card_detail
        <set>
            <if test="cardDetailStatus != null and cardDetailStatus != ''">
                CARD_DETAIL_STATUS = #{cardDetailStatus},
            </if>
            UPDATE_USER = #{updateUser}
        </set>
        WHERE CARD_DETAIL_ID = #{cardDetailId}
    </update>

    <update id="updateCardDetailCustId" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        UPDATE b_recharge_card_detail
        <set>
            <if test="custId != null and custId != ''">
                CUST_ID = #{custId},
            </if>
            <if test="mobile != null and mobile != ''">
                MOBILE = #{mobile},
            </if>
            <if test="cardDetailStatus != null and cardDetailStatus != ''">
                CARD_DETAIL_STATUS = #{cardDetailStatus},
            </if>
            ACTIVATE_TIME = now()
        </set>
        WHERE CARD_DETAIL_ID = #{cardDetailId}
    </update>

    <update id="updateCardDetailAmt" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        UPDATE b_recharge_card_detail
        <set>
            <if test="actualBalance != null">
                ACTUAL_BALANCE = #{actualBalance},
            </if>
            <if test="giftBalance != null">
                GIFT_BALANCE = #{giftBalance},
            </if>
            <if test="cardDetailStatus != null and cardDetailStatus != ''">
                CARD_DETAIL_STATUS = #{cardDetailStatus}
            </if>
        </set>
        WHERE CARD_DETAIL_ID = #{cardDetailId}
    </update>



    <select id="myRechargeCardInfos" parameterType="com.ls.ner.billing.gift.bo.RechargeCardDetail" resultType="com.ls.ner.billing.gift.bo.RechargeCardDetail">
        SELECT
        d.CARD_DETAIL_ID  as cardDetailId,
        a.CARD_NAME as cardName,
        a.CARD_ID as cardId,
        d.CARD_NO as cardNo,
        d.CUST_ID as custId,
        d.MOBILE as mobile,
        (COALESCE(d.ACTUAL_AMT, 0) + COALESCE(d.GIFT_AMT, 0)) as totalAmt,
        d.ACTUAL_AMT as actualAmt,
        d.GIFT_AMT as giftAmt,
        (COALESCE(d.ACTUAL_BALANCE, 0) + COALESCE(d.GIFT_BALANCE, 0)) as totalBalance,
        d.ACTUAL_BALANCE as actualBalance,
        d.GIFT_BALANCE as giftBalance,
        d.ACTIVATE_TIME as activateTime,
        d.CARD_DETAIL_STATUS as cardDetailStatus,
        CASE
        WHEN d.CARD_DETAIL_STATUS = '0' THEN '待激活'
        WHEN d.CARD_DETAIL_STATUS = '1' THEN '已激活'
        WHEN d.CARD_DETAIL_STATUS = '2' THEN '已冻结'
        WHEN d.CARD_DETAIL_STATUS = '3' THEN '已退款'
        ELSE '使用完毕' END cardDetailStatusName
        FROM b_recharge_card_detail d
        left join b_recharge_card a on d.CARD_ID = a.CARD_ID
        <where>
            <if test="mobile !=null and mobile !=''">
                and d.MOBILE like concat('%',#{mobile},'%')
            </if>
            <if test="custId !=null and custId !=''">
                and d.CUST_ID = #{custId}
            </if>
            <if test="cardNo !=null and cardNo !=''">
                and d.CARD_NO like concat('%',#{cardNo},'%')
            </if>
            <if test="cardSecret !=null and cardSecret !=''">
                and d.CARD_SECRET like concat('%',#{cardSecret},'%')
            </if>
            <if test="cardDetailStatus !=null and cardDetailStatus  !=''">
                AND d.CARD_DETAIL_STATUS = #{cardDetailStatus}
            </if>
        </where>
        order by d.CARD_DETAIL_STATUS asc
    </select>


    <select id="getRechargeCardList"  resultType="java.util.Map">
        select
            CARD_ID AS cardId,
            CARD_NAME AS cardName,
            CARD_TIME_TYPE as cardTimeType,
            DATE_FORMAT(EFT_DATE,"%Y-%m-%d %H:%i:%s") AS eftDate,
            DATE_FORMAT(INV_DATE,"%Y-%m-%d %H:%i:%s") AS invDate
        from b_recharge_card
        where
            CARD_STATUS = '1'
    </select>

    <insert id="insertRechargeCardOrder" parameterType="com.ls.ner.billing.gift.bo.RechargeCardOrder">
        INSERT INTO b_recharge_card_order (
            CARD_ID,
            CARD_DETAIL_ID,
            CARD_SOURCE,
            ORDER_NO,
            ACTUAL_AMT,
            GIFT_AMT
        ) VALUES (
             #{cardId},
             #{cardDetailId},
             #{cardSource},
             #{orderNo},
             #{actualAmt},
             #{giftAmt}
         )
    </insert>


    <select id="queryCardOrderGroupByCardId" parameterType="com.ls.ner.billing.gift.bo.RechargeCardOrder" resultType="com.ls.ner.billing.gift.bo.RechargeCardOrder">
        SELECT
        CARD_ID as cardId,
        IFNULL(sum(COALESCE(ACTUAL_AMT, 0)),0)   as actualAmt,
        IFNULL(sum(COALESCE(GIFT_AMT, 0)),0)  as giftAmt
        FROM b_recharge_card_order
        <where>
            <if test="settleTimeBegin !=null and settleTimeBegin !=''">
                AND UPDATE_TIME >= #{settleTimeBegin}
            </if>
            <if test="settleTimeEnd !=null and settleTimeEnd  !=''">
                AND UPDATE_TIME <![CDATA[ <= ]]> concat(#{settleTimeEnd}, ' 23:59:59')
            </if>

        </where>
        group by CARD_ID
    </select>

    <select id="queryCardOrderGroupByCardDetailId" parameterType="com.ls.ner.billing.gift.bo.RechargeCardOrder" resultType="com.ls.ner.billing.gift.bo.RechargeCardOrder">
        SELECT
        CARD_DETAIL_ID as cardDetailId,
        IFNULL(sum(COALESCE(ACTUAL_AMT, 0)),0)   as actualAmt,
        IFNULL(sum(COALESCE(GIFT_AMT, 0)),0)  as giftAmt
        FROM b_recharge_card_order
        <where>
            <if test="settleTimeBegin !=null and settleTimeBegin !=''">
                AND UPDATE_TIME >= #{settleTimeBegin}
            </if>
            <if test="settleTimeEnd !=null and settleTimeEnd  !=''">
                AND UPDATE_TIME <![CDATA[ <= ]]> concat(#{settleTimeEnd}, ' 23:59:59')
            </if>

        </where>
        group by CARD_DETAIL_ID
    </select>

    <select id="queryCardOrderByOrderNo"  parameterType="java.util.Map"  resultType="java.util.Map">
        SELECT
        ORDER_NO as orderNo,
        IFNULL(sum(COALESCE(ACTUAL_AMT, 0)),0)  as actualAmt
        FROM b_recharge_card_order
        <where>
            <if test="orderNoList != null and  orderNoList.size() > 0">
                and  ORDER_NO in(
                <foreach collection="orderNoList" item="item" index="index" separator="," >
                    #{item}
                </foreach>
                )
            </if>
            <if test="cardSource !=null and cardSource !=''">
                AND CARD_SOURCE = #{cardSource}
            </if>
        </where>
        group by ORDER_NO
    </select>

    <select id="cardOrderNoList" resultType="java.lang.String">
        SELECT ORDER_NO as orderNo
        FROM b_recharge_card_order
        <where>
            <if test="cardId != null and cardId != ''">
                CARD_ID = #{cardId}
            </if>
            <if test="cardDetailId != null and cardDetailId != ''">
                AND CARD_DETAIL_ID = #{cardDetailId}
            </if>
        </where>
    </select>


    <select id="queryCardInfosByChargeOrderList"  parameterType="java.util.Map"  resultType="java.util.Map">
        SELECT
            a.ORDER_NO orderNo,
            GROUP_CONCAT( b.CARD_NO ) AS cardNo,
            GROUP_CONCAT( ifnull( a.ACTUAL_AMT,0) ) AS cardActAmt,
            GROUP_CONCAT( ifnull( a.GIFT_AMT,0) ) AS cardGiftAmt
        FROM
            b_recharge_card_order a
                LEFT JOIN b_recharge_card_detail b ON a.CARD_DETAIL_ID = b.CARD_DETAIL_ID
        <where>
            b.CARD_NO is not null
            and
            <![CDATA[ ( ifnull( a.ACTUAL_AMT,0)  <> 0 or  ifnull( a.GIFT_AMT,0) <> 0 )]]>
            <if test="orderNoList != null and orderNoList.size() > 0">
                AND a.ORDER_NO IN
                <foreach collection="orderNoList" item="item"  open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderNo !=null and orderNo !=''">
                AND a.ORDER_NO = #{orderNo}
            </if>
        </where>
        GROUP BY
            a.ORDER_NO
    </select>


    <select id="getOrderListByCardNo"  parameterType="java.util.Map"  resultType="java.lang.String">
        SELECT
            a.ORDER_NO
        FROM
            b_recharge_card_order a
            LEFT JOIN b_recharge_card_detail b ON a.CARD_DETAIL_ID = b.CARD_DETAIL_ID
        where
            b.CARD_NO is not null
            and
            <![CDATA[ ( ifnull( a.ACTUAL_AMT,0)  <> 0 or  ifnull( a.GIFT_AMT,0) <> 0 )]]>
            and
            b.CARD_NO like CONCAT('%' ,#{cardNo},'%')
        GROUP BY
            a.ORDER_NO
    </select>
</mapper>
