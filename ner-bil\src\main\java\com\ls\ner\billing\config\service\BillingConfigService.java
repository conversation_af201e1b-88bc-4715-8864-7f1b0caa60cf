package com.ls.ner.billing.config.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.api.rent.model.BillingFunc;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.common.bo.TariffPlanBo;
import com.ls.ner.billing.config.bo.BillingConfigForm;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.service.impl.BillingConfigServiceImpl;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceType;

@Service(target = { ServiceType.APPLICATION }, value = "billingConfigService")
public class BillingConfigService implements IBillingConfigService {

	@Autowired
	BillingConfigServiceImpl billingConfigServiceImpl;

	public BillingConfigForm initFormByPlanNo(String planNo) {
		return billingConfigServiceImpl.initFormByPlanNo(planNo);
	}

	public BillingFunc tariffPlanToBillingFunc(TariffPlanBo tariffPlanBo) {
		return billingConfigServiceImpl.tariffPlanToBillingFunc(tariffPlanBo);
	}

	public BillingConfigForm getFormByBillingNo(String billingNo) {
		return billingConfigServiceImpl.getFormByBillingNo(billingNo);
	}

	public List<BillingConfigBo> pagi(BillingConfigQueryCondition q) {
		return billingConfigServiceImpl.pagi(q);
	}

	public String create(BillingConfigForm form) {
		return billingConfigServiceImpl.create(form);
	}

	public void update(BillingConfigForm form) {
		billingConfigServiceImpl.update(form);
	}

	@Override
	public void deleteBillings(String[] ids) {
		billingConfigServiceImpl.deleteBillings(ids);
	}

	@Override
	public String createAdjust(BillingConfigForm form) {
		return billingConfigServiceImpl.createAdjust(form);
	}

	@Override
	public String copyConfig(BillingConfigBo bo) {
		return billingConfigServiceImpl.copyConfig(bo);
	}

	@Override
	public List<BillingConfigBo> getAutoBillingList(BillingConfigQueryCondition condition) {
		return billingConfigServiceImpl.getAutoBillingList(condition);
	}
	
	
}
