package com.ls.ner.billing.vip.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.vip.bo.VipRPCBo;
import com.ls.ner.billing.gift.constant.IntegralEnum;
import com.ls.ner.billing.market.bo.*;
import com.ls.ner.billing.market.dao.ICouponPutDao;
import com.ls.ner.billing.market.dao.IIntegeralCustRPCDao;
import com.ls.ner.billing.market.service.IIntegeralCustService;
import com.ls.ner.billing.market.service.impl.CouponPutServiceImpl;
import com.ls.ner.billing.market.vo.ExclusiveOfferVo;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.billing.vip.bo.RegularVipRankBo;
import com.ls.ner.billing.vip.bo.VipLevelBo;
import com.ls.ner.billing.vip.bo.VipLevelCalcConfig;
import com.ls.ner.billing.vip.dao.IMemberBenefitsDao;
import com.ls.ner.billing.vip.service.IMemberBenefitsService;
import com.ls.ner.billing.vip.service.IVipLevelCalcConfigService;
import com.ls.ner.billing.vip.service.IVipLevelService;
import com.ls.ner.def.api.account.service.IDefrayAccountRpcService;
import com.ls.ner.def.api.market.service.ICouponRpcService;
import com.ls.ner.order.api.service.IChargeOrderRpcService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.MathUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.common.utils.tools.DateUtil;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
@Service(target = {ServiceType.LOCAL}, value = "vipLevelService")
public class VipLevelServiceImpl implements IVipLevelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(VipLevelServiceImpl.class);

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IChargeOrderRpcService chargeOrderRpcService;

    @Autowired
    private IMemberBenefitsDao memberBenefitsDao;

    @Autowired(required = true)
    private IIntegeralCustRPCDao integeralCustRPCDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "seqRpcService")
    private ISeqRpcService seqRpcService;

    @ServiceAutowired(value="memberBenefitsService")
    private IMemberBenefitsService memberBenefitsService;

    @ServiceAutowired(value = "vipLevelCalcConfigService", serviceTypes = ServiceType.LOCAL)
    private IVipLevelCalcConfigService configService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC,value = "defaryAccountRpcService")
    private IDefrayAccountRpcService defrayAccountRpcService;

    @Autowired(required = true)
    private ICouponPutDao putDao; // 发放主表

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "accuCouponRpcService")
    private ICouponRpcService accuCouponRpcService; // 支付中心-优惠券接口

    @ServiceAutowired(value = "IntegeralCustService", serviceTypes = ServiceType.LOCAL)
    private IIntegeralCustService integeralCustService;

    //轻享
    private static final List<String> LIGHT_VIP = Collections.unmodifiableList(
            Arrays.asList("01", "02", "03", "04")
    );
    //尊享
    private static final List<String> NOBLE_VIP = Collections.unmodifiableList(
            Arrays.asList("05", "06", "07", "08")
    );

    @Override
    public String updateVipLevel(VipRPCBo vipRPCBo) {
        //获取当前会员等级配置
        VipLevelCalcConfig config = configService.getLatestConfig();
        if (config == null || StringUtils.isEmpty(config.getCalcMode())) {
            return "1";
        }
        //查询当前用户的累计充电量
        String custId = vipRPCBo.getCustId();
        Map chargePqMap = chargeOrderRpcService.getChargePqByCustId(custId);
        BigDecimal chargePq = (BigDecimal) chargePqMap.get("chargePq");
        BigDecimal chargeAmt = (BigDecimal) chargePqMap.get("chargeAmt");

        LOGGER.error("会员:{},结算获取用户当前充电量:{},充电金额:{}",custId,chargePq,chargeAmt);
        //查询当前用户会员等级
        Map<String, Object> levelMap = new HashMap<>();
        if ("quantity".equals(config.getCalcMode())){
            levelMap.put("chargePq",chargePq.add(vipRPCBo.getChargePq()));
        }else if ("amount".equals(config.getCalcMode())){
            levelMap.put("chargeAmt",chargeAmt.add(vipRPCBo.getVipPrice()));
        }

        RegularVipRankBo regularVipLevel = memberBenefitsDao.getRegularVipLevel(levelMap);

        VipLevelBo userVipLevel = memberBenefitsDao.getUserVipLevel(custId);
        if (userVipLevel==null){
            VipLevelBo bo = new VipLevelBo();
            bo.setChargeTime(DateUtil.dateTimeToStr(new Date()));
            bo.setCustId(custId);
            bo.setMobile(vipRPCBo.getMobile());
            bo.setVipLevel(regularVipLevel.getRankNo());
            memberBenefitsDao.insertVipLevel(bo);
        }else {
            if (!regularVipLevel.getRankNo().equals(userVipLevel.getVipLevel())){
                //升级
                userVipLevel.setVipLevel(regularVipLevel.getRankNo());
                userVipLevel.setChargeTime(DateUtil.dateTimeToStr(new Date()));
                userVipLevel.setLevelFlag("1");
                memberBenefitsDao.updateVipLevel(userVipLevel);
                //判断用户是否付费会员
                Map<String, Object> inMap = new HashMap();
                inMap.put("custId", custId);
                Map<String,Object> accountMap = defrayAccountRpcService.qryAccount(inMap);
                Boolean vipFlag = true;
                if (null != accountMap.get("vipExpireTime")) {
                    String vipExpireTimeStr = StringUtil.nullToString(accountMap.get("vipExpireTime"));
                    LocalDateTime vipExpireTime = LocalDateTime.parse(vipExpireTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime now = LocalDateTime.now();
                    if (vipExpireTime.compareTo(now) > 0) {
                        vipFlag = false;
                    }
                }
                MemberBenefitsBo benefitsBo = new MemberBenefitsBo();
                if (vipFlag){
                    //不是付费会员 获取用户是否有升级礼包
                    MemberBenefitsBo memberBenefitsBo = new MemberBenefitsBo();
                    memberBenefitsBo.setCustId(custId);
                    memberBenefitsBo.setIsUpgradePackage("1");
                    List<MemberBenefitsBo> benefitsUser = memberBenefitsDao.getBenefitsUser(memberBenefitsBo);
                    if (CollectionUtils.isNotEmpty(benefitsUser)){
                        benefitsBo = benefitsUser.get(0);
                    }
                }else {
                    String custVipType = integeralCustService.getCustVipType(custId.toString());
                    MemberBenefitsBo req = new MemberBenefitsBo();
                    req.setVipType("02");
                    if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
                        req.setVipLevel("6");
                    }else {
                        req.setVipLevel("5");
                    }
                    benefitsBo = memberBenefitsDao.getMemberBenefits(req);
                    if (!benefitsBo.getIsUpgradePackage().equals("1")){
                        LOGGER.error("用户升级未获取到升级礼包:{}", JSON.toJSONString(benefitsBo));
                        return regularVipLevel.getRankNo();
                    }
                }
                String integralNumber = Integer.toString(benefitsBo.getUpgradePackage());
                //赠送积分
                IntegeralCustBo bo = new IntegeralCustBo();
                IntegeralCustLogBo logBo = new IntegeralCustLogBo();
                bo.setCustNo(custId);
                int count = integeralCustRPCDao.ixExistsCustNum(bo);
                //记录积分流水
                if (count>0) {
                    //用户已有积分,更新用户积分
                    //获取用户积分
                    String num = integeralCustRPCDao.getCustIntegeralNum(bo);
                    bo.setIntegralNumber(MathUtils.add(integralNumber,num));
                    logBo.setIntegralNumber(MathUtils.add(integralNumber,num));
                    integeralCustRPCDao.updateIntegeralCust(bo);
                } else {
                    //添加用户积分
                    bo.setIntegralNumber(integralNumber);
                    logBo.setIntegralNumber(integralNumber);
                    integeralCustRPCDao.addIntegeralCust(bo);
                }
                logBo.setIntegralNo(seqRpcService.getAppNo());//积分流水号
                logBo.setPhone(vipRPCBo.getMobile());
                logBo.setChargeNum(integralNumber);
                logBo.setEventName(IntegralEnum.PAID_MEMBERSHIP_UPGRADE_PACKAGE.getName());
                logBo.setEventType(IntegralEnum.PAID_MEMBERSHIP_UPGRADE_PACKAGE.getCode());
                logBo.setChargeType("01");
                integeralCustRPCDao.addIntegeralCustLog(logBo);

                MemberBenefitsBo req = new MemberBenefitsBo();
                req.setVipLevel(regularVipLevel.getRankNo());
                MemberBenefitsBo memberBenefits = memberBenefitsDao.getMemberBenefits(req);

                //专享优惠
                if (memberBenefits!=null&&!memberBenefits.getIsExclusiveOffer().equals("0")&&StringUtil.isNotEmpty(memberBenefits.getExclusiveOffer())&&!memberBenefits.getExclusiveOffer().equals("[]")){
                    Map<String, String> custMap = new HashMap<>();
                    custMap.put("custId", vipRPCBo.getCustId());
                    custMap.put("mobile", vipRPCBo.getMobile());
                    List<Map<String, String>> list = new ArrayList<>();
                    list.add(custMap);
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        List<ExclusiveOfferVo> coupons = objectMapper.readValue(memberBenefits.getExclusiveOffer(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
                        Map<String, CouponBo> collect = memberBenefitsDao.queryCpnById(coupons).stream()
                                .collect(Collectors.toMap(CouponBo::getCpnId, Function.identity()));
                        for (ExclusiveOfferVo coupon : coupons) {
                            Integer couponNum = Integer.valueOf(coupon.getCouponNum());
                            CouponBo couponBo = collect.get(coupon.getCouponId());
                            for (Integer i = 0; i < couponNum; i++) {
                                CouponPutBo putBo = new CouponPutBo();
                                putBo.setCpnId(coupon.getCouponId());
                                putBo.setGetChannel("02");
                                putBo.setBusiType("02");
                                putBo.setLimGetNum(coupon.getLimit());
                                putBo.setInvDate(couponBo.getInvDate());
                                putBo.setEftDate(couponBo.getEftDate());
                                putBo.setTimeDuration(couponBo.getTimeDuration());
                                putBo.setTimeUnit(couponBo.getTimeUnit());
                                putBo.setCpnTimeType(couponBo.getCpnTimeType());
                                put(putBo, list);
                            }
                        }

                    } catch (Exception e) {
                        LOGGER.error("会员升级专享优惠发券异常:{}", e);
                    }
                }

                //开卡礼
                if (memberBenefits!=null&&!memberBenefits.getIsWelcomeGift().equals("0")&&StringUtil.isNotEmpty(memberBenefits.getWelcomeGift())&&!memberBenefits.getWelcomeGift().equals("[]")){
                    Map<String, String> custMap = new HashMap<>();
                    custMap.put("custId", vipRPCBo.getCustId());
                    custMap.put("mobile", vipRPCBo.getMobile());
                    List<Map<String, String>> list = new ArrayList<>();
                    list.add(custMap);
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        List<ExclusiveOfferVo> coupons = objectMapper.readValue(memberBenefits.getWelcomeGift(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
                        Map<String, CouponBo> collect = memberBenefitsDao.queryCpnById(coupons).stream()
                                .collect(Collectors.toMap(CouponBo::getCpnId, Function.identity()));
                        for (ExclusiveOfferVo coupon : coupons) {
                            Integer couponNum = Integer.valueOf(coupon.getCouponNum());
                            CouponBo couponBo = collect.get(coupon.getCouponId());
                            for (Integer i = 0; i < couponNum; i++) {
                                CouponPutBo putBo = new CouponPutBo();
                                putBo.setCpnId(coupon.getCouponId());
                                putBo.setGetChannel("02");
                                putBo.setBusiType("02");
                                putBo.setLimGetNum(coupon.getLimit());
                                putBo.setInvDate(couponBo.getInvDate());
                                putBo.setEftDate(couponBo.getEftDate());
                                putBo.setTimeDuration(couponBo.getTimeDuration());
                                putBo.setTimeUnit(couponBo.getTimeUnit());
                                putBo.setCpnTimeType(couponBo.getCpnTimeType());
                                put(putBo, list);
                            }
                        }

                    } catch (Exception e) {
                        LOGGER.error("会员升级开卡礼发券异常:{}", e);
                    }
                }

            }
        }
        return regularVipLevel.getRankNo();
    }


    /**
     * 会员升级礼包
     * @param vipRPCBo
     */
    @Override
    public void upgradeGift(VipRPCBo vipRPCBo) {
        LOGGER.error("会员升级礼包入参:{}",  JSONObject.toJSONString(vipRPCBo));
        String custId = vipRPCBo.getCustId();
        String custVipType = integeralCustService.getCustVipType(custId);
        MemberBenefitsBo req = new MemberBenefitsBo();
        req.setVipType("02");
        if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
            req.setVipLevel("6");
        }else {
            req.setVipLevel("5");
        }
        MemberBenefitsBo benefitsBo = memberBenefitsDao.getMemberBenefits(req);
        if (!benefitsBo.getIsUpgradePackage().equals("1")){
            LOGGER.error("未获取到会员升级礼包:{}", JSON.toJSONString(benefitsBo));
            return ;
        }
        String integralNumber = Integer.toString(benefitsBo.getUpgradePackage());
        //赠送积分
        IntegeralCustBo bo = new IntegeralCustBo();
        IntegeralCustLogBo logBo = new IntegeralCustLogBo();
        bo.setCustNo(custId);
        int count = integeralCustRPCDao.ixExistsCustNum(bo);
        //记录积分流水
        if (count>0) {
            //用户已有积分,更新用户积分
            //获取用户积分
            String num = integeralCustRPCDao.getCustIntegeralNum(bo);
            bo.setIntegralNumber(MathUtils.add(integralNumber,num));
            logBo.setIntegralNumber(MathUtils.add(integralNumber,num));
            integeralCustRPCDao.updateIntegeralCust(bo);
        } else {
            //添加用户积分
            bo.setIntegralNumber(integralNumber);
            logBo.setIntegralNumber(integralNumber);
            integeralCustRPCDao.addIntegeralCust(bo);
        }
        logBo.setIntegralNo(seqRpcService.getAppNo());//积分流水号
        logBo.setPhone(vipRPCBo.getMobile());
        logBo.setChargeNum(integralNumber);
        logBo.setEventName(IntegralEnum.PAID_MEMBERSHIP_UPGRADE_PACKAGE.getName());
        logBo.setEventType(IntegralEnum.PAID_MEMBERSHIP_UPGRADE_PACKAGE.getCode());
        logBo.setChargeType("01");
        integeralCustRPCDao.addIntegeralCustLog(logBo);
    }

    @Override
    public String getLevelFlag(VipRPCBo vipRPCBo) {
        //获取当前会员等级配置
        VipLevelCalcConfig config = configService.getLatestConfig();
        if (config == null || StringUtils.isEmpty(config.getCalcMode())) {
            return "1";
        }
        String custId = vipRPCBo.getCustId();
        VipLevelBo levelFlag = memberBenefitsDao.getLevelFlag(custId);
        if (levelFlag!=null){
            return levelFlag.getLevelFlag();
        }else {
            Map chargePqMap = chargeOrderRpcService.getChargePqByCustId(custId);
            BigDecimal chargePq = (BigDecimal) chargePqMap.get("chargePq");
            BigDecimal chargeAmt = (BigDecimal) chargePqMap.get("chargeAmt");

            //查询当前用户会员等级
            Map<String, Object> levelMap = new HashMap<>();
            if ("quantity".equals(config.getCalcMode())){
                levelMap.put("chargePq",chargePq.add(vipRPCBo.getChargePq()));
            }else if ("amount".equals(config.getCalcMode())){
                levelMap.put("chargeAmt",chargeAmt.add(vipRPCBo.getVipPrice()));
            }

            //查询当前用户会员等级
            RegularVipRankBo regularVipLevel = memberBenefitsDao.getRegularVipLevel(levelMap);
            VipLevelBo bo = new VipLevelBo();
            bo.setChargeTime(DateUtil.dateTimeToStr(new Date()));
            bo.setCustId(custId);
            bo.setMobile(vipRPCBo.getMobile());
            bo.setVipLevel(regularVipLevel.getRankNo());
            memberBenefitsDao.insertVipLevel(bo);
            return regularVipLevel.getRankNo();
        }
    }

    @Override
    public Map updateLevelFlag(VipRPCBo vipRPCBo) {
        Map resultMap = new HashedMap();
        VipLevelBo bo = new VipLevelBo();
        bo.setCustId(vipRPCBo.getCustId());
        bo.setLevelFlag("0");
        memberBenefitsDao.updateLevelFlag(bo);
        resultMap.put("ret",200);
        resultMap.put("msg","更新成功");
        return resultMap;
    }

    /**
     * 会员开卡礼
     * @param vipRPCBo
     */
    @Override
    public void openGift(VipRPCBo vipRPCBo) {
        LOGGER.error("会员开卡礼入参:{}",  JSONObject.toJSONString(vipRPCBo));
        String custId = vipRPCBo.getCustId();
        String custVipType = integeralCustService.getCustVipType(custId);
        MemberBenefitsBo req = new MemberBenefitsBo();
        req.setVipType("02");
        if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
            req.setVipLevel("6");
        }else {
            req.setVipLevel("5");
        }
        MemberBenefitsBo bo = memberBenefitsDao.getMemberBenefits(req);
        if (bo.getWelcomeGift() != null) {
            Map<String, String> custMap = new HashMap<>();
            custMap.put("custId", custId);
            custMap.put("mobile", vipRPCBo.getMobile());
            List<Map<String, String>> list = new ArrayList<>();
            list.add(custMap);
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                List<ExclusiveOfferVo> coupons = objectMapper.readValue(bo.getWelcomeGift(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
                Map<String, CouponBo> collect = memberBenefitsDao.queryCpnById(coupons).stream()
                        .collect(Collectors.toMap(CouponBo::getCpnId, Function.identity()));
                for (ExclusiveOfferVo coupon : coupons) {
                    Integer couponNum = Integer.valueOf(coupon.getCouponNum());
                    CouponBo couponBo = collect.get(coupon.getCouponId());
                    for (Integer i = 0; i < couponNum; i++) {
                        CouponPutBo putBo = new CouponPutBo();
                        putBo.setCpnId(coupon.getCouponId());
                        putBo.setGetChannel("02");
                        putBo.setBusiType("02");
                        putBo.setLimGetNum(coupon.getLimit());
                        putBo.setInvDate(couponBo.getInvDate());
                        putBo.setEftDate(couponBo.getEftDate());
                        putBo.setTimeDuration(couponBo.getTimeDuration());
                        putBo.setTimeUnit(couponBo.getTimeUnit());
                        putBo.setCpnTimeType(couponBo.getCpnTimeType());
                        put(putBo, list);
                    }
                }

            } catch (Exception e) {
                LOGGER.error("会员开卡礼发券异常:{}", e);
            }
        }
    }

    /**
     * 会员专享优惠
     * @param vipRPCBo
     */
    @Override
    public void exclusiveOffer(VipRPCBo vipRPCBo) {
        LOGGER.error("会员专享优惠入参:{}",  JSONObject.toJSONString(vipRPCBo));
        String custId = vipRPCBo.getCustId();
        String custVipType = integeralCustService.getCustVipType(custId);
        MemberBenefitsBo req = new MemberBenefitsBo();
        req.setVipType("02");
        if (StringUtil.isNotEmpty(custVipType)&&NOBLE_VIP.contains(custVipType)){
            req.setVipLevel("6");
        }else {
            req.setVipLevel("5");
        }

        MemberBenefitsBo bo = memberBenefitsDao.getMemberBenefits(req);
        if (bo.getExclusiveOffer() != null) {
            Map<String, String> custMap = new HashMap<>();
            custMap.put("custId", custId);
            custMap.put("mobile", vipRPCBo.getMobile());
            List<Map<String, String>> list = new ArrayList<>();
            list.add(custMap);
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                List<ExclusiveOfferVo> coupons = objectMapper.readValue(bo.getExclusiveOffer(), objectMapper.getTypeFactory().constructCollectionType(List.class, ExclusiveOfferVo.class));
                Map<String, CouponBo> collect = memberBenefitsDao.queryCpnById(coupons).stream()
                        .collect(Collectors.toMap(CouponBo::getCpnId, Function.identity()));
                for (ExclusiveOfferVo coupon : coupons) {
                    Integer couponNum = Integer.valueOf(coupon.getCouponNum());
                    CouponBo couponBo = collect.get(coupon.getCouponId());
                    for (Integer i = 0; i < couponNum; i++) {
                        CouponPutBo putBo = new CouponPutBo();
                        putBo.setCpnId(coupon.getCouponId());
                        putBo.setGetChannel("02");
                        putBo.setBusiType("02");
                        putBo.setLimGetNum(coupon.getLimit());
                        putBo.setInvDate(couponBo.getInvDate());
                        putBo.setEftDate(couponBo.getEftDate());
                        putBo.setTimeDuration(couponBo.getTimeDuration());
                        putBo.setTimeUnit(couponBo.getTimeUnit());
                        putBo.setCpnTimeType(couponBo.getCpnTimeType());
                        put(putBo, list);
                    }
                }

            } catch (Exception e) {
                LOGGER.error("会员专享优惠发券异常:{}", e);
            }
        }
    }

    public void put(CouponPutBo bo, List<Map<String, String>> custList) {
        Map<String, String> retMap = new HashMap<String, String>();
        StringBuffer outMsg = new StringBuffer();
        if (custList != null && custList.size() > 0) {
            try {
                //优惠券id
                String cpnId = bo.getCpnId();
                //每人限领
                String limGetNum = bo.getLimGetNum();
                String putTime = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                bo.setPutTime(putTime);
                // 生效~失效时间计算
                String eftDate = "", invDate = "";
                Map<String, String> timeMap = this.calcTimeRanges(bo);
                if (timeMap != null && timeMap.size() > 0) {
                    String outCode = timeMap.get("outCode");
                    if ("0".equals(outCode)) {
                        eftDate = timeMap.get("eftDate");
                        invDate = timeMap.get("invDate");
                    } else {
                        throw new BusinessWarning(timeMap.get("outMsg"), this.getClass());
                    }
                }
                // 新增发放
                bo.setPutEmpType("01");
                bo.setPutNum(custList.size() + "");
                String getChannel = bo.getGetChannel();
                if (StringUtil.isBlank(getChannel)) {
                    getChannel = "01";
                }
                bo.setPutChannel(getChannel);
                int flag = putDao.insertCouponPut(bo);
                // 调用RPC支付中心 RPC04-02-003优惠券发放
                String putId = bo.getPutId();
                if (StringUtil.isNotBlank(putId)) {
                    //  新增发放明细
                    for (Map<String, String> map : custList) {
                        CouponPutDetBo det = new CouponPutDetBo();
                        det.setPutId(putId);
                        det.setPutTime(putTime);
                        String detCustId = map.get("custId");
                        String detMobile = map.get("mobile");
                        if (StringUtil.isBlank(detCustId) && StringUtil.isBlank(detMobile)) {
                            continue;
                        }
                        if (StringUtil.isBlank(detCustId)) detCustId = null;
                        det.setCustId(detCustId);
                        det.setMobile(detMobile);
                        det.setPutFlag("0");
                        det.setFailReason("未调用【RPC04-02-003优惠券发放】接口");
                        putDao.insertCouponPutDet(det);
                    }
                    Map<String, Object> inMap = new HashMap<String, Object>();
                    inMap.put("cpnId", cpnId);
                    inMap.put("limGetNum", limGetNum);
                    inMap.put("eftDate", eftDate);
                    inMap.put("invDate", invDate);
                    inMap.put("putTime", putTime);
                    inMap.put("custList", custList);
                    inMap.put("getChannel", "02");//02现场，领取渠道channel，存d_account_coupon表的领取来源GET_CHANNEL
                    inMap.put("handFlag", "01");
                    // add biaoxiangd 2017-09-27 添加业务类01租车02充电
                    String prodBusiType = bo.getBusiType();
                    inMap.put("prodBusiType", prodBusiType);
                    inMap.put("getSource", "09");//09优惠券发放，领取来源actType，存d_account_coupon表的领取来源GET_SOURCE
                    LOGGER.error("会员发券入参:{}", inMap);

                    // RPC04-02-003优惠券发放
                    Map<String, Object> prcMap = accuCouponRpcService.couponPut(inMap);
                    if (prcMap != null && prcMap.size() > 0) {
                        String putNum = String.valueOf(prcMap.get("putNum"));
                        if (StringUtil.isNotBlank(putNum) && StringUtils.isNumeric(putNum)) {
                            int failCount = 0;
                            if (Integer.parseInt(putNum) > 0) {
                                outMsg.append("发送成功笔数【" + putNum + "】;");
                            }
                            // 更新发放主表的发放数量
                            bo.setPutNum(putNum);
                            flag = putDao.updateCouponPut(bo);
                            if (flag > 0) {
                                // 更新发放详细表的成功标志
                                List<Map<String, Object>> prcCustList = (List<Map<String, Object>>) prcMap.get("custList");
                                if (prcCustList != null && prcCustList.size() > 0) {
                                    for (int i = 0; i < prcCustList.size(); i++) {
                                        Map<String, Object> prcCustMap = prcCustList.get(i);
                                        if (prcCustMap != null && prcCustMap.size() > 0) {
                                            String custId = (String) prcCustMap.get("custId");
                                            String mobile = (String) prcCustMap.get("mobile");
                                            if (StringUtil.isBlank(custId) && StringUtil.isBlank(mobile)) {
                                                continue;
                                            } else {
                                                String putFlag = (String) prcCustMap.get("putFlag");
                                                if (StringUtil.isNotBlank(putFlag)) {
                                                    CouponPutDetBo det = new CouponPutDetBo();
                                                    det.setPutId(putId);
                                                    det.setCustId(custId);
                                                    det.setMobile(mobile);
                                                    det.setPutFlag(putFlag);
                                                    det.setFailReason((String) prcCustMap.get("failReason"));
                                                    putDao.updateCouponPutDet(det);
                                                    if ("0".equals(putFlag)) {
                                                        failCount++;
//                                                                outMsg.append("发放失败原因【" + String.valueOf(prcCustMap.get("failReason")) + "】");
                                                    } else {
                                                        //todo 发放成功后 通知接口
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    if (failCount > 0) {
                                        outMsg.append("发送失败笔数【" + failCount + "】,详情请点击【发送数量】");
                                    }
                                } else {
                                    throw new BusinessWarning("RPC04-02-003优惠券发放接口返回用户集为空，请核查", this.getClass());
                                }
                            } else {
                                throw new BusinessWarning("RPC04-02-003优惠券发放接口更新发放信息失败，请核查", this.getClass());
                            }
                        } else {
                            throw new BusinessWarning("RPC04-02-003优惠券发放接口获取已领数量数据失败，请核查", this.getClass());
                        }
                    } else {
                        throw new BusinessWarning("RPC04-02-003优惠券发放接口获取数据失败，请核查", this.getClass());
                    }

                } else {
                    // 抛异常，发放主表生成失败
                    throw new BusinessWarning("未生成发放标识，请核查", this.getClass());
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessWarning(e.getLocalizedMessage(), CouponPutServiceImpl.class);
            }
        } else {
            throw new BusinessWarning("未选择发放的客户，请核查", this.getClass());
        }
        retMap.put("outMsg", outMsg.toString());
        LOGGER.error("会员发券:{}", JsonUtil.obj2Json(retMap));
    }

    private Map<String, String> calcTimeRanges(CouponPutBo bo) {
        Map<String, String> retMap = new HashMap<String, String>();
        String outCode = "0", outMsg = "ok";
        // 生效日期,失效日期
        String eftDate = "", invDate = "";
        try {
            if (bo != null) {
                String cpnTimeType = bo.getCpnTimeType();
                if (StringUtil.isNotBlank(cpnTimeType)) {
                    if ("2".equals(cpnTimeType)) {
                        // 日、月、年转换
                        SimpleDateFormat bgnSdf = new SimpleDateFormat("yyyy-MM-dd");
                        SimpleDateFormat endSdf = new SimpleDateFormat("yyyy-MM-dd");
                        Calendar bgnCale = Calendar.getInstance();
                        Calendar endCale = Calendar.getInstance();
                        // 生效时间计算
                        String bgnDate = bo.getPutTime();
                        if (StringUtil.isBlank(bgnDate)) {
                            bgnDate = DateTools.getStringDateShort("yyyy-MM-dd HH:mm:ss");
                        }
                        bgnCale.setTime(bgnSdf.parse(bgnDate));
                        eftDate = bgnSdf.format(bgnCale.getTime());
                        int iYear = 0, iMonth = 0, iDay = 0;
                        if (!StringUtils.isBlank(eftDate)) {
                            iYear = Integer.parseInt(eftDate.substring(0, 4));
                            iMonth = Integer.parseInt(eftDate.substring(5, 7)) - 1;
                            iDay = Integer.parseInt(eftDate.substring(8, 10));
                        } else {
                            retMap.put("outCode", "-1");
                            retMap.put("outMsg", "生效时间为空，请核查");
                            return retMap;
                        }
                        // 失效时间计算
                        String timeUnit = bo.getTimeUnit();
                        if (StringUtil.isBlank(timeUnit)) {
                            timeUnit = "1";
                        }
                        String timeDuration = bo.getTimeDuration();
                        if (StringUtil.isNotBlank(timeDuration) && StringUtils.isNumeric(timeDuration)) {
                            int iTimeDuration = Integer.parseInt(timeDuration);
                            switch (timeUnit) {
                                case "1": // 日
                                    iTimeDuration = iTimeDuration + iDay;
                                    endCale.set(Calendar.DATE, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "4": // 月
                                    iTimeDuration = iTimeDuration + iMonth;
                                    endCale.set(Calendar.MONTH, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                                case "5": // 年
                                    iTimeDuration = iTimeDuration + iYear;
                                    endCale.set(Calendar.YEAR, iTimeDuration);
                                    invDate = endSdf.format(endCale.getTime());
                                    break;
                            }
                        } else {
                            outCode = "-1";
                            outMsg = "生效时间偏移量数据错误，请核查";
                        }
                    } else {
                        eftDate = bo.getEftDate();
                        invDate = bo.getInvDate();
                    }
                } else {
                    outCode = "-1";
                    outMsg = "优惠券生效时间类型为空，请核查";
                }
            } else {
                outCode = "-1";
                outMsg = "转换日期函数参数为空，请核查";
            }
        } catch (ParseException e) {
            e.printStackTrace();
            outCode = "-1";
            outMsg = "日期转换错误，请核查";
        }
        retMap.put("eftDate", eftDate);
        retMap.put("invDate", invDate);
        retMap.put("outCode", outCode);
        retMap.put("outMsg", outMsg);
        return retMap;
    }
}
