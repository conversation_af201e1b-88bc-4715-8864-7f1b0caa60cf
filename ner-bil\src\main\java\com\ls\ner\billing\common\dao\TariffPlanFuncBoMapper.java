package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.TariffPlanFuncBo;
import com.ls.ner.billing.common.bo.TariffPlanFuncBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TariffPlanFuncBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int countByExample(TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int deleteByExample(TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int insert(TariffPlanFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int insertSelective(TariffPlanFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    List<TariffPlanFuncBo> selectByExampleWithBLOBs(TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    List<TariffPlanFuncBo> selectByExample(TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    TariffPlanFuncBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByExampleSelective(@Param("record") TariffPlanFuncBo record, @Param("example") TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByExampleWithBLOBs(@Param("record") TariffPlanFuncBo record, @Param("example") TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByExample(@Param("record") TariffPlanFuncBo record, @Param("example") TariffPlanFuncBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByPrimaryKeySelective(TariffPlanFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByPrimaryKeyWithBLOBs(TariffPlanFuncBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_tariff_plan_func
     *
     * @mbggenerated Wed Mar 09 18:52:06 CST 2016
     */
    int updateByPrimaryKey(TariffPlanFuncBo record);
}