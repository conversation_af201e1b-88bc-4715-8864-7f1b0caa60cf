package com.ls.ner.billing.mktact.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.controller.ControllerSupport;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.api.BillConstants.ActStatus;
import com.ls.ner.billing.gift.bo.ResultBean;
import com.ls.ner.billing.gift.util.HttpUtils;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.mktact.bo.ActInfoBo;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.service.IActInfoService;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.billing.mktact.service.IPreSectionService;
import com.ls.ner.billing.mktact.util.ImagesUtils;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.OrgCodeUtil;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.http.OkHttpUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <pre><b><font color="blue">MarketActController</font></b></pre>
 *
 * <pre><b>&nbsp;--营销活动相关功能--</b></pre>
 * <pre></pre>
 * <pre>
 * <b>--样例--</b>
 * </pre>
 * JDK版本：JDK1.7
 *
 * <AUTHOR>
 */
@Controller
public class MarketActController extends QueryController<MarketActBo> {
    private static final Logger logger = LoggerFactory.getLogger(MarketActController.class);

    @Autowired
    private ControllerSupport controllerSupport;

    @ServiceAutowired
    private Authentication authentication;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IOrgService orgService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private ICodeService codeService;

    @ServiceAutowired(value = "orgRpcService", serviceTypes = ServiceType.RPC)
    private IOrgRpcService orgRpcService;

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    @ServiceAutowired("preSectionService")
    private IPreSectionService preSectionService;

    @ServiceAutowired(value = "actInfoService")
    private IActInfoService actInfoService;

    @ServiceAutowired(serviceTypes=ServiceType.RPC,value="attachService")
    private IAttachRpcService attachService;

    @ServiceAutowired(serviceTypes = {ServiceType.RPC}, value = "accountService")
    private IAccountService accountService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
    private ISysParamService sysParamService;

    /**
     * 活动管理--页面初始化
     *
     * @param m
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/init", method = RequestMethod.GET)
    public String init(Model m) {

        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE, ActStatus.CODE_TYPE, "orderType");
        m.addAttribute("searchForm", new MarketActBo());
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/marketact";
    }

    /**
     * 描述:活动管理--页面初始化(活动类型条件)
     *
     * @param: [m, actType]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/7/8 2:21
     */
    @RequestMapping(value = "/marketacts/init/{actType}", method = RequestMethod.GET)
    public String init(Model m, @PathVariable("actType") String actType) {
        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE, ActStatus.CODE_TYPE, "orderType");
        MarketActBo bo = new MarketActBo();
        bo.setActType(actType);
        m.addAttribute("searchForm", bo);
        m.addAttribute("actType",actType);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        logger.debug("========>actType{}",actType);
        if ("01".equals(actType)){
            logger.debug("===========>进入优惠券页面");
            return "/bil/mktact/marketactCoupon";
        }else{
            return "/bil/mktact/marketact";
        }
    }

    /**
     * 获取营销活动列表
     *
     * @param params
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts")
    public @ItemResponseBody
    QueryResultObject queryMarketActives(
            @QueryRequestParam("params") RequestCondition params) {

        MarketActBo condition = this.rCondition2QCondition(params);

        //获取当前账号
        String accountName = authentication.getCurrentAccount().getAccountName();
        //判断当前账号是否是系统账号
        if (!accountService.isAdmin(accountName)) {
            //不是系统账号，获取当前系统账号的管理单位
            OrgBo orgBo = orgService.getOrgByAccountName(accountName);
            if (orgBo != null) {
                String orgCode = orgBo.getOrgCode();
                if (StringUtil.isNotBlank(orgCode)) {
                    condition.setOrgCode(orgCode);
                }
            }
        }

        condition.setMainFlag("1");
        List<MarketActBo> list = marketActService.qryMarketActives(condition);

        return RestUtils.wrappQueryResult(list, condition.getNums());
    }

    /**
     * 创建/修改活动页面初始化
     *
     * @param m
     * @param actId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/edit", method = RequestMethod.GET)
    public String editMarketactForm(Model m, @RequestParam("actId") String actId, @Validated String actType) {
        MarketActBo marketActBo = new MarketActBo();
        if (!StringUtils.nullOrBlank(actId)) {
            marketActBo = marketActService.queryByPrimaryKey(Long.valueOf(actId));
            if(StringUtils.nullOrBlank(marketActBo.getActSubType())){
                marketActBo.setActSubType(BillConstants.ActSubType.ACT_SUB_TYPE_0701);
            }
            AttachBo attach = new AttachBo();
            attach.setRelaTable("actImg");
            attach.setRelaId(actId);
            AttachBo oldAttach = attachService.qryAttachInfo(attach);
            logger.info("活动图片获取: {}", JSONObject.toJSONString(oldAttach));
            if (oldAttach != null) {
                marketActBo.setFileId(oldAttach.getAttachId());
            }
        } else {
            if (StringUtil.isNotBlank(actType)) {
                marketActBo.setActType(actType);
            } else {
                marketActBo.setActType("07");//默认“预存赠送”
            }
        }
        List<CodeBO> cpnStatusList = codeService.getStandardCodes(BillConstants.ACT_CHANNEL, null);
        if (CollectionUtils.isNotEmpty(cpnStatusList)){
            String actChannel = "";
            for (CodeBO codeBO : cpnStatusList) {
                actChannel = actChannel + codeBO.getCodeValue()+",";
            }
            if (actChannel.length()>0){
                actChannel = actChannel.substring(0,actChannel.length()-1);
                marketActBo.setActChannel(actChannel);
            }
        }
        logger.info("活动编辑获取: {}", JSONObject.toJSONString(marketActBo));
        m.addAttribute("marketactForm", marketActBo);

        //普通下拉框
        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE, "orderType");
        //单选按钮组
        controllerSupport.addCheckListMapsCodes(m, BillConstants.ACT_CHANNEL);

        List<CodeBO> actSubTypeList = codeService.getSubStandardCodes(BillConstants.ACT_TYPE,marketActBo.getActType(),null);
        m.addAttribute("actSubTypeList",actSubTypeList);
        if(CollectionUtils.isNotEmpty(actSubTypeList)){
            m.addAttribute("actSubTypeVisible","block");
        }else{
            m.addAttribute("actSubTypeVisible","none");
        }
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/marketactEdit";
    }

    /**
     * 关闭/删除活动
     *
     * @param oprType
     * @param actId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/{oprType}")
    public @ItemResponseBody
    QueryResultObject operateMarketAct(
            @PathVariable String oprType, @RequestParam Long actId) {
        String mark = "T";
        try {
            marketActService.operateMarketAct(oprType, actId);
        } catch (Exception e) {
            logger.error("", e);
            mark = "F";
        }
        return RestUtils.wrappQueryResult(mark);
    }

    /**
     * 保存 -- 营销活动
     *
     * @param model
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/saving", method = RequestMethod.POST)
    public @ItemResponseBody QueryResultObject saveMarketAct(MarketActBo model, MultipartHttpServletRequest httpRequest) {
        String mark = "T";
        String url = "";
        try {
            logger.debug(">>>>>dataParams:{}", JSONObject.toJSONString(model));
//            MarketActBo model = getBean(dataParams);
            //新增时，设置创建人员为当前登陆人员，单位为当前登陆人员所属单位
            if (model.getActId() == 0) {
                AccountBo currentAccount = authentication.getCurrentAccount();
                model.setCreEmp(currentAccount.getAccountId());
                OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
                if (orgBo != null) {
                    model.setOrgCode(orgBo.getOrgCode());
                }
            }
            long actId = 0;
            try {
                MultipartFile file = null;
                Iterator<String> iter = httpRequest.getFileNames();
                if (iter.hasNext()) {
                    file = httpRequest.getFile(iter.next());
                }

                if (file != null) {
                    logger.info("校验图片");
                    BufferedImage image = ImagesUtils.readImage(file.getInputStream());
                    if (image == null) {
                        return RestUtils.wrappQueryResult("商品图片格式有误，请检查");
                    }
                }
//                else if (StringUtil.isEmpty(model.getFileId())) {
//                    return RestUtils.wrappQueryResult("请上传活动图片");
//                }
                actId = marketActService.saveMarketAct(model, file);
            } catch (Exception e) {
                logger.error("保存失败：{}", e.getMessage());
                return RestUtils.wrappQueryResult(e.getMessage());
            }

            if ("T".equals(mark)) {
                Map<String, Object> urlMap = new HashMap<String, Object>();
                urlMap.put("actId", String.valueOf(actId));
                urlMap.put("actType", model.getActType());
                urlMap.put("actSubType", model.getActSubType());
                urlMap.put("httpRequest", httpRequest);
                urlMap.put("busiType", model.getProdBusiType());
                url = this.responseUrl(urlMap);
                if ("F".equals(url)) {
                    logger.debug("营销活动保存功能入参为空：actId={},actType={}", String.valueOf(actId), model.getActType());
                    mark = "F";
                }
            }

        } catch (Exception e) {
            logger.error("", e);
            mark = "F";
        }

        return RestUtils.wrappQueryResult(new Object[]{mark, url});
    }

    /**
     * 描述:活动地址转发
     *
     * @param: [map]
     * @return: java.lang.Stringo
     * 创建人:biaoxiangd
     * 创建时间:2017/7/8 23:32
     */
    private String responseUrl(Map<String, Object> map) {
        String actId = StringUtil.getString(map.get("actId"));
        String actType = StringUtil.getString(map.get("actType"));
        String actSubType = StringUtil.getString(map.get("actSubType"));
        String busiType = StringUtil.getString(map.get("busiType"));
        MultipartHttpServletRequest httpRequest = (MultipartHttpServletRequest) map.get("httpRequest");
        if (StringUtil.isBlank(actId) && StringUtil.isBlank(actType)) {
            return "F";
        }
        StringBuffer sbUrl = new StringBuffer();
        // localhost test
        String host = httpRequest.getContextPath();
        sbUrl.append(host);
        //01	优惠券推广 ; 02	限时打折 ; 03	满减满送 ; 04	首单免 ; 05	注册送 ; 06	邀请送 ; 07	预存赠送
        switch (actType) {
            case "01":
                sbUrl.append("/billing/couponPush/couponPushMain");
                break;
            case "02":
                sbUrl.append("/marketacts/limDisCountManage");
                break;
            case "03":
                sbUrl.append("/marketacts/firstFreeManage");
                break;
            case "04":
                sbUrl.append("/marketacts/firstFreeManage");
                break;
            case "05":
                sbUrl.append("/marketacts/registerSendManage");
                break;
            case "06":
                sbUrl.append("/marketacts/registerSendManage");
                break;
            case "07":
                switch (actSubType){
                    case "0701":
                        sbUrl.append("/sections/edit");
                        break;
                    case "0702":
                        sbUrl.append("/sections/cpn/edit");
                        break;
                    default://07
                        sbUrl.append("/sections/edit");
                        break;
                }
                break;
            case "08":
                sbUrl.append("/billing/couponPush/couponPushMain");
                break;
            default://07
                sbUrl.append("/sections/init");
                break;
        }
        sbUrl.append("?actId=" + actId + "&actType=" + actType + "&actSubType=" + actSubType +"&busiType="+ busiType);
        return sbUrl.toString();
    }


    protected MarketActBo getBean(Map<String, Object> dataParams) {
        MarketActBo model = new MarketActBo();
        controllerSupport.populateSubmitParams(dataParams, model);
        return model;
    }

    /**
     * 活动状态轮询job
     *
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/mktstatejob")
    public @ResponseBody
    Object marketStateJob() {
        try {
            logger.debug(">>>>>开始执行活动状态轮询job-----");
            marketActService.marketStateJob();
        } catch (Exception e) {
            logger.error("活动状态轮询job异常：", e);
        }
        return "执行成功";
    }

    /**
     * 活动查询 --页面初始化
     *
     * @param m
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketqry/init", method = RequestMethod.GET)
    public String query(Model m) {

        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE);
        List<CodeBO> codeList = codeService.getStandardCodes(ActStatus.CODE_TYPE, null);
        for (CodeBO code : codeList) {
            if ("0".equals(code.getCodeValue())) {
                codeList.remove(code);
                break;
            }
        }
        m.addAttribute("actStateList", codeList);
        m.addAttribute("searchForm", new MarketActBo());

        return "/bil/mktact/marketactQry";
    }

    /**
     * 获取营销活动列表--orgCode in 登陆账号所属单位、及其所有上级单位
     *
     * @param params
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketqry")
    public @ItemResponseBody
    QueryResultObject queryMarketActivesByOrg(
            @QueryRequestParam("params") RequestCondition params) {
        MarketActBo condition = this.rCondition2QCondition(params);
        try {
            condition.setOrgCodes(orgRpcService.getSupOrgCodes());
        } catch (Exception e) {
            logger.error("", e);
        }
        condition.setActQryFlag("0");
        List<MarketActBo> list = marketActService.qryMarketActives(condition);

        return RestUtils.wrappQueryResult(list, condition.getNums());
    }

    /**
     * 活动详情--页面初始化
     *
     * @param m
     * @param actId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketqry/{actId}")
    public String marketDetail(Model m, @PathVariable String actId) {
        MarketActBo marketActBo = new MarketActBo();
        if (!StringUtils.nullOrBlank(actId)) {
            marketActBo = marketActService.queryByPrimaryKey(Long.valueOf(actId));
            AttachBo attach = new AttachBo();
            attach.setRelaTable("actImg");
            attach.setRelaId(actId);
            AttachBo oldAttach = attachService.qryAttachInfo(attach);
            logger.info("活动图片获取: {}", JSONObject.toJSONString(oldAttach));
            if (oldAttach != null) {
                marketActBo.setFileId(oldAttach.getAttachId());
            }
        }

        if (marketActBo != null && BillConstants.ActType.ACT_TYPE_03.equals(marketActBo.getActType())){
            // 优惠条件类别
            List<CodeBO> cpnTypeList = codeService.getStandardCodes("dctCondType", null);
            m.addAttribute("dctCondTypeList", cpnTypeList);
        }

        //普通下拉框
        controllerSupport.addStCodes(m, BillConstants.ACT_TYPE, ActStatus.CODE_TYPE);
        //单选按钮组
        controllerSupport.addCheckListMapsCodes(m, BillConstants.ACT_CHANNEL);
        List<Map<String, String>> infoTypeList = new ArrayList<Map<String, String>>();
        Map<String, String> actTypeMap = new HashMap<String, String>();
        actTypeMap.put("text", "图文");
        actTypeMap.put("value", "1");
        infoTypeList.add(actTypeMap);
        Map<String, String> typeMap = new HashMap<String, String>();
        typeMap.put("text", "外部链接");
        typeMap.put("value", "2");
        infoTypeList.add(typeMap);
        m.addAttribute("infoTypeList", infoTypeList);
        ActInfoBo actInfoBo = new ActInfoBo();
        actInfoBo.setActId(actId);
        ActInfoBo infoBo = actInfoService.queryActInfo(actInfoBo);
        if (infoBo != null){
            marketActBo.setContent(infoBo.getContent());
            marketActBo.setContentUrl(infoBo.getContentUrl());
            marketActBo.setLinkUrl(infoBo.getLinkUrl());
            marketActBo.setInfoType(infoBo.getInfoType());
            AttachBo attachBo = new AttachBo();
            attachBo.setRelaTable("mkt_act_info");
            attachBo.setRelaId(infoBo.getActInfoId()+"02");
            attachBo = attachService.qryAttachInfo(attachBo);
            String pubPath = PublicConstants.ApplicationPath.getPubPath();
            marketActBo.setInfoImgUrl(pubPath + "/api/v0.1/attachs/" + attachBo.getAttachId());
        }
        m.addAttribute("marketactForm", marketActBo);
        if (BillConstants.ActType.ACT_TYPE_0701.equals(marketActBo.getActSubType())){
            m.addAttribute("showFlag", "true");
            m.addAttribute("showFlag1", true);
        }else{
            m.addAttribute("showFlag", "false");
            m.addAttribute("showFlag1", false);
        }
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        return "/bil/mktact/marketactDetail";
    }

    /**
     * 描述:活动资讯发布页面
     *
     * @param: [m, actId]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/26 15:29
     */
    @RequestMapping(value = "/marketacts/actInfo", method = RequestMethod.GET)
    public String actInfoForm(Model m, @RequestParam("actType") String actType,
                              @RequestParam("actId") String actId) {
        ActInfoBo actInfoBo = new ActInfoBo();
        actInfoBo.setActId(actId);
        actInfoBo.setActType(actType);
        actInfoBo.setInfoType("1");
//        actInfoBo.setPicFlag("0");
        ActInfoBo InfoBo = actInfoService.queryActInfo(actInfoBo);
        logger.debug(">>>>>>>>>>资讯发布controller出参:{}",IJsonUtil.obj2Json(InfoBo));
        if (InfoBo != null) {
            InfoBo.setActId(actId);
            InfoBo.setActType(actType);
            if (StringUtil.isBlank(InfoBo.getCoberPic())) {
                InfoBo.setPicFlag("1");
            } else {
                InfoBo.setPicFlag("0");
            }

        } else {
            InfoBo = actInfoBo;
        }
        // 初始化资讯内容
        List<Map<String, String>> infoTypeList = new ArrayList<Map<String, String>>();
        Map<String, String> actTypeMap = new HashMap<String, String>();
        actTypeMap.put("text", "图文");
        actTypeMap.put("value", "1");
        infoTypeList.add(actTypeMap);
        // 优惠券推广不显示外部链接
//        if (!"01".equals(actType)) {
            Map<String, String> typeMap = new HashMap<String, String>();
            typeMap.put("text", "外部链接");
            typeMap.put("value", "2");
            infoTypeList.add(typeMap);
//        }
        m.addAttribute("infoTypeList", infoTypeList);

        m.addAttribute("actinfoForm", InfoBo);
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

        return "/bil/mktact/mktactinfo";
    }


    /**
     * 描述:活动资讯保存
     *
     * @param: [m, actId]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/26 15:29
     */
    @RequestMapping(value = "/marketacts/saveActInfo", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveCoupon(ActInfoBo bo, Model m, MultipartHttpServletRequest request) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        int flag = 0;
        try {
            // 去除content的特殊字符
            String content = ESAPI.encoder().encodeForHTML(bo.getContent());
            bo.setContent(content);
            if (StringUtil.isBlank(bo.getActInfoId())) {
                flag = actInfoService.saveActInfo(bo, request);
            } else {
                flag = actInfoService.updateActInfo(bo, request);
            }
            if (flag > 0) {
                retMap.put("successful", true);
                retMap.put("resultHint", "获得资讯发布成功");
            } else {
                retMap.put("successful", false);
                retMap.put("resultHint", "活动资讯发布失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("successful", false);
            retMap.put("resultHint", e.getMessage());
        }
        return WrappedResult.successWrapedResult(retMap);
    }
    /**
     * @param params
     * @description  查询注册送/邀请送，已经分配的优惠券
     * <AUTHOR>
     * @create 2017-08-09 10:43:00
     */
    @RequestMapping(value = "/marketacts/registerSendManage/queryActCpn")
    public @ItemResponseBody
    QueryResultObject queryActCpn(@QueryRequestParam("params") RequestCondition params) throws Exception {
        MarketActBo condition = this.rCondition2QCondition(params);
        List<CouponBo> list = marketActService.queryActCpn(condition);
        return RestUtils.wrappQueryResult(list, list.size());
    }

    /**
     * @param
     * @description  根据活动查询优惠卷
     * <AUTHOR>
     * @create 2018-06-04 14:47:41
     */
    @RequestMapping("/queryCouponByActId/{actId}")
    public @ResponseBody WrappedResult queryCouponByActId(@PathVariable String actId){
        //查询优惠券
        List<Map<String, Object>> actCouponList = marketActService.queryActCoupon(actId);
        return WrappedResult.successWrapedResult(actCouponList);
    }

    /**
     * @param dataParams
     * @description  无资讯活动发布
     * <AUTHOR>
     * @create 2020-09-22 14:39:46
     */
    @RequestMapping(value = "/marketacts/releaseAct", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult releaseAct(@RequestBody Map<String, Object> dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        try {
            logger.debug("活动发布入参：{}",IJsonUtil.obj2Json(dataParams));
            actInfoService.releaseAct(String.valueOf(dataParams.get("actId")),String.valueOf(dataParams.get("isLink")));
            retMap.put("successful", true);
            retMap.put("resultHint", "活动发布成功");
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("successful", false);
            retMap.put("resultHint", e.getMessage());
        }
        return WrappedResult.successWrapedResult(retMap);
    }


    /**
     * @param dataParams
     * @description  无资讯活动发布
     * <AUTHOR>
     * @create 2020-09-22 14:39:46
     */
    @RequestMapping(value = "/marketacts/isLinkdoSave", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult isLinkdoSave(@RequestBody Map<String, Object> dataParams) throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        try {
            logger.debug("活动保存入参：{}",IJsonUtil.obj2Json(dataParams));
            String actId = String.valueOf(dataParams.get("actId"));
            String isLink = String.valueOf(dataParams.get("isLink"));
            MarketActBo bo = new MarketActBo();
            bo.setActId(Long.parseLong(actId));
            bo.setIsLink(isLink);
            actInfoService.updateActForIsLink(bo);
            retMap.put("successful", true);
            retMap.put("resultHint", "活动发布成功");
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("successful", false);
            retMap.put("resultHint", e.getMessage());
        }
        return WrappedResult.successWrapedResult(retMap);
    }


    @Override
    protected MarketActBo initCondition() {
        return new MarketActBo();
    }


    /**
     * 获取二维码跳转链接
     *
     * @param actId
     * @return
     */
    @RequestMapping(value = "/getURLLink/{actId}")
    public @ResponseBody WrappedResult getURLLink(@PathVariable String actId) {
        logger.debug("获取二维码跳转链接入参：{}",actId);
        Map<String, Object> retMap = new HashMap<String, Object>();
        try {
            String appId = sysParamService.getSysParamsValues("web_mini_appid");
            String secret = sysParamService.getSysParamsValues("web_mini_secret");
            String tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + secret;
            String envVersion = sysParamService.getSysParamsValues("web_env_version");

            String tokenResp = HttpUtils.getRequest(tokenUrl);
            logger.debug("获取二维码token出参：{}",tokenResp);
            Map tokenMap = JSONObject.parseObject(tokenResp, Map.class);
            String accessToken = tokenMap.get("access_token").toString();

            String url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=" + accessToken;
            Map paramMap = new HashMap<>();
            paramMap.put("path", "/pages/active/coupon/index");
            paramMap.put("query", "id="+actId);
            paramMap.put("env_version", envVersion);
            OkHttpUtil.HttpResult httpResult = OkHttpUtil.getInstance().postByJson(url, paramMap);
            Map map = JSONObject.parseObject(httpResult.getBody(), Map.class);
            logger.debug("获取二维码URLLink出参：{}", map);
            if (map.get("errcode").toString().equals("0")){
                retMap.put("urlLink",map.get("url_link").toString());
                return WrappedResult.successWrapedResult(retMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("urlLink","");
            retMap.put("successful", false);
            retMap.put("resultHint", e.getMessage());
        }
        return WrappedResult.successWrapedResult(retMap);
    }

}
