package com.ls.ner.billing.charge.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.cust.api.service.ICustCenterRpcService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import com.ls.ner.billing.charge.service.IChargeBillingConfService;
import com.ls.ner.util.FormatUtil;
import com.ls.ner.util.ListMapUtil;
import com.ls.ner.util.MergeUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 * @param
 * @description 充电计费配置-用户
 * <AUTHOR>
 * @create 2018-07-19 11:27:07
 */
@Controller
@RequestMapping("/billing/chargeUserbilconf")
public class ChargeUserBilConfController extends QueryController<ChargeBillingConfQueryCondition> {

	private static final Logger logger = LoggerFactory.getLogger(ChargeUserBilConfController.class);

	@ServiceAutowired("chargeBillingConfService")
	private IChargeBillingConfService chargeBillingConfService;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value = "orgService", serviceTypes = ServiceType.RPC)
	private IOrgService orgService;

	@ServiceAutowired(value = "archivesRpcService", serviceTypes = ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired(value = "custCenterRpcService", serviceTypes = ServiceType.RPC)
	private ICustCenterRpcService custCenterRpcService;

	
	@ServiceAutowired
	Authentication authentication;

	@Override
	protected ChargeBillingConfQueryCondition initCondition() {
		return new ChargeBillingConfQueryCondition();
	}

	/**
	 * @param m
	 * @description 初始化计费配置
	 * <AUTHOR>
	 * @create 2018-07-19 11:28:40
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m) {
		addCodes(m, BizConstants.CodeType.VALID_FLAG);
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();

		m.addAttribute("searchForm", condition);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("cstPath", PublicConstants.ApplicationPath.getCstPath());
		return "/bil/chargeConf/userChargeConfIndex";
	}
	
	/**
	 * <AUTHOR>
	 * @throws IllegalAccessException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchFieldException 
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置
	 */
	@RequestMapping(value = "/queryChargeBillingConfs")
	public @ItemResponseBody QueryResultObject queryChargeSerItems(
			@QueryRequestParam("params") RequestCondition params) throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
		ChargeBillingConfQueryCondition condition = this.rCondition2QCondition(params);
		condition.setBillType("2");
		List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
		if(list!=null && list.size()>0){
			//2019-05-20 zhangdan  修改列表无单位名称
			List<String> custIdList = new ArrayList<>();
			//StringBuilder custIdSb = new StringBuilder();
			for(ChargeBillingConfBo cbcBo:list){
				if(ChargeConstant.validFlag.DRAFT.equals(cbcBo.getChcStatus())){
					cbcBo.setChcStatusName("草稿");
				}else
					cbcBo.setChcStatusName("-");
				if(!StringUtils.nullOrBlank(cbcBo.getCustId())){
					custIdList.add(cbcBo.getCustId());
					//custIdSb.append(cbcBo.getCustId()).append(",");
				}
			}
			/*String custIds = custIdSb.toString();
			if(!StringUtils.nullOrBlank(custIds)){
				List<String> custIdList = new ArrayList<>();
				custIds = custIds.substring(0, custIds.length() - 1);
				custIdList.add(custIds);
				Map<String, Object> searchMap = new HashMap<String, Object>();
				searchMap.put("custIdList", custIdList);
				List<Map<String, Object>> custList = custCenterRpcService.getCustList(searchMap);
				MergeUtil.mergeList(list, custList, "custId",  new String[]{"custName"});
			}*/
			if (CollectionUtils.isNotEmpty(custIdList)){
				Map<String, Object> searchMap = new HashMap<String, Object>();
				searchMap.put("custIdList", custIdList);
				List<Map<String, Object>> custList = custCenterRpcService.getCustList(searchMap);
				MergeUtil.mergeList(list, custList, "custId",  new String[]{"custName"});
			}
		}
		int recordCount = chargeBillingConfService.queryChargeBillingConfsNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增/修改充电计费配置
	 */
	@RequestMapping(value = "/addChargeBillingConf", method = RequestMethod.GET)
	public String addChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		if (StringUtils.nullOrBlank(chcNo)) {
			ChargeBillingConfBo cbc = new ChargeBillingConfBo();
			cbc.setRangeFlag(BillConstants.RANGE_FLAG.ALL);
			m.addAttribute("chargeForm", cbc);
		} else {
			ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
			condition.setChcNo(chcNo);
			List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
			if(list != null && list.size() > 0){
				ChargeBillingConfBo bo =list.get(0);
				if(!StringUtils.nullOrBlank(bo.getCustId())){
					List<String> custIdList = new ArrayList<>();
					custIdList.add(bo.getCustId());
					Map<String, Object> searchMap = new HashMap<String, Object>();
					searchMap.put("custIdList", custIdList);

					List<Map<String, Object>> custList = custCenterRpcService.getCustList(searchMap);
					if(custList!=null && custList.size()>0)
						bo.setCustName(String.valueOf(custList.get(0).get("custName")));
				}
				bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
				if(StringUtil.isBlank(bo.getRangeFlag())){
					bo.setRangeFlag(BillConstants.RANGE_FLAG.ALL);
				}
				m.addAttribute("chargeForm", bo);
			}else {
				ChargeBillingConfBo  vo=  new ChargeBillingConfBo();
				vo.setBillCtlMode("2");
				m.addAttribute("chargeForm",vo);
			}
		}
//		addCodes(m, BizConstants.CodeType.CHC_BILLING_CHARGE_MODE);
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> list= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		for( CodeBO code :billCtlModeCodes){
			if ("1".equals(code.getCodeValue())){
				billCtlModeCodes.remove(code);
			}
		}

		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", list);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
		m.addAttribute("cstPath", PublicConstants.ApplicationPath.getCstPath());

		List<Map<String, Object>> stationRangeList = new ArrayList<Map<String, Object>>();
		Map<String, Object> sMap = new HashMap<String, Object>();
		sMap.put("text","全部");
		sMap.put("value",BillConstants.RANGE_FLAG.ALL);
		stationRangeList.add(sMap);
		sMap = new HashMap<String, Object>();
		sMap.put("text","部分");
		sMap.put("value",BillConstants.RANGE_FLAG.PART);
		stationRangeList.add(sMap);
		m.addAttribute("stationRangeList", stationRangeList);
		return "/bil/chargeConf/addUserChargeConf";
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 查看充电计费配置
	 */
	@RequestMapping(value = "/showChargeBillingConf", method = RequestMethod.GET)
	public String showChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfQueryCondition condition = new ChargeBillingConfQueryCondition();
		condition.setChcNo(chcNo);
		List<ChargeBillingConfBo> list =chargeBillingConfService.queryChargeBillingConfs(condition);
		if(list != null && list.size() > 0){
			ChargeBillingConfBo bo =list.get(0);
			bo.setChargePrice(FormatUtil.formatNumber(bo.getChargePrice(),4));
			List<String> custIdList = new ArrayList<>();
			custIdList.add(bo.getCustId());
			Map<String, Object> searchMap = new HashMap<String, Object>();
			searchMap.put("custIdList", custIdList);

			List<Map<String, Object>> custList = custCenterRpcService.getCustList(searchMap);
			if(custList!=null && custList.size()>0)
				bo.setCustName(String.valueOf(custList.get(0).get("custName")));

			if(StringUtil.isBlank(bo.getRangeFlag())){
				bo.setRangeFlag(BillConstants.RANGE_FLAG.ALL);
			}
			m.addAttribute("chargeForm", bo);
		}else
			m.addAttribute("chargeForm", new ChargeBillingConfBo());
		List<CodeBO> li = codeService.getStandardCodes(BizConstants.CodeType.CHC_BILLING_CHARGE_MODE,null);
		List<Map<String, Object>> l= ListMapUtil.toCheckListMap(li);
		List<CodeBO> billCtlModeCodes = codeService.getStandardCodes(BizConstants.CodeType.BILL_CTL_MODE,null);
		List<Map<String, Object>> billCtlModeList = ListMapUtil.toCheckListMap(billCtlModeCodes);
		List<CodeBO> timeFlagList = codeService.getStandardCodes(BizConstants.ProductType.TIME_FLAG,null);
		m.addAttribute("chcBillingChargeModeList", l);
		m.addAttribute("billCtlModeList", billCtlModeList);
		m.addAttribute("timeFlagList", timeFlagList);
		m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());

		List<Map<String, Object>> stationRangeList = new ArrayList<Map<String, Object>>();
		Map<String, Object> sMap = new HashMap<String, Object>();
		sMap.put("text","全部");
		sMap.put("value",BillConstants.RANGE_FLAG.ALL);
		stationRangeList.add(sMap);
		sMap = new HashMap<String, Object>();
		sMap.put("text","部分");
		sMap.put("value",BillConstants.RANGE_FLAG.PART);
		stationRangeList.add(sMap);
		m.addAttribute("stationRangeList", stationRangeList);
		return "/bil/chargeConf/showUserChargeConf";
	}
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 保存充电计费配置
	 */
	@RequestMapping(value = "/saveChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult saveChargeBillingConf(@RequestBody Map<String, Object> dataParams) throws IllegalAccessException, InvocationTargetException{
		List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		for (Map<String, Object> map : saveList) {
			BeanUtils.populate(bo, map);
		}
		try{
			String systemId = bo.getSystemId();
			if (systemId != null && !"".equals(systemId)) {
				chargeBillingConfService.updateChargeBillingConf(bo);
			} else {
				chargeBillingConfService.insertChargeBillingConf(bo);
			}
			return WrappedResult.successWrapedResult(bo);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 删除充电计费配置
	 */
	@RequestMapping(value = "/delChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult delChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		chargeBillingConfService.delChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 发布充电计费配置
	 */
	@RequestMapping(value = "/releaseChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult releaseChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		try{
			ChargeBillingConfBo bo = new ChargeBillingConfBo();
			bo.setChcNo(chcNo);
			chargeBillingConfService.releaseChargeBillingConf(bo);
			return WrappedResult.successWrapedResult(true);
		}catch(Exception e){
			logger.error("",e);
			e.printStackTrace();
			return WrappedResult.failedWrappedResult("系统错误！");
		}
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-27
	 * @description 复制充电计费配置
	 */
	@RequestMapping(value = "/copyChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult copyChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		bo = chargeBillingConfService.copyChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询分时时段设置
	 */
	@RequestMapping(value = "/queryChargePeriods")
	public @ResponseBody WrappedResult queryChargePeriods(@RequestBody ChargeBillingConfQueryCondition condition) {
//		condition.setPageEnd(0);
		List<ChargePeriodsBo> list =chargeBillingConfService.queryChargePeriods(condition);
		if(list!= null){
			for(ChargePeriodsBo cpBo:list){
				cpBo.setPrice(FormatUtil.formatNumber(cpBo.getPrice(),4));
//				if(!StringUtils.nullOrBlank(cpBo.getTimeFlag())){
//					CodeBO code = codeService.getStandardCode(BizConstants.ProductType.TIME_FLAG, cpBo.getTimeFlag(), null);
//					if(code!=null){
//						cpBo.setTimeFlag(code.getCodeName());
//					}
//				}
			}
		}
		return WrappedResult.successWrapedResult(list);
	}

	@RequestMapping(value = "/cancellChargeBillingConf", method = RequestMethod.POST)
	public @ResponseBody WrappedResult cancellChargeBillingConf(Model m, @RequestParam("chcNo") String chcNo) {
		ChargeBillingConfBo bo = new ChargeBillingConfBo();
		bo.setChcNo(chcNo);
		chargeBillingConfService.cancellChargeBillingConf(bo);
		return WrappedResult.successWrapedResult(true);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询分时时段设置
	 */
	@RequestMapping(value = "/queryBillingStation")
	public @ResponseBody WrappedResult queryBillingStation(@RequestBody ChargeBillingConfQueryCondition condition) {
		List<ChargeBillingConfBo> list =chargeBillingConfService.queryBillingStation(condition);
		List<Map<String,Object>> stationList = new ArrayList<Map<String,Object>>();
		if(list!=null && list.size() > 0){
			List<String> sList = new ArrayList<String>();
			for (ChargeBillingConfBo confBo : list) {
				sList.add(confBo.getStationId());
			}
			Map<String,Object> inMap = new HashMap<String, Object>();
			inMap.put("stations",sList);
			inMap.put("removeStations",new ArrayList<>());
			inMap.put("busiType","02");
			logger.debug("=====stations:"+ JsonUtil.obj2Json(inMap));
			stationList  = archivesRpcService.queryStationList(inMap);
		}
		return WrappedResult.successWrapedResult(stationList);
	}

	private void addCodes(Model m,String ... strings){
		for(String str:strings){
			m.addAttribute(str+"List", codeService.getStandardCodes(str,null));
		}
	}

}
