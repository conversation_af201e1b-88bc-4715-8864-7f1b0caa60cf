package com.ls.ner.billing.charge.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ls.ner.billing.charge.service.ITimeShareServicePriceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.ls.ner.billing.charge.bo.ChargeBillingRltBo;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.dao.IChargeSerItemDao;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;

/**
 * 充电资费管理---服务项目维护
 * <AUTHOR>
 */
@Service(target = {ServiceType.APPLICATION}, value = "chargeSerItemService")
public class ChargeSerItemServiceImpl implements IChargeSerItemService {
private static final Logger LOGGER = LoggerFactory
		.getLogger(ChargeSerItemServiceImpl.class);

	@Autowired
	private IChargeSerItemDao chargeSerItemDao;

	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;

	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	@ServiceAutowired("servicePriceService")
	private ITimeShareServicePriceService servicePriceService;
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public List<ChargeSerItemBo> queryChargeSerItems(ChargeSerItemQueryCondition bo){
		return chargeSerItemDao.queryChargeSerItems(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询刷卡费用条数C_CAR_PAY
	 */
	public int queryChargeSerItemsNum(ChargeSerItemQueryCondition bo){
		return chargeSerItemDao.queryChargeSerItemsNum(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 根据itemName查询追加收费项目条数E_APPEND_CHARGE_ITEM
	 */
	public int queryChargeSerItemsNumByName(ChargeSerItemQueryCondition bo){
		return chargeSerItemDao.queryChargeSerItemsNumByName(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询追加收费项目最大的sn值 E_APPEND_CHARGE_ITEM 
	 */
	public int queryChargeSerMaxSn(ChargeSerItemQueryCondition bo){
		return chargeSerItemDao.queryChargeSerMaxSn(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增追加收费项目E_APPEND_CHARGE_ITEM 
	 */
	public void insertChargeSerItem(ChargeSerItemBo bo){
	/*	String itemNo = seqRpcService.getDefNo();
		bo.setItemNo(itemNo);*/
		chargeSerItemDao.insertChargeSerItem(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新追加收费项目E_APPEND_CHARGE_ITEM
	 */
	public void updateChargeSerItem(ChargeSerItemBo bo){
		chargeSerItemDao.updateChargeSerItem(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目及费用
	 * @param itemNo 多个编号    如：1,2,3,4,5
	 * @param price  多个编号价格  如：1.21,2.11,3.11
	 */
	public List<ChargeSerItemBo> querySerItemsByNo(ChargeSerItemQueryCondition bo) throws Exception{
		if(!StringUtils.isNotEmpty(bo.getItemNo()) || !StringUtils.isNotEmpty(bo.getPrice()))
			throw new Exception("传参错误");
		List<ChargeSerItemBo> csiBo = chargeSerItemDao.queryChargeSerItems(bo);
		if (servicePriceService.getServicePriceSwitch()){
			modifyReturnChargeSerItemsByTimeShare(csiBo, bo);
		}else {
			modifyReturnChargeSerItems(csiBo, bo);
		}
		return csiBo;
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目名称
	 * @param attachItemNos 多个编号    如：1,2,3,4,5
	 * @param attachItemPrices  服务项目单价  如：1.21,2.11,3.11
	 * @param attachItemUnits  服务项目单位  如：1.21,2.11,3.11
	 * @param attachItemNums  服务项目数量  如：1.21,2.11,3.11
	 * @param attachItemAmts  服务项目金额  如：1.21,2.11,3.11
	 */
	public List<Map<String,Object>> querySerItemsByNo(ChargeBillingRltBo cbrBo) throws Exception{
		List<Map<String,Object>> returnMap = new ArrayList<Map<String,Object>> ();
		if(StringUtils.isNotEmpty(cbrBo.getAttachItemNos())){
			ChargeSerItemQueryCondition condition = new ChargeSerItemQueryCondition();
			condition.setItemNo(cbrBo.getAttachItemNos());
			List<ChargeSerItemBo> csiBo = chargeSerItemDao.queryChargeSerItems(condition);
			String[] nos = cbrBo.getAttachItemNos().split(",");
			String[] prices = cbrBo.getAttachItemPrices().split(",");
			String[] units = cbrBo.getAttachItemUnits().split(",");
			String[] nums = cbrBo.getAttachItemNums().split(",");
			String[] amts = cbrBo.getAttachItemAmts().split(",");
			for(int i = 0;i < nos.length; i ++){
				Map<String,Object> m = new HashMap<String,Object> ();
				m.put("itemCode",nos[i]);
				CodeBO code = codeService.getStandardCode(BizConstants.CodeType.CHC_BILLING_PRICE_UNIT,units[i], null);
				m.put("itemUnits",code!=null?"元/"+code.getCodeName():"");
				m.put("pricingAmt",amts[i]);
				m.put("amount",nums[i]);
				m.put("price",prices[i]);
				if(csiBo != null && csiBo.size() > 0)
					for(int j = 0;j < csiBo.size();j ++){
						ChargeSerItemBo csBo = csiBo.get(j);
						if(nos[i].equals(csBo.getItemNo())){
							m.put("itemName",csBo.getItemName());
							csiBo.remove(j);
							break;
						}
					}
				returnMap.add(m);
			}
		}
		return returnMap;
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询服务项目及费用
	 * @param itemNo 多个编号    如：1,2,3,4,5
	 */
	public String queryUnitsByNo(String itemNo){
		StringBuffer returnSb = new StringBuffer();
		if(StringUtils.isNotEmpty(itemNo)){
			ChargeSerItemQueryCondition bo = new ChargeSerItemQueryCondition();
			bo.setItemNo(itemNo);
			List<ChargeSerItemBo> csiBo = chargeSerItemDao.queryChargeSerItems(bo);
			if(csiBo!=null){
				String[] no = itemNo.split(",");
				for(int j = 0;j<no.length;j++){
					for(int i = 0;i<csiBo.size();i++){
						ChargeSerItemBo csi = csiBo.get(i);
						if(no[j].equals(csi.getItemNo())){
							if(j+1 != no.length){
								returnSb.append(csi.getItemUnit()).append(",");
							}else{
								returnSb.append(csi.getItemUnit());
							}
							break;
						}
					}
				}
			}
		}
		return returnSb.toString();
	}
	
	private void modifyReturnChargeSerItems(List<ChargeSerItemBo> csiBo,ChargeSerItemQueryCondition bo)throws Exception{
		String[] itemNo = bo.getItemNo().split(",");
		String[] price = bo.getPrice().split(",");
		String[] itemChargeMode =  bo.getItemChargeMode() == null ? null : bo.getItemChargeMode().split(",");
		String[] freeNums = bo.getFreeNum() == null ? null : bo.getFreeNum().split(",");
		if(itemNo.length != price.length)
			throw new Exception("项目编号与价格数量匹配不上，项目编号："+bo.getItemNo()+",价格："+bo.getPrice());
		if(csiBo==null || csiBo.size()!=itemNo.length)
			throw new Exception("查询出来的服务条数与项目编号数量匹配不上");

		Map<String,String> m = new HashMap<String,String>();
		for(int i=0;i<itemNo.length;i++){
			m.put(itemNo[i], price[i]);
		}
		Map<String,String> freeMap = new HashMap<String,String>();
		if(freeNums != null && freeNums.length == itemNo.length){
			for(int i=0;i<itemNo.length;i++){
				freeMap.put(itemNo[i], freeNums[i]);
			}
		}
		Map<String,String> itemModeMap = new HashMap<String,String>();
		if(itemChargeMode != null){
			for(int i=0;i<itemNo.length;i++){
				itemModeMap.put(itemNo[i], itemChargeMode[i]);
			}
		}
		for(ChargeSerItemBo csi:csiBo){
			csi.setServicePrice(m.get(csi.getItemNo()));
			csi.setFreeNum(freeMap.get(csi.getItemNo()));
			csi.setItemChargeMode(itemModeMap.get(csi.getItemNo()));
			LOGGER.debug("=============csi.getItemNo():"+itemModeMap.get(csi.getItemNo()));
		}
	}

	private void modifyReturnChargeSerItemsByTimeShare(List<ChargeSerItemBo> csiBo,ChargeSerItemQueryCondition bo)throws Exception{
		String[] itemNo = bo.getItemNo().split(",");
		String[] price = bo.getPrice().split(",");
		String[] itemChargeMode =  bo.getItemChargeMode() == null ? null : bo.getItemChargeMode().split(",");
		String[] freeNums = bo.getFreeNum() == null ? null : bo.getFreeNum().split(",");
		if(itemNo.length != price.length)
			throw new Exception("项目编号与价格数量匹配不上，项目编号："+bo.getItemNo()+",价格："+bo.getPrice());
		if(csiBo==null || csiBo.size()!=itemNo.length)
			throw new Exception("查询出来的服务条数与项目编号数量匹配不上");

		Map<String,String> m = new HashMap<String,String>();
		for(int i=0;i<itemNo.length;i++){
			m.put(itemNo[i], price[i]);
		}
		Map<String,String> freeMap = new HashMap<String,String>();
		if(freeNums != null && freeNums.length == itemNo.length){
			for(int i=0;i<itemNo.length;i++){
				freeMap.put(itemNo[i], freeNums[i]);
			}
		}
		Map<String,String> itemModeMap = new HashMap<String,String>();
		if(itemChargeMode != null){
			for(int i=0;i<itemNo.length;i++){
				itemModeMap.put(itemNo[i], itemChargeMode[i]);
			}
		}
		for(ChargeSerItemBo csi:csiBo){
			csi.setServicePrice(m.get(csi.getItemNo()));
			csi.setFreeNum(freeMap.get(csi.getItemNo()));
			csi.setItemChargeMode(itemModeMap.get(csi.getItemNo()));
			LOGGER.debug("=============csi.getItemNo():"+itemModeMap.get(csi.getItemNo()));
		}
	}
}
