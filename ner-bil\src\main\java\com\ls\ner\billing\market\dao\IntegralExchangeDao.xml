<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ls.ner.billing.market.dao.IntegralExchangeDao">

    <resultMap id="integralExchange" type="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        <result column="INTEGRAL_ID" property="integralId" jdbcType="VARCHAR"/>
        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"/>
        <result column="CUST_ID" property="custId" jdbcType="VARCHAR"/>
        <result column="FILE_ID" property="fileId" jdbcType="VARCHAR"/>
        <result column="CUST_MOBILE" property="custMobile" jdbcType="VARCHAR"/>
        <result column="EXCHANGE_TIME" property="exchangeTime" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="NUMBER" property="number" jdbcType="INTEGER"/>
        <result column="INTEGRAL" property="integral" jdbcType="VARCHAR"/>
        <result column="CONSIGNEE" property="consignee" jdbcType="VARCHAR"/>
        <result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_NAME" property="logisticsName" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR"/>
        <result column="GOODS_TYPE" property="goodsType" jdbcType="VARCHAR"/>
        <result column="GOODS_VR_TYPE" property="goodsVrType" jdbcType="VARCHAR"/>
        <result column="goods_mode" property="goodsMode" jdbcType="VARCHAR"/>
        <result column="goods_amt" property="goodsAmt" jdbcType="DECIMAL"/>
        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="GOODS_FREIGHT" property="goodsFreight" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getIntegralExchangeList" resultMap="integralExchange"
            parameterType="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        select INTEGRAL_ID,GOODS_ID,CUST_ID,CUST_MOBILE,FILE_ID,goods_mode,goods_amt,
        date_format(EXCHANGE_TIME,'%Y-%m-%d %H:%i:%s') EXCHANGE_TIME,STATUS,NUMBER,INTEGRAL,CONSIGNEE,
        ADDRESS,LOGISTICS_NAME,LOGISTICS_NO,GOODS_TYPE, GOODS_NAME,MOBILE,GOODS_FREIGHT
        from e_integral_exchange
        <where>
            <if test="integralId != null and integralId !=''">
                AND INTEGRAL_ID = #{integralId}
            </if>
            <if test="goodsId != null and goodsId !='' ">
                AND GOODS_ID = #{goodsId}
            </if>
            <if test="custId != null and custId !=''">
                AND CUST_ID = #{custId}
            </if>
            <if test="custMobile != null and custMobile !=''">
                AND CUST_MOBILE = #{custMobile}
            </if>
            <if test="status != null and status !=''">
                AND status = #{status}
            </if>
            <if test="goodsType != null and goodsType !=''">
                AND GOODS_TYPE = #{goodsType}
            </if>
            <if test="goodsName != null and goodsName !=''">
                AND GOODS_NAME like concat('%',#{goodsName},'%')
            </if>
            <if test="startTime != null and startTime!=''">
                AND EXCHANGE_TIME >= #{startTime}
            </if>
            <if test="endTime != null and endTime!=''">
                <![CDATA[AND EXCHANGE_TIME <= #{endTime}]]>
            </if>
        </where>
        ORDER BY EXCHANGE_TIME desc
        <if test="end!=null and end!=0">
            limit #{begin} ,#{end}
        </if>
    </select>

    <select id="getIntegralExchangeListByWX" resultMap="integralExchange"
            parameterType="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        select a.INTEGRAL_ID,
               a.GOODS_ID,
               a.CUST_ID,
               a.CUST_MOBILE,
               a.FILE_ID,
               a.goods_mode,
               a.goods_amt,
               date_format(a.EXCHANGE_TIME,'%Y-%m-%d %H:%i:%s') EXCHANGE_TIME,
               a.STATUS,
               a.NUMBER,
               a.INTEGRAL,
               a.CONSIGNEE,
               a.ADDRESS,
               a.LOGISTICS_NAME,
               a.LOGISTICS_NO,
               a.GOODS_TYPE,
               b.GOODS_VR_TYPE,
               a.GOODS_NAME,
               a.MOBILE,
               a.GOODS_FREIGHT
        from e_integral_exchange a
        left join ner_public.p_goods b on a.goods_id = b.goods_id
        <where>
            <if test="integralId != null and integralId !=''">
                AND a.INTEGRAL_ID = #{integralId}
            </if>
            <if test="goodsId != null and goodsId !='' ">
                AND a.GOODS_ID = #{goodsId}
            </if>
            <if test="custId != null and custId !=''">
                AND a.CUST_ID = #{custId}
            </if>
            <if test="custMobile != null and custMobile !=''">
                AND a.CUST_MOBILE = #{custMobile}
            </if>
            <if test="status != null and status !=''">
                AND a.status = #{status}
            </if>
            <if test="goodsType != null and goodsType !=''">
                AND a.GOODS_TYPE = #{goodsType}
            </if>
            <if test="goodsName != null and goodsName !=''">
                AND a.GOODS_NAME like concat('%',#{goodsName},'%')
            </if>
            <if test="startTime != null and startTime!=''">
                AND a.EXCHANGE_TIME >= #{startTime}
            </if>
            <if test="endTime != null and endTime!=''">
                <![CDATA[AND a.EXCHANGE_TIME <= #{endTime}]]>
            </if>
        </where>
        ORDER BY EXCHANGE_TIME desc
        <if test="limit!=null and limit!=0">
            limit #{start} ,#{limit}
        </if>
    </select>

    <select id="getExchangeCount" resultType="int"
            parameterType="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        select COALESCE(SUM(NUMBER), 0)
        from e_integral_exchange
        <where>
            <if test="goodsId != null and goodsId !='' ">
                AND GOODS_ID = #{goodsId}
            </if>
            <if test="custId != null and custId !=''">
                AND CUST_ID = #{custId}
            </if>
        </where>
    </select>

    <select id="getIntegralExchangeListCount" resultType="int"
            parameterType="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        select count(1)
        from e_integral_exchange
        <where>
            <if test="integralId != null and integralId !=''">
                AND INTEGRAL_ID = #{integralId}
            </if>
            <if test="goodsId != null and goodsId !='' ">
                AND GOODS_ID = #{goodsId}
            </if>
            <if test="custId != null and custId !=''">
                AND CUST_ID = #{custId}
            </if>
            <if test="custMobile != null and custMobile !=''">
                AND CUST_MOBILE = #{custMobile}
            </if>
            <if test="status != null and status !=''">
                AND status = #{status}
            </if>
            <if test="goodsType != null and goodsType !=''">
                AND GOODS_TYPE = #{goodsType}
            </if>
            <if test="goodsName != null and goodsName !=''">
                AND GOODS_NAME like concat('%',#{goodsName},'%')
            </if>
            <if test="startTime != null and startTime!=''">
                AND EXCHANGE_TIME >= #{startTime}
            </if>
            <if test="endTime != null and endTime!=''">
                <![CDATA[AND EXCHANGE_TIME <= #{endTime}]]>
            </if>
        </where>
    </select>
    <select id="queryEva" resultType="com.ls.ner.billing.api.market.vo.EvaVO">
        select b.EVA_ID evaId,
               b.EVA_USER_NAME evaUserName,
               b.EVA_SCORE evaScore,
               b.EVA_REMARK evaRemark,
               b.eva_url evaUrl,
               b.REPLY_REMARK replyRemark
        from ner_order.o_order a
            join ner_order.o_order_eva b on b.order_id = a.order_id
        where a.ORDER_NO = #{integralId}
        limit 1
    </select>
    <select id="queryEvaItems" resultType="java.util.Map">
        select
               EVA_ITEM_CODE evaItemCode,
               EVA_SCORE evaScore
        from ner_order.o_order_eva_item
        where EVA_ID = #{evaId}
    </select>
    <update id="update" parameterType="com.ls.ner.billing.market.bo.IntegeralRuleBo">
        update e_integral_exchange
        <set>
            <if test="logisticsName != null and logisticsName!='' ">
                LOGISTICS_NAME = #{logisticsName} ,
            </if>
            <if test="logisticsNo != null and logisticsNo!='' ">
                LOGISTICS_NO = #{logisticsNo} ,
            </if>
            <if test="status != null and status!='' ">
                status = #{status} ,
            </if>
        </set>
        where INTEGRAL_ID = #{integralId}
    </update>

    <insert id="insert" parameterType="com.ls.ner.billing.market.vo.IntegralExchangeVo">
        INSERT INTO e_integral_exchange(
        <if test=" goodsId !=null and goodsId != '' ">GOODS_ID,</if>
        <if test=" custId !=null and custId != '' ">CUST_ID,</if>
        <if test=" custMobile !=null and custMobile != '' ">CUST_MOBILE,</if>
        <if test=" exchangeTime !=null and exchangeTime != '' ">EXCHANGE_TIME,</if>
        <if test=" status !=null and status != '' ">STATUS,</if>
        <if test=" number !=null and number != '' ">NUMBER,</if>
        <if test=" integral !=null and integral != '' ">INTEGRAL,</if>
        <if test=" consignee !=null and consignee != '' ">CONSIGNEE,</if>
        <if test=" address !=null and address != '' ">ADDRESS,</if>
        <if test=" logisticsName !=null and logisticsName != '' ">LOGISTICS_NAME,</if>
        <if test=" logisticsNo !=null and logisticsNo != '' ">LOGISTICS_NO,</if>
        <if test=" goodsType !=null and goodsType != '' ">GOODS_TYPE,</if>
        <if test=" goodsMode !=null and goodsMode != '' ">goods_mode,</if>
        <if test=" goodsAmt !=null and goodsAmt != '' ">goods_amt,</if>
        <if test=" goodsName !=null and goodsName != '' ">GOODS_NAME,</if>
        <if test=" mobile !=null and mobile != '' ">MOBILE,</if>
        <if test=" goodsFreight !=null and goodsFreight != '' ">GOODS_FREIGHT,</if>
        <if test=" fileId !=null and fileId != '' ">FILE_ID,</if>
        <if test=" payChannel !=null and payChannel != '' ">PAY_CHANNEL,</if>
        INTEGRAL_ID
        )
        values (
        <if test=" goodsId !=null and goodsId != '' ">#{goodsId},</if>
        <if test=" custId !=null and custId != '' ">#{custId},</if>
        <if test=" custMobile !=null and custMobile != '' ">#{custMobile},</if>
        <if test=" exchangeTime !=null and exchangeTime != '' ">#{exchangeTime},</if>
        <if test=" status !=null and status != '' ">#{status},</if>
        <if test=" number !=null and number != '' ">#{number},</if>
        <if test=" integral !=null and integral != '' ">#{integral},</if>
        <if test=" consignee !=null and consignee != '' ">#{consignee},</if>
        <if test=" address !=null and address != '' ">#{address},</if>
        <if test=" logisticsName !=null and logisticsName != '' ">#{logisticsName},</if>
        <if test=" logisticsNo !=null and logisticsNo != '' ">#{logisticsNo},</if>
        <if test=" goodsType !=null and goodsType != '' ">#{goodsType},</if>
        <if test=" goodsMode !=null and goodsMode != '' ">#{goodsMode},</if>
        <if test=" goodsAmt !=null and goodsAmt != '' ">#{goodsAmt},</if>
        <if test=" goodsName !=null and goodsName != '' ">#{goodsName},</if>
        <if test=" mobile !=null and mobile != '' ">#{mobile},</if>
        <if test=" goodsFreight !=null and goodsFreight != '' ">#{goodsFreight},</if>
        <if test=" fileId !=null and fileId != '' ">#{fileId},</if>
        <if test=" payChannel !=null and payChannel != '' ">#{payChannel},</if>
        #{integralId}
        )
    </insert>

    <update id="updateInvoiceFlag" parameterType="com.ls.ner.billing.api.market.vo.IntegralInvoiceUpdateBo">
        update e_integral_exchange
        <set>
            <if test="invoiceFlag != null">
                INVOICE_FLAG = #{invoiceFlag},
            </if>
        </set>
        WHERE INTEGRAL_ID IN
        <foreach collection="integralOrderNos" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
