package com.ls.ner.billing.utils;

import com.ls.ner.base.cache.RedisCluster;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

/**
 * Redis 分布式锁工具类（基于 Redis 的 SETNX + GETSET 实现）
 *
 * 用途：
 * - 控制多个线程或节点对共享资源的并发访问（如：systemId 对应的数据更新）
 * - 防止数据库死锁或重复操作
 *
 * 特性：
 * - 支持自动重试获取锁
 * - 支持自定义锁过期时间
 * - 安全释放锁（仅当当前锁未被其他线程修改时才删除）
 */
public class RedisDistributedLock {

    private static final Logger log = LoggerFactory.getLogger(RedisDistributedLock.class);

    // 默认锁的过期时间（单位：秒），防止锁被永久占用
    private static final int DEFAULT_EXPIRE_SECONDS = 5;

    // 获取锁失败时的最大重试次数
    private static final int DEFAULT_RETRY_TIMES = 3;

    // 每次重试之间的等待时间（单位：毫秒）
    private static final long SLEEP_INTERVAL = 100;

    // RedisCluster 实例，用于执行 Redis 命令
    private final RedisCluster redisCluster;

    /**
     * 构造函数
     *
     * @param redisCluster RedisCluster 实例
     */
    public RedisDistributedLock(RedisCluster redisCluster) {
        this.redisCluster = redisCluster;
    }

    /**
     * 尝试获取分布式锁，带重试机制
     *
     * @param lockKey       锁的业务 key（如 systemId）
     * @param expireSeconds 锁的过期时间（单位：秒）
     * @return 成功获取锁则返回对应的 UUID，失败返回 null
     * @throws InterruptedException 线程中断异常
     */
    public String tryLock(String lockKey, int expireSeconds) throws InterruptedException {
        String actualLockKey = "lock:charge:" + lockKey;

        for (int i = 0; i < DEFAULT_RETRY_TIMES; i++) {
            String uuid = acquireLock(actualLockKey, expireSeconds);
            if (uuid != null) {
                log.info("获取锁成功，key={}", actualLockKey);
                return uuid;
            }
            log.warn("第 {} 次尝试获取锁失败，等待 {} ms 后重试", i + 1, SLEEP_INTERVAL);
            Thread.sleep(SLEEP_INTERVAL); // 等待一段时间后重试
        }
        log.warn("多次尝试获取锁失败，key={}", actualLockKey);
        return null;
    }

    /**
     * 尝试获取锁（不进行重试）
     *
     * @param lockKey       锁的标识 key
     * @param expireSeconds 锁的过期时间（单位：秒）
     * @return 是否成功获取锁
     */
    public String tryLockOnce(String lockKey, int expireSeconds) {
        String actualLockKey = "lock:charge:" + lockKey;
        return acquireLock(actualLockKey, expireSeconds);
    }

    /**
     * 释放指定 key 的锁
     *
     * @param lockKey 锁的标识 key
     * @param uuid 当前持有的 UUID
     */
    public void releaseLock(String lockKey, String uuid) {
        String actualLockKey = "lock:charge:" + lockKey;
        releaseLockInternal(actualLockKey, uuid);
    }

    /**
     * 获取锁的核心逻辑
     *
     * 实现方式：
     * - 使用 setNx 设置锁（如果 key 不存在则设置成功）
     * - 如果 key 已存在且已过期，则使用 getSet 替换值并加锁
     *
     * @param lockKey       锁的 key
     * @param expireSeconds 锁的过期时间（单位：秒）
     * @return 是否成功获取锁
     */
    private String acquireLock(String lockKey, int expireSeconds) {
        try {
            String uuid = UUID.randomUUID().toString();
            long expires = System.currentTimeMillis() + ((long) expireSeconds * 1000) + 1L;
            String lockValue = uuid + ":" + expires;

            // 尝试设置锁
            if (redisCluster.setNx(lockKey, lockValue) == 1L) {
                redisCluster.expire(lockKey, expireSeconds);
                return uuid;
            }

            // 锁已存在，检查是否过期
            Object currentObj = redisCluster.get(lockKey);
            if (currentObj == null) {
                return null; // 锁已经被释放
            }

            String currentValueStr = currentObj.toString();
            String[] parts = currentValueStr.split(":", 2);
            if (parts.length < 2) {
                return null; // 格式不合法
            }

            long currentExpireTime = Long.parseLong(parts[1]);
            if (currentExpireTime < System.currentTimeMillis()) {
                // 锁已过期，尝试用 getSet 替换旧值
                String oldValueStr = redisCluster.getSet(lockKey, lockValue);
                if (oldValueStr != null && oldValueStr.equals(currentValueStr)) {
                    redisCluster.expire(lockKey, expireSeconds);
                    return uuid;
                }
            }

            return null; // 获取锁失败

        } catch (Exception e) {
            log.error("获取分布式锁异常，lockKey={}", lockKey, e);
            return null;
        }
    }

    /**
     * 释放锁的内部逻辑
     *
     * 安全释放逻辑：
     * - 只有当前锁是自己加的才删除
     *
     * @param lockKey 锁的 key
     * @param uuid 当前持有的 UUID
     */
    private void releaseLockInternal(String lockKey, String uuid) {
        try {
            Object currentObj = redisCluster.get(lockKey);
            if (currentObj == null) {
                log.info("锁已被释放，无需操作，key={}", lockKey);
                return;
            }

            String currentValue = currentObj.toString();
            if (StringUtils.isNotEmpty(currentValue)) {
                String[] parts = currentValue.split(":", 2);
                if (parts.length >= 1 && parts[0].equals(uuid)) {
                    // 确认是自己加的锁，可以删除
                    redisCluster.remove(lockKey);
                    log.info("成功释放锁，key={}", lockKey);
                } else {
                    log.warn("锁不属于当前线程，跳过删除，key={}, expectedUuid={}, actualValue={}",
                            lockKey, uuid, currentValue);
                }
            }
        } catch (Exception e) {
            log.error("释放分布式锁异常，lockKey={}", lockKey, e);
        }
    }
}
