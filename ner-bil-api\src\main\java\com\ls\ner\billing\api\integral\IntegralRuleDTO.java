package com.ls.ner.billing.api.integral;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public class IntegralRuleDTO implements Serializable {
    private String ruleId;                 // 规则ID
    private String ruleName;               // 规则名称
    private String eventType;              // 事件类型
    private String ruleStatus;             // 规则状态
    private String isDiscriminateUser;     // 是否区分用户
    private String userType;               // 用户类型
    private Double money;                  // 金额
    private Integer maxTimes;              // 最大次数
    private Double chargingPq;             // 计费PQ
    private String extendJson;             // 扩展JSON

    private IntegeralRuleSignExtendDTO ruleSignExtend;

    public IntegeralRuleSignExtendDTO getRuleSignExtend() {
        return ruleSignExtend;
    }

    public void setRuleSignExtend(IntegeralRuleSignExtendDTO ruleSignExtend) {
        this.ruleSignExtend = ruleSignExtend;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getRuleStatus() {
        return ruleStatus;
    }

    public void setRuleStatus(String ruleStatus) {
        this.ruleStatus = ruleStatus;
    }

    public String getIsDiscriminateUser() {
        return isDiscriminateUser;
    }

    public void setIsDiscriminateUser(String isDiscriminateUser) {
        this.isDiscriminateUser = isDiscriminateUser;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Double getMoney() {
        return money;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    public Integer getMaxTimes() {
        return maxTimes;
    }

    public void setMaxTimes(Integer maxTimes) {
        this.maxTimes = maxTimes;
    }

    public Double getChargingPq() {
        return chargingPq;
    }

    public void setChargingPq(Double chargingPq) {
        this.chargingPq = chargingPq;
    }

    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }
}
