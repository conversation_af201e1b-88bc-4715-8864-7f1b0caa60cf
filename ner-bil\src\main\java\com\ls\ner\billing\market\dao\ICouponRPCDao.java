package com.ls.ner.billing.market.dao;

import com.ls.ner.billing.market.bo.BillBo;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.bo.CouponContentBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 描述:RPC05-11 优惠券RPC接口 [实现DAO]
 * CouponRPCServiceImpl.java
 * 作者：biaoxiangd
 * 创建日期：2017-06-06 17:23
 **/
public interface ICouponRPCDao {

    /**
     *描述:获取RPC05-11-01 优惠券详情
     * @param: [list]
     *@return: java.util.List<com.ls.ner.billing.market.bo.CouponBo>
     *创建人:biaoxiangd
     *创建时间: 2017-06-08 14:15
     */
    List<CouponContentBo> queryCouponList(List list);
    /**
     * 描述: 验证订单是否存在并获取费用总金额
     *
     * @param: [orderId]
     * @return: java.util.List<com.ls.ner.billing.market.bo.BillBo>
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-06 18:01
     */
    List<BillBo> queryBillInfo(@Param("orderNo") String orderNo);

    /**
     * 描述:查询优惠券信息
     *
     * @param: [cpnId]
     * @param: [prodBusiType]
     * @return: com.ls.ner.billing.market.bo.CouponBo
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 9:31
     */
    CouponBo queryCouponInfo(@Param("cpnId") String cpnId, @Param("prodBusiType") String prodBusiType);

    /**
     * 描述:新增订单优惠券流水
     *
     * @param: [billBo]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 9:30
     */
    int insertBillCoupon(BillBo billBo);

    /**
     * 描述:更新订单计费结果中的订单优惠总金额
     *
     * @param: [orderNo]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-07 9:33
     */
    int updateBill(@Param("spTAmt") String spTAmt, @Param("orderNo") String orderNo);

    /**
     * @Description: 查询订单数量
     * @method: queryBillNum
     * @param inMap
     * @return: int
     * @Author: cailianL
     * @Time: 2017/7/14 9:37
     */
    int queryBillNum(Map<String, Object> inMap);


    /**
     * 描述:通过过滤条件查询优惠券信息
     *
     * @param: queryMap
     * 创建人:songyuhang
     * 创建时间: 2018-11-14
     */
    List<CouponContentBo> queryCouponListByCondition(Map<String, Object> queryMap);

    /**
     * @param queryMap
     * @description 通过条件过滤我的优惠券
     * <AUTHOR>
     * @create 2019-05-26 12:14:26
     */
    List<CouponContentBo> queryMyCouponListByCondition(Map<String, Object> queryMap);


    /**
     * @param queryMap
     * @description
     * <AUTHOR>
     * @create 2019-05-26 16:37:55
     */
    List<Map<String,Object>> queryMyCouponInfo(Map<String, Object> queryMap);
}
