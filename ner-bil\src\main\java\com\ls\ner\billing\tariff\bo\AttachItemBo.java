/**
 *
 * @(#) AttachItemBo.java
 * @Package com.ls.ner.billing.tariff.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.bo;

import java.io.Serializable;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: AttachItemBo.java,v 1.1 2016/02/26 02:34:00 34544 Exp $
 * 
 *          History: 2016年2月23日 下午5:22:40 lipf Created.
 * 
 */
public class AttachItemBo implements Serializable {
	private static final long serialVersionUID = 4013654741568696693L;
	private String planNo;
	private String attachNo;
	private String itemName;
	private String itemNo;
	private String chargeType;
	private String buyIdentity;
	private String remark;

	public String getPlanNo() {
		return planNo;
	}

	public void setPlanNo(String planNo) {
		this.planNo = planNo;
	}

	public String getAttachNo() {
		return attachNo;
	}

	public void setAttachNo(String attachNo) {
		this.attachNo = attachNo;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}

	public String getBuyIdentity() {
		return buyIdentity;
	}

	public void setBuyIdentity(String buyIdentity) {
		this.buyIdentity = buyIdentity;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}
