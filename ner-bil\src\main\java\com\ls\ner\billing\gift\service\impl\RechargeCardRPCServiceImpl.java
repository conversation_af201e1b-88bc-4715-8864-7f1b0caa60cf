package com.ls.ner.billing.gift.service.impl;

import com.ls.ner.billing.api.card.IRechargeCardRPCService;
import com.ls.ner.billing.gift.bo.RechargeCardBo;
import com.ls.ner.billing.gift.service.IRechargeCardService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.gift.service.impl
 * @ClassName: RechargeCardRPCServiceImpl
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/5/8 16:00
 * @Version: 1.0
 */
@Service(target = { ServiceType.RPC }, value = "rechargeCardRPCService")
public class RechargeCardRPCServiceImpl implements IRechargeCardRPCService {

    @ServiceAutowired(value="rechargeCardService")
    private IRechargeCardService iRechargeCardService;

    @Override
    public Map<String, Object> rechargeCardCode(Map<String, String> map) {
        return iRechargeCardService.rechargeCardCode(map);
    }

    @Override
    public Map<String, Object> myRechargeCardInfos(Map<String, String> map) {
        return iRechargeCardService.myRechargeCardInfos(map);
    }

    @Override
    public List<Map<String, String>> getRechargeCardList() {
        return iRechargeCardService.getRechargeCardList();
    }

    @Override
    public Map<String, Object> rechargeCardInfo(Map<String, String> map) {
        Map<String, Object> result = new HashMap<>();
        result.put("actualAmt", BigDecimal.ZERO);
        if(map!=null){
            String cardId = StringUtil.nullToString(map.get("cardId"));
            if(StringUtils.isNotEmpty(cardId)){
                RechargeCardBo bo = new RechargeCardBo();
                bo.setCardId(Long.valueOf(cardId));
                RechargeCardBo queryDetail = iRechargeCardService.queryDetail(bo);
                if(queryDetail!=null){
                    result.put("actualAmt",queryDetail.getActualAmt());
                }
            }
        }
        return result;
    }

    @Override
    public void updateRechargeCardByOrder(Map<String, String> map) throws Exception {
        iRechargeCardService.updateRechargeCardByOrder(map);
    }

    @Override
    public List<String> cardOrderNoList(String cardId, String cardDetailId) {
        return  iRechargeCardService.cardOrderNoList(cardId, cardDetailId);
    }

    @Override
    public List<Map<String, Object>> queryCardOrderByOrderNo(Map<String, Object> map) {
        return iRechargeCardService.queryCardOrderByOrderNo(map);
    }

    @Override
    public List<Map<String, Object>> queryCardInfosByChargeOrderList(Map<String, Object> map) {
        return iRechargeCardService.queryCardInfosByChargeOrderList(map);
    }

    @Override
    public List<String> getOrderListByCardNo(Map<String, Object> map) {
        return iRechargeCardService.getOrderListByCardNo(map);
    }
}
