/**
 *
 * @(#) TariffCondition.java
 * @Package com.ls.ner.billing.vo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.vo;

import java.io.Serializable;

/**
 * 类描述：
 * 
 * @author: lipf
 * @version $Id: TariffCondition.java,v 1.1 2016/02/26 02:34:02 34544 Exp $
 * 
 *          History: 2016年2月18日 上午9:39:20 lipf Created.
 * 
 */
public class TariffCondition implements Serializable {

	private static final long serialVersionUID = -8572179506332474550L;
	private String peBe;// 业务大类：01租车 02 充电
	private String subBe;// 业务小类：0101时租，0102日租，0103周租，0104月租，0105年租

	public String getPeBe() {
		return peBe;
	}

	public void setPeBe(String peBe) {
		this.peBe = peBe;
	}

	public String getSubBe() {
		return subBe;
	}

	public void setSubBe(String subBe) {
		this.subBe = subBe;
	}

}
