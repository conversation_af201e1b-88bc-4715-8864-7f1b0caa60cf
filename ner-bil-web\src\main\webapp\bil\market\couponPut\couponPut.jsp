<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01
Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="优惠券发放">
    <script  type="text/javascript" src="<%=request.getContextPath()%>/bil/comm/js/common.js" ></script>
</ls:head>
<ls:body>
    <ls:form name="couponManageForm" id="couponManageForm">
        <ls:title text="查询条件"></ls:title>
        <table width="100%" align="center" class="tab_search">
            <tr>
                <td><ls:label text="优惠券名称"></ls:label></td>
                <td><ls:text name="cpnName"></ls:text></td>

                <td><ls:label text="优惠券类型"></ls:label></td>
                <td>
                    <ls:select name="cpnType" property="cpnType" noClear="true">
                        <ls:options property="cpnTypeList" scope="request" text="codeName" value="codeValue"/>
                    </ls:select>
                </td>

                <td><ls:label text="优惠券内容"></ls:label></td>
                <td>
                    <ls:select name="busiType" property="busiType" noClear="true">
<%--                        <ls:options property="busiTypeList" scope="request" text="codeName" value="codeValue"/>--%>
                        <ls:option text="充电" value="02"></ls:option>
                    </ls:select>
                </td>
            </tr>

            <tr>
                <td colspan="6">
                    <div class="pull-right">
                        <ls:button text="查询" onclick="query"/>
                        <ls:button text="清空" onclick="clearAll"/>
                    </div>
                </td>
            </tr>
        </table>
        <table align="center" class="tab_search">
            <tr style="vertical-align: top">
                <td><ls:grid url="" name="couponGrid" caption="发放列表" showRowNumber="false"
                             height="350px" showCheckBox="false" singleSelect="true">
                    <ls:gridToolbar name="operation">
                        <ls:gridToolbarItem name="add" text="发放" imageKey="add"
                                            onclick="addCouponPut"></ls:gridToolbarItem>
                    </ls:gridToolbar>
                    <ls:column caption="优惠券主键" name="cpnId" hidden="true"/>
                    <ls:column caption="生效日期" name="eftDate" hidden="true"/>
                    <ls:column caption="失效日期" name="invDate" hidden="true"/>
                    <ls:column caption="优惠券名称" name="cpnName" formatFunc="linkFunc" align="center" width="25%"/>
                    <ls:column caption="优惠券内容" name="busiType" hidden="true"/>
                    <ls:column caption="优惠券内容" name="busiTypeName" align="center" width="10%"/>
                    <ls:column caption="优惠券类型" name="cpnType" hidden="true"/>
                    <ls:column caption="优惠券类型" name="cpnTypeName" align="center" width="10%"/>
                    <ls:column caption="面额/折扣(元/折)" name="cpnAmt" align="right" width="10%"/>
                    <ls:column caption="发行数量" name="cpnNum" align="center" width="5%"/>
                    <ls:column caption="已领数量" name="alrdyGetNum" align="center" width="5%"/>
                    <ls:column caption="有效时间范围" name="effectTime" align="center" width="20%"/>
                    <%--<ls:column caption="优惠券状态" name="cpnStatusName" align="center" width="10%"/>--%>
                    <ls:column caption="状态" name="cpnStatus" hidden="true"/>
                    <ls:column caption="发放数量" name="putNum" align="center" formatFunc="putDet" width="5%"/>
                    <ls:column caption="是否预约" name="isDelayFlag" align="center" formatFunc="handleDelay" width="5%"/>
                    <ls:pager pageSize="15,20"></ls:pager>
                </ls:grid></td>
            </tr>
        </table>
    </ls:form>
    <ls:script>
        function handleDelay(rowdata){
        return rowdata.delay == 1? "是" : "否";
        }
        function putDet(rowdata){
        var _cpnId = rowdata.cpnId;
        var _putNum = rowdata.putNum;
        if(LS.isEmpty(_putNum)){
        _putNum = "0";
        }
        var iPutNum = parseInt(_putNum);
        if(iPutNum > 0){
        return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"'"
        + "onmouseout='this.style.color=\"#333333\"' onclick='showPutDetail(\"" + _cpnId + "\")'>"+_putNum+"</a></u>";
        }else{
        return _putNum;
        }
        }
        window.showPutDetail=function(_cpnId){
        LS.dialog("~/billing/couponPut/showPutDetail?cpnId="+_cpnId,"发放明细",850,480,true, null);
        }
        query();

        <%-- 描述:点击发放按钮  创建人:biaoxiangd  创建时间:2017-06-03 11:59 --%>
        function addCouponPut(){

            var item = couponGrid.getSelectedItem();
            var _cpnId = "";
            if(item!=null){
                _cpnId = item.cpnId;
            }else{
                LS.message("info","请选择一条记录!");
                return;
            }
            if(item.eftDate !=null && item.eftDate !=''){
                if((item.eftDate> nowDateFormat("yyyy-MM-dd")) || (nowDateFormat("yyyy-MM-dd") > item.invDate)){
                    LS.message("info","发放日期必须在优惠券有效时间范围内");
                    return;
                }
            }
            var _alrdyGetNum = parseInt(item.alrdyGetNum);
            var _cpnNum = parseInt(item.cpnNum);
            var _putNum = parseInt(item.putNum);
            if(_alrdyGetNum + _putNum >= _cpnNum){
                LS.message("info","发放+领取数量不能大于发行数量！");
                return;
            }
            LS.dialog("~/billing/couponPut/editCouponPut?cpnId="+_cpnId,"优惠券发放",850,450,true, null);
        }

        window.query=query;
        function query(){
        var datas={};
        datas = couponManageForm.getFormData();
        couponGrid.query("~/billing/couponPut/getCouponPut",datas);
        }
        function clearAll(){
        couponManageForm.clear();
        }
        function linkFunc(rowdata){
        var _cpnId = rowdata.cpnId;
        var _cpnName = escapeHtml(rowdata.cpnName);
        return "<u><a href='javascript:void(0);' onmouseover='this.style.color=\"#FFBE60\"' onmouseout='this.style.color=\"#333333\"' onclick='showCouponDetail(\"" + _cpnId + "\")'>"+_cpnName+"</a></u>";
        }
        function escapeHtml(unsafe) {
            if (typeof unsafe !== 'string') {
                return unsafe;
            }
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            const reg = /[&<>"']/g;
            return unsafe.replace(reg, match => map[match]);
        }

        window.showCouponDetail = function(_cpnId){
        LS.dialog("~/billing/coupon/initCouponDetail?cpnId="+_cpnId,"优惠券详细信息",850, 450,true);
        }

        <%--当前日期--%>
        function nowDateFormat(fmt){
            var d = new Date();
            var o = {
                "M+": d.getMonth() + 1, //月份
                "d+": d.getDate(), //日
                "h+": d.getHours(), //小时
                "m+": d.getMinutes(), //分
                "s+": d.getSeconds(), //秒
                "q+": Math.floor((d.getMonth() + 3) / 3), //季度
                "S": d.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        }

        window.onload = function() {
        initGridHeight('couponManageForm','couponGrid');
        }
    </ls:script>
</ls:body>
</html>
