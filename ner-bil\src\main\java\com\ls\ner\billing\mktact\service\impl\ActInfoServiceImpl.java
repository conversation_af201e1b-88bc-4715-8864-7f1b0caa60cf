package com.ls.ner.billing.mktact.service.impl;

import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.mktact.bo.ActInfoBo;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.dao.IActInfoDao;
import com.ls.ner.billing.mktact.dao.IMarketActDao;
import com.ls.ner.billing.mktact.service.IActInfoService;
import com.ls.ner.billing.mktact.service.IMarketActService;
import com.ls.ner.pub.api.attach.bo.AttachBo;
import com.ls.ner.pub.api.attach.service.IAttachRpcService;
import com.ls.ner.pub.api.msg.service.IMsgRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.IJsonUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.common.utils.json.JsonUtil;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 描述:资讯信息
 * ActInfoServiceImpl.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 11:47
 **/
@Service(target = {ServiceType.LOCAL}, value = "actInfoService")
public class ActInfoServiceImpl implements IActInfoService {
    private static final Logger logger = LoggerFactory.getLogger(ActInfoServiceImpl.class);

    @Autowired(required = true)
    protected IActInfoDao dao;

    @Autowired(required = true)
    protected IMarketActDao marketActDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "attachRpcService")
    private IAttachRpcService attachService;

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private IMsgRpcService msgRpcService;

    /**
     * 描述: 获取资讯信息
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangde
     * 创建时间:2017/6/27 20:42
     */
    @Override
    public ActInfoBo queryActInfo(ActInfoBo bo) {
        ActInfoBo infoBo = new ActInfoBo();
        if (bo != null && StringUtil.isNotBlank(bo.getActId())) {
            infoBo = dao.queryActInfo(bo);
            if (infoBo != null) {
                String picJson = infoBo.getCoberPic();
                if (StringUtil.isNotBlank(picJson)) {
                    infoBo.setAttachId(picJson);
                    AttachBo attachBo=new AttachBo();
                    //查询图片
                    attachBo.setFileId(picJson);
                    attachBo.setContentType("02");
                    attachBo.setRelaTable("mkt_act_info");
                    AttachBo picBo=  attachService.qryAttachInfo(attachBo);
                    infoBo.setRelaId(picBo.getRelaId());
                }
            }
        }

        return infoBo;
    }

    /**
     * 描述:保存资讯信息
     *
     * @param: [infoBo, request]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 18:04
     */
    @Override
    public int saveActInfo(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        boolean updateFlag = false;
        Map<String, String> map = new HashMap<String, String>();
        // 新增活动资讯e_mkt_act_info
        String actId = infoBo.getActId();
        if (StringUtil.isBlank(actId)) {
            throw new RuntimeException("发布活动资讯信息未获取活动编号，请核查");
        }
        String infoType = infoBo.getInfoType();
        if ("1".equals(infoType)) {
            if (StringUtil.isBlank(infoBo.getContent())) {
                throw new RuntimeException("发布活动资讯信息中的内容为空，请核查");
            }
            infoBo.setLinkUrl("");
        } else {
            if (StringUtil.isBlank(infoBo.getLinkUrl())) {
                throw new RuntimeException("发布活动资讯信息中的外部链接为空，请核查");
            }
            infoBo.setContent("");
        }
        String contentHtml = infoBo.getContentUrl();
        infoBo.setContentUrl("");
        flag = dao.saveActInfo(infoBo);
        if (flag > 0) {
            String actInfoId = infoBo.getActInfoId();
            // 保存html富文本附件
            map.put("relaId", actInfoId + "01");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "1");
            map.put("content", contentHtml);
            String contentUrlJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(contentUrlJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(contentUrlJson);
                if (contMap != null && contMap.size() > 0) {
                    String attachId = contMap.get("fileId");
                    if (StringUtil.isNotBlank(attachId)) {
                        String contentUrl =  attachId;
                        infoBo.setContentUrl(contentUrl);
                        updateFlag = true;
                    }
                }
            }

            // 保存图片附件
            map.put("relaId", actInfoId + "02");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "0");
            map.put("picFlag", infoBo.getPicFlag());
            String coberPicJson = this.uploadAttach(map, request);
            String coberPicName = "";
            if (StringUtil.isNotBlank(coberPicJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(coberPicJson);
                coberPicName = contMap.get("fileName");
                String attachId = contMap.get("fileId");
                if (StringUtil.isNotBlank(attachId)) {
                    infoBo.setCoberPic( attachId);
                    updateFlag = true;
                }
            }
            // 更新当前资讯的图片路径和富文本路径
            if (updateFlag) {
                flag = dao.updateActInfo(infoBo);
            }
            MarketActBo actBo = marketActService.queryByPrimaryKey(Long.parseLong(actId));
            MarketActBo marketActBo = new MarketActBo();
            marketActBo.setActId(Long.parseLong(actId));
            marketActBo.setActState(BillConstants.ActStatus.UNSTART);
            marketActDao.updateMarketActive(marketActBo);


            if (StringUtil.isNotEmpty(actBo.getActName()) && StringUtil.isNotEmpty(actBo.getActMarks())&&actBo.getActChannel().indexOf("01")>-1 && !"0".equals(actBo.getActState())) {
                Map<String, String> msmMap = new HashedMap();
                msmMap.put("msgContent", actBo.getActMarks());
                msmMap.put("msgTitle", actBo.getActName());
                msmMap.put("msgType", PublicConstants.MsgType.ACT_NOTICE);
                Map<String, String> link = new HashedMap();
                String pubPath = PublicConstants.ApplicationPath.getPubPath();
                String picUrl = pubPath + "/api/v0.1/attachs/"+infoBo.getCoberPic() + "?filename=" + coberPicName;
                link.put("picUrl",picUrl);
                if("2".equals(infoType)){//外部链接
                    link.put("contentUrl",infoBo.getLinkUrl());
                }else{
                    link.put("contentUrl",pubPath + "/api/v0.1/attachs/"+infoBo.getContentUrl());
                }
                link.put("actId",actId);
                try {
                    BufferedImage sourceImg = ImageIO.read(new URL(StringUtil.getString(picUrl)).openStream());
                    link.put("picWidth",sourceImg.getWidth()+"");
                    link.put("picHeight",sourceImg.getHeight()+"");
                } catch (Exception e) {
                    logger.error("pic",e);
                    link.put("picWidth","640");
                    link.put("picHeight","270");
                }
                msmMap.put("link", IJsonUtil.obj2Json(link));
                msmMap.put("route", PublicConstants.MsgRoute.ACT);
                if (DateTools.getSecondTonow(actBo.getEffTime() + ":00") < 0) {
                    msmMap.put("planSendTime", actBo.getEffTime() + ":00");
                }
                msgRpcService.doSendWithoutTemplate(msmMap);
            }
        }
        return flag;
    }

    /**
     * 描述:变更资讯信息
     *
     * @param: [infoBo, request]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 18:04
     */
    @Override
    public int updateActInfo(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        boolean updateFlag = false;
        Map<String, String> map = new HashMap<String, String>();
        // 新增活动资讯e_mkt_act_info
        String actId = infoBo.getActId();
        String actInfoId = infoBo.getActInfoId();
        if (StringUtil.isBlank(actId) && StringUtil.isBlank(actInfoId)) {
            throw new RuntimeException("发布活动资讯信息未获取活动编号，请核查");
        }
        String infoType = infoBo.getInfoType();
        if ("1".equals(infoType)) {
            if (StringUtil.isBlank(infoBo.getContent())) {
                throw new RuntimeException("发布活动资讯信息中的内容为空，请核查");
            }
            infoBo.setLinkUrl("");
        } else {
            if (StringUtil.isBlank(infoBo.getLinkUrl())) {
                throw new RuntimeException("发布活动资讯信息中的外部链接为空，请核查");
            }
            infoBo.setContent("");
        }
        String contentHtml = infoBo.getContentUrl();
        infoBo.setContentUrl("");
        flag = dao.updateActInfo(infoBo);
        if (flag > 0) {
            // 保存html富文本附件
            map.put("relaId", actInfoId + "01");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "1");
            map.put("content", contentHtml);
            String contentUrlJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(contentUrlJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(contentUrlJson);
                if (contMap != null && contMap.size() > 0) {
                    String attachId = contMap.get("fileId");
                    if (StringUtil.isNotBlank(attachId)) {
                        String contentUrl = attachId;
                        infoBo.setContentUrl(contentUrl);
                        updateFlag = true;
                    }
                }
            }
            // 保存图片附件
            map.put("relaId", actInfoId + "02");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "0");
            map.put("picFlag", infoBo.getPicFlag());
            String coberPicJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(coberPicJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(coberPicJson);
                String attachId = contMap.get("fileId");
                if (StringUtil.isNotBlank(attachId)) {
                    infoBo.setCoberPic(attachId);
                    updateFlag = true;
                }
            } /*else {
                infoBo.setCoberPic("");
                updateFlag = true;
            }*/

            if (updateFlag) {
                // 更新当前资讯的图片路径和富文本路径
                flag = dao.updateActInfo(infoBo);
            }
        }
        return flag;
    }

    /**
     * @param
     * @description 新版发布资讯
     * <AUTHOR>
     * @create 2019/6/4 11:32
     */
    @Override
    public int saveActInfoNew(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        boolean updateFlag = false;
        Map<String, String> map = new HashMap<String, String>();
        // 新增活动资讯e_mkt_act_info
        String actId = infoBo.getActId();
        if (StringUtil.isBlank(actId)) {
            throw new RuntimeException("发布活动资讯信息未获取活动编号，请核查");
        }
        String infoType = infoBo.getInfoType();
        if ("1".equals(infoType)) {
            if (StringUtil.isBlank(infoBo.getContent())) {
                throw new RuntimeException("发布活动资讯信息中的内容为空，请核查");
            }
            infoBo.setLinkUrl("");
        } else {
            if (StringUtil.isBlank(infoBo.getLinkUrl())) {
                throw new RuntimeException("发布活动资讯信息中的外部链接为空，请核查");
            }
            infoBo.setContent("");
        }
        String contentHtml = infoBo.getContentUrl();
        infoBo.setContentUrl("");
        flag = dao.saveActInfo(infoBo);
        if (flag > 0) {
            String actInfoId = infoBo.getActInfoId();
            // 保存html富文本附件
            map.put("relaId", actInfoId + "01");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "1");
            map.put("content", contentHtml);
            String contentUrlJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(contentUrlJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(contentUrlJson);
                if (contMap != null && contMap.size() > 0) {
                    String attachId = contMap.get("fileId");
                    if (StringUtil.isNotBlank(attachId)) {
                        String contentUrl =  attachId;
                        infoBo.setContentUrl(contentUrl);
                        updateFlag = true;
                    }
                }
            }

            // 保存图片附件
            map.put("relaId", actInfoId + "02");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "0");
            map.put("picFlag", infoBo.getPicFlag());
            String coberPicJson = this.uploadAttach(map, request);
            String coberPicName = "";
            if (StringUtil.isNotBlank(coberPicJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(coberPicJson);
                coberPicName = contMap.get("fileName");
                String attachId = contMap.get("fileId");
                if (StringUtil.isNotBlank(attachId)) {
                    infoBo.setCoberPic( attachId);
                    updateFlag = true;
                }
            }
            // 更新当前资讯的图片路径和富文本路径
            if (updateFlag) {
                flag = dao.updateActInfo(infoBo);
            }
            MarketActBo actBo = marketActService.queryByPrimaryKey(Long.parseLong(actId));
            if ("01".equals(actBo.getActType())){
                MarketActBo marketActBo = new MarketActBo();
                marketActBo.setActId(Long.parseLong(actId));
                marketActBo.setActState(BillConstants.ActStatus.UNSTART);
                marketActDao.updateMarketActive(marketActBo);
            }


            logger.info("资讯中查询获得的活动状态-------"+actBo.getActState());
            if (StringUtil.isNotEmpty(actBo.getActName()) && StringUtil.isNotEmpty(actBo.getActMarks())&& actBo.getActChannel().indexOf("01")>-1 && !"0".equals(actBo.getActState())) {
                logger.info("发送到活动列表-----------------");
                Map<String, String> msmMap = new HashedMap();
                msmMap.put("msgContent", actBo.getActMarks());
                msmMap.put("msgTitle", actBo.getActName());
                msmMap.put("msgType", PublicConstants.MsgType.ACT_NOTICE);
                Map<String, String> link = new HashedMap();
                String pubPath = PublicConstants.ApplicationPath.getPubPath();
                String picUrl = pubPath + "/api/v0.1/attachs/"+infoBo.getCoberPic() + "?filename=" + coberPicName;
                link.put("picUrl",picUrl);
                if("2".equals(infoType)){//外部链接
                    link.put("contentUrl",infoBo.getLinkUrl());
                }else{
                    link.put("contentUrl",pubPath + "/api/v0.1/attachs/"+infoBo.getContentUrl());
                }
                link.put("actId",actId);
                try {
                    BufferedImage sourceImg = ImageIO.read(new URL(StringUtil.getString(picUrl)).openStream());
                    link.put("picWidth",sourceImg.getWidth()+"");
                    link.put("picHeight",sourceImg.getHeight()+"");
                } catch (Exception e) {
                    logger.error("pic",e.getMessage());
                    link.put("picWidth","640");
                    link.put("picHeight","270");
                }
                msmMap.put("link", IJsonUtil.obj2Json(link));
                msmMap.put("route", PublicConstants.MsgRoute.ACT);
                if (DateTools.getSecondTonow(actBo.getEffTime() + ":00") < 0) {
                    msmMap.put("planSendTime", actBo.getEffTime() + ":00");
                }
                msmMap.put("msgSendId","");
                logger.info("发送消息-----------------"+msmMap);
                msgRpcService.doSendWithoutTemplate(msmMap);
            }
        }
        return flag;
    }

    /**
     * @param
     * @description 新版变更资讯信息
     * <AUTHOR>
     * @create 2019/6/4 11:32
     */
    @Override
    public int updateActInfoNew(ActInfoBo infoBo, MultipartHttpServletRequest request) throws Exception {
        int flag = 0;
        boolean updateFlag = false;
        Map<String, String> map = new HashMap<String, String>();
        // 新增活动资讯e_mkt_act_info
        String actId = infoBo.getActId();
        String actInfoId = infoBo.getActInfoId();
        if (StringUtil.isBlank(actId) && StringUtil.isBlank(actInfoId)) {
            throw new RuntimeException("发布活动资讯信息未获取活动编号，请核查");
        }
        String infoType = infoBo.getInfoType();
        if ("1".equals(infoType)) {
            if (StringUtil.isBlank(infoBo.getContent())) {
                throw new RuntimeException("发布活动资讯信息中的内容为空，请核查");
            }
            infoBo.setLinkUrl("");
        } else {
            if (StringUtil.isBlank(infoBo.getLinkUrl())) {
                throw new RuntimeException("发布活动资讯信息中的外部链接为空，请核查");
            }
            infoBo.setContent("");
        }
        String contentHtml = infoBo.getContentUrl();
        infoBo.setContentUrl("");
        flag = dao.updateActInfo(infoBo);
        if (flag > 0) {
            // 保存html富文本附件
            map.put("relaId", actInfoId + "01");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "1");
            map.put("content", contentHtml);
            String contentUrlJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(contentUrlJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(contentUrlJson);
                if (contMap != null && contMap.size() > 0) {
                    String attachId = contMap.get("fileId");
                    if (StringUtil.isNotBlank(attachId)) {
                        String contentUrl = attachId;
                        infoBo.setContentUrl(contentUrl);
                        updateFlag = true;
                    }
                }
            }
            // 保存图片附件
            map.put("relaId", actInfoId + "02");
            map.put("relaTable", "mkt_act_info");
            map.put("fileType", "0");
            map.put("picFlag", infoBo.getPicFlag());
            String coberPicJson = this.uploadAttach(map, request);
            if (StringUtil.isNotBlank(coberPicJson)) {
                Map<String, String> contMap = JsonUtil.parseJsonToMap(coberPicJson);
                String attachId = contMap.get("fileId");
                if (StringUtil.isNotBlank(attachId)) {
                    infoBo.setCoberPic(attachId);
                    updateFlag = true;
                }
            } /*else {
                infoBo.setCoberPic("");
                updateFlag = true;
            }*/

            if (updateFlag) {
                // 更新当前资讯的图片路径和富文本路径
                flag = dao.updateActInfo(infoBo);
            }
        }
        return flag;
    }

    @Override
    public void releaseAct(String actId,String isLink) throws Exception {
        // 新增活动资讯e_mkt_act_info
        if ("null".equals(actId) || StringUtil.isBlank(actId)) {
            throw new RuntimeException("发布活动资讯信息未获取活动编号，请核查");
        }
        MarketActBo marketActBo = new MarketActBo();
        marketActBo.setActId(Long.parseLong(actId));
        marketActBo.setActState(BillConstants.ActStatus.UNSTART);
        marketActBo.setIsLink(isLink);
        marketActDao.updateMarketActive(marketActBo);
    }

    @Override
    public void updateActForIsLink(MarketActBo infoBo) throws Exception {
        MarketActBo marketActBo = new MarketActBo();
        marketActBo.setActId(infoBo.getActId());
        marketActBo.setIsLink(infoBo.getIsLink());
        marketActDao.updateMarketActive(marketActBo);
    }


    /**
     * 描述:图片上传
     *
     * @param: [map, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 12:00
     */
    private String uploadAttach(Map<String, String> map, MultipartHttpServletRequest request) throws Exception {
        String retJson = "";
        String relaId = map.get("relaId");
        String relaTable = map.get("relaTable");
        String fileType = map.get("fileType"); // 0:文件上传按钮，1:富文本
        String picFlag = map.get("picFlag"); // 图片处理：0新增，1删除
        AttachBo attachBo = null;
        String filePath = "", attachId = "";
        String fileId = "";
        if ("0".equals(fileType)) {   // 图片处理
            if ("0".equals(picFlag)) {
                Iterator<String> itr = request.getFileNames();
                while (itr.hasNext()) {
                    MultipartFile file = request.getFile(itr.next());
                    if (file != null) {
                        attachBo = new AttachBo();
                        attachBo.setRelaId(relaId);
                        attachBo.setRelaTable(relaTable);
                        attachBo.setContentType("02");
                        attachBo.setFileSize(file.getSize() + "");
                        attachBo.setFileName(file.getOriginalFilename());
                        attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(file.getInputStream()), attachBo);
                    }
                }
            } else {
                // 删除
                attachService.deleteByRelaId(relaId, relaTable);
            }
        } else if ("1".equals(fileType)) {
            String content = map.get("content");
            if (StringUtil.isNotBlank(content)) {
                attachBo = new AttachBo();
                attachBo.setRelaId(relaId);
                attachBo.setRelaTable(relaTable);
                attachBo.setContentType("01");
                attachBo.setFileName(relaId + DateTools.getStringDateShort("yyyyMMddHHmmss") + ".html");
                InputStream sbs = new ByteArrayInputStream(content.toString().getBytes());
                attachBo = attachService.uploadOneAttach(IOUtils.toByteArray(sbs), attachBo);
            }
        }
        if (attachBo != null) {
            filePath = attachBo.getFilePath();
            attachId = attachBo.getAttachId();
            fileId = attachBo.getFileId();
            retJson = "{\"fileId\":\""+fileId+"\",\"filePath\":\"" + filePath + "\",\"attachId\":\"" + attachId + "\",\"fileName\":\"" + attachBo.getFileName() + "\"}";
        }

        return retJson;
    }
}
