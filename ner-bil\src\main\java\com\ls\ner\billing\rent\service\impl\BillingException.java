package com.ls.ner.billing.rent.service.impl;

public class BillingException extends RuntimeException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public BillingException() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BillingException(String message, Throwable cause,
			boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
		// TODO Auto-generated constructor stub
	}

	public BillingException(String message, Throwable cause) {
		super(message, cause);
		// TODO Auto-generated constructor stub
	}

	public BillingException(String message) {
		super(message);
		// TODO Auto-generated constructor stub
	}

	public BillingException(Throwable cause) {
		super(cause);
		// TODO Auto-generated constructor stub
	}

}
