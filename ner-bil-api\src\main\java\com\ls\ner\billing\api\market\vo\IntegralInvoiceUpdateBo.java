package com.ls.ner.billing.api.market.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @ProjectName: ner-bil-boot
 * @Package: com.ls.ner.billing.api.market.vo
 * @ClassName: IntegralInvoiceUpdateBo
 * @Author: bdBoWenYang
 * @Description:
 * @Date: 2025/3/10 22:12
 * @Version: 1.0
 */
public class IntegralInvoiceUpdateBo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> integralOrderNos;

    private String invoiceFlag;


    public List<String> getIntegralOrderNos() {
        return integralOrderNos;
    }

    public void setIntegralOrderNos(List<String> integralOrderNos) {
        this.integralOrderNos = integralOrderNos;
    }

    public String getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(String invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }
}
