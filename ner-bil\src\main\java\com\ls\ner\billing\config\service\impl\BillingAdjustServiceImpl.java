/**
 *
 * @(#) BillingAdjustServiceImpl.java
 * @Package com.ls.ner.billing.config.service.impl
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ls.ner.pub.api.orgmgr.service.IOrgRpcService;
import com.ls.ner.util.JodaDateTime;
import com.ls.ner.util.json.JsonMapper;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.rent.model.BillingFunc;
import com.ls.ner.billing.api.rent.model.RangeChargeItem;
import com.ls.ner.billing.config.bo.BillingConfigFunc;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.bo.CalendarDataVo;
import com.ls.ner.billing.config.dao.IBillingConfigDao;
import com.ls.ner.billing.config.service.IBillingAdjustService;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年4月10日 下午2:07:38   lipf   Created.
 *           
 */
@Component
public class BillingAdjustServiceImpl implements IBillingAdjustService {
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	ICodeService codeService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private Authentication authentication;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private IOrgService orgService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC,value="orgRpcService")
	private IOrgRpcService orgRpcService;
	
	@Autowired
	IBillingConfigDao billingConfigDao;
	/**
	 * 
	 * 方法说明：车型调价表格数据查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:08:26
	 * History:  2016年4月10日 下午2:08:26   lipf   Created.
	 *
	 * @param condition
	 * @return
	 * @throws InvocationTargetException 
	 * @throws IllegalAccessException 
	 *
	 */
	public List<BillingConfigFunc> getBillingAdjustList(BillingConfigQueryCondition condition) throws IllegalAccessException, InvocationTargetException {
		String nowDay = JodaDateTime.getFormatDate("yyyy-MM-dd");
		//当前月份与传入的月份进行比较  如果是同月 则当前日期至月底的时间段。
		//如果不是同月 则是当月1号至当月月底时间段   
		//前台传入的日期格式是2015-05  
		if(!nowDay.substring(0, 7).equals(condition.getApplyDate())){
			nowDay = condition.getApplyDate()+"-01";
		}
		String lastDay = JodaDateTime.getLastDay(nowDay,"yyyy-MM-dd");
		List<String> dayArray = JodaDateTime.getYmdArray(nowDay, lastDay, "yyyy-MM-dd");
		condition.setRows(dayArray.size());//总条数
		int pageEnd = condition.getPageEnd()-1;
		if(pageEnd>=dayArray.size()){
			pageEnd = dayArray.size()-1;
		}
		condition.setApplyDate(dayArray.get(pageEnd));
		//查询时间段内的定价和调价信息  根据orgCode、subBe、autoModelNo、applyDate
		List<BillingConfigFunc> dataList = getBillingAdjust(condition);
		Map<String,BillingConfigFunc> map= new HashMap<String, BillingConfigFunc>();
		if(dataList!=null&&dataList.size()>0){
			for (BillingConfigFunc billingConfigFunc : dataList) {
				map.put(billingConfigFunc.getApplyDate(), billingConfigFunc);
				formatFunc(billingConfigFunc);//定价详情拼装 格式化为details
			}
		}else{
			condition.setRows(0);
			return new ArrayList<BillingConfigFunc>();
		}
		List<BillingConfigFunc> list = new ArrayList<BillingConfigFunc>();
		for (int i = condition.getPageBegin()-1; i <= pageEnd; i++) {
			BillingConfigFunc func = new BillingConfigFunc();
			if(map.get(dayArray.get(i))== null){
				BeanUtils.copyProperties(map.get("99999999"), func);
			}else{
				BeanUtils.copyProperties(map.get(dayArray.get(i)), func);
			}
			func.setApplyDate(dayArray.get(i));
			list.add(func);
		}
		return list;
	}

	/*
	 * 
	 */
	private void formatFunc(BillingConfigFunc func) {
		BillingFunc billingFunc = JsonMapper.DEFAULT.fromJson(func.getContent(), BillingFunc.class);
//		func.setMinLimitPrice(billingFunc.getMainChargeItem().getMin().getLimitQuantity()+"元");
//		func.setMaxLimitPrice(billingFunc.getMainChargeItem().getMax().getLimitQuantity()+"元");
		StringBuffer sb = new StringBuffer();
		if(ChargeWay.BY_TIME.equals(billingFunc.getMainChargeItem().getChargeWay())||ChargeWay.BY_TIME_AND_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())){
			//按时间
			if(ChargeMode.STANDARD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//标准
				sb.append(billingFunc.getMainChargeItem().getTimeChargeItem().getPrice()+"元/"
						+ billingFunc.getMainChargeItem().getTimeChargeItem().getPriceUnit().getDesc()+"\n");
			} else if(ChargeMode.PERIOD.equals(billingFunc.getMainChargeItem().getChargeMode())||ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//分时
				List<RangeChargeItem> rangeChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem().getRangeChargeItems();
				for (RangeChargeItem item : rangeChargeItem) {
					sb.append(item.getRange()+billingFunc.getMainChargeItem().getTimeChargeItem().getPriceUnit().getUnitName()+":"+ item.getPrice()+"元/"+item.getPriceUnit().getDesc()+"\n");
				}
			} /*else if(ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//阶梯
			}*/
		}
		if(ChargeWay.BY_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())||ChargeWay.BY_TIME_AND_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())){
			//按里程
			if(ChargeMode.STANDARD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//标准
				sb.append(billingFunc.getMainChargeItem().getMillChargeItem().getPrice()+"元/"
						+ billingFunc.getMainChargeItem().getMillChargeItem().getPriceUnit().getDesc()+"\n");
			} else if(ChargeMode.PERIOD.equals(billingFunc.getMainChargeItem().getChargeMode())||ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//分时
				List<RangeChargeItem> rangeChargeItem = billingFunc.getMainChargeItem().getMillChargeItem().getRangeChargeItems();
				for (RangeChargeItem item : rangeChargeItem) {
					sb.append(item.getRange()+billingFunc.getMainChargeItem().getMillChargeItem().getPriceUnit().getUnitName()+":"+ item.getPrice()+"元/"+item.getPriceUnit().getDesc()+"\n");
				}
			}
		} /*else if(ChargeWay.BY_TIME_AND_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())){
			//按时间+里程
			if(ChargeMode.STANDARD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//标准
			} else if(ChargeMode.PERIOD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//分时
			} else if(ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//阶梯
			}
		}*/
		func.setDetails(sb.toString());
	}

	/**
	 * 
	 * 方法说明：车型调价表格数据记录数查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:08:22
	 * History:  2016年4月10日 下午2:08:22   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public int getBillingAdjustListCount(BillingConfigQueryCondition condition) {
		if (!StringUtils.nullOrBlank(condition.getOrgCode())) {
			String[] orgCode = condition.getOrgCode().split(",");
			List<String> orgCodes = new ArrayList<String>();
			for (String string : orgCode) {
				orgCodes.add(string);
			}
			condition.setOrgCodes(orgCodes);
			condition.setOrgCode(null);
		} else {
			try {
				condition.setOrgCodes(orgRpcService.getSubOrgCodes());
			} catch (Exception e) {
				Validate.isTrue(false, e.getMessage());
			}
		}
//		condition.setOrgCode("4600001");
		return billingConfigDao.getBillingAdjustListCount(condition);
	}

	@Override
	public List<CalendarDataVo> getBillingAdjustCalendar(BillingConfigQueryCondition condition) {
		String nowDay = JodaDateTime.getFormatDate("yyyy-MM-dd");
		//当前月份与传入的月份进行比较  如果是同月 则当前日期至月底的时间段。
		//如果不是同月 则是当月1号至当月月底时间段   
		//前台传入的日期格式是2015-05  
		if(nowDay.substring(0, 7).equals(condition.getApplyDate())){
			nowDay = condition.getApplyDate()+"-01";
		}
		String lastDay = JodaDateTime.getLastDay(nowDay,"yyyy-MM-dd");
		List<String> dayArray = JodaDateTime.getYmdArray(nowDay, lastDay, "yyyy-MM-dd");
		condition.setRows(dayArray.size());//总条数
		condition.setApplyDate(dayArray.get(dayArray.size()-1));
		List<BillingConfigFunc> dataList = getBillingAdjust(condition);
		Map<String,BillingConfigFunc> map= new HashMap<String, BillingConfigFunc>();
		if(dataList!=null&&dataList.size()>0){
			for (BillingConfigFunc billingConfigFunc : dataList) {
				map.put(billingConfigFunc.getApplyDate(), billingConfigFunc);
			}
		}
		List<CalendarDataVo> list = new ArrayList<CalendarDataVo>();
		for (int i = 0; i < dayArray.size()-1; i++) {
			CalendarDataVo vo = new CalendarDataVo();
			vo.setStart(dayArray.get(i));
			vo.setEnd(dayArray.get(i));
			BillingConfigFunc func = new BillingConfigFunc();
//			func = map.get(dayArray.get(i));
//			BeanUtils.copyProperties(map.get(dayArray.get(i)), func);
			if(map.get(dayArray.get(i))== null){
				BeanUtils.copyProperties(map.get("99999999"), func);
				
			}else{
				BeanUtils.copyProperties(map.get(dayArray.get(i)), func);
			}
			formatFunc(func);
			vo.setTitle(func.getDetails());
			vo.setId(dayArray.get(i));
//			BillingFunc billingFunc = JsonMapper.mapper().fromJson(func.getContent(), BillingFunc.class);
//			func.setMinLimitPrice(billingFunc.getMainChargeItem().getMin().getLimitQuantity()+"元");
//			func.setMaxLimitPrice(billingFunc.getMainChargeItem().getMax().getLimitQuantity()+"元");
//			func.setApplyDate(dayArray.get(i));
//			func.setBillingFunc(billingFunc);
			list.add(vo);
		}
		return list;
	}
	
	public List<BillingConfigFunc> getBillingAdjust(BillingConfigQueryCondition condition) {
		if (!StringUtils.nullOrBlank(condition.getOrgCode())) {
			String[] orgCode = condition.getOrgCode().split(",");
			List<String> orgCodes = new ArrayList<String>();
			for (String string : orgCode) {
				orgCodes.add(string);
			}
			condition.setOrgCodes(orgCodes);
			condition.setOrgCode(null);
		} else {
			try {
				condition.setOrgCodes(orgRpcService.getSubOrgCodes());
			} catch (Exception e) {
				Validate.isTrue(false, e.getMessage());
			}
		}
//		condition.setOrgCode("4600001");
		return billingConfigDao.getBillingAdjustList(condition);
	}
	
}
