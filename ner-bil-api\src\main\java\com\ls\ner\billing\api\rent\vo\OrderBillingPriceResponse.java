/**
 *
 * @(#) OrderBillingPriceResponse.java
 * @Package com.ls.ner.billing.api.rent.vo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.api.rent.vo;

import java.util.Map;

import com.ls.ner.billing.api.rent.model.BillingFunc;

/**
 *  类描述：查询订单计费详细响应信息
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年4月7日 下午3:42:25   lipf   Created.
 *           
 */
public class OrderBillingPriceResponse extends OrderBillingResponse{
	
	private Map<String,BillingFunc> dayPriceMap;

	public Map<String, BillingFunc> getDayPriceMap() {
		return dayPriceMap;
	}

	public void setDayPriceMap(Map<String, BillingFunc> dayPriceMap) {
		this.dayPriceMap = dayPriceMap;
	}

}
