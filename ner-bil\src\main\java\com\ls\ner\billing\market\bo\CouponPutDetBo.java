package com.ls.ner.billing.market.bo;

import com.pt.poseidon.api.framework.DicAttribute;

/**
*描述:发放详情
* @param:
*@return:
*创建人:biaoxiangd
*创建时间: 2017-06-07 18:15
*/
public class CouponPutDetBo {
	private String putId; // 发放ID
	private String putTime;//发放时间
	private String custId;// 客户标识
	private String mobile;// 手机号码
	private String putFlag;// 发放成功标志，1是 0否
	private String custName;
	private String custSortCode;
	@DicAttribute(dicName = "codeDict", key = "custSortCode", subType = "custSortCode")
	private String custSortCodeName;
	private String failReason; // 发放失败原因

	public String getPutFlag() {
		return putFlag;
	}

	public void setPutFlag(String putFlag) {
		this.putFlag = putFlag;
	}

	public String getFailReason() {
		return failReason;
	}

	public void setFailReason(String failReason) {
		this.failReason = failReason;
	}

	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getCustSortCode() {
		return custSortCode;
	}
	public void setCustSortCode(String custSortCode) {
		this.custSortCode = custSortCode;
	}
	public String getCustSortCodeName() {
		return custSortCodeName;
	}
	public void setCustSortCodeName(String custSortCodeName) {
		this.custSortCodeName = custSortCodeName;
	}
	
	public String getPutId() {
		return putId;
	}
	public void setPutId(String putId) {
		this.putId = putId;
	}
	public String getPutTime() {
		return putTime;
	}
	public void setPutTime(String putTime) {
		this.putTime = putTime;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

}
