package com.ls.ner.billing.market.service.impl;/**
 * Created by Administrator on 2017-06-05.
 */

import com.ls.ner.util.StringUtil;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 测试
 * 作者：biaoxiangd
 * 创建日期：2017 - 06 - 05 : 10:40
 * 修改人：
 * 修改日志：2017 - 06 - 05 : 10:40
 **/
public class TestMain {


    public static void json(){
        String custInfoList = "{'custId':'','mobile':''}";
        Map<String, String> map = null;
        try {
            map = (Map<String, String>) JsonUtils.json2Object(custInfoList,Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("ustList="+map.toString());
    }

    public static void main(String[] args) {
        json();
    }
}
