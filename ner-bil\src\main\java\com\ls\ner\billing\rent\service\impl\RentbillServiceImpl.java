package com.ls.ner.billing.rent.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.billing.api.BillingConstants;
import com.ls.ner.billing.api.BillingConstants.BuyType;
import com.ls.ner.billing.api.BillingConstants.ChargeMode;
import com.ls.ner.billing.api.BillingConstants.ChargeWay;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.api.common.bo.BillingConfigBo;
import com.ls.ner.billing.api.common.bo.BillingOrderRelaBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeBo;
import com.ls.ner.billing.api.prccharge.bo.EPrcChargeDetBo;
import com.ls.ner.billing.api.prccharge.service.IEPrcChargeService;
import com.ls.ner.billing.api.rent.model.*;
import com.ls.ner.billing.api.rent.model.RentRun.DateItem;
import com.ls.ner.billing.api.rent.service.IRentbillService;
import com.ls.ner.billing.api.rent.vo.OrderBillingPriceResponse;
import com.ls.ner.billing.api.rent.vo.OrderBillingResponse;
import com.ls.ner.billing.common.bo.BillingConfigBoExample;
import com.ls.ner.billing.common.bo.BillingOrderPricingBo;
import com.ls.ner.billing.common.bo.BillingOrderRelaBoExample;
import com.ls.ner.billing.common.dao.BillingConfigBoMapper;
import com.ls.ner.billing.common.dao.BillingFuncBoMapper;
import com.ls.ner.billing.common.dao.BillingOrderPricingBoMapper;
import com.ls.ner.billing.common.dao.BillingOrderRelaBoMapper;
import com.ls.ner.billing.config.dao.IBillingConfigDao;
import com.ls.ner.billing.config.service.impl.BillingConfigServiceImpl;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.DateTools;
import com.ls.ner.util.JodaDateTime;
import com.ls.ner.util.StringUtil;
import com.ls.ner.util.json.JsonMapper;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.param.api.ISysParamService;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

@Service(target = { ServiceType.APPLICATION }, value = "rentbillService")
public class RentbillServiceImpl implements IRentbillService {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(RentbillServiceImpl.class);
	
	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;
	
	@Autowired
	private BillingConfigBoMapper billingConfigBoMapper;
	
	@Autowired
	private BillingFuncBoMapper billingFuncBoMapper;
	
	@Autowired
	private BillingOrderRelaBoMapper billingOrderRelaBoMapper;
	
	@Autowired
	private BillingOrderPricingBoMapper billingOrderPricingBoMapper;
	
	@Autowired
	private BillingConfigServiceImpl billingConfigServiceImpl;
	
	@Autowired
	private IBillingConfigDao billingConfigDao;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC,value = "orgService")
	private IOrgService orgService;
	
	@Autowired
	private RentBillingCalculator billingCalculator;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC)
	private ISysParamService sysParamService;
	
	@ServiceAutowired(value="ePrcChargeService")
	private IEPrcChargeService ePrcChargeService;
	
	/**
	 * 保存订单和定价相关的部分
	 * @param orderBillingRela
	 */
	public OrderBillingRelaResult saveOrderBillingRela(OrderBillingRela orderBillingRela){
		LOGGER.debug("订单提交计算预收、保存计费开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		String appNo = orderBillingRela.getAppNo();
		String rentType = orderBillingRela.getRentType();

		IBillingLocateCondition billingLocateCondition = orderBillingRela;
		//上面是由四个参数换成了一个传参用的对象，所以把原来用到的取出来一次
		
		BillingConfigBo billingConfigBo = locateBillingConfigBo(rentType, billingLocateCondition);
		Validate.notNull(billingConfigBo,"找不到订单"+appNo+"对应的定价配置");
		
		OrderBillingRelaResult orderBillingRelaResult = new OrderBillingRelaResult();
		//时租 情况 主费用项不计算预收 只计算押金项和附加项
		//时租情况下押金预收  其余全部后付费 不判断是否预收 lipf注释20160414
		if(SubBe.SUB_BE_HOURS.contains(orderBillingRela.getRentType())|| SubBe.DILIVER.equals(orderBillingRela.getRentType())){
			BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingConfigBo.getBillingNo());
			//押金项预收计费
			BigDecimal depositResult = new BigDecimal("0.00");
			List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
			if(depositChargeItems!=null && depositChargeItems.size()>0){
				for (DepositChargeItem depositChargeItem : depositChargeItems) {
//					if(ChargeTimePoint.PRE.equals(depositChargeItem.getChargeTimePoint())){
						depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
						depositChargeItem.setResult(new BigDecimal(depositChargeItem.getPrice()));
//					}
				}
			}
			//附加项预收计费
			BigDecimal millResult = new BigDecimal("0.00");
			// 附加项目
			// 如果不存在用户订购的附加项，就不需要计算这部分内容了
			// 分时租赁预收的时候 只计算单位为一次的费用项
			//时租情况下押金预收  其余全部后付费 不判断是否预收 lipf注释20160414
//			List<AppendChargeItem> newAttachChargeItems = Lists.newArrayList();
//			if (StringUtils.isNotBlank(orderBillingRela.getSelectedAttachItemNos())) {
//				List<AppendChargeItem> attachChargeItems = billingFunc
//						.getAttachChargeItems();
//				if (attachChargeItems != null && attachChargeItems.size() > 0) {
//					DateItem item = new DateItem();
//					item.setFirstDay(true);
//					for (AppendChargeItem attachChargeItem : attachChargeItems) {
//						// 只有选中的项目才需要计算
//						if (orderBillingRela.getSelectedAttachItemNos().indexOf(attachChargeItem.getItemNo()) >= 0) {
//							if (billingCalculator.isChargeTimePointMatched(attachChargeItem,
//									ChargeTimePoint.POST)) {
//								if(attachChargeItem.getPriceUnit().equalToOnce()){
//									attachResult = attachResult.add(billingCalculator.calculate(attachChargeItem,
//											item));
//									newAttachChargeItems.add(attachChargeItem);
//								}
//							}
//						}
//					}
//				}
//			}
			if(SubBe.DILIVER.equals(orderBillingRela.getRentType())&&!ChargeWay.BY_TIME.equals(billingFunc.getMainChargeItem().getChargeWay())){
//				SubChargeItem millChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
//				Item item = new Item();
//				Limit min = millChargeItem.getMin();
//				Limit max = millChargeItem.getMax();
//				long limitedQuantity = billingCalculator.limitByUnit(Optional.fromNullable(min), Optional.fromNullable(max), orderBillingRela.getMeters());
//				item.setMeters(limitedQuantity);
//				millResult = millResult.add(billingCalculator.calculateMillChargeItem(millChargeItem , item ));
//				millResult = billingCalculator.limitByAmount(Optional.fromNullable(min), Optional.fromNullable(max), millResult);
				DateItem dateItem = new DateItem();
				dateItem.setMeters(orderBillingRela.getMeters());
				millResult = billingCalculator.calculate(billingFunc.getMainChargeItem(), dateItem );
			}
			billingFunc.setMainChargeItem(null);
//			billingFunc.setAttachChargeItems(newAttachChargeItems);
			billingFunc.setAttachChargeItems(null);
			orderBillingRelaResult.setAttachResult(new BigDecimal(0.00));
			orderBillingRelaResult.setDepositResult(depositResult);
			orderBillingRelaResult.setMainResult(millResult);
			orderBillingRelaResult.setMillResult(millResult);
			orderBillingRelaResult.setResult(millResult);//不含押金
			String relaNo = null;
			if (orderBillingRela.isSaveLogFlag()){
				relaNo = seqRpcService.getDefNo();
			} else {
				return orderBillingRelaResult;
			}
			BillingOrderRelaBo billingOrderRelaBo = new BillingOrderRelaBo();
			billingOrderRelaBo.setAppNo(appNo);
			billingOrderRelaBo.setRelaNo(relaNo);
			billingOrderRelaBo.setBillType(BillingConstants.BillType.PREPAY);
			billingOrderRelaBo.setSelectedAttachItemNos(orderBillingRela.getSelectedAttachItemNos());
			billingOrderRelaBo.setSubBe(billingConfigBo.getSubBe());
			billingOrderRelaBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
			billingOrderRelaBo.setDefaultBillingNo(billingConfigBo.getBillingNo());
			billingOrderRelaBo.setVersionLimit(billingConfigServiceImpl.getNowVersion());
			billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
			billingOrderRelaBo.setDateOperType("I");
			billingOrderRelaBo.setPricingAmt(depositResult);
			billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
			BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
			billingOrderPricingBo.setAppNo(orderBillingRela.getAppNo());
			String pricingDetailNo = seqRpcService.getDefNo();
			billingOrderPricingBo.setPricingDetailNo(pricingDetailNo);
			billingOrderPricingBo.setRelaNo(relaNo);
			billingOrderPricingBo.setBillingNo(billingConfigBo.getBillingNo());
			billingOrderPricingBo.setSubBe(orderBillingRela.getRentType());
			billingOrderPricingBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
			billingOrderPricingBo.setVersion(billingConfigBo.getVersion());
			billingOrderPricingBo.setApplyDate(billingConfigServiceImpl.getTodayString());
			billingOrderPricingBo.setPricingAmt(orderBillingRelaResult.getResult());
			billingOrderPricingBo.setDetails(JsonMapper.nonEmptyMapper().toJson(billingFunc));
			billingOrderPricingBo.setDataOperTime(new DateTime().toDate());
			billingOrderPricingBo.setDateOperType("I");
			billingOrderPricingBoMapper.insertSelective(billingOrderPricingBo);
			List<EPrcChargeBo> list = new ArrayList<EPrcChargeBo>();
			EPrcChargeBo ePrcChargeBo = new EPrcChargeBo();
			ePrcChargeBo.setAppNo(orderBillingRela.getAppNo());
			ePrcChargeBo.setCalcId(relaNo);
			ePrcChargeBo.setBillingType(BillingConstants.BillType.PREPAY);
			ePrcChargeBo.setPricingAmt(depositResult+"");
			list.add(ePrcChargeBo);
			Map<String,Object> prcCharge = new HashMap<String, Object>();
			prcCharge.put("ePrcChargeList", list);
//			ePrcChargeService.insertEPrcCharge(list);
			List<EPrcChargeDetBo> detList = new ArrayList<EPrcChargeDetBo>();
			if (depositChargeItems != null && depositChargeItems.size() > 0) {
				for (DepositChargeItem depositChargeItem : depositChargeItems) {
					EPrcChargeDetBo detBo = new EPrcChargeDetBo();
					detBo.setItemCode(depositChargeItem.getItemNo());
					detBo.setItemName(depositChargeItem.getItemName());
					detBo.setItemUnits(depositChargeItem.getPriceUnit().getUnitName());
					detBo.setPricingAmt(String.format("%.2f",Double.valueOf(depositChargeItem.getPrice())));
					detBo.setAmount(depositChargeItem.getPriceUnit().getValue()+"");
					detBo.setPrice(String.format("%.2f",Double.valueOf(depositChargeItem.getPrice())));
					detBo.setItemDesc(depositChargeItem.getRemarks());
					detBo.setCalcId(relaNo);
					detList.add(detBo);
				}
			}
			prcCharge.put("ePrcChargeDetList", detList);
			orderBillingRelaResult.setPrcCharge(prcCharge);
//			if(detList!=null&&detList.size()>0){
//				ePrcChargeService.insertEPrcChargeDet(detList);
//			}
		}else{
			LOGGER.debug("日租计费");
			orderBillingRelaResult = this.rentPrepay(orderBillingRela, null,billingConfigBo);
		}
		
		LOGGER.debug("订单提交计算预收、保存计费结束，出参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRelaResult));
		return orderBillingRelaResult;
//		BigDecimal depositResult = new BigDecimal("0.00");
//		List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
//		for (DepositChargeItem depositChargeItem : depositChargeItems) {
//			if(ChargeTimePoint.PRE.equals(depositChargeItem.getChargeTimePoint())){
//				depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
//			}
//		}
//		BigDecimal attachResult = new BigDecimal("0.00");
//		List<AppendChargeItem> appendChargeItems = billingFunc.getAttachChargeItems();
//		for (AppendChargeItem appendChargeItem : appendChargeItems) {
//			if(ChargeTimePoint.PRE.equals(appendChargeItem.getChargeTimePoint())){
//				attachResult = attachResult.add(new BigDecimal(appendChargeItem.getPrice()));
//			}
//		}
//		OrderBillingRelaResult orderBillingRelaResult = new OrderBillingRelaResult();
//		orderBillingRelaResult.setDepositResult(billingCalculator.bigDecimal2String(depositResult));
//		orderBillingRelaResult.setAttachResult(billingCalculator.bigDecimal2String(attachResult));
////		orderBillingRelaResult.setResult(billingCalculator.bigDecimal2String(result));
//		orderBillingRelaResult.setMainResult(billingCalculator.bigDecimal2String(billingFunc.getMainChargeItem().getResult()));
//		billingOrderRelaBo.setAppNo(appNo);
//		billingOrderRelaBo.setSelectedAttachItemNos(selectedAttachItemNos);
//		
//		billingOrderRelaBo.setSubBe(billingConfigBo.getSubBe());
//		
//		billingOrderRelaBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
//		billingOrderRelaBo.setDefaultBillingNo(billingConfigBo.getBillingNo());
//		billingOrderRelaBo.setVersionLimit(billingConfigServiceImpl.getNowVersion());
//		billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
//		billingOrderRelaBo.setDateOperType("I");
//		billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
//		
//		return orderBillingRelaResult;
	}
	
	/**
	 * 根据租赁行驶记录计费
	 * @param rentRun 租赁行驶记录
	 * @param chargeTimePointFilter 付费时间点 00 算完整费用 01 只算预付费部分 02 只算后付费部分
	 * @return
	 */
	public OrderBillingRelaResult calculate(RentRun rentRun,String chargeTimePointFilter){
		LOGGER.debug("订单结算、保存计费开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(rentRun));
		String appNo = rentRun.getAppNo();
		//关联到定价的方式比较复杂。先从billing_order_rela里找到appNo对应的，然后从billing_config里找到和日匹配的billingNo 然后找到billingFunc
		BillingOrderRelaBoExample billingOrderPricingBoExample = new BillingOrderRelaBoExample();
		billingOrderPricingBoExample.createCriteria().andAppNoEqualTo(appNo);
		List<BillingOrderRelaBo> billingOrderPricingBos = billingOrderRelaBoMapper.selectByExample(billingOrderPricingBoExample);
		//先搜索出下单时记录的相关信息，找到租赁类型 租赁点/公司 车型 版本号等数据
		BillingOrderRelaBo billingOrderRelaBo = billingOrderPricingBos.get(0);
		//再搜索出定价里符合条件的（按照APPLY_DATE分组好的，在下单时间戳之前生效的，指定 租赁类型 租赁点/公司 车型 的 定价
		List<BillingConfigBo> orderBillingConfigs = billingConfigDao.selectByOrderRela(billingOrderRelaBo);
		//无论是不是调整后的定价，都在map里 因为这里必须做这个判断来获取BillingFunc
		Map<String,BillingFunc> billingFuncMap = Maps.newHashMap();
		for (BillingConfigBo orderBillingConfig  : orderBillingConfigs) {
			String billingNo = orderBillingConfig.getBillingNo();
			
			BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
			LOGGER.debug("适用日期:{} 定价:{}",orderBillingConfig.getApplyDate(),ToStringBuilder.reflectionToString(billingFunc));
			billingFuncMap.put(orderBillingConfig.getApplyDate(), billingFunc);
		}	
		
		List<DateItem> dateItems = rentRun.getDateItems();
		
		BigDecimal result = new BigDecimal("0.00");
		int i=0;
		String relaNo = null;
		if (rentRun.isSaveLogFlag()){
			relaNo = seqRpcService.getDefNo();
		}
		long minutes = 0;
		long meters = 0;
		BigDecimal timeResult = new BigDecimal("0.00");//主费用项 按时间计费费用 
		BigDecimal millResult = new BigDecimal("0.00");//主费用项 按里程计费 费用 
		List<AppendChargeItem> newAttachChargeItems = Lists.newArrayList();
		Map<String,BigDecimal> tempResult = new HashMap<String, BigDecimal>();
		Map<String,Object> dailyBillDetMap = Maps.newHashMap();
		List<Map<String,Object>> dailyBillDetMapList = Lists.newArrayList();
		BillingFunc billingFunc = null;
        BillingFunc defaultBillingFunc = billingFuncMap.get(BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE);
		for (DateItem dateItem : dateItems) {
			BigDecimal dayResult = new BigDecimal("0.00");
			meters += dateItem.getMeters();
			minutes += dateItem.getMinutes();
			String recordDate = dateItem.getRecordDate();
			String selectedAttachItemNos = billingOrderRelaBo.getSelectedAttachItemNos();
			//在订单定价信息扩展里，有选择的附加项
			dateItem.setSelectedAttachChargeItemNos(selectedAttachItemNos);
			if(i==0){
				//标识为第一天的数据
				dateItem.setFirstDay(true);
			}
			else{
				dateItem.setFirstDay(false);
			}
			
			

//			BillingFunc billingFunc = findOrderBillingFunc(appNo,recordDate);

			billingFunc = billingFuncMap.get(recordDate);
			//找不到特定当天特别调整的数据，就用默认通用的数据
			if(billingFunc == null){
				billingFunc = defaultBillingFunc.deepClone();
			}
            LOGGER.debug("billingFunc：{}",JsonMapper.nonEmptyMapper().toJson(billingFunc));
			billingFunc.setLicenseNo(rentRun.getLicenseNo());
			billingFunc.setMinutes(dateItem.getMinutes());
			billingFunc.setMeters(dateItem.getMeters());
			if(StringUtils.isEmpty(selectedAttachItemNos)){
				billingFunc.setAttachChargeItems(null);
			}
			billingFunc.setDepositChargeItems(null);//结算的时候不算押金项
			//主费用项结算
			BigDecimal tempMainResult = billingCalculator.calculate(billingFunc.getMainChargeItem(),
					dateItem);
			dayResult = dayResult.add(tempMainResult);
			timeResult = timeResult.add(billingFunc.getMainChargeItem().getTimeResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getTimeResult());
			millResult = millResult.add(billingFunc.getMainChargeItem().getMillResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getMillResult());
			// 附加项目
			// 如果不存在用户订购的附加项，就不需要计算这部分内容了
			List<AppendChargeItem> attachChargeItems = billingFunc
					.getAttachChargeItems();
			List<AppendChargeItem> tempAttachChargeItems = new ArrayList<AppendChargeItem>();
			if (StringUtils.isNotBlank(dateItem.getSelectedAttachChargeItemNos())) {
				if (attachChargeItems != null && attachChargeItems.size() > 0) {
					for (AppendChargeItem attachChargeItem : attachChargeItems) {
						// 只有选中的项目才需要计算
						if (dateItem.getSelectedAttachChargeItemNos().indexOf(attachChargeItem.getItemNo()) >= 0) {
							BigDecimal attachMainResult = billingCalculator.calculate(attachChargeItem,
										dateItem);
							dayResult = dayResult.add(attachMainResult);
							if(i==0){
								newAttachChargeItems.add(attachChargeItem);
							}
							String tempItemNo = attachChargeItem.getItemNo();
							Map<String,Object> tempAttachMap = Maps.newHashMap();
							tempAttachMap.put("dailyDate", recordDate);
							tempAttachMap.put("dailyBeginTime", dateItem.getStartTime());
							tempAttachMap.put("dailyEndTime", dateItem.getEndTime());
							if((PriceUnit.DAY+PriceUnit.HOUR+PriceUnit.MINUTE).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
								tempAttachMap.put("dailyNum", minutes);
							}else if((PriceUnit.KM+PriceUnit.M).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
								tempAttachMap.put("dailyNum", meters);
							} else {
								tempAttachMap.put("dailyNum","1");
							}
							tempAttachMap.put("dailyAmt", attachChargeItem.getResult());
							tempAttachMap.put("dailyComments", "");
							List<Map<String,Object>> billSectionDetList = Lists.newArrayList();
							tempAttachMap.put("billSectionDet", billSectionDetList);
							Map<String,Object> billSectionDet = Maps.newHashMap();
							billSectionDet.put("billSectNum", tempAttachMap.get("dailyNum"));
							billSectionDet.put("billSectStartValue", dateItem.getStartTime());
							billSectionDet.put("billSectEndValue", dateItem.getEndTime());
							billSectionDet.put("billSectAmt", attachChargeItem.getResult());
							billSectionDet.put("tariffUnitNum", attachChargeItem.getPriceUnit().getValue());
							billSectionDet.put("tariffUnit", attachChargeItem.getPriceUnit().getUnitName());
							billSectionDet.put("scaledRate", attachChargeItem.getPrice());
							billSectionDetList.add(billSectionDet);
							if(tempResult.get(tempItemNo) != null){
								tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(attachChargeItem.getResult()));
								List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(tempItemNo);
								newDailyBillDetMapList.add(tempAttachMap);
								dailyBillDetMap.put(tempItemNo, newDailyBillDetMapList);
							}else{
								tempResult.put(tempItemNo,attachChargeItem.getResult());
								dailyBillDetMapList = Lists.newArrayList();
								dailyBillDetMapList.add(tempAttachMap);
								dailyBillDetMap.put(tempItemNo, dailyBillDetMapList);
							}
							tempAttachChargeItems.add(attachChargeItem);
						}
					}
				}
			}
			billingFunc.setAttachChargeItems(tempAttachChargeItems);
			result = result.add(dayResult);;
//			记录每日数据其实不是原始需求。先不写了。能够梳理一下就好了。chargeTimePoint可能要写进表里，来区分多批计算。由此产生计算批次的概念？pricing_batch_no
//			BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
			//费用详细信息保存e_billing_order_rela_pricing（isSaveLogFlag字段在下单的时候要传入）
			if(rentRun.isSaveLogFlag()){
				BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
				billingOrderPricingBo.setAppNo(appNo);
				String pricingDetailNo = seqRpcService.getDefNo();
				billingOrderPricingBo.setPricingDetailNo(pricingDetailNo);
				billingOrderPricingBo.setRelaNo(relaNo);
				billingOrderPricingBo.setBillingNo(billingFunc.getBillingNo());
				billingOrderPricingBo.setSubBe(billingOrderRelaBo.getSubBe());
				billingOrderPricingBo.setOrgAutoModelKey(billingOrderRelaBo.getOrgAutoModelKey());
				billingOrderPricingBo.setVersion(billingOrderRelaBo.getVersionLimit());
				billingOrderPricingBo.setApplyDate(recordDate);
				billingOrderPricingBo.setPricingAmt(dayResult);
				billingOrderPricingBo.setDetails(JsonMapper.nonEmptyMapper().toJson(billingFunc));
				billingOrderPricingBo.setDataOperTime(new DateTime().toDate());
				billingOrderPricingBo.setDateOperType("I");
				billingOrderPricingBoMapper.insertSelective(billingOrderPricingBo);
			}
			Map<String,Object> tempTimeMap = Maps.newHashMap();
			tempTimeMap.put("dailyDate", recordDate);
			tempTimeMap.put("dailyBeginTime", dateItem.getStartTime());
			tempTimeMap.put("dailyEndTime", dateItem.getEndTime());
			tempTimeMap.put("dailyNum", dateItem.getMinutes());
			tempTimeMap.put("dailyAmt", billingFunc.getMainChargeItem().getTimeResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getTimeResult());
			tempTimeMap.put("dailyComments", "");
			List<Map<String,Object>> timeBillSectionDetList = Lists.newArrayList();
			tempTimeMap.put("billSectionDet", timeBillSectionDetList);
			LOGGER.debug("++++{}",JsonMapper.nonEmptyMapper().toJson(tempTimeMap));

			SubChargeItem timeChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
			if(timeChargeItem!=null){
				Map<String,Object> billSectionDet = Maps.newHashMap();
				if(timeChargeItem.getRangeChargeItems()!=null){
//					for (RangeChargeItem rangeChargeItem : timeChargeItem.getRangeChargeItems()) {
						billSectionDet = Maps.newHashMap();
						billSectionDet.put("billSectNum", dateItem.getMinutes());
						billSectionDet.put("billSectStartValue", dateItem.getStartTime());
						billSectionDet.put("billSectEndValue", dateItem.getEndTime());
						billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getTimeResult());
						billSectionDet.put("tariffUnitNum", timeChargeItem.getPriceUnit().getValue());
						billSectionDet.put("tariffUnit", timeChargeItem.getPriceUnit().getUnitName());
						billSectionDet.put("scaledRate", timeChargeItem.getRangeChargeItems().get(0).getPrice());
						timeBillSectionDetList.add(billSectionDet);
//					}
				} else {
					billSectionDet = Maps.newHashMap();
					billSectionDet.put("billSectNum", dateItem.getMinutes());
					billSectionDet.put("billSectStartValue", dateItem.getStartTime());
					billSectionDet.put("billSectEndValue", dateItem.getEndTime());
					billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getTimeResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getTimeResult());
					billSectionDet.put("tariffUnitNum", timeChargeItem.getPriceUnit().getValue());
					billSectionDet.put("tariffUnit", timeChargeItem.getPriceUnit().getUnitName());
					billSectionDet.put("scaledRate", timeChargeItem.getPrice());
					timeBillSectionDetList.add(billSectionDet);
				}
			}
			if(dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO)!=null){
				List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
				newDailyBillDetMapList.add(tempTimeMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO, newDailyBillDetMapList);
			} else {
				dailyBillDetMapList = Lists.newArrayList();
				dailyBillDetMapList.add(tempTimeMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO, dailyBillDetMapList);
			}
			Map<String,Object> tempMillMap = Maps.newHashMap();
			tempMillMap.put("dailyDate", recordDate);
			tempMillMap.put("dailyBeginTime", dateItem.getStartTime());
			tempMillMap.put("dailyEndTime", dateItem.getEndTime());
			tempMillMap.put("dailyNum", dateItem.getMeters());
			tempMillMap.put("dailyAmt", billingFunc.getMainChargeItem().getMillResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getMillResult());
			tempMillMap.put("dailyComments", "");
			List<Map<String,Object>> billSectionDetList = Lists.newArrayList();
			tempMillMap.put("billSectionDet", billSectionDetList);
			SubChargeItem millChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
			if(millChargeItem!=null){
				Map<String,Object> billSectionDet = Maps.newHashMap();
				if(millChargeItem.getRangeChargeItems()!=null){
//					for (RangeChargeItem rangeChargeItem : millChargeItem.getRangeChargeItems()) {
						billSectionDet = Maps.newHashMap();
						billSectionDet.put("billSectNum", dateItem.getMeters());
						billSectionDet.put("billSectStartValue", dateItem.getStartTime());
						billSectionDet.put("billSectEndValue", dateItem.getEndTime());
						billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getMillResult());
						billSectionDet.put("tariffUnitNum", millChargeItem.getPriceUnit().getValue());
						billSectionDet.put("tariffUnit", millChargeItem.getPriceUnit().getUnitName());
						billSectionDet.put("scaledRate", millChargeItem.getRangeChargeItems().get(0).getPrice());
						billSectionDetList.add(billSectionDet);
//					}
				} else {
					billSectionDet = Maps.newHashMap();
					billSectionDet.put("billSectNum", dateItem.getMeters());
					billSectionDet.put("billSectStartValue", dateItem.getStartTime());
					billSectionDet.put("billSectEndValue", dateItem.getEndTime());
					billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getMillResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getMillResult());
					billSectionDet.put("tariffUnitNum", millChargeItem.getPriceUnit().getValue());
					billSectionDet.put("tariffUnit", millChargeItem.getPriceUnit().getUnitName());
					billSectionDet.put("scaledRate", millChargeItem.getPrice());
					billSectionDetList.add(billSectionDet);
				}
			}
			if(dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO)!=null){
				List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
				newDailyBillDetMapList.add(tempMillMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO, newDailyBillDetMapList);
			} else {
				dailyBillDetMapList = Lists.newArrayList();
				dailyBillDetMapList.add(tempMillMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO, dailyBillDetMapList);
			}
			LOGGER.debug("tempTimeMap+++{}",tempMillMap);
			i++;		
		}
		// 需要细化。。。可以把价格计算也用APPLY_DATE是999999的数据来代表总计的price
		
		//预付费 后付费 怎么写表？ 重点在于是否余额不足？ 如果需要判断，可以把余额先传进来，如果发现余额不足，就记录
		//记录日志 ？总价？
		OrderBillingRelaResult orderBillingRelaResult = new OrderBillingRelaResult();
		if(rentRun.isSaveLogFlag()){
			billingOrderRelaBo.setRelaNo(relaNo);
			billingOrderRelaBo.setBillType(BillingConstants.BillType.SETTLE);
			billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
			billingOrderRelaBo.setDateOperType("I");
			billingOrderRelaBo.setPricingAmt(result);
			billingOrderRelaBo.setSystemId(null);
			billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
		}
			String chargeWay = orderBillingConfigs.get(0).getChargeWay();
			List<EPrcChargeBo> ePrcChargeBoList = new ArrayList<EPrcChargeBo>();
			EPrcChargeBo ePrcChargeBo = new EPrcChargeBo();
			ePrcChargeBo.setAppNo(rentRun.getAppNo());
			ePrcChargeBo.setCalcId(relaNo);
			ePrcChargeBo.setBillingType(BillingConstants.BillType.SETTLE);
			ePrcChargeBo.setPricingAmt(result+"");
			ePrcChargeBo.setLease(minutes+"");
			ePrcChargeBoList.add(ePrcChargeBo);
			Map<String,Object> prcCharge = new HashMap<String, Object>();
			prcCharge.put("ePrcChargeList", ePrcChargeBoList);
//			ePrcChargeService.insertEPrcCharge(ePrcChargeBoList);
			List<EPrcChargeDetBo> ePrcChargeDetBoList = new ArrayList<EPrcChargeDetBo>();
			if(ChargeWay.BY_MILL.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				EPrcChargeDetBo detBo = new EPrcChargeDetBo();
				detBo.setItemCode(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
				detBo.setItemName("里程费");
				detBo.setPricingAmt(millResult+"");
				detBo.setAmount(meters+"");
				detBo.setCalcId(relaNo);
				detBo.setItemDesc(billingFunc==null?"":billingFunc.getMainChargeItem().getMillChargeItem().getPriceRemark());
				detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO));
				ePrcChargeDetBoList.add(detBo);
			} 
			if(ChargeWay.BY_TIME.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				EPrcChargeDetBo detBo = new EPrcChargeDetBo();
				detBo.setItemCode(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
				detBo.setItemName("租金");
				detBo.setPricingAmt(timeResult+"");
				detBo.setAmount(minutes+"");
				detBo.setCalcId(relaNo);
				detBo.setItemDesc(billingFunc==null?"":billingFunc.getMainChargeItem().getTimeChargeItem().getPriceRemark());
				detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO));
				LOGGER.debug("++++{}",JsonMapper.nonEmptyMapper().toJson(detBo));
				ePrcChargeDetBoList.add(detBo);
			}
			if(newAttachChargeItems != null &&newAttachChargeItems.size()>0){
				for (AppendChargeItem attachChargeItem : newAttachChargeItems) {
					EPrcChargeDetBo detBo = new EPrcChargeDetBo();
					detBo.setItemCode(attachChargeItem.getItemNo());
					detBo.setItemName(attachChargeItem.getItemName());
					detBo.setPrice(attachChargeItem.getPrice());
					detBo.setPricingAmt(String.format("%.2f", tempResult.get(attachChargeItem.getItemNo())));
					detBo.setItemUnits(attachChargeItem.getPriceUnit().getDesc());
					if((PriceUnit.DAY+PriceUnit.HOUR+PriceUnit.MINUTE).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
						detBo.setAmount(minutes+"");
					}else if((PriceUnit.KM+PriceUnit.M).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
						detBo.setAmount(meters+"");
					} else {
						detBo.setAmount("1");
					}
					detBo.setCalcId(relaNo);
					detBo.setItemDesc(attachChargeItem.getPriceRemarks());
					detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(attachChargeItem.getItemNo()));
					ePrcChargeDetBoList.add(detBo);
				}
			}
			prcCharge.put("ePrcChargeDetList", ePrcChargeDetBoList);
			orderBillingRelaResult.setPrcCharge(prcCharge);
//			ePrcChargeService.insertEPrcChargeDet(ePrcChargeDetBoList);
		
		LOGGER.debug("订单结算、保存计费结束，出参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRelaResult));
		orderBillingRelaResult.setResult(result);
		orderBillingRelaResult.setMeters(meters);
		orderBillingRelaResult.setMinutes(minutes);
		orderBillingRelaResult.setTimeResult(timeResult);
		orderBillingRelaResult.setMillResult(millResult);
		return orderBillingRelaResult;
	}




	@Deprecated
	@Override
	public Map<String, BillingConfigBo> getBillingConfigBoMap(
			String rentType, List<String> orgAutoModelKeys) {
		BillingConfigBoExample example = new BillingConfigBoExample();
		//待定 不知道如何获取公司？
//		orgService.getSuperOrgByOrgNoAndTypes(orgCode, Lists.newArrayList(""));
		String defaultDate = BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE;
		String today = getTodayString();
		example.createCriteria().andSubBeEqualTo(rentType)
		//要改造成支持租赁点和公司同时查的
		.andOrgAutoModelKeyIn(orgAutoModelKeys)
		//适用日期为默认或当天
		.andApplyDateIn(Lists.newArrayList(defaultDate,today))
		//只取最新版本
		.andIsLatestVersionEqualTo("1");
		Map<String, BillingConfigBo> map = Maps.newHashMap();
		List<BillingConfigBo> list = billingConfigBoMapper.selectByExample(example);
		if(list !=null && list.size()>0){
			LOGGER.debug("{}",list);
			for (BillingConfigBo curBo : list) {
				String autoModelNo = curBo.getAutoModelNo();
				//这里有个筛选的过程，因为调整过的价格需要记录调整对应的日期，所以是具体的日期，如20160310 如果是普通的，就是00000000
				//是否为调整过的价格。是的优先。在维护数据的时候保证同一版本调整过的价格只有一个
				//如果没有当前日期，这个也无法做到
//				String isAdjusted = billingConfigBo.getIsAdjusted();//这个其实可以靠适用日期来，所以不需要
				BillingConfigBo boInMap = map.get(autoModelNo);
				if(boInMap == null){
					map.put(autoModelNo, curBo);
				}
				else{
					//需要一个if 租赁点（门店）自己的配置比公司的默认配置优先。目前不知道怎么判断这个
//					String curOperOrgCode = boInMap.getOperOrgCode();
//					String boOperOrgCode = curBo.getOperOrgCode();
					//如果原来在map里的是default的，而新的是today的，则以today的优先
					if(defaultDate.equals(boInMap.getApplyDate())&&today.equals(curBo.getApplyDate())){
						map.put(curBo.getOrgAutoModelKey(), curBo);
					}
				}
				
			}
		}
		return map;
	}
	
	@Override
	public BillingConfigBo locateBillingConfigBo(String rentType,IBillingLocateCondition billingLocateCondition){
		return locateBillingConfigBoMap(rentType,Lists.newArrayList(billingLocateCondition)).get(getBillingLocateKey(billingLocateCondition));
	}

	protected String getBillingLocateKey(
			IBillingLocateCondition billingLocateCondition) {
		return billingLocateCondition.getRtNo()+"#"+billingLocateCondition.getAutoModelNo();
	}
	/**
	 * 不指定时租/日租等，查询各租赁点车型下所有可选的定价列表，列表中按照时租、日租...的顺序排列
	 * @param billingLocateConditions
	 * @return
	 */
	public Map<String, List<BillingConfigBo>> locateBillingConfigBoListMap(List<? extends IBillingLocateCondition> billingLocateConditions){
		LOGGER.debug("不指定时租/日租等，查询各租赁点车型下所有可选的定价列表,入参：{}",JsonMapper.nonEmptyMapper().toJson(billingLocateConditions));
		Map<String, List<BillingConfigBo>> result = Maps.newHashMap();
		String[] rentTypes = SubBe.VALID_SUB_BES;
		for (String rentType : rentTypes) {
			Map<String, BillingConfigBo> map = locateBillingConfigBoMap(rentType,billingLocateConditions);
			for(Entry<String, BillingConfigBo> entry:map.entrySet()){
				List<BillingConfigBo> list = result.get(entry.getKey());
				if(list == null){
					list = Lists.newArrayList();
					result.put(entry.getKey(), list);
				}
				list.add(entry.getValue());
			}
		}
		LOGGER.debug("不指定时租/日租等，查询各租赁点车型下所有可选的定价列表,出参：{}",JsonMapper.nonEmptyMapper().toJson(result));
		return result;
	}
			
	
	
	public Map<String, BillingConfigBo> locateBillingConfigBoMap(
			String rentType, List<? extends IBillingLocateCondition> billingLocateConditions) {
		LOGGER.debug("指定时租/日租等，查询各租赁点车型下所有可选的定价列表，入参：rentType={},condition={}",rentType,JsonMapper.nonEmptyMapper().toJson(billingLocateConditions));
		//临时用的，适配两套值   （lipf 0412 说明subBe已经统一用rentType 01 02）
//		if("01".equals(rentType)){
//			rentType = "0101";
//		}
//		else if("02".equals(rentType)||"0201".equals(rentType)){
//			rentType = "0102";
//		}
//		else if("03".equals(rentType)){
//			rentType = "0103";
//		}
//		else if("04".equals(rentType)){
//			rentType = "0104";
//		}
		
		BillingConfigBoExample example = new BillingConfigBoExample();
		List<String> orgAutoModelKeys = Lists.newArrayList();
		for (IBillingLocateCondition bc : billingLocateConditions) {
			//租赁点和公司都要查，租赁点优先
			orgAutoModelKeys.add(getRentPointKey(bc));
			orgAutoModelKeys.add(getCompanyKey(bc));
		}

		String defaultDate = BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE;
//		String today = new DateTime().toString("yyyyMMdd");
		String today = getTodayString();
		
		example.createCriteria().andSubBeEqualTo(rentType)
		//要改造成支持租赁点和公司同时查的
		.andOrgAutoModelKeyIn(orgAutoModelKeys)
		
		//适用日期为默认或当天
		.andApplyDateIn(Lists.newArrayList(defaultDate,today))
//		.andApplyDateEqualTo(defaultDate)
		//只取最新版本
//		.andIsLatestVersionEqualTo("1")
		//选取当前已经生效的
		.andEftDateLessThanOrEqualTo(new DateTime().toDate());
		example.setOrderByClause("EFT_DATE desc");
		Map<String, BillingConfigBo> map = Maps.newHashMap();
		Map<String, BillingConfigBo> resultMap = Maps.newHashMap();
		List<BillingConfigBo> list = billingConfigBoMapper.selectByExample(example);
		if(list !=null && list.size()>0){
			LOGGER.debug("{}",list);
			for (BillingConfigBo curBo : list) {
				String orgAutoModelKey = curBo.getOrgAutoModelKey();
				//这里有个筛选的过程，因为调整过的价格需要记录调整对应的日期，所以是具体的日期，如20160310 如果是普通的，就是00000000
				//是否为调整过的价格。是的优先。在维护数据的时候保证同一版本调整过的价格只有一个
				//如果没有当前日期，这个也无法做到
//				String isAdjusted = billingConfigBo.getIsAdjusted();//这个其实可以靠适用日期来，所以不需要
				BillingConfigBo boInMap = map.get(orgAutoModelKey);
				if(boInMap == null){
					map.put(orgAutoModelKey, curBo);
				}
				else{
					
					//如果原来在map里的是default的，而新的是today的，则以today的优先
					if(defaultDate.equals(boInMap.getApplyDate())&&today.equals(curBo.getApplyDate())){
						map.put(orgAutoModelKey, curBo);
					}
				}
				
			}
			
			for (IBillingLocateCondition bc : billingLocateConditions) {
				//租赁点自己的优先
				BillingConfigBo bo = map.get(getRentPointKey(bc));
				//然后是公司的
				if(bo==null){
					bo = map.get(getCompanyKey(bc));
				}
				resultMap.put(getBillingLocateKey(bc), bo);
				
			}
		}
		LOGGER.debug("指定时租/日租等，查询各租赁点车型下所有可选的定价列表，出参：{}",JsonMapper.nonEmptyMapper().toJson(resultMap));
		return resultMap;
	}

	protected String getTodayString() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String today = sdf.format(new Date());
		return today;
	}
	
	public String getNowVersion() {
		return new DateTime().toString("yyyyMMddHHmmss");
	}

	protected String getCompanyKey(IBillingLocateCondition bc) {
		//前7位就是总公司编码
//		return "C"+getRootOrgCode(bc)+"#"+bc.getAutoModelNo();
		return "C"+bc.getOrgCode()+"#"+bc.getAutoModelNo();
	}

	protected String getRootOrgCode(IBillingLocateCondition bc) {
		if(bc.getOrgCode()!=null&&bc.getOrgCode().length()>=7){
		return bc.getOrgCode().substring(0,7);
		}
		//正常的数据其实不该走到这里的。考虑到对异常数据的健壮，判断一下
		return bc.getOrgCode();
	}

	protected String getRentPointKey(IBillingLocateCondition bc) {
		return "R"+bc.getRtNo()+"#"+bc.getAutoModelNo();
	}

	@Override
	public OrderBillingResponse getOrderBillingInfo(OrderBillingRela orderBillingRela) {
		LOGGER.debug("预收计费开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		BillingConfigBo billingConfigBo = locateBillingConfigBo(orderBillingRela.getRentType(), orderBillingRela);
		OrderBillingResponse orderBillingResponse = new OrderBillingResponse();
		String billingNo = billingConfigBo.getBillingNo();
		BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
		String priceUnitDesc = billingConfigBo.getMinPriceUnitDesc();
		if(ChargeMode.PERIOD.equals(billingConfigBo.getChargeMode())){
			String chargeWay = billingConfigBo.getChargeWay();
			List<RangeChargeItem> rangeItems = new ArrayList<RangeChargeItem>();
			if (ChargeWay.BY_MILL.equals(chargeWay)) {
				rangeItems  = billingFunc.getMainChargeItem().getMillChargeItem().getRangeChargeItems();
				priceUnitDesc = getMinPriceUnitDesc(rangeItems);
			} else if (ChargeWay.BY_TIME.equals(chargeWay)) {
				rangeItems  = billingFunc.getMainChargeItem().getTimeChargeItem().getRangeChargeItems();
				priceUnitDesc = getMinPriceUnitDesc(rangeItems);
			} else {
				rangeItems  = billingFunc.getMainChargeItem().getTimeChargeItem().getRangeChargeItems();
				priceUnitDesc = getMinPriceUnitDesc(rangeItems);
				rangeItems  = billingFunc.getMainChargeItem().getMillChargeItem().getRangeChargeItems();
				priceUnitDesc = priceUnitDesc+","+getMinPriceUnitDesc(rangeItems);
			}
		}
		BigDecimal depositResult = new BigDecimal("0.00");
		List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
		if(depositChargeItems!=null&&depositChargeItems.size()>0){
			for (DepositChargeItem depositChargeItem : depositChargeItems) {
//				if(ChargeTimePoint.PRE.equals(depositChargeItem.getChargeTimePoint())){
					depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
					depositChargeItem.setResult(new BigDecimal(depositChargeItem.getPrice()));
//				}
			}
		}
		LOGGER.debug("===================================orderBillingResponse,{}",JsonMapper.DEFAULT.toJson(orderBillingResponse));
		orderBillingResponse.setResult(depositResult);
		orderBillingResponse.setDepositResult(depositResult);
		orderBillingResponse.setPriceUnitDesc(priceUnitDesc);
		//时租 情况 主费用项不计算预收
		OrderBillingRelaResult res = null;
		if(!SubBe.HOUR.equals(orderBillingRela.getRentType())){
			List<AppendChargeItem> attachChargeItems = billingFunc
					.getAttachChargeItems();
			String selectedAttachItemNos = "";
			if(attachChargeItems!=null&&attachChargeItems.size()>0){
				for (AppendChargeItem appendChargeItem : attachChargeItems) {
					selectedAttachItemNos+=appendChargeItem.getItemNo();
				}
			}
			orderBillingRela.setSelectedAttachItemNos(selectedAttachItemNos);
			res = this.rentPrepay(orderBillingRela, billingFunc,billingConfigBo);
			orderBillingResponse.setResult(res.getResult());
			orderBillingResponse.setMeters(res.getMeters()/1000);
			orderBillingResponse.setMinutes(res.getMinutes()/1440);
		}
		
//		orderBillingResponse.setRemark(billingConfigBo.getRemark());
		orderBillingResponse.setMainChargeItem(billingFunc.getMainChargeItem());
		orderBillingResponse.setDepositChargeItems(billingFunc.getDepositChargeItems());
		orderBillingResponse.setAttachChargeItems(billingFunc.getAttachChargeItems());
		LOGGER.debug("预收计费结束，出参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingResponse));
		return orderBillingResponse;
	}
	
	protected String getMinPriceUnitDesc(List<RangeChargeItem> rangeItems){
		String nowHHMMTime = JodaDateTime.getFormatDate("HH:mm");
		for (RangeChargeItem rangeChargeItem : rangeItems) {
			if(nowHHMMTime.compareTo(rangeChargeItem.getRange().getFrom())>=0&&nowHHMMTime.compareTo(rangeChargeItem.getRange().getTo())<0){
				return billingConfigServiceImpl.getPerPriceUnitDesc(rangeChargeItem);
			} else if(rangeChargeItem.getRange().getFrom().compareTo(rangeChargeItem.getRange().getTo())>0){
				if(nowHHMMTime.compareTo(rangeChargeItem.getRange().getFrom())>=0||nowHHMMTime.compareTo(rangeChargeItem.getRange().getTo())>=0){
					return billingConfigServiceImpl.getPerPriceUnitDesc(rangeChargeItem);
				}
			}
		}
		return "";
	}
	/**
	 * 
	 * 方法说明：预收费     //日租情况下租金、押金、服务费全部预收  不判断是否预收 lipf注释20160414
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月6日 上午9:49:44
	 * History:  2016年4月6日 上午9:49:44   lipf   Created.
	 *
	 * @param rentRun
	 * @param billingLocateCondition
	 * @param billingConfigBo 
	 * @return
	 *
	 */
	public OrderBillingRelaResult rentPrepay(OrderBillingRela orderBillingRela,BillingFunc billingFunc2, BillingConfigBo billingConfigBo){
		LOGGER.debug("日租预收费，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		if(StringUtils.isBlank(orderBillingRela.getPlanHcTime())){
			if(SubBe.SUB_BE_MONTHS.contains(orderBillingRela.getRentType())||SubBe.SUB_BE_YEARS.contains(orderBillingRela.getRentType())){
				orderBillingRela.setPlanHcTime(DateTools.offsetDate(orderBillingRela.getPlanQcTime(), "1", "4", "yyyy-MM-dd HH:mm"));
			} else if(SubBe.SUB_BE_DAYS.contains(orderBillingRela.getRentType())){
				orderBillingRela.setPlanHcTime(DateTools.offsetDate(orderBillingRela.getPlanQcTime(), "1", "1", "yyyy-MM-dd HH:mm"));
			}
		}
		LOGGER.debug("日租预收费，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		//先搜索出下单时记录的相关信息，找到租赁类型 租赁点/公司 车型 版本号等数据
		BillingOrderRelaBo billingOrderRelaBo = new BillingOrderRelaBo();
		billingOrderRelaBo.setVersionLimit(orderBillingRela.getPlanHcTime().replace("-", "").replace(":", "").replace(" ", "")+"00");
		billingOrderRelaBo.setSubBe(orderBillingRela.getRentType());
		billingOrderRelaBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
		//再搜索出定价里符合条件的（按照APPLY_DATE分组好的，在下单时间戳之前生效的，指定 租赁类型 租赁点/公司 车型 的 定价
//		List<BillingConfigBo> orderBillingConfigs = billingConfigDao.selectByOrderRela(billingOrderRelaBo);
		Map<String,Object> inMap = new HashMap<String, Object>();
		String orgAutoModelKey = getCompanyKey(orderBillingRela);
		inMap.put("rentType", orderBillingRela.getRentType());
		inMap.put("orgAutoModelKey", orgAutoModelKey);
		inMap.put("versionLimit", getNowVersion());
		
		String nowTime = JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
		String maxRentDay =sysParamService.getSysParamsValues("maxRentDay");//日租最大可租天数
		String maxRentTime = DateTools.getPreOrFutureDay(Integer.valueOf(maxRentDay),nowTime);
		inMap.put("maxRentTime", maxRentTime);
		//再搜索出定价里符合条件的（按照APPLY_DATE分组好的，在下单时间戳之前生效的，指定 租赁类型 租赁点/公司 车型 的 定价
		List<BillingConfigBo> orderBillingConfigs = billingConfigDao.selectPrepayByRela(inMap);
		//无论是不是调整后的定价，都在map里 因为这里必须做这个判断来获取BillingFunc
		Map<String,BillingFunc> billingFuncMap = Maps.newHashMap();
		for (BillingConfigBo orderBillingConfig  : orderBillingConfigs) {
			String billingNo = orderBillingConfig.getBillingNo();
			BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
			billingFunc.setBillingNo(billingNo);
			billingFunc.setVersion(orderBillingConfig.getVersion());
			LOGGER.debug("适用日期:{} 定价:{}",orderBillingConfig.getApplyDate(),ToStringBuilder.reflectionToString(billingFunc));
			billingFuncMap.put(orderBillingConfig.getApplyDate(), billingFunc);
		}	
		
		List<DateItem> dateItems = this.transBillingConditionByDay(orderBillingRela);
		
		BigDecimal result = new BigDecimal("0.00");//总的预收费用
		BigDecimal prepayTimeResult = new BigDecimal("0.00");//主费用项 按时间计费 预收费用 
		BigDecimal prepayMillResult = new BigDecimal("0.00");//主费用项 按里程计费 预收费用 
		BigDecimal mainResult = new BigDecimal("0.00");//主费用项 总的预收费用
		BigDecimal depositResult = new BigDecimal("0.00");//押金项预收费用
		BigDecimal attachResult = new BigDecimal("0.00");//附加项预收费用
		int i=0;
		String relaNo = null;
		if (orderBillingRela.isSaveLogFlag()){
			relaNo = seqRpcService.getDefNo();
		}
		Map<String,BigDecimal> tempResult = new HashMap<String, BigDecimal>();
		List<AppendChargeItem> newAttachChargeItems = null;
		List<EPrcChargeDetBo> detList = new ArrayList<EPrcChargeDetBo>();
		
		Map<String,Object> dailyBillDetMap = Maps.newHashMap();
		List<Map<String,Object>> dailyBillDetMapList = Lists.newArrayList();
		long meters = 0;
		long minutes = 0;
		BillingFunc billingFunc = null;
		for (DateItem dateItem : dateItems) {
			meters += dateItem.getMeters();
			minutes += dateItem.getMinutes();
			BigDecimal dailyResult = new BigDecimal("0.00");//每日总费用 每次循环初始化为0
			String recordDate = dateItem.getRecordDate();
			//在订单定价信息扩展里，有选择的附加项
			dateItem.setSelectedAttachChargeItemNos(orderBillingRela.getSelectedAttachItemNos());
			billingFunc = billingFuncMap.get(recordDate);
			//找不到特定当天特别调整的数据，就用默认通用的数据
			if(billingFunc == null){
				billingFunc = billingFuncMap.get(BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE);
			}
			if(i==0){
				//标识为第一天的数据
				dateItem.setFirstDay(true);
				if (orderBillingRela.isSaveLogFlag()) {
					List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
					if (depositChargeItems != null && depositChargeItems.size() > 0) {
						for (DepositChargeItem depositChargeItem : depositChargeItems) {
							EPrcChargeDetBo detBo = new EPrcChargeDetBo();
							detBo.setItemCode(depositChargeItem.getItemNo());
							detBo.setItemName(depositChargeItem.getItemName());
							detBo.setItemUnits(depositChargeItem.getPriceUnit().getUnitName());
							detBo.setPricingAmt(depositChargeItem.getPrice());
							detBo.setAmount(depositChargeItem.getPriceUnit().getValue() + "");
							detBo.setPrice(depositChargeItem.getPrice());
							detBo.setItemDesc(depositChargeItem.getRemarks());
							detBo.setCalcId(relaNo);
							detList.add(detBo);
						}
					}
				}
			}
			else{
				dateItem.setFirstDay(false);
			}
			
			//时租 情况 主费用项不计算预收
			if(!SubBe.SUB_BE_HOURS.contains(orderBillingRela.getRentType())){
				//预收计算主收费项目
				MainChargeItem mainChargeItem = billingFunc.getMainChargeItem();
				BigDecimal tempMainResult = billingCalculator.calculate(mainChargeItem, dateItem);
				mainResult = mainResult.add(tempMainResult);
				dailyResult = dailyResult.add(tempMainResult);
				if(mainChargeItem.getTimeResult()!=null){
					prepayTimeResult = prepayTimeResult.add(mainChargeItem.getTimeResult());
				}
				if(mainChargeItem.getMillResult()!=null){
					prepayMillResult = prepayMillResult.add(mainChargeItem.getMillResult());
				}
			}
			//预收计算押金项目
			// 押金项目
			List<DepositChargeItem> depositChargeItems = billingFunc
					.getDepositChargeItems();
			if (depositChargeItems != null && depositChargeItems.size() > 0) {
				for (DepositChargeItem depositChargeItem : depositChargeItems) {
					//if (billingCalculator.isChargeTimePointMatched(depositChargeItem, ChargeTimePoint.PRE)) {//日租情况下租金、押金、服务费全部预收  不判断是否预收 lipf注释20160414
						//算法里只会计算第一天的押金  其实也就是只计算一次
						BigDecimal temp = billingCalculator.calculate(depositChargeItem, dateItem);
						depositResult = depositResult.add(temp);
//						dailyResult = dailyResult.add(temp);
						String tempItemNo = depositChargeItem.getItemNo();
						if(tempResult.get(tempItemNo) != null){
							tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(depositChargeItem.getResult()));
						}else{
							tempResult.put(tempItemNo,depositChargeItem.getResult());
						}
						
					//}
				}
				//这里押金算入总费用  如果押金不算入 这行注释掉即可
//				if(i==0){
//					dailyResult = dailyResult.add(depositResult);
//				}
			}
			newAttachChargeItems = Lists.newArrayList();
			// 附加项目
			// 如果不存在用户订购的附加项，就不需要计算这部分内容了
			List<AppendChargeItem> tempAttachChargeItems = new ArrayList<AppendChargeItem>();
			if (StringUtils.isNotBlank(dateItem.getSelectedAttachChargeItemNos())) {
				List<AppendChargeItem> attachChargeItems = billingFunc
						.getAttachChargeItems();
				if (attachChargeItems != null && attachChargeItems.size() > 0) {
					for (AppendChargeItem attachChargeItem : attachChargeItems) {
						// 只有选中的项目才需要计算
						if (dateItem.getSelectedAttachChargeItemNos().indexOf(attachChargeItem.getItemNo()) >= 0||BuyType.REQUIRED.equals(attachChargeItem.getBuyType())) {
							//if (billingCalculator.isChargeTimePointMatched(attachChargeItem,ChargeTimePoint.PRE)) {//日租情况下租金、押金、服务费全部预收  不判断是否预收 lipf注释20160414
								BigDecimal temp = billingCalculator.calculate(attachChargeItem,
										dateItem);
							
								if(!orderBillingRela.isSaveLogFlag()){//说明不是提交订单时候计算预收
									if(BuyType.REQUIRED.equals(attachChargeItem.getBuyType())){
//										attachResult = attachResult.add(temp);
										dailyResult = dailyResult.add(temp);
									}
								}else{
									attachResult = attachResult.add(temp);
									dailyResult = dailyResult.add(temp);
								}
								newAttachChargeItems.add(attachChargeItem);
								String tempItemNo = attachChargeItem.getItemNo();
								Map<String,Object> tempAttachMap = Maps.newHashMap();
								tempAttachMap.put("dailyDate", recordDate);
								tempAttachMap.put("dailyBeginTime", dateItem.getStartTime());
								tempAttachMap.put("dailyEndTime", dateItem.getEndTime());
								if((PriceUnit.DAY+PriceUnit.HOUR+PriceUnit.MINUTE).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
									tempAttachMap.put("dailyNum", minutes);
								}else if((PriceUnit.KM+PriceUnit.M).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
									tempAttachMap.put("dailyNum", meters);
								} else {
									tempAttachMap.put("dailyNum","1");
								}
								tempAttachMap.put("dailyAmt", attachChargeItem.getResult());
								tempAttachMap.put("dailyComments", "");
								List<Map<String,Object>> billSectionDetList = Lists.newArrayList();
								tempAttachMap.put("billSectionDet", billSectionDetList);
								Map<String,Object> billSectionDet = Maps.newHashMap();
								billSectionDet.put("billSectNum", tempAttachMap.get("dailyNum"));
								billSectionDet.put("billSectStartValue", StringUtil.nullToString(dateItem.getStartTime()));
								billSectionDet.put("billSectEndValue", StringUtil.nullToString(dateItem.getEndTime()));
								billSectionDet.put("billSectAmt", attachChargeItem.getResult());
								billSectionDet.put("tariffUnitNum", attachChargeItem.getPriceUnit().getValue());
								billSectionDet.put("tariffUnit", attachChargeItem.getPriceUnit().getUnitName());
								billSectionDet.put("scaledRate", attachChargeItem.getPrice());
								billSectionDetList.add(billSectionDet);
								if(tempResult.get(tempItemNo) != null){
									tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(attachChargeItem.getResult()));
									List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(tempItemNo);
									newDailyBillDetMapList.add(tempAttachMap);
									dailyBillDetMap.put(tempItemNo, newDailyBillDetMapList);
								}else{
									tempResult.put(tempItemNo,attachChargeItem.getResult());
									dailyBillDetMapList = Lists.newArrayList();
									dailyBillDetMapList.add(tempAttachMap);
									dailyBillDetMap.put(tempItemNo, dailyBillDetMapList);
								}
								tempAttachChargeItems.add(attachChargeItem);
							//}
						}
					}
					
				}
			}
			billingFunc.setAttachChargeItems(newAttachChargeItems);
			result = result.add(dailyResult);
			billingFunc.setMinutes(dateItem.getMinutes());
			billingFunc.setMeters(dateItem.getMeters());
			//费用详细信息保存e_billing_order_rela_pricing（isSaveLogFlag字段在下单的时候要传入）
			if(orderBillingRela.isSaveLogFlag()){
				BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
				billingOrderPricingBo.setAppNo(orderBillingRela.getAppNo());
				String pricingDetailNo = seqRpcService.getDefNo();
				billingOrderPricingBo.setPricingDetailNo(pricingDetailNo);
				billingOrderPricingBo.setRelaNo(relaNo);
				billingOrderPricingBo.setBillingNo(billingFunc.getBillingNo());
				billingOrderPricingBo.setSubBe(orderBillingRela.getRentType());
				billingOrderPricingBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
				billingOrderPricingBo.setVersion(billingFunc.getVersion());
				billingOrderPricingBo.setApplyDate(recordDate);
				billingOrderPricingBo.setPricingAmt(dailyResult.add(depositResult));
				billingOrderPricingBo.setDetails(JsonMapper.nonEmptyMapper().toJson(billingFunc));
				billingOrderPricingBo.setDataOperTime(new DateTime().toDate());
				billingOrderPricingBo.setDateOperType("I");
				billingOrderPricingBoMapper.insertSelective(billingOrderPricingBo);
			}
			Map<String,Object> tempTimeMap = Maps.newHashMap();
			tempTimeMap.put("dailyDate", recordDate);
			tempTimeMap.put("dailyBeginTime", dateItem.getStartTime());
			tempTimeMap.put("dailyEndTime", dateItem.getEndTime());
			tempTimeMap.put("dailyNum", dateItem.getMinutes());
			tempTimeMap.put("dailyAmt", billingFunc.getMainChargeItem().getTimeResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getTimeResult());
			tempTimeMap.put("dailyComments", "");
			List<Map<String,Object>> timeBillSectionDetList = Lists.newArrayList();
			tempTimeMap.put("billSectionDet", timeBillSectionDetList);
			LOGGER.debug("++++{}",JsonMapper.nonEmptyMapper().toJson(tempTimeMap));

			SubChargeItem timeChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem();
			if(timeChargeItem!=null){
				Map<String,Object> billSectionDet = Maps.newHashMap();
				if(timeChargeItem.getRangeChargeItems()!=null){
					for (RangeChargeItem rangeChargeItem : timeChargeItem.getRangeChargeItems()) {
						billSectionDet = Maps.newHashMap();
						billSectionDet.put("billSectNum", rangeChargeItem.getActAmount());
						billSectionDet.put("billSectStartValue", rangeChargeItem.getRange().getFrom());
						billSectionDet.put("billSectEndValue", rangeChargeItem.getRange().getTo());
						billSectionDet.put("billSectAmt", rangeChargeItem.getResult());
						billSectionDet.put("tariffUnitNum", rangeChargeItem.getPriceUnit().getValue());
						billSectionDet.put("tariffUnit", rangeChargeItem.getPriceUnit().getUnitName());
						billSectionDet.put("scaledRate", rangeChargeItem.getPrice());
						timeBillSectionDetList.add(billSectionDet);
					}
				} else {
					billSectionDet = Maps.newHashMap();
					billSectionDet.put("billSectNum", dateItem.getMinutes());
					billSectionDet.put("billSectStartValue", dateItem.getStartTime());
					billSectionDet.put("billSectEndValue", dateItem.getEndTime());
					billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getTimeResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getTimeResult());
					billSectionDet.put("tariffUnitNum", timeChargeItem.getPriceUnit().getValue());
					billSectionDet.put("tariffUnit", timeChargeItem.getPriceUnit().getUnitName());
					billSectionDet.put("scaledRate", timeChargeItem.getPrice());
					timeBillSectionDetList.add(billSectionDet);
				}
			}
			if(dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO)!=null){
				List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
				newDailyBillDetMapList.add(tempTimeMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO, newDailyBillDetMapList);
			} else {
				dailyBillDetMapList = Lists.newArrayList();
				dailyBillDetMapList.add(tempTimeMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO, dailyBillDetMapList);
			}
			Map<String,Object> tempMillMap = Maps.newHashMap();
			tempMillMap.put("dailyDate", recordDate);
			tempMillMap.put("dailyBeginTime", dateItem.getStartTime());
			tempMillMap.put("dailyEndTime", dateItem.getEndTime());
			tempMillMap.put("dailyNum", dateItem.getMeters());
			tempMillMap.put("dailyAmt", billingFunc.getMainChargeItem().getMillResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getMillResult());
			tempMillMap.put("dailyComments", "");
			List<Map<String,Object>> billSectionDetList = Lists.newArrayList();
			tempMillMap.put("billSectionDet", billSectionDetList);
			SubChargeItem millChargeItem = billingFunc.getMainChargeItem().getMillChargeItem();
			if(millChargeItem!=null){
				Map<String,Object> billSectionDet = Maps.newHashMap();
				if(millChargeItem.getRangeChargeItems()!=null){
					for (RangeChargeItem rangeChargeItem : millChargeItem.getRangeChargeItems()) {
						billSectionDet = Maps.newHashMap();
						billSectionDet.put("billSectNum", rangeChargeItem.getActAmount());
						billSectionDet.put("billSectStartValue", rangeChargeItem.getRange().getFrom());
						billSectionDet.put("billSectEndValue", rangeChargeItem.getRange().getTo());
						billSectionDet.put("billSectAmt", rangeChargeItem.getResult());
						billSectionDet.put("tariffUnitNum", rangeChargeItem.getPriceUnit().getValue());
						billSectionDet.put("tariffUnit", rangeChargeItem.getPriceUnit().getUnitName());
						billSectionDet.put("scaledRate", rangeChargeItem.getPrice());
						billSectionDetList.add(billSectionDet);
					}
				} else {
					billSectionDet = Maps.newHashMap();
					billSectionDet.put("billSectNum", dateItem.getMeters());
					billSectionDet.put("billSectStartValue", dateItem.getStartTime());
					billSectionDet.put("billSectEndValue", dateItem.getEndTime());
					billSectionDet.put("billSectAmt", billingFunc.getMainChargeItem().getMillResult()==null?new BigDecimal("0.00"):billingFunc.getMainChargeItem().getMillResult());
					billSectionDet.put("tariffUnitNum", millChargeItem.getPriceUnit().getValue());
					billSectionDet.put("tariffUnit", millChargeItem.getPriceUnit().getUnitName());
					billSectionDet.put("scaledRate", millChargeItem.getPrice());
					billSectionDetList.add(billSectionDet);
				}
			}
			if(dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO)!=null){
				List<Map<String,Object>> newDailyBillDetMapList = (List<Map<String,Object>>)dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
				newDailyBillDetMapList.add(tempMillMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO, newDailyBillDetMapList);
			} else {
				dailyBillDetMapList = Lists.newArrayList();
				dailyBillDetMapList.add(tempMillMap);
				dailyBillDetMap.put(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO, dailyBillDetMapList);
			}
			i++;
		}
		OrderBillingRelaResult orderBillingRelaResult = new OrderBillingRelaResult();
		//主费用项保存（isSaveLogFlag字段在下单的时候要传入）
		if(orderBillingRela.isSaveLogFlag()){
			billingOrderRelaBo.setRelaNo(relaNo);
			billingOrderRelaBo.setBillType(BillingConstants.BillType.PREPAY);
			billingOrderRelaBo.setAppNo(orderBillingRela.getAppNo());
			billingOrderRelaBo.setSelectedAttachItemNos(orderBillingRela.getSelectedAttachItemNos());
			billingOrderRelaBo.setDefaultBillingNo(billingConfigBo.getBillingNo());
			billingOrderRelaBo.setVersionLimit(billingConfigServiceImpl.getNowVersion());
			billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
			billingOrderRelaBo.setDateOperType("I");
			billingOrderRelaBo.setPricingAmt(result.add(depositResult));
			billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
			List<EPrcChargeBo> list = new ArrayList<EPrcChargeBo>();
			EPrcChargeBo ePrcChargeBo = new EPrcChargeBo();
			ePrcChargeBo.setAppNo(orderBillingRela.getAppNo());
			ePrcChargeBo.setCalcId(relaNo);
			ePrcChargeBo.setBillingType(BillingConstants.BillType.PREPAY);
			ePrcChargeBo.setPricingAmt(result.add(depositResult)+"");
			list.add(ePrcChargeBo);
			Map<String,Object> prcCharge = new HashMap<String, Object>();
			prcCharge.put("ePrcChargeList", list);
//			ePrcChargeService.insertEPrcCharge(list);
			String chargeWay = orderBillingConfigs.get(0).getChargeWay();
			if(ChargeWay.BY_MILL.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				EPrcChargeDetBo detBo = new EPrcChargeDetBo();
				detBo.setItemCode(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
				detBo.setItemName("里程费");
				detBo.setPricingAmt(prepayMillResult+"");
				detBo.setAmount(meters+"");
				detBo.setCalcId(relaNo);
				detBo.setItemDesc(billingFunc==null?"":billingFunc.getMainChargeItem().getMillChargeItem().getPriceRemark());
				detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO));
				detList.add(detBo);
			} 
			if(ChargeWay.BY_TIME.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				EPrcChargeDetBo detBo = new EPrcChargeDetBo();
				detBo.setItemCode(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
				detBo.setItemName("租金");
				detBo.setPricingAmt(prepayTimeResult+"");
				detBo.setAmount(minutes+"");
				detBo.setCalcId(relaNo);
				detBo.setItemDesc(billingFunc==null?"":billingFunc.getMainChargeItem().getTimeChargeItem().getPriceRemark());
				detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO));
				detList.add(detBo);
			}
			if(newAttachChargeItems != null &&newAttachChargeItems.size()>0){
				for (AppendChargeItem attachChargeItem : newAttachChargeItems) {
					EPrcChargeDetBo detBo = new EPrcChargeDetBo();
					detBo.setItemCode(attachChargeItem.getItemNo());
					detBo.setItemName(attachChargeItem.getItemName());
					detBo.setPrice(attachChargeItem.getPrice());
					detBo.setPricingAmt(String.format("%.2f", tempResult.get(attachChargeItem.getItemNo())));
					detBo.setItemUnits(attachChargeItem.getPriceUnit().getDesc());
					if((PriceUnit.DAY+PriceUnit.HOUR+PriceUnit.MINUTE).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
						detBo.setAmount(minutes+"");
					}else if((PriceUnit.KM+PriceUnit.M).indexOf(attachChargeItem.getPriceUnit().getUnit())>-1){
						detBo.setAmount(meters+"");
					} else {
						detBo.setAmount("1");
					}
					detBo.setCalcId(relaNo);
					detBo.setDailyBillDetList((List<Map<String, Object>>) dailyBillDetMap.get(attachChargeItem.getItemNo()));
					detList.add(detBo);
				}
			}
			prcCharge.put("ePrcChargeDetList", detList);
			orderBillingRelaResult.setPrcCharge(prcCharge);
//			ePrcChargeService.insertEPrcChargeDet(detList);
		}
		if(billingFunc2!=null){
			billingFunc2.getMainChargeItem().setResult(prepayTimeResult.add(prepayMillResult));
			billingFunc2.getMainChargeItem().setTimeResult(prepayTimeResult);
			billingFunc2.getMainChargeItem().setMillResult(prepayMillResult);
			if(billingFunc2.getAttachChargeItems()!=null){
				for (AppendChargeItem item : billingFunc2.getAttachChargeItems()) {
					item.setResult(tempResult.get(item.getItemNo()));
				}
			}
		}
		orderBillingRelaResult.setAttachResult(attachResult);
		orderBillingRelaResult.setDepositResult(depositResult);
		orderBillingRelaResult.setMainResult(mainResult);
		orderBillingRelaResult.setResult(result);
		orderBillingRelaResult.setMeters(meters);
		orderBillingRelaResult.setMinutes(minutes);
		return orderBillingRelaResult;
	}
	/**
	 * 根据appNo查找按日期（applyDate）分组好的定价配置
	 */
	@Override
	public Map<String, BillingFunc> getApplyDateBillingConfig(OrderBillingRela orderBillingRela) {
		LOGGER.debug("根据appNo查找按日期（applyDate）分组好的定价配置,入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		String appNo = orderBillingRela.getAppNo();
		//关联到定价的方式比较复杂。先从billing_order_rela里找到appNo对应的，然后从billing_config里找到和日匹配的billingNo 然后找到billingFunc
		BillingOrderRelaBoExample billingOrderPricingBoExample = new BillingOrderRelaBoExample();
		billingOrderPricingBoExample.createCriteria().andAppNoEqualTo(appNo);
		List<BillingOrderRelaBo> billingOrderPricingBos = billingOrderRelaBoMapper.selectByExample(billingOrderPricingBoExample);
		//先搜索出下单时记录的相关信息，找到租赁类型 租赁点/公司 车型 版本号等数据
		BillingOrderRelaBo billingOrderRelaBo = billingOrderPricingBos.get(0);
		//再搜索出定价里符合条件的（按照APPLY_DATE分组好的，在下单时间戳之前生效的，指定 租赁类型 租赁点/公司 车型 的 定价
		List<BillingConfigBo> orderBillingConfigs = billingConfigDao.selectByOrderRela(billingOrderRelaBo);
		//无论是不是调整后的定价，都在map里 因为这里必须做这个判断来获取BillingFunc
		Map<String,BillingFunc> billingFuncMap = Maps.newHashMap();
		for (BillingConfigBo orderBillingConfig  : orderBillingConfigs) {
			String billingNo = orderBillingConfig.getBillingNo();
			
			BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
			LOGGER.debug("适用日期:{} 定价:{}",orderBillingConfig.getApplyDate(),ToStringBuilder.reflectionToString(billingFunc));
			billingFuncMap.put(orderBillingConfig.getApplyDate(), billingFunc);
		}
		LOGGER.debug("根据appNo查找按日期（applyDate）分组好的定价配置,出参：{}",JsonMapper.nonEmptyMapper().toJson(billingFuncMap));
		return billingFuncMap;
	}
	/**
	 * 根据appNo、billType查询订单计费信息(billType-01预收、02结算)
	 *		出参billingOrderRela、billingOrderPricingList 
	 */
	@Override
	public OrderBillingPriceResponse getBillingOrderPriceInfo(BillingOrderRelaBo billingOrderRelaBo) {
		LOGGER.debug("订单费用详情查询开始，入参：{}",JsonMapper.nonEmptyMapper().toJson(billingOrderRelaBo));
		OrderBillingPriceResponse response = new OrderBillingPriceResponse();
		
		BillingOrderRelaBo bo = billingConfigServiceImpl.getBillingOrderRela(billingOrderRelaBo);
		if(bo==null){
			return null;
		}
		billingOrderRelaBo.setRelaNo(bo.getRelaNo());
		List<BillingOrderPricingBo> list = billingConfigServiceImpl.getBillingOrderPricingList(billingOrderRelaBo);
		BigDecimal timeResult = new BigDecimal("0.00");//主费用项 按时间计费费用 
		BigDecimal millResult = new BigDecimal("0.00");//主费用项 按里程计费 费用 
		BigDecimal mainResult = new BigDecimal("0.00");//主费用项 总的费用
		Map<String,BillingFunc> dayPriceMap = new HashMap<String, BillingFunc>();
		List<DepositChargeItem> totalDepositChargeItems = null;
		List<AppendChargeItem> totalAttachChargeItems = null;
		MainChargeItem mainChargeItem = null;
		Map<String,BigDecimal> tempResult = new HashMap<String, BigDecimal>();
		long meters = 0;
		long minutes = 0;
		if(list!=null&&list.size()>0){
			for (BillingOrderPricingBo billingOrderPricingBo : list) {
				BillingFunc billingFunc = JsonMapper.DEFAULT.fromJson(billingOrderPricingBo.getDetails(), BillingFunc.class);
				meters += billingFunc.getMeters();
				minutes += billingFunc.getMinutes();
				if(billingFunc.getMainChargeItem()!=null&&mainChargeItem==null){
					mainChargeItem = new MainChargeItem();
					BeanUtils.copyProperties(billingFunc.getMainChargeItem(), mainChargeItem);
				}
				dayPriceMap.put(billingOrderPricingBo.getApplyDate(), billingFunc);
				if(billingFunc.getMainChargeItem()!=null&&billingFunc.getMainChargeItem().getResult()!=null){
					mainResult = mainResult.add(billingFunc.getMainChargeItem().getResult());
				}
				if(billingFunc.getMainChargeItem()!=null&&billingFunc.getMainChargeItem().getMillResult()!=null){
					millResult = millResult.add(billingFunc.getMainChargeItem().getMillResult());
				}
				if(billingFunc.getMainChargeItem()!=null&&billingFunc.getMainChargeItem().getTimeResult()!=null){
					timeResult = timeResult.add(billingFunc.getMainChargeItem().getTimeResult());
				}
				
				List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
				if(depositChargeItems!=null&&depositChargeItems.size()>0){
					if(totalDepositChargeItems==null||totalDepositChargeItems.size()<1){
						totalDepositChargeItems = depositChargeItems;
					}
					for (DepositChargeItem depositChargeItem : depositChargeItems) {
						String tempItemNo = depositChargeItem.getItemNo();
						if(tempResult.get(tempItemNo) != null){
							tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(depositChargeItem.getResult()));
						}else{
							tempResult.put(tempItemNo,depositChargeItem.getResult());
						}
					}
				}
				List<AppendChargeItem> attachChargeItems = billingFunc.getAttachChargeItems();
				if(attachChargeItems!=null&&attachChargeItems.size()>0){
					if(totalAttachChargeItems==null||totalAttachChargeItems.size()<1){
						totalAttachChargeItems = attachChargeItems;
					}
					for (AppendChargeItem appendChargeItem : attachChargeItems) {
						String tempItemNo = appendChargeItem.getItemNo();
						if(tempResult.get(tempItemNo) != null){
							tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(appendChargeItem.getResult()));
						}else{
							tempResult.put(tempItemNo,appendChargeItem.getResult());
						}
					}
				}
			}
		}
		if(totalAttachChargeItems!=null&&totalAttachChargeItems.size()>0){
			for (AppendChargeItem appendChargeItem : totalAttachChargeItems) {
				appendChargeItem.setResult(tempResult.get(appendChargeItem.getItemNo()));
			}
			response.setAttachChargeItems(totalAttachChargeItems);
		}
		if(totalDepositChargeItems!=null&&totalDepositChargeItems.size()>0){
			for (DepositChargeItem depositChargeItem : totalDepositChargeItems) {
				depositChargeItem.setResult(tempResult.get(depositChargeItem.getItemNo()));
			}
			response.setDepositChargeItems(totalDepositChargeItems);
		}
		
		if(mainChargeItem!=null){
			mainChargeItem.setMillResult(millResult);
			mainChargeItem.setTimeResult(timeResult);
			mainChargeItem.setResult(mainResult);
			response.setMainChargeItem(mainChargeItem);
		}
		
		
//		response.setResult(bo.getPricingAmt());
//		response.setDepositResult(depositResult);
//		response.setAttachResult(attachResult);
//		response.setMainResult(mainResult);
		response.setMeters(meters/1000);
		response.setMinutes(minutes);
		response.setResult(bo.getPricingAmt());
//		response.setDayPriceMap(dayPriceMap);
		LOGGER.debug("订单费用详情查询结束，出参：{}",JsonMapper.nonEmptyMapper().toJson(response));
		return response;
	}

	/**
	 * 
	 * 方法说明：用于预收费用时候将默认取车时间 还车时间分隔成按日期分组好的格式
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月6日 下午3:42:36
	 * History:  2016年4月6日 下午3:42:36   lipf   Created.
	 *
	 * @param billingLocateCondition
	 * @return
	 *
	 */
	private List<DateItem> transBillingCondition(OrderBillingRela orderBillingRela) {
		String startDate = orderBillingRela.getPlanQcTime().substring(0, 10).replace("-", "");
		String endDate = orderBillingRela.getPlanHcTime().substring(0, 10).replace("-", "");
		List<DateItem> itemList = Lists.newArrayList();
		List<String> arrayList= JodaDateTime.getYmdArray(startDate, endDate,"yyyyMMdd");
		for (int i = 0; i < arrayList.size(); i++) {
			DateItem item = new DateItem();
			item.setRecordDate(arrayList.get(i));
			item.setMeters(orderBillingRela.getMeters());
			if(arrayList.size()==1){
				item.setMinutes(DateTools.getMinutesBetween(orderBillingRela.getPlanQcTime()+":00", orderBillingRela.getPlanHcTime()+":00"));
			} else if(i==0){
				item.setMinutes(DateTools.getMinutesBetween(orderBillingRela.getPlanQcTime()+":00", DateTools.formatDateStr(arrayList.get(i)+" 2400","yyyyMMdd HHmm","yyyy-MM-dd HH:mm:ss")));
			} else if (i==arrayList.size()-1){
				item.setMinutes(DateTools.getMinutesBetween(DateTools.formatDateStr(arrayList.get(i),"yyyyMMdd","yyyy-MM-dd HH:mm:ss"), orderBillingRela.getPlanHcTime()+":00"));
			} else {
				item.setMinutes(1440);
			}
			itemList.add(item);
		}
		return itemList;
	}
	
	private List<DateItem> transBillingConditionByDay(OrderBillingRela orderBillingRela) {
		if( SubBe.SUB_BE_MONTHS.contains(orderBillingRela.getRentType())||SubBe.SUB_BE_YEARS.contains(orderBillingRela.getRentType())){
			List<String> months = JodaDateTime.getMonthArray(orderBillingRela.getPlanQcTime()+":00", orderBillingRela.getPlanHcTime()+":00", "yyyy-MM-dd HH:mm:ss");
			List<DateItem> itemList = Lists.newArrayList();
			for (int i = 0; i < months.size(); i++) {
				DateItem item = new DateItem();
				item.setRecordDate(months.get(i).substring(0, 10).replace("-", ""));
				item.setMeters(orderBillingRela.getMeters());
				item.setMinutes(1);
                item.setStartTime(orderBillingRela.getPlanQcTime()+":00");
                item.setEndTime(orderBillingRela.getPlanHcTime()+":00");
				itemList.add(item);
			}
			return itemList;
		}
		int minutes = DateTools.getMinutesBetween(orderBillingRela.getPlanQcTime()+":00", orderBillingRela.getPlanHcTime()+":00");
		long days = minutes / 1440;
		long b = minutes % 1440;
		long quantity = days;
		if (b != 0) {
			quantity += 1;
		}
		int startDate = Integer.valueOf(orderBillingRela.getPlanQcTime().substring(0, 10).replace("-", ""));
		List<DateItem> itemList = Lists.newArrayList();
		for (int i = 0; i < quantity; i++) {
			DateItem item = new DateItem();
			item.setRecordDate(startDate+i+"");
			item.setMeters(orderBillingRela.getMeters());
			if(quantity==1 || i==quantity-1){
				item.setMinutes(b==0?1440:b);
			} else {
				item.setMinutes(1440);
			}
            item.setStartTime(orderBillingRela.getPlanQcTime()+":00");
            item.setEndTime(orderBillingRela.getPlanHcTime()+":00");
			itemList.add(item);
		}
		return itemList;
	}
	

	/**
	 * @param inMap
	 * <li>inMap.put("rentType")
	 * <li>inMap.put("orgCode")
	 * <li>inMap.put("autoModelNo")
	 * <li>inMap.put("rtNo")
	 * 以下rentType为02时必传
	 * <li>inMap.put("planQcTime")
	 * <li>inMap.put("planHcTime")
	 * <li>inMap.put("meters")
	 */
	@Override
	public Map<String, Object> rentPrepayForHour(OrderBillingRela orderBillingRela) {
		LOGGER.debug("时租预收计费开始，只计算押金，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		Map<String,Object> resultMap = new HashMap<String, Object>();
		BillingConfigBo billingConfigBo = locateBillingConfigBo(orderBillingRela.getRentType(), orderBillingRela);
		String billingNo = billingConfigBo.getBillingNo();
		BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
		
		List<Map<String,Object>> mainChargeItemList = new ArrayList<Map<String,Object>>();
		String chargeWay = billingConfigBo.getChargeWay();
		Map<String,Object> map = null;
		String priceUnitDesc = billingConfigBo.getMinPriceUnitDesc();
		Map<String,Object> tMap = formatFunc(billingFunc);
		if(ChargeWay.BY_MILL.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
			map = new HashMap<String, Object>();
			map.put("itemNo", "7777777");
			map.put("itemName", "里程费");
			map.put("price", priceUnitDesc.split(",").length>1?priceUnitDesc.split(",")[1]:priceUnitDesc);
			Map<String,Object> t= new HashMap<String, Object>();
			t.put("applyDate", billingConfigBo.getApplyDate());
			t.put("millPriceList", tMap.get("millChargeItemList"));
			map.put("millChargeItemList", t);
			mainChargeItemList.add(map);
		} 
		if(ChargeWay.BY_TIME.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
			map = new HashMap<String, Object>();
			map.put("itemNo", "5201314");
			map.put("itemName", "租金");
			map.put("price", priceUnitDesc.split(",").length>1?priceUnitDesc.split(",")[0]:priceUnitDesc);
			Map<String,Object> t= new HashMap<String, Object>();
			t.put("applyDate", billingConfigBo.getApplyDate());
			t.put("timePriceList", tMap.get("timeChargeItemList"));
			map.put("timeChargeItemList", t);
			mainChargeItemList.add(map);
		} 
		resultMap.put("mainChargeItemList", mainChargeItemList);
		List<Map<String,Object>> depositChargeItemList = new ArrayList<Map<String,Object>>();
		BigDecimal depositResult = new BigDecimal("0.00");
		List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
		List<EPrcChargeDetBo> detList = new ArrayList<EPrcChargeDetBo>();
		if(depositChargeItems!=null&&depositChargeItems.size()>0){
			for (DepositChargeItem depositChargeItem : depositChargeItems) {
				depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
				depositChargeItem.setResult(new BigDecimal(depositChargeItem.getPrice()));
				map = new HashMap<String, Object>();
				map.put("itemNo", depositChargeItem.getItemNo());
				map.put("itemName", depositChargeItem.getItemName());
				map.put("price", String.format("%.2f", Double.valueOf(depositChargeItem.getPrice()))+"元/"+depositChargeItem.getPriceUnit().getDesc());
				map.put("priceAmt", String.format("%.2f", Double.valueOf(depositChargeItem.getPrice()))+"元/"+depositChargeItem.getPriceUnit().getDesc());
				map.put("buyType", depositChargeItem.getBuyType());
				map.put("buyTypeName", depositChargeItem.getBuyTypeName());
				map.put("remark", depositChargeItem.getRemarks()==null?"":depositChargeItem.getRemarks());
				depositChargeItemList.add(map);
				if(orderBillingRela.isSaveLogFlag()){
					EPrcChargeDetBo detBo = new EPrcChargeDetBo();
					detBo.setItemCode(depositChargeItem.getItemNo());
					detBo.setItemName(depositChargeItem.getItemName());
					detBo.setItemUnits(depositChargeItem.getPriceUnit().getUnitName());
					detBo.setPricingAmt(depositChargeItem.getPrice());
					detBo.setAmount(depositChargeItem.getPriceUnit().getValue()+"");
					detBo.setPrice(depositChargeItem.getPrice()+"/"+depositChargeItem.getPriceUnit().getDesc());
					detBo.setItemDesc(depositChargeItem.getRemarks());
					detList.add(detBo);
				}
			}
		}
		resultMap.put("depositChargeItemList", depositChargeItemList);
		List<Map<String,Object>> attachChargeItemList = new ArrayList<Map<String,Object>>();
		List<AppendChargeItem> attachChargeItems = billingFunc.getAttachChargeItems();
		if(attachChargeItems!=null&&attachChargeItems.size()>0){
			for (AppendChargeItem appendChargeItem : attachChargeItems) {
				map = new HashMap<String, Object>();
				map.put("itemNo", appendChargeItem.getItemNo());
				map.put("itemName", appendChargeItem.getItemName());
				map.put("price", String.format("%.2f", Double.valueOf(appendChargeItem.getPrice()))+"元/"+appendChargeItem.getPriceUnit().getDesc());
				map.put("priceAmt", String.format("%.2f", Double.valueOf(appendChargeItem.getPrice()))+"元/"+appendChargeItem.getPriceUnit().getDesc());
				map.put("buyType", appendChargeItem.getBuyType());
				map.put("buyTypeName", appendChargeItem.getBuyTypeName());
				map.put("remark", appendChargeItem.getRemarks()==null?"":appendChargeItem.getRemarks());
				attachChargeItemList.add(map);
			}
		}
		resultMap.put("attachChargeItemList", attachChargeItemList);
		resultMap.put("prepayResult", new BigDecimal("0.00"));
		resultMap.put("depositResult", String.format("%.2f",depositResult));
		if(orderBillingRela.isSaveLogFlag()){
			String relaNo = seqRpcService.getDefNo();
			BillingOrderRelaBo billingOrderRelaBo = new BillingOrderRelaBo();
			billingOrderRelaBo.setAppNo(orderBillingRela.getAppNo());
			billingOrderRelaBo.setRelaNo(relaNo);
			billingOrderRelaBo.setBillType(BillingConstants.BillType.PREPAY);
			billingOrderRelaBo.setSelectedAttachItemNos(orderBillingRela.getSelectedAttachItemNos());
			billingOrderRelaBo.setSubBe(billingConfigBo.getSubBe());
			billingOrderRelaBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
			billingOrderRelaBo.setDefaultBillingNo(billingConfigBo.getBillingNo());
			billingOrderRelaBo.setVersionLimit(billingConfigServiceImpl.getNowVersion());
			billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
			billingOrderRelaBo.setDateOperType("I");
			billingOrderRelaBo.setPricingAmt(depositResult);
			billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
			List<EPrcChargeBo> list = new ArrayList<EPrcChargeBo>();
			EPrcChargeBo ePrcChargeBo = new EPrcChargeBo();
			ePrcChargeBo.setAppNo(orderBillingRela.getAppNo());
			ePrcChargeBo.setCalcId(relaNo);
			ePrcChargeBo.setBillingType(BillingConstants.BillType.PREPAY);
			ePrcChargeBo.setPricingAmt(depositResult+"");
			list.add(ePrcChargeBo);
			ePrcChargeService.insertEPrcCharge(list);
			EPrcChargeDetBo detBo = new EPrcChargeDetBo();
			detBo.setCalcId(relaNo);
			if(detList!=null&&detList.size()>0){
				for (EPrcChargeBo ePrcChargeBo2 : list) {
					ePrcChargeBo2.setCalcId(relaNo);
				}
				ePrcChargeService.insertEPrcChargeDet(detList);
			}
			
			/*BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
			billingOrderPricingBo.setAppNo(orderBillingRela.getAppNo());
			String pricingDetailNo = seqRpcService.getDefNo();
			billingOrderPricingBo.setPricingDetailNo(pricingDetailNo);
			billingOrderPricingBo.setRelaNo(relaNo);
			billingOrderPricingBo.setBillingNo(billingConfigBo.getBillingNo());
			billingOrderPricingBo.setSubBe(orderBillingRela.getRentType());
			billingOrderPricingBo.setOrgAutoModelKey(billingConfigBo.getOrgAutoModelKey());
			billingOrderPricingBo.setVersion(billingConfigBo.getVersion());
			billingOrderPricingBo.setApplyDate(billingConfigServiceImpl.getTodayString());
			billingOrderPricingBo.setPricingAmt(depositResult);
			billingFunc.setMainChargeItem(null);
			billingFunc.setAttachChargeItems(null);
			billingOrderPricingBo.setDetails(JsonMapper.nonEmptyMapper().toJson(billingFunc));
			billingOrderPricingBo.setDataOperTime(new DateTime().toDate());
			billingOrderPricingBo.setDateOperType("I");
			billingOrderPricingBoMapper.insertSelective(billingOrderPricingBo);*/
			
		}
		
		LOGGER.debug("时租预收计费开始，只计算押金，出参：{}",resultMap);
		return resultMap;
	}
	
	@Override
	public Map<String, Object> rentPrepayForDaily(OrderBillingRela orderBillingRela) {
		LOGGER.debug("日租预收计费开始，主费用项、押金项及附加费用项全部计算，入参：{}",JsonMapper.nonEmptyMapper().toJson(orderBillingRela));
		Map<String,Object> inMap = new HashMap<String, Object>();
		String orgAutoModelKey = getCompanyKey(orderBillingRela);
		inMap.put("rentType", orderBillingRela.getRentType());
		inMap.put("orgAutoModelKey", orgAutoModelKey);
		inMap.put("versionLimit", getNowVersion());
		
		String nowTime = JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
		String maxRentDay =sysParamService.getSysParamsValues("maxRentDay");//日租最小可租天数
		String maxRentTime = DateTools.getPreOrFutureDay(Integer.valueOf(maxRentDay),nowTime);
		inMap.put("maxRentTime", maxRentTime);
		//再搜索出定价里符合条件的（按照APPLY_DATE分组好的，在下单时间戳之前生效的，指定 租赁类型 租赁点/公司 车型 的 定价
		List<BillingConfigBo> orderBillingConfigs = billingConfigDao.selectPrepayByRela(inMap);
		Map<String,Object> resultMap = new HashMap<String, Object>();
		List<Map<String,Object>> mainChargeItemList = new ArrayList<Map<String,Object>>();
		Map<String,Object> map = null;
		
		//无论是不是调整后的定价，都在map里 因为这里必须做这个判断来获取BillingFunc
		Map<String,BillingFunc> billingFuncMap = Maps.newHashMap();
		Map<String,Object> periodMap = new HashMap<String, Object>();
		List<Map<String,Object>> millChargeItemList = new ArrayList<Map<String,Object>>();
		List<Map<String,Object>> timeChargeItemList = new ArrayList<Map<String,Object>>();
		int flag=1;
		String chargeWay =orderBillingConfigs.get(0).getChargeWay();
		StringBuffer defaultBillingNo = new StringBuffer();
		List<EPrcChargeDetBo> detList = new ArrayList<EPrcChargeDetBo>();
		for (BillingConfigBo orderBillingConfig  : orderBillingConfigs) {
			String billingNo = orderBillingConfig.getBillingNo();
			defaultBillingNo.append(billingNo);
			BillingFunc billingFunc = billingConfigServiceImpl.getBillingFunc(billingNo);
			billingFunc.setBillingNo(billingNo);
			billingFunc.setVersion(orderBillingConfig.getVersion());
			LOGGER.debug("适用日期:{} 定价:{}",orderBillingConfig.getApplyDate(),ToStringBuilder.reflectionToString(billingFunc));
			billingFuncMap.put(orderBillingConfig.getApplyDate(), billingFunc);
			
			Map<String,Object> tMap = formatFunc(billingFunc);
			periodMap = new HashMap<String, Object>();
			periodMap.put("applyDate", orderBillingConfig.getApplyDate());
			if(ChargeWay.BY_MILL.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				periodMap.put("millPriceList", tMap.get("millChargeItemList"));
				millChargeItemList.add(periodMap);
				if(flag == orderBillingConfigs.size()){
					map = new HashMap<String, Object>();
					map.put("itemNo", BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO);
					map.put("itemName", "里程费");
					map.put("millChargeItemList", millChargeItemList);
					mainChargeItemList.add(map);
				}
			}
			periodMap = new HashMap<String, Object>();
			periodMap.put("applyDate", orderBillingConfig.getApplyDate());
			if(ChargeWay.BY_TIME.equals(chargeWay)||ChargeWay.BY_TIME_AND_MILL.equals(chargeWay)){
				periodMap.put("timePriceList", tMap.get("timeChargeItemList"));
				timeChargeItemList.add(periodMap);
				if(flag == orderBillingConfigs.size()){
					map = new HashMap<String, Object>();
					map.put("itemNo", BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO);
					map.put("itemName", "租金");
					map.put("timeChargeItemList", timeChargeItemList);
					mainChargeItemList.add(map);
				}
			} 
			flag++;
		}
		resultMap.put("mainChargeItemList", mainChargeItemList);
		
//		List<DateItem> dateItems = this.transBillingCondition(orderBillingRela);
		//按每24小时来切割 而不是每晚24：00来分割
		List<DateItem> dateItems = this.transBillingConditionByDay(orderBillingRela);
		BigDecimal prepayResult = new BigDecimal("0.00");//总的预收费用
		BigDecimal prepayTimeResult = new BigDecimal("0.00");//主费用项 按时间计费 预收费用 
		BigDecimal prepayMillResult = new BigDecimal("0.00");//主费用项 按里程计费 预收费用 
		BigDecimal mainResult = new BigDecimal("0.00");//主费用项 总的预收费用  暂无用
		BigDecimal depositResult = new BigDecimal("0.00");//押金项预收费用
		BigDecimal attachResult = new BigDecimal("0.00");//附加项预收费用  暂无用
		int i=0;
		String relaNo = null;
		if (orderBillingRela.isSaveLogFlag()){
			relaNo = seqRpcService.getDefNo();
		}
		Map<String,BigDecimal> tempResult = new HashMap<String, BigDecimal>();
		List<Map<String,Object>> attachChargeItemList = new ArrayList<Map<String,Object>>();
		long meters =0;
		long minutes = 0;
		for (DateItem dateItem : dateItems) {
			BigDecimal dailyResult = new BigDecimal("0.00");//每日总费用 每次循环初始化为0
			String recordDate = dateItem.getRecordDate();
			//在订单定价信息扩展里，有选择的附加项
			dateItem.setSelectedAttachChargeItemNos(orderBillingRela.getSelectedAttachItemNos());
			BillingFunc billingFunc = billingFuncMap.get(recordDate);
			//找不到特定当天特别调整的数据，就用默认通用的数据
			if(billingFunc == null){
				billingFunc = billingFuncMap.get(BillingConstants.DEFAULT_BILLING_CONFIG_APPLY_DATE);
			}
			if(i==0){
				//标识为第一天的数据
				dateItem.setFirstDay(true);
				List<Map<String,Object>> depositChargeItemList = new ArrayList<Map<String,Object>>();
				List<DepositChargeItem> depositChargeItems = billingFunc.getDepositChargeItems();
				if(depositChargeItems!=null&&depositChargeItems.size()>0){
					for (DepositChargeItem depositChargeItem : depositChargeItems) {
						depositResult = depositResult.add(new BigDecimal(depositChargeItem.getPrice()));
						dailyResult = dailyResult.add(depositResult);
						map = new HashMap<String, Object>();
						map.put("itemNo", depositChargeItem.getItemNo());
						map.put("itemName", depositChargeItem.getItemName());
						map.put("price", String.format("%.2f", Double.valueOf(depositChargeItem.getPrice())));
						map.put("priceAmt", String.format("%.2f", Double.valueOf(depositChargeItem.getPrice()))+"元/"+depositChargeItem.getPriceUnit().getDesc());
						map.put("buyType", depositChargeItem.getBuyType());
						map.put("buyTypeName", depositChargeItem.getBuyTypeName());
						map.put("remark", depositChargeItem.getRemarks()==null?"":depositChargeItem.getRemarks());
						depositChargeItemList.add(map);
						if(orderBillingRela.isSaveLogFlag()){
							EPrcChargeDetBo detBo = new EPrcChargeDetBo();
							detBo.setItemCode(depositChargeItem.getItemNo());
							detBo.setItemName(depositChargeItem.getItemName());
							detBo.setItemUnits(depositChargeItem.getPriceUnit().getUnitName());
							detBo.setPricingAmt(depositChargeItem.getPrice());
							detBo.setAmount(depositChargeItem.getPriceUnit().getValue()+"");
							detBo.setPrice(depositChargeItem.getPrice());
							detBo.setItemDesc(depositChargeItem.getRemarks());
							detList.add(detBo);
						}
					}
				}
				resultMap.put("depositChargeItemList", depositChargeItemList);
			}
			else{
				dateItem.setFirstDay(false);
			}
			
			//预收计算主收费项目
			MainChargeItem mainChargeItem = billingFunc.getMainChargeItem();
			BigDecimal tempMainResult = billingCalculator.calculate(mainChargeItem, dateItem);
			mainResult = mainResult.add(tempMainResult);
			dailyResult = dailyResult.add(tempMainResult);
			prepayResult = prepayResult.add(tempMainResult);
			if(mainChargeItem.getTimeResult()!=null){
				prepayTimeResult = prepayTimeResult.add(mainChargeItem.getTimeResult());
			}
			if(mainChargeItem.getMillResult()!=null){
				prepayMillResult = prepayMillResult.add(mainChargeItem.getMillResult());
			}
			
			List<AppendChargeItem> newAttachChargeItems = Lists.newArrayList();
			// 附加项目
			// 如果不存在用户订购的附加项，就不需要计算这部分内容了
			List<AppendChargeItem> attachChargeItems = billingFunc
					.getAttachChargeItems();
			if (attachChargeItems != null && attachChargeItems.size() > 0) {
				for (AppendChargeItem attachChargeItem : attachChargeItems) {
					if(i==0){
						map = new HashMap<String, Object>();
						map.put("itemNo", attachChargeItem.getItemNo());
						map.put("itemName", attachChargeItem.getItemName());
						map.put("price", String.format("%.2f", Double.valueOf(attachChargeItem.getPrice())));
						map.put("buyType", attachChargeItem.getBuyType());
						map.put("buyTypeName", attachChargeItem.getBuyTypeName());
						map.put("remark", attachChargeItem.getRemarks()==null?"":attachChargeItem.getRemarks());
						map.put("unit", attachChargeItem.getPriceUnit().getUnit());
						map.put("unitName", attachChargeItem.getPriceUnit().getDesc());
						attachChargeItemList.add(map);
						resultMap.put("attachChargeItemList", attachChargeItemList);
					}
					BigDecimal temp = billingCalculator.calculate(attachChargeItem,
							dateItem);
					if(BuyType.REQUIRED.equals(attachChargeItem.getBuyType())||(StringUtils.isNotBlank(dateItem.getSelectedAttachChargeItemNos())&&dateItem.getSelectedAttachChargeItemNos().indexOf(attachChargeItem.getItemNo()) >= 0)){
						dailyResult = dailyResult.add(temp);
						prepayResult = prepayResult.add(temp);
						newAttachChargeItems.add(attachChargeItem);
					}
					String tempItemNo = attachChargeItem.getItemNo();
					if(tempResult.get(tempItemNo) != null){
						tempResult.put(tempItemNo,tempResult.get(tempItemNo).add(attachChargeItem.getResult()));
					}else{
						tempResult.put(tempItemNo,attachChargeItem.getResult());
					}
				}
			}
			billingFunc.setAttachChargeItems(newAttachChargeItems);
			meters += dateItem.getMeters();
			minutes += dateItem.getMinutes();
			//费用详细信息保存e_billing_order_rela_pricing（isSaveLogFlag字段在下单的时候要传入）
			//日租 如果不按天来分割 再按日来保存费用就没啥意义 因此注释掉
			/*if(orderBillingRela.isSaveLogFlag()){
				billingFunc.setMinutes(dateItem.getMinutes());
				billingFunc.setMeters(dateItem.getMeters());
				BillingOrderPricingBo billingOrderPricingBo = new BillingOrderPricingBo();
				billingOrderPricingBo.setAppNo(orderBillingRela.getAppNo());
				String pricingDetailNo = seqRpcService.getDefNo();
				billingOrderPricingBo.setPricingDetailNo(pricingDetailNo);
				billingOrderPricingBo.setRelaNo(relaNo);
				billingOrderPricingBo.setBillingNo(billingFunc.getBillingNo());
				billingOrderPricingBo.setSubBe(orderBillingRela.getRentType());
				billingOrderPricingBo.setOrgAutoModelKey(orgAutoModelKey);
				billingOrderPricingBo.setVersion(billingFunc.getVersion());
				billingOrderPricingBo.setApplyDate(recordDate);
				billingOrderPricingBo.setPricingAmt(dailyResult);
				billingOrderPricingBo.setDetails(JsonMapper.nonEmptyMapper().toJson(billingFunc));
				billingOrderPricingBo.setDataOperTime(new DateTime().toDate());
				billingOrderPricingBo.setDateOperType("I");
				billingOrderPricingBoMapper.insertSelective(billingOrderPricingBo);
			}*/
			i++;
		}
		//主费用项保存（isSaveLogFlag字段在下单的时候要传入）
		if(orderBillingRela.isSaveLogFlag()){
			BillingOrderRelaBo billingOrderRelaBo = new BillingOrderRelaBo();
			billingOrderRelaBo.setVersionLimit(getNowVersion());
			billingOrderRelaBo.setSubBe(orderBillingRela.getRentType());
			billingOrderRelaBo.setOrgAutoModelKey(orgAutoModelKey);
			billingOrderRelaBo.setRelaNo(relaNo);
			billingOrderRelaBo.setBillType(BillingConstants.BillType.PREPAY);
			billingOrderRelaBo.setAppNo(orderBillingRela.getAppNo());
			billingOrderRelaBo.setSelectedAttachItemNos(orderBillingRela.getSelectedAttachItemNos());
			billingOrderRelaBo.setDefaultBillingNo(defaultBillingNo.toString());
			billingOrderRelaBo.setVersionLimit(billingConfigServiceImpl.getNowVersion());
			billingOrderRelaBo.setDataOperTime(new DateTime().toDate());
			billingOrderRelaBo.setDateOperType("I");
			billingOrderRelaBo.setPricingAmt(prepayResult.add(depositResult));
			billingOrderRelaBoMapper.insertSelective(billingOrderRelaBo);
			List<EPrcChargeBo> list = new ArrayList<EPrcChargeBo>();
			EPrcChargeBo ePrcChargeBo = new EPrcChargeBo();
			ePrcChargeBo.setAppNo(orderBillingRela.getAppNo());
			ePrcChargeBo.setCalcId(relaNo);
			ePrcChargeBo.setLease(minutes+"");
			ePrcChargeBo.setBillingType(BillingConstants.BillType.PREPAY);
			ePrcChargeBo.setPricingAmt(prepayResult.add(depositResult)+"");
			list.add(ePrcChargeBo);
			ePrcChargeService.insertEPrcCharge(list);
		}
		if(mainChargeItemList != null && mainChargeItemList.size()>0){
			for (Map<String, Object> map2 : mainChargeItemList) {
				String price = "";
				String amount = "";
				if(BillingConstants.DEFAULT_MILL_CHARGE_ITEM_NO.equals(map2.get("itemNo"))){
					price = String.format("%.2f", prepayMillResult);
					amount = meters+"";
				}
				if(BillingConstants.DEFAULT_TIME_CHARGE_ITEM_NO.equals(map2.get("itemNo"))){
					price = String.format("%.2f", prepayTimeResult);
					amount = minutes +"";
				}
				map2.put("price", price+"元");
				if(orderBillingRela.isSaveLogFlag()){
					EPrcChargeDetBo detBo = new EPrcChargeDetBo();
					detBo.setItemCode(map2.get("itemNo")+"");
					detBo.setItemName(map2.get("itemName")+"");
					detBo.setPricingAmt(price+"");
					detBo.setAmount(amount);
					detList.add(detBo);
				}
			}
		}
		if(attachChargeItemList!=null&&attachChargeItemList.size()>0){
			for (Map<String, Object> attachChargeItem : attachChargeItemList) {
				attachChargeItem.put("priceAmt", String.format("%.2f", tempResult.get(attachChargeItem.get("itemNo")))+"元");
				if(orderBillingRela.isSaveLogFlag()){
					if(BuyType.REQUIRED.equals(attachChargeItem.get("buyType"))||(StringUtils.isNotBlank(orderBillingRela.getSelectedAttachItemNos())&&orderBillingRela.getSelectedAttachItemNos().indexOf(attachChargeItem.get("itemNo")+"") >= 0)){
						EPrcChargeDetBo detBo = new EPrcChargeDetBo();
						detBo.setItemCode(attachChargeItem.get("itemNo")+"");
						detBo.setItemName(attachChargeItem.get("itemName")+"");
						detBo.setPrice(attachChargeItem.get("price")+"");
						detBo.setPricingAmt(String.format("%.2f", tempResult.get(attachChargeItem.get("itemNo"))));
						detBo.setItemUnits(attachChargeItem.get("unitName")+"");
						if((PriceUnit.DAY+PriceUnit.HOUR+PriceUnit.MINUTE).indexOf(attachChargeItem.get("unit")+"")>-1){
							detBo.setAmount(minutes+"");
						}else if((PriceUnit.KM+PriceUnit.M).indexOf(attachChargeItem.get("unit")+"")>-1){
							detBo.setAmount(meters+"");
						} else {
							detBo.setAmount("1");
						}
						detList.add(detBo);
					}
				}
			}
		}
		if(orderBillingRela.isSaveLogFlag()){
			if(detList!=null&&detList.size()>0){
				for (EPrcChargeDetBo ePrcChargeBo2 : detList) {
					ePrcChargeBo2.setCalcId(relaNo);
				}
				ePrcChargeService.insertEPrcChargeDet(detList);
			}
		}
		resultMap.put("prepayResult", String.format("%.2f", prepayResult));
		resultMap.put("depositResult", String.format("%.2f", depositResult));
		return resultMap;
	}
	
	private Map<String, Object> formatFunc(BillingFunc billingFunc) {
		Map<String, Object> retMap = new HashMap<String, Object>();
		List<Map<String,Object>> timeChargeItemList = new ArrayList<Map<String,Object>>();
		List<Map<String,Object>> millChargeItemList = new ArrayList<Map<String,Object>>();
		if(ChargeWay.BY_TIME.equals(billingFunc.getMainChargeItem().getChargeWay())||ChargeWay.BY_TIME_AND_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())){
			Map<String,Object> timeMap = new HashMap<String, Object>();
			//按时间
			if(ChargeMode.STANDARD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				timeMap = new HashMap<String, Object>();
				timeMap.put("timePeriod", "按时间标准计费");//"00:00-24:00"
				timeMap.put("timePrice", String.format("%.2f", Double.valueOf(billingFunc.getMainChargeItem().getTimeChargeItem().getPrice()))+"元/"
						+ billingFunc.getMainChargeItem().getTimeChargeItem().getPriceUnit().getDesc());
				timeChargeItemList.add(timeMap);
			} else if(ChargeMode.PERIOD.equals(billingFunc.getMainChargeItem().getChargeMode())||ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//分时
				List<RangeChargeItem> rangeChargeItem = billingFunc.getMainChargeItem().getTimeChargeItem().getRangeChargeItems();
				for (RangeChargeItem item : rangeChargeItem) {
					timeMap = new HashMap<String, Object>();
					timeMap.put("timePeriod", item.getRange().getDesc());
					timeMap.put("timePrice", String.format("%.2f", Double.valueOf(item.getPrice()))+"元/"+item.getPriceUnit().getDesc());
					timeChargeItemList.add(timeMap);
				}
			}
			retMap.put("timeChargeItemList", timeChargeItemList);
		}
		if(ChargeWay.BY_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())||ChargeWay.BY_TIME_AND_MILL.equals(billingFunc.getMainChargeItem().getChargeWay())){
			Map<String,Object> millMap = new HashMap<String, Object>();
			//按里程
			if(ChargeMode.STANDARD.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//标准
				millMap = new HashMap<String, Object>();
				millMap.put("millPeriod", "按里程标准计费");
				millMap.put("millPrice", String.format("%.2f", Double.valueOf(billingFunc.getMainChargeItem().getMillChargeItem().getPrice()))+"元/"
						+ billingFunc.getMainChargeItem().getMillChargeItem().getPriceUnit().getDesc());
				millChargeItemList.add(millMap);
			} else if(ChargeMode.PERIOD.equals(billingFunc.getMainChargeItem().getChargeMode())||ChargeMode.STEP.equals(billingFunc.getMainChargeItem().getChargeMode())){
				//分时
				List<RangeChargeItem> rangeChargeItem = billingFunc.getMainChargeItem().getMillChargeItem().getRangeChargeItems();
				for (RangeChargeItem item : rangeChargeItem) {
					//标准
					millMap = new HashMap<String, Object>();
					millMap.put("millPeriod", item.getRange().getDesc()+billingFunc.getMainChargeItem().getMillChargeItem().getPriceUnit().getUnitName());
					millMap.put("millPrice", String.format("%.2f", Double.valueOf(item.getPrice()))+"元/"+item.getPriceUnit().getDesc());
					millChargeItemList.add(millMap);
				}
			}
			retMap.put("millChargeItemList", millChargeItemList);
		}
		
		return retMap;
	}
	/**
	 * 通过订单编号appNo和计费类型billingType获取订单总费用和明细
	 */
	@Override
	public Map<String, Object> getOrderPrcCharge(Map<String, String> paraMap){
		Map<String, Object> returnMap=new HashMap<String, Object>();
		returnMap.put("prcChargeList", ePrcChargeService.getPrcCharge(paraMap));
		returnMap.put("prcChargeDetList", ePrcChargeService.getPrcChargeDet(paraMap));
		return returnMap;
	}


}
