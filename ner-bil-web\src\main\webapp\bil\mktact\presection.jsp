<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="段落管理" />
<script type="text/javascript" src="../comm/validate/validation.js"></script>
<ls:body>
	<ls:form id="sectionForm" name="sectionForm">
		<ls:text name="actId" property="actId" type="hidden" />
		<ls:text name="actSubType" property="actSubType" type="hidden" />
		<ls:text name="release" property="release" type="hidden" />
		<table>
			<tr>
				<td><img src="<%=request.getContextPath() %>/bil/mktact/img/step2.png" alt=""></td>
			</tr>
		</table>
		<table align="center" class="tab_search">
			<tr>
				<td><ls:grid url="" name="sectionGrid" height="80px;" width="100%" 
						showCheckBox="false" singleSelect="true" caption="段落列表" primaryKey="presentSectionId">
						<ls:gridToolbar name="sectionGridBar">
							<ls:gridToolbarItem name="addBtn" imageKey="add" onclick="doAdd" text="创建" />
							<ls:gridToolbarItem name="deleteBtn" imageKey="delete" onclick="doDel" text="删除" />
						</ls:gridToolbar>
						<ls:column caption="" name="presentSectionId" hidden="true" />
						<ls:column caption="段落名称" name="sectionName" />
						<ls:column caption="段落关系" name="sectionRelaTypeName" />
						<ls:column caption="段落类型" name="sectionTypeName" />
						<ls:column caption="赠送余额类型" name="presentBalTypeName" />
						<ls:column caption="赠送最大值" name="maxValue" />
						<ls:column caption="赠送周期类型" name="presentCycleTypeName" />
					</ls:grid></td>
			</tr>
		</table>
	</ls:form>
	<table>
		<tr>
			<td>
				<div class="pull-right">
					<ls:button text="上一步" onclick="doUpStep" />
					<ls:button text="下一步" onclick="doRelease" />
				</div>
			</td>
		</tr>
	</table>
	<ls:script>
	
	window.doSearch=doSearch;
    doSearch();
	function doSearch(){
		var actIdValue = actId.getValue();
		var actSubTypeValue = actSubType.getValue();
		sectionGrid.query('~/sections?actId='+actIdValue + "&presentBalType=" + actSubTypeValue, null, function(){
			var items = sectionGrid.getItems();
			if (items.length==0) 
				sectionGrid.toolbarItems.addBtn.setEnabled(true);
			else
				sectionGrid.toolbarItems.addBtn.setEnabled(false);
		});
	}
	
    function doAdd(){
		var actIdValue = actId.getValue();
		LS.dialog("~/sections/edit?actId="+actIdValue,"创建段落", 800, 525, true, null);
    }
    
 	function doDel(){
		var item = sectionGrid.getSelectedItem();
    	if(LS.isEmpty(item)){
	   		LS.message("error","请选择一条记录！");
    		return;
	   	}
		LS.confirm('确定要删除该规则？',function(result){
			if(result){
				LS.ajax("~/sections/delete?presentSectionId="+item.presentSectionId, null, function(e){
					if(e.items[0]=="T"){
						LS.message("info","删除成功！");
						doSearch();
						return;
					}else{
						LS.message("info","操作失败或网络异常，请重试！");
						return;
					}
				});
			}
		});	
    }
    
    function doUpStep() {
		var actIdValue = actId.getValue();
		window.location.href="<%=request.getContextPath() %>/marketacts/edit?actId="+actIdValue;
	}
	
    function doRelease() {
    	var items = sectionGrid.getItems();
		if (items.length==0) {
			LS.message("info","请先完成段落规则维护！");
			return;
		}
      	release.setValue("true");
		sectionForm.submit("~/marketacts/saving",function(e){
        	if(e.items[0][0]=="T"){
	          	LS.parent().doSearch();
				var actIdValue = actId.getValue();
				<%--setTimeout("LS.window.close()",200);--%>
				window.location.href="<%=request.getContextPath() %>/marketacts/actInfo?actId="+actIdValue+"&actType=";
        	}
        	else {
          		LS.message("info", "操作失败或网络异常，请重试！");
        	}
		});
	}
	
    </ls:script>
</ls:body>
</html>