/**
 *
 * @(#) IBillingAdjustService.java
 * @Package com.ls.ner.billing.api.config.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.config.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import com.ls.ner.billing.config.bo.BillingConfigFunc;
import com.ls.ner.billing.config.bo.BillingConfigQueryCondition;
import com.ls.ner.billing.config.bo.CalendarDataVo;

/**
 *  类描述：车型调价service接口
 * 
 *  @author:  lipf
 *  @version  $Id: Exp$ 
 *
 *  History:  2016年4月10日 下午2:02:45   lipf   Created.
 *           
 */
public interface IBillingAdjustService {

	/**
	 * 
	 * 方法说明：车型调价表格数据查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:04:00
	 * History:  2016年4月10日 下午2:04:00   lipf   Created.
	 *
	 * @param condition
	 * @return
	 * @throws InvocationTargetException 
	 * @throws IllegalAccessException 
	 *
	 */
	public List<BillingConfigFunc> getBillingAdjustList(BillingConfigQueryCondition condition) throws IllegalAccessException, InvocationTargetException;
	
	/**
	 * 
	 * 方法说明：车型调价表格数据记录数查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月10日 下午2:04:23
	 * History:  2016年4月10日 下午2:04:23   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public int getBillingAdjustListCount(BillingConfigQueryCondition condition);

	/**
	 * 
	 * 方法说明：车辆调价日历数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年4月11日 上午8:56:20
	 * History:  2016年4月11日 上午8:56:20   lipf   Created.
	 *
	 * @param condition
	 * @return
	 *
	 */
	public List<CalendarDataVo> getBillingAdjustCalendar(BillingConfigQueryCondition condition);

}
