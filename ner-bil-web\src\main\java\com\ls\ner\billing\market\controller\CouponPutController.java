package com.ls.ner.billing.market.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.ls.ner.base.cache.RedisCluster;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.constants.RedisKeyConstants;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.market.service.ICouponRPCService;
import com.ls.ner.billing.market.bo.*;
import com.ls.ner.billing.market.service.ICouponPutService;
import com.ls.ner.billing.market.vo.MarketCondition;
import com.ls.ner.cust.api.service.ICustCenterRpcService;
import com.ls.ner.cust.api.service.ICustXpRpcService;
import com.ls.ner.util.ListMapUtil;
import com.ls.ner.util.StringUtil;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.exception.BusinessWarning;
import com.pt.poseidon.common.utils.json.JsonUtil;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import net.sf.json.JSONArray;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券发放
 * description
 * 创建时间 2016年5月31日下午2:27:48
 * 创建人 biaoxiangd
 */
@Controller
@RequestMapping("/billing/couponPut")
public class CouponPutController extends QueryController<MarketCondition> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CouponPutController.class);

    @ServiceAutowired(serviceTypes = ServiceType.LOCAL, value = "bilCouponPutService")
    private ICouponPutService couponPutService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC)
    private Authentication authentication;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "custCenterRpcService")
    private ICustCenterRpcService custCenterRpcService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "couponRPCService")
    private ICouponRPCService couponRPCService; // RPC05-11 优惠券RPC接口 【测试用】

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "custXpRpcService")
    private ICustXpRpcService custXpRpcService;


    private RedisCluster redisCluster = RedisCluster.getInstance();
    /**
     * 描述: 优惠券发放
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:39
     */
    @RequestMapping("/couponPut")
    public String couponPut(CouponBo bo, Model m, HttpServletRequest request) {
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        if (cpnTypeList != null && cpnTypeList.size() > 0) {
            m.addAttribute("cpnTypeList", cpnTypeList);
        }
        List<CodeBO> busiTypeList = codeService.getStandardCodes("orderType", null);
        if (busiTypeList != null && busiTypeList.size() > 0) {
            m.addAttribute("busiTypeList", busiTypeList);
        }
//        bo.setBusiType("01");
//        bo.setCpnType("01");
        m.addAttribute("couponManageForm", bo);
        return "/bil/market/couponPut/couponPut";
    }

    /**
     * 描述:
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:39
     */
    private void init(Model m, String chargeType) {
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        List<CodeBO> newTypeList = new ArrayList<CodeBO>();
        for (int i = 0; i < cpnTypeList.size(); i++) {
            CodeBO bo = cpnTypeList.get(i);
            if (bo.getCodeValue().startsWith(chargeType)) {
                newTypeList.add(bo);
            }
        }
        m.addAttribute("chargeType", chargeType);
        m.addAttribute("cpnTypeList", newTypeList);
    }

    /**
     * 描述:点击发放数量-展现发放明细
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 11:43
     */
    @RequestMapping("/showPutDetail")
    public String showPutDetail(Model m, HttpServletRequest request) {

        String cpnId = request.getParameter("cpnId");
        if (StringUtil.isBlank(cpnId)) {
            m.addAttribute("couponPutForm", new CouponBo());
        } else {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            CouponBo bo = couponPutService.getCoupon(condition);
            if (bo != null) {
                String cpnStatus = bo.getCpnStatus();
                if (StringUtil.isNotBlank(cpnStatus)) {
                    CodeBO codeBo = codeService.getStandardCode("cpnState", cpnStatus, null);
                    if (codeBo != null) {
                        bo.setCpnStatusName(codeBo.getCodeName());
                    }
                }
                String cpnType = bo.getCpnType();
                if(StringUtil.isNotBlank(cpnType)){
                    CodeBO codeBo = codeService.getStandardCode("cpnType", cpnType, null);
                    if (codeBo != null) {
                        bo.setCpnTypeName(codeBo.getCodeName());
                    }
                }
                String busiType = bo.getBusiType();
                if(StringUtil.isNotBlank(busiType)){
                    CodeBO codeBo = codeService.getStandardCode("orderType", busiType, null);
                    if (codeBo != null) {
                        bo.setBusiTypeName(codeBo.getCodeName());
                    }
                }
            }

            m.addAttribute("couponPutForm", bo);
        }
        return "/bil/market/couponPut/putDet";
    }

    /**
     * 选择客户
     * description
     * 创建时间 2016年6月3日下午5:11:20
     * 创建人 lise
     */
    @RequestMapping("/choiceCust")
    public String choiceCust(Model m, HttpServletRequest request) {
        List<CodeBO> custSortCodeList = codeService.getStandardCodes(BizConstants.CustType.CUST_SORTCODE, null);
        m.addAttribute("custSortCodeList", custSortCodeList);
//		m.addAttribute("custForm", new CustBo());
        return "/bil/market/couponPut/choiceCust";
    }


    /**
     * 描述:编辑优惠券发放
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 11:55
     */
    @RequestMapping("/editCouponPut")
    public String editCouponPut(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        if (StringUtil.isBlank(cpnId)) {
            m.addAttribute("couponPutForm", new CouponBo());
        } else {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            CouponBo bo = couponPutService.getCouponDetail(condition);
            m.addAttribute("couponPutForm", bo);
        }
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        m.addAttribute("cstPath", PublicConstants.ApplicationPath.getCstPath());

        return "/bil/market/couponPut/editCouponPut";
    }
    @RequestMapping(value = "/queryCouponDelay",method = RequestMethod.POST)
    @ResponseBody
    public WrappedResult queryCouponDelay(@RequestParam String cpnId){
          try {
              return WrappedResult.successWrapedResult(couponPutService.qureyCouponDelayInfo(cpnId));
          }catch (Exception e) {
              return WrappedResult.failedWrappedResult(e.getMessage());
          }
    }


    /**
     * 描述: 导入界面
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-04 10:58
     */
    @RequestMapping("/importFile")
    public String importFile(Model m, HttpServletRequest request) {
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        return "/bil/market/couponPut/importFile";
    }

    /**
     * 描述:导入按钮
     *
     * @param: [m, request]
     * @return: com.pt.poseidon.webcommon.rest.object.WrappedResult
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-04 11:03
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/saveFile", method = RequestMethod.POST)
    public @ResponseBody
    WrappedResult saveCoupon(
            Model m, MultipartHttpServletRequest request) throws IOException {
        List<Map> custList = couponPutService.saveFile(request);
        return WrappedResult.successWrapedResult(custList);
    }

    /**
     * 描述:保存发放信息
     *
     * @param: [dataParams, m]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-05 0:44
     */
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/savePutInfo", method = RequestMethod.POST)
    public @ItemResponseBody
    //与delayPut写表串行阻塞执行，保证e_coupon_put.isDelay字段的读和写互斥
    @Transactional
    QueryResultObject savePutInfo(@RequestBody Map<String, Object> dataParams, Model m) {
        String mark = "success";
        String redisLockKey="DelayPutCpnIdLock:";
        CouponPutBo bo= new CouponPutBo();
        boolean isLock=false;
        try {
            List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
            for (Map<String, Object> map : saveList) {
                BeanUtil.copyProperties(map, bo);
            }
            LOGGER.info("savePutInfo请求对象{}"+JSON.toJSONString(bo));;
            AccountBo userBo = authentication.getCurrentAccount();
            bo.setPutEmp(userBo != null ? userBo.getAccountName() : "SYSADMIN");
            if (StringUtil.isBlank(bo.getCpnId())){
                throw new BusinessWarning("cpnId不能为空",this.getClass());
            }
            if ("1".equals(bo.getIsDelay().toString())){

                //同一个优惠卷定时消费上锁
                Long setNx = redisCluster.setNx(redisLockKey + bo.getCpnId(), null);
                isLock=true;
                redisCluster.expire(redisLockKey + bo.getCpnId(),60);
                if (setNx!= 1L){
                    throw new BusinessWarning("优惠券定时发放已被占用,请稍后重试",this.getClass());
                }
                DelayInfo delayInfo = couponPutService.qureyCouponDelayInfo(bo.getCpnId());
                if (delayInfo!= null&&delayInfo.getPutId()!=null){
                    throw new BusinessWarning("优惠券定时发放已被请取消预约后再试",this.getClass());
                }

            }

            //存储优惠券发放日志(含Coupon参数检查逻辑)
            couponPutService.savePutCouponPut(bo);
            //若需定时的交给定时任务
            if (!"1".equals(bo.getIsDelay().toString())){
                if (BooleanUtil.isTrue(bo.getIsAllCust())){
                    //补充逻辑
                    couponPutService.excuteCouponPutAllCust(bo);
                    mark = "aysncAllUser";
                }else {
                    //执行优惠卷发放
                    Map<String, String> result= couponPutService.excuteCouponPut(bo, false);
                   LOGGER.info("优惠券发放结果：{}"+JSON.toJSONString(result));
                    mark = "success";
                }
            }else {
              //存储客户列表
                if (!bo.getIsAllCust()){
                    JSONArray jsonArray = JSONArray.fromObject(bo.getCustInfoList());
                    List<Map<String, Object>> custList = (List<Map<String, Object>>) JSONArray.toCollection(jsonArray, Map.class);
                    couponPutService.saveCouponPutDetList(custList,bo.getPutId(),bo.getPutTime());
                }
            }

        //    }
//            mark = retMap.get("outMsg");
        } catch (BusinessWarning e) {
            mark = e.getKey();
           LOGGER.error("优惠券发放业务异常：{}",ExceptionUtil.stacktraceToString(e));
        } catch (Exception e) {
            mark = "error";
            LOGGER.error("优惠券系统异常：{}",ExceptionUtil.stacktraceToString(e));
        }finally {
            //释放锁
            if (isLock){
                redisCluster.remove(redisLockKey + bo.getCpnId());
            }
        }
        return RestUtils.wrappQueryResult(mark);
    }




    @RequestMapping(value = "/cancelDelay", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject cancelDelay(@RequestParam String putId){
        LOGGER.info("取消预约优惠券ID：{}",putId);
      //  是否预约 1-预约 0-不预约 2-预约已完成 3-预约已取消
        boolean flag= couponPutService.updateDelayByPutId(putId,3,1);;

        return RestUtils.wrappQueryResult(flag?"success":"fail");
    }

    /**
     * 描述:优惠券发放历史记录
     *
     * @param: [params]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-09 14:22
     */
    @RequestMapping("/getCouponPutLog")
    public @ItemResponseBody
    QueryResultObject getCouponPutLog(@QueryRequestParam("params")
                                              RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        List<CouponPutBo> dataList = couponPutService.getCouponPutLog(condition);
        int count = couponPutService.getCouponPutLogCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }


    /**
     * 描述:查询优惠券发放记录
     *
     * @param: [params]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 10:48
     */
    @RequestMapping("/getCouponPut")
    public @ItemResponseBody
    QueryResultObject getCouponPut(@QueryRequestParam("params")
                                           RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        condition.setCpnStatus("1");
        List<CouponBo> dataList = couponPutService.getCouponPut(condition);
        int count = couponPutService.getCouponPutCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    /**
     * 查询优惠券发放客户列表
     * description
     * 创建时间 2016年6月4日下午5:39:31
     * 创建人 lise
     */
    @RequestMapping("/getPutCustList")
    public @ItemResponseBody
    QueryResultObject getPutCustList(@QueryRequestParam("params")
                                             RequestCondition params) {
        MarketCondition condition = this.rCondition2QCondition(params);
        List<CouponPutBo> dataList = couponPutService.getCouponPutDetLog(condition);
        int count = couponPutService.getCouponPutLogDetCount(condition);
        return RestUtils.wrappQueryResult(dataList, count);
    }

    @Override
    protected MarketCondition initCondition() {
        return new MarketCondition();
    }

    @SuppressWarnings("deprecation")
    @RequestMapping("/downExcel")
    public void downExcel(
            HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        String[] headers = {"手机号码"};
        // 声明一个工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 生成一个表格
        HSSFSheet sheet = workbook.createSheet();
        sheet.setColumnWidth(5, 220 * 15);
        sheet.setColumnWidth(6, 200 * 15);
        sheet.setColumnWidth(7, 200 * 15);
        sheet.setColumnWidth(8, 320 * 15);
        sheet.setColumnWidth(12, 370 * 15);
        sheet.setColumnWidth(13, 370 * 15);


        // 生成标题行样式
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment((HSSFCellStyle.ALIGN_CENTER));
        cellStyle.setVerticalAlignment((HSSFCellStyle.VERTICAL_CENTER));
        //=======创建单元格样式:结束===========

        // 产生表格标题行
        HSSFRow row = sheet.createRow(0);
        for (short i = 0; i < headers.length; i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(headers[i]);
        }
        String fileName = "手机号导入模板.xls";
        response.setHeader("Cache-Control", "max-age=0");
        // 设置文件头编码方式和文件名
        response.addHeader("Content-Disposition", "attachment;filename="
                + new String(fileName.getBytes("gb2312"), "iso8859-1"));
        response.setContentType("application/vnd.ms-excel");
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        out.flush();
        out.close();
    }

    /**
     * 描述: 通过手机号码查询客户信息
     *
     * @param: [dataParams, m]
     * @return: com.pt.poseidon.webcommon.rest.object.QueryResultObject
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-03 17:22
     */
    @RequestMapping(value = "/queryMobiByCust")
    public @ItemResponseBody
    QueryResultObject queryMobiByCust(@RequestBody Map<String, Object> dataParams, Model m) {
        Map<String, Object> custInfoMap = new HashMap<String, Object>();
        custInfoMap.put("outCode", "-1");
        custInfoMap.put("outMsg", "error");
        custInfoMap = couponPutService.queryMobiByCust(dataParams);
        if (custInfoMap != null && custInfoMap.size() > 0) {
            custInfoMap.put("outCode", "0");
            custInfoMap.put("outMsg", "ok");
        }
        return RestUtils.wrappQueryResult(custInfoMap);
    }


    /**
     * 描述:展现发放方式界面
     *
     * @param: [m, request]
     * @return: java.lang.String
     * 创建人:biaoxiangd
     * 创建时间: 2017-06-04 15:50
     */
    @RequestMapping("/showSms")
    public String showSms(Model m, HttpServletRequest request) {
        CouponPutBo bo = new CouponPutBo();
        bo.setNoticeType("01");
        bo.setNoticeType("01");
        List<Map<String, Object>> noticeTypeList = ListMapUtil.toCheckListMap(codeService.getStandardCodes("noticeType", null));
        m.addAttribute("noticeTypeList", noticeTypeList);
        m.addAttribute("smsForm", bo);
        return "/bil/market/couponPut/smsInit";
    }

    /**
     * 描述:RPC05-11-03 优惠券优惠计算【测试】
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-07 10:03
     */
    @RequestMapping(value = "/testCouponRpc03")
    public @ResponseBody
    Object testCouponRpc03() throws Exception {
        Map<String, Object> retMap = new HashMap<String, Object>();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderNo", "1");
        map.put("cpnId", "4");
        return couponRPCService.couponCalc(map);
    }

    /**
     * 描述:RPC05-11-01 优惠券详情【测试】
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-07 10:03
     */
    @RequestMapping(value = "/testCouponRpc01")
    public @ResponseBody
    Object testCouponRpc01() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        List<String> list = new ArrayList<String>();
        list.add("4");
        list.add("6");
        list.add("7");
        map.put("cpnList", list);
        return couponRPCService.couponInfo(map);
    }

    /**
     * 描述:RPC05-11-02 验证优惠券可用性【测试】
     *
     * @param:
     * @return: 创建人:biaoxiangd
     * 创建时间: 2017-06-07 10:03
     */
    @RequestMapping(value = "/testCouponRpc02")
    public @ResponseBody
    Object testCouponRpc02() throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        List<CouponContentBo> list = new ArrayList<CouponContentBo>();
        CouponContentBo bo = new CouponContentBo();
        bo.setCpnId("15");
        list.add(bo);
        map.put("cpnList", list);
        map.put("billAmt", "1");
        return couponRPCService.couponValid(map);

    }

    //======================================================小鹏优惠券发放==============================================
    /**
     * @param bo
     * @param m
     * @param request
     * @description 小鹏优惠券发放首页
     * <AUTHOR>
     * @create 2018-06-22 14:46:14
     */
    @RequestMapping("/xp/couponPut")
    public String xpCouponPut(CouponBo bo, Model m, HttpServletRequest request) {
        List<CodeBO> cpnTypeList = codeService.getStandardCodes("cpnType", null);
        if (cpnTypeList != null && cpnTypeList.size() > 0) {
            m.addAttribute("cpnTypeList", cpnTypeList);
        }
        bo.setBusiType("02");//小鹏优惠券发放只展示充电的优惠券
        m.addAttribute("xpCouponManageForm", bo);
        return "/bil/market/xpCouponPut/xpCouponPut";
    }
    /**
     * @param m
     * @param request
     * @description 发放按钮跳转事件
     * <AUTHOR>
     * @create 2018-06-22 15:22:00
     */
    @RequestMapping("/xp/editCouponPut")
    public String xpEditCouponPut(Model m, HttpServletRequest request) {
        String cpnId = request.getParameter("cpnId");
        if (StringUtil.isBlank(cpnId)) {
            m.addAttribute("couponPutForm", new CouponBo());
        } else {
            MarketCondition condition = new MarketCondition();
            condition.setCpnId(cpnId);
            CouponBo bo = couponPutService.getCoupon(condition);
            if (bo != null) {
                CodeBO codeBO = new CodeBO();
                // 优惠券类型
                String cpnType = bo.getCpnType();
                if (StringUtil.isNotBlank(cpnType)) {
                    codeBO = codeService.getStandardCode("cpnType", cpnType, null);
                    if (codeBO != null) {
                        bo.setCpnTypeName(codeBO.getCodeName());
                    }
                }
                // 优惠券状态
                String cpnStatus = bo.getCpnStatus();
                if (StringUtil.isNotBlank(cpnStatus)) {
                    codeBO = codeService.getStandardCode("cpnState", cpnStatus, null);
                    if (codeBO != null) {
                        bo.setCpnStatusName(codeBO.getCodeName());
                    }
                }
                // 优惠券内容
                String busiType = bo.getBusiType();
                if (StringUtil.isNotBlank(busiType)) {
                    codeBO = codeService.getStandardCode("orderType", busiType, null);
                    if (codeBO != null) {
                        bo.setBusiTypeName(codeBO.getCodeName());
                    }
                }
            }
            m.addAttribute("xpCouponEditForm", bo);
        }
        m.addAttribute("pubPath", PublicConstants.ApplicationPath.getPubPath());
        return "/bil/market/xpCouponPut/xpEditCouponPut";
    }
    /**
     * @param m
     * @param request
     * @description 选择客户事件
     * <AUTHOR>
     * @create 2018-06-22 17:21:25
     */
    @RequestMapping("/xp/custSelect")
    public String xpCustSelect(Model m, HttpServletRequest request) {
        return "/bil/market/xpCouponPut/xpCpnSelectCust";
    }
    /**
     * @param params
     * @description 客户表格查询
     * <AUTHOR>
     * @create 2018-06-22 17:58:09
     */
    @RequestMapping("/xp/getCustInfo")
    public @ItemResponseBody QueryResultObject queryCityInfo(@QueryRequestParam("params") RequestCondition params) {
        List<Map<String,Object>> custList  = new ArrayList<>();
        try {
            MarketCondition condition = this.rCondition2QCondition(params);
            LOGGER.debug("======查询小鹏客户信息====="+ JsonUtil.obj2Json(condition));
            Map<String,Object> inMap = new HashMap<String, Object>();
            Map<String,Object> resultMap = new HashMap<String, Object>();
            if("01".equals(condition.getCustSearchCondition())){//通过手机号查询
                inMap.put("mobile",condition.getCustSearchResult());
                resultMap = custXpRpcService.getUserInfoByMobile(inMap);
            }else if("02".equals(condition.getCustSearchCondition())){//通过VIN查询
                inMap.put("vin",condition.getCustSearchResult());
                resultMap = custXpRpcService.getUserInfoByVin(inMap);
            }else if("03".equals(condition.getCustSearchCondition())){//通过UID查询
                inMap.put("uid",condition.getCustSearchResult());
                resultMap = custXpRpcService.getUserInfoByUid(inMap);
            }
            LOGGER.debug("查询小鹏客户信息出参：" + resultMap);
            if(StringUtil.isNotEmpty(resultMap)&&resultMap.size()>0){
                String custType = StringUtil.getString(resultMap.get("custType"));
                if(StringUtil.isNotBlank(custType)){
                    CodeBO bo = codeService.getStandardCode("custProperty",custType,null);
                    if(bo!=null){
                        resultMap.put("custTypeName",bo.getCodeName());
                    }
                }
                custList.add(resultMap);
            }
        }catch (Exception e){
            LOGGER.error("======查询小鹏客户信息异常====="+ e.getMessage());
        }
        return RestUtils.wrappQueryResult(custList);
    }
    /**
     * @param dataParams
     * @param m
     * @description 小鹏优惠券发放
     * <AUTHOR>
     * @create 2018-06-25 15:55:19
     */
    @RequestMapping(value = "/xp/savePutInfo", method = RequestMethod.POST)
    public @ItemResponseBody QueryResultObject xpSavePutInfo(@RequestBody Map<String, Object> dataParams, Model m) {
        String mark = "success";
        try {
            AccountBo userBo = authentication.getCurrentAccount();
            List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
            LOGGER.debug("小鹏——发放优惠券入参：" + JsonUtil.obj2Json(saveList));
            CouponPutBo bo = new CouponPutBo();
            for (Map<String, Object> map : saveList) {
                BeanUtils.populate(bo, map);
            }
            bo.setPutEmp(userBo != null ? userBo.getAccountName() : "SYSADMIN");
            couponPutService.xpSavePutInfo(bo);
        } catch (BusinessWarning e) {
            mark = e.getKey();
        } catch (Exception e) {
            mark = "error";
        }
        return RestUtils.wrappQueryResult(mark);
    }
}
