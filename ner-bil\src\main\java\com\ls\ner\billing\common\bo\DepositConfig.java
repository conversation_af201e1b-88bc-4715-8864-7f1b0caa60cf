package com.ls.ner.billing.common.bo;

import java.math.BigDecimal;
import java.util.Date;

public class DepositConfig {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Long systemId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String billingNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.DEPOSIT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String depositNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String itemNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.CHARGE_METHOD
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String chargeMethod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private BigDecimal price;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private Date dataOperTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column e_deposit_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    private String dataOperType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.SYSTEM_ID
     *
     * @return the value of e_deposit_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Long getSystemId() {
        return systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.SYSTEM_ID
     *
     * @param systemId the value for e_deposit_config.SYSTEM_ID
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.BILLING_NO
     *
     * @return the value of e_deposit_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getBillingNo() {
        return billingNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.BILLING_NO
     *
     * @param billingNo the value for e_deposit_config.BILLING_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setBillingNo(String billingNo) {
        this.billingNo = billingNo == null ? null : billingNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.DEPOSIT_NO
     *
     * @return the value of e_deposit_config.DEPOSIT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDepositNo() {
        return depositNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.DEPOSIT_NO
     *
     * @param depositNo the value for e_deposit_config.DEPOSIT_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDepositNo(String depositNo) {
        this.depositNo = depositNo == null ? null : depositNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.ITEM_NAME
     *
     * @return the value of e_deposit_config.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemName() {
        return itemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.ITEM_NAME
     *
     * @param itemName the value for e_deposit_config.ITEM_NAME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.ITEM_NO
     *
     * @return the value of e_deposit_config.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.ITEM_NO
     *
     * @param itemNo the value for e_deposit_config.ITEM_NO
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.CHARGE_METHOD
     *
     * @return the value of e_deposit_config.CHARGE_METHOD
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getChargeMethod() {
        return chargeMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.CHARGE_METHOD
     *
     * @param chargeMethod the value for e_deposit_config.CHARGE_METHOD
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setChargeMethod(String chargeMethod) {
        this.chargeMethod = chargeMethod == null ? null : chargeMethod.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.PRICE
     *
     * @return the value of e_deposit_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.PRICE
     *
     * @param price the value for e_deposit_config.PRICE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.DATA_OPER_TIME
     *
     * @return the value of e_deposit_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public Date getDataOperTime() {
        return dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.DATA_OPER_TIME
     *
     * @param dataOperTime the value for e_deposit_config.DATA_OPER_TIME
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperTime(Date dataOperTime) {
        this.dataOperTime = dataOperTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column e_deposit_config.DATA_OPER_TYPE
     *
     * @return the value of e_deposit_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public String getDataOperType() {
        return dataOperType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column e_deposit_config.DATA_OPER_TYPE
     *
     * @param dataOperType the value for e_deposit_config.DATA_OPER_TYPE
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    public void setDataOperType(String dataOperType) {
        this.dataOperType = dataOperType == null ? null : dataOperType.trim();
    }
}