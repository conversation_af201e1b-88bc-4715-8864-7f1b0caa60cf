<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="其他服务项目费用配置" />
<ls:body>
	<table align="center" class="tab_search">
		<tr>
			<td><ls:grid url="" name="appendChargeItemGrid" height="230px" width="100%"
					showCheckBox="false">
					<ls:column name="itemNo" caption="" hidden="true" />	
					<ls:column name="sn" caption="排序" hidden="false" />				
					<ls:column name="itemName" caption="费用项名称" />
					<ls:column name="itemUnit" caption="费用项单位" hidden="true" />
					<ls:column name="itemUnitName" caption="费用项单位" />
					<ls:column name="buyTypeName" caption="是否必购" />
          <ls:column name="remarks" caption="费用描述" hidden="false" />
          <ls:column name="itemStatusName" caption="状态" hidden="false" />
				</ls:grid></td>
		</tr>
	</table>

	<ls:form id="appendChargeItemForm" name="appendChargeItemForm">
		<table class="tab_search">
			<ls:text name="itemNo" property="itemNo" type="hidden"></ls:text>
			<tr>

				<td><ls:label text="费用项名称" ref="itemName" /></td>
				<td><ls:text name="itemName" property="itemName"
						required="true"  maxlength="100" ></ls:text></td>
				<td><ls:label text="费用项单位" ref="itemUnit" /></td>
				<td>
					<ls:select name="itemUnit" property="itemUnit" required="true">
						<ls:options property="billingPriceUnitList" scope="request" text="codeName" value="codeValue"/>
					</ls:select>				
				</td>
				<td><ls:label text="排序" ref="sn" /></td>
				<td><ls:text  type="number" name="sn" property="sn" required="true" min="0" max="9999"></ls:text></td>
			</tr>
			<tr>
			 <td><ls:label text="是否必购" ref="buyType" /></td>
        <td>
          <ls:select name="buyType" property="buyType" required="true">
            <ls:options property="billingAppendItemBuyTypeList" scope="request" text="codeName" value="codeValue"/>
          </ls:select>  
        </td>
        <td><ls:label text="费用描述"/></td>
        <td>
          <ls:text name="remarks" property="remarks"></ls:text>
        </td>
			</tr>

		</table>
		<table>
			<tr>
				<td>
					<div class="pull-right">
						<ls:button text="清空" onclick="doAdd" />
<%-- 						<ls:button text="清除" onclick="doClear" /> --%>
						<ls:button text="保存" onclick="doSave" />
						<%-- <ls:button text="删除" onclick="doDel" name="delBtn"/> --%>
						<ls:button text="启用" onclick="doStart" name="startBtn"/>
            <ls:button text="停用" onclick="doStop" name="stopBtn"/>
					</div>
				</td>
			</tr>
		</table>
	</ls:form>

	<ls:script>
	var baseUrl = "~/billing/appendchargeitem/attachs";
    query();
    function query(){
    
      appendChargeItemGrid.query(baseUrl);
    }
    function doAdd(){  
      doClear();
      itemNo.setValue();
    }
    function doClear(){
      appendChargeItemForm.clear();
    };
    function doSave(){
	    var url = baseUrl;
	     url += '/post';
	    var itemNoValue = itemNo.getValue();
	    if(!!itemNoValue){
	    	url += "/" + itemNoValue;
	    }
	   
      appendChargeItemForm.submit(url, {valid : true},function(result){
     		query();
     		if(!!itemNoValue){
	        LS.message("info","更新成功");
	      }else{
	        LS.message("info","保存成功");
	      }
	      doAdd();
      });
    }
    function doDel(){      
      var item = appendChargeItemGrid.getSelectedItem();
      if(item==null){
      	 LS.message("info","请选择一条记录!");
      	 return;
      }
      if(item.itemStatus=="1"){
         LS.message("info","已经启用的项目不能删除!");
         return;
        }
        LS.confirm('确认删除吗?', function(data) {
        if (data) {
          var url = baseUrl +"/delete/"+item.itemNo;
          LS.ajax(url,{},function(result){
          	 query();
          	 doAdd();
             LS.message("info","删除成功");
            });
          }
        });
      
    }
    function doStart(){
      var items = appendChargeItemGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var itemNos = [];
      for(var i=0;i < items.length;i++){
        if(items[i].itemStatus=="1"){
          LS.message('info','选中的记录包含已经启用的费用项，请重新选择');
          return;
        }else{
          itemNos.push(items[i].itemNo);
        }
      }
      LS.confirm('确认启用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = itemNos;
              LS.ajax("~/billing/appendchargeitem/start", params, function(result) {
                LS.message("info","启用成功");
                query();
              });
          }
      });
    }
    function doStop(){      
      var items = appendChargeItemGrid.getCheckedItems();
      if(items.length==0){
        LS.message("info","请选择一条记录");
        return;
      }
      var itemNos = [];
      for(var i=0;i < items.length;i++){
        if(items[i].itemStatus!="1"){
          LS.message('info','选中的记录不是启用状态，请重新选择');
          return;
        }else{
          itemNos.push(items[i].itemNo);
        }
      }
      LS.confirm('确认停用吗?', function(data) {
          if (data) {
              var params = {};
              params.ids = itemNos;
              LS.ajax("~/billing/appendchargeitem/stop", params, function(result) {
                LS.message("info","停用成功");
                query();
              });
          }
      });
    }
    appendChargeItemGrid.itemclick = function(){
        doAdd();
	      var item = appendChargeItemGrid.getSelectedItem();
	      if(item.itemStatus=="1"){
	       //delBtn.setEnabled(false);
	       startBtn.setEnabled(false);
	       stopBtn.setEnabled(true);
         return;
        }
        //delBtn.setEnabled(true);
        startBtn.setEnabled(true);
        stopBtn.setEnabled(false);
	      itemNo.setValue(item.getValue("itemNo"));
        itemName.setValue(item.getValue("itemName"));
         sn.setValue(item.getValue("sn"));
	       itemUnit.setValue(item.getValue("itemUnit"));
	       buyType.setValue(item.getValue("buyType"));
	       remarks.setValue(item.getValue("remarks"));
      };
   

    </ls:script>
</ls:body>
</html>