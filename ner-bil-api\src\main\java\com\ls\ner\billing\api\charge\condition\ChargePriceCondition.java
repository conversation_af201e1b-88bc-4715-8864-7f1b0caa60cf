package com.ls.ner.billing.api.charge.condition;

import com.pt.poseidon.webcommon.rest.object.QueryCondition;
/**
 * 充电计费查询条件
 * <AUTHOR>
 */
public class ChargePriceCondition extends QueryCondition {

	private String prodBusiType;//产品业务类
	private String prodType;//产品类别
	private String city;//城市
	private String stationId;//站点id
	public String getProdBusiType() {
		return prodBusiType;
	}
	public void setProdBusiType(String prodBusiType) {
		this.prodBusiType = prodBusiType;
	}
	public String getProdType() {
		return prodType;
	}
	public void setProdType(String prodType) {
		this.prodType = prodType;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
}
