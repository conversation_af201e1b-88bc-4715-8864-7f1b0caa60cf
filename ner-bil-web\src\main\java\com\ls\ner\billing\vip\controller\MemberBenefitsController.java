package com.ls.ner.billing.vip.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ls.ner.base.log.Logger;
import com.ls.ner.base.log.LoggerFactory;
import com.ls.ner.billing.api.market.service.ICouponRPCService;
import com.ls.ner.billing.market.vo.IntegralExchangeVo;
import com.ls.ner.billing.vip.bo.VipCouponBo;
import com.ls.ner.billing.vip.service.IMemberBenefitsService;
import com.ls.ner.billing.vip.bo.MemberBenefitsBo;
import com.ls.ner.util.MapUtils;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @description MemberBenefitsController 会员权益管理
 */
@Controller
@RequestMapping("/member")
public class MemberBenefitsController{

    private static final Logger logger = LoggerFactory.getLogger(MemberBenefitsController.class);

    @ServiceAutowired(value="memberBenefitsService")
    private IMemberBenefitsService memberBenefitsService;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "couponRpcService")
    private ICouponRPCService couponRPCService;

    /**
     * @param m
     * @description 会员权益管理页面
     */
    @RequestMapping(value = "/benefits/init")
    public String init(Model m) {
        return "/bil/market/vip/info";
    }

    /**
     * @description 会员权益管理查询
     */
    @RequestMapping(value = "/benefits/query")
    public @ItemResponseBody
    QueryResultObject querys(){
        List<MemberBenefitsBo> dataList = memberBenefitsService.list();
        int count = memberBenefitsService.count();
        return RestUtils.wrappQueryResult(dataList, count);
    }

    @RequestMapping(value = "/benefits/save", method = RequestMethod.POST)
    public @ItemResponseBody
    QueryResultObject save(MemberBenefitsBo memberBenefitsBo) {
        logger.info("/benefits/save,params:{}", JSONObject.toJSONString(memberBenefitsBo));
        String mark = "N";
        try {
            List<Map<String, Object>> retList = couponRPCService.getGoodsVrCoupon();
            logger.info("retList,params:{}", JSONObject.toJSONString(retList));
            String exclusiveOffer = memberBenefitsBo.getExclusiveOffer();
            String welcomeGift = memberBenefitsBo.getWelcomeGift();
            List<VipCouponBo> exclusiveOfferBos = JSONObject.parseArray(exclusiveOffer, VipCouponBo.class);
            List<VipCouponBo> welcomeGiftBos =  JSONObject.parseArray(welcomeGift, VipCouponBo.class);
            for (Map<String, Object> map : retList) {
                String cpnName = MapUtils.getValue(map, "cpn_name");
                for (VipCouponBo exclusiveOfferBo : exclusiveOfferBos) {
                    if (cpnName.equals(exclusiveOfferBo.getCouponName())) {
                        String limitGetNum = MapUtils.getValue(map, "lim_get_num");
                        String couponId = MapUtils.getValue(map, "cpn_id");
                        String effectTime = MapUtils.getValue(map, "effect_time");
                        String remark = MapUtils.getValue(map, "cpn_marks");
                        BigDecimal amt = new BigDecimal(MapUtils.getValue(map, "cpn_amt"));
                        exclusiveOfferBo.setCouponId(couponId);
                        exclusiveOfferBo.setLimit(limitGetNum);
                        exclusiveOfferBo.setEffectTime(effectTime);
                        exclusiveOfferBo.setRemark(remark);
                        exclusiveOfferBo.setAmt(amt);
                    }
                }
                for (VipCouponBo welcomeGiftBo : welcomeGiftBos) {
                    if (cpnName.equals(welcomeGiftBo.getCouponName())) {
                        String limitGetNum = MapUtils.getValue(map, "lim_get_num");
                        String couponId = MapUtils.getValue(map, "cpn_id");
                        String effectTime = MapUtils.getValue(map, "effect_time");
                        String remark = MapUtils.getValue(map, "cpn_marks");
                        BigDecimal amt = new BigDecimal(MapUtils.getValue(map, "cpn_amt"));
                        welcomeGiftBo.setCouponId(couponId);
                        welcomeGiftBo.setLimit(limitGetNum);
                        welcomeGiftBo.setEffectTime(effectTime);
                        welcomeGiftBo.setRemark(remark);
                        welcomeGiftBo.setAmt(amt);
                    }
                }
            }

            memberBenefitsBo.setExclusiveOffer(JSONObject.toJSONString(exclusiveOfferBos));
            memberBenefitsBo.setWelcomeGift(JSONObject.toJSONString(welcomeGiftBos));
            if(StringUtil.isEmpty(memberBenefitsBo.getIsMemberStationExplain())){
                memberBenefitsBo.setIsMemberStationExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getIsExclusiveCustomerExplain())){
                memberBenefitsBo.setIsExclusiveCustomerExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getIsUpgradePackageExplain())){
                memberBenefitsBo.setIsUpgradePackageExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getIsDoubleExplain())){
                memberBenefitsBo.setIsDoubleExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getIsExchangeExplain())){
                memberBenefitsBo.setIsExchangeExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getIsMemberDayAdditionalDiscountExplain())){
                memberBenefitsBo.setIsMemberDayAdditionalDiscountExplain("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getMonthDegreeConfig())){
                memberBenefitsBo.setMonthDegreeConfig("");
            }

            if(StringUtil.isEmpty(memberBenefitsBo.getChargeDegree1())){
                memberBenefitsBo.setChargeDegree1("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getChargeDegree2())){
                memberBenefitsBo.setChargeDegree2("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getChargeDegree3())){
                memberBenefitsBo.setChargeDegree3("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getChargeDegree4())){
                memberBenefitsBo.setChargeDegree4("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getChargeDegree5())){
                memberBenefitsBo.setChargeDegree5("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getDiscountLimit())){
                memberBenefitsBo.setDiscountLimit("");
            }
            if(StringUtil.isEmpty(memberBenefitsBo.getDiscountLimit())){
                if (StringUtil.isEmpty(memberBenefitsBo.getLimitAmt())){
                    memberBenefitsBo.setLimitAmt(BigDecimal.ZERO);
                }
                if (StringUtil.isEmpty(memberBenefitsBo.getLimitNum())){
                    memberBenefitsBo.setLimitNum(0);
                }
            }
            memberBenefitsService.save(memberBenefitsBo);
            mark = "Y";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return RestUtils.wrappQueryResult(mark);
    }

    @RequestMapping(value = "/benefits/initEdit", method = RequestMethod.GET)
    public String initEdit(Model m, @RequestParam("id") String vipId) {
        MemberBenefitsBo params = new MemberBenefitsBo();
        params.setVipId(vipId);
        MemberBenefitsBo bo = memberBenefitsService.getMemberBenefits(params);
        m.addAttribute("infoForm", bo);

        List<Map<String, Object>> retList = couponRPCService.getGoodsVrCoupon();
//        logger.info("retList,params:{}", JSONObject.toJSONString(retList));
        List<Map<String, Object>> goodsVrCouponList = new ArrayList<Map<String, Object>>();

        if (CollectionUtils.isNotEmpty(retList)) {
            for (Map<String, Object> ret : retList) {

                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("name", MapUtils.getValue(ret, "cpn_name"));
                tempMap.put("value", MapUtils.getValue(ret, "cpn_id"));
                goodsVrCouponList.add(tempMap);

            }
        }
        m.addAttribute("couponList", goodsVrCouponList);
        logger.info("goodsVrCouponList,params:{}", JSONObject.toJSONString(goodsVrCouponList));

        return "/bil/market/vip/memberBenefitsEdit";
    }

    @RequestMapping(value = "/benefits/initInfo", method = RequestMethod.GET)
    public String initInfo(Model m, @RequestParam("id") String vipId) {
        MemberBenefitsBo params = new MemberBenefitsBo();
        params.setVipId(vipId);
        MemberBenefitsBo bo = memberBenefitsService.getMemberBenefits(params);
        m.addAttribute("infoForm", bo);

        List<Map<String, Object>> retList = couponRPCService.getGoodsVrCoupon();
        logger.info("retList,params:{}", JSONObject.toJSONString(retList));
        List<Map<String, Object>> goodsVrCouponList = new ArrayList<Map<String, Object>>();

        if (CollectionUtils.isNotEmpty(retList)) {
            for (Map<String, Object> ret : retList) {

                Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("name", MapUtils.getValue(ret, "cpn_name"));
                tempMap.put("value", MapUtils.getValue(ret, "cpn_id"));
                goodsVrCouponList.add(tempMap);

            }
        }
        m.addAttribute("couponList", goodsVrCouponList);
        logger.info("goodsVrCouponList,params:{}", JSONObject.toJSONString(goodsVrCouponList));

        return "/bil/market/vip/memberBenefitsInfo";
    }

}
