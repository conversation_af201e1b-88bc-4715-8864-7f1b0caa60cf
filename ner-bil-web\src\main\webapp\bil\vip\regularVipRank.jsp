<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://www.longshine.com/taglib/ls" prefix="ls"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<ls:head title="会员等级配置"/>
<ls:body>
		<table  align="center" class="tab_search">
			<tr>
				<td>
 					<div style="display: flex; align-items: center; margin-bottom: 8px;">
 						<span style="font-weight: bold; font-size: 16px;">会员等级列表</span>
 						<div style="margin-left: 20px;">
 							<ls:checklist type="radio" name="calcModeRadio" required="true" onchanged="onCalcModeChange">
 								<ls:checkitem value="quantity" text="按累计充电量计算"></ls:checkitem>
 								<ls:checkitem value="amount" text="按累计充电金额计算"></ls:checkitem>
 							</ls:checklist>
 						</div>
 					</div>
 					<!-- grid放在标题和单选框下方 -->
					<ls:grid url="" showRowNumber="false" name="regularVipRank" height="250px" showCheckBox="false"
							 allowScroll="true">
						<ls:gridToolbar name="operation">
							<ls:gridToolbarItem name="modify" text="修改" imageKey="edit" onclick="modifyOne" ></ls:gridToolbarItem>
						</ls:gridToolbar>
						<ls:column caption="主键" name="rankId" hidden="true" width="100px"></ls:column>
						<ls:column caption="等级" name="rankNo" width="100px"></ls:column>
						<ls:column caption="等级名称" name="rankName" width="150px"></ls:column>
						<ls:column caption="累计充电量下限" name="rankMin" width="200px"></ls:column>
						<ls:column caption="累计充电量上限" name="rankMax" width="200px"></ls:column>
						<ls:column caption="累计充电金额下限" name="rankAmtMin" width="200px"></ls:column>
						<ls:column caption="累计充电金额上限" name="rankAmtMax" width="200px"></ls:column>
						<ls:column caption="会员持续时间(天)" name="durationDays" width="200px"></ls:column>
					</ls:grid>
				</td>
			</tr>
		</table>
	<ls:script>

		query();
		window.query=query;

		function query(){
			regularVipRank.query("~/vip/regular/vipRankConfigList");
			regularVipRank.setCell(regularVipRank.getItems().length - 1,"rankMax","无上限")
		}

		function modifyOne() {
            var item = regularVipRank.getSelectedItem();
            if(item==null){
                LS.message("info","请选择一条记录!");
                return;
            }
			LS.dialog("~/vip/regular/vipRankConfigEdit?rankId="+item.rankId,"修改会员等级配置",600,400,true, null);
		}

		// 页面初始化获取配置，完全对齐积分规则配置风格
		function initShow() {
			LS.ajax("~/vip/regular/getConfig",null,function(result){
				calcModeRadio.setValue(result);
			});
		}
		window.onload = function() {
			initShow();
		};

		// 恢复原计算方式选项
		function restoreOriginalCalcMode() {
			LS.ajax("~/vip/regular/getConfig",null,function(result){
				var data = result && result.items && result.items[0] ? result.items[0] : {};
				if(data.calcMode) {
					calcModeRadio.setValue(data.calcMode);
				} else {
					calcModeRadio.setValue('quantity'); // 默认
				}
			});
		}

		// 切换计算方式时弹出确认框，输入比例，保存到后端
		function onCalcModeChange() {
			var mode = calcModeRadio.getValue();
			LS.dialog("~/vip/regular/calcRatio?mode="+mode,"会员等级配置切换",350,101,true);
		}

	</ls:script>
</ls:body>
</html>
