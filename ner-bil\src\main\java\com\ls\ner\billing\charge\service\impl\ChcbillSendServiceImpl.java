package com.ls.ner.billing.charge.service.impl;

import com.google.common.collect.Maps;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.ast.api.oper.service.IOperRpcService;
import com.ls.ner.base.constants.AssetConstants;
import com.ls.ner.base.constants.PublicConstants;
import com.ls.ner.base.mq.MqConstants;
import com.ls.ner.base.mq.ProducerHelper;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.charge.exception.ChargeBillingRltException;
import com.ls.ner.billing.charge.ChargeConstant;
import com.ls.ner.billing.charge.bo.*;
import com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition;
import com.ls.ner.billing.charge.dao.*;
import com.ls.ner.billing.charge.service.IChcbillSendService;
import com.ls.ner.billing.xpcharge.dao.IXpStationSendDao;
import com.ls.ner.billing.xpcharge.service.IXpPeakService;
import com.ls.ner.billing.xpcharge.service.IXpPileSendService;
import com.ls.ner.pub.api.sequence.service.ISeqRpcService;
import com.ls.ner.util.*;
import com.ls.ner.util.json.IJsonUtil;
import com.ls.ner.util.tenant.TenantUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.param.api.ISysParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.*;

/**
 * 充电配置下发
 * <AUTHOR>
 */
@Service(target = {ServiceType.APPLICATION}, value = "chcbillSendService")
public class ChcbillSendServiceImpl implements IChcbillSendService {
	private static final Logger LOGGER = LoggerFactory.getLogger(ChcbillSendServiceImpl.class);

	@Autowired
	private IChargeBillingConfDao chargeBillingConfDao;

	@Autowired
	private IChargePeriodsDao chargePeriodsDao;
	
	@Autowired
	private IChcbillSendDao chcbillSendDao;

	@Autowired
	private IChcbillPileSendDao chcbillPileSendDao;

	@ServiceAutowired("xpPileSendService")
	private IXpPileSendService xpPileSendService;

	@Autowired
	private IXpStationSendDao xpStationSendDao;

	@Autowired
	private IChcbillPileSendLogDao chcbillPileSendLogDao;

	@ServiceAutowired(value="seqRpcService", serviceTypes=ServiceType.RPC)
	private ISeqRpcService seqRpcService;

	@ServiceAutowired(value="archivesRpcService", serviceTypes=ServiceType.RPC)
	private IArchivesRpcService archivesRpcService;

	@ServiceAutowired(value="operRpcService", serviceTypes=ServiceType.RPC)
	private IOperRpcService operRpcService;

	@ServiceAutowired(serviceTypes = ServiceType.RPC, value = "sysParamService")
	private ISysParamService sysParamService;
	
	@Autowired
	private ChargeBillingRltUtil rltUtil;

	@ServiceAutowired("peakService")
	private IXpPeakService peakService;
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询充电计费配置下发信息  E_CHCBILL_SEND
	 */
	public List<ChcbillSendBo> queryChcbillSends(ChcbillSendQueryCondition bo){
		return chcbillSendDao.queryChcbillSends(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description  查询充电计费配置下发信息条数  E_CHCBILL_SEND
	 */
	public int queryChcbillSendsNum(ChcbillSendQueryCondition bo){
		return chcbillSendDao.queryChcbillSendsNum(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息  E_CHCBILL_PILESEND
	 */
	public List<ChcbillPileSendBo> queryChcbillPileSends(ChcbillSendQueryCondition bo){
		return chcbillPileSendDao.queryChcbillPileSends(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发信息条数  E_CHCBILL_PILESEND
	 */
	public int queryChcbillPileSendsNum(ChcbillSendQueryCondition bo){
		return chcbillPileSendDao.queryChcbillPileSendsNum(bo);
	}

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志   E_CHCBILL_PILESEND_LOG
	 */
	public List<ChcbillPileSendLogBo> queryChcbillPileSendLogs(ChcbillSendQueryCondition bo){
		return chcbillPileSendLogDao.queryChcbillPileSendLogs(bo);
	}
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询桩电价下发日志条数   E_CHCBILL_PILESEND_LOG
	 */
	public int queryChcbillPileSendLogsNum(ChcbillSendQueryCondition bo){
		return chcbillPileSendLogDao.queryChcbillPileSendLogsNum(bo);
	}

	//================================计费模型参数下发---开始===========================================
	
	/**
	 * @description 充电计费配置下发到充电站
	 * @param chcNo 要下发的充电计费配置编号
	 * @return String 下发完成状态  02下发失败   03部分成功  04下发成功 05正在下发
	 * @throws IllegalAccessException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchFieldException 
	 * */
	public void issuredStation(String chcNo,Map<String,Object> map) throws Exception{
		//1、查找充电计费配置
		ChargeBillingConfBo cbcBo = obtainChargeBillingConf(chcNo);
		//2、查询充电站下所有未下发成功的桩
		List<Map<String,String>> piles = archivesRpcService.queryPilesByStationId(cbcBo.getStationId(),null);
		ChcbillSendQueryCondition sendQueryCondition = new ChcbillSendQueryCondition();
		sendQueryCondition.setChcNo(chcNo);
		sendQueryCondition.setPileSendStatus(ChargeConstant.successFlag.SUCCESS);
		List<ChcbillPileSendBo> pileSends = chcbillPileSendDao.queryChcbillPileSends(sendQueryCondition);
		piles = MergeUtil.removeDuplicate(piles, pileSends, "pileNo");
		LOGGER.debug("开始下发站点计费模型");
		//3、下发充电计费配置到充电桩
		String dataSource = operRpcService.obtainDatasource(null, cbcBo.getStationId(), null);
		List<String> formatDatas = combinateFormat(piles, cbcBo,dataSource);
		ProducerHelper.batchProduce(MqConstants.CPMQ_CHARGING_PRIING_SEND, formatDatas);
		//4、记录下发结果
		for(Map<String,String> pile : piles){
			addIssuredLog(chcNo, pile.get("pileNo"), ChargeConstant.successFlag.UNDER_WAY, null,map);
		}
		//4、记录更新充电计费配置下发信息  E_CHCBILL_SEND
		ChcbillSendBo cSendBo = new ChcbillSendBo();
		cSendBo.setChcNo(chcNo);
		cSendBo.setSendTime(DateTools.getStringDateShort("yyyy-MM-dd HH:mm"));
		cSendBo.setSendStatus(ChargeConstant.chcBillSendStatus.UNDER_WAY);
		chcbillSendDao.updateChcbillSend(cSendBo);
	}

	/**
	 * @description 充电计费配置下发到充电桩
	 * @param chcNo 要下发的充电计费配置编号
	 * @param pileNo 要下发的充电桩编号
	 * @return boolean true 下发成功   false 下发失败
	 * */
	public void issuredPile(String chcNo,String pileNo,Map<String,Object> map) throws Exception{
		//1、查找充电计费配置
		ChargeBillingConfBo cbcBo = obtainChargeBillingConf(chcNo);
		//2、查询充电站下所有未下发成功的桩
		List<Map<String,String>> piles = archivesRpcService.queryPilesByStationId(cbcBo.getStationId(),pileNo);
		//3、下发充电计费配置到充电桩
		String dataSource = operRpcService.obtainDatasource(null, cbcBo.getStationId(), null);
		List<String> formatDatas = combinateFormat(piles, cbcBo,dataSource);
		ProducerHelper.batchProduce(MqConstants.CPMQ_CHARGING_PRIING_SEND, formatDatas);
		//4、记录下发结果
		for(Map<String,String> pile : piles){
			addIssuredLog(chcNo, pile.get("pileNo"), ChargeConstant.successFlag.UNDER_WAY, null,map);
		}
	}

	@Override
	public void autoIssuredPile(Map map) throws Exception{
		String dataSource = StringUtil.nullToString(map.get("dataSource"));
		TenantUtil.setTenantId(StringUtil.nullToString(map.get("tenantId")));
		Map<String,String> pileMap = getPileInfo(map);//获取桩信息
		if(pileMap != null){
			String operStatus = StringUtil.nullToString(pileMap.get("operStatus"));//判断桩是否投运 0否 1是
			if("01".equals(operStatus) || "04".equals(operStatus) || "05".equals(operStatus)){//未启用01、停运04、报废05 不做处理
				LOGGER.debug(">>>>>桩未投运,不重新下发计费模型");
				return;
			}else{
				String bilFlag = isStationBilValid(pileMap);//判断站点是否配置有效计费模型  0否 1是
				if("0".equals(bilFlag)){//未配置不做处理
					LOGGER.debug(">>>>>站点未配置有效计费模型,不重新下发计费模型");
					return;
				}else if("1".equals(bilFlag)){
					Map paramMap = new HashMap();
					paramMap.put("pileNo",StringUtil.nullToString(pileMap.get("pileNo")));
					//判断今天是否有成功下发记录 0无
					int count = chcbillPileSendDao.querySendSucceedCount(paramMap);
					if(count>0){//如果当前桩找到的电价下发日志，且最后一条是成功，下发时间是当日，则不处理。
						LOGGER.debug(">>>>>今日已有下发成功记录,不重新下发计费模型");
						return;
					}else if(count == 0){
						//查询最新计费模型
						Map bilConfLast = chcbillPileSendDao.qyeruBilConfLast(pileMap);
						if(bilConfLast != null) {
							String chcNo = "";
							chcNo = StringUtil.nullToString(bilConfLast.get("chcNo"));
							ChargeBillingConfBo cbcBo = obtainChargeBillingConf(chcNo);

							//将数据组合成下发到车联网的格式
							List<Map<String, String>> piles = new ArrayList<>();
							piles.add(pileMap);
							List<String> formatDatas = combinateFormat(piles, cbcBo, dataSource);
							ProducerHelper.batchProduce(MqConstants.CPMQ_CHARGING_PRIING_SEND, formatDatas);
							//记录下发结果
							for (Map<String, String> pile : piles) {
								addIssuredLog(chcNo, pile.get("pileNo"), ChargeConstant.successFlag.UNDER_WAY, null,null);
							}
							//记录更新充电计费配置下发信息  E_CHCBILL_SEND
							ChcbillSendBo cSendBo = new ChcbillSendBo();
							cSendBo.setChcNo(chcNo);
							cSendBo.setSendTime(DateTools.getStringDateShort("yyyy-MM-dd HH:mm"));
							cSendBo.setSendStatus(ChargeConstant.chcBillSendStatus.UNDER_WAY);
							chcbillSendDao.updateChcbillSend(cSendBo);
						}
					}
				}
			}
		}
	}

	/**
	 *@Description:
	 * 充电桩登录时，采集通过指定topic把桩号传到业务boss，bil自动识别并判断是否有计费规则需要下发。
	 * 前提条件：桩是否投运、所属站点是否配置有效的计费规则。
	 * 判断规则：
	 * 如果当前桩找到的电价下发日志，且最后一条是成功，下发时间是当日，则不处理。其他情况则下发最新计费配置。
	  *@Author: qianghuang
	  *@Time: 2017/11/16 16:38
	  */

	public void newAutoIssuredPile(Map map) throws Exception{
		String dataSource = StringUtil.nullToString(map.get("dataSource"));
		TenantUtil.setTenantId(StringUtil.nullToString(map.get("tenantId")));
		Map<String,String> pileMap = getPileInfo(map);//获取桩信息
		if(pileMap != null){
			String operStatus = StringUtil.nullToString(pileMap.get("operStatus"));//判断桩是否投运 0否 1是
			if("01".equals(operStatus) || "04".equals(operStatus) || "05".equals(operStatus)){//未启用01、停运04、报废05 不做处理
				LOGGER.debug(">>>>>桩未投运,不重新下发计费模型");
				return;
			}else{
				String bilFlag = newIsStationBilValid(pileMap);//判断站点是否配置有效计费模型  0否 1是
				if("0".equals(bilFlag)){//未配置不做处理
					LOGGER.debug(">>>>>未配置有效计费模型,不重新下发计费模型");
					return;
				}else if("1".equals(bilFlag)){
					Map paramMap = new HashMap();
					paramMap.put("pileNo",StringUtil.nullToString(pileMap.get("pileNo")));
					//判断今天是否有成功下发记录 0无
					int count = chcbillPileSendDao.querySendSucceedCount(paramMap);
					if(count>0){//如果当前桩找到的电价下发日志，且最后一条是成功，下发时间是当日，则不处理。
						LOGGER.debug(">>>>>今日已有下发成功记录,不重新下发计费模型");
						return;
					}else if(count == 0){
						//查询最新计费模型
						ChcbillSendQueryCondition condition= new ChcbillSendQueryCondition();
						condition.setPileId(StringUtil.nullToString(pileMap.get("pileId")));
						List<Map> bilConfLastList = xpPileSendService.queryTheNewChc(condition);// 先找桩  再找站
						Map bilConfLast =new HashMap();
						if (bilConfLastList!=null && bilConfLastList.size()>0){
							bilConfLast.get(0);
						}else {
							bilConfLast = chcbillPileSendDao.qyeruBilConfLastNew(pileMap);
						}
						if(bilConfLast != null) {
							String chcNo = "";
							chcNo = StringUtil.nullToString(bilConfLast.get("chcNo"));
							ChargeBillingConfBo cbcBo = obtainChargeBillingConf(chcNo);

							//将数据组合成下发到车联网的格式
							List<Map<String, String>> piles = new ArrayList<>();
							piles.add(pileMap);
							List<String> formatDatas = combinateFormat(piles, cbcBo, dataSource);
							ProducerHelper.batchProduce(MqConstants.CPMQ_CHARGING_PRIING_SEND, formatDatas);
							//记录下发结果
							for (Map<String, String> pile : piles) {
								addIssuredLog(chcNo, pile.get("pileNo"), ChargeConstant.successFlag.UNDER_WAY, null,null);
							}

							//4、记录充电计费配置下发信息  E_CHCBILL_SEND
							ChcbillSendBo cSendBo = new ChcbillSendBo();
							cSendBo.setChcNo(chcNo);
							cSendBo.setSendTime(DateTools.getStringDateShort("yyyy-MM-dd HH:mm"));
							cSendBo.setSendStatus(ChargeConstant.chcBillSendStatus.UNDER_WAY);
							cSendBo.setStationId(StringUtil.nullToString(pileMap.get("stationId")));
							int sendCount=xpStationSendDao.queryChcbillSend(cSendBo);
							if (sendCount>0){
								xpStationSendDao.updateChcbillSend(cSendBo);
							}else {
								xpStationSendDao.insertChcbillSend(cSendBo);
							}
						}
					}
				}
			}
		}
	}

	@Override
	public ChcbillSendBo querySendLongByStationIdAndChcNo(String stationId, String chcNo) {
		return chcbillSendDao.selectByStationIdAndChcNo(stationId, chcNo);
	}

	//获取桩信息
	public Map getPileInfo(Map map){
		Map<String,Object> resultMap = new HashMap();
		String dataTime = StringUtil.nullToString(map.get("dataTime"));
		String dataSource = StringUtil.nullToString(map.get("dataSource"));
		String operatorId = StringUtil.nullToString(map.get("operatorId"));
		String pileId = "";
		String pileNo = "";
		String gunId = "";
		String gunNo = "";
		if("1".equals(dataSource)){
			gunId = StringUtil.nullToString(map.get("equipId"));
		}else if("2".equals(dataSource)){
			pileNo = StringUtil.nullToString(map.get("equipNo"));
			gunNo = StringUtil.nullToString(map.get("gunNo"));
		}else if("3".equals(dataSource)){
			pileId = StringUtil.nullToString(map.get("equipId"));
		}
		Map paramMap = new HashMap();
		paramMap.put("gunId",gunId);
		paramMap.put("pileNo",pileNo);
		paramMap.put("gunNo",gunNo);
		paramMap.put("pileId",pileId);
		if(StringUtil.isNotEmpty(gunId) || StringUtil.isNotEmpty(pileNo) || StringUtil.isNotEmpty(pileId)){
			resultMap = archivesRpcService.queryPileInfo(paramMap);
			if(resultMap != null){
				resultMap.put("operatorId",operatorId);
			}
		}
		return resultMap;
	}

	//判断站点是否配置有效计费模型  0否 1是
	public String isStationBilValid(Map<String,String> pileMap){
		String bilFlag = "0";
		String stationId = StringUtil.nullToString(pileMap.get("stationId"));
		if(!StringUtils.isEmpty(stationId)) {
			ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
			String endEftDate= JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
			cbcCondition.setEndEftDate(endEftDate);
			cbcCondition.setChcStatus(ChargeConstant.validFlag.ENABLE);
			cbcCondition.setStationId(stationId);
			//按照生效时间排序，取与当前时间节点最接近的一条返回
			List<ChargeBillingConfBo> cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
			if(cbcList != null || cbcList.size() > 0){
				bilFlag = "1";
			}
		}
		return bilFlag;
	}


	//判断站点是否配置有效新版计费模型  0否 1是
	public String newIsStationBilValid(Map<String,String> pileMap){
		String bilFlag = "0";
		String stationId = StringUtil.nullToString(pileMap.get("stationId"));
		String pileId = StringUtil.nullToString(pileMap.get("pileId"));
		String billingConfig = StringUtil.nullToString(pileMap.get("billingConfig"));
		if(!StringUtils.isEmpty(stationId)) {
			ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
			String endEftDate= JodaDateTime.getFormatDate("yyyy-MM-dd HH:mm");
			cbcCondition.setEndEftDate(endEftDate);
			cbcCondition.setChcStatus(ChargeConstant.validFlag.ENABLE);
			cbcCondition.setStationId(stationId);
			//按照生效时间排序，取与当前时间节点最接近的一条返回
			List<ChargeBillingConfBo> cbcList = null;
			String isAssistPileBilling=sysParamService.getSysParamsValues("isAssistPileBilling");
			if (AssetConstants.billingConfig.BILLING_BY_PILE.equals(billingConfig) && PublicConstants.YN.TRUE.equals(isAssistPileBilling)){
				cbcCondition.setPileId(pileId);
				cbcList = chargeBillingConfDao.queryPileChargeBillingConfs(cbcCondition);
			}else{
				cbcList = chargeBillingConfDao.queryNewChargeBillingConfs(cbcCondition);
			}
			if(cbcList != null || cbcList.size() > 0){
				bilFlag = "1";
			}
		}
		return bilFlag;
	}

	/**
	 * @description 写入充电桩下发日志
	 * @param chcNo 充电计费编号
	 * @param pileNo 充电桩外部标识，产生规则为流水号
	 * @param pileSendStatus 下发状态，下发状态，001成功、003失败、002下发中
	 * @param failReason 下发失败原因
	 * */
	private void addIssuredLog(String chcNo, String pileNo, String pileSendStatus, String failReason,Map<String,Object> map){
			rltUtil.isNotEmpty(chcNo, "充电计费编号不能为空");
			rltUtil.isNotEmpty(pileNo, "充电桩编号不能为空");
			//1、 写入桩电价下发日志   E_CHCBILL_PILESEND_LOG
			ChcbillPileSendLogBo log = new ChcbillPileSendLogBo();
			log.setChcNo(chcNo);
			log.setPileNo(pileNo);
			log.setPileSendStatus(pileSendStatus);
			log.setFailReason(failReason);
			log.setPileSendId(String.valueOf(seqRpcService.getDefId()));
			log.setSendTime("now");
			if (map!=null) {
					String networkIp = String.valueOf(map.get("networkIp"));
					if (!com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(networkIp)) {
						if (networkIp.length()>32){
							log.setNetworkIp(networkIp.substring(0, 32));
						}else{
							log.setNetworkIp(networkIp);
						}
					}
					String intranetIp = String.valueOf(map.get("intranetIp"));
					if (!com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(intranetIp)) {
						if (intranetIp.length() > 32) {
							log.setIntranetIp(intranetIp.substring(0, 32));
						}else {
							log.setIntranetIp(intranetIp);
						}
					}
					String operator = String.valueOf(map.get("operator"));
					if (!com.pt.poseidon.common.utils.tools.StringUtils.nullOrBlank(operator)) {
						log.setOperator(operator);
					}
			}
			chcbillPileSendLogDao.insertChcbillPileSendLog(log);
			//2、桩电价下发信息是否存在，不存在则写入，存在则更新
			ChcbillPileSendBo cPileSendBo = new ChcbillPileSendBo();
			cPileSendBo.setChcNo(chcNo);
			cPileSendBo.setPileNo(pileNo);
			cPileSendBo.setPileSendId(log.getPileSendId());
			ChcbillSendQueryCondition cSendCondition = new ChcbillSendQueryCondition();
			cSendCondition.setChcNo(chcNo);
			cSendCondition.setPileNo(pileNo);
			//int count = chcbillPileSendDao.queryChcbillPileSendsNum(cSendCondition);
			String systemId = chcbillPileSendDao.queryChcbillPileSendsNumId(cSendCondition);
			//if(count == 0){
			if(StringUtils.isEmpty(systemId)){
				chcbillPileSendDao.insertChcbillPileSend(cPileSendBo);
			}else{
				cPileSendBo.setSystemId(systemId);
				chcbillPileSendDao.updateChcbillPileSendById(cPileSendBo);
			}
	}

	/**
	 * @description 查找充电计费配置
	 * @param chcNo 充电计费编号
	 * @return ChargeBillingConfBo 充电计费配置
	 * */
	private ChargeBillingConfBo obtainChargeBillingConf(String chcNo){
		ChargeBillingConfQueryCondition cbcCondition = new ChargeBillingConfQueryCondition();
		cbcCondition.setChcNo(chcNo);
		List<ChargeBillingConfBo> cbcList = chargeBillingConfDao.queryChargeBillingConfs(cbcCondition);
		if(cbcList == null)
			throw new ChargeBillingRltException("该充电站没有有效的充电计费配置");
		if(cbcList.size() != 1)
			throw new ChargeBillingRltException("该充电站有多个有效的充电计费配置");
		ChargeBillingConfBo cbcBo = cbcList.get(0);
		List<ChargePeriodsBo> cperiods = null;
		if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbcBo.getChargeMode())){//分时计费
			ChargeBillingConfQueryCondition periodsCondition = new ChargeBillingConfQueryCondition();
			periodsCondition.setChcNo(rltUtil.isNotEmpty(cbcBo.getChcNo(), "查询不到充电计费编号"));
			cperiods = peakService.queryPeriodListCheckPeak(periodsCondition);
			cbcBo.setCperiods(cperiods);
		}
		return cbcBo;
	}
	
	/**
	 * 将数据组合成下发到车联网的格式
	 * */
	private List<String> combinateFormat(List<Map<String,String>> piles,ChargeBillingConfBo cbcBo,String dataSource){
		List<String> combinates = new LinkedList<String>();
		if(piles!=null && piles.size() > 0){
			for(Map<String,String> pile : piles){
				Map<String,Object> combinate = new HashMap<String,Object>();
				if("1".equals(dataSource)){//没有gun的下发
					LOGGER.error("--------------------------没有枪的电价下发----------------------------------");
				}else if("2".equals(dataSource)){//dataSource=2，则必填。equipNo为桩编号
					combinate.put("operatorId", pile.get("operatorId"));//运营商标识
					combinate.put("equipNo", pile.get("pileNo"));//桩编号
				}else if("3".equals(dataSource)){//桩id
					combinate.put("equipId", String.valueOf(pile.get("pileId")));//桩id
				}
				combinate.put("equipType", "0202");//充电枪
				combinate.put("dataSource", dataSource);//传递桩编号
				combinate.put("pricingId", cbcBo.getChcNo());//定价ID/计费模型ID
				combinate.put("effTime", cbcBo.getEftDate());//桩编号
				if(ChargeConstant.chcBillingChargeMode.STANDARD.equals(cbcBo.getChargeMode())){//如果是标准计费方式
					combinate.put("timeSharFlag", "0");//分时标志,不分时
					combinate.put("rate", cbcBo.getChargePrice());//非分时的费率
				 }else if(ChargeConstant.chcBillingChargeMode.TIME_SHARE.equals(cbcBo.getChargeMode())){//如果是分时计费方式
					combinate.put("timeSharFlag", "1");//分时标志,分时
					combinate.put("periodsNum", String.valueOf(cbcBo.getCperiods().size()));//分时标志,分时
					List<Map<String,String>> cperiodList = new LinkedList<Map<String,String>>();
					for(ChargePeriodsBo cperiod : cbcBo.getCperiods()){
						Map<String,String> cperiodMap = Maps.newHashMap();
						cperiodMap.put("sn", cperiod.getSn());//时段序号
						cperiodMap.put("beginTime", cperiod.getBeginTime());//起始时间
						cperiodMap.put("endTime", cperiod.getEndTime());//结束时间
						cperiodMap.put("periodFlag", cperiod.getTimeFlag());//时段标志
						cperiodMap.put("periodRate", cperiod.getPrice());//时段费率
						cperiodList.add(cperiodMap);
					}
					combinate.put("periodsList", cperiodList);
				 }
				//服务费
				String attachItemPrices = cbcBo.getAttachItemPrices();
				if(attachItemPrices != null && !"".equals(attachItemPrices)){
					String [] prices = attachItemPrices.split(",");
					combinate.put("serPrice", prices[0]);
				}
				combinates.add(IJsonUtil.obj2Json(combinate));
			}
		}
		return combinates;
	}
	//================================计费模型参数下发---结束===========================================
}
