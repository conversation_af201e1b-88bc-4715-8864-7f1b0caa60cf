package com.ls.ner.billing.api.xpcharge.condition;

import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

import java.util.List;

/**
 * 充电计费配置查询条件
 * <AUTHOR>
 */
public class XpChargeBillingConfQueryCondition extends QueryCondition {

	private String orgCode;//管理单位
	private List orgCodeList;//管理单位
	private String orgCodeName;
	private String chcNo;//充电计费编号
	private String chcName;//充电计费名称
	private String stationName;//充电站名称
	private String stationId;//充电站编号
	private String chcStatus;//状态，01有效、02无效、03草稿
	private List<String> chcStatusList;
	private String billCtlMode;//费控模式，1本地 2远程
	private String endEftDate;//生效日期止
	private String unChcStatus;//不生效状态
	private String upChcNo;//上个版本
	private String isMain;//是否是新版计费主界面（0否，1是）
	private String isFrom;//0默认 1客户分组计费版本
	private String stationIdIsNull;//1 查询条件stationId为空
	private String custIdIsNull;//1 查询条件custId为空

	public List<String> getChcStatusList() {
		return chcStatusList;
	}

	public void setChcStatusList(List<String> chcStatusList) {
		this.chcStatusList = chcStatusList;
	}

	public String getChcName() {
		return chcName;
	}

	public void setChcName(String chcName) {
		this.chcName = chcName;
	}

	public String getUpChcNo() {
		return upChcNo;
	}

	public void setUpChcNo(String upChcNo) {
		this.upChcNo = upChcNo;
	}

	public String getUnChcStatus() {
		return unChcStatus;
	}

	public void setUnChcStatus(String unChcStatus) {
		this.unChcStatus = unChcStatus;
	}

	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	public String[] getStationIds() {
		if(StringUtils.nullOrBlank(stationId))
			return null;
		return stationId.split(",");
	}
	public String getChcStatus() {
		return chcStatus;
	}
	public void setChcStatus(String chcStatus) {
		this.chcStatus = chcStatus;
	}
	public String getEndEftDate() {
		return endEftDate;
	}
	public void setEndEftDate(String endEftDate) {
		this.endEftDate = endEftDate;
	}

	public String getBillCtlMode() {
		return billCtlMode;
	}

	public void setBillCtlMode(String billCtlMode) {
		this.billCtlMode = billCtlMode;
	}

	public String getIsMain() {
		return isMain;
	}

	public void setIsMain(String isMain) {
		this.isMain = isMain;
	}

	public String getIsFrom() {
		return isFrom;
	}

	public void setIsFrom(String isFrom) {
		this.isFrom = isFrom;
	}

	public String getStationIdIsNull() {
		return stationIdIsNull;
	}

	public void setStationIdIsNull(String stationIdIsNull) {
		this.stationIdIsNull = stationIdIsNull;
	}

	public String getCustIdIsNull() {
		return custIdIsNull;
	}

	public void setCustIdIsNull(String custIdIsNull) {
		this.custIdIsNull = custIdIsNull;
	}

	public List getOrgCodeList() {
		return orgCodeList;
	}

	public void setOrgCodeList(List orgCodeList) {
		this.orgCodeList = orgCodeList;
	}
}
