<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.charge.dao.IChcbillSendDao">

	 <resultMap id="BaseResultMap" type="com.ls.ner.billing.charge.bo.ChcbillSendBo">
		<result column="CHC_NO" property="chcNo" />
		<result column="SEND_TIME" property="sendTime" />
		<result column="SEND_STATUS" property="sendStatus" />
		<result column="CHC_NAME" property="chcName" />
		<result column="EFT_DATE" property="eftDate" />
		<result column="CHC_STATUS" property="chcStatus" />
		<result column="STATION_NAME" property="stationName" />
		<result column="ORG_CODE" property="orgCode" />
		<result column="STATION_ID" property="stationId" />
	 </resultMap>

	<sql id="chcbillSendItem">
		a.CHC_NO,a.SEND_TIME,a.SEND_STATUS
	</sql>
	<sql id="chargeBillingConfItem">
		b.CHC_NAME,b.EFT_DATE,b.CHC_STATUS,b.ORG_CODE,b.STATION_ID
	</sql>
	 <!--fenge <sql id="pileStationItem">
	 	c.STATION_NAME,c.ORG_CODE
	 </sql> -->
	<sql id="queryChcbillSendsWhere">
		<if test="sendStatus !=null and sendStatus !=''">
		    and a.SEND_STATUS =#{sendStatus}
		</if>
		<if test="stationId !=null and stationId !=''">
		    and b.STATION_ID =#{stationId}
		</if>
		<if test="orgCode !=null and orgCode !=''">
			and b.ORG_CODE like concat(#{orgCode},'%')
		</if>
	</sql>
	<!--fenge <sql id="queryPileStationWhere">
		<if test="orgCode !=null and orgCode !=''">
		    and c.ORG_CODE like concat(#{orgCode},'%')
		</if>
	</sql> -->

	<!-- 查询充电计费配置下发信息  E_CHCBILL_SEND-->
	<select id="queryChcbillSends" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultMap="BaseResultMap">
		select
			<include refid="chcbillSendItem"></include>,
			<include refid="chargeBillingConfItem"></include><!--fenge ,
			<include refid="pileStationItem"></include> -->
		from E_CHCBILL_SEND a
			LEFT JOIN e_charge_billing_conf b ON a.CHC_NO = b.CHC_NO
			<!--fenge LEFT JOIN C_PILE_STATION c on b.STATION_ID = c.STATION_ID -->
		<where>
			b.CHC_STATUS != 0
			<include refid="queryChcbillSendsWhere"></include>
			<!--fenge <include refid="queryPileStationWhere"></include> -->
		</where>
		ORDER BY b.EFT_DATE DESC
		<if test="end!=null and end!=0">
			limit #{begin} ,#{end}
		</if>
	</select>

	<!-- 查询充电计费配置下发信息条数  E_CHCBILL_SEND-->
	<select id="queryChcbillSendsNum" parameterType="com.ls.ner.billing.charge.condition.ChcbillSendQueryCondition" resultType="int">
		select
			count(1)
		from E_CHCBILL_SEND a
			LEFT JOIN e_charge_billing_conf b ON a.CHC_NO = b.CHC_NO
			<!--fenge LEFT JOIN C_PILE_STATION c on b.STATION_ID = c.STATION_ID -->
		<where>
			b.CHC_STATUS != 0
			<include refid="queryChcbillSendsWhere"></include>
			<!--fenge <include refid="queryPileStationWhere"></include> -->
		</where>
	</select>

	<select id="queryPilesByMap" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT PILE_NO FROM E_CHCBILL_PILESEND_LOG
		WHERE CHC_NO = #{chcNo} and PILE_SEND_STATUS = #{pileSendStatus}
	</select>

	<!-- 新增充电计费配置下发信息 E_CHCBILL_SEND -->
	<insert id="insertChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo" useGeneratedKeys="true" keyProperty="systemId">
		insert into E_CHCBILL_SEND
		<trim prefix="(" suffix=")">
	     	<if test="chcNo !=null and chcNo !=''">CHC_NO,</if>
			<if test="sendTime !=null and sendTime !=''">SEND_TIME,</if>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS,</if>
	      	DATA_OPER_TIME,DATA_OPER_TYPE
	    </trim>
	    <trim prefix="values (" suffix=")">
			<if test="chcNo !=null and chcNo !=''">#{chcNo},</if>
			<if test="sendTime !=null and sendTime !=''">#{sendTime},</if>
			<if test="sendStatus !=null and sendStatus !=''">#{sendStatus},</if>
	      	now(),'I'
	    </trim>
	</insert>

	<!-- 更新充电计费配置下发信息 E_CHCBILL_SEND -->
	<update id="updateChcbillSend" parameterType="com.ls.ner.billing.charge.bo.ChcbillSendBo">
		update E_CHCBILL_SEND
		<set>
			<choose>
				<when test="sendTime == 'clear'">
					SEND_TIME = null,
				</when>
				<when test="sendTime != null and sendTime != ''">
					SEND_TIME =#{sendTime},
				</when>
			</choose>
			<if test="sendStatus !=null and sendStatus !=''">SEND_STATUS =#{sendStatus},</if>
			DATA_OPER_TIME =now(),
			DATA_OPER_TYPE ='U'
		</set>
		<where>
			CHC_NO = #{chcNo}
			<if test="stationId !=null and stationId !=''">and  STATION_ID=#{stationId}</if>
			<if test="pileNo !=null and pileNo !=''">and PILE_NO=#{pileNo}</if>
		</where>
	</update>

	<!-- 删除充电计费配置下发信息 E_CHCBILL_SEND -->
	<delete id="deleteChcbillSend" parameterType="string">
		delete from E_CHCBILL_SEND where CHC_NO = #{chcNo}
	</delete>

	<select id="selectByStationIdAndChcNo" resultMap="BaseResultMap">
		select
			a.CHC_NO,a.SEND_TIME,a.SEND_STATUS
		from e_chcbill_send a
		where a.STATION_ID = #{stationId}
		and a.CHC_NO = #{chcNo}
		and a.PILE_ID is null
		order by a.SYSTEM_ID desc
		limit 1
	</select>

</mapper>
