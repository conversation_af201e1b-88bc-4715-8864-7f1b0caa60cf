package com.ls.ner.billing.xpcharge.util;

/**
 * <AUTHOR>
 * @description 常量
 * @date 2022/6/21 15:18
 */
public class XpConstant {

    /**
     * timeFlag	时段标识- 1尖 2峰 3平 4谷
     */
    public static final String TIMEFLAG_J = "1";
    public static final String TIMEFLAG_F = "2";
    public static final String TIMEFLAG_P = "3";
    public static final String TIMEFLAG_G = "4";

    /**
     * 费用类型 1000000000 电费  1000001000-服务费
     */
    public static final String CHARGE = "1000000000";
    public static final String SERVICE = "1000001000";

    /**
     * 尖峰计费状态 1-启用 2-停用 3-删除
     */
    public static final String BILLING_STATE_ON = "1";
    public static final String BILLING_STATE_OFF = "2";
    public static final String BILLING_STATE_DEL = "3";
}
