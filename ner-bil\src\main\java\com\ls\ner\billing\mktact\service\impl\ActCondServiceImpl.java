package com.ls.ner.billing.mktact.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.billing.api.BillConstants;
import com.ls.ner.billing.charge.bo.ChargeSerItemBo;
import com.ls.ner.billing.charge.condition.ChargeSerItemQueryCondition;
import com.ls.ner.billing.charge.service.IChargeSerItemService;
import com.ls.ner.billing.market.bo.CouponBo;
import com.ls.ner.billing.market.dao.ICouponDao;
import com.ls.ner.billing.market.service.ICouponService;
import com.ls.ner.billing.mktact.bo.MarketActBo;
import com.ls.ner.billing.mktact.bo.MarketActGiveBo;
import com.ls.ner.billing.mktact.bo.MarketTypeBo;
import com.ls.ner.billing.mktact.dao.IActCondDao;
import com.ls.ner.billing.mktact.service.IActCondService;
import com.ls.ner.util.StringUtil;
import com.pt.poseidon.api.framework.Service;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.param.api.ISysParamService;
import com.pt.poseidon.webcommon.rest.utils.JsonUtils;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述:资讯信息
 * ActInfoServiceImpl.java
 * 作者：biaoxiangd
 * 创建日期：2017/6/27 11:47
 **/
@Service(target = {ServiceType.LOCAL}, value = "actCondService")
public class ActCondServiceImpl implements IActCondService {
    private static final Logger logger = LoggerFactory.getLogger(ActCondServiceImpl.class);

    @Autowired(required = true)
    private IActCondDao dao;

    @Autowired(required = true)
    private ICouponDao couponDao;

    @ServiceAutowired(serviceTypes = ServiceType.RPC, value = "codeService")
    private ICodeService codeService;

    @ServiceAutowired(value = "couponService", serviceTypes = ServiceType.LOCAL)
    private ICouponService couponService;

    @ServiceAutowired("chargeSerItemService")
    private IChargeSerItemService chargeSerItemService;

    @ServiceAutowired(value = "sysParamService",serviceTypes = ServiceType.RPC)
    private ISysParamService sysParamService;

    /**
     * 描述: 保存优惠条件主表
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    @Override
    public String saveActCond(MarketTypeBo bo) throws Exception {
        String dctCondType = bo.getDctCondType();
        if ("00".equals(dctCondType)) {
            bo.setProdId(null);
            bo.setDctCondUnit(null);
        }
        dao.saveActCond(bo);
        return bo.getActCondId();
    }

    /**
     * 描述: 更新优惠条件主表
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    @Override
    public int updateActCond(MarketTypeBo bo) throws Exception {
        return dao.updateActCond(bo);
    }

    /**
     * 描述: :有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    public List<MarketTypeBo> queryActDctAll(MarketTypeBo bo) throws Exception {
        List<MarketTypeBo> list = new ArrayList<MarketTypeBo>();
        list = dao.queryActDctAll(bo);
        int j = list.size();
        if (j > 0) {
            for (int i = 0; i < j; i++) {
                MarketTypeBo typeBo = list.get(i);
                String allFree = typeBo.getAllFree();
                String dctType = typeBo.getDctType();
                String calcMethod = typeBo.getDctCalcMethod();
                String actDctCondUnit = typeBo.getActDctCondUnit();
                //整单
                if ("0100".equals(dctType) || BillConstants.DctType.ALL_ORDER_SER_AMT.equals(dctType)) {
                    list.get(i).setActDctProdName("无");
                    if ("02".equals(calcMethod)){
                        list.get(i).setDctValue(typeBo.getDctValue() + "折");
                    }else{
                        if ("是".equals(allFree)){
                            list.get(i).setDctValue("-");
                        }else{
                            list.get(i).setDctValue(typeBo.getDctValue() + "元");
                        }
                    }
                } else {
                    // 0101：计价数量；0102：计价金额
                    if ("否".equals(allFree)) {
                        if ("0102".equals(dctType) && "".equals(actDctCondUnit)) {
                            list.get(i).setDctValue(typeBo.getDctValue() + "元");
                        } else {
                            CodeBO codeBo = codeService.getStandardCode("tariffUnit", actDctCondUnit, null);
                            if (codeBo != null) {
                                list.get(i).setDctValue(typeBo.getDctValue() + codeBo.getCodeName());
                            }
                        }
                    }

                    String actDctProdId = typeBo.getActDctProdId();
                    if (StringUtil.isNotBlank(actDctProdId)) {
                        Map<String, String> paramMap = new HashMap<String, String>();
                        paramMap.put("prodId", actDctProdId);
                        List<Map> prodInfoList = this.setProdInfo(paramMap);
                        if (prodInfoList != null && prodInfoList.size() > 0) {
                            list.get(i).setActDctProdName(prodInfoList.get(0).get("PROD_NAME").toString());
                        }
                    }

                }

            }
        }
        return list;
    }

    /**
     * 描述: :限时打折 -- 有条件查询活动明细
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    public List<MarketTypeBo> queryActAll(MarketTypeBo bo) throws Exception {
        List<MarketTypeBo> list = new ArrayList<MarketTypeBo>();
        list = dao.queryActAll(bo);
        int j = list.size();
        if (j > 0) {
            for (int i = 0; i < j; i++) {
                MarketTypeBo typeBo = list.get(i);
                String allFree = typeBo.getAllFree();
                String dctType = typeBo.getDctType();
                String actDctCondUnit = typeBo.getActDctCondUnit();
                //整单
                if ("0100".equals(dctType) || BillConstants.DctType.ALL_ORDER_SER_AMT.equals(dctType)) {
                    list.get(i).setActDctProdName("无");
                } else {
                    // 0101：计价数量；0102：计价金额
                    if ("否".equals(allFree)) {
                        if ("0102".equals(dctType) && "".equals(actDctCondUnit)) {
                            list.get(i).setDctValue(typeBo.getDctValue() + "元");
                        } else {
                            CodeBO codeBo = codeService.getStandardCode("tariffUnit", actDctCondUnit, null);
                            if (codeBo != null) {
                                list.get(i).setDctValue(typeBo.getDctValue() + codeBo.getCodeName());
                            }
                        }
                    }

                    String actDctProdId = typeBo.getActDctProdId();
                    if (StringUtil.isNotBlank(actDctProdId)) {
                        if (BizConstants.PriceCode.CHARGE_PRICE_CODE.equals(actDctProdId)){
                            list.get(i).setActDctProdName("电费");
                        }else if (BizConstants.PriceCode.SERVICE_PRICE_CODE.equals(actDctProdId)){
                            list.get(i).setActDctProdName("服务费");
                        }else {
                            Map<String, String> paramMap = new HashMap<String, String>();
                            paramMap.put("prodId", actDctProdId);
                            List<Map> prodInfoList = this.setProdInfo(paramMap);
                            if (prodInfoList != null && prodInfoList.size() > 0) {
                                list.get(i).setActDctProdName(prodInfoList.get(0).get("PROD_NAME").toString());
                            }
                        }

                    }

                }

            }
        }
        return list;
    }

    /**
     * 描述: :获取活动优惠条件
     *
     * @param: [bo]
     * @return: com.ls.ner.billing.mktact.bo.ActInfoBo
     * 创建人:biaoxiangd
     * 创建时间:2017/6/27 20:42
     */
    public MarketTypeBo queryActCond(MarketTypeBo bo) throws Exception {
        MarketTypeBo marketTypeBo = new MarketTypeBo();

        String actId = bo.getActId();
        if (StringUtil.isBlank(actId)) {
            throw new RuntimeException("获取活动编号失败");
        }

        List<MarketTypeBo> list = dao.queryActCond(bo);
        if (list != null && list.size() > 0) {
            marketTypeBo = list.get(0);
        }
        return marketTypeBo;
    }

    /**
     * 描述:首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    @Override
    public int saveActCondDet(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            // 新增明细
            MarketTypeBo typeBo = new MarketTypeBo();
            BeanUtils.populate(typeBo, paramMap);
            String actCondId = typeBo.getActCondId();
            if (StringUtil.isNotBlank(actCondId)) {
                flag = dao.saveActCondDet(typeBo);
                String actCondDetId = typeBo.getActCondDetId();
                String dctType = typeBo.getDctType();
                String calcMethod = typeBo.getDctCalcMethod();
                List<Map> list = new ArrayList<Map>();
                if (BillConstants.DctType.ALL_ORDER_AMT.equals(dctType) || BillConstants.DctType.ALL_ORDER_SER_AMT.equals(dctType)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("actCondDetId", actCondDetId);
                    map.put("dctType", dctType);
                    map.put("dctCalcMethod", calcMethod);
                    map.put("dctCondUnit", null);
                    map.put("prodId", null);
                    if ("1".equals(paramMap.get("allFree"))){
                        map.put("dctCalcMethod","01");
                        map.put("dctValue", paramMap.get("dctCondValue"));
                    }else{
                        map.put("dctValue", paramMap.get("dctValue"));
                    }
                    map.put("allFree", paramMap.get("allFree"));


                    logger.debug(">>>>>保存优惠明细入参map：{}", map);
                    list.add(map);
                } else {
                    // 新增优惠内容
                    List<Map> actDetList = (List) paramMap.get("detItems");
                    if (actDetList != null && actDetList.size() > 0) {
                        //actCondDetId,dctType,prodId,allFree，dctValue，dctCondUnit
                        for (Map<String, Object> map : actDetList) {
                            map.put("actCondDetId", actCondDetId);
                            map.put("dctType", dctType);
                            String actDctProdId = map.get("actDctProdId").toString();
                            if (StringUtil.isNotBlank(actDctProdId)) {
                                String[] arr = actDctProdId.split("\\$");
                                map.put("prodId", arr[0]);
                            }
                            String allFree = (String) map.get("actDctAllFree");
                            if (StringUtil.isBlank(allFree)) {
                                map.put("allFree", "");
                                if ("0101".equals(dctType)) {
                                    map.put("dctCondUnit", map.get("actDctCondUnit").toString());
                                } else {
                                    map.put("dctCondUnit", "");
                                }
                            } else {
                                map.put("dctValue", null);
                                map.put("dctCondUnit", "");
                                map.put("allFree", allFree);
                            }
                            list.add(map);
                        }

                    } else {
                        throw new RuntimeException("优惠内容无数据");
                    }
                }
                logger.debug(">>>>>saveMketActDct 保存入参：{}", list);
                flag = dao.saveMketActDct(list);
            } else {
                throw new RuntimeException("未获取优惠内容明细信息，无法保存优惠内容");
            }
        } else {
            throw new RuntimeException("保存优惠内容参数错误");
        }

        return flag;
    }

    /**
     * 描述:首单免/满减满送 -- 优惠条件明细保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    @Override
    public int delActCondDet(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            String actCondDetId = String.valueOf(paramMap.get("actCondDetId"));
            String actDctId = String.valueOf(paramMap.get("actDctId"));
            MarketTypeBo bo = new MarketTypeBo();
            bo.setActCondDetId(actCondDetId);
            bo.setActDctId(actDctId);
            if (StringUtil.isNotBlank(actDctId)) {
                flag = dao.delMketActDct(bo);
            }
            if (StringUtil.isNotBlank(actCondDetId)) {
                int count = dao.queryMketActDctCount(bo);
                if (count == 0) {
                    flag = dao.delActCondDet(bo);
                }
            }
        } else {
            throw new RuntimeException("删除优惠明细参数错误");
        }
        return flag;
    }

    /**
     * 描述:查询优惠条件明细
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    @Override
    public List<MarketTypeBo> queryMktActCondDet(MarketTypeBo bo) throws Exception {
        return dao.queryMktActCondDet(bo);
    }

    /**
     * 描述:查询优惠内容
     *
     * @param: [bo]
     * @return: java.util.List<com.ls.ner.billing.mktact.bo.MarketTypeBo>
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 15:30
     */
    @Override
    public List<MarketTypeBo> queryMktActDct(MarketTypeBo bo) throws Exception {
        return dao.queryMktActDct(bo);
    }

    /**
     * 描述: 获取优惠内容
     *
     * @param: [paraMap]
     * @return: java.util.List<java.util.Map>
     * 创建人:biaoxiangd
     * 创建时间:2017/6/30 21:07
     */
    @Override
    public List<Map> setProdInfo(Map<String, String> paramMap) throws Exception {
        String actDctProdObj = paramMap.get("actDctProdObj");
        List<Map> retList = new ArrayList<Map>();
        Map<String, String> inMap = new HashMap<String, String>();
        inMap.put("prodId", paramMap.get("prodId"));
        List<Map> prodList = couponService.queryBProd(inMap);
        if (prodList != null && prodList.size() > 0) {
            for (Map<String, Object> map : prodList) {
                Map<String, Object> prodMap = new HashMap<String, Object>();
                String PROD_ID = map.get("PROD_ID") + "$" + map.get("MEASURE_METHOD");
                if (StringUtil.isBlank(actDctProdObj)) {
                    prodMap.put("PROD_ID", map.get("PROD_ID") + "$" + map.get("MEASURE_METHOD"));
                    prodMap.put("PROD_NAME", map.get("PROD_NAME"));
                    retList.add(prodMap);
                } else {
                    if (actDctProdObj.indexOf(PROD_ID) == -1) {
                        prodMap.put("PROD_ID", map.get("PROD_ID") + "$" + map.get("MEASURE_METHOD"));
                        prodMap.put("PROD_NAME", map.get("PROD_NAME"));
                        retList.add(prodMap);
                    }
                }

            }

        } else {
            Map<String, String> prodMap = new HashMap<String, String>();
            prodMap.put("PROD_ID", "0");
            prodMap.put("PROD_NAME", "无");
            retList.add(prodMap);
        }
        return retList;
    }


    /**
     * 描述:保存活动的优惠条件、优惠内容、优惠条件明细
     *
     * @param: [bo]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/7 1:29
     */
    @Override
    public int saveAll(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            MarketTypeBo delBo = new MarketTypeBo();
            String actId = paramMap.get("actId").toString();
            List<Map> actDetList = (List) paramMap.get("detItems");
            String actCondDetId = actDetList.get(0).get("actCondDetId").toString();
            // 先删除
            delBo.setActCondDetId(actCondDetId);
            dao.delMketActDct(delBo);
            delBo.setActCondId(String.valueOf(paramMap.get("actCondId")));
            dao.delActCondDet(delBo);
            delBo.setActId(actId);
            dao.delActCond(delBo);

            //后新增
            MarketTypeBo bo = new MarketTypeBo();
            bo.setDctCondFlag("2");
            bo.setDctCalcMethod(BillConstants.DctCalcMethod.DISCOUNT);
            bo.setActId(actId);
            bo.setDctLvl(null);
            bo.setDctCondType(null);
            bo.setProdId(null);
            bo.setDctCondUnit(null);
            flag = dao.saveActCond(bo);
            String actCondId = bo.getActCondId();
            if (StringUtil.isNotBlank(actCondId)) {
                paramMap.put("actCondId", actCondId);
                flag = this.saveActCondDet(paramMap);
            }
        } else {
            throw new RuntimeException("发布参数异常");
        }
        return flag;
    }

    /**
     * @param
     * @description
     * <AUTHOR>
     * @create 2019/6/2 4:19
     */
    @Override
    public int saveAllNew(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            MarketTypeBo delBo = new MarketTypeBo();
            String actId = paramMap.get("actId").toString();
            String actCondDetId = String.valueOf(paramMap.get("actCondDetId"));
            logger.info("删除的actCondDetId---------------"+actCondDetId);
            // 先删除
            if(StringUtil.isNotBlank(actCondDetId) && !"null".equals(actCondDetId)){
                delBo.setActCondDetId(actCondDetId);
                dao.delMketActDct(delBo);
                delBo.setActCondId(String.valueOf(paramMap.get("actCondId")));
                dao.delActCondDet(delBo);
                delBo.setActId(actId);
                dao.delActCond(delBo);
            }


            //后新增
            MarketTypeBo bo = new MarketTypeBo();
            bo.setDctCondFlag("2");
            bo.setDctCalcMethod(BillConstants.DctCalcMethod.DISCOUNT);
            bo.setActId(actId);
            bo.setDctLvl(null);
            bo.setDctCondType(null);
            bo.setProdId(null);
            bo.setDctCondUnit(null);
            flag = dao.saveActCond(bo);
            String actCondId = bo.getActCondId();
            if (StringUtil.isNotBlank(actCondId)) {
                paramMap.put("actCondId", actCondId);
                flag = this.saveActCondDet(paramMap);
            }
        } else {
            throw new RuntimeException("发布参数异常");
        }
        return flag;
    }

    /**
     * 描述:注册送/邀请送 -- 赠送内容保存
     *
     * @param: [paramMap]
     * @return: int
     * 创建人:biaoxiangd
     * 创建时间:2017/7/5 9:45
     */
    @Override
    public int saveMktActGive(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            MarketTypeBo bo = new MarketTypeBo();
            String actId = paramMap.get("actId").toString();
            String actCondId = StringUtil.getString(paramMap.get("actCondId"));
            String actCondDetId = StringUtil.getString(paramMap.get("actCondDetId"));
            if (actCondId == null || "".equals(actCondId)) {
                bo.setActId(actId);
                bo.setDctCondFlag("2");
                bo.setDctLvl(null);
                bo.setDctCondType(null);
                bo.setProdId(null);
                bo.setDctCondUnit(null);
                flag = dao.saveActCond(bo);
                actCondId = bo.getActCondId();
                // 添加优惠条件明细（冗余
                bo.setActCondId(actCondId);
                bo.setDctCondValue(null);
                bo.setDctType("");
                flag = dao.saveActCondDet(bo);
                actCondDetId = bo.getActCondDetId();
            }
            if (StringUtil.isNotBlank(actCondId)) {
                if (StringUtil.isNotBlank(actCondDetId)) {
                    //查询已经保存的优惠券
                    MarketActBo marketActBo = new MarketActBo();
                    marketActBo.setActId(Long.parseLong(actId));
                    List<CouponBo> couponBoList = couponDao.queryActCpn(marketActBo);
                    // 新增赠送内容
                    List<Map> actDetList = (List) paramMap.get("detItems");
                    List<Map> list = new ArrayList<Map>();
                    if (actDetList != null && actDetList.size() > 0) {
                        for (Map<String, Object> map : actDetList) {
                            int repFlag = 0;
                            for (CouponBo couponBo : couponBoList) {
                                //判断是否和数据库已经有的重复，如果重复不重复保存
                                logger.debug(">>>>>cpnId:{},map cpnId{}", couponBo.getCpnId(), map.get("cpnId").toString());
                                if (couponBo.getCpnId().equals(map.get("cpnId").toString())) {
                                    repFlag = 1;
                                    break;
                                }
                            }
                            if (repFlag == 0) {
                                map.put("actId", actId);
                                map.put("actCondDetId", actCondDetId);
                                map.put("cpnId", map.get("cpnId"));
                                list.add(map);
                            }
                        }
                        if (list != null && list.size() > 0) {
                            flag = dao.saveMktActGive(list);
                        } else {
                            flag = 1;
                        }
                    }else {
                        flag = 1;
                        return flag;
                    }
                } else {
                    throw new RuntimeException("新增活动得优惠明细异常");
                }
            } else {
                throw new RuntimeException("新增活动的优惠条件异常");
            }
        }
        return flag;
    }

    /**
     * @param paramMap
     * @description 注册送/邀请送 -- 优惠券删除
     * <AUTHOR>
     * @create 2017-08-09 10:50:01
     */
    @Override
    public int delMktActGive(Map<String, Object> paramMap) throws Exception {
        int flag = 0;
        if (paramMap != null && paramMap.size() > 0) {
            MarketTypeBo bo = new MarketTypeBo();
            String actId = paramMap.get("actId").toString();
            bo.setActId(actId);
            bo.setActCondId(paramMap.get("actCondId").toString());
//            dao.delActCond(bo);
            MarketActGiveBo actGiveBo = new MarketActGiveBo();
            actGiveBo.setActId(actId);
//            Object str = "["+paramMap.get("selItem").toString()+"]";
//            List<Map> selItemList = (List<Map>) str;
            actGiveBo.setCpnId(paramMap.get("cpnId").toString());
            flag = dao.delMktActGive(actGiveBo);
        }
        return flag;
    }

    /**
     * @description 查询活动关联的站点
     * <AUTHOR>
     * @create 2019-05-09 16:42:09
     */
    public List<MarketTypeBo> qryActStationList(Map<String, Object> actMap) {
        return dao.qryActStationList(actMap);
    }

    /**
     * @description 保存活动和站点关联
     * <AUTHOR>
     * @create 2019-05-09 16:57:34
     */
    public void saveActStation(List<Map<String, String>> actMap) {
        dao.saveActStation(actMap);
    }

    /**
     * @description 查询活动关联的站点条数
     * <AUTHOR>
     * @create 2019-05-09 17:33:02
     */
    public int qryActStationCount(Map<String, Object> serchMap) {
        return dao.qryActStationCount(serchMap);
    }

    /**
     * @description 删除关联站点
     * <AUTHOR>
     * @create 2019-05-09 17:43:18
     */
    public void delActStation(Map<String, Object> dataParams) {
        List<Map<String, Object>> saveList = (List<Map<String, Object>>) (dataParams.get("items"));
        if (CollectionUtils.isNotEmpty(saveList)) {
            for (Map<String, Object> map : saveList) {
                dao.delActStation(map);
            }
        }
    }

    public List<MarketTypeBo> queryActAllNew(MarketTypeBo bo) throws Exception {
        List<MarketTypeBo> list = new ArrayList<MarketTypeBo>();
        list = dao.queryActAll(bo);
        for (MarketTypeBo typeBo : list){
            String actDctProdId = typeBo.getActDctProdId();
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("itemNo", actDctProdId);
            Map<String,Object> itemMap = this.setItemInfoNew(paramMap);
            if (itemMap != null && itemMap.size() > 0) {
                typeBo.setActDctProdName(String.valueOf(itemMap.get("itemName")));
            }else {
                typeBo.setActDctProdName("无");
            }
        }

        return list;
    }

    @Override
    public Map setItemInfoNew(Map<String, String> paramMap) throws Exception {
        Map<String,Object> resultMap = new HashMap<>();
        ChargeSerItemQueryCondition condition = new ChargeSerItemQueryCondition();
        condition.setpBe("02");//业务大类：01租车 02 充电
        condition.setItemType("01");//项目类型：01服务02押金
        condition.setItemNo(paramMap.get("itemNo"));
        List<ChargeSerItemBo> list =chargeSerItemService.queryChargeSerItems(condition);
        resultMap.put("itemNo", list.get(0).getItemNo());
        resultMap.put("itemName",list.get(0).getItemName());
        return resultMap;
    }
}
