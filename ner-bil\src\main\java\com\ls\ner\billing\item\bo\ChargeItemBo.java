/**
 *
 * @(#) ChargeItemBo.java
 * @Package com.ls.ner.billing.item.bo
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.item.bo;

import com.pt.poseidon.api.framework.DicAttribute;
import com.pt.poseidon.webcommon.rest.object.QueryCondition;

/**
 * Description :费用项
 * 
 * @author: lipf
 * 
 *          History: 2015年7月3日 下午4:58:37 lipf Created.
 * 
 */
public class ChargeItemBo extends QueryCondition {

	private String itemId;
	private String chargeItemCode;
	private String itemName;
	private String chargeType;
	private String needFlag;
	private String itemDesc;
	private String effectFlag;
	private String buildDate;
	private String timeLine;

	private String msg;

	private String billingType;
	private String appNo;
	private String pricingAmt;
	@DicAttribute(dicName = "codeDict", key = "chargeType", subType = "chargeType")
	private String chargeTypeName;
	@DicAttribute(dicName = "codeDict", key = "needFlag", subType = "ynJudgeFlag")
	private String needFlagName;
	@DicAttribute(dicName = "codeDict", key = "timeLine", subType = "timeLine")
	private String timeLineName;

	public String getItemId() {
		return itemId;
	}

	public void setItemId(String itemId) {
		this.itemId = itemId;
	}

	public String getChargeItemCode() {
		return chargeItemCode;
	}

	public void setChargeItemCode(String chargeItemCode) {
		this.chargeItemCode = chargeItemCode;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getChargeType() {
		return chargeType;
	}

	public void setChargeType(String chargeType) {
		this.chargeType = chargeType;
	}

	public String getNeedFlag() {
		return needFlag;
	}

	public void setNeedFlag(String needFlag) {
		this.needFlag = needFlag;
	}

	public String getItemDesc() {
		return itemDesc;
	}

	public void setItemDesc(String itemDesc) {
		this.itemDesc = itemDesc;
	}

	public String getEffectFlag() {
		return effectFlag;
	}

	public void setEffectFlag(String effectFlag) {
		this.effectFlag = effectFlag;
	}

	public String getBuildDate() {
		return buildDate;
	}

	public void setBuildDate(String buildDate) {
		this.buildDate = buildDate;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getChargeTypeName() {
		return chargeTypeName;
	}

	public void setChargeTypeName(String chargeTypeName) {
		this.chargeTypeName = chargeTypeName;
	}

	public String getNeedFlagName() {
		return needFlagName;
	}

	public void setNeedFlagName(String needFlagName) {
		this.needFlagName = needFlagName;
	}

	public String getBillingType() {
		return billingType;
	}

	public void setBillingType(String billingType) {
		this.billingType = billingType;
	}

	public String getAppNo() {
		return appNo;
	}

	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}

	public String getPricingAmt() {
		return pricingAmt;
	}

	public void setPricingAmt(String pricingAmt) {
		this.pricingAmt = pricingAmt;
	}

	public String getTimeLine() {
		return timeLine;
	}

	public void setTimeLine(String timeLine) {
		this.timeLine = timeLine;
	}

	public String getTimeLineName() {
		return timeLineName;
	}

	public void setTimeLineName(String timeLineName) {
		this.timeLineName = timeLineName;
	}

}
