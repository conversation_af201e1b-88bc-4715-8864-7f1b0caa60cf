package com.ls.ner.billing.common.dao;

import com.ls.ner.billing.common.bo.StepConfigBo;
import com.ls.ner.billing.common.bo.StepConfigBoExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface StepConfigBoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int countByExample(StepConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByExample(StepConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int deleteByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insert(StepConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int insertSelective(StepConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    List<StepConfigBo> selectByExample(StepConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    StepConfigBo selectByPrimaryKey(Long systemId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExampleSelective(@Param("record") StepConfigBo record, @Param("example") StepConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByExample(@Param("record") StepConfigBo record, @Param("example") StepConfigBoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKeySelective(StepConfigBo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table e_step_config
     *
     * @mbggenerated Fri Mar 04 21:08:25 CST 2016
     */
    int updateByPrimaryKey(StepConfigBo record);
}