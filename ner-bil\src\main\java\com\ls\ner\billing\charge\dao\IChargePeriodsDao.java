package com.ls.ner.billing.charge.dao;

import java.util.List;

import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 充电分时设置  E_CHARGE_PERIODS
 */
public interface IChargePeriodsDao {
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电分时设置  E_CHARGE_PERIODS
	 */
	public List<ChargePeriodsBo> queryChargePeriods(ChargeBillingConfQueryCondition bo);

	/**
	 * @Description 通过计费编号查询分时设置
	 * <AUTHOR>
	 * @Date 2022/6/21 15:09
	 * @param chcNo
	 */
	List<ChargePeriodsBo> selectChargePeriodsByChcNo(@Param("chcNo") String chcNo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电分时设置  E_CHARGE_PERIODS
	 */
	public int queryChargePeriodsNum(ChargeBillingConfQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增充电分时设置  E_CHARGE_PERIODS
	 */
	public void insertChargePeriods(ChargePeriodsBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新充电分时设置  E_CHARGE_PERIODS
	 */
	public void updateChargePeriods(ChargePeriodsBo bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 删除充电分时设置  E_CHARGE_PERIODS
	 */
	public void deleteChargePeriods(ChargePeriodsBo bo);
}
