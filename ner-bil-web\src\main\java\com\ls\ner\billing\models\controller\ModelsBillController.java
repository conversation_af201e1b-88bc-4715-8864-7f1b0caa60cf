/**
 *
 * @(#) ModelsBillController.java
 * @Package com.ls.ner.billing.models.controller
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.models.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.ls.ner.base.constants.PublicConstants;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.Validate;
import org.owasp.esapi.ESAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ls.ner.ast.api.archives.service.IArchivesRpcService;
import com.ls.ner.base.constants.BizConstants;
import com.ls.ner.billing.api.BillingConstants.SubBe;
import com.ls.ner.billing.models.bo.ModelsBillBo;
import com.ls.ner.billing.models.service.IModelsBillService;
import com.pt.eunomia.api.account.IAccountService;
import com.pt.eunomia.api.account.bo.AccountBo;
import com.pt.eunomia.api.security.Authentication;
import com.pt.poseidon.api.framework.ServiceAutowired;
import com.pt.poseidon.api.framework.ServiceType;
import com.pt.poseidon.code.api.ICodeService;
import com.pt.poseidon.code.api.bo.CodeBO;
import com.pt.poseidon.common.utils.tools.StringUtils;
import com.pt.poseidon.org.api.IOrgService;
import com.pt.poseidon.org.api.bo.OrgBo;
import com.pt.poseidon.ui.taglib.utils.TagUtil;
import com.pt.poseidon.webcommon.rest.annotation.IdRequestBody;
import com.pt.poseidon.webcommon.rest.annotation.ItemResponseBody;
import com.pt.poseidon.webcommon.rest.annotation.QueryRequestParam;
import com.pt.poseidon.webcommon.rest.object.IDRequestObject;
import com.pt.poseidon.webcommon.rest.object.QueryController;
import com.pt.poseidon.webcommon.rest.object.QueryResultObject;
import com.pt.poseidon.webcommon.rest.object.RequestCondition;
import com.pt.poseidon.webcommon.rest.object.WrappedResult;
import com.pt.poseidon.webcommon.rest.utils.RestUtils;

/**
 *  Description : 车型租赁设置
 * 
 *  @author:  qixiyu
 *
 *  History:  2016年10月27日 下午8:39:16   qixiyu   Created.
 *           
 */
@Controller
@RequestMapping("/billing/models")
public class ModelsBillController extends QueryController<ModelsBillBo> {
	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private IOrgService orgService;
	
	@ServiceAutowired("modelsBillService")
	private IModelsBillService modelsBillService;
	
	@Autowired
	private Authentication authentication;
	
	@ServiceAutowired(serviceTypes=ServiceType.RPC, value="accountService")
	private IAccountService accountService;
	
	@ServiceAutowired(serviceTypes=ServiceType.RPC)
	private ICodeService codeService;
	
	@ServiceAutowired(serviceTypes = ServiceType.RPC, value="archivesRpcService")
	private IArchivesRpcService archivesRpcService;
	
	/**
	 * 
	 * Method description : 车型租赁设置页面初始化
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月3日 上午10:48:36
	 * History:  2016年11月3日 上午10:48:36   qixiyu   Created.
	 *
	 * @param m
	 * @param request
	 * @return
	 *
	 */
	@RequestMapping(value = "/init", method = RequestMethod.GET)
	public String init(Model m, HttpServletRequest request) {
		//flag用于选择车型租赁方式界面
		String flag=ESAPI.encoder().encodeForJavaScript(request.getParameter("flag"));
		if(!StringUtils.nullOrBlank(flag)){
			m.addAttribute("subBe", ESAPI.encoder().encodeForJavaScript(request.getParameter("subBe")));
		}
		request.setAttribute("flag", flag);
		List<CodeBO> autoLevelList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_LEVEL,null);
		List<CodeBO> autoBrandList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_BRAND,null);
		m.addAttribute("autoLevelList", autoLevelList);//车辆级别
		m.addAttribute("autoBrandList", autoBrandList);//品牌
		m.addAttribute("searchForm", new ModelsBillBo());
		m.addAttribute("astPath", PublicConstants.ApplicationPath.getAstPath());
		return "/bil/models/modelsRental";
	}
	
	/**
	 * 
	 * Method description : 车型租赁设置树根节点数据获取
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月3日 上午10:48:56
	 * History:  2016年11月3日 上午10:48:56   qixiyu   Created.
	 *
	 * @param request
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping(value = "/getModelRentalTree", method = RequestMethod.GET)
	@ResponseBody
	public Object getModelRentalTree(HttpServletRequest request) throws Exception {
		List<OrgBo> subOrgList = new ArrayList<OrgBo>();
		AccountBo accountBo = authentication.getCurrentAccount();
//		if (this.accountService.isAdmin(accountBo.getAccountName())) {
			subOrgList = this.orgService.getRootOrgs();
//		} else {
//			OrgBo orgBo = orgService.getOrgByAccountName(accountBo.getAccountName());
//			if(orgBo != null){
//				subOrgList.add(this.orgService.getOrgByNo(orgBo.getOrgCode()));
//			} else {
//				Validate.isTrue(false,"当前登录人员没有对应公司，或是会话已失效");
//			}
//		}
		Map<String,Object> returnMap = new HashMap<String, Object>();
		returnMap.put("nodes", tranOrg2tree(subOrgList, request));
		return WrappedResult.successWrapedResult(returnMap);
	}
	/**
	 * 
	 * 方法说明：车型租赁设置树子节点数据获取
	 *
	 * Author：        lipf                
	 * Create Date：   2016年5月30日 上午9:39:07
	 * History:  2016年5月30日 上午9:39:07   lipf   Created.
	 *
	 * @param request
	 * @param orgCode
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping(value = "/getModelRentalTree/{orgCode}", method = RequestMethod.GET)
	@ResponseBody
	public Object getOrgSubTree(HttpServletRequest request, @PathVariable("orgCode")
	String orgCode) throws Exception {
		List<OrgBo> subOrgList = new ArrayList<OrgBo>();
		Map<String,Object> returnMap = new HashMap<String, Object>();
		List<Map<String,Object>> subTreeList = new ArrayList<Map<String,Object>>();
		List<CodeBO> rentTypeList = codeService.getAllStandardCodes(SubBe.CODE_TYPE,null);
		for (int i = 0; i < rentTypeList.size(); i++) {
			CodeBO code = rentTypeList.get(i);
			if("02,03,04".indexOf(code.getCodeValue())>-1){
				rentTypeList.remove(code);
				i--;
			}
		}
		ModelsBillBo condition = new ModelsBillBo();
		Map<String,Object> subNode = null ;
		
//		if ("root".equals(orgCode)) {
//			subOrgList = this.orgService.getRootOrgs();
//		} else {
//			subOrgList = this.orgService.getChildOrgs(orgCode);
//		}
		
		
//		condition.setOrgCode(orgCode);
		int dayNum = 0;
		int hourNum = 0;
		Map<String,Integer> map = new HashMap<String, Integer>();
		List<ModelsBillBo> list = modelsBillService.getModelRentalList(condition);
		if(list!=null&&list.size()>0){
			for (ModelsBillBo ModelsBillBo : list) {
				if(map.get(ModelsBillBo.getSubBe())==null||map.get(ModelsBillBo.getSubBe())==0){
					map.put(ModelsBillBo.getSubBe(), 1);
				}else{
					map.put(ModelsBillBo.getSubBe(), map.get(ModelsBillBo.getSubBe())+1);
				}
//				if("02".equals(ModelsBillBo.getSubBe())){
//					dayNum++;
//				} else if("01".equals(ModelsBillBo.getSubBe())) {
//					hourNum++;
//				}
			}
		}
		String bundleUrl = TagUtil.getBundleUrl(request);
		if(rentTypeList!=null&&rentTypeList.size()>0){
			for (CodeBO codeBo : rentTypeList) {
				subNode = new HashMap<String, Object>();
				subNode.put("id", orgCode+"_"+codeBo.getCodeValue());
				subNode.put("text", codeBo.getCodeName()+"("+(map.get(codeBo.getCodeValue())==null?0:map.get(codeBo.getCodeValue()))+")");
				subNode.put("hasChildren", false);
				subNode.put("parentCode", orgCode);
				subNode.put("item", orgCode);
//				subNode.put("icon", bundleUrl + "source/image/org.gif");
				subNode.put("itemType", "subBe");
				subNode.put("orgCode", orgCode);
				subNode.put("subBe", codeBo.getCodeValue());
				subTreeList.add(subNode);
			}
		}
//		subNode = new HashMap<String, Object>();
//		subNode.put("id", orgCode+"_"+SubBe.DAY);
//		
//		subNode.put("text", "日租("+dayNum+")");
//		subNode.put("hasChildren", false);
//		subNode.put("parentCode", orgCode);
//		subNode.put("item", orgCode);
////		subNode.put("icon", bundleUrl + "source/image/org.gif");
//		subNode.put("itemType", "subBe");
//		subNode.put("orgCode", orgCode);
//		subNode.put("subBe", "02");
//		subTreeList.add(subNode);
//		subNode = new HashMap<String, Object>();
//		subNode.put("id", orgCode+"_"+SubBe.HOUR);
//		subNode.put("text", "时租("+hourNum+")");
//		subNode.put("hasChildren", false);
//		subNode.put("parentCode", orgCode);
//		subNode.put("item", orgCode);
////		subNode.put("icon", bundleUrl + "source/image/org.gif");
//		subNode.put("itemType", "subBe");
//		subNode.put("orgCode", orgCode);
//		subNode.put("subBe", "01");
//		subTreeList.add(subNode);
//		subTreeList.addAll(tranOrg2tree(subOrgList, request));
		returnMap.put("nodes",subTreeList);
		return WrappedResult.successWrapedResult(returnMap);
	}
	
	private List<Map<String,Object>> tranOrg2tree(List<OrgBo> subOrgList,HttpServletRequest request){
		List<Map<String,Object>> subTreeList = new ArrayList<Map<String,Object>>();
		for (int i = 0; i < subOrgList.size(); i++) {
			OrgBo orgBo = subOrgList.get(i);
			Map<String,Object> subNode = new HashMap<String, Object>();
			String bundleUrl = TagUtil.getBundleUrl(request);
			subNode.put("id", orgBo.getOrgCode());
			subNode.put("text", orgBo.getOrgShortName());
			subNode.put("hasChildren", true);
			subNode.put("parentCode", orgBo.getOrgParentCode());
			subNode.put("item", orgBo);
//			subNode.put("icon", bundleUrl + "source/image/org.gif");
			subNode.put("itemType", "org");
			subNode.put("orgCode", orgBo.getOrgCode());
			subNode.put("subBe", "");
			subTreeList.add(subNode);
		}
		return subTreeList;
	}
	
	/**
	 * 
	 * Method description : 车型租赁设置表格数据查询
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月3日 上午10:49:26
	 * History:  2016年11月3日 上午10:49:26   qixiyu   Created.
	 *
	 * @param params
	 * @return
	 * @throws Exception
	 *
	 */
	@RequestMapping(value = "/getModelRentalList")
	public @ItemResponseBody QueryResultObject getModelRentalList(@QueryRequestParam("params") RequestCondition params) throws Exception {
		ModelsBillBo condition = this.rCondition2QCondition(params);
//		condition.setPageBegin(condition.getPageBegin() - 1);
//		if(StringUtils.nullOrBlank(condition.getOrgCode())){
//			AccountBo currentAccount = authentication.getCurrentAccount();
//			OrgBo orgBo = orgService.getOrgByAccountName(currentAccount.getAccountName());
//			if(orgBo!=null){
//				condition.setOrgCode(orgBo.getOrgCode());
//			}
//		}
//		condition.setOrgCode("4600001");
		List<ModelsBillBo> list = modelsBillService.getModelRentalList(condition);
		if(list!=null&&list.size()>0){
			Map<String, Object> inMap = Maps.newHashMap();
			List<Map<String,Object>> listMap = Lists.newArrayList();
			Map<String, Object> temp = null;
			for (ModelsBillBo modelsBillBo : list) {
				temp = Maps.newHashMap();
				temp.put("modelId", modelsBillBo.getModelId());
				listMap.add(temp);
			}
			inMap.put("modelList", listMap);
			List<Map<String,Object>> retMap = archivesRpcService.queryModels(inMap);
			Map<String, ModelsBillBo> tempBo = new HashMap<String, ModelsBillBo>();
			if(retMap!=null&&retMap.size()>0){
				for (Map<String, Object> map : retMap) {
					ModelsBillBo bo = new ModelsBillBo();
					BeanUtils.populate(bo, map);
					tempBo.put(map.get("modelId")+"", bo);
				}
			}
			for (ModelsBillBo modelsBillBo : list) {
				if(tempBo.get(modelsBillBo.getModelId())==null){
					continue;
				}
				modelsBillBo.setModelName(tempBo.get(modelsBillBo.getModelId()).getModelName());
				modelsBillBo.setBrandName(tempBo.get(modelsBillBo.getModelId()).getBrandName());
				modelsBillBo.setSpec(tempBo.get(modelsBillBo.getModelId()).getSpec());
				if (!StringUtils.nullOrBlank(modelsBillBo.getOrgCode())) {
					OrgBo org = orgService.getOrgByNo(modelsBillBo.getOrgCode());
					if (org != null)
						modelsBillBo.setOrgCodeName(org.getOrgShortName());
				}
			}
		}
		int recordCount = modelsBillService.getModelRentalListNum(condition);
		return RestUtils.wrappQueryResult(list, recordCount);
	}

//	@RequestMapping(value = "/addComModelRentalInit")
//	public String addComModelRentalInit(Model m, @RequestParam("subBe")
//	String subBe,@RequestParam("orgCode")
//	String orgCode) {
//		ModelsBillBo bo = new ModelsBillBo();
//		bo.setOrgCode(orgCode);
//		bo.setSubBe(subBe);
//		addModelCode(m);
//		m.addAttribute("addComModelRentalInitForm", bo);
//		return "/asset/models/addComModelRentalInit";
//	}
//	
//	@RequestMapping(value = "/queryAutoNotExistRentalList")
//	public @ItemResponseBody QueryResultObject queryAutoNotExistRentalList(@QueryRequestParam("params") RequestCondition params) {
//		ModelsBillBo condition = this.rCondition2QCondition(params);
//		List<ModelsBillBo> list = modelsBillService.queryAutoNotExistRentalList(condition);
//		int recordCount = modelsBillService.queryAutoNotExistRentalNum(condition);
//		return RestUtils.wrappQueryResult(list, recordCount);
//	}
	
	/**
	 * 
	 * Method description : 车型租赁设置数据保存
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月3日 上午10:49:41
	 * History:  2016年11月3日 上午10:49:41   qixiyu   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	@RequestMapping(value = "/saveAutoModelRental")
	public @ItemResponseBody
	QueryResultObject saveAutoModelRental(@RequestBody ModelsBillBo bo) {
		String mark = "N";
		try {
			modelsBillService.saveModelRental(bo);
			mark = "Y";
		} catch (Exception e) {
			e.printStackTrace();
			mark = e.getMessage();
		}

		return RestUtils.wrappQueryResult(mark);
	}
	
	/**
	 * 
	 * Method description : 车型租赁设置删除
	 *
	 * Author：        qixiyu                
	 * Create Date：   2016年11月3日 上午10:49:53
	 * History:  2016年11月3日 上午10:49:53   qixiyu   Created.
	 *
	 * @param idObject
	 * @return
	 *
	 */
	@RequestMapping("/delComModelRental")
	public @ItemResponseBody
	QueryResultObject delComModelRental(@IdRequestBody
	IDRequestObject idObject) {
		String mark = "N";
		try {
			String[] ids = idObject.getIds();
			modelsBillService.delModelRentals(ids);
			mark = "Y";
		} catch (Exception e) {
			e.printStackTrace();
			mark = e.getMessage();
		}

		return RestUtils.wrappQueryResult(mark);
	}
	
	private void addModelCode(Model m){
		List<CodeBO> autoTypeList = codeService.getStandardCodes("autoType",null);
		List<CodeBO> autoBrandList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_BRAND,null);
		List<CodeBO> autoSpecList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_SPEC,null);
		List<CodeBO> autoSeriesList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_ASERIES, null);
		List<CodeBO> modelYearList = codeService.getStandardCodes(BizConstants.RentalType.MODEL_YEAR, null);
		List<CodeBO> displacementList = codeService.getStandardCodes(BizConstants.RentalType.DISPLACEMENT, null);
		List<CodeBO> fuelLableList = codeService.getStandardCodes(BizConstants.RentalType.FUELLABLE, null);
		List<CodeBO> gearBoxList = codeService.getStandardCodes(BizConstants.RentalType.GEARBOX, null);
		List<CodeBO> engineTypeList = codeService.getStandardCodes(BizConstants.RentalType.ENGINE_TYPE, null);
		List<CodeBO> manuFacturerList = codeService.getStandardCodes(BizConstants.RentalType.AUTO_MANFAC, null);
		m.addAttribute("autoTypeList", autoTypeList);//车辆类型
		m.addAttribute("autoBrandList", autoBrandList);//品牌
		m.addAttribute("autoSeriesList", autoSeriesList);//车系
		m.addAttribute("modelYearList", modelYearList);//年款
		m.addAttribute("displacementList", displacementList);//排量
		m.addAttribute("autoSpecList", autoSpecList);//规格
		m.addAttribute("fuelLableList", fuelLableList);//燃油标号
		m.addAttribute("engineTypeList", engineTypeList);//动力类型
		m.addAttribute("gearBoxList", gearBoxList);//变速箱
		m.addAttribute("manufacturerList", manuFacturerList);//变速箱
	}

	@Override
	protected ModelsBillBo initCondition() {
		return new ModelsBillBo();
	}
}
