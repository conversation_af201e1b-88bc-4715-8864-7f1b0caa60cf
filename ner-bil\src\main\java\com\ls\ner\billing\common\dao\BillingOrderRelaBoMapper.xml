<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ls.ner.billing.common.dao.BillingOrderRelaBoMapper">
  <resultMap id="BaseResultMap" type="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    <id column="SYSTEM_ID" jdbcType="BIGINT" property="systemId" />
    <result column="APP_NO" jdbcType="VARCHAR" property="appNo" />
    <result column="RELA_NO" jdbcType="VARCHAR" property="relaNo" />
    <result column="BILL_TYPE" jdbcType="VARCHAR" property="billType" />
    <result column="SUB_BE" jdbcType="VARCHAR" property="subBe" />
    <result column="ORG_AUTO_MODEL_KEY" jdbcType="VARCHAR" property="orgAutoModelKey" />
    <result column="VERSION_LIMIT" jdbcType="VARCHAR" property="versionLimit" />
    <result column="DEFAULT_BILLING_NO" jdbcType="VARCHAR" property="defaultBillingNo" />
    <result column="SELECTED_ATTACH_ITEM_NOS" jdbcType="VARCHAR" property="selectedAttachItemNos" />
    <result column="DATE_OPER_TYPE" jdbcType="VARCHAR" property="dateOperType" />
    <result column="DATA_OPER_TIME" jdbcType="TIMESTAMP" property="dataOperTime" />
    <result column="PRICING_AMT" jdbcType="DECIMAL" property="pricingAmt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    SYSTEM_ID, APP_NO, RELA_NO, BILL_TYPE, SUB_BE, ORG_AUTO_MODEL_KEY, VERSION_LIMIT, DEFAULT_BILLING_NO, 
    SELECTED_ATTACH_ITEM_NOS, DATE_OPER_TYPE, DATA_OPER_TIME, PRICING_AMT
  </sql>
  <select id="selectByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderRelaBoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from e_billing_order_rela
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    select 
    <include refid="Base_Column_List" />
    from e_billing_order_rela
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    delete from e_billing_order_rela
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderRelaBoExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    delete from e_billing_order_rela
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    insert into e_billing_order_rela (SYSTEM_ID, APP_NO, SUB_BE, 
      ORG_AUTO_MODEL_KEY, VERSION_LIMIT, DEFAULT_BILLING_NO, 
      SELECTED_ATTACH_ITEM_NOS, DATE_OPER_TYPE, DATA_OPER_TIME, 
      PRICING_AMT)
    values (#{systemId,jdbcType=BIGINT}, #{appNo,jdbcType=VARCHAR}, #{subBe,jdbcType=VARCHAR}, 
      #{orgAutoModelKey,jdbcType=VARCHAR}, #{versionLimit,jdbcType=VARCHAR}, #{defaultBillingNo,jdbcType=VARCHAR}, 
      #{selectedAttachItemNos,jdbcType=VARCHAR}, #{dateOperType,jdbcType=VARCHAR}, #{dataOperTime,jdbcType=TIMESTAMP}, 
      #{pricingAmt,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    insert into e_billing_order_rela
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        SYSTEM_ID,
      </if>
      <if test="appNo != null">
        APP_NO,
      </if>
      <if test="relaNo != null">
        RELA_NO,
      </if>
      <if test="billType != null">
        BILL_TYPE,
      </if>
      <if test="subBe != null">
        SUB_BE,
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY,
      </if>
      <if test="versionLimit != null">
        VERSION_LIMIT,
      </if>
      <if test="defaultBillingNo != null">
        DEFAULT_BILLING_NO,
      </if>
      <if test="selectedAttachItemNos != null">
        SELECTED_ATTACH_ITEM_NOS,
      </if>
      <if test="dateOperType != null">
        DATE_OPER_TYPE,
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME,
      </if>
      <if test="pricingAmt != null">
        PRICING_AMT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="systemId != null">
        #{systemId,jdbcType=BIGINT},
      </if>
      <if test="appNo != null">
        #{appNo,jdbcType=VARCHAR},
      </if>
      <if test="relaNo != null">
        #{relaNo,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="versionLimit != null">
        #{versionLimit,jdbcType=VARCHAR},
      </if>
      <if test="defaultBillingNo != null">
        #{defaultBillingNo,jdbcType=VARCHAR},
      </if>
      <if test="selectedAttachItemNos != null">
        #{selectedAttachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="dateOperType != null">
        #{dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingAmt != null">
        #{pricingAmt,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ls.ner.billing.common.bo.BillingOrderRelaBoExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    select count(*) from e_billing_order_rela
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    update e_billing_order_rela
    <set>
      <if test="record.systemId != null">
        SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      </if>
      <if test="record.appNo != null">
        APP_NO = #{record.appNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subBe != null">
        SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      </if>
      <if test="record.orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="record.versionLimit != null">
        VERSION_LIMIT = #{record.versionLimit,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultBillingNo != null">
        DEFAULT_BILLING_NO = #{record.defaultBillingNo,jdbcType=VARCHAR},
      </if>
      <if test="record.selectedAttachItemNos != null">
        SELECTED_ATTACH_ITEM_NOS = #{record.selectedAttachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOperType != null">
        DATE_OPER_TYPE = #{record.dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataOperTime != null">
        DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pricingAmt != null">
        PRICING_AMT = #{record.pricingAmt,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    update e_billing_order_rela
    set SYSTEM_ID = #{record.systemId,jdbcType=BIGINT},
      APP_NO = #{record.appNo,jdbcType=VARCHAR},
      SUB_BE = #{record.subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{record.orgAutoModelKey,jdbcType=VARCHAR},
      VERSION_LIMIT = #{record.versionLimit,jdbcType=VARCHAR},
      DEFAULT_BILLING_NO = #{record.defaultBillingNo,jdbcType=VARCHAR},
      SELECTED_ATTACH_ITEM_NOS = #{record.selectedAttachItemNos,jdbcType=VARCHAR},
      DATE_OPER_TYPE = #{record.dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{record.dataOperTime,jdbcType=TIMESTAMP},
      PRICING_AMT = #{record.pricingAmt,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    update e_billing_order_rela
    <set>
      <if test="appNo != null">
        APP_NO = #{appNo,jdbcType=VARCHAR},
      </if>
      <if test="subBe != null">
        SUB_BE = #{subBe,jdbcType=VARCHAR},
      </if>
      <if test="orgAutoModelKey != null">
        ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      </if>
      <if test="versionLimit != null">
        VERSION_LIMIT = #{versionLimit,jdbcType=VARCHAR},
      </if>
      <if test="defaultBillingNo != null">
        DEFAULT_BILLING_NO = #{defaultBillingNo,jdbcType=VARCHAR},
      </if>
      <if test="selectedAttachItemNos != null">
        SELECTED_ATTACH_ITEM_NOS = #{selectedAttachItemNos,jdbcType=VARCHAR},
      </if>
      <if test="dateOperType != null">
        DATE_OPER_TYPE = #{dateOperType,jdbcType=VARCHAR},
      </if>
      <if test="dataOperTime != null">
        DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pricingAmt != null">
        PRICING_AMT = #{pricingAmt,jdbcType=DECIMAL},
      </if>
    </set>
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ls.ner.billing.api.common.bo.BillingOrderRelaBo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 16 15:59:45 CST 2016.
    -->
    update e_billing_order_rela
    set APP_NO = #{appNo,jdbcType=VARCHAR},
      SUB_BE = #{subBe,jdbcType=VARCHAR},
      ORG_AUTO_MODEL_KEY = #{orgAutoModelKey,jdbcType=VARCHAR},
      VERSION_LIMIT = #{versionLimit,jdbcType=VARCHAR},
      DEFAULT_BILLING_NO = #{defaultBillingNo,jdbcType=VARCHAR},
      SELECTED_ATTACH_ITEM_NOS = #{selectedAttachItemNos,jdbcType=VARCHAR},
      DATE_OPER_TYPE = #{dateOperType,jdbcType=VARCHAR},
      DATA_OPER_TIME = #{dataOperTime,jdbcType=TIMESTAMP},
      PRICING_AMT = #{pricingAmt,jdbcType=DECIMAL}
    where SYSTEM_ID = #{systemId,jdbcType=BIGINT}
  </update>
</mapper>