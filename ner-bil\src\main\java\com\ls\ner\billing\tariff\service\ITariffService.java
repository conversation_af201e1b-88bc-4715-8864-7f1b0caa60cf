/**
 *
 * @(#) ITariffService.java
 * @Package com.ls.ner.billing.tariff.service
 * 
 * Copyright © Longshine Corporation. All rights reserved.
 *
 */

package com.ls.ner.billing.tariff.service;

import java.util.List;

import com.ls.ner.billing.tariff.bo.AttachItemBo;
import com.ls.ner.billing.tariff.bo.TariffPlanBo;

/**
 *  类描述：
 * 
 *  @author:  lipf
 *  @version  $Id: ITariffService.java,v 1.1 2016/02/26 02:34:01 34544 Exp $ 
 *
 *  History:  2016年2月23日 下午5:30:56   lipf   Created.
 *           
 */
public interface ITariffService {

	/**
	 * 
	 * 方法说明：
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月23日 下午5:31:08
	 * History:  2016年2月23日 下午5:31:08   lipf   Created.
	 *
	 * @param dataList
	 * @param formBo
	 *
	 */
	void saveTariffPlan(List<AttachItemBo> dataList, TariffPlanBo formBo);

	/**
	 * 
	 * 方法说明：
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月23日 下午5:31:15
	 * History:  2016年2月23日 下午5:31:15   lipf   Created.
	 *
	 * @param dataList
	 * @param formBo
	 *
	 */
	void updateTariffPlan(List<AttachItemBo> dataList, TariffPlanBo formBo);

	/**
	 * 
	 * 方法说明：资费查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:57:10
	 * History:  2016年2月24日 上午9:57:10   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	List<TariffPlanBo> getTariff(TariffPlanBo bo);

	/**
	 * 
	 * 方法说明：资费数量查询
	 *
	 * Author：        lipf                
	 * Create Date：   2016年2月24日 上午9:57:23
	 * History:  2016年2月24日 上午9:57:23   lipf   Created.
	 *
	 * @param bo
	 * @return
	 *
	 */
	int getTariffCount(TariffPlanBo bo);

}
