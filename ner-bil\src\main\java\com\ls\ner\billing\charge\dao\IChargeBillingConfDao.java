package com.ls.ner.billing.charge.dao;

import java.util.List;

import com.ls.ner.billing.api.charge.bo.ChargeBillConfigServiceModeBO;
import com.ls.ner.billing.api.charge.condition.ChargeBillingConfQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.ChargeBillingConfHisQueryCondition;
import com.ls.ner.billing.api.xpcharge.condition.XpChargeBillingConfQueryCondition;
import com.ls.ner.billing.charge.bo.ChargeBillingConfBo;
import com.ls.ner.billing.charge.bo.ChargeBillingConfHistoryBo;
import com.ls.ner.billing.charge.bo.ChargePeriodsBo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;


/**
 * <AUTHOR>
 * @dateTime 2016-03-24
 * @description 充电计费配置  E_CHARGE_BILLING_CONF
 */
public interface IChargeBillingConfDao {
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置E_CHARGE_BILLING_CONF
	 */
	public List<ChargeBillingConfBo> queryChargeBillingConfs(ChargeBillingConfQueryCondition bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置条数E_CHARGE_BILLING_CONF 
	 */
	public int queryChargeBillingConfsNum(ChargeBillingConfQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-04-08
	 * @description 查询同一个生效时间、同一个充电站有多少条记录
	 */
	public int queryCbcByEftDateAndStationId(ChargeBillingConfBo bo);
	
	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 新增充电计费配置E_CHARGE_BILLING_CONF
	 */
	public void insertChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 更新充电计费配置E_CHARGE_BILLING_CONF
	 */
	public void updateChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 删除充电计费配置E_CHARGE_BILLING_CONF
	 */
	public void deleteChargeBillingConf(ChargeBillingConfBo bo);

	/**
	 * 查询计费配置
	 * @param condition
	 * @return
	 */
	List<ChargeBillingConfBo> queryChargeBillingMap(ChargeBillingConfQueryCondition condition);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置--新版
	 */
	public List<ChargeBillingConfBo> queryNewChargeBillingConfs(ChargeBillingConfQueryCondition bo);

	/**
	 * <AUTHOR>
	 * @dateTime 2016-03-24
	 * @description 查询充电计费配置--新版
	 */
	public List<ChargeBillingConfBo> queryPileChargeBillingConfs(ChargeBillingConfQueryCondition bo);

	/**
	 * @param inMap
	 * @description 查询大客户计费
	 * <AUTHOR>
	 * @create 2018-10-31 10:07:17
	 */
  List<ChargeBillingConfBo> queryCustGroupChargeBilling(Map inMap);
  /**
   * @param list
   * @description 企业计费存储可使用站点
   * <AUTHOR>
   * @create 2019-05-08 17:13:56
   */
  public void insertBillingStation(List<Map<String, Object>> list);
  public void delBillingStation(ChargeBillingConfBo bo);
  public List<ChargeBillingConfBo> queryBillingStation(ChargeBillingConfQueryCondition bo);

  void updateStationBilling(Map map);
  void updatePileBilling(Map map);

  /**
   * @Description 查询计费与站点和桩的关联
   * <AUTHOR>
   * @Date 2022/6/14 18:03
   * @param chcNo
   */
  List<ChargeBillingConfBo> selectBillRels(@Param("chcNo") String chcNo);

    ChargeBillConfigServiceModeBO queryServiceMode(String appNo);

	void addChargeBillingConfHistory(ChargeBillingConfHistoryBo bo);

	List<ChargeBillingConfHistoryBo> queryChargeBillingConfHistory(ChargeBillingConfHisQueryCondition bo);

	int queryChargeBillingConfHistoryCount(ChargeBillingConfHisQueryCondition condition);
}
