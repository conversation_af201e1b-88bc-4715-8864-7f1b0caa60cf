package com.ls.ner.billing.charge.bo;

import com.ls.ner.base.constants.BizConstants;
import com.pt.poseidon.api.framework.DicAttribute;



/**
 * <AUTHOR>
 * @dateTime 2016-04-08
 * @description 充电计费配置下发信息 E_CHCBILL_SEND
 */
public class ChcbillSendBo {
	private String chcNo;//充电计费编号
	private String sendTime;//下发时间
	private String sendStatus;//下发状态，01未下发 02下发失败 03部分成功 04下发成功
	
	//充电计费配置  E_CHARGE_BILLING_CONF
	private String chcName;//充电计费名称
	private String eftDate;//生效日期
	private String chcStatus;//状态，1有效、2无效、3草稿
	
	//充电站 C_PILE_STATION
	private String stationId;//站点id
	private String stationName;//充电站名称
	private String orgCode;//单位编号
	private String orgCodeName;//单位名称
	private String pileNo;//桩编号
	private String pileId;//桩编号



	@DicAttribute(dicName = "codeDict", key = "chcStatus", subType = BizConstants.CodeType.VALID_FLAG)
	private String chcStatusName;//状态名称，01有效、02无效、03草稿
	@DicAttribute(dicName = "codeDict", key = "sendStatus", subType = BizConstants.CodeType.CHC_BILL_SEND_STATUS)
	private String sendStatusName;//电价下发状态，01未下发，02下发失败，03部分成功，04下发成功

	public String getPileNo() {
		return pileNo;
	}
	public void setPileNo(String pileNo) {
		this.pileNo = pileNo;
	}
	public String getChcNo() {
		return chcNo;
	}
	public void setChcNo(String chcNo) {
		this.chcNo = chcNo;
	}
	public String getSendTime() {
		return sendTime;
	}
	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}
	public String getSendStatus() {
		return sendStatus;
	}
	public void setSendStatus(String sendStatus) {
		this.sendStatus = sendStatus;
	}
	public String getChcName() {
		return chcName;
	}
	public void setChcName(String chcName) {
		this.chcName = chcName;
	}
	public String getEftDate() {
		return eftDate;
	}
	public void setEftDate(String eftDate) {
		this.eftDate = eftDate;
	}
	public String getChcStatus() {
		return chcStatus;
	}
	public void setChcStatus(String chcStatus) {
		this.chcStatus = chcStatus;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getChcStatusName() {
		return chcStatusName;
	}
	public void setChcStatusName(String chcStatusName) {
		this.chcStatusName = chcStatusName;
	}
	public String getSendStatusName() {
		return sendStatusName;
	}
	public void setSendStatusName(String sendStatusName) {
		this.sendStatusName = sendStatusName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgCodeName() {
		return orgCodeName;
	}
	public void setOrgCodeName(String orgCodeName) {
		this.orgCodeName = orgCodeName;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}

	public String getPileId() {
		return pileId;
	}

	public void setPileId(String pileId) {
		this.pileId = pileId;
	}
}