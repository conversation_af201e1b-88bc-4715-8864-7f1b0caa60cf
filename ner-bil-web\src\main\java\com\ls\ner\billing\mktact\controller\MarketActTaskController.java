package com.ls.ner.billing.mktact.controller;

import com.ls.ner.billing.mktact.service.IMarketActService;
import com.pt.poseidon.api.framework.ServiceAutowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @description 活动状态的定时任务
 * @create 2017-08-10 10:44
 */
@Controller
@RequestMapping("/task/bil")
public class MarketActTaskController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketActController.class);

    @ServiceAutowired("marketActService")
    private IMarketActService marketActService;

    /**
     * 活动状态轮询job
     *
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "/marketacts/mktstatejob")
    public @ResponseBody Object marketStateJob() {
        try {
            LOGGER.debug(">>>>>开始执行活动状态轮询job-----");
            marketActService.marketStateJob();
        } catch (Exception e) {
            LOGGER.error("活动状态轮询job异常：", e);
        }
        return "执行成功";
    }
}
