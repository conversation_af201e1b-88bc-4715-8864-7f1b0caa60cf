package com.ls.ner.billing.xpcharge.bo;

/**
 * <AUTHOR>
 * @description 尖峰电价时间配置
 * @date 2022/6/20 14:39
 */
public class PeakTimeBo {

    /**
     * 主键
     */
    private String systemId;
    /**
     * 状态 0-停用 1-启用
     */
    private String state;
    /**
     * 生效日期
     */
    private String eftDate;
    /**
     * 失效日期
     */
    private String invDate;
    /**
     * 操作人
     */
    private String operator;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getEftDate() {
        return eftDate;
    }

    public void setEftDate(String eftDate) {
        this.eftDate = eftDate;
    }

    public String getInvDate() {
        return invDate;
    }

    public void setInvDate(String invDate) {
        this.invDate = invDate;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
